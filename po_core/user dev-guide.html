<!DOCTYPE html><html><head>
      <title>user dev-guide</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////Users/<USER>/.cursor/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:<PERSON>sol<PERSON>,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}@font-face{font-family:"Material Icons";font-style:normal;font-weight:400;src:local("Material Icons"),local("MaterialIcons-Regular"),url("data:application/x-font-woff;charset=utf-8;base64,d09GRgABAAAAAAfIAAsAAAAADDAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHU1VCAAABCAAAADMAAABCsP6z7U9TLzIAAAE8AAAARAAAAFZW7kosY21hcAAAAYAAAADTAAACjtP6ytBnbHlmAAACVAAAAxgAAAQ4zRtvlGhlYWQAAAVsAAAALwAAADYRwZsnaGhlYQAABZwAAAAcAAAAJAeKAzxobXR4AAAFuAAAABIAAAA8OGQAAGxvY2EAAAXMAAAAIAAAACAG5AfwbWF4cAAABewAAAAfAAAAIAEfAERuYW1lAAAGDAAAAVcAAAKFkAhoC3Bvc3QAAAdkAAAAYgAAAK2vz7wkeJxjYGRgYOBikGPQYWB0cfMJYeBgYGGAAJAMY05meiJQDMoDyrGAaQ4gZoOIAgCKIwNPAHicY2BkPsQ4gYGVgYOpk+kMAwNDP4RmfM1gxMjBwMDEwMrMgBUEpLmmMDgwVLy4xKzzX4chhrmK4QpQmBEkBwAZygyweJzFkr0NwjAQhZ+TEP6CRUfHBEwRUWaQTICyQbpMwRCskA5RUIONxG0RnnNpKAIV4qzPku/8c353ACYAYrIjCWCuMAh2ptf0/hiL3p/gyPUWa3osqlt0L1zu9r71z8dGrJRykFoauXQd932Lj5vhG+MjxGeYI8MKETObMpslf5EyP8tg+vHun5r539PvlvXzaVhRFVQDTPEWKVQR90KhnnC5Ek67vUKN4VuFasM/ldARj43CCkCsEjpJSoVVgRyU0GVSK6wUpFFCx8lFgX0BiXpRPQB4nE2TTWjcRhTH3xttpDhxN7uxPlp3u/FK7moRPixafRijNosxSw/LUsIwNcaEHPZggo/FmEKMCKWU4kNOOftQSlhE8alnH0Ix9BqWnHooPRrTQ0+mnu2bXTu2pPdGM9LM/6c3fwECTM4gBBMYQNqxzLrZAjqYSlqu2TAHZQA0/DQJH6FtzqGDnvbt4Ggwvzw/nL8EfH8kW0fsuRqhgWXZnY7M1picaUL7Du5BHeDzMIl83dAt016wH1qmvtSMo5R6YRJHTR//FXsff/nj/tc/5K9P5d+nP22+fFK5u7v3K39SW3y+OtDKO3L85vD09PD9z5X17a2N1g4tqk01RlqX7gyoEmnsWQtVr4rtZMmukEaFBZxzefkCn11cyKMLZgshRwgTYNoLNXCBz2ja7HvZG7hDpPSNfoo5vs0knK/9hb+rNpu+8kHPgk/Ao4kK3tWtTpSEtvkA9c+wE6UaUdwieNkaHg55tBEtRiEPw1s0+FtrtTcc9two2lhMknV7PZF/cs6+uUFTmpTGbEx7sQCPSLOttHS3GRltqp7SNzVSKzl6aWnZT/CX5k6/v9N3Hh8fHBwffJVjhrC6OgH5dkIt/tPsq+d/PD5Qz7G7efzq1THFjdZVPe/N6ulQ3JnDWSE5junsFsVIiFwL/htf1S5gJ3BfOcUxfHKLnzqpFpyfZ9cX+/5WB6a+Y0pHpzkNrYNVDwMsikK+y7WuLCRg/oFHkA8VT3rDg5ZnU6ktzzINymV0m74Xd5pfIGXyFeVEQSShkzqG7TBBa2OxVRKitLXv7h3uuftXnXq7lz2tZ/WnWa9dx9dCjDhHzmuVQATlmljr9dZErUydSo2Hbi/b1vXtrOeGCk2/8s3ZlO8+ueJT8BVlw5pGw2oYccdSiHHqx0RlabHqdNR9jAETl6PreJcPBnnfpTLnOQ8C3OV8AmQGzouV1iZdeb5SSIoVc8W8/kcDtksUH5FrU6/aqBqNWcMEzxG4DAQ14qRQhi9mWU0rzepKezbjfgCwQKxVYq5ajRgpRqy45CqwkJydcEkbTkvRz8P5/2ZpDTN4nGNgZGBgAOKb6v+/xvPbfGXgZmEAgeuB2kkI+v8bFgbmKiCXg4EJJAoAPyAKhQB4nGNgZGBg1vmvwxDDwgACQJKRARXwAwAzZQHQeJxjYQCCFAYGFgbSMQAcWACdAAAAAAAAAAwALgBgAIQAmADSAQgBIgE8AVABoAHeAfwCHHicY2BkYGDgZ7BgYGMAASYg5gJCBob/YD4DAA/hAWQAeJxlkbtuwkAURMc88gApQomUJoq0TdIQzEOpUDokKCNR0BuzBiO/tF6QSJcPyHflE9Klyyekz2CuG8cr7547M3d9JQO4xjccnJ57vid2cMHqxDWc40G4Tv1JuEF+Fm6ijRfhM+oz4Ra6eBVu4wZvvMFpXLIa40PYQQefwjVc4Uu4Tv1HuEH+FW7i1mkKn6Hj3Am3sHC6wm08Ou8tpSZGe1av1PKggjSxPd8zJtSGTuinyVGa6/Uu8kxZludCmzxMEzV0B6U004k25W35fj2yNlCBSWM1paujKFWZSbfat+7G2mzc7weiu34aczzFNYGBhgfLfcV6iQP3ACkSaj349AxXSN9IT0j16JepOb01doiKbNWt1ovippz6sVYYwsXgX2rGVFIkq7Pl2PNrI6qW6eOshj0xaSq9mpNEZIWs8LZUfOouNkVXxp/d5woqebeYIf4D2J1ywQB4nG3LOw6AIBAE0B384B+PAkgEa+QwNnYmHt+EpXSal5lkSBBnoP8oCFSo0aCFRIceA0ZMmLFAYSW88rmvtMUjG3RiQ9HvpfusM6zWNmtc5H/iPewha50tOt5PS/QBx2IeSwAA") format("woff")}.admonition{box-shadow:0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12),0 3px 1px -2px rgba(0,0,0,.2);position:relative;margin:1.5625em 0;padding:0 1.2rem;border-left:.4rem solid rgba(68,138,255,.8);border-radius:.2rem;background-color:rgba(255,255,255,.05);overflow:auto}.admonition>p{margin-top:.8rem}.admonition>.admonition-title{margin:0 -1.2rem;padding:.8rem 1.2rem .8rem 3.6rem;border-bottom:1px solid rgba(68,138,255,.2);background-color:rgba(68,138,255,.1);font-weight:700}.admonition>.admonition-title:before{position:absolute;left:1.2rem;font-size:1.5rem;color:rgba(68,138,255,.8);content:"\E3C9"}.admonition>.admonition-title:before{font-family:Material Icons;font-style:normal;font-variant:normal;font-weight:400;line-height:2rem;text-transform:none;white-space:nowrap;speak:none;word-wrap:normal;direction:ltr}.admonition.abstract,.admonition.summary,.admonition.tldr{border-left-color:rgba(0,176,255,.8)}.admonition.abstract>.admonition-title,.admonition.summary>.admonition-title,.admonition.tldr>.admonition-title{background-color:rgba(0,176,255,.1);border-bottom-color:rgba(0,176,255,.2)}.admonition.abstract>.admonition-title:before,.admonition.summary>.admonition-title:before,.admonition.tldr>.admonition-title:before{color:#00b0ff;content:"\E8D2"}.admonition.hint,.admonition.tip{border-left-color:rgba(0,191,165,.8)}.admonition.hint>.admonition-title,.admonition.tip>.admonition-title{background-color:rgba(0,191,165,.1);border-bottom-color:rgba(0,191,165,.2)}.admonition.hint>.admonition-title:before,.admonition.tip>.admonition-title:before{color:#00bfa5;content:"\E80E"}.admonition.info,.admonition.todo{border-left-color:rgba(0,184,212,.8)}.admonition.info>.admonition-title,.admonition.todo>.admonition-title{background-color:rgba(0,184,212,.1);border-bottom-color:rgba(0,184,212,.2)}.admonition.info>.admonition-title:before,.admonition.todo>.admonition-title:before{color:#00b8d4;content:"\E88E"}.admonition.check,.admonition.done,.admonition.success{border-left-color:rgba(0,200,83,.8)}.admonition.check>.admonition-title,.admonition.done>.admonition-title,.admonition.success>.admonition-title{background-color:rgba(0,200,83,.1);border-bottom-color:rgba(0,200,83,.2)}.admonition.check>.admonition-title:before,.admonition.done>.admonition-title:before,.admonition.success>.admonition-title:before{color:#00c853;content:"\E876"}.admonition.faq,.admonition.help,.admonition.question{border-left-color:rgba(100,221,23,.8)}.admonition.faq>.admonition-title,.admonition.help>.admonition-title,.admonition.question>.admonition-title{background-color:rgba(100,221,23,.1);border-bottom-color:rgba(100,221,23,.2)}.admonition.faq>.admonition-title:before,.admonition.help>.admonition-title:before,.admonition.question>.admonition-title:before{color:#64dd17;content:"\E887"}.admonition.attention,.admonition.caution,.admonition.warning{border-left-color:rgba(255,145,0,.8)}.admonition.attention>.admonition-title,.admonition.caution>.admonition-title,.admonition.warning>.admonition-title{background-color:rgba(255,145,0,.1);border-bottom-color:rgba(255,145,0,.2)}.admonition.attention>.admonition-title:before{color:#ff9100;content:"\E417"}.admonition.caution>.admonition-title:before,.admonition.warning>.admonition-title:before{color:#ff9100;content:"\E002"}.admonition.fail,.admonition.failure,.admonition.missing{border-left-color:rgba(255,82,82,.8)}.admonition.fail>.admonition-title,.admonition.failure>.admonition-title,.admonition.missing>.admonition-title{background-color:rgba(255,82,82,.1);border-bottom-color:rgba(255,82,82,.2)}.admonition.fail>.admonition-title:before,.admonition.failure>.admonition-title:before,.admonition.missing>.admonition-title:before{color:#ff5252;content:"\E14C"}.admonition.bug,.admonition.danger,.admonition.error{border-left-color:rgba(255,23,68,.8)}.admonition.bug>.admonition-title,.admonition.danger>.admonition-title,.admonition.error>.admonition-title{background-color:rgba(255,23,68,.1);border-bottom-color:rgba(255,23,68,.2)}.admonition.danger>.admonition-title:before{color:#ff1744;content:"\E3E7"}.admonition.error>.admonition-title:before{color:#ff1744;content:"\E14C"}.admonition.bug>.admonition-title:before{color:#ff1744;content:"\E868"}.admonition.example,.admonition.snippet{border-left-color:rgba(0,184,212,.8)}.admonition.example>.admonition-title,.admonition.snippet>.admonition-title{background-color:rgba(0,184,212,.1);border-bottom-color:rgba(0,184,212,.2)}.admonition.example>.admonition-title:before,.admonition.snippet>.admonition-title:before{color:#00b8d4;content:"\E242"}.admonition.cite,.admonition.quote{border-left-color:rgba(158,158,158,.8)}.admonition.cite>.admonition-title,.admonition.quote>.admonition-title{background-color:rgba(158,158,158,.1);border-bottom-color:rgba(158,158,158,.2)}.admonition.cite>.admonition-title:before,.admonition.quote>.admonition-title:before{color:#9e9e9e;content:"\E244"}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<h1 id="posdk-用户开发指南">PoSDK 用户开发指南 </h1>
<h2 id="1-系统简介">1. 系统简介 </h2>
<p>PoSDK（原 PoMVG）是一个用于多视图几何处理和位姿估计的 C++ 库系统。该系统采用模块化的插件架构设计，允许用户通过开发自定义插件来扩展系统功能，而无需修改核心库代码。</p>
<h3 id="11-系统架构">1.1 系统架构 </h3>
<p>PoSDK 系统主要包含以下组件：</p>
<ul>
<li><strong>核心库 (<code>po_core_lib</code>)</strong>: 提供基础数据结构、算法接口和插件框架</li>
<li><strong>插件系统</strong>: 允许用户二次开发、动态加载自定义算法和功能扩展</li>
</ul>
<h2 id="2-环境准备">2. 环境准备 </h2>
<h3 id="21-系统要求">2.1 系统要求 </h3>
<ul>
<li><strong>操作系统</strong>: Linux （后续支持macOS 或 Windows）</li>
<li><strong>编译器</strong>: 支持 C++17 标准的编译器 (GCC 7+, Clang 5+, MSVC 2019+)</li>
<li><strong>构建工具</strong>: CMake 3.15+</li>
</ul>
<h3 id="22-依赖项">2.2 依赖项 </h3>
<ul>
<li>
<p><strong>必需依赖</strong>:</p>
<ul>
<li>Eigen3 (3.3+)</li>
<li>Boost (1.65+), 主要使用 filesystem 和 system 组件</li>
<li>Protobuf (3.6+)</li>
<li>GTest (1.10+)</li>
</ul>
</li>
<li>
<p><strong>可选依赖</strong>（当前PoSDK必须，后续版本会考虑识别并变更为可选依赖）:</p>
<ul>
<li>OpenCV (4.0+): 用于图像处理相关功能</li>
<li>OpenGV: 用于几何视觉计算</li>
</ul>
</li>
</ul>
<h2 id="3-使用预编译的-po_core_lib">3. 使用预编译的 po_core_lib </h2>
<p>PoSDK 设计为允许用户直接使用预编译的 po_core_lib 库，而无需了解其内部实现细节。</p>
<h3 id="31-准备-po_core_lib">3.1 准备 po_core_lib </h3>
<ol>
<li>将提供的预编译库和头文件放置在一个目录中，例如：</li>
</ol>
<pre data-role="codeBlock" data-info="" class="language-text"><code>po_core_lib/
├── bin/              # 可执行文件目录
├── configs/          # 配置文件目录
│   └── methods/      # 方法配置文件
├── include/          # 头文件目录
│   ├── po_core/      # 核心库头文件
│   └── proto/        # Protobuf 生成的头文件
└── lib/              # 库文件目录
    └── cmake/        # CMake 配置文件
</code></pre><h3 id="32-在-cmake-项目中集成-po_core_lib">3.2 在 CMake 项目中集成 po_core_lib </h3>
<p>在您的 <code>CMakeLists.txt</code> 中，按照如下方式集成 po_core_lib：</p>
<pre data-role="codeBlock" data-info="cmake" class="language-cmake cmake"><code><span class="token keyword keyword-option">option</span><span class="token punctuation">(</span>USE_INTEGRATED_PO_CORE <span class="token string">"Use integrated po_core instead of installed version"</span> <span class="token boolean">ON</span><span class="token punctuation">)</span>
<span class="token comment"># 设置 po_core_lib 路径</span>
<span class="token keyword keyword-set">set</span><span class="token punctuation">(</span>po_core_folder <span class="token string">"/path/to/po_core_lib"</span> <span class="token variable">CACHE</span> PATH <span class="token string">"Path to po_core_lib directory"</span><span class="token punctuation">)</span>

<span class="token comment"># 链接到您的目标</span>
<span class="token keyword keyword-target_link_libraries">target_link_libraries</span><span class="token punctuation">(</span>your_target <span class="token namespace">PRIVATE</span> <span class="token inserted class-name">PoMVG::po_core</span><span class="token punctuation">)</span>
</code></pre><h2 id="4-插件注册宏">4. 插件注册宏 </h2>
<p>所有插件都需要使用 <code>REGISTRATION_PLUGIN</code> 宏在对应的 <code>.cpp</code> 源文件末尾进行注册。</p>
<p><strong><code>REGISTRATION_PLUGIN</code> 参数说明：</strong></p>
<ul>
<li><strong>第一个参数</strong>: 插件的 <strong>类名</strong> (例如 <code>MyDataPlugin</code>, <code>MyMethod</code>)</li>
<li><strong>第二个参数</strong>: 插件的 <strong>类型字符串</strong> (例如 <code>"my_data_plugin"</code>, <code>"my_method"</code>)，这个字符串必须与类中 <code>GetType()</code> 方法返回的字符串完全一致。</li>
</ul>
<div class="admonition note">
<p class="admonition-title">注意事项</p>
<ul>
<li><code>REGISTRATION_PLUGIN</code> <strong>必须</strong> 放置在 <strong>源文件 (.cpp)</strong> 中，<strong>不能</strong> 放置在头文件 (.hpp) 中，否则会导致链接错误。</li>
<li>确保注册的类型字符串在整个系统中是唯一的。</li>
</ul>
</div>
<p><strong>示例:</strong></p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 在 my_method.cpp 文件末尾</span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"pomvg_plugin_register.hpp"</span> <span class="token comment">// 确保包含此头文件</span></span>

<span class="token comment">// ... (MyMethod 类的实现) ...</span>

<span class="token function">REGISTRATION_PLUGIN</span><span class="token punctuation">(</span>MyMethod<span class="token punctuation">,</span> <span class="token string">"my_method"</span><span class="token punctuation">)</span>
</code></pre><hr>
<h2 id="基础开发">基础开发 </h2>
<p>本部分介绍 PoSDK 插件开发的基础知识，包括三种主要插件类型（Data, Method, Behavior）的核心接口、简单派生、配置和示例。</p>
<h3 id="4-数据插件-data-plugin">4. 数据插件 (Data Plugin) </h3>
<p>数据插件负责数据的封装、加载、保存和访问。</p>
<h4 id="41-接口">4.1 接口 </h4>
<h5 id="必须重载接口">必须重载接口 </h5>
<ul>
<li><strong><code>virtual const std::string&amp; GetType() const override;</code></strong>
<ul>
<li>返回插件的唯一类型字符串标识。<strong>必须</strong> 使用 <code>static const std::string</code> 实现。</li>
<li><strong>示例</strong>: <code>"data_images"</code>, <code>"data_tracks"</code></li>
</ul>
</li>
<li><strong><code>virtual void* GetData() override;</code></strong>
<ul>
<li>返回指向插件内部核心数据存储的 <code>void*</code> 指针。用户在使用 <code>GetDataPtr&lt;T&gt;</code> 时进行类型智能转换。</li>
</ul>
</li>
</ul>
<h5 id="可选重载接口">可选重载接口 </h5>
<ul>
<li><strong>读写接口</strong>：</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-virtual">virtual</span> <span class="token keyword keyword-bool">bool</span> <span class="token function">Save</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> folder <span class="token operator">=</span> <span class="token string">""</span><span class="token punctuation">,</span> 
                <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> filename <span class="token operator">=</span> <span class="token string">""</span><span class="token punctuation">,</span> 
                <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> extension <span class="token operator">=</span> <span class="token string">".pb"</span><span class="token punctuation">)</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
<span class="token keyword keyword-virtual">virtual</span> <span class="token keyword keyword-bool">bool</span> <span class="token function">Load</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> filepath <span class="token operator">=</span> <span class="token string">""</span><span class="token punctuation">,</span> 
                <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> file_type <span class="token operator">=</span> <span class="token string">"pb"</span><span class="token punctuation">)</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
</code></pre><p>用户可以重载Save/Load方法，实现将数据保存到文件的逻辑。</p>
<ul>
<li><strong>回调函数接口</strong>：实现自定义的回调函数接口，用于外部调用插件的特定功能。</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-virtual">virtual</span> <span class="token keyword keyword-void">void</span> <span class="token function">Call</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>string func<span class="token punctuation">,</span> <span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p>使用 <code>FUNC_INTERFACE_BEGIN</code>, <code>CALL</code>, <code>FUNC_INTERFACE_END</code> 宏来定义接口。例如：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token function">FUNC_INTERFACE_BEGIN</span><span class="token punctuation">(</span>MyDataPlugin<span class="token punctuation">)</span>
 <span class="token comment">// 对应函数原型 void SetData(std::vector&lt;double&gt;&amp; data, int id, const std::string&amp; name)</span>
<span class="token function">CALL</span><span class="token punctuation">(</span><span class="token keyword keyword-void">void</span><span class="token punctuation">,</span> SetData<span class="token punctuation">,</span> <span class="token function">REF</span><span class="token punctuation">(</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-double">double</span><span class="token operator">&gt;</span><span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">VAL</span><span class="token punctuation">(</span><span class="token keyword keyword-int">int</span><span class="token punctuation">)</span><span class="token punctuation">,</span> <span class="token function">STR</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span>
<span class="token function">FUNC_INTERFACE_END</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
</code></pre><p>在 interfaces.hpp 中提供了参数类型宏 - 简化参数获取</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">define</span> <span class="token macro-name function">REF</span><span class="token expression"><span class="token punctuation">(</span>type<span class="token punctuation">)</span> <span class="token punctuation">(</span><span class="token operator">*</span><span class="token function">va_arg</span><span class="token punctuation">(</span>argp<span class="token punctuation">,</span> type<span class="token operator">*</span><span class="token punctuation">)</span><span class="token punctuation">)</span> </span><span class="token comment">// 获取指针/引用类型参数</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">define</span> <span class="token macro-name function">VAL</span><span class="token expression"><span class="token punctuation">(</span>type<span class="token punctuation">)</span> <span class="token function">va_arg</span><span class="token punctuation">(</span>argp<span class="token punctuation">,</span> type<span class="token punctuation">)</span> </span><span class="token comment">// 获取基本类型参数</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">define</span> <span class="token macro-name function">STR</span><span class="token expression"><span class="token punctuation">(</span><span class="token punctuation">)</span> std<span class="token double-colon punctuation">::</span><span class="token function">string</span><span class="token punctuation">(</span><span class="token function">va_arg</span><span class="token punctuation">(</span>argp<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-char">char</span><span class="token operator">*</span><span class="token punctuation">)</span><span class="token punctuation">)</span> </span><span class="token comment">// 获取字符串类型参数</span></span>
</code></pre><ul>
<li><strong><code>virtual DataPtr CopyData() const override;</code></strong>
<ul>
<li>实现数据的深拷贝逻辑，输出的DataPtr与原本DataPtr指向数据内容一样，地址无关。</li>
</ul>
</li>
</ul>
<h4 id="42-可选派生方式">4.2 可选派生方式 </h4>
<ul>
<li><strong>虚基类</strong>：<strong><code>Interface::DataIO</code></strong>
<ul>
<li>提供最基本的数据插件接口。</li>
</ul>
</li>
<li><strong>序列化类</strong>：<strong><code>Interface::SDataIO</code></strong>
<ul>
<li>提供基于 Protobuf 的序列化和反序列化支持。</li>
<li>派生类需要实现 <code>CreateProtoMessage</code>, <code>ToProto</code>, <code>FromProto</code>。</li>
<li>提供了 <code>PROTO_SET_*</code> 和 <code>PROTO_GET_*</code> 系列宏简化序列化代码。</li>
<li>自动处理文件保存 (<code>Save</code>) 和加载 (<code>Load</code>)。</li>
</ul>
</li>
<li><strong>映射类</strong>：<code>DataMap&lt;T&gt;</code>
<ul>
<li>提供数据映射功能的接口，用户可以作为临时的DataPtr数据类型使用，不用每次都要写单独的数据插件。</li>
<li>提供 <code>GetDataPtr&lt;T&gt;</code> 方法获取映射的值。</li>
</ul>
</li>
</ul>
<h4 id="43-示例">4.3 示例 </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// my_data_plugin.hpp</span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;po_core.hpp&gt;</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;vector&gt;</span></span>

<span class="token keyword keyword-namespace">namespace</span> MyPlugin <span class="token punctuation">{</span>
<span class="token keyword keyword-using">using</span> <span class="token keyword keyword-namespace">namespace</span> PoMVG<span class="token punctuation">;</span>
<span class="token keyword keyword-using">using</span> <span class="token keyword keyword-namespace">namespace</span> Interface<span class="token punctuation">;</span>

<span class="token keyword keyword-struct">struct</span> <span class="token class-name">CustomData</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-int">int</span> id<span class="token punctuation">;</span>
    std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-double">double</span><span class="token operator">&gt;</span> values<span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>

<span class="token keyword keyword-class">class</span> <span class="token class-name">MyDataPlugin</span> <span class="token operator">:</span> <span class="token base-clause"><span class="token keyword keyword-public">public</span> <span class="token class-name">DataIO</span></span> <span class="token punctuation">{</span>
<span class="token keyword keyword-private">private</span><span class="token operator">:</span>
    CustomData data_<span class="token punctuation">;</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-override">override</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string type <span class="token operator">=</span> <span class="token string">"my_custom_data"</span><span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> type<span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token keyword keyword-void">void</span><span class="token operator">*</span> <span class="token function">GetData</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-override">override</span> <span class="token punctuation">{</span>
        <span class="token keyword keyword-return">return</span> <span class="token generic-function"><span class="token function">static_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-void">void</span><span class="token operator">*</span><span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token operator">&amp;</span>data_<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>
    <span class="token comment">// 可选：实现 Save/Load/Call/CopyData</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span> <span class="token comment">// namespace MyPlugin</span>

<span class="token comment">// my_data_plugin.cpp</span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"my_data_plugin.hpp"</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"pomvg_plugin_register.hpp"</span></span>

<span class="token function">REGISTRATION_PLUGIN</span><span class="token punctuation">(</span>MyPlugin<span class="token double-colon punctuation">::</span>MyDataPlugin<span class="token punctuation">,</span> <span class="token string">"my_custom_data"</span><span class="token punctuation">)</span>
</code></pre><h4 id="44-常用数据类型">4.4 常用数据类型 </h4>
<p>核心库提供了多种预定义的数据类型，可以直接在插件中使用或作为参考（详见 <code>po_core/types.hpp</code>）：</p>
<ul>
<li><strong>基础类型</strong>:
<ul>
<li><code>IndexT</code>: 索引类型 (<code>uint32_t</code>)</li>
<li><code>ViewId</code>: 视图 ID (<code>uint32_t</code>)</li>
<li><code>PtsId</code>: 点/轨迹 ID (<code>uint32_t</code>)</li>
<li><code>Size</code>: 尺寸类型 (<code>uint32_t</code>)</li>
</ul>
</li>
<li><strong>Eigen 类型</strong>:
<ul>
<li><code>Vector2d</code>, <code>Vector3d</code>, <code>VectorXd</code>: 列向量</li>
<li><code>Matrix3d</code>, <code>MatrixXd</code>: 矩阵</li>
<li><code>SpMatrix</code>: 稀疏矩阵</li>
</ul>
</li>
<li><strong>特征与匹配</strong>:
<ul>
<li><code>Feature</code>: 基础 2D 特征点 (<code>Vector2d</code>)</li>
<li><code>FeaturePoint</code>: 包含坐标、大小、角度、描述子的完整特征点。</li>
<li><code>ImageFeatureInfo</code>: 单张图像的特征信息 (路径, <code>std::vector&lt;FeaturePoint&gt;</code>, 使用标记)。</li>
<li><code>FeaturesInfo</code>: 所有图像特征的集合 (<code>std::vector&lt;ImageFeatureInfo&gt;</code>)。</li>
<li><code>IdMatch</code>: 单个特征匹配 (索引 <code>i</code>, 索引 <code>j</code>, 内点标记)。</li>
<li><code>IdMatches</code>: <code>std::vector&lt;IdMatch&gt;</code>。</li>
<li><code>ViewPair</code>: 视图对 (<code>std::pair&lt;IndexT, IndexT&gt;</code>)。</li>
<li><code>Matches</code>: 所有视图对之间的匹配 (<code>std::map&lt;ViewPair, IdMatches&gt;</code>)。</li>
</ul>
</li>
<li><strong>轨迹与观测</strong>:
<ul>
<li><code>ObsInfo</code>: 单个观测信息 (视图ID, 点ID, 观测ID, 2D坐标, 使用标记)。</li>
<li><code>Track</code>: 单条轨迹，<code>std::vector&lt;ObsInfo&gt;</code>。</li>
<li><code>TrackInfo</code>: 包含轨迹 (<code>Track</code>) 和使用标记。</li>
<li><code>Tracks</code>: 所有轨迹的集合 (<code>std::vector&lt;TrackInfo&gt;</code>), 包含归一化状态。</li>
</ul>
</li>
<li><strong>位姿</strong>:
<ul>
<li><code>RelativeRotation</code>: 相对旋转 (视图 <code>i</code>, <code>j</code>, 旋转矩阵 <code>Rij</code>, 权重)。</li>
<li><code>RelativeRotations</code>: <code>std::vector&lt;RelativeRotation&gt;</code>。</li>
<li><code>RelativePose</code>: 相对位姿 (视图 <code>i</code>, <code>j</code>, 旋转 <code>Rij</code>, 平移 <code>tij</code>, 权重)。</li>
<li><code>RelativePoses</code>: <code>std::vector&lt;RelativePose&gt;</code>。</li>
<li><code>GlobalRotations</code>: 全局旋转矩阵 (<code>std::vector&lt;Matrix3d&gt;</code>)。</li>
<li><code>GlobalTranslations</code>: 全局平移向量 (<code>std::vector&lt;Vector3d&gt;</code>)。</li>
<li><code>GlobalPoses</code>: 全局位姿 (包含 <code>rotations</code>, <code>translations</code>, <code>est_info</code>, <code>pose_format</code>)。</li>
</ul>
</li>
<li><strong>相机模型</strong>:
<ul>
<li><code>CameraIntrinsics</code>: 相机内参 (fx, fy, cx, cy, 畸变参数等)。</li>
<li><code>CameraModel</code>: 完整相机模型 (内参, 制造商, 型号等)。</li>
<li><code>CameraModels</code>: <code>std::vector&lt;CameraModel&gt;</code>。</li>
</ul>
</li>
<li><strong>其他</strong>:
<ul>
<li><code>ImagePaths</code>: 图像路径集合 (<code>std::vector&lt;std::pair&lt;std::string, bool&gt;&gt;</code>)</li>
<li><code>BearingVectors</code>: 归一化观测向量 (<code>Eigen::Matrix&lt;double,3,Eigen::Dynamic&gt;</code>)</li>
<li><code>BearingPairs</code>: 匹配的归一化观测向量对 (<code>std::vector&lt;Eigen::Matrix&lt;double,6,1&gt;&gt;</code>)</li>
</ul>
</li>
</ul>
<h4 id="45-数据配置">4.5 数据配置 </h4>
<p>数据插件通常不使用独立的 <code>.ini</code> 配置文件进行参数设置。其配置和数据填充主要通过成员变量和以下方式进行：</p>
<ol>
<li>
<p><strong><code>Load()</code> 方法</strong>: 对于支持从文件加载的数据插件（如 <code>DataTracks</code>, <code>DataGlobalPoses</code>, <code>SDataIO</code> 派生类），可以通过调用 <code>Load(filepath, file_type)</code> 来加载指定路径和类型的数据。</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-auto">auto</span> tracks_data <span class="token operator">=</span> <span class="token class-name">FactoryData</span><span class="token double-colon punctuation">::</span><span class="token function">Create</span><span class="token punctuation">(</span><span class="token string">"data_tracks"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
tracks_data<span class="token operator">-&gt;</span><span class="token function">Load</span><span class="token punctuation">(</span><span class="token string">"/path/to/your/tracks.tracks"</span><span class="token punctuation">,</span> <span class="token string">"tracks"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
<li>
<p><strong><code>Call()</code> 回调接口</strong>: 插件可以定义自己的成员函数，并通过 <code>Call()</code> 接口暴露出来，允许外部代码传递参数或触发特定操作。</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 假设 MyDataPlugin 有一个 SetValues(const std::vector&lt;double&gt;&amp;) 方法</span>
<span class="token keyword keyword-auto">auto</span> my_data <span class="token operator">=</span> <span class="token class-name">FactoryData</span><span class="token double-colon punctuation">::</span><span class="token function">Create</span><span class="token punctuation">(</span><span class="token string">"my_custom_data"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span><span class="token keyword keyword-double">double</span><span class="token operator">&gt;</span> values <span class="token operator">=</span> <span class="token punctuation">{</span><span class="token number">1.0</span><span class="token punctuation">,</span> <span class="token number">2.0</span><span class="token punctuation">,</span> <span class="token number">3.0</span><span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token comment">// 注意：传递引用参数需要取地址</span>
my_data<span class="token operator">-&gt;</span><span class="token function">Call</span><span class="token punctuation">(</span><span class="token string">"SetValues"</span><span class="token punctuation">,</span> <span class="token operator">&amp;</span>values<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><p>这需要插件内部使用 <code>FUNC_INTERFACE_BEGIN</code>, <code>CALL</code>, <code>FUNC_INTERFACE_END</code> 宏定义好对应的接口。</p>
</li>
<li>
<p><strong><code>SetStorageFolder()</code></strong> (仅限 <code>SDataIO</code> 派生类):</p>
<ul>
<li>对于继承自 <code>SDataIO</code> 的插件（支持 Protobuf 序列化），可以使用 <code>SetStorageFolder(path)</code> 方法设置默认的文件保存和加载目录。</li>
<li>当调用 <code>Save()</code> 或 <code>Load()</code> 时不指定完整路径，插件会使用这个默认目录。</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-auto">auto</span> serializable_data <span class="token operator">=</span> <span class="token class-name">FactoryData</span><span class="token double-colon punctuation">::</span><span class="token function">Create</span><span class="token punctuation">(</span><span class="token string">"my_serializable_data"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> sdata_io <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">dynamic_pointer_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span>SDataIO<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>serializable_data<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>sdata_io<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    sdata_io<span class="token operator">-&gt;</span><span class="token function">SetStorageFolder</span><span class="token punctuation">(</span><span class="token string">"/path/to/default/storage"</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 设置默认存储路径</span>
    <span class="token comment">// 如果设置了默认存储路径，则Save可以省略路径，会保存到默认目录</span>
    serializable_data<span class="token operator">-&gt;</span><span class="token function">Save</span><span class="token punctuation">(</span><span class="token string">""</span><span class="token punctuation">,</span> <span class="token string">"my_data_file"</span><span class="token punctuation">,</span> <span class="token string">".pb"</span><span class="token punctuation">)</span><span class="token punctuation">;</span> 
<span class="token punctuation">}</span>
</code></pre></li>
<li>
<p><strong>直接成员函数调用</strong>: 用户可以在创建实例后直接调用其公有成员函数来获取核心数据来进行算法设计。</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-auto">auto</span> camera_data <span class="token operator">=</span> <span class="token class-name">FactoryData</span><span class="token double-colon punctuation">::</span><span class="token function">Create</span><span class="token punctuation">(</span><span class="token string">"data_camera_model"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> camera_models <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>CameraModels<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>camera_data<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>camera_models<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    CameraModel cam<span class="token punctuation">;</span> 
    cam<span class="token punctuation">.</span><span class="token function">SetCameraIntrinsics</span><span class="token punctuation">(</span><span class="token comment">/*...*/</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    camera_models<span class="token operator">-&gt;</span><span class="token function">push_back</span><span class="token punctuation">(</span>cam<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre></li>
</ol>
<hr>
<h3 id="5-方法插件-method-plugin">5. 方法插件 (Method Plugin) </h3>
<p>方法插件用于实现具体的算法逻辑和数据处理流程。</p>
<h4 id="51-接口">5.1 接口 </h4>
<h5 id="必须重载接口-1">必须重载接口 </h5>
<ul>
<li><strong>方法名称</strong>：必须使用 <code>static const std::string</code> 实现</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-virtual">virtual</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
<span class="token comment">// 返回插件的唯一类型字符串标识。</span>
<span class="token comment">// 示例: `"method_sift"`, `"method_matches2tracks"`</span>
</code></pre><ul>
<li><strong>算法主入口</strong>:Build和Run函数二选一，推荐Run函数</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-virtual">virtual</span> DataPtr <span class="token function">Build</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> DataPtr<span class="token operator">&amp;</span> material_ptr <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
<span class="token comment">// 用于实现核心算法：</span>
<span class="token comment">// （1）material_ptr 是可选的输入数据。如果方法需要多个输入，将它们打包在 `DataPackage` 中传入。</span>
<span class="token comment">// （2）返回处理结果 (`DataPtr`)，如果处理失败或无结果，返回 `nullptr`。</span>
</code></pre><pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-virtual">virtual</span> DataPtr <span class="token function">Run</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
<span class="token comment">// 用于实现核心算法：基类的`Build` 方法会负责调用 `Run` 并处理输入/输出检查和性能分析等。</span>
<span class="token comment">// （1）用户需要在构造函数中设置输入数据类型：required_package_["data_type"] = nullptr;</span>
<span class="token comment">// （2）之后可以在Run函数中使用GetDataPtr&lt;T&gt;(required_package_["data_type"])来获取输入数据。</span>
</code></pre><h5 id="可选重载接口-针对-methodpreset-及其派生类">可选重载接口 (针对 <code>MethodPreset</code> 及其派生类) </h5>
<ul>
<li><strong>设置算法的先验信息：</strong></li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-virtual">virtual</span> <span class="token keyword keyword-void">void</span> <span class="token function">SetPriorInfo</span><span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> DataPtr<span class="token operator">&amp;</span> data_ptr<span class="token punctuation">,</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> type <span class="token operator">=</span> <span class="token string">""</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// （1）`MethodPreset` 设置先验信息，用于需要额外指导信息（如初始位姿、权重）的算法。</span>
<span class="token comment">// （2）`data_ptr` 是先验信息数据指针，`type` 是先验信息类型字符串。</span>

<span class="token keyword keyword-virtual">virtual</span> <span class="token keyword keyword-void">void</span> <span class="token function">ResetPriorInfo</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// 重置先验信息（清空所有先验信息）</span>
</code></pre><h4 id="52-可选派生方式">5.2 可选派生方式 </h4>
<ul>
<li><strong><code>Interface::Method</code></strong> (基类)
<ul>
<li>提供最基本的方法插件接口。需要自己处理输入检查、配置加载等。</li>
</ul>
</li>
<li><strong><code>Interface::MethodPreset</code></strong> (派生自 <code>Method</code>)
<ul>
<li>提供了预设的功能框架，简化开发：
<ul>
<li><strong>自动输入检查</strong>: 基于 <code>required_package_</code> 成员变量检查输入数据类型。</li>
<li><strong>配置管理</strong>: 支持通过 INI 文件配置 <code>method_options_</code>。</li>
<li><strong>先验信息</strong>: 支持通过 <code>SetPriorInfo</code> 传递额外信息。</li>
<li><strong>核心逻辑分离</strong>: 将核心算法实现在 <code>Run()</code> 中，<code>Build()</code> 负责流程控制。</li>
</ul>
</li>
</ul>
</li>
<li><strong><code>Interface::MethodPresetProfiler</code></strong> (派生自 <code>MethodPreset</code>)
<ul>
<li>在 <code>MethodPreset</code> 基础上增加了<strong>性能分析</strong>功能。</li>
<li>自动记录执行时间、内存使用，并可导出 CSV 报告。</li>
<li>可以通过 <code>enable_profiling</code> 选项控制是否启用分析。</li>
</ul>
</li>
<li><strong><code>Interface::RobustEstimator&lt;TSample&gt;</code></strong> (派生自 <code>MethodPresetProfiler</code>)
<ul>
<li>模板类 (<code>RobustEstimator&lt;TSample&gt;</code>)，提供了鲁棒估计的框架 (如 RANSAC, GNC-IRLS)。</li>
<li>需要配合模型估计器 (<code>model_estimator_ptr_</code>) 和代价评估器 (<code>cost_evaluator_ptr_</code>) 使用。</li>
<li>输入数据<strong>要求</strong>是 <code>DataSample&lt;TSample&gt;</code> 类型。</li>
</ul>
</li>
</ul>
<h4 id="53-示例">5.3 示例 </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// my_method.hpp</span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;po_core.hpp&gt;</span></span>

<span class="token keyword keyword-namespace">namespace</span> MyPlugin <span class="token punctuation">{</span>
<span class="token keyword keyword-using">using</span> <span class="token keyword keyword-namespace">namespace</span> PoMVG<span class="token punctuation">;</span>
<span class="token keyword keyword-using">using</span> <span class="token keyword keyword-namespace">namespace</span> Interface<span class="token punctuation">;</span>
<span class="token keyword keyword-using">using</span> <span class="token keyword keyword-namespace">namespace</span> types<span class="token punctuation">;</span> <span class="token comment">// 引入常用类型</span>

<span class="token keyword keyword-class">class</span> <span class="token class-name">MyMethod</span> <span class="token operator">:</span> <span class="token base-clause"><span class="token keyword keyword-public">public</span> <span class="token class-name">MethodPresetProfiler</span></span> <span class="token punctuation">{</span> <span class="token comment">// 继承Profiler以获得性能分析</span>
<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token function">MyMethod</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
    DataPtr <span class="token function">Run</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span> <span class="token comment">// 实现核心逻辑</span>

    <span class="token comment">// 可选: 如果继承MethodPreset，可以重写GetInputTypes</span>
    <span class="token comment">// const std::vector&lt;std::string&gt;&amp; GetInputTypes() const override;</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span> <span class="token comment">// namespace MyPlugin</span>

<span class="token comment">// my_method.cpp</span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"my_method.hpp"</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"pomvg_plugin_register.hpp"</span></span>

<span class="token keyword keyword-namespace">namespace</span> MyPlugin <span class="token punctuation">{</span>

<span class="token class-name">MyMethod</span><span class="token double-colon punctuation">::</span><span class="token function">MyMethod</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 定义需要的输入数据类型</span>
    required_package_<span class="token punctuation">[</span><span class="token string">"data_tracks"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>
    required_package_<span class="token punctuation">[</span><span class="token string">"data_global_poses"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>

    <span class="token comment">// 初始化默认选项 (会被配置文件覆盖)</span>
    method_options_<span class="token punctuation">[</span><span class="token string">"threshold"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token string">"0.5"</span><span class="token punctuation">;</span>
    method_options_<span class="token punctuation">[</span><span class="token string">"max_iterations"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token string">"100"</span><span class="token punctuation">;</span>

    <span class="token comment">// 加载默认配置文件 (如果存在)</span>
    <span class="token function">InitializeDefaultConfigPath</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 初始化日志目录</span>
    <span class="token function">InitializeLogDir</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token class-name">MyMethod</span><span class="token double-colon punctuation">::</span><span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string type <span class="token operator">=</span> <span class="token string">"my_method"</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> type<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

DataPtr <span class="token class-name">MyMethod</span><span class="token double-colon punctuation">::</span><span class="token function">Run</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 1. 获取输入数据 (MethodPreset已处理检查)</span>
    <span class="token keyword keyword-auto">auto</span> tracks <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>Tracks<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>required_package_<span class="token punctuation">[</span><span class="token string">"data_tracks"</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-auto">auto</span> poses <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>GlobalPoses<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>required_package_<span class="token punctuation">[</span><span class="token string">"data_global_poses"</span><span class="token punctuation">]</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    <span class="token comment">// 检查数据有效性</span>
    <span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>tracks <span class="token operator">||</span> <span class="token operator">!</span>poses<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        std<span class="token double-colon punctuation">::</span>cerr <span class="token operator">&lt;&lt;</span> <span class="token string">"["</span> <span class="token operator">&lt;&lt;</span> <span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token string">"] Error: Missing required input data."</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
        <span class="token keyword keyword-return">return</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>
    <span class="token punctuation">}</span>

    <span class="token comment">// 2. 获取配置参数</span>
    <span class="token keyword keyword-float">float</span> threshold <span class="token operator">=</span> <span class="token function">GetOptionAsFloat</span><span class="token punctuation">(</span>method_options_<span class="token punctuation">,</span> <span class="token string">"threshold"</span><span class="token punctuation">,</span> <span class="token number">0.5f</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-int">int</span> max_iter <span class="token operator">=</span> <span class="token function">GetOptionAsIndexT</span><span class="token punctuation">(</span>method_options_<span class="token punctuation">,</span> <span class="token string">"max_iterations"</span><span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

    std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"["</span> <span class="token operator">&lt;&lt;</span> <span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> <span class="token string">"] Running with threshold="</span> <span class="token operator">&lt;&lt;</span> threshold
              <span class="token operator">&lt;&lt;</span> <span class="token string">", max_iterations="</span> <span class="token operator">&lt;&lt;</span> max_iter <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>

    <span class="token comment">// 3. 实现核心算法逻辑...</span>
    <span class="token comment">// ... process tracks and poses ...</span>

    <span class="token comment">// 4. 创建并返回结果 (例如，返回处理后的位姿)</span>
    <span class="token comment">// 注意: 如果不希望修改输入数据，可以先深拷贝DataPtr</span>
    <span class="token comment">// auto result_poses_data = poses-&gt;CopyData(); // 假设DataGlobalPoses实现了CopyData</span>
    <span class="token comment">// auto result_poses = GetDataPtr&lt;GlobalPoses&gt;(result_poses_data);</span>
    <span class="token comment">// ... 修改 result_poses ...</span>
    <span class="token comment">// return result_poses_data;</span>

    <span class="token comment">// 或者创建新的数据对象返回</span>
    <span class="token keyword keyword-auto">auto</span> result_data <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">make_shared</span><span class="token generic class-name"><span class="token operator">&lt;</span>DataMap<span class="token operator">&lt;</span>std<span class="token double-colon punctuation">::</span>string<span class="token operator">&gt;&gt;</span></span></span><span class="token punctuation">(</span><span class="token string">"Processing finished"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> result_data<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token punctuation">}</span> <span class="token comment">// namespace MyPlugin</span>

<span class="token comment">// 注册插件</span>
<span class="token function">REGISTRATION_PLUGIN</span><span class="token punctuation">(</span>MyPlugin<span class="token double-colon punctuation">::</span>MyMethod<span class="token punctuation">,</span> <span class="token string">"my_method"</span><span class="token punctuation">)</span>
</code></pre><h4 id="54-常用数据类型">5.4 常用数据类型 </h4>
<p>方法插件通常处理核心数据类型，如 <code>Tracks</code>, <code>Matches</code>, <code>GlobalPoses</code>, <code>RelativePoses</code>, <code>CameraModels</code> 等。输入和输出类型取决于具体算法，具体见 <code>types.hpp</code> 和后续章节。</p>
<h4 id="55-方法参数配置">5.5 方法参数配置 </h4>
<ul>
<li><strong>设置默认值</strong>:<br>
(1) 在插件类的构造函数中，通过 <code>method_options_["param_name"] = "default_value";</code> 设置默认参数<br>
(2) 建议利用获取配置参数的函数来设置默认值，见 <code>types.hpp</code> 和后续章节。</li>
<li><strong>配置文件 (.ini)</strong>:
<ul>
<li>在 <code>configs/methods/</code> 目录下创建与 <code>GetType()</code> 返回值同名的 <code>.ini</code> 文件 (例如 <code>my_method.ini</code>)。</li>
<li>文件格式为标准的 INI 格式，包含一个与方法类型同名的节 <code>[my_method]</code>。</li>
<li>在节下面定义 <code>key=value</code> 对。</li>
<li><code>MethodPreset</code> 会自动加载此文件，配置文件中的值会覆盖构造函数中的默认值。</li>
</ul>
</li>
<li><strong>运行时设置</strong>:
<ul>
<li>可以通过 <code>SetMethodOptions(const MethodOptions&amp; options)</code> 批量设置选项。</li>
<li>可以通过 <code>SetMethodOption(const MethodParams&amp; key, const ParamsValue&amp; value)</code> 单独设置选项。</li>
<li>运行时设置的优先级最高，会覆盖配置文件和默认值。</li>
</ul>
</li>
<li><strong>获取配置值</strong>:
<ul>
<li>在 <code>Run()</code> 或其他成员函数中，使用 <code>GetOptionAsString</code>, <code>GetOptionAsIndexT</code>, <code>GetOptionAsFloat</code>, <code>GetOptionAsBool</code> 等辅助函数安全地获取配置值，见 <code>types.hpp</code> 和后续章节。</li>
</ul>
</li>
<li><strong>参数设置的优先级</strong>：运行时设置 &gt; 配置文件 &gt; 默认值</li>
</ul>
<hr>
<h3 id="6-行为插件-behavior-plugin">6. 行为插件 (Behavior Plugin) </h3>
<p>行为插件是 <code>Method</code> 的一种特殊形式，通常用于封装一系列方法调用，形成一个特定的功能模块或工作流。</p>
<h4 id="61-接口">6.1 接口 </h4>
<p>行为插件除了实现 <code>Method</code> 的<strong>必须接口</strong> (<code>GetType</code>, <code>Build</code>) 外，通常还会重载以下<strong>可选接口</strong>：</p>
<ul>
<li><strong><code>virtual const std::string&amp; GetMaterialType() const override;</code></strong>
<ul>
<li>返回该行为期望的主要输入数据类型。</li>
</ul>
</li>
<li><strong><code>virtual const std::string&amp; GetProductType() const override;</code></strong>
<ul>
<li>返回该行为最终生成的产品数据类型。</li>
</ul>
</li>
<li><strong><code>virtual void SetOptionsFromConfigFile(const std::string&amp; path, const std::string&amp; file_type) override;</code></strong>
<ul>
<li>如果需要，可以自定义当前行为所有方法配置的加载逻辑。</li>
</ul>
</li>
</ul>
<h4 id="62-可选派生方式">6.2 可选派生方式 </h4>
<ul>
<li><strong><code>Interface::Behavior</code></strong> (派生自 <code>Method</code>)
<ul>
<li>提供行为插件的基础接口。</li>
</ul>
</li>
<li><strong><code>Interface::BehaviorPreset</code></strong> (派生自 <code>Behavior</code>)
<ul>
<li>(似乎在当前代码中未完全实现或使用，但概念上存在) 可以提供类似 <code>MethodPreset</code> 的预设功能，如自动加载行为步骤、管理子方法配置等。</li>
</ul>
</li>
</ul>
<h4 id="63-示例">6.3 示例 </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// my_behavior.hpp</span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">&lt;po_core.hpp&gt;</span></span>

<span class="token keyword keyword-namespace">namespace</span> MyPlugin <span class="token punctuation">{</span>
<span class="token keyword keyword-using">using</span> <span class="token keyword keyword-namespace">namespace</span> PoMVG<span class="token punctuation">;</span>
<span class="token keyword keyword-using">using</span> <span class="token keyword keyword-namespace">namespace</span> Interface<span class="token punctuation">;</span>

<span class="token keyword keyword-class">class</span> <span class="token class-name">MyBehavior</span> <span class="token operator">:</span> <span class="token base-clause"><span class="token keyword keyword-public">public</span> <span class="token class-name">BehaviorPreset</span></span> <span class="token punctuation">{</span> <span class="token comment">// 继承 BehaviorPreset</span>

<span class="token keyword keyword-public">public</span><span class="token operator">:</span>
    <span class="token function">MyBehavior</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
    <span class="token comment">// Run() 通常在 BehaviorPreset 中不需要重载，Build 会处理流程</span>
    <span class="token comment">// DataPtr Run() override; </span>
    <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token function">GetMaterialType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token function">GetProductType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token keyword keyword-override">override</span><span class="token punctuation">;</span> 
<span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span> <span class="token comment">// namespace MyPlugin</span>


<span class="token comment">// my_behavior.cpp</span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"my_behavior.hpp"</span></span>
<span class="token macro property"><span class="token directive-hash">#</span><span class="token directive keyword">include</span> <span class="token string">"pomvg_plugin_register.hpp"</span></span>

<span class="token keyword keyword-namespace">namespace</span> MyPlugin <span class="token punctuation">{</span>

<span class="token class-name">MyBehavior</span><span class="token double-colon punctuation">::</span><span class="token function">MyBehavior</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 定义行为包含的串行方法步骤</span>
    sequential_methods_ <span class="token operator">=</span> <span class="token punctuation">{</span>
        <span class="token string">"method_feature_extraction"</span><span class="token punctuation">,</span> 
        <span class="token string">"method_matching"</span><span class="token punctuation">,</span> 
        <span class="token string">"method_track_building"</span>
    <span class="token punctuation">}</span><span class="token punctuation">;</span>
    

<span class="token punctuation">}</span>

<span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token class-name">MyBehavior</span><span class="token double-colon punctuation">::</span><span class="token function">GetType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string type <span class="token operator">=</span> <span class="token string">"my_behavior"</span><span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> type<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token class-name">MyBehavior</span><span class="token double-colon punctuation">::</span><span class="token function">GetMaterialType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string type <span class="token operator">=</span> <span class="token string">"data_images"</span><span class="token punctuation">;</span> <span class="token comment">// 假设输入是图像</span>
    <span class="token keyword keyword-return">return</span> type<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string<span class="token operator">&amp;</span> <span class="token class-name">MyBehavior</span><span class="token double-colon punctuation">::</span><span class="token function">GetProductType</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token keyword keyword-const">const</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-static">static</span> <span class="token keyword keyword-const">const</span> std<span class="token double-colon punctuation">::</span>string type <span class="token operator">=</span> <span class="token string">"data_tracks"</span><span class="token punctuation">;</span> <span class="token comment">// 假设输出是轨迹</span>
    <span class="token keyword keyword-return">return</span> type<span class="token punctuation">;</span>
<span class="token punctuation">}</span>

<span class="token comment">// Build 方法由 BehaviorPreset 基类提供，它会按 sequential_methods_ 顺序执行方法</span>
<span class="token comment">// DataPtr MyBehavior::Build(const DataPtr&amp; material_ptr) { ... }</span>

<span class="token punctuation">}</span> <span class="token comment">// namespace MyPlugin</span>

<span class="token function">REGISTRATION_PLUGIN</span><span class="token punctuation">(</span>MyPlugin<span class="token double-colon punctuation">::</span>MyBehavior<span class="token punctuation">,</span> <span class="token string">"my_behavior"</span><span class="token punctuation">)</span>
</code></pre><h4 id="64-常用数据类型">6.4 常用数据类型 </h4>
<p>行为插件的输入 (<code>MaterialType</code>) 和输出 (<code>ProductType</code>) 取决于其封装的工作流程。</p>
<h4 id="65-行为参数配置">6.5 行为参数配置 </h4>
<ul>
<li><strong>设置</strong>：行为插件本身可以通过 <code>SetOptionsFromConfigFile</code> 加载配置（例如定义方法调用顺序）。</li>
<li><strong>管理</strong>：行为插件内部调用的每个方法，其配置通常由行为插件的 <code>method_options_</code>来管理和传递。</li>
<li><strong>优先级</strong>：行为插件的配置 &gt; 方法插件的配置</li>
</ul>
<hr>
<h2 id="深入开发">深入开发 </h2>
<p>本部分将深入探讨 PoSDK 插件开发的高级主题。</p>
<h3 id="7-详细的插件派生选择">7. 详细的插件派生选择 </h3>
<h4 id="71-数据插件-data-plugin">7.1 数据插件 (Data Plugin) </h4>
<ul>
<li>
<p><strong><code>Interface::DataIO</code></strong>:</p>
<ul>
<li><strong>用途</strong>: 实现最基础的数据容器，或当数据不需要序列化/反序列化时使用。</li>
<li><strong>优点</strong>: 简单直接。</li>
<li><strong>缺点</strong>: 需要手动实现 <code>Save</code>/<code>Load</code>。</li>
</ul>
</li>
<li>
<p><strong><code>Interface::SDataIO</code></strong>:</p>
<ul>
<li><strong>用途</strong>: 需要将数据通过 Protobuf 进行序列化/反序列化（保存到文件或网络传输）的场景。</li>
<li><strong>优点</strong>: 自动处理文件 I/O；提供宏简化 Protobuf 字段映射。</li>
<li><strong>缺点</strong>: 引入 Protobuf 依赖；需要定义 <code>.proto</code> 文件并配置 CMake 生成代码。</li>
<li><strong>关键实现</strong>: 用户需要重载 <code>CreateProtoMessage</code>, <code>ToProto</code>, <code>FromProto</code>。推荐使用如下辅助宏（详情见 <code>interfaces_preset.hpp</code> 中）来简化实现：</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>    <span class="token operator">-</span>   `PROTO_SET_BASIC` <span class="token operator">/</span> `PROTO_GET_BASIC`<span class="token operator">:</span> 基础类型 <span class="token punctuation">(</span><span class="token keyword keyword-int">int</span><span class="token punctuation">,</span> <span class="token keyword keyword-float">float</span><span class="token punctuation">,</span> <span class="token keyword keyword-bool">bool</span><span class="token punctuation">,</span> string<span class="token punctuation">)</span>。
    <span class="token operator">-</span>   `PROTO_SET_VECTOR2F<span class="token operator">/</span>D` <span class="token operator">/</span> `PROTO_GET_VECTOR2F<span class="token operator">/</span>D`<span class="token operator">:</span> Eigen<span class="token double-colon punctuation">::</span>Vector2f<span class="token operator">/</span>d。
    <span class="token operator">-</span>   `PROTO_SET_VECTOR3D` <span class="token operator">/</span> `PROTO_GET_VECTOR3D`<span class="token operator">:</span> Eigen<span class="token double-colon punctuation">::</span>Vector3d。
    <span class="token operator">-</span>   `PROTO_SET_MATRIX3D` <span class="token operator">/</span> `PROTO_GET_MATRIX3D`<span class="token operator">:</span> Eigen<span class="token double-colon punctuation">::</span>Matrix3d。
    <span class="token operator">-</span>   `PROTO_SET_ARRAY` <span class="token operator">/</span> `PROTO_GET_ARRAY`<span class="token operator">:</span> `std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>基础类型<span class="token operator">&gt;</span>`。
    <span class="token operator">-</span>   `PROTO_SET_ENUM` <span class="token operator">/</span> `PROTO_GET_ENUM`<span class="token operator">:</span> 枚举类型。
</code></pre></li>
<li>
<p><strong><code>Interface::DataMap&lt;T&gt;</code></strong>:</p>
<ul>
<li><strong>模板参数 <code>T</code></strong>: 任意可默认构造的类型。</li>
<li><strong>用途</strong>: 快速包装 <strong>单个</strong> 数据对象（如 <code>RelativePose</code>, <code>GlobalPoses</code>, <code>std::vector</code>, <code>std::map</code>, 自定义结构体等）为 <code>DataPtr</code>，方便在方法间传递或作为方法返回值，避免为简单数据结构创建完整的数据插件。</li>
<li><strong>优点</strong>: 方便将现有数据集成到 PoSDK 框架，不用专门声明和编译新的 Data 数据类。</li>
<li><strong>缺点</strong>: 本身不直接支持序列化 (<code>SDataIO</code>)，如果需要持久化，需要包含它的插件（如 <code>DataPackage</code>）或外部逻辑来处理。</li>
<li><strong>访问</strong>: 使用 <code>GetDataPtr&lt;T&gt;(data_map_ptr)</code> 获取内部数据的指针。</li>
<li><strong>示例</strong>: 返回一个 <code>RelativePose</code> 对象<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// In Method's Run() function</span>
RelativePose estimated_pose <span class="token operator">=</span> <span class="token function">CalculatePose</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 假设得到一个 RelativePose 对象</span>
<span class="token keyword keyword-return">return</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">make_shared</span><span class="token generic class-name"><span class="token operator">&lt;</span>DataMap<span class="token operator">&lt;</span>RelativePose<span class="token operator">&gt;&gt;</span></span></span><span class="token punctuation">(</span>estimated_pose<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
</ul>
</li>
<li>
<p><strong><code>Interface::DataSample&lt;T&gt;</code></strong>:</p>
<ul>
<li><strong>模板参数 <code>T</code></strong>: <strong>必须</strong> 是 <code>std::vector&lt;ValueType&gt;</code> 类型，<code>ValueType</code> 是样本元素的类型 (如 <code>BearingPair</code>, <code>IdMatch</code>)。</li>
<li><strong>用途</strong>: 专用于鲁棒估计器 (如 RANSAC, GNC-IRLS) 的样本数据管理。支持零拷贝（共享内存）的随机子集和索引子集生成。</li>
<li><strong>优点</strong>: 高效的子集采样，避免大数据拷贝；提供 STL 兼容接口 (<code>begin</code>, <code>end</code>, <code>operator[]</code>, <code>size</code> 等)，方便访问样本数据（包括子集）。</li>
<li><strong>缺点</strong>: 主要设计用于鲁棒估计流程，通用性不如 <code>DataMap</code> 或标准 <code>DataIO</code>。</li>
<li><strong>关键接口</strong>:详细请见 <code>interfaces_robust_estimator.hpp</code> 中 <code>DataSample</code> 的定义</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>    <span class="token function">GetPopulationPtr</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">:</span> 获取总体样本数据指针
    <span class="token function">GetPopulationSize</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token operator">:</span> 获取总体样本数据大小
    <span class="token function">GetRandomSubset</span><span class="token punctuation">(</span>size<span class="token punctuation">)</span><span class="token operator">:</span> 获取随机子集
    <span class="token function">GetSubset</span><span class="token punctuation">(</span>indices<span class="token punctuation">)</span><span class="token operator">:</span> 获取指定索引子集
    <span class="token function">GetInlierSubset</span><span class="token punctuation">(</span>indices<span class="token punctuation">)</span><span class="token operator">:</span> 获取内点子集
    <span class="token punctuation">.</span><span class="token punctuation">.</span><span class="token punctuation">.</span>
</code></pre></li>
<li>
<p><strong><code>Interface::DataCosts</code></strong>: (后续可能考虑弃用，因为用<code>DataMap&lt;double&gt;</code>可以替代)</p>
<ul>
<li><strong>用途</strong>: 存储由代价评估器 (<code>MethodRelativeCost</code> 等) 计算出的残差或代价值列表 (<code>std::vector&lt;double&gt;</code>)。</li>
<li><strong>优点</strong>: 标准化的代价存储容器；提供 <code>std::vector</code> 接口，方便访问和处理。</li>
<li><strong>使用场景</strong>: 通常作为代价评估器 (<code>CostEvaluator</code>) 的<strong>输出</strong>，用于 RANSAC 等鲁棒估计器的内点判断。</li>
<li><strong>接口</strong>: 提供了 <code>push_back</code>, <code>operator[]</code>, <code>size</code>, <code>empty</code>, <code>begin</code>, <code>end</code> 等 <code>std::vector&lt;double&gt;</code> 兼容接口。</li>
</ul>
</li>
<li>
<p><strong><code>Interface::DataPackage</code></strong>:</p>
<ul>
<li><strong>用途</strong>: 将多个不同的 <code>DataPtr</code> 对象打包成一个单一的 <code>DataPtr</code>，主要用于向 <code>Method::Build()</code> 或 <code>Behavior::Build()</code> 传递多个输入数据。（后续会配合开发并行和异构机制的method基类来支持发送任务驱动的消息响应包 /拆分子包并行加速算法运行的功能）</li>
<li><strong>优点</strong>: 简化了需要多输入的 <code>Method</code> 或 <code>Behavior</code> 的接口，只需传递一个 <code>DataPackagePtr</code>。</li>
<li><strong>缺点</strong>: 本身不提供序列化；需要通过类型字符串（键）来存取内部数据。</li>
<li><strong>关键接口</strong> (详见 <code>interfaces_preset.hpp</code>):
<ul>
<li><strong><code>AddData(const DataPtr&amp; data_ptr)</code></strong>: 添加数据，使用数据的 <code>GetType()</code> 作为键。</li>
<li><strong><code>AddData(const Package&amp; package)</code></strong>: 合并另一个 <code>DataPackage</code>。</li>
<li><strong><code>AddData(const DataType&amp; alias_type, const DataPtr&amp; data_ptr)</code></strong>: 使用自定义别名作为键添加数据。</li>
<li><strong><code>GetData(const DataType&amp; type)</code></strong>: 根据类型字符串（键）获取内部的 <code>DataPtr</code>。</li>
<li><strong><code>GetPackage()</code></strong>: 获取当前Package的常量引用。</li>
</ul>
</li>
<li><strong>示例</strong>:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-auto">auto</span> data_package <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">make_shared</span><span class="token generic class-name"><span class="token operator">&lt;</span>DataPackage<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
data_package<span class="token operator">-&gt;</span><span class="token function">AddData</span><span class="token punctuation">(</span>tracks_data<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 使用 "data_tracks" 作为键</span>
data_package<span class="token operator">-&gt;</span><span class="token function">AddData</span><span class="token punctuation">(</span><span class="token string">"initial_poses"</span><span class="token punctuation">,</span> global_pose_data<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 使用别名 "initial_poses"</span>

<span class="token comment">// 在方法内部获取数据</span>
<span class="token keyword keyword-auto">auto</span> tracks <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>Tracks<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>data_package<span class="token operator">-&gt;</span><span class="token function">GetData</span><span class="token punctuation">(</span><span class="token string">"data_tracks"</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> poses <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>GlobalPoses<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>data_package<span class="token operator">-&gt;</span><span class="token function">GetData</span><span class="token punctuation">(</span><span class="token string">"initial_poses"</span><span class="token punctuation">)</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
</ul>
</li>
</ul>
<h4 id="72-方法插件-method-plugin">7.2 方法插件 (Method Plugin) </h4>
<ul>
<li><strong><code>Interface::Method</code></strong>:
<ul>
<li><strong>用途</strong>: 实现独立的、不需要复杂配置或输入检查的简单算法。</li>
<li><strong>优点</strong>: 最大灵活性，无额外框架约束。</li>
<li><strong>缺点</strong>: 需要手动处理所有输入/输出、配置加载和错误检查。</li>
</ul>
</li>
<li><strong><code>Interface::MethodPreset</code></strong>:
<ul>
<li><strong>用途</strong>: <strong>推荐</strong>用于大多数方法插件开发。实现具有标准化输入/输出、配置管理和可选先验信息的算法。</li>
<li><strong>优点</strong>: 简化开发流程，提供结构化框架；自动 INI 配置加载；自动输入数据检查；支持先验信息传递。</li>
<li><strong>缺点</strong>: 引入少量框架约定（如 <code>Run()</code> vs <code>Build()</code>, <code>required_package_</code>）。</li>
</ul>
</li>
<li><strong><code>Interface::MethodPresetProfiler</code></strong>:
<ul>
<li><strong>用途</strong>: 需要对方法性能（执行时间、内存占用）进行分析和记录的场景。</li>
<li><strong>优点</strong>: 自动性能数据采集和 CSV 导出；跨平台内存监控。</li>
<li><strong>缺点</strong>: 增加少量性能开销（可通过 <code>enable_profiling</code> 配置禁用）。</li>
</ul>
</li>
<li><strong><code>Interface::RobustEstimator&lt;TSample&gt;</code></strong>:
<ul>
<li><strong>用途</strong>: 实现基于 RANSAC 或 GNC-IRLS 等鲁棒估计算法。</li>
<li><strong>优点</strong>: 提供标准化的鲁棒估计流程；将模型估计和代价评估解耦。</li>
<li><strong>缺点</strong>: 仅适用于特定类型的鲁棒估计问题；需要配合 <code>DataSample&lt;TSample&gt;</code> 输入。</li>
</ul>
</li>
</ul>
<h4 id="73-行为插件-behavior-plugin">7.3 行为插件 (Behavior Plugin) </h4>
<ul>
<li><strong><code>Interface::Behavior</code></strong>:
<ul>
<li><strong>用途</strong>: 封装一系列方法调用，形成一个完整的功能流或特定应用场景。</li>
<li><strong>优点</strong>: 模块化组织复杂流程；定义清晰的输入（<code>MaterialType</code>）和输出（<code>ProductType</code>）。</li>
<li><strong>缺点</strong>: 需要手动管理内部方法的创建、配置和数据传递。</li>
</ul>
</li>
<li><strong><code>Interface::BehaviorPreset</code></strong>: (当前仅支持序列化方法调用)
<ul>
<li><strong>用途</strong>: 可以提供更高级的行为管理，如基于配置自动编排方法、统一处理子方法配置等。</li>
</ul>
</li>
</ul>
<h3 id="8-数据映射与异常处理">8. 数据映射与异常处理 </h3>
<h4 id="81-数据访问与类型安全-getdataptr">8.1 数据访问与类型安全 (<code>GetDataPtr</code>) </h4>
<ul>
<li><strong>核心</strong>: 使用 <code>Interface::GetDataPtr&lt;T&gt;(data_ptr)</code> 模板函数进行安全的数据类型转换。</li>
<li><strong>优点</strong>: 避免直接使用 <code>static_cast</code> 带来的类型风险；统一数据访问接口。</li>
<li><strong>示例</strong>:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 在方法插件的 Run() 中</span>
<span class="token keyword keyword-auto">auto</span> tracks_data <span class="token operator">=</span> required_package_<span class="token punctuation">[</span><span class="token string">"data_tracks"</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> tracks <span class="token operator">=</span> Interface<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>Tracks<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>tracks_data<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>tracks<span class="token punctuation">)</span> <span class="token punctuation">{</span><span class="token comment">//也可以不写，因为GetDataPtr会自动检查</span>
    std<span class="token double-colon punctuation">::</span>cerr <span class="token operator">&lt;&lt;</span> <span class="token string">"错误：输入的 data_tracks 类型不正确或数据为空！"</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
    <span class="token keyword keyword-return">return</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
<span class="token comment">// 现在可以安全使用 tracks 指针</span>
<span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> track_info <span class="token operator">:</span> <span class="token operator">*</span>tracks<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// ...</span>
<span class="token punctuation">}</span>
</code></pre></li>
</ul>
<h4 id="82-输入数据提供方式-methodpreset-派生类">8.2 输入数据提供方式 (<code>MethodPreset</code> 派生类) </h4>
<p>对于继承自 <code>MethodPreset</code> 或其派生类（如 <code>MethodPresetProfiler</code>, <code>RobustEstimator</code>）的方法插件，有以下几种方式可以提供所需的输入数据：</p>
<ol>
<li>
<p><strong>通过 <code>Build</code> 方法传入</strong>:</p>
<ul>
<li>在调用 <code>method_ptr-&gt;Build(data_ptr)</code> 时，可以将输入数据 <code>data_ptr</code> 直接传入。</li>
<li><strong>前提</strong>: 必须在插件的构造函数中，通过 <code>required_package_["data_type"] = nullptr;</code> 预先声明该方法所需的输入数据类型 (<code>data_type</code>)。</li>
<li>如果需要传入多个不同类型的数据，应将它们打包在一个 <code>DataPackage</code> 中，然后将 <code>DataPackage</code> 指针传给 <code>Build</code>。</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 插件构造函数中声明</span>
required_package_<span class="token punctuation">[</span><span class="token string">"data_tracks"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>
required_package_<span class="token punctuation">[</span><span class="token string">"data_camera_model"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>

<span class="token comment">// 调用时</span>
<span class="token keyword keyword-auto">auto</span> data_package <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">make_shared</span><span class="token generic class-name"><span class="token operator">&lt;</span>DataPackage<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
data_package<span class="token operator">-&gt;</span><span class="token function">AddData</span><span class="token punctuation">(</span>tracks_data_ptr<span class="token punctuation">)</span><span class="token punctuation">;</span>
data_package<span class="token operator">-&gt;</span><span class="token function">AddData</span><span class="token punctuation">(</span>camera_model_data_ptr<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> result <span class="token operator">=</span> method_ptr<span class="token operator">-&gt;</span><span class="token function">Build</span><span class="token punctuation">(</span>data_package<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
<li>
<p><strong>通过 <code>SetRequiredData</code> 方法设置</strong>:</p>
<ul>
<li>在调用 <code>Build</code> 之前，可以单独调用 <code>method_ptr-&gt;SetRequiredData(data_ptr)</code> 来设置输入数据。</li>
<li><strong>前提</strong>: 同样需要在插件的构造函数中声明所需的数据类型。</li>
<li>如果需要设置多个数据，需要多次调用 <code>SetRequiredData</code>。</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 插件构造函数中声明</span>
required_package_<span class="token punctuation">[</span><span class="token string">"data_matches"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>

<span class="token comment">// 调用时</span>
method_ptr<span class="token operator">-&gt;</span><span class="token function">SetRequiredData</span><span class="token punctuation">(</span>matches_data_ptr<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-auto">auto</span> result <span class="token operator">=</span> method_ptr<span class="token operator">-&gt;</span><span class="token function">Build</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// Build 时可以不传参数</span>
</code></pre></li>
<li>
<p><strong>通过配置文件指定路径加载</strong>:</p>
<ul>
<li>可以在方法的 <code>.ini</code> 配置文件中，使用与 <code>required_package_</code> 中声明的数据类型 <strong>同名的键</strong> 来指定一个文件路径。</li>
<li><code>MethodPreset</code> 的 <code>Build</code> 方法在检查输入时，如果发现某个 <code>required_package_</code> 中的 <code>DataPtr</code> 仍为 <code>nullptr</code>，它会尝试在 <code>method_options_</code> 中查找同名键，并调用对应 <code>DataIO</code> 子类的 <code>Load()</code> 方法从该路径加载数据。</li>
<li><strong>注意</strong>: 这要求对应的数据插件正确实现了 <code>Load()</code> 方法。</li>
</ul>
<pre data-role="codeBlock" data-info="ini" class="language-ini ini"><code><span class="token comment"># 在 my_method.ini 中</span>
<span class="token section"><span class="token punctuation">[</span><span class="token section-name selector">my_method</span><span class="token punctuation">]</span></span>
<span class="token key attr-name">data_tracks</span> <span class="token punctuation">=</span> <span class="token value attr-value">/path/to/my_tracks.pb</span> 
...
</code></pre><pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 插件构造函数中声明</span>
required_package_<span class="token punctuation">[</span><span class="token string">"data_tracks"</span><span class="token punctuation">]</span> <span class="token operator">=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">;</span>

<span class="token comment">// 调用时 (假设配置文件已加载)</span>
<span class="token keyword keyword-auto">auto</span> result <span class="token operator">=</span> method_ptr<span class="token operator">-&gt;</span><span class="token function">Build</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 会自动尝试加载配置文件中指定的 data_tracks</span>
</code></pre></li>
<li>
<p><strong>通过 <code>SetPriorInfo</code> 方法传入 (用于先验信息)</strong>:</p>
<ul>
<li><strong>目的</strong>: 向方法插件传递额外的"指导"信息，这些信息不是核心输入数据，但可能影响算法行为（例如，RANSAC 的初始模型、优化的初始位姿估计、带权重的样本等）。</li>
<li><strong>函数签名</strong>: <code>void SetPriorInfo(const DataPtr&amp; data_ptr, const std::string&amp; type = "");</code>
<ul>
<li><code>data_ptr</code>: 包含先验信息的 <code>DataPtr</code>。</li>
<li><code>type</code> (可选): 一个字符串标识符，用于区分不同类型的先验信息。如果留空，则行为会有所不同（见下文）。</li>
</ul>
</li>
<li><strong>两种模式</strong>:
<ol>
<li><strong><code>type</code> 为空</strong>: 此时 <code>data_ptr</code> <strong>必须</strong> 是一个 <code>DataPackagePtr</code>，支持一次性设置多种先验信息的情况。</li>
<li><strong><code>type</code> 不为空</strong>: 此时 <code>data_ptr</code> 可以是<strong>任意</strong> <code>DataPtr</code>。<code>SetPriorInfo</code> 会将这个 <code>data_ptr</code> 以 <code>type</code> 字符串为键，<strong>添加或更新</strong> 到插件内部的 <code>prior_info_</code> 成员中。这适用于设置单个或特定类型的先验信息。</li>
</ol>
</li>
<li><strong>使用</strong>: 在调用 <code>Build()</code> 之前调用此方法设置先验信息。</li>
<li><strong>插件内部访问</strong>: 在插件的 <code>Run()</code> 方法或其他成员函数中，通过访问受保护的 <code>prior_info_</code> 成员变量（<code>std::unordered_map&lt;std::string, DataPtr&gt;</code>）来获取设置的先验数据，并使用 <code>GetDataPtr&lt;T&gt;()</code> 进行类型转换。</li>
</ul>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 示例：设置初始位姿作为先验信息</span>
<span class="token keyword keyword-auto">auto</span> initial_pose_data <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">make_shared</span><span class="token generic class-name"><span class="token operator">&lt;</span>DataMap<span class="token operator">&lt;</span>RelativePose<span class="token operator">&gt;&gt;</span></span></span><span class="token punctuation">(</span>initial_pose<span class="token punctuation">)</span><span class="token punctuation">;</span>
method_ptr<span class="token operator">-&gt;</span><span class="token function">SetPriorInfo</span><span class="token punctuation">(</span>initial_pose_data<span class="token punctuation">,</span> <span class="token string">"initial_guess"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 示例：一次性设置多个先验信息</span>
<span class="token keyword keyword-auto">auto</span> prior_package <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">make_shared</span><span class="token generic class-name"><span class="token operator">&lt;</span>DataPackage<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
prior_package<span class="token operator">-&gt;</span><span class="token function">AddData</span><span class="token punctuation">(</span><span class="token string">"weights"</span><span class="token punctuation">,</span> weights_data<span class="token punctuation">)</span><span class="token punctuation">;</span>
prior_package<span class="token operator">-&gt;</span><span class="token function">AddData</span><span class="token punctuation">(</span><span class="token string">"mask"</span><span class="token punctuation">,</span> mask_data<span class="token punctuation">)</span><span class="token punctuation">;</span>
method_ptr<span class="token operator">-&gt;</span><span class="token function">SetPriorInfo</span><span class="token punctuation">(</span>prior_package<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// type 为空</span>

<span class="token keyword keyword-auto">auto</span> result <span class="token operator">=</span> method_ptr<span class="token operator">-&gt;</span><span class="token function">Build</span><span class="token punctuation">(</span>main_input_data<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><ul>
<li><strong>重置</strong>: 调用 <code>ResetPriorInfo()</code> 可以清空所有已设置的先验信息。</li>
</ul>
</li>
</ol>
<h4 id="83-异常处理">8.3 异常处理 </h4>
<ul>
<li><strong>策略</strong>: PoSDK 核心库倾向于使用返回值 (<code>nullptr</code> 或 <code>false</code>) 表示错误 + cerr显示错误信息，而不是抛出异常 <code>throw()</code> 导致程序终止，以避免跨插件边界的异常处理复杂性。</li>
<li><strong>后续</strong>：考虑给Method制定log和error等级与id，方便用户调试</li>
<li><strong>插件开发建议</strong>:
<ul>
<li>在插件内部可以使用 C++ 标准异常处理 (<code>try-catch</code>) 来捕获和处理内部错误。</li>
<li>在覆盖的接口方法（如 <code>Run</code>, <code>Build</code>, <code>Save</code>, <code>Load</code>）中，应捕获所有内部异常，并转换为 <code>nullptr</code> 或 <code>false</code> 返回值。</li>
<li>使用 <code>std::cerr</code> 输出详细的错误信息，帮助用户定位问题。</li>
<li>检查从工厂函数 (<code>FactoryData::Create</code>, <code>FactoryMethod::Create</code>) 或 <code>GetDataPtr</code> 返回的指针是否为 <code>nullptr</code>。</li>
</ul>
</li>
</ul>
<h3 id="9-性能统计与分析-methodpresetprofiler">9. 性能统计与分析 (<code>MethodPresetProfiler</code>) </h3>
<ul>
<li>
<p><strong>基类</strong>: 继承 <code>Interface::MethodPresetProfiler</code> 而不是 <code>MethodPreset</code>。</p>
</li>
<li>
<p><strong>启用/禁用</strong>:</p>
<ul>
<li>默认启用。</li>
<li>可以通过配置文件中的 <code>enable_profiling=false</code> 选项禁用。</li>
</ul>
</li>
<li>
<p><strong>自动记录</strong>: <code>Build</code> 方法会自动记录：</p>
<ul>
<li><strong>总执行时间 (<code>total_time_ms</code>)</strong>: 从 <code>Build</code> 开始到结束。</li>
<li><strong>计算时间 (<code>compute_time_ms</code>)</strong>: <code>Run()</code> 方法的执行时间。</li>
<li><strong>内存使用</strong>:
<ul>
<li><code>current_memory_usage</code>: <code>Build</code> 结束时的内存占用。</li>
<li><code>peak_memory_usage</code>: <code>Build</code> 过程中的峰值内存占用 (在 Linux 上通过 <code>getrusage</code>, Windows 上通过 <code>GetProcessMemoryInfo</code>)。</li>
</ul>
</li>
<li><strong>输入检查时间 (<code>input_check</code>)</strong>: <code>CheckInput</code> 的执行时间。</li>
</ul>
</li>
<li>
<p><strong>自定义阶段计时</strong>: 在 <code>Run()</code> 方法内部，可以使用 <code>std::chrono</code> 手动记录特定代码段的耗时，并添加到 <code>ProfileInfo</code> 的 <code>stage_timings</code> 中。</p>
<p>(注意: <code>MethodPresetProfiler</code> 的 <code>Build</code> 内部创建 <code>ProfileInfo</code>，目前 <code>Run</code> 无法直接访问它。如果需要在 <code>Run</code> 中添加自定义计时，需要修改框架或采用外部计时机制。)</p>
</li>
<li>
<p><strong>报告输出</strong>:</p>
<ul>
<li><strong>控制台打印</strong>: <code>Build</code> 结束后会自动调用 <code>PrintProfileReport</code> 打印摘要信息。</li>
<li><strong>CSV 文件</strong>:
<ul>
<li><code>Build</code> 结束后会自动调用 <code>ExportToCSV</code> 将性能数据追加到 CSV 文件。</li>
<li><strong>默认路径</strong>: <code>performance_log/method_performance.csv</code>。</li>
<li><strong>自定义路径</strong>:
<ul>
<li>在构造函数中调用 <code>InitializeLogDir()</code> 可设置方法特定的日志目录 (如 <code>"method_name_performance_log"</code> )。</li>
<li>可以重载 <code>GetLogDir()</code> 和 <code>GetCSVPath()</code> 来自定义目录和文件名。</li>
</ul>
</li>
<li>CSV 文件包含时间戳、方法名、配置描述、时间和内存指标等。</li>
</ul>
</li>
</ul>
</li>
</ul>
<h3 id="10-序列化存储与读取-sdataio">10. 序列化存储与读取 (<code>SDataIO</code>) </h3>
<ul>
<li><strong>目的</strong>: 提供一种标准化的方式来保存和加载插件数据，利用 Protobuf 实现跨平台和版本兼容。</li>
<li><strong>基类</strong>: 继承 <code>Interface::SDataIO</code>。</li>
<li><strong>核心虚函数</strong>:
<ul>
<li><strong><code>virtual std::unique_ptr&lt;google::protobuf::Message&gt; CreateProtoMessage() const = 0;</code></strong>: 创建一个空的 Protobuf 消息对象，用于反序列化。</li>
<li><strong><code>virtual std::unique_ptr&lt;google::protobuf::Message&gt; ToProto() const = 0;</code></strong>: 将插件内部数据转换为 Protobuf 消息对象并返回。</li>
<li><strong><code>virtual bool FromProto(const google::protobuf::Message&amp; message) = 0;</code></strong>: 从 Protobuf 消息对象中解析数据并填充到插件内部。</li>
</ul>
</li>
<li><strong>自动文件 I/O</strong>: <code>SDataIO</code> 的 <code>Save</code> 和 <code>Load</code> 方法会自动处理文件读写和 Protobuf 的序列化/反序列化流程。开发者只需实现上述三个核心函数。</li>
<li><strong>辅助宏</strong>:
<ul>
<li><strong><code>PROTO_SET_BASIC(proto_msg, field, value)</code></strong>: 设置基础类型字段 (int, float, bool, string等)。</li>
<li><strong><code>PROTO_GET_BASIC(proto_msg, field, value)</code></strong>: 获取基础类型字段。</li>
<li><strong><code>PROTO_SET_VECTOR2F/D(proto_msg, field, vec)</code></strong>: 设置 Eigen Vector2f/d。</li>
<li><strong><code>PROTO_GET_VECTOR2F/D(proto_msg, field, vec)</code></strong>: 获取 Eigen Vector2f/d。</li>
<li><strong><code>PROTO_SET_VECTOR3D(proto_msg, field, vec)</code></strong>: 设置 Eigen Vector3d。</li>
<li><strong><code>PROTO_GET_VECTOR3D(proto_msg, field, vec)</code></strong>: 获取 Eigen Vector3d。</li>
<li><strong><code>PROTO_SET_MATRIX3D(proto_msg, field, mat)</code></strong>: 设置 Eigen Matrix3d (按列优先)。</li>
<li><strong><code>PROTO_GET_MATRIX3D(proto_msg, field, mat)</code></strong>: 获取 Eigen Matrix3d。</li>
<li><strong><code>PROTO_SET_ARRAY(proto_msg, field, array)</code></strong>: 设置 <code>std::vector&lt;基础类型&gt;</code>。</li>
<li><strong><code>PROTO_GET_ARRAY(proto_msg, field, array)</code></strong>: 获取 <code>std::vector&lt;基础类型&gt;</code>。</li>
<li><strong><code>PROTO_SET_ENUM(proto_msg, field, value)</code></strong>: 设置枚举类型字段。</li>
<li><strong><code>PROTO_GET_ENUM(proto_msg, field, value)</code></strong>: 获取枚举类型字段。</li>
</ul>
</li>
<li><strong>使用流程</strong>:
<ol>
<li>定义数据的 <code>.proto</code> 文件。</li>
<li>在 CMake 中配置 Protobuf 代码生成。</li>
<li>插件类继承 <code>SDataIO</code>。</li>
<li>实现 <code>GetType</code>, <code>GetData</code>。</li>
<li>实现 <code>CreateProtoMessage</code>, <code>ToProto</code>, <code>FromProto</code>，使用辅助宏进行字段映射。</li>
<li>(可选) 实现 <code>CopyData</code>。</li>
<li>在 <code>.cpp</code> 文件中使用 <code>REGISTRATION_PLUGIN</code> 注册。</li>
</ol>
</li>
<li><strong>存储路径</strong>:
<ul>
<li><code>SetStorageFolder(path)</code>: 设置默认保存/加载目录。</li>
<li><code>Save(folder, filename, ext)</code>:
<ul>
<li>如果 <code>folder</code> 为空，使用 <code>storage_dir_</code>。</li>
<li>如果 <code>filename</code> 为空，使用 <code>GetType() + "_default"</code>。</li>
<li>如果 <code>extension</code> 为空，使用 <code>.pb</code>。</li>
</ul>
</li>
<li><code>Load(filepath, file_type)</code>:
<ul>
<li>如果 <code>filepath</code> 为空，使用 <code>storage_dir_ / (GetType() + "_default.pb")</code>。</li>
<li>如果 <code>filepath</code> 没有扩展名，默认添加 <code>.pb</code>。</li>
</ul>
</li>
</ul>
</li>
</ul>
<h3 id="11-方法配置文件-ini">11. 方法配置文件 (INI) </h3>
<ul>
<li><strong>格式</strong>: 标准 INI 文件格式。<pre data-role="codeBlock" data-info="ini" class="language-ini ini"><code><span class="token section"><span class="token punctuation">[</span><span class="token section-name selector">section_name</span><span class="token punctuation">]</span></span> ; 对应方法或行为的 GetType() 返回值
<span class="token key attr-name">key1</span> <span class="token punctuation">=</span> <span class="token value attr-value">value1</span>
<span class="token key attr-name">key2</span> <span class="token punctuation">=</span> <span class="token value attr-value">value2 # 支持注释</span>
<span class="token comment"># 空行和注释行会被忽略</span>
<span class="token key attr-name">@inherit</span> <span class="token punctuation">=</span> <span class="token value attr-value">/path/to/base_config.ini ; 可选：继承指令</span>
</code></pre></li>
<li><strong>加载机制 (<code>MethodPreset::InitializeDefaultConfigPath</code>, <code>LoadMethodOptions</code>)</strong>:
<ol>
<li><strong>优先级</strong>: 运行时设置 &gt; 代码中 <code>method_options_</code> &gt; 当前目录 <code>configs/methods/</code> &gt; 构建目录 <code>configs/methods/</code> &gt; 安装目录 <code>configs/methods/</code>。</li>
<li><strong>自动加载</strong>: <code>MethodPreset</code> 构造函数会尝试按上述优先级加载与 <code>GetType()</code> 同名的 <code>.ini</code> 文件。</li>
<li><strong>继承</strong>: 支持 <code>@inherit</code> 指令加载基础配置，当前配置会覆盖继承的配置。</li>
<li><strong>特定配置加载</strong>: <code>LoadMethodOptions</code> 可以传入第二个参数，加载 INI 文件中特定的节作为配置。</li>
</ol>
</li>
<li><strong>读取配置 (<code>GetOption*</code> in <code>types.hpp</code>)</strong>:
<ul>
<li>在插件代码中，使用 <code>method_options_</code> 成员变量访问配置。</li>
<li>使用 <code>GetOptionAsString</code>, <code>GetOptionAsIndexT</code>, <code>GetOptionAsFloat</code>, <code>GetOptionAsBool</code> 辅助函数安全地读取配置值，并提供默认值。</li>
</ul>
</li>
<li><strong>写入配置 (<code>ConfigurationTools</code>)</strong>:
<ul>
<li>虽然 <code>MethodPreset</code> 主要用于读取，但 <code>ConfigurationTools</code> 类 (<code>inifile.hpp</code>) 提供了写入 INI 文件的功能 (<code>WriteItem</code>, <code>WriteFile</code>)。可以用于保存用户修改后的配置。</li>
</ul>
</li>
</ul>
<h3 id="12-鲁棒估计器框架">12. 鲁棒估计器框架 </h3>
<p>PoSDK 提供了一个鲁棒估计器的框架，目前支持 RANSAC 和 GNC-IRLS。</p>
<h4 id="121-datasampletsample-数据容器">12.1 <code>DataSample&lt;TSample&gt;</code> 数据容器 </h4>
<ul>
<li><strong>目的</strong>: 管理用于鲁棒估计的样本数据，特别是支持高效的子集采样而无需复制原始数据（零拷贝）。</li>
<li><strong>模板参数 <code>TSample</code></strong>: 必须是 <code>std::vector&lt;ValueType&gt;</code> 类型，其中 <code>ValueType</code> 是样本中单个元素的类型（例如，<code>BearingPair</code>）。</li>
<li><strong>核心特性</strong>:
<ul>
<li><strong>零拷贝子集</strong>: <code>GetRandomSubset(size)</code> 和 <code>GetSubset(indices)</code> 返回一个新的 <code>DataSample</code> 对象，该对象共享原始数据 (<code>data_map_ptr_</code>)，但通过 <code>subset_indices_</code> 存储子集的索引。</li>
<li><strong>STL 兼容接口</strong>: 提供 <code>begin()</code>, <code>end()</code>, <code>operator[]</code>, <code>size()</code>, <code>empty()</code> 等接口，使得可以像使用 <code>std::vector</code> 一样访问样本数据（无论是完整样本还是子集）。迭代器和 <code>operator[]</code> 会自动处理索引映射。</li>
</ul>
</li>
<li><strong>使用</strong>:
<ul>
<li>通常作为 <code>RobustEstimator</code> 的输入数据类型。</li>
<li><code>RobustEstimator</code> 内部会使用 <code>GetRandomSubset</code> 来获取随机样本进行模型估计。</li>
<li><code>GetInlierSubset</code> 用于获取内点子集以进行最终模型优化。</li>
</ul>
</li>
<li><strong>示例</strong>:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 假设有原始数据 std::vector&lt;MySampleType&gt; original_data;</span>
<span class="token keyword keyword-auto">auto</span> data_sample <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>make_shared<span class="token operator">&lt;</span>DataSample<span class="token operator">&lt;</span>std<span class="token double-colon punctuation">::</span>vector<span class="token operator">&lt;</span>MySampleType<span class="token operator">&gt;&gt;</span><span class="token operator">&gt;</span><span class="token punctuation">(</span>original_data<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 获取随机子集</span>
<span class="token keyword keyword-auto">auto</span> random_subset <span class="token operator">=</span> data_sample<span class="token operator">-&gt;</span><span class="token function">GetRandomSubset</span><span class="token punctuation">(</span><span class="token number">10</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
std<span class="token double-colon punctuation">::</span>cout <span class="token operator">&lt;&lt;</span> <span class="token string">"Random subset size: "</span> <span class="token operator">&lt;&lt;</span> random_subset<span class="token operator">-&gt;</span><span class="token function">size</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token operator">&lt;&lt;</span> std<span class="token double-colon punctuation">::</span>endl<span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>random_subset<span class="token operator">-&gt;</span><span class="token function">empty</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 访问子集元素</span>
    MySampleType first_element <span class="token operator">=</span> <span class="token punctuation">(</span><span class="token operator">*</span>random_subset<span class="token punctuation">)</span><span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
    <span class="token comment">// 迭代子集</span>
    <span class="token keyword keyword-for">for</span> <span class="token punctuation">(</span><span class="token keyword keyword-const">const</span> <span class="token keyword keyword-auto">auto</span><span class="token operator">&amp;</span> element <span class="token operator">:</span> <span class="token operator">*</span>random_subset<span class="token punctuation">)</span> <span class="token punctuation">{</span>
        <span class="token comment">// ... process element ...</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre></li>
</ul>
<h4 id="122-robustestimatortsample-基类">12.2 <code>RobustEstimator&lt;TSample&gt;</code> 基类 </h4>
<ul>
<li><strong>模板参数 <code>TSample</code></strong>: 定义了估计器处理的样本数据类型（通常是 <code>BearingPairs</code> 或类似结构）。</li>
<li><strong>核心组件</strong>:
<ul>
<li><strong>模型估计器 (<code>model_estimator_ptr_</code>)</strong>: 继承自 <code>MethodPreset</code>，负责从一个最小样本集估计模型参数（例如，LiRP 方法估计相对位姿）。其类型通过 <code>model_estimator_type</code> 配置选项指定。</li>
<li><strong>代价评估器 (<code>cost_evaluator_ptr_</code>)</strong>: 继承自 <code>MethodPreset</code>，负责计算每个样本点相对于给定模型的残差（代价）。其类型通过 <code>cost_evaluator_type</code> 配置选项指定。</li>
</ul>
</li>
<li><strong>工作流程</strong>:
<ol>
<li>通过 <code>FactoryMethod::Create</code> 创建 <code>RobustEstimator</code> 实例 (如 <code>RANSACEstimator</code> 或 <code>GNCIRLSEstimator</code>)。</li>
<li>设置 <code>model_estimator_type</code> 和 <code>cost_evaluator_type</code> 选项。</li>
<li>(可选) 通过 <code>SetModelEstimatorOptions</code> 和 <code>SetCostEvaluatorOptions</code> 为内部的模型估计器和代价评估器设置特定参数。</li>
<li>通过 <code>SetRequiredData</code> 设置包含 <code>DataSample&lt;TSample&gt;</code> 的输入数据。</li>
<li>调用 <code>Build()</code> 启动鲁棒估计流程。</li>
</ol>
</li>
<li><strong>派生类</strong>:
<ul>
<li><code>RANSACEstimator&lt;TSample&gt;</code>: 实现标准的 RANSAC 算法。</li>
<li><code>GNCIRLSEstimator&lt;TSample&gt;</code>: 实现 GNC-IRLS 算法，通常更鲁棒但计算量更大。</li>
</ul>
</li>
<li><strong>配置</strong>: 通过 <code>.ini</code> 文件或 <code>SetMethodOptions</code> 配置鲁棒估计器的参数（如迭代次数、置信度、内点阈值）以及内部模型估计器和代价评估器的类型。</li>
</ul>
<h4 id="123-使用示例-test_ransac_lirpcpp-简化版">12.3 使用示例 (<code>test_ransac_lirp.cpp</code> 简化版) </h4>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 1. 创建 RANSAC 估计器实例</span>
<span class="token keyword keyword-auto">auto</span> ransac_estimator <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">dynamic_pointer_cast</span><span class="token generic class-name"><span class="token operator">&lt;</span>RobustEstimator<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>
    <span class="token class-name">FactoryMethod</span><span class="token double-colon punctuation">::</span><span class="token function">Create</span><span class="token punctuation">(</span><span class="token string">"ransac_estimator"</span><span class="token punctuation">)</span> <span class="token comment">// 假设 TSample 是 BearingPairs</span>
<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token function">ASSERT_TRUE</span><span class="token punctuation">(</span>ransac_estimator <span class="token operator">!=</span> <span class="token keyword keyword-nullptr">nullptr</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 2. 设置 RANSAC 参数</span>
MethodOptions ransac_options <span class="token punctuation">{</span>
    <span class="token punctuation">{</span><span class="token string">"model_estimator_type"</span><span class="token punctuation">,</span> <span class="token string">"method_LiRP"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>         <span class="token comment">// 使用 LiRP 估计模型</span>
    <span class="token punctuation">{</span><span class="token string">"cost_evaluator_type"</span><span class="token punctuation">,</span> <span class="token string">"method_relative_cost"</span><span class="token punctuation">}</span><span class="token punctuation">,</span> <span class="token comment">// 使用相对位姿代价评估</span>
    <span class="token punctuation">{</span><span class="token string">"max_iterations"</span><span class="token punctuation">,</span> <span class="token string">"1000"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span><span class="token string">"confidence"</span><span class="token punctuation">,</span> <span class="token string">"0.99"</span><span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token punctuation">{</span><span class="token string">"inlier_threshold"</span><span class="token punctuation">,</span> <span class="token string">"1e-4"</span><span class="token punctuation">}</span><span class="token punctuation">,</span> <span class="token comment">// RANSAC 内点阈值</span>
    <span class="token punctuation">{</span><span class="token string">"min_sample_size"</span><span class="token punctuation">,</span> <span class="token string">"8"</span><span class="token punctuation">}</span>      <span class="token comment">// LiRP 需要 8 对点</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
ransac_estimator<span class="token operator">-&gt;</span><span class="token function">SetMethodOptions</span><span class="token punctuation">(</span>ransac_options<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 3. (可选) 设置 LiRP 和 Cost Evaluator 的特定参数</span>
MethodOptions lirp_options <span class="token punctuation">{</span> <span class="token punctuation">{</span><span class="token string">"identify_mode"</span><span class="token punctuation">,</span> <span class="token string">"PPO"</span><span class="token punctuation">}</span> <span class="token comment">/* ... 其他 LiRP 选项 ... */</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
ransac_estimator<span class="token operator">-&gt;</span><span class="token function">SetModelEstimatorOptions</span><span class="token punctuation">(</span>lirp_options<span class="token punctuation">)</span><span class="token punctuation">;</span>

MethodOptions cost_options <span class="token punctuation">{</span> <span class="token punctuation">{</span><span class="token string">"residual_type"</span><span class="token punctuation">,</span> <span class="token string">"sampson"</span><span class="token punctuation">}</span> <span class="token comment">/* ... 其他 Cost 选项 ... */</span> <span class="token punctuation">}</span><span class="token punctuation">;</span>
ransac_estimator<span class="token operator">-&gt;</span><span class="token function">SetCostEvaluatorOptions</span><span class="token punctuation">(</span>cost_options<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 4. 准备输入数据 (DataSample&lt;BearingPairs&gt;)</span>
BearingPairs bearing_pairs_data <span class="token operator">=</span> <span class="token function">GenerateBearingPairs</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 假设有函数生成数据</span>
<span class="token keyword keyword-auto">auto</span> sample_data <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">make_shared</span><span class="token generic class-name"><span class="token operator">&lt;</span>DataSample<span class="token operator">&lt;</span>BearingPairs<span class="token operator">&gt;&gt;</span></span></span><span class="token punctuation">(</span>bearing_pairs_data<span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 5. 设置输入数据</span>
DataPackage input_package<span class="token punctuation">;</span>
input_package<span class="token punctuation">.</span><span class="token function">AddData</span><span class="token punctuation">(</span><span class="token string">"data_sample"</span><span class="token punctuation">,</span> sample_data<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// RobustEstimator 需要 data_sample</span>
<span class="token comment">// 如果 model_estimator 或 cost_evaluator 需要其他数据 (如相机模型)，也需添加到包中</span>
<span class="token comment">// input_package.AddData("data_camera_model", camera_model_data);</span>
ransac_estimator<span class="token operator">-&gt;</span><span class="token function">SetRequiredData</span><span class="token punctuation">(</span>input_package<span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 或者直接传入 Build</span>

<span class="token comment">// 6. 执行 RANSAC</span>
<span class="token keyword keyword-auto">auto</span> result <span class="token operator">=</span> ransac_estimator<span class="token operator">-&gt;</span><span class="token function">Build</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>

<span class="token comment">// 7. 处理结果 (通常是 DataMap&lt;RelativePose&gt;)</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>result<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token keyword keyword-auto">auto</span> pose <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>RelativePose<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>result<span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// ... 使用估计的位姿 ...</span>
<span class="token punctuation">}</span>
</code></pre><hr>
<h2 id="附录">附录 </h2>
<h2 id="a-核心数据类型详解">A. 核心数据类型详解 </h2>
<h3 id="基础类型">基础类型 </h3>
<h4 id="indext">IndexT </h4>
<ul>
<li><strong>定义</strong>: <code>using IndexT = uint32_t;</code></li>
<li><strong>含义</strong>: 索引类型，用于各种索引标识</li>
<li><strong>取值范围</strong>: 0 ~ 4,294,967,295</li>
<li><strong>使用场景</strong>: 作为集合索引、ID标识等</li>
</ul>
<h4 id="viewid">ViewId </h4>
<ul>
<li><strong>定义</strong>: <code>using ViewId = uint32_t;</code></li>
<li><strong>含义</strong>: 视图ID，用于标识相机视图</li>
<li><strong>使用场景</strong>: 在跟踪、位姿数据中引用特定视图</li>
</ul>
<h4 id="ptsid">PtsId </h4>
<ul>
<li><strong>定义</strong>: <code>using PtsId = uint32_t;</code></li>
<li><strong>含义</strong>: 点/轨迹ID，用于标识3D点或特征轨迹</li>
<li><strong>使用场景</strong>: 在轨迹数据中引用特定3D点或特征</li>
</ul>
<h3 id="特征与匹配">特征与匹配 </h3>
<h4 id="feature">Feature </h4>
<ul>
<li><strong>定义</strong>: <code>using Feature = Vector2d;</code></li>
<li><strong>含义</strong>: 基础2D特征点，即图像中的点坐标(x, y)</li>
<li><strong>用途</strong>: 表示图像中的特征点位置</li>
</ul>
<h4 id="featurepoint">FeaturePoint </h4>
<ul>
<li><strong>含义</strong>: 完整特征点信息</li>
<li><strong>用途</strong>: 存储特征点的位置、大小、方向和描述子</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>coord</code>: 特征点坐标(x, y)</li>
<li><code>size</code>: 特征点大小</li>
<li><code>angle</code>: 特征点方向角度</li>
<li><code>descriptor</code>: 特征描述子向量</li>
<li><code>is_used</code>: 是否使用该特征点的标志</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>FeaturePoint()</code>: 默认构造函数</li>
<li><code>FeaturePoint(const Feature&amp; pos, float size_=0, float angle_=0)</code>: 从基础特征点构造</li>
<li><code>FeaturePoint(float x, float y, float size_=0, float angle_=0)</code>: 从坐标构造</li>
</ul>
</li>
</ul>
<h4 id="imagefeatureinfo">ImageFeatureInfo </h4>
<ul>
<li><strong>含义</strong>: 单张图像的特征信息</li>
<li><strong>用途</strong>: 存储一张图像中的所有特征点</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>image_path</code>: 图像文件路径</li>
<li><code>features</code>: 特征点集合(<code>std::vector&lt;FeaturePoint&gt;</code>)</li>
<li><code>is_used</code>: 该图像特征是否被使用</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>ImageFeatureInfo()</code>: 默认构造函数</li>
<li><code>ImageFeatureInfo(const std::string&amp; path, bool used=true)</code>: 带参数构造函数</li>
<li><code>AddFeature(const FeaturePoint&amp; feat)</code>: 添加特征点</li>
<li><code>GetNumFeatures()</code>: 获取特征点数量</li>
<li><code>ClearUnusedFeatures()</code>: 清除未使用的特征点</li>
</ul>
</li>
</ul>
<h4 id="featuresinfo">FeaturesInfo </h4>
<ul>
<li><strong>含义</strong>: 所有图像的特征信息集合</li>
<li><strong>用途</strong>: 管理多张图像的特征点信息</li>
<li><strong>类型</strong>: <code>std::vector&lt;ImageFeatureInfo&gt;</code>的派生类</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>AddImageFeatures(const std::string&amp; image_path, bool is_used=true)</code>: 添加图像特征</li>
<li><code>GetNumValidImages()</code>: 获取有效图像数量</li>
<li><code>ClearUnusedImages()</code>: 清除未使用的图像</li>
<li><code>ClearAllUnusedFeatures()</code>: 清除所有未使用的特征点</li>
</ul>
</li>
</ul>
<h4 id="idmatch">IdMatch </h4>
<ul>
<li><strong>含义</strong>: 特征点匹配</li>
<li><strong>用途</strong>: 表示两个特征点之间的匹配关系</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>i</code>: 第一个特征的索引</li>
<li><code>j</code>: 第二个特征的索引</li>
<li><code>is_inlier</code>: RANSAC内点标志，默认为true</li>
</ul>
</li>
</ul>
<h4 id="matches">Matches </h4>
<ul>
<li><strong>定义</strong>: <code>using Matches = std::map&lt;ViewPair, IdMatches&gt;;</code></li>
<li><strong>含义</strong>: 所有视图对之间的匹配</li>
<li><strong>用途</strong>: 存储不同视图对之间的特征匹配信息</li>
<li><strong>结构</strong>: 键为视图对(ViewPair)，值为对应的匹配集合(IdMatches)</li>
</ul>
<h3 id="轨迹与观测">轨迹与观测 </h3>
<h4 id="obsinfo">ObsInfo </h4>
<ul>
<li><strong>含义</strong>: 3D点的单个观测信息</li>
<li><strong>用途</strong>: 记录3D点在某一视图中的观测</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>view_id</code>: 视图ID</li>
<li><code>pts_id</code>: 3D点ID</li>
<li><code>obs_id</code>: 观测ID，默认为0</li>
<li><code>coord</code>: 图像特征点的2D坐标</li>
<li><code>is_used</code>: 当前观测是否被使用，默认为true</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>ObsInfo()</code>: 默认构造函数</li>
<li><code>ObsInfo(ViewId vid, PtsId pid, const Vector2d&amp; c)</code>: 带参数构造函数</li>
<li><code>GetCoord(Vector2d&amp; coord_out)</code>: 获取观测坐标</li>
<li><code>GetHomoCoord()</code>: 获取齐次坐标</li>
<li><code>SetUsed(bool used)</code>: 设置观测使用状态</li>
</ul>
</li>
</ul>
<h4 id="track">Track </h4>
<ul>
<li><strong>含义</strong>: 3D点的观测信息集合</li>
<li><strong>用途</strong>: 存储一个3D点在多个视图中的观测</li>
<li><strong>类型</strong>: <code>std::vector&lt;ObsInfo&gt;</code>的派生类</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>AddObservation(ViewId view_id, PtsId track_id, const Vector2d&amp; coord)</code>: 添加观测</li>
<li><code>GetNumObservations()</code>: 获取观测点数量</li>
<li><code>GetObservation(IndexT index)</code>: 获取指定索引的观测信息</li>
<li><code>GetValidObservationCount()</code>: 获取有效观测数量</li>
<li><code>SetObservationUsed(IndexT index, bool used)</code>: 设置指定观测的使用状态</li>
</ul>
</li>
</ul>
<h4 id="trackinfo">TrackInfo </h4>
<ul>
<li><strong>含义</strong>: 跟踪信息，包括使用标志</li>
<li><strong>用途</strong>: 封装Track并添加使用状态</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>track</code>: 轨迹数据</li>
<li><code>is_used</code>: 该轨迹是否被使用，默认为true</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>TrackInfo()</code>: 默认构造函数</li>
<li><code>TrackInfo(const Track&amp; t, bool used=true)</code>: 带参数构造函数</li>
<li><code>GetObservationCount()</code>: 获取观测数量</li>
<li><code>GetValidObservationCount()</code>: 获取有效观测数量</li>
<li><code>GetObservation(IndexT index)</code>: 获取指定观测信息</li>
<li><code>GetObservationCoord(IndexT index, Vector2d&amp; coord_out)</code>: 获取指定观测坐标</li>
<li><code>SetUsed(bool used)</code>: 设置轨迹使用状态</li>
<li><code>SetObservationUsed(IndexT index, bool used)</code>: 设置指定观测使用状态</li>
<li><code>AddObservation(const ObsInfo&amp; obs)</code>: 添加新观测</li>
</ul>
</li>
</ul>
<h4 id="tracks">Tracks </h4>
<ul>
<li><strong>含义</strong>: 所有轨迹的集合</li>
<li><strong>用途</strong>: 管理多个轨迹数据，支持轨迹的查询和操作</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>tracks_</code>: 轨迹信息向量</li>
<li><code>is_normalized_</code>: 是否已归一化标志</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li>支持标准容器接口: <code>size()</code>, <code>empty()</code>, <code>clear()</code>, <code>begin()</code>, <code>end()</code>, <code>[]</code>等</li>
<li><code>AddTrack(const std::vector&lt;ObsInfo&gt;&amp; observations)</code>: 添加新轨迹</li>
<li><code>AddObservation(PtsId track_id, const ObsInfo&amp; obs)</code>: 向指定轨迹添加观测</li>
<li><code>GetTrackCount()</code>: 获取轨迹数量</li>
<li><code>GetObservationCount(PtsId track_id)</code>: 获取指定轨迹的观测数量</li>
<li><code>GetObservation(PtsId track_id, IndexT index)</code>: 获取指定轨迹的指定观测</li>
<li><code>GetValidTrackCount()</code>: 获取有效轨迹数量</li>
<li><code>IsNormalized()</code>: 获取归一化状态</li>
<li><code>SetNormalized(bool normalized)</code>: 设置归一化状态</li>
</ul>
</li>
</ul>
<h3 id="位姿">位姿 </h3>
<h4 id="relativerotation">RelativeRotation </h4>
<ul>
<li><strong>含义</strong>: 相对旋转关系</li>
<li><strong>用途</strong>: 存储两个相机视图间的相对旋转变换</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>i</code>: 源相机视图索引</li>
<li><code>j</code>: 目标相机视图索引</li>
<li><code>Rij</code>: 从视图i到视图j的相对旋转矩阵</li>
<li><code>weight</code>: 相对旋转的权重因子，默认为1.0</li>
</ul>
</li>
<li><strong>构造函数</strong>:
<ul>
<li><code>RelativeRotation(IndexT i_=0, IndexT j_=0, const Matrix3d&amp; Rij_=Matrix3d::Identity(), float weight_=1.0f)</code></li>
</ul>
</li>
</ul>
<h4 id="relativepose">RelativePose </h4>
<ul>
<li><strong>含义</strong>: 相对位姿表示</li>
<li><strong>用途</strong>: 存储两个相机视图间的相对位姿变换</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>i</code>: 源相机视图索引</li>
<li><code>j</code>: 目标相机视图索引</li>
<li><code>Rij</code>: 从视图i到视图j的相对旋转矩阵</li>
<li><code>tij</code>: 从视图i到视图j的相对平移向量</li>
<li><code>weight</code>: 位姿估计的权重因子，默认为1.0</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>RelativePose(IndexT i_=0, IndexT j_=0, const Matrix3d&amp; Rij_=Matrix3d::Identity(), const Vector3d&amp; tij_=Vector3d::Zero(), float weight_=1.0f)</code>: 构造函数</li>
<li><code>GetEssentialMatrix()</code>: 获取本质矩阵，返回3×3矩阵</li>
</ul>
</li>
</ul>
<h4 id="globalposes">GlobalPoses </h4>
<ul>
<li><strong>含义</strong>: 全局位姿信息</li>
<li><strong>用途</strong>: 存储所有视图的全局位姿信息</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>rotations</code>: 所有视图的旋转矩阵数组(<code>std::vector&lt;Matrix3d&gt;</code>)</li>
<li><code>translations</code>: 所有视图的平移向量数组(<code>std::vector&lt;Vector3d&gt;</code>)</li>
<li><code>est_info</code>: 估计状态信息</li>
<li><code>pose_format</code>: 位姿格式，默认为RwTw</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>Init(size_t num_views)</code>: 初始化位姿数据</li>
<li><code>GetPoseFormat()</code>: 获取当前位姿格式</li>
<li><code>SetPoseFormat(PoseFormat format)</code>: 设置位姿格式</li>
<li><code>GetRotation(ViewId original_id)</code>: 获取视图的旋转矩阵(使用原始ID)</li>
<li><code>GetTranslation(ViewId original_id)</code>: 获取视图的平移向量(使用原始ID)</li>
<li><code>GetRotationByEstId(ViewId est_id)</code>: 获取视图的旋转矩阵(使用估计ID)</li>
<li><code>GetTranslationByEstId(ViewId est_id)</code>: 获取视图的平移向量(使用估计ID)</li>
<li><code>SetRotation(ViewId original_id, const Matrix3d&amp; rotation)</code>: 设置视图的旋转矩阵</li>
<li><code>SetTranslation(ViewId original_id, const Vector3d&amp; translation)</code>: 设置视图的平移向量</li>
<li><code>SetRotationByEstId(ViewId est_id, const Matrix3d&amp; rotation)</code>: 使用估计ID设置旋转</li>
<li><code>SetTranslationByEstId(ViewId est_id, const Vector3d&amp; translation)</code>: 使用估计ID设置平移</li>
<li><code>Size()</code>: 获取位姿数量</li>
<li><code>GetEstInfo()</code>: 获取EstInfo引用</li>
<li><code>BuildEstInfoFromTracks(const TracksPtr&amp; tracks_ptr, const ViewId fixed_id=0)</code>: 从Tracks构建EstInfo</li>
</ul>
</li>
</ul>
<h3 id="相机模型">相机模型 </h3>
<h4 id="cameraintrinsics">CameraIntrinsics </h4>
<ul>
<li><strong>含义</strong>: 相机内参数据结构</li>
<li><strong>用途</strong>: 存储相机的内参信息</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>fx, fy</code>: x、y方向焦距</li>
<li><code>cx, cy</code>: x、y方向主点偏移</li>
<li><code>width, height</code>: 图像宽度和高度</li>
<li><code>model_type</code>: 相机模型类型，默认为针孔模型</li>
<li><code>distortion_type</code>: 畸变类型，默认为无畸变</li>
<li><code>radial_distortion</code>: 径向畸变参数数组</li>
<li><code>tangential_distortion</code>: 切向畸变参数数组</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>GetK(Matrix3d&amp; K)</code>: 获取相机内参矩阵</li>
<li><code>SetK(const Matrix3d&amp; K)</code>: 设置相机内参矩阵</li>
<li><code>SetCameraIntrinsics(...)</code>: 多种重载方法设置内参</li>
</ul>
</li>
</ul>
<h4 id="cameramodel">CameraModel </h4>
<ul>
<li><strong>含义</strong>: 相机模型完整定义</li>
<li><strong>用途</strong>: 包含相机内参和元数据信息</li>
<li><strong>成员变量</strong>:
<ul>
<li><code>intrinsics</code>: 相机内参</li>
<li><code>camera_make</code>: 相机制造商</li>
<li><code>camera_model</code>: 相机型号</li>
<li><code>serial_number</code>: 序列号</li>
</ul>
</li>
<li><strong>主要方法</strong>:
<ul>
<li><code>PixelToNormalized(const Vector2d&amp; pixel_coord)</code>: 像素坐标转归一化坐标</li>
<li><code>NormalizedToPixel(const Vector2d&amp; normalized_coord)</code>: 归一化坐标转像素坐标</li>
<li><code>SetKMat(const Matrix3d&amp; K)</code>: 设置相机内参矩阵</li>
<li><code>GetKMat(Matrix3d&amp; K)</code>: 获取相机内参矩阵</li>
<li><code>SetDistortionParams(...)</code>: 设置畸变参数</li>
<li><code>SetModelType(const CameraModelType&amp; model_type)</code>: 设置相机模型类型</li>
<li><code>SetCameraInfo(...)</code>: 设置相机元数据信息</li>
<li><code>SetCameraIntrinsics(...)</code>: 多种重载方法设置内参</li>
</ul>
</li>
</ul>
<h4 id="cameramodels">CameraModels </h4>
<ul>
<li><strong>定义</strong>: <code>using CameraModels = std::vector&lt;CameraModel&gt;;</code></li>
<li><strong>含义</strong>: 相机模型集合</li>
<li><strong>用途</strong>: 管理多个相机模型，支持单相机和多相机配置</li>
<li><strong>辅助函数</strong>:
<ul>
<li><code>GetCameraModel(CameraModels&amp; camera_models, ViewId view_id)</code>: 获取指定视图相机模型</li>
<li><code>GetCameraModel(const CameraModels&amp; camera_models, ViewId view_id)</code>: 常量版本</li>
</ul>
</li>
</ul>
<h3 id="其他实用类型">其他实用类型 </h3>
<h4 id="imagepaths">ImagePaths </h4>
<ul>
<li><strong>定义</strong>: <code>using ImagePaths = std::vector&lt;std::pair&lt;std::string, bool&gt;&gt;;</code></li>
<li><strong>含义</strong>: 图像路径集合，每项包含路径和可用性标志</li>
<li><strong>用途</strong>: 存储项目中使用的所有图像文件路径和状态</li>
</ul>
<h4 id="bearingvectors">BearingVectors </h4>
<ul>
<li><strong>定义</strong>: <code>using BearingVectors = Eigen::Matrix&lt;double,3,Eigen::Dynamic&gt;;</code></li>
<li><strong>含义</strong>: 归一化观测向量，3×N矩阵</li>
<li><strong>用途</strong>: 存储相机归一化坐标系中的方向向量</li>
</ul>
<h4 id="bearingpairs">BearingPairs </h4>
<ul>
<li><strong>定义</strong>: <code>using BearingPairs = std::vector&lt;Eigen::Matrix&lt;double,6,1&gt;&gt;;</code></li>
<li><strong>含义</strong>: 匹配的归一化观测向量对</li>
<li><strong>用途</strong>: 存储两个相机观测的配对方向向量，用于相对位姿估计</li>
</ul>
<h3 id="工具函数">工具函数 </h3>
<h4 id="位姿格式转换">位姿格式转换 </h4>
<ul>
<li>
<p><strong>RwTw_to_RwTc</strong>: 将位姿从RwTw格式转换为RwTc格式</p>
<ul>
<li>参数:
<ul>
<li><code>poses</code>: 需要转换的全局位姿</li>
<li><code>ref_id</code>: 参考视图ID(默认为0)</li>
<li><code>fixed_id</code>: 用于尺度归一化的固定视图ID(默认为最远视图)</li>
</ul>
</li>
<li>返回: 是否转换成功</li>
</ul>
</li>
<li>
<p><strong>RwTc_to_RwTw</strong>: 将位姿从RwTc格式转换为RwTw格式</p>
<ul>
<li>参数与RwTw_to_RwTc相同</li>
</ul>
</li>
<li>
<p><strong>ConvertPoseFormat</strong>: 在不同位姿格式之间转换</p>
<ul>
<li>参数:
<ul>
<li><code>poses</code>: 需要转换的全局位姿</li>
<li><code>target_format</code>: 目标位姿格式</li>
<li><code>ref_id</code>, <code>fixed_id</code>: 同上</li>
</ul>
</li>
<li>返回: 是否转换成功</li>
</ul>
</li>
</ul>
<h4 id="配置参数获取">配置参数获取 </h4>
<ul>
<li><strong>GetOptionAsIndexT</strong>: 从方法配置获取整数值</li>
<li><strong>GetOptionAsFloat</strong>: 从方法配置获取浮点值</li>
<li><strong>GetOptionAsBool</strong>: 从方法配置获取布尔值</li>
<li><strong>GetOptionAsString</strong>: 从方法配置获取字符串值</li>
</ul>
<hr>
<h2 id="b-常用工具函数">B. 常用工具函数 </h2>
<h3 id="配置参数获取-1">&gt;&gt;配置参数获取 </h3>
<p>以下工具函数用于从方法配置(MethodOptions)中安全地获取各种类型的参数值：</p>
<h4 id="getoptionasindext">GetOptionAsIndexT </h4>
<ul>
<li>功能: 从方法配置获取无符号整数值(IndexT/uint32_t)</li>
<li>参数:
<ul>
<li>options: const MethodOptions&amp; - 方法配置字典</li>
<li>key: const MethodParams&amp; - 参数键名</li>
<li>default_value: IndexT - 默认值，当键不存在或转换失败时返回，默认为0</li>
</ul>
</li>
<li>返回值: IndexT类型的参数值</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 获取迭代次数，默认为100</span>
IndexT max_iterations <span class="token operator">=</span> <span class="token function">GetOptionAsIndexT</span><span class="token punctuation">(</span>method_options_<span class="token punctuation">,</span> <span class="token string">"max_iterations"</span><span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
</ul>
<h4 id="getoptionasfloat">GetOptionAsFloat </h4>
<ul>
<li>功能: 从方法配置获取浮点数值</li>
<li>参数:
<ul>
<li>options: const MethodOptions&amp; - 方法配置字典</li>
<li>key: const MethodParams&amp; - 参数键名</li>
<li>default_value: float - 默认值，当键不存在或转换失败时返回，默认为0.0f</li>
</ul>
</li>
<li>返回值: float类型的参数值</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 获取阈值参数，默认为0.5</span>
<span class="token keyword keyword-float">float</span> threshold <span class="token operator">=</span> <span class="token function">GetOptionAsFloat</span><span class="token punctuation">(</span>method_options_<span class="token punctuation">,</span> <span class="token string">"threshold"</span><span class="token punctuation">,</span> <span class="token number">0.5f</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
</ul>
<h4 id="getoptionasbool">GetOptionAsBool </h4>
<ul>
<li>功能: 从方法配置获取布尔值</li>
<li>参数:
<ul>
<li>options: const MethodOptions&amp; - 方法配置字典</li>
<li>key: const MethodParams&amp; - 参数键名</li>
<li>default_value: bool - 默认值，当键不存在或转换失败时返回，默认为false</li>
</ul>
</li>
<li>返回值: bool类型的参数值</li>
<li>注意: 配置中的"true","yes","1","on"会被识别为true；"false","no","0","off"会被识别为false</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 获取是否启用特定功能，默认为false</span>
<span class="token keyword keyword-bool">bool</span> enable_feature <span class="token operator">=</span> <span class="token function">GetOptionAsBool</span><span class="token punctuation">(</span>method_options_<span class="token punctuation">,</span> <span class="token string">"enable_feature"</span><span class="token punctuation">,</span> <span class="token boolean">false</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
</ul>
<h4 id="getoptionasstring">GetOptionAsString </h4>
<ul>
<li>功能: 从方法配置获取字符串值</li>
<li>参数:
<ul>
<li>options: const MethodOptions&amp; - 方法配置字典</li>
<li>key: const MethodParams&amp; - 参数键名</li>
<li>default_value: const std::string&amp; - 默认值，当键不存在时返回，默认为空字符串</li>
</ul>
</li>
<li>返回值: std::string类型的参数值</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 获取算法模式，默认为"default"</span>
std<span class="token double-colon punctuation">::</span>string mode <span class="token operator">=</span> <span class="token function">GetOptionAsString</span><span class="token punctuation">(</span>method_options_<span class="token punctuation">,</span> <span class="token string">"algorithm_mode"</span><span class="token punctuation">,</span> <span class="token string">"default"</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
</ul>
<h3 id="-数据类型转换">&gt;&gt; 数据类型转换 </h3>
<h4 id="getdataptrt函数">GetDataPtr<t>函数 </t></h4>
<ul>
<li>功能: 安全地获取并转换数据指针到指定类型</li>
<li>模板参数:
<ul>
<li>T: 要转换到的目标数据类型</li>
</ul>
</li>
<li>参数:
<ul>
<li>data_ptr: const DataPtr&amp; - 输入数据指针</li>
</ul>
</li>
<li>返回值: 指向类型T的指针，如果转换失败则返回nullptr</li>
<li>工作原理:
<ol>
<li>检查输入是否为nullptr</li>
<li>使用dynamic_cast进行安全类型转换</li>
<li>如果转换成功，返回指向底层数据的指针</li>
</ol>
</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 转换数据指针到GlobalPoses类型</span>
<span class="token keyword keyword-auto">auto</span> poses <span class="token operator">=</span> <span class="token generic-function"><span class="token function">GetDataPtr</span><span class="token generic class-name"><span class="token operator">&lt;</span>GlobalPoses<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>data_ptr<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword keyword-if">if</span> <span class="token punctuation">(</span>poses<span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 现在可以安全地使用poses访问位姿数据</span>
    size_t num_poses <span class="token operator">=</span> poses<span class="token operator">-&gt;</span><span class="token function">Size</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre></li>
</ul>
<h4 id="string2indext">String2IndexT </h4>
<ul>
<li>功能: 将字符串转换为IndexT类型(uint32_t)</li>
<li>参数:
<ul>
<li>value: const std::string&amp; - 输入字符串</li>
<li>default_value: IndexT - 转换失败时返回的默认值，默认为0</li>
</ul>
</li>
<li>返回值: 转换后的IndexT值</li>
<li>使用场景: 用于配置文件字符串值到整数的安全转换</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>IndexT max_iter <span class="token operator">=</span> <span class="token function">String2IndexT</span><span class="token punctuation">(</span><span class="token string">"1000"</span><span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 返回1000</span>
IndexT invalid <span class="token operator">=</span> <span class="token function">String2IndexT</span><span class="token punctuation">(</span><span class="token string">"abc"</span><span class="token punctuation">,</span> <span class="token number">100</span><span class="token punctuation">)</span><span class="token punctuation">;</span>   <span class="token comment">// 返回100</span>
</code></pre></li>
</ul>
<h4 id="string2float">String2Float </h4>
<ul>
<li>功能: 将字符串转换为float类型</li>
<li>参数:
<ul>
<li>value: const std::string&amp; - 输入字符串</li>
<li>default_value: float - 转换失败时返回的默认值，默认为0.0f</li>
</ul>
</li>
<li>返回值: 转换后的float值</li>
<li>使用场景: 用于配置文件字符串值到浮点数的安全转换</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-float">float</span> threshold <span class="token operator">=</span> <span class="token function">String2Float</span><span class="token punctuation">(</span><span class="token string">"0.75"</span><span class="token punctuation">,</span> <span class="token number">0.5f</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 返回0.75</span>
<span class="token keyword keyword-float">float</span> invalid <span class="token operator">=</span> <span class="token function">String2Float</span><span class="token punctuation">(</span><span class="token string">"xyz"</span><span class="token punctuation">,</span> <span class="token number">0.5f</span><span class="token punctuation">)</span><span class="token punctuation">;</span>    <span class="token comment">// 返回0.5</span>
</code></pre></li>
</ul>
<h4 id="string2bool">String2Bool </h4>
<ul>
<li>功能: 将字符串转换为bool类型</li>
<li>参数:
<ul>
<li>str: const std::string&amp; - 输入字符串</li>
<li>default_value: bool - 转换失败时返回的默认值，默认为false</li>
</ul>
</li>
<li>返回值: 转换后的bool值</li>
<li>注意: "true","yes","1","on"会被转换为true；"false","no","0","off"会被转换为false</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-bool">bool</span> enabled <span class="token operator">=</span> <span class="token function">String2Bool</span><span class="token punctuation">(</span><span class="token string">"yes"</span><span class="token punctuation">,</span> <span class="token boolean">false</span><span class="token punctuation">)</span><span class="token punctuation">;</span>    <span class="token comment">// 返回true</span>
<span class="token keyword keyword-bool">bool</span> disabled <span class="token operator">=</span> <span class="token function">String2Bool</span><span class="token punctuation">(</span><span class="token string">"off"</span><span class="token punctuation">,</span> <span class="token boolean">true</span><span class="token punctuation">)</span><span class="token punctuation">;</span>    <span class="token comment">// 返回false</span>
<span class="token keyword keyword-bool">bool</span> invalid <span class="token operator">=</span> <span class="token function">String2Bool</span><span class="token punctuation">(</span><span class="token string">"maybe"</span><span class="token punctuation">,</span> <span class="token boolean">false</span><span class="token punctuation">)</span><span class="token punctuation">;</span>  <span class="token comment">// 返回默认值false</span>
</code></pre></li>
</ul>
<h3 id="位姿转换函数">位姿转换函数 </h3>
<h4 id="rwtw_to_rwtc">RwTw_to_RwTc </h4>
<ul>
<li>功能: 将位姿从RwTw格式转换为RwTc格式</li>
<li>参数:
<ul>
<li>poses: GlobalPoses&amp; - 需要转换的位姿数据</li>
<li>ref_id: ViewId - 参考视图ID，默认为0</li>
<li>fixed_id: ViewId - 固定视图ID，用于尺度标定，默认为最大值(不使用)</li>
</ul>
</li>
<li>返回值: bool - 转换是否成功</li>
<li>说明:
<ul>
<li>RwTw: [R|t]，其中t是相机中心在世界坐标系中的位置</li>
<li>RwTc: [R|t]，其中t是从世界坐标系原点到相机中心的向量</li>
</ul>
</li>
</ul>
<h4 id="rwtc_to_rwtw">RwTc_to_RwTw </h4>
<ul>
<li>功能: 将位姿从RwTc格式转换为RwTw格式</li>
<li>参数:
<ul>
<li>poses: GlobalPoses&amp; - 需要转换的位姿数据</li>
<li>ref_id: ViewId - 参考视图ID，默认为0</li>
<li>fixed_id: ViewId - 固定视图ID，用于尺度标定，默认为最大值(不使用)</li>
</ul>
</li>
<li>返回值: bool - 转换是否成功</li>
</ul>
<h4 id="convertposeformat">ConvertPoseFormat </h4>
<ul>
<li>功能: 在不同位姿格式之间转换</li>
<li>参数:
<ul>
<li>poses: GlobalPoses&amp; - 需要转换的位姿数据</li>
<li>target_format: PoseFormat - 目标位姿格式</li>
<li>ref_id: ViewId - 参考视图ID，默认为0</li>
<li>fixed_id: ViewId - 固定视图ID，默认为最大值(不使用)</li>
</ul>
</li>
<li>返回值: bool - 转换是否成功</li>
<li>使用示例:<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 将位姿从RwTc格式转换为RwTw格式</span>
<span class="token keyword keyword-bool">bool</span> success <span class="token operator">=</span> <span class="token function">ConvertPoseFormat</span><span class="token punctuation">(</span>global_poses<span class="token punctuation">,</span> PoseFormat<span class="token double-colon punctuation">::</span>RwTw<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></li>
</ul>
<hr>
<h2 id="c-插件命名建议">C. 插件命名建议 </h2>
<p>为了保持整个系统的一致性，建议按照以下规则命名您的插件：</p>
<h3 id="数据插件-data_功能">数据插件: data_[功能] </h3>
<ul>
<li>例如: data_tracks, data_camera_model, data_images</li>
<li>遵循这一命名约定可以帮助系统自动识别插件类型</li>
</ul>
<h3 id="方法插件-method_功能">方法插件: method_[功能] </h3>
<ul>
<li>例如: method_sift, method_matches2tracks, method_pose_estimation</li>
<li>功能部分应简明扼要地描述方法的用途</li>
</ul>
<h3 id="行为插件-behavior_功能">行为插件: behavior_[功能] </h3>
<ul>
<li>例如: behavior_sfm, behavior_localization</li>
<li>通常用于表示更大的处理管线或工作流程</li>
</ul>
<h4 id="注意事项">注意事项 </h4>
<ul>
<li>插件命名应使用小写字母和下划线</li>
<li>保持名称简短但具有描述性</li>
<li>类名可以使用大写字母开头的驼峰命名法，但GetType()返回的字符串应遵循上述命名规则</li>
</ul>
<hr>
<h2 id="d-性能分析">D. 性能分析 </h2>
<p>MethodPresetProfiler类提供的性能分析功能是PoSDK中监控和优化算法的重要工具。这个框架不仅记录了方法的执行时间和内存使用情况，还允许开发者记录算法内部各个阶段的详细性能数据。</p>
<h3 id="当前功能">当前功能 </h3>
<h4 id="基础指标收集">基础指标收集 </h4>
<ul>
<li>
<p><strong>执行时间追踪</strong>：</p>
<ul>
<li>总执行时间(total_time_ms)：从Build开始到结束的时间</li>
<li>计算时间(compute_time_ms)：Run()方法的执行时间</li>
<li>输入检查时间(input_check)：CheckInput的执行时间</li>
</ul>
</li>
<li>
<p><strong>内存监控</strong>：</p>
<ul>
<li>当前内存使用(current_memory_usage)：Build结束时的内存占用</li>
<li>峰值内存使用(peak_memory_usage)：Build过程中的峰值内存占用</li>
</ul>
</li>
</ul>
<h4 id="自定义阶段计时">自定义阶段计时 </h4>
<p>可以为算法中的关键阶段添加自定义计时：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-auto">auto</span> stage_start <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>high_resolution_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token comment">// 执行某个阶段的代码...</span>
<span class="token keyword keyword-auto">auto</span> stage_end <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>high_resolution_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
profile<span class="token punctuation">.</span>stage_timings<span class="token punctuation">[</span><span class="token string">"feature_extraction"</span><span class="token punctuation">]</span> <span class="token operator">=</span> 
    std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">duration</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-double">double</span><span class="token punctuation">,</span> std<span class="token double-colon punctuation">::</span>milli<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>stage_end <span class="token operator">-</span> stage_start<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">count</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre><h4 id="数据导出与报告">数据导出与报告 </h4>
<ul>
<li><strong>CSV文件导出</strong>：自动将性能数据追加到CSV文件，便于长期趋势分析</li>
<li><strong>控制台报告</strong>：Build结束后自动打印性能摘要信息</li>
</ul>
<h3 id="后续拓展方向">后续拓展方向 </h3>
<h4 id="1-高级内存分析">1. 高级内存分析 </h4>
<ul>
<li><strong>内存分配追踪</strong>：记录每个阶段的内存分配和释放</li>
<li><strong>内存泄漏检测</strong>：集成内存泄漏检测工具</li>
<li><strong>数据结构内存分析</strong>：分析关键数据结构的内存占用</li>
</ul>
<h4 id="2-算法复杂度评估">2. 算法复杂度评估 </h4>
<ul>
<li><strong>计算复杂度分析</strong>：自动估计算法的时间复杂度（如O(n²)、O(n log n)）</li>
<li><strong>规模扩展性测试</strong>：自动测试不同输入规模下的性能变化</li>
<li><strong>瓶颈识别</strong>：自动识别算法中的性能瓶颈</li>
</ul>
<h4 id="3-硬件利用率监控">3. 硬件利用率监控 </h4>
<ul>
<li><strong>CPU利用率</strong>：监控多核CPU的利用情况</li>
<li><strong>GPU资源监控</strong>：对GPU加速算法监控GPU内存和计算资源使用</li>
<li><strong>并行效率评估</strong>：评估并行算法的加速比和效率</li>
</ul>
<h4 id="4-可视化增强">4. 可视化增强 </h4>
<ul>
<li><strong>交互式性能仪表板</strong>：提供Web界面查看性能数据</li>
<li><strong>性能热图</strong>：可视化代码中的性能热点</li>
<li><strong>时间线可视化</strong>：直观显示各阶段执行时间和重叠情况</li>
</ul>
<h4 id="5-持续集成与监控">5. 持续集成与监控 </h4>
<ul>
<li><strong>性能回归测试</strong>：自动检测代码更改导致的性能下降</li>
<li><strong>阈值警报系统</strong>：当性能指标超出预设阈值时发出警报</li>
<li><strong>历史趋势分析</strong>：长期追踪性能变化趋势</li>
</ul>
<h3 id="使用方法">使用方法 </h3>
<p>要在您的插件中启用和使用这些性能分析功能：</p>
<ol>
<li>
<p><strong>继承正确的基类</strong>：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token keyword keyword-class">class</span> <span class="token class-name">MyMethod</span> <span class="token operator">:</span> <span class="token base-clause"><span class="token keyword keyword-public">public</span> <span class="token class-name">MethodPresetProfiler</span></span> <span class="token punctuation">{</span>
    <span class="token comment">// ...</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre></li>
<li>
<p><strong>配置启用分析</strong>：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code><span class="token comment">// 在构造函数中</span>
<span class="token class-name">MyMethod</span><span class="token double-colon punctuation">::</span><span class="token function">MyMethod</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 默认启用性能分析</span>
    <span class="token comment">// 也可以在配置文件中使用enable_profiling=false禁用</span>
    <span class="token function">InitializeLogDir</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span> <span class="token comment">// 设置日志目录</span>
<span class="token punctuation">}</span>
</code></pre></li>
<li>
<p><strong>添加自定义阶段计时</strong>：</p>
<pre data-role="codeBlock" data-info="cpp" class="language-cpp cpp"><code>DataPtr <span class="token class-name">MyMethod</span><span class="token double-colon punctuation">::</span><span class="token function">Run</span><span class="token punctuation">(</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// 阶段1计时</span>
    <span class="token keyword keyword-auto">auto</span> stage1_start <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>high_resolution_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    <span class="token comment">// 执行阶段1...</span>
    <span class="token keyword keyword-auto">auto</span> stage1_end <span class="token operator">=</span> std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span>high_resolution_clock<span class="token double-colon punctuation">::</span><span class="token function">now</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
    
    <span class="token comment">// 记录时间（将在Build中自动收集）</span>
    profile_<span class="token operator">-&gt;</span>stage_timings<span class="token punctuation">[</span><span class="token string">"stage1"</span><span class="token punctuation">]</span> <span class="token operator">=</span> 
        std<span class="token double-colon punctuation">::</span>chrono<span class="token double-colon punctuation">::</span><span class="token generic-function"><span class="token function">duration</span><span class="token generic class-name"><span class="token operator">&lt;</span><span class="token keyword keyword-double">double</span><span class="token punctuation">,</span> std<span class="token double-colon punctuation">::</span>milli<span class="token operator">&gt;</span></span></span><span class="token punctuation">(</span>stage1_end <span class="token operator">-</span> stage1_start<span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">count</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
        
    <span class="token comment">// 阶段2...</span>
    <span class="token comment">// ...</span>
    
    <span class="token keyword keyword-return">return</span> result<span class="token punctuation">;</span>
<span class="token punctuation">}</span>
</code></pre></li>
<li>
<p><strong>分析性能报告</strong>：<br>
查看输出的CSV文件和控制台报告，识别性能瓶颈和优化机会。</p>
</li>
</ol>
<p>这些性能分析功能将帮助开发者深入了解算法行为，指导优化方向，并在不同场景和数据集上评估算法的适用性。</p>

      </div>
      
      
    
    
    
    
    
    
  
    </body></html>