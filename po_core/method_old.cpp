/**
 * @file method_TwoViewOptimizer.cpp
 * @brief 双视图位姿优化器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "method_TwoViewOptimizer.hpp"

#include <boost/algorithm/string.hpp>
#include "relative_residuals.hpp"
#include "relative_pose.hpp"
#include "interfaces_robust_estimator.hpp"
#include <cmath>
#include <iostream>
#include <string>

// Ceres Solver头文件
#include "ceres/ceres.h"
#include "ceres/rotation.h"

// 三角化函数声明
Eigen::Vector3d triangulateOnePoint(const Eigen::Matrix3d &R, const Eigen::Vector3d &t,
                                    const Eigen::Vector3d &point1, const Eigen::Vector3d &point2);
Eigen::Vector3d triangulate2(const Eigen::Matrix3d &R, const Eigen::Vector3d &t,
                             const Eigen::Vector3d &point1, const Eigen::Vector3d &point2);

namespace PoSDK
{

    // -----------------------------------------------------------------------------
    // PPO OpenGV残差函数声明 - 实现在relative_residuals.cpp中
    // -----------------------------------------------------------------------------

    // -----------------------------------------------------------------------------
    // 损失函数实现
    // -----------------------------------------------------------------------------

    /**
     * @brief Huber损失函数
     */
    double huber_loss(double residual, double threshold)
    {
        double abs_residual = std::abs(residual);
        if (abs_residual <= threshold)
        {
            return 0.5 * residual * residual;
        }
        else
        {
            return threshold * (abs_residual - 0.5 * threshold);
        }
    }

    /**
     * @brief Huber损失函数的导数
     */
    double huber_loss_derivative(double residual, double threshold)
    {
        double abs_residual = std::abs(residual);
        if (abs_residual <= threshold)
        {
            return residual;
        }
        else
        {
            return threshold * (residual > 0 ? 1.0 : -1.0);
        }
    }

    // -----------------------------------------------------------------------------
    // MethodTwoViewOptimizer主类实现
    // -----------------------------------------------------------------------------

    MethodTwoViewOptimizer::MethodTwoViewOptimizer()
    {
        // 设置需要的输入数据包
        required_package_["data_sample"] = nullptr; // DataSample<BearingPairs>
        required_package_["data_map"] = nullptr;    // RelativePose初始估计

        // 初始化默认配置路径
        InitializeDefaultConfigPath();
    }

    DataPtr MethodTwoViewOptimizer::Run()
    {
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "TwoViewOptimizer started" << std::endl;
            DisplayConfigInfo();
        }

        // 1. 验证输入数据
        if (!ValidateInputData())
        {
            PO_LOG_ERR << "Input data validation failed" << std::endl;
            return nullptr;
        }

        // 2. 从data_map获取初始位姿估计
        auto pose_data = required_package_["data_map"];
        auto initial_pose_ptr = GetDataPtr<RelativePose>(pose_data);
        if (!initial_pose_ptr)
        {
            PO_LOG_ERR << "Failed to get initial RelativePose data" << std::endl;
            return nullptr;
        }
        RelativePose optimized_pose = *initial_pose_ptr;

        // 3. 转换bearing pairs到bearing vectors
        BearingVectors points1, points2;
        if (!ConvertBearingPairsToBearingVectors(points1, points2))
        {
            PO_LOG_ERR << "Failed to convert bearing pairs" << std::endl;
            return nullptr;
        }

        // 4. 检查是否有权重信息
        VectorXd *weights_ptr = nullptr;
        VectorXd weights;
        if (!prior_info_.empty() && prior_info_.find("weights") != prior_info_.end())
        {
            auto weights_data = std::dynamic_pointer_cast<DataMap<VectorXd>>(prior_info_["weights"]);
            if (weights_data && weights_data->GetMapPtr())
            {
                weights = *(weights_data->GetMapPtr());
                weights_ptr = &weights;
                PO_LOG(PO_LOG_VERBOSE) << "Using weights from prior_info_" << std::endl;
            }
        }

        // 5. 执行位姿优化
        if (!OptimizeRelativePose(points1, points2, optimized_pose, weights_ptr))
        {
            PO_LOG_ERR << "Failed to optimize relative pose" << std::endl;
            return nullptr;
        }

        // 6. 更新DataSample状态（优化后所有点都被认为是内点）
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (sample_ptr && !sample_ptr->empty())
        {
            auto inliers_ptr = std::make_shared<std::vector<size_t>>();
            inliers_ptr->reserve(sample_ptr->size());
            for (size_t i = 0; i < sample_ptr->size(); ++i)
            {
                inliers_ptr->push_back(i);
            }
            sample_ptr->SetBestInliers(inliers_ptr);
        }

        // 7. 返回优化后的位姿
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "TwoViewOptimizer completed successfully" << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Optimized rotation matrix:\n"
                                  << optimized_pose.Rij << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Optimized translation vector:\n"
                                  << optimized_pose.tij << std::endl;
        }

        return std::make_shared<DataMap<RelativePose>>(optimized_pose);
    }

    bool MethodTwoViewOptimizer::ValidateInputData()
    {
        auto sample_data = required_package_["data_sample"];
        auto pose_data = required_package_["data_map"];

        if (!sample_data || !pose_data)
        {
            PO_LOG_ERR << "Missing required input data" << std::endl;
            return false;
        }

        auto bearing_pairs_ptr = GetDataPtr<BearingPairs>(sample_data);
        if (!bearing_pairs_ptr || bearing_pairs_ptr->empty())
        {
            PO_LOG_ERR << "Empty bearing pairs data" << std::endl;
            return false;
        }

        if (bearing_pairs_ptr->size() < kMinNumPoints)
        {
            PO_LOG_ERR << "Insufficient points for optimization: got "
                       << bearing_pairs_ptr->size() << ", need at least " << kMinNumPoints << std::endl;
            return false;
        }

        return true;
    }

    bool MethodTwoViewOptimizer::ConvertBearingPairsToBearingVectors(
        BearingVectors &points1,
        BearingVectors &points2) const
    {
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (!sample_ptr || sample_ptr->empty())
        {
            return false;
        }

        const size_t num_points = sample_ptr->size();
        points1.resize(3, num_points);
        points2.resize(3, num_points);

        size_t i = 0;
        for (const auto &bearing_pair : *sample_ptr)
        {
            points1.col(i) = bearing_pair.template head<3>();
            points2.col(i) = bearing_pair.template tail<3>();
            ++i;
        }

        return true;
    }

    bool MethodTwoViewOptimizer::OptimizeRelativePose(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights)
    {
        std::string optimizer_type = GetOptionAsString("optimizer_type", "eigen_lm");
        auto optimizer = CreateOptimizer(optimizer_type);

        if (!optimizer)
        {
            PO_LOG_ERR << "Failed to create optimizer: " << optimizer_type << std::endl;
            return false;
        }

        // 设置优化器参数
        optimizer->SetMaxIterations(GetOptionAsIndexT("max_iterations", 50));
        optimizer->SetConvergenceThreshold(GetOptionAsFloat("convergence_threshold", 1e-8));
        optimizer->SetVerbose(log_level_ >= PO_LOG_VERBOSE);

        std::string residual_type = GetResidualType();
        std::string loss_type = GetLossType();

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "Using optimizer: " << optimizer_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using residual: " << residual_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using loss function: " << loss_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using 5-parameter optimization (3 Cayley + 2 translation with ||t||=1 constraint)" << std::endl;
        }

        return optimizer->Optimize(points1, points2, pose, weights, residual_type, loss_type);
    }

    std::string MethodTwoViewOptimizer::GetResidualType() const
    {
        return GetOptionAsString("residual_type", "ppo_opengv");
    }

    std::string MethodTwoViewOptimizer::GetLossType() const
    {
        return GetOptionAsString("loss_type", "huber");
    }

    std::unique_ptr<TwoViewOptimizerBase> MethodTwoViewOptimizer::CreateOptimizer(const std::string &optimizer_type)
    {
        if (boost::iequals(optimizer_type, "eigen_lm"))
        {
            return std::make_unique<EigenLMOptimizer>();
        }
        else if (boost::iequals(optimizer_type, "gauss_newton"))
        {
            return std::make_unique<GaussNewtonOptimizer>();
        }
        else if (boost::iequals(optimizer_type, "dog_leg"))
        {
            return std::make_unique<DogLegOptimizer>();
        }
        else if (boost::iequals(optimizer_type, "ceres"))
        {
            return std::make_unique<CeresOptimizer>();
        }
        else
        {
            PO_LOG_ERR << "Unknown optimizer type: " << optimizer_type << std::endl;
            return nullptr;
        }
    }

    // -----------------------------------------------------------------------------
    // Eigen LM优化器实现
    // -----------------------------------------------------------------------------

    namespace
    {
        // 使用types.hpp中统一的Cayley参数化函数
        using types::CayleyToRotation;
        using types::RotationToCayley;

        // 残差函数计算器
        struct TwoViewResidualFunctor
        {
            typedef double Scalar;
            typedef Eigen::VectorXd InputType;
            typedef Eigen::VectorXd ValueType;
            typedef Eigen::MatrixXd JacobianType;

            enum
            {
                InputsAtCompileTime = Eigen::Dynamic,
                ValuesAtCompileTime = Eigen::Dynamic
            };

            int m_inputs, m_values;
            mutable int iteration_count_;     // 添加迭代计数器
            mutable double initial_t3_sign_;  // 记录初始t3的符号
            mutable VectorXd prev_x_;         // 上一次的参数向量
            mutable bool prev_x_initialized_; // 是否已初始化
            bool verbose_;                    // 是否显示调试信息

            TwoViewResidualFunctor(
                const BearingVectors &points1,
                const BearingVectors &points2,
                const VectorXd *weights,
                const std::string &residual_type,
                const std::string &loss_type,
                double initial_t3_sign,
                bool verbose = true)
                : m_inputs(5),
                  m_values(GetResidualDimension(residual_type, points1.cols())),
                  points1_(points1),
                  points2_(points2),
                  weights_(weights),
                  residual_type_(residual_type),
                  loss_type_(loss_type),
                  huber_threshold_(GetHuberThreshold()),
                  iteration_count_(0),
                  initial_t3_sign_(initial_t3_sign),
                  prev_x_initialized_(false),
                  verbose_(verbose) {}

            int inputs() const { return m_inputs; }
            int values() const { return m_values; }

            int operator()(const VectorXd &x, VectorXd &fvec) const
            {
                iteration_count_++;

                // 从5个参数向量恢复位姿
                // x[0], x[1]: 平移的前两个分量
                // x[2], x[3], x[4]: Cayley参数

                double t1 = x(0);
                double t2 = x(1);

                // 通过归一化约束计算第三个分量：||t|| = 1
                double t_squared = t1 * t1 + t2 * t2;
                if (t_squared >= 1.0)
                {
                    // 边界情况：将前两个分量归一化
                    double norm = std::sqrt(t_squared);
                    t1 /= norm;
                    t2 /= norm;
                    t_squared = t1 * t1 + t2 * t2;
                }
                double t3 = initial_t3_sign_ * std::sqrt(1.0 - t_squared); // 保持初始符号

                Vector3d translation(t1, t2, t3);
                Vector3d cayley = x.segment<3>(2);

                // 检查Cayley参数是否合理
                double cayley_norm = cayley.norm();
                if (cayley_norm > 3.14159) // 约束Cayley参数避免奇异
                {
                    cayley = cayley * (3.14159 / cayley_norm);
                }

                Matrix3d rotation = CayleyToRotation(cayley);

                RelativePose pose;
                pose.Rij = rotation;
                pose.tij = translation;

                // 计算残差
                VectorXd residuals;
                if (boost::iequals(residual_type_, "ppo_opengv"))
                {
                    residuals = residual_ppo_opengv(points1_, points2_, pose, weights_);
                }
                else
                {
                    // 使用其他残差函数（从relative_residuals.hpp）
                    residuals = ComputeOtherResidual(pose);
                }

                // 应用损失函数
                ApplyLossFunction(residuals, fvec);

                // 计算残差统计信息
                double cost = fvec.squaredNorm();

                // 对于6D残差向量，需要特殊处理统计信息
                double mean_residual, max_residual, min_residual;
                double rms_residual;

                if (boost::iequals(residual_type_, "ppo_opengv"))
                {
                    // 对于6D残差：计算每个点的总残差
                    size_t num_points = residuals.size() / 6;
                    VectorXd point_residuals(num_points);
                    for (size_t i = 0; i < num_points; ++i)
                    {
                        Vector3d diff1 = residuals.segment<3>(6 * i);
                        Vector3d diff2 = residuals.segment<3>(6 * i + 3);
                        point_residuals(i) = std::sqrt(diff1.squaredNorm() + diff2.squaredNorm());
                    }
                    mean_residual = point_residuals.mean();
                    max_residual = point_residuals.maxCoeff();
                    min_residual = point_residuals.minCoeff();
                    rms_residual = std::sqrt(point_residuals.squaredNorm() / num_points);
                }
                else
                {
                    // 对于标量残差
                    mean_residual = residuals.mean();
                    max_residual = residuals.maxCoeff();
                    min_residual = residuals.minCoeff();
                    rms_residual = std::sqrt(residuals.squaredNorm() / residuals.size());
                }

                // 计算参数变化量（除了第一次）
                double dx_norm = 0.0;

                if (prev_x_initialized_)
                {
                    dx_norm = (x - prev_x_).norm();
                }
                else
                {
                    prev_x_initialized_ = true;
                }
                prev_x_ = x;

                // 每5次迭代或第1次输出调试信息（合并为一行），只有verbose模式下才显示
                if (verbose_ && (iteration_count_ == 1 || iteration_count_ % 5 == 0))
                {
                    std::cout << "[TwoViewOptimizer] iter=" << iteration_count_
                              << ", cost=" << std::scientific << std::setprecision(6) << cost
                              << ", dx=" << std::scientific << std::setprecision(3) << dx_norm
                              << ", residual[mean/max/min/rms]=[" << std::fixed << std::setprecision(6)
                              << mean_residual << "/" << max_residual << "/" << min_residual << "/" << rms_residual << "]"
                              << ", t=[" << std::fixed << std::setprecision(6)
                              << t1 << "," << t2 << "," << t3 << "], |t|=" << translation.norm()
                              << ", cayley=[" << cayley(0) << "," << cayley(1) << "," << cayley(2) << "]" << std::endl;
                }

                return 0;
            }

        private:
            VectorXd ComputeOtherResidual(const RelativePose &pose) const
            {
                // 支持所有残差函数类型
                if (boost::iequals(residual_type_, "sampson"))
                {
                    return residual_sampson(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ppo"))
                {
                    return residual_PPO(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ppo_angle"))
                {
                    return residual_PPO_angle(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ba"))
                {
                    return residual_BA(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "coplanar"))
                {
                    return residual_coplanar(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "kneip"))
                {
                    return residual_Kneip(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "opengv"))
                {
                    return residual_opengv(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ligt"))
                {
                    return residual_LiGT(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "lirt"))
                {
                    return residual_LiRT(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ppog"))
                {
                    return residual_PPOG(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ppo_invd"))
                {
                    return residual_PPO_invd(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ligt_direct"))
                {
                    return residual_LiGT_direct(points1_, points2_, pose, weights_);
                }
                else if (boost::iequals(residual_type_, "ligt_d3"))
                {
                    return residual_LiGT_d3(points1_, points2_, pose, weights_);
                }
                else
                {
                    // 默认使用PPO OpenGV
                    std::cout << "[Warning] Unknown residual type: " << residual_type_
                              << ", falling back to ppo_opengv" << std::endl;
                    return residual_ppo_opengv(points1_, points2_, pose, weights_);
                }
            }

            void ApplyLossFunction(const VectorXd &residuals, VectorXd &fvec) const
            {
                fvec.resize(residuals.size());

                if (boost::iequals(loss_type_, "huber"))
                {
                    if (boost::iequals(residual_type_, "ppo_opengv"))
                    {
                        // 对于6D残差：对每个3D分量分别应用Huber损失
                        size_t num_points = residuals.size() / 6;
                        for (size_t i = 0; i < num_points; ++i)
                        {
                            Vector3d diff1 = residuals.segment<3>(6 * i);
                            Vector3d diff2 = residuals.segment<3>(6 * i + 3);

                            double norm1 = diff1.norm();
                            double norm2 = diff2.norm();

                            // 应用Huber损失到每个3D分量的范数
                            double huber_factor1 = std::sqrt(huber_loss(norm1, huber_threshold_)) / (norm1 + 1e-12);
                            double huber_factor2 = std::sqrt(huber_loss(norm2, huber_threshold_)) / (norm2 + 1e-12);

                            fvec.segment<3>(6 * i) = huber_factor1 * diff1;
                            fvec.segment<3>(6 * i + 3) = huber_factor2 * diff2;
                        }
                    }
                    else
                    {
                        // 对于标量残差：直接应用Huber损失
                        for (int i = 0; i < residuals.size(); ++i)
                        {
                            fvec(i) = std::sqrt(huber_loss(residuals(i), huber_threshold_));
                        }
                    }
                }
                else if (boost::iequals(loss_type_, "cauchy"))
                {
                    double c = huber_threshold_;
                    if (boost::iequals(residual_type_, "ppo_opengv"))
                    {
                        // 对于6D残差：对每个3D分量分别应用Cauchy损失
                        size_t num_points = residuals.size() / 6;
                        for (size_t i = 0; i < num_points; ++i)
                        {
                            Vector3d diff1 = residuals.segment<3>(6 * i);
                            Vector3d diff2 = residuals.segment<3>(6 * i + 3);

                            double norm1 = diff1.norm();
                            double norm2 = diff2.norm();

                            // 应用Cauchy损失到每个3D分量的范数
                            double cauchy_factor1 = c * std::sqrt(std::log(1.0 + (norm1 * norm1) / (c * c))) / (norm1 + 1e-12);
                            double cauchy_factor2 = c * std::sqrt(std::log(1.0 + (norm2 * norm2) / (c * c))) / (norm2 + 1e-12);

                            fvec.segment<3>(6 * i) = cauchy_factor1 * diff1;
                            fvec.segment<3>(6 * i + 3) = cauchy_factor2 * diff2;
                        }
                    }
                    else
                    {
                        // 对于标量残差：直接应用Cauchy损失
                        for (int i = 0; i < residuals.size(); ++i)
                        {
                            double r = residuals(i);
                            fvec(i) = c * std::sqrt(std::log(1.0 + (r * r) / (c * c)));
                        }
                    }
                }
                else // l2 loss
                {
                    fvec = residuals;
                }
            }

            double GetHuberThreshold() const
            {
                // 根据残差类型调整阈值
                if (boost::iequals(residual_type_, "ppo_opengv"))
                {
                    return 0.01; // 6D向量残差，基于坐标差，阈值较大
                }
                else if (boost::iequals(residual_type_, "ppo_angle"))
                {
                    // PPO_angle残差使用角度误差(1-cos(θ))，0.1度对应的阈值
                    const double max_angle_deviation_deg = 0.1;
                    const double max_angle_deviation_rad = max_angle_deviation_deg * M_PI / 180.0;
                    return 1.0 - std::cos(max_angle_deviation_rad); // 约为 1.5e-5
                }
                else if (boost::iequals(residual_type_, "ppo"))
                {
                    // PPO残差为弦长 ||u-v|| = 2*sin(theta/2)，也基于角度
                    // 使用与ppo_angle相同的角度阈值
                    const double max_angle_deviation_deg = 0.1;
                    const double max_angle_deviation_rad = max_angle_deviation_deg * M_PI / 180.0;
                    return 2.0 * std::sin(max_angle_deviation_rad / 2.0); // 约为 0.0017
                }
                else
                {
                    return 1e-3; // 其他残差的默认阈值
                }
            }

            static int GetResidualDimension(const std::string &residual_type, int num_points)
            {
                // 对于Eigen LM：残差维度处理
                if (boost::iequals(residual_type, "ppo_opengv"))
                {
                    return 6 * num_points; // 6D残差向量
                }
                else
                {
                    return num_points; // 标量残差
                }
            }

            const BearingVectors &points1_;
            const BearingVectors &points2_;
            const VectorXd *weights_;
            std::string residual_type_;
            std::string loss_type_;
            double huber_threshold_;
        };
    }

    bool EigenLMOptimizer::Optimize(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        std::cout << "\n[TwoViewOptimizer] Starting Eigen LM optimization..." << std::endl;
        std::cout << "Residual type: " << residual_type << ", Loss type: " << loss_type << std::endl;

        const size_t num_points = points1.cols();

        // 检查是否为BA或OpenGV残差（需要pose+3D点联合优化）
        bool use_bundle_adjustment = (residual_type == "ba" || residual_type == "opengv");

        if (use_bundle_adjustment)
        {
            std::cout << "Using Bundle Adjustment mode (pose + 3D points optimization)" << std::endl;
            // 对于Eigen LM，我们暂时使用原有实现但特别标注
            std::cout << "Warning: BA/OpenGV residuals with Eigen LM currently use pose-only optimization." << std::endl;
            std::cout << "For proper Bundle Adjustment, consider using Ceres optimizer." << std::endl;
        }
        else
        {
            std::cout << "Using Pose-only optimization mode" << std::endl;
        }

        std::cout << "Initial pose:" << std::endl;
        std::cout << "  Rotation:\n"
                  << pose.Rij << std::endl;
        std::cout << "  Translation: " << pose.tij.transpose() << ", norm=" << pose.tij.norm() << std::endl;

        // 初始化5个参数向量
        VectorXd x(5);

        // 确保平移向量归一化
        Vector3d t_normalized = pose.tij.normalized();
        x(0) = t_normalized(0); // t1
        x(1) = t_normalized(1); // t2
        // 记录初始t3的符号
        double initial_t3_sign = (t_normalized(2) >= 0) ? 1.0 : -1.0;

        std::cout << "Initial 5-parameter vector:" << std::endl;
        std::cout << "  t1=" << x(0) << ", t2=" << x(1) << ", t3_sign=" << initial_t3_sign << std::endl;

        x.segment<3>(2) = RotationToCayley(pose.Rij); // Cayley参数
        std::cout << "  cayley=[" << x(2) << "," << x(3) << "," << x(4) << "]" << std::endl;

        // 计算初始残差以作为基准（静默模式，不显示调试信息）
        TwoViewResidualFunctor initial_functor(points1, points2, weights, residual_type, loss_type, initial_t3_sign, false);
        VectorXd initial_fvec;
        initial_functor(x, initial_fvec);
        double initial_cost = initial_fvec.squaredNorm();

        // 计算初始残差统计
        double initial_mean_residual = 0.0;
        if (boost::iequals(residual_type, "ppo_opengv"))
        {
            // 对于6D残差：计算每个点的总残差
            size_t num_points = points1.cols();
            VectorXd initial_residuals;
            if (boost::iequals(residual_type, "ppo_opengv"))
            {
                initial_residuals = residual_ppo_opengv(points1, points2, pose, weights);
            }

            VectorXd point_residuals(num_points);
            for (size_t i = 0; i < num_points; ++i)
            {
                Vector3d diff1 = initial_residuals.segment<3>(6 * i);
                Vector3d diff2 = initial_residuals.segment<3>(6 * i + 3);
                point_residuals(i) = std::sqrt(diff1.squaredNorm() + diff2.squaredNorm());
            }
            initial_mean_residual = point_residuals.mean();
        }

        std::cout << "Initial cost: " << std::scientific << initial_cost
                  << ", mean_residual: " << std::fixed << std::setprecision(6) << initial_mean_residual << std::endl;

        // 创建残差函数对象（显示调试信息）
        TwoViewResidualFunctor functor(points1, points2, weights, residual_type, loss_type, initial_t3_sign, true);
        Eigen::NumericalDiff<TwoViewResidualFunctor> numDiff(functor);
        Eigen::LevenbergMarquardt<Eigen::NumericalDiff<TwoViewResidualFunctor>> lm(numDiff);

        // 设置LM参数
        lm.resetParameters();

        // 尝试从配置文件读取Eigen LM专用参数，如果没有则使用convergence_threshold_作为默认值
        double ftol = convergence_threshold_; // 默认值
        double xtol = convergence_threshold_; // 默认值
        int maxfev = max_iterations_ * 20;    // 默认值

        // 这里需要访问配置，但由于我们在优化器类中，没有直接访问配置的方法
        // 暂时使用默认值，后续可以考虑通过参数传递或其他方式获取配置

        lm.parameters.ftol = ftol;
        lm.parameters.xtol = xtol;
        lm.parameters.maxfev = maxfev;

        std::cout << "\n[TwoViewOptimizer] Starting LM iterations..." << std::endl;
        std::cout << "LM parameters: ftol=" << std::scientific << lm.parameters.ftol
                  << ", xtol=" << lm.parameters.xtol
                  << ", maxfev=" << lm.parameters.maxfev
                  << ", max_iterations=" << max_iterations_ << std::endl;

        // 执行优化
        Eigen::LevenbergMarquardtSpace::Status status = lm.minimize(x);

        // 计算最终残差（静默模式，避免重复显示）
        TwoViewResidualFunctor final_functor(points1, points2, weights, residual_type, loss_type, initial_t3_sign, false);
        VectorXd final_fvec;
        final_functor(x, final_fvec);
        double final_cost = final_fvec.squaredNorm();

        // 计算最终残差统计（重复用到的逻辑可以提取为函数）
        double final_mean_residual = 0.0;
        if (boost::iequals(residual_type, "ppo_opengv"))
        {
            // 重新计算原始残差（未经损失函数处理）
            RelativePose final_pose;
            final_pose.Rij = CayleyToRotation(x.segment<3>(2));
            final_pose.tij = Vector3d(x(0), x(1), initial_t3_sign * std::sqrt(1.0 - x(0) * x(0) - x(1) * x(1)));

            VectorXd final_residuals = residual_ppo_opengv(points1, points2, final_pose, weights);
            size_t num_points = points1.cols();
            VectorXd point_residuals(num_points);
            for (size_t i = 0; i < num_points; ++i)
            {
                Vector3d diff1 = final_residuals.segment<3>(6 * i);
                Vector3d diff2 = final_residuals.segment<3>(6 * i + 3);
                point_residuals(i) = std::sqrt(diff1.squaredNorm() + diff2.squaredNorm());
            }
            final_mean_residual = point_residuals.mean();
        }

        std::cout << "\n[TwoViewOptimizer] Optimization finished." << std::endl;
        std::cout << "Final cost: " << std::scientific << final_cost
                  << ", mean_residual: " << std::fixed << std::setprecision(6) << final_mean_residual << std::endl;
        std::cout << "Cost improvement: " << std::scientific << (initial_cost - final_cost) << std::endl;
        std::cout << "Cost ratio (final/initial): " << std::fixed << std::setprecision(6) << (final_cost / initial_cost) << std::endl;
        std::cout << "Residual improvement: " << std::fixed << std::setprecision(6)
                  << "mean_residual ratio=" << (final_mean_residual / (initial_mean_residual + 1e-12)) << std::endl;

        // 从5个参数更新位姿
        double t1 = x(0);
        double t2 = x(1);

        // 重新计算第三个分量并归一化，保持初始符号
        double t_squared = t1 * t1 + t2 * t2;
        if (t_squared >= 1.0)
        {
            double norm = std::sqrt(t_squared);
            t1 /= norm;
            t2 /= norm;
            t_squared = t1 * t1 + t2 * t2;
        }
        double t3 = initial_t3_sign * std::sqrt(1.0 - t_squared);

        pose.tij = Vector3d(t1, t2, t3);
        pose.Rij = CayleyToRotation(x.segment<3>(2));

        std::cout << "Final pose:" << std::endl;
        std::cout << "  Rotation:\n"
                  << pose.Rij << std::endl;
        std::cout << "  Translation: " << pose.tij.transpose() << ", norm=" << pose.tij.norm() << std::endl;

        bool success = (status == Eigen::LevenbergMarquardtSpace::RelativeReductionTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::RelativeErrorTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::RelativeErrorAndReductionTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::XtolTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::FtolTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::CosinusTooSmall);

        std::string status_desc;
        switch (status)
        {
        case Eigen::LevenbergMarquardtSpace::RelativeReductionTooSmall:
            status_desc = "RelativeReductionTooSmall (收敛成功)";
            break;
        case Eigen::LevenbergMarquardtSpace::RelativeErrorTooSmall:
            status_desc = "RelativeErrorTooSmall (收敛成功)";
            break;
        case Eigen::LevenbergMarquardtSpace::RelativeErrorAndReductionTooSmall:
            status_desc = "RelativeErrorAndReductionTooSmall (收敛成功)";
            break;
        case Eigen::LevenbergMarquardtSpace::XtolTooSmall:
            status_desc = "XtolTooSmall (参数变化太小，收敛成功)";
            break;
        case Eigen::LevenbergMarquardtSpace::FtolTooSmall:
            status_desc = "FtolTooSmall (函数值变化太小，收敛成功)";
            break;
        case Eigen::LevenbergMarquardtSpace::CosinusTooSmall:
            status_desc = "CosinusTooSmall (梯度太小，收敛成功)";
            break;
        case Eigen::LevenbergMarquardtSpace::TooManyFunctionEvaluation:
            status_desc = "TooManyFunctionEvaluation (达到最大迭代次数)";
            break;
        case Eigen::LevenbergMarquardtSpace::ImproperInputParameters:
            status_desc = "ImproperInputParameters (输入参数错误)";
            break;
        default:
            status_desc = "Unknown status";
            break;
        }
        std::cout << "Eigen LM optimization finished with status: " << status
                  << " (" << status_desc << ")"
                  << " after " << lm.iter << " iterations" << std::endl;
        std::cout << "Optimization " << (success ? "成功" : "失败") << std::endl;

        return success;
    }

    // -----------------------------------------------------------------------------
    // 其他优化器的占位实现
    // -----------------------------------------------------------------------------

    bool GaussNewtonOptimizer::Optimize(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        // TODO: 实现Gauss-Newton优化器
        std::cerr << "GaussNewtonOptimizer not yet implemented" << std::endl;
        return false;
    }

    bool DogLegOptimizer::Optimize(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        // TODO: 实现Dog Leg优化器
        std::cerr << "DogLegOptimizer not yet implemented" << std::endl;
        return false;
    }

    namespace
    {
        // Bundle Adjustment Cost Function (pose + 3D point optimization)
        struct BundleAdjustmentCostFunction
        {
            BundleAdjustmentCostFunction(const Eigen::Vector3d &bearing1,
                                         const Eigen::Vector3d &bearing2)
                : bearing1_(bearing1), bearing2_(bearing2)
            {
            }

            template <typename T>
            bool operator()(const T *const pose_params, const T *const point3d, T *residuals) const
            {
                // 1. Recover pose (R, t) from params
                T R[9], t[3];
                t[0] = pose_params[0];
                t[1] = pose_params[1];
                T t_squared = t[0] * t[0] + t[1] * t[1];
                T t3_squared = T(1.0) - t_squared;
                if (t3_squared < T(0.0))
                    t3_squared = T(0.0);
                t[2] = ceres::sqrt(t3_squared);
                if (pose_params[5] < T(0.0))
                    t[2] = -t[2];

                const T *cayley = &pose_params[2];
                CayleyToRotationMatrix(cayley, R);

                // 2. 按照residual_BA的逻辑：
                //    reproj_v1 = points3D (已经是相机1坐标系中的点)
                //    reproj_v2 = R.transpose() * (points3D - t)
                T reproj_v1[3] = {point3d[0], point3d[1], point3d[2]};

                // 变换到相机2：R.transpose() * (point3d - t)
                T p_minus_t[3] = {point3d[0] - t[0], point3d[1] - t[1], point3d[2] - t[2]};
                T reproj_v2[3];
                // R^T multiplication (R是row-major存储)
                reproj_v2[0] = R[0] * p_minus_t[0] + R[3] * p_minus_t[1] + R[6] * p_minus_t[2];
                reproj_v2[1] = R[1] * p_minus_t[0] + R[4] * p_minus_t[1] + R[7] * p_minus_t[2];
                reproj_v2[2] = R[2] * p_minus_t[0] + R[5] * p_minus_t[1] + R[8] * p_minus_t[2];

                // 3. 归一化：reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1(2, i)
                const T epsilon = T(1e-12);
                if (ceres::abs(reproj_v1[2]) < epsilon || ceres::abs(reproj_v2[2]) < epsilon)
                {
                    // 设置大残差并返回
                    for (int i = 0; i < 6; ++i)
                    {
                        residuals[i] = T(1e6);
                    }
                    return true;
                }

                reproj_v1[0] /= reproj_v1[2];
                reproj_v1[1] /= reproj_v1[2];
                reproj_v1[2] /= reproj_v1[2]; // = 1.0

                reproj_v2[0] /= reproj_v2[2];
                reproj_v2[1] /= reproj_v2[2];
                reproj_v2[2] /= reproj_v2[2]; // = 1.0

                // 4. 归一化原始bearing vectors：v1.col(i) / v1(2, i)
                T v1[3] = {T(bearing1_(0)) / T(bearing1_(2)),
                           T(bearing1_(1)) / T(bearing1_(2)),
                           T(1.0)};

                T v2[3] = {T(bearing2_(0)) / T(bearing2_(2)),
                           T(bearing2_(1)) / T(bearing2_(2)),
                           T(1.0)};

                // 5. 计算6D残差向量：[v1.col(i) / v1(2, i) - reproj_v1.col(i)；v2.col(i) / v2(2, i) - reproj_v2.col(i)]
                T diff1[3] = {v1[0] - reproj_v1[0], v1[1] - reproj_v1[1], v1[2] - reproj_v1[2]};
                T diff2[3] = {v2[0] - reproj_v2[0], v2[1] - reproj_v2[1], v2[2] - reproj_v2[2]};

                // 存储6D残差向量：[diff1; diff2]
                residuals[0] = diff1[0];
                residuals[1] = diff1[1];
                residuals[2] = diff1[2];
                residuals[3] = diff2[0];
                residuals[4] = diff2[1];
                residuals[5] = diff2[2];

                return true;
            }

        private:
            template <typename T>
            void CayleyToRotationMatrix(const T *cayley, T *rotation) const
            {
                T c1 = cayley[0], c2 = cayley[1], c3 = cayley[2];
                T c1_sq = c1 * c1, c2_sq = c2 * c2, c3_sq = c3 * c3;
                T denominator = T(1.0) + c1_sq + c2_sq + c3_sq;

                rotation[0] = (T(1.0) + c1_sq - c2_sq - c3_sq) / denominator;
                rotation[1] = (T(2.0) * (c1 * c2 - c3)) / denominator;
                rotation[2] = (T(2.0) * (c1 * c3 + c2)) / denominator;
                rotation[3] = (T(2.0) * (c1 * c2 + c3)) / denominator;
                rotation[4] = (T(1.0) - c1_sq + c2_sq - c3_sq) / denominator;
                rotation[5] = (T(2.0) * (c2 * c3 - c1)) / denominator;
                rotation[6] = (T(2.0) * (c1 * c3 - c2)) / denominator;
                rotation[7] = (T(2.0) * (c2 * c3 + c1)) / denominator;
                rotation[8] = (T(1.0) - c1_sq - c2_sq + c3_sq) / denominator;
            }

            Eigen::Vector3d bearing1_, bearing2_;
        };

        // OpenGV Bundle Cost Function (pose + 3D point optimization with angular residuals)
        struct OpenGVBundleCostFunction
        {
            OpenGVBundleCostFunction(const Eigen::Vector3d &bearing1,
                                     const Eigen::Vector3d &bearing2)
                : bearing1_(bearing1), bearing2_(bearing2)
            {
            }

            template <typename T>
            bool operator()(const T *const pose_params, const T *const point3d, T *residuals) const
            {
                // 1. Recover pose (R, t) from params
                T R[9], t[3];
                t[0] = pose_params[0];
                t[1] = pose_params[1];
                T t_squared = t[0] * t[0] + t[1] * t[1];
                T t3_squared = T(1.0) - t_squared;
                if (t3_squared < T(0.0))
                    t3_squared = T(0.0);
                t[2] = ceres::sqrt(t3_squared);
                if (pose_params[5] < T(0.0))
                    t[2] = -t[2];

                const T *cayley = &pose_params[2];
                CayleyToRotationMatrix(cayley, R);

                // 2. 按照residual_opengv的逻辑：计算重投影的方向向量
                T reproj_v1[3] = {point3d[0], point3d[1], point3d[2]};

                // 变换到相机2：R.transpose() * (point3d - t)
                T p_minus_t[3] = {point3d[0] - t[0], point3d[1] - t[1], point3d[2] - t[2]};
                T reproj_v2[3];
                // R^T multiplication (R是row-major存储)
                reproj_v2[0] = R[0] * p_minus_t[0] + R[3] * p_minus_t[1] + R[6] * p_minus_t[2];
                reproj_v2[1] = R[1] * p_minus_t[0] + R[4] * p_minus_t[1] + R[7] * p_minus_t[2];
                reproj_v2[2] = R[2] * p_minus_t[0] + R[5] * p_minus_t[1] + R[8] * p_minus_t[2];

                // 3. 归一化为方向向量
                T norm1 = ceres::sqrt(reproj_v1[0] * reproj_v1[0] + reproj_v1[1] * reproj_v1[1] + reproj_v1[2] * reproj_v1[2]);
                T norm2 = ceres::sqrt(reproj_v2[0] * reproj_v2[0] + reproj_v2[1] * reproj_v2[1] + reproj_v2[2] * reproj_v2[2]);

                const T epsilon = T(1e-12);
                if (norm1 < epsilon || norm2 < epsilon)
                {
                    // 设置大残差并返回
                    for (int i = 0; i < 6; ++i)
                    {
                        residuals[i] = T(1e6);
                    }
                    return true;
                }

                reproj_v1[0] /= norm1;
                reproj_v1[1] /= norm1;
                reproj_v1[2] /= norm1;

                reproj_v2[0] /= norm2;
                reproj_v2[1] /= norm2;
                reproj_v2[2] /= norm2;

                // 4. 计算6D残差向量：方向向量差
                // 根据residual_opengv的实现，应该是角度误差，但这里我们用向量差来近似
                T diff1[3] = {reproj_v1[0] - T(bearing1_(0)), reproj_v1[1] - T(bearing1_(1)), reproj_v1[2] - T(bearing1_(2))};
                T diff2[3] = {reproj_v2[0] - T(bearing2_(0)), reproj_v2[1] - T(bearing2_(1)), reproj_v2[2] - T(bearing2_(2))};

                // 存储6D残差向量：[diff1; diff2]
                residuals[0] = diff1[0];
                residuals[1] = diff1[1];
                residuals[2] = diff1[2];
                residuals[3] = diff2[0];
                residuals[4] = diff2[1];
                residuals[5] = diff2[2];

                return true;
            }

        private:
            template <typename T>
            void CayleyToRotationMatrix(const T *cayley, T *rotation) const
            {
                T c1 = cayley[0], c2 = cayley[1], c3 = cayley[2];
                T c1_sq = c1 * c1, c2_sq = c2 * c2, c3_sq = c3 * c3;
                T denominator = T(1.0) + c1_sq + c2_sq + c3_sq;

                rotation[0] = (T(1.0) + c1_sq - c2_sq - c3_sq) / denominator;
                rotation[1] = (T(2.0) * (c1 * c2 - c3)) / denominator;
                rotation[2] = (T(2.0) * (c1 * c3 + c2)) / denominator;
                rotation[3] = (T(2.0) * (c1 * c2 + c3)) / denominator;
                rotation[4] = (T(1.0) - c1_sq + c2_sq - c3_sq) / denominator;
                rotation[5] = (T(2.0) * (c2 * c3 - c1)) / denominator;
                rotation[6] = (T(2.0) * (c1 * c3 - c2)) / denominator;
                rotation[7] = (T(2.0) * (c2 * c3 + c1)) / denominator;
                rotation[8] = (T(1.0) - c1_sq - c2_sq + c3_sq) / denominator;
            }

            Eigen::Vector3d bearing1_, bearing2_;
        };

        // Ceres Cost Function for Two-View Pose Optimization
        struct TwoViewCeresCostFunction
        {
            TwoViewCeresCostFunction(const Eigen::Vector3d &point1,
                                     const Eigen::Vector3d &point2,
                                     const std::string &residual_type,
                                     double weight = 1.0)
                : point1_(point1), point2_(point2),
                  residual_type_(residual_type), weight_(weight) {}

            template <typename T>
            bool operator()(const T *const params, T *residuals) const
            {
                // 从6参数恢复位姿（5参数 + 1符号）
                T t1 = params[0];
                T t2 = params[1];
                T t_squared = t1 * t1 + t2 * t2;

                // 归一化约束处理
                T t3_squared = T(1.0) - t_squared;
                if (t3_squared < T(0.0))
                {
                    t3_squared = T(0.0);
                }
                T t3 = ceres::sqrt(t3_squared);
                if (params[5] < T(0.0))
                { // 使用符号参数
                    t3 = -t3;
                }

                // Cayley参数转旋转矩阵
                T cayley[3] = {params[2], params[3], params[4]};
                T rotation[9];
                CayleyToRotationMatrix(cayley, rotation);

                // 计算残差
                if (residual_type_ == "ppo_opengv")
                {
                    return ComputePPOOpenGVResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "ppo")
                {
                    return ComputePPOResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "ppo_angle")
                {
                    return ComputePPOAngleResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "sampson")
                {
                    return ComputeSampsonResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "coplanar")
                {
                    return ComputeCoplanarResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "kneip")
                {
                    return ComputeKneipResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "ba")
                {
                    return ComputeBAResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "opengv")
                {
                    return ComputeOpenGVResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "ligt")
                {
                    return ComputeLiGTResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "lirt")
                {
                    return ComputeLiRTResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else if (residual_type_ == "ppog")
                {
                    return ComputePPOGResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
                else
                {
                    // 默认使用PPO OpenGV
                    return ComputePPOOpenGVResidual(point1_, point2_, rotation, t1, t2, t3, weight_, residuals);
                }
            }

        private:
            template <typename T>
            void CayleyToRotationMatrix(const T *cayley, T *rotation) const
            {
                T c1 = cayley[0], c2 = cayley[1], c3 = cayley[2];
                T c1_sq = c1 * c1, c2_sq = c2 * c2, c3_sq = c3 * c3;
                T denominator = T(1.0) + c1_sq + c2_sq + c3_sq;

                rotation[0] = (T(1.0) + c1_sq - c2_sq - c3_sq) / denominator;
                rotation[1] = (T(2.0) * (c1 * c2 - c3)) / denominator;
                rotation[2] = (T(2.0) * (c1 * c3 + c2)) / denominator;
                rotation[3] = (T(2.0) * (c1 * c2 + c3)) / denominator;
                rotation[4] = (T(1.0) - c1_sq + c2_sq - c3_sq) / denominator;
                rotation[5] = (T(2.0) * (c2 * c3 - c1)) / denominator;
                rotation[6] = (T(2.0) * (c1 * c3 - c2)) / denominator;
                rotation[7] = (T(2.0) * (c2 * c3 + c1)) / denominator;
                rotation[8] = (T(1.0) - c1_sq - c2_sq + c3_sq) / denominator;
            }

            template <typename T>
            bool ComputePPOOpenGVResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                          const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // PPO OpenGV残差实现（6维残差）- 与residual_ppo_opengv一致
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                // 计算叉积
                T tmp2[3] = {RX2[1] * T(X1(2)) - RX2[2] * T(X1(1)),
                             RX2[2] * T(X1(0)) - RX2[0] * T(X1(2)),
                             RX2[0] * T(X1(1)) - RX2[1] * T(X1(0))};

                T tmp3[3] = {T(X1(1)) * t3 - T(X1(2)) * t2,
                             T(X1(2)) * t1 - T(X1(0)) * t3,
                             T(X1(0)) * t2 - T(X1(1)) * t1};

                T tmp4[3] = {RX2[1] * t3 - RX2[2] * t2,
                             RX2[2] * t1 - RX2[0] * t3,
                             RX2[0] * t2 - RX2[1] * t1};

                T tmp2_norm = ceres::sqrt(tmp2[0] * tmp2[0] + tmp2[1] * tmp2[1] + tmp2[2] * tmp2[2]);
                T tmp3_norm = ceres::sqrt(tmp3[0] * tmp3[0] + tmp3[1] * tmp3[1] + tmp3[2] * tmp3[2]);
                T tmp4_norm = ceres::sqrt(tmp4[0] * tmp4[0] + tmp4[1] * tmp4[1] + tmp4[2] * tmp4[2]);

                // 避免除零错误
                const T epsilon = T(1e-12);
                if (tmp2_norm < epsilon || tmp3_norm < epsilon || tmp4_norm < epsilon)
                {
                    // 对于无效点，设置大的残差值
                    for (int i = 0; i < 6; ++i)
                    {
                        residuals[i] = T(weight) * T(1e3);
                    }
                    return true;
                }

                // 计算重投影坐标
                T reproj_coord1[3] = {tmp2_norm * t1 + tmp3_norm * RX2[0],
                                      tmp2_norm * t2 + tmp3_norm * RX2[1],
                                      tmp2_norm * t3 + tmp3_norm * RX2[2]};

                T reproj_coord2[3] = {tmp4_norm * T(X1(0)) - tmp2_norm * t1,
                                      tmp4_norm * T(X1(1)) - tmp2_norm * t2,
                                      tmp4_norm * T(X1(2)) - tmp2_norm * t3};

                // 归一化重投影坐标
                T reproj1_norm = ceres::sqrt(reproj_coord1[0] * reproj_coord1[0] +
                                             reproj_coord1[1] * reproj_coord1[1] +
                                             reproj_coord1[2] * reproj_coord1[2]);
                T reproj2_norm = ceres::sqrt(reproj_coord2[0] * reproj_coord2[0] +
                                             reproj_coord2[1] * reproj_coord2[1] +
                                             reproj_coord2[2] * reproj_coord2[2]);

                reproj_coord1[0] /= reproj1_norm;
                reproj_coord1[1] /= reproj1_norm;
                reproj_coord1[2] /= reproj1_norm;

                reproj_coord2[0] /= reproj2_norm;
                reproj_coord2[1] /= reproj2_norm;
                reproj_coord2[2] /= reproj2_norm;

                // 计算6D残差向量 [diff1; diff2]
                T diff1[3] = {reproj_coord1[0] - T(X1(0)),
                              reproj_coord1[1] - T(X1(1)),
                              reproj_coord1[2] - T(X1(2))};
                T diff2[3] = {reproj_coord2[0] - RX2[0],
                              reproj_coord2[1] - RX2[1],
                              reproj_coord2[2] - RX2[2]};

                // 存储6D残差向量 [diff1; diff2]
                residuals[0] = T(weight) * diff1[0];
                residuals[1] = T(weight) * diff1[1];
                residuals[2] = T(weight) * diff1[2];
                residuals[3] = T(weight) * diff2[0];
                residuals[4] = T(weight) * diff2[1];
                residuals[5] = T(weight) * diff2[2];

                return true;
            }

            template <typename T>
            bool ComputePPOResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                    const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // PPO残差实现（3维残差）- 与residual_PPO_3D一致
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                T tmp2[3] = {RX2[1] * T(X1(2)) - RX2[2] * T(X1(1)),
                             RX2[2] * T(X1(0)) - RX2[0] * T(X1(2)),
                             RX2[0] * T(X1(1)) - RX2[1] * T(X1(0))};

                T tmp3[3] = {T(X1(1)) * t3 - T(X1(2)) * t2,
                             T(X1(2)) * t1 - T(X1(0)) * t3,
                             T(X1(0)) * t2 - T(X1(1)) * t1};

                T tmp2_norm = ceres::sqrt(tmp2[0] * tmp2[0] + tmp2[1] * tmp2[1] + tmp2[2] * tmp2[2]);
                T tmp3_norm = ceres::sqrt(tmp3[0] * tmp3[0] + tmp3[1] * tmp3[1] + tmp3[2] * tmp3[2]);

                T reproj[3] = {tmp2_norm * t1 + tmp3_norm * RX2[0],
                               tmp2_norm * t2 + tmp3_norm * RX2[1],
                               tmp2_norm * t3 + tmp3_norm * RX2[2]};

                T reproj_norm = ceres::sqrt(reproj[0] * reproj[0] + reproj[1] * reproj[1] + reproj[2] * reproj[2]);
                reproj[0] /= reproj_norm;
                reproj[1] /= reproj_norm;
                reproj[2] /= reproj_norm;

                // 计算3D残差向量：reproj_coord/reproj_norm - X1
                T diff[3] = {reproj[0] - T(X1(0)), reproj[1] - T(X1(1)), reproj[2] - T(X1(2))};

                // 返回3D残差向量
                residuals[0] = T(weight) * diff[0];
                residuals[1] = T(weight) * diff[1];
                residuals[2] = T(weight) * diff[2];

                return true;
            }

            template <typename T>
            bool ComputePPOAngleResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                         const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // PPO角度误差残差实现（1维残差）- 与residual_PPO_angle一致
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                T tmp2[3] = {RX2[1] * T(X1(2)) - RX2[2] * T(X1(1)),
                             RX2[2] * T(X1(0)) - RX2[0] * T(X1(2)),
                             RX2[0] * T(X1(1)) - RX2[1] * T(X1(0))};

                T tmp3[3] = {T(X1(1)) * t3 - T(X1(2)) * t2,
                             T(X1(2)) * t1 - T(X1(0)) * t3,
                             T(X1(0)) * t2 - T(X1(1)) * t1};

                T tmp2_norm = ceres::sqrt(tmp2[0] * tmp2[0] + tmp2[1] * tmp2[1] + tmp2[2] * tmp2[2]);
                T tmp3_norm = ceres::sqrt(tmp3[0] * tmp3[0] + tmp3[1] * tmp3[1] + tmp3[2] * tmp3[2]);

                T reproj[3] = {tmp2_norm * t1 + tmp3_norm * RX2[0],
                               tmp2_norm * t2 + tmp3_norm * RX2[1],
                               tmp2_norm * t3 + tmp3_norm * RX2[2]};

                // 归一化重投影坐标和真实观测
                T reproj_norm = ceres::sqrt(reproj[0] * reproj[0] + reproj[1] * reproj[1] + reproj[2] * reproj[2]);
                T reproj_normalized[3] = {reproj[0] / reproj_norm, reproj[1] / reproj_norm, reproj[2] / reproj_norm};

                T X1_norm = ceres::sqrt(T(X1(0)) * T(X1(0)) + T(X1(1)) * T(X1(1)) + T(X1(2)) * T(X1(2)));
                T X1_normalized[3] = {T(X1(0)) / X1_norm, T(X1(1)) / X1_norm, T(X1(2)) / X1_norm};

                // 计算角度误差：1 - cos(angle) = 1 - dot(v1, v2)
                T dot_product = reproj_normalized[0] * X1_normalized[0] +
                                reproj_normalized[1] * X1_normalized[1] +
                                reproj_normalized[2] * X1_normalized[2];

                // 确保点积在有效范围内（数值稳定性）
                if (dot_product > T(1.0))
                    dot_product = T(1.0);
                if (dot_product < T(-1.0))
                    dot_product = T(-1.0);

                T angle_error = T(1.0) - dot_product;

                // 返回标量残差
                residuals[0] = T(weight) * angle_error;

                return true;
            }

            template <typename T>
            bool ComputeSampsonResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                        const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // Sampson残差实现 - 与residual_sampson一致
                // 先归一化到同质坐标
                T X1_norm[3] = {T(X1(0)) / T(X1(2)), T(X1(1)) / T(X1(2)), T(1.0)};
                T X2_norm[3] = {T(X2(0)) / T(X2(2)), T(X2(1)) / T(X2(2)), T(1.0)};

                T t_cross[9] = {T(0), -t3, t2, t3, T(0), -t1, -t2, t1, T(0)};
                T E[9]; // Essential matrix = t_cross * R
                for (int i = 0; i < 9; ++i)
                {
                    E[i] = T(0);
                    for (int j = 0; j < 3; ++j)
                    {
                        E[i] += t_cross[3 * (i / 3) + j] * R[3 * j + (i % 3)];
                    }
                }

                T epipolar = E[0] * X1_norm[0] * X2_norm[0] + E[1] * X1_norm[0] * X2_norm[1] + E[2] * X1_norm[0] * X2_norm[2] +
                             E[3] * X1_norm[1] * X2_norm[0] + E[4] * X1_norm[1] * X2_norm[1] + E[5] * X1_norm[1] * X2_norm[2] +
                             E[6] * X1_norm[2] * X2_norm[0] + E[7] * X1_norm[2] * X2_norm[1] + E[8] * X1_norm[2] * X2_norm[2];

                // 计算Ex2和E^T*x1，只取前两行（与residual_sampson一致）
                T Ex2[2] = {E[0] * X2_norm[0] + E[1] * X2_norm[1] + E[2] * X2_norm[2],
                            E[3] * X2_norm[0] + E[4] * X2_norm[1] + E[5] * X2_norm[2]};

                T EtX1[2] = {E[0] * X1_norm[0] + E[3] * X1_norm[1] + E[6] * X1_norm[2],
                             E[1] * X1_norm[0] + E[4] * X1_norm[1] + E[7] * X1_norm[2]};

                T denom = (Ex2[0] * Ex2[0] + Ex2[1] * Ex2[1]) + (EtX1[0] * EtX1[0] + EtX1[1] * EtX1[1]);
                residuals[0] = T(weight) * ceres::sqrt((epipolar * epipolar) / (denom + T(1e-10)));

                return true;
            }

            template <typename T>
            bool ComputeCoplanarResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                         const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // Coplanar残差实现 - 与residual_coplanar一致
                T t_cross[9] = {T(0), -t3, t2, t3, T(0), -t1, -t2, t1, T(0)};
                T E[9];
                for (int i = 0; i < 9; ++i)
                {
                    E[i] = T(0);
                    for (int j = 0; j < 3; ++j)
                    {
                        E[i] += t_cross[3 * (i / 3) + j] * R[3 * j + (i % 3)];
                    }
                }

                T epipolar = E[0] * T(X1(0)) * T(X2(0)) + E[1] * T(X1(0)) * T(X2(1)) + E[2] * T(X1(0)) * T(X2(2)) +
                             E[3] * T(X1(1)) * T(X2(0)) + E[4] * T(X1(1)) * T(X2(1)) + E[5] * T(X1(1)) * T(X2(2)) +
                             E[6] * T(X1(2)) * T(X2(0)) + E[7] * T(X1(2)) * T(X2(1)) + E[8] * T(X1(2)) * T(X2(2));

                residuals[0] = T(weight) * ceres::abs(epipolar);
                return true;
            }

            template <typename T>
            bool ComputeKneipResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                      const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // Kneip残差实现 - 与residual_Kneip一致
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                T cross[3] = {T(X1(1)) * RX2[2] - T(X1(2)) * RX2[1],
                              T(X1(2)) * RX2[0] - T(X1(0)) * RX2[2],
                              T(X1(0)) * RX2[1] - T(X1(1)) * RX2[0]};

                T cross_norm = ceres::sqrt(cross[0] * cross[0] + cross[1] * cross[1] + cross[2] * cross[2]);
                T dot_product = cross[0] * t1 + cross[1] * t2 + cross[2] * t3;

                if (cross_norm > T(1e-12))
                {
                    residuals[0] = T(weight) * ceres::abs(dot_product) / cross_norm;
                }
                else
                {
                    residuals[0] = T(0.0);
                }
                return true;
            }

            template <typename T>
            bool ComputeBAResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                   const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // BA残差实现 - 与residual_BA一致，需要通过三角测量计算3D点
                // 这里我们需要先计算3D点的深度，这在只优化pose的模式下是不准确的
                // 正确的做法应该使用Bundle Adjustment模式

                // 使用简化的深度估计（基于视差）
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                // 计算视差角度来估计深度
                T dot_product = T(X1(0)) * RX2[0] + T(X1(1)) * RX2[1] + T(X1(2)) * RX2[2];
                T parallax_angle = ceres::acos(ceres::abs(dot_product));

                // 基于视差角度的深度估计（较大视差 -> 较近距离）
                T estimated_depth = T(1.0) / (ceres::sin(parallax_angle) + T(0.1));
                if (estimated_depth > T(10.0))
                    estimated_depth = T(10.0);
                if (estimated_depth < T(0.1))
                    estimated_depth = T(0.1);

                T point3d[3] = {estimated_depth * T(X1(0)),
                                estimated_depth * T(X1(1)),
                                estimated_depth * T(X1(2))};

                // 变换到第二个相机
                T transformed[3] = {
                    R[0] * point3d[0] + R[1] * point3d[1] + R[2] * point3d[2] + t1,
                    R[3] * point3d[0] + R[4] * point3d[1] + R[5] * point3d[2] + t2,
                    R[6] * point3d[0] + R[7] * point3d[1] + R[8] * point3d[2] + t3};

                // 投影到归一化平面
                if (ceres::abs(transformed[2]) < T(1e-6))
                {
                    residuals[0] = T(weight) * T(1e3); // 大残差值
                    return true;
                }

                T projected[2] = {transformed[0] / transformed[2],
                                  transformed[1] / transformed[2]};

                // 重投影误差
                T reproj_error1 = (T(X1(0)) / T(X1(2)) - estimated_depth * T(X1(0)) / (estimated_depth * T(X1(2))));
                T reproj_error2 = (T(X1(1)) / T(X1(2)) - estimated_depth * T(X1(1)) / (estimated_depth * T(X1(2))));
                T reproj_error3 = (projected[0] - T(X2(0)) / T(X2(2)));
                T reproj_error4 = (projected[1] - T(X2(1)) / T(X2(2)));

                T total_error = reproj_error1 * reproj_error1 + reproj_error2 * reproj_error2 +
                                reproj_error3 * reproj_error3 + reproj_error4 * reproj_error4;

                residuals[0] = T(weight) * ceres::sqrt(total_error);

                return true;
            }

            template <typename T>
            bool ComputeOpenGVResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                       const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // OpenGV残差实现 - 与residual_opengv一致
                // 需要通过三角测量计算重投影的方向向量

                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                // 使用相同的深度估计方法
                T dot_product = T(X1(0)) * RX2[0] + T(X1(1)) * RX2[1] + T(X1(2)) * RX2[2];
                T parallax_angle = ceres::acos(ceres::abs(dot_product));
                T estimated_depth = T(1.0) / (ceres::sin(parallax_angle) + T(0.1));
                if (estimated_depth > T(10.0))
                    estimated_depth = T(10.0);
                if (estimated_depth < T(0.1))
                    estimated_depth = T(0.1);

                // 三角测量得到的3D点
                T point3d[3] = {estimated_depth * T(X1(0)),
                                estimated_depth * T(X1(1)),
                                estimated_depth * T(X1(2))};

                // 重投影到两个视图
                T reproj_v1[3] = {point3d[0], point3d[1], point3d[2]};
                T reproj_v2[3] = {
                    R[0] * point3d[0] + R[1] * point3d[1] + R[2] * point3d[2] + t1,
                    R[3] * point3d[0] + R[4] * point3d[1] + R[5] * point3d[2] + t2,
                    R[6] * point3d[0] + R[7] * point3d[1] + R[8] * point3d[2] + t3};

                // 归一化为方向向量
                T norm1 = ceres::sqrt(reproj_v1[0] * reproj_v1[0] + reproj_v1[1] * reproj_v1[1] + reproj_v1[2] * reproj_v1[2]);
                T norm2 = ceres::sqrt(reproj_v2[0] * reproj_v2[0] + reproj_v2[1] * reproj_v2[1] + reproj_v2[2] * reproj_v2[2]);

                reproj_v1[0] /= norm1;
                reproj_v1[1] /= norm1;
                reproj_v1[2] /= norm1;
                reproj_v2[0] /= norm2;
                reproj_v2[1] /= norm2;
                reproj_v2[2] /= norm2;

                // 计算角度误差：1 - cos(angle)
                T dot1 = reproj_v1[0] * T(X1(0)) + reproj_v1[1] * T(X1(1)) + reproj_v1[2] * T(X1(2));
                T dot2 = reproj_v2[0] * RX2[0] + reproj_v2[1] * RX2[1] + reproj_v2[2] * RX2[2];

                T error = (T(1.0) - dot1) + (T(1.0) - dot2);
                residuals[0] = T(weight) * error;

                return true;
            }

            template <typename T>
            bool ComputeLiGTResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                     const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // LiGT残差实现 - 返回3D残差向量
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                // 计算交叉乘积
                T tmp1[3] = {T(X1(1)) * RX2[2] - T(X1(2)) * RX2[1],
                             T(X1(2)) * RX2[0] - T(X1(0)) * RX2[2],
                             T(X1(0)) * RX2[1] - T(X1(1)) * RX2[0]};

                T tmp2[3] = {RX2[1] * T(X1(2)) - RX2[2] * T(X1(1)),
                             RX2[2] * T(X1(0)) - RX2[0] * T(X1(2)),
                             RX2[0] * T(X1(1)) - RX2[1] * T(X1(0))};

                T tmp3[3] = {T(X1(1)) * t3 - T(X1(2)) * t2,
                             T(X1(2)) * t1 - T(X1(0)) * t3,
                             T(X1(0)) * t2 - T(X1(1)) * t1};

                T tmp2_norm_sq = tmp2[0] * tmp2[0] + tmp2[1] * tmp2[1] + tmp2[2] * tmp2[2];
                T tmp3_norm_sq = tmp3[0] * tmp3[0] + tmp3[1] * tmp3[1] + tmp3[2] * tmp3[2];
                T tmp2_dot_tmp3 = tmp2[0] * tmp3[0] + tmp2[1] * tmp3[1] + tmp2[2] * tmp3[2];

                // 计算cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1
                T cost_vec[3] = {tmp2_norm_sq * tmp3[0] + tmp2_dot_tmp3 * tmp1[0],
                                 tmp2_norm_sq * tmp3[1] + tmp2_dot_tmp3 * tmp1[1],
                                 tmp2_norm_sq * tmp3[2] + tmp2_dot_tmp3 * tmp1[2]};

                // 归一化：cost_vec = cost_vec / tmp3.squaredNorm() * tmp2.norm()
                T tmp2_norm = ceres::sqrt(tmp2_norm_sq);
                cost_vec[0] = cost_vec[0] / tmp3_norm_sq * tmp2_norm;
                cost_vec[1] = cost_vec[1] / tmp3_norm_sq * tmp2_norm;
                cost_vec[2] = cost_vec[2] / tmp3_norm_sq * tmp2_norm;

                // 返回3D残差向量
                residuals[0] = T(weight) * cost_vec[0];
                residuals[1] = T(weight) * cost_vec[1];
                residuals[2] = T(weight) * cost_vec[2];

                return true;
            }

            template <typename T>
            bool ComputeLiRTResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                     const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // LiRT残差实现 - 返回3D残差向量
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                // 计算交叉乘积 tmp = X1.cross(R * X2)
                T tmp[3] = {T(X1(1)) * RX2[2] - T(X1(2)) * RX2[1],
                            T(X1(2)) * RX2[0] - T(X1(0)) * RX2[2],
                            T(X1(0)) * RX2[1] - T(X1(1)) * RX2[0]};

                T tmp_sq_norm = tmp[0] * tmp[0] + tmp[1] * tmp[1] + tmp[2] * tmp[2];

                // 计算叉积矩阵 crossMatrix(R * X2) 和 crossMatrix(X1)
                T cross_RX2[9] = {T(0), -RX2[2], RX2[1],
                                  RX2[2], T(0), -RX2[0],
                                  -RX2[1], RX2[0], T(0)};

                T cross_X1[9] = {T(0), -T(X1(2)), T(X1(1)),
                                 T(X1(2)), T(0), -T(X1(0)),
                                 -T(X1(1)), T(X1(0)), T(0)};

                // 计算 tmp1 = X1 * tmp.transpose() * crossMatrix(R * X2)
                // 计算 tmp2 = (R * X2) * tmp.transpose() * crossMatrix(X1)
                // 计算 tmp3 = tmp.squaredNorm() * Identity
                // cost_vec = (tmp1 - tmp2 + tmp3) * t

                T translation[3] = {t1, t2, t3};
                T cost_vec[3] = {T(0), T(0), T(0)};

                // 简化计算：直接实现 (tmp1 - tmp2 + tmp3) * t
                for (int i = 0; i < 3; ++i)
                {
                    T tmp1_row[3] = {T(0), T(0), T(0)};
                    T tmp2_row[3] = {T(0), T(0), T(0)};

                    // tmp1[i] = X1[i] * tmp.transpose() * crossMatrix(RX2)
                    for (int j = 0; j < 3; ++j)
                    {
                        tmp1_row[j] = T(X1(i)) * (tmp[0] * cross_RX2[0 * 3 + j] +
                                                  tmp[1] * cross_RX2[1 * 3 + j] +
                                                  tmp[2] * cross_RX2[2 * 3 + j]);
                        tmp2_row[j] = RX2[i] * (tmp[0] * cross_X1[0 * 3 + j] +
                                                tmp[1] * cross_X1[1 * 3 + j] +
                                                tmp[2] * cross_X1[2 * 3 + j]);
                    }

                    // 添加对角元素 tmp.squaredNorm() * I
                    T diag_element = (i == 0 || i == 1 || i == 2) ? tmp_sq_norm : T(0);
                    tmp1_row[i] += diag_element;

                    // cost_vec[i] = (tmp1_row - tmp2_row) * translation
                    cost_vec[i] = (tmp1_row[0] - tmp2_row[0]) * translation[0] +
                                  (tmp1_row[1] - tmp2_row[1]) * translation[1] +
                                  (tmp1_row[2] - tmp2_row[2]) * translation[2];
                }

                // 返回3D残差向量
                residuals[0] = T(weight) * cost_vec[0];
                residuals[1] = T(weight) * cost_vec[1];
                residuals[2] = T(weight) * cost_vec[2];

                return true;
            }

            template <typename T>
            bool ComputePPOGResidual(const Eigen::Vector3d &X1, const Eigen::Vector3d &X2,
                                     const T *R, T t1, T t2, T t3, double weight, T *residuals) const
            {
                // PPOG残差实现 - 返回3D残差向量
                T RX2[3] = {R[0] * T(X2(0)) + R[1] * T(X2(1)) + R[2] * T(X2(2)),
                            R[3] * T(X2(0)) + R[4] * T(X2(1)) + R[5] * T(X2(2)),
                            R[6] * T(X2(0)) + R[7] * T(X2(1)) + R[8] * T(X2(2))};

                // 计算交叉乘积
                T tmp2[3] = {RX2[1] * T(X1(2)) - RX2[2] * T(X1(1)),
                             RX2[2] * T(X1(0)) - RX2[0] * T(X1(2)),
                             RX2[0] * T(X1(1)) - RX2[1] * T(X1(0))};

                T tmp3[3] = {T(X1(1)) * t3 - T(X1(2)) * t2,
                             T(X1(2)) * t1 - T(X1(0)) * t3,
                             T(X1(0)) * t2 - T(X1(1)) * t1};

                T t_cross_RX2[3] = {t2 * RX2[2] - t3 * RX2[1],
                                    t3 * RX2[0] - t1 * RX2[2],
                                    t1 * RX2[1] - t2 * RX2[0]};

                T tmp2_norm = ceres::sqrt(tmp2[0] * tmp2[0] + tmp2[1] * tmp2[1] + tmp2[2] * tmp2[2]);
                T tmp3_norm = ceres::sqrt(tmp3[0] * tmp3[0] + tmp3[1] * tmp3[1] + tmp3[2] * tmp3[2]);
                T t_cross_RX2_norm = ceres::sqrt(t_cross_RX2[0] * t_cross_RX2[0] +
                                                 t_cross_RX2[1] * t_cross_RX2[1] +
                                                 t_cross_RX2[2] * t_cross_RX2[2]);

                // 计算 reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2)
                T reproj_coord[3] = {tmp2_norm * t1 + tmp3_norm * RX2[0],
                                     tmp2_norm * t2 + tmp3_norm * RX2[1],
                                     tmp2_norm * t3 + tmp3_norm * RX2[2]};

                // 计算 X1 * (t.cross(R * X2)).norm()
                T scaled_X1[3] = {T(X1(0)) * t_cross_RX2_norm,
                                  T(X1(1)) * t_cross_RX2_norm,
                                  T(X1(2)) * t_cross_RX2_norm};

                // 计算3D残差向量：reproj_coord - X1 * (t.cross(R * X2)).norm()
                T diff[3] = {reproj_coord[0] - scaled_X1[0],
                             reproj_coord[1] - scaled_X1[1],
                             reproj_coord[2] - scaled_X1[2]};

                // 返回3D残差向量
                residuals[0] = T(weight) * diff[0];
                residuals[1] = T(weight) * diff[1];
                residuals[2] = T(weight) * diff[2];

                return true;
            }

            Eigen::Vector3d point1_, point2_;
            std::string residual_type_;
            double weight_;
        };
    }

    bool CeresOptimizer::Optimize(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        std::cout << "\n[TwoViewOptimizer] Starting Ceres optimization..." << std::endl;
        std::cout << "Residual type: " << residual_type << ", Loss type: " << loss_type << std::endl;

        const size_t num_points = points1.cols();

        // 检查是否为BA或OpenGV残差（需要pose+3D点联合优化）
        bool use_bundle_adjustment = (residual_type == "ba" || residual_type == "opengv");

        if (use_bundle_adjustment)
        {
            std::cout << "Using Bundle Adjustment mode (pose + 3D points optimization)" << std::endl;
            return OptimizePoseWith3DPoints(points1, points2, pose, weights, residual_type, loss_type);
        }
        else
        {
            std::cout << "Using Pose-only optimization mode" << std::endl;
            return OptimizePoseOnly(points1, points2, pose, weights, residual_type, loss_type);
        }
    }

    // 仅优化位姿的方法（原有实现）
    bool CeresOptimizer::OptimizePoseOnly(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        // 初始化6个参数：t1, t2, cayley1, cayley2, cayley3, t3_sign
        double params[6];

        Vector3d t_normalized = pose.tij.normalized();
        params[0] = t_normalized(0);                     // t1
        params[1] = t_normalized(1);                     // t2
        params[5] = (t_normalized(2) >= 0) ? 1.0 : -1.0; // t3符号

        Vector3d cayley = RotationToCayley(pose.Rij);
        params[2] = cayley(0);
        params[3] = cayley(1);
        params[4] = cayley(2);

        std::cout << "Pose-only optimization. Initial params: [" << params[0] << ", " << params[1] << ", "
                  << params[2] << ", " << params[3] << ", " << params[4] << ", " << params[5] << "]" << std::endl;

        // 创建Ceres问题
        ceres::Problem problem;

        const size_t num_points = points1.cols();
        int residual_dim = (residual_type == "ppo_opengv") ? 6 : 1;

        // 为每个点添加残差块
        for (size_t i = 0; i < num_points; ++i)
        {
            double weight = (weights != nullptr) ? (*weights)(i) : 1.0;

            ceres::CostFunction *cost_function = nullptr;

            if (residual_type == "ppo_opengv")
            {
                cost_function = new ceres::AutoDiffCostFunction<TwoViewCeresCostFunction, 6, 6>(
                    new TwoViewCeresCostFunction(points1.col(i), points2.col(i), residual_type, weight));
            }
            else if (residual_type == "ligt" || residual_type == "lirt" || residual_type == "ppog" || residual_type == "ppo")
            {
                cost_function = new ceres::AutoDiffCostFunction<TwoViewCeresCostFunction, 3, 6>(
                    new TwoViewCeresCostFunction(points1.col(i), points2.col(i), residual_type, weight));
            }
            else if (residual_type == "ppo_angle_uniform")
            {
                cost_function = new ceres::AutoDiffCostFunction<TwoViewCeresCostFunction, 2, 6>(
                    new TwoViewCeresCostFunction(points1.col(i), points2.col(i), residual_type, weight));
            }
            else
            {
                cost_function = new ceres::AutoDiffCostFunction<TwoViewCeresCostFunction, 1, 6>(
                    new TwoViewCeresCostFunction(points1.col(i), points2.col(i), residual_type, weight));
            }

            // 添加损失函数
            ceres::LossFunction *loss_function = nullptr;
            if (loss_type == "huber")
            {
                loss_function = new ceres::HuberLoss(0.01);
            }
            else if (loss_type == "cauchy")
            {
                loss_function = new ceres::CauchyLoss(0.01);
            }

            problem.AddResidualBlock(cost_function, loss_function, params);
        }

        // 设置参数约束
        problem.SetParameterLowerBound(params, 0, -0.99); // t1 约束
        problem.SetParameterUpperBound(params, 0, 0.99);
        problem.SetParameterLowerBound(params, 1, -0.99); // t2 约束
        problem.SetParameterUpperBound(params, 1, 0.99);
        problem.SetParameterLowerBound(params, 5, -1.0); // t3符号约束
        problem.SetParameterUpperBound(params, 5, 1.0);

        // 配置求解器选项
        ceres::Solver::Options options;
        options.linear_solver_type = ceres::DENSE_QR;
        options.minimizer_progress_to_stdout = true;
        options.max_num_iterations = max_iterations_;
        options.function_tolerance = convergence_threshold_;
        options.parameter_tolerance = convergence_threshold_;

        // 求解
        ceres::Solver::Summary summary;
        ceres::Solve(options, &problem, &summary);

        std::cout << summary.BriefReport() << std::endl;

        // 从优化后的参数恢复位姿
        double t1 = params[0], t2 = params[1];
        double t_squared = t1 * t1 + t2 * t2;
        if (t_squared >= 1.0)
        {
            double norm = std::sqrt(t_squared);
            t1 /= norm;
            t2 /= norm;
            t_squared = t1 * t1 + t2 * t2;
        }
        double t3 = params[5] * std::sqrt(1.0 - t_squared);

        pose.tij = Vector3d(t1, t2, t3);
        pose.Rij = CayleyToRotation(Vector3d(params[2], params[3], params[4]));

        std::cout << "Final cost: " << summary.final_cost << std::endl;
        std::cout << "Iterations: " << summary.iterations.size() << std::endl;
        std::cout << "Optimization " << (summary.termination_type == ceres::CONVERGENCE ? "成功" : "失败") << std::endl;

        return summary.termination_type == ceres::CONVERGENCE;
    }

    // 联合优化位姿和3D点的方法（BA和OpenGV）
    bool CeresOptimizer::OptimizePoseWith3DPoints(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        const size_t num_points = points1.cols();

        std::cout << "Bundle adjustment optimization (pose + 3D points)" << std::endl;
        std::cout << "Number of 3D points to optimize: " << num_points << std::endl;

        // 初始化6个位姿参数：t1, t2, cayley1, cayley2, cayley3, t3_sign
        double pose_params[6];

        Vector3d t_normalized = pose.tij.normalized();
        pose_params[0] = t_normalized(0);                     // t1
        pose_params[1] = t_normalized(1);                     // t2
        pose_params[5] = (t_normalized(2) >= 0) ? 1.0 : -1.0; // t3符号

        Vector3d cayley = RotationToCayley(pose.Rij);
        pose_params[2] = cayley(0);
        pose_params[3] = cayley(1);
        pose_params[4] = cayley(2);

        // 使用三角化获得3D点初始值
        std::vector<double *> point3d_params(num_points);
        for (size_t i = 0; i < num_points; ++i)
        {
            point3d_params[i] = new double[3];

            Vector3d triangulated_point;
            if (residual_type == "ba")
            {
                triangulated_point = triangulateOnePoint(pose.Rij, pose.tij, points1.col(i), points2.col(i));
            }
            else
            { // opengv
                triangulated_point = triangulate2(pose.Rij, pose.tij, points1.col(i), points2.col(i));
            }

            point3d_params[i][0] = triangulated_point(0);
            point3d_params[i][1] = triangulated_point(1);
            point3d_params[i][2] = triangulated_point(2);

            // 确保3D点在相机前方，但保持原始深度（不强制限制到0.1）
            if (point3d_params[i][2] < 1e-6)
            {
                point3d_params[i][2] = 1e-6; // 只在极小深度时才调整
            }
        }

        std::cout << "Initial pose params: [" << pose_params[0] << ", " << pose_params[1] << ", "
                  << pose_params[2] << ", " << pose_params[3] << ", " << pose_params[4] << ", " << pose_params[5] << "]" << std::endl;
        std::cout << "Triangulated " << num_points << " 3D points as initial values" << std::endl;

        // 创建Ceres问题
        ceres::Problem problem;

        // 为每个点添加Bundle Adjustment残差块
        for (size_t i = 0; i < num_points; ++i)
        {
            double weight = (weights != nullptr) ? (*weights)(i) : 1.0;

            ceres::CostFunction *cost_function = nullptr;

            if (residual_type == "ba")
            {
                cost_function = new ceres::AutoDiffCostFunction<BundleAdjustmentCostFunction, 6, 6, 3>(
                    new BundleAdjustmentCostFunction(points1.col(i), points2.col(i)));
            }
            else
            { // opengv
                cost_function = new ceres::AutoDiffCostFunction<OpenGVBundleCostFunction, 6, 6, 3>(
                    new OpenGVBundleCostFunction(points1.col(i), points2.col(i)));
            }

            // 添加损失函数
            ceres::LossFunction *loss_function = nullptr;
            if (loss_type == "huber")
            {
                loss_function = new ceres::HuberLoss(0.01);
            }
            else if (loss_type == "cauchy")
            {
                loss_function = new ceres::CauchyLoss(0.01);
            }

            problem.AddResidualBlock(cost_function, loss_function, pose_params, point3d_params[i]);
        }

        // 设置位姿参数约束
        problem.SetParameterLowerBound(pose_params, 0, -0.99); // t1 约束
        problem.SetParameterUpperBound(pose_params, 0, 0.99);
        problem.SetParameterLowerBound(pose_params, 1, -0.99); // t2 约束
        problem.SetParameterUpperBound(pose_params, 1, 0.99);
        problem.SetParameterLowerBound(pose_params, 5, -1.0); // t3符号约束
        problem.SetParameterUpperBound(pose_params, 5, 1.0);

        // 设置3D点深度约束（避免点在相机后方）
        for (size_t i = 0; i < num_points; ++i)
        {
            problem.SetParameterLowerBound(point3d_params[i], 2, 1e-6); // z > 1e-6，更宽松的约束
        }

        // 配置求解器选项（使用Schur求解器适合Bundle Adjustment）
        ceres::Solver::Options options;
        options.linear_solver_type = ceres::DENSE_SCHUR; // 适合BA问题
        options.minimizer_progress_to_stdout = true;
        options.max_num_iterations = max_iterations_;
        options.function_tolerance = convergence_threshold_;
        options.parameter_tolerance = convergence_threshold_;

        // 求解
        ceres::Solver::Summary summary;
        ceres::Solve(options, &problem, &summary);

        std::cout << summary.BriefReport() << std::endl;

        // 从优化后的参数恢复位姿
        double t1 = pose_params[0], t2 = pose_params[1];
        double t_squared = t1 * t1 + t2 * t2;
        if (t_squared >= 1.0)
        {
            double norm = std::sqrt(t_squared);
            t1 /= norm;
            t2 /= norm;
            t_squared = t1 * t1 + t2 * t2;
        }
        double t3 = pose_params[5] * std::sqrt(1.0 - t_squared);

        pose.tij = Vector3d(t1, t2, t3);
        pose.Rij = CayleyToRotation(Vector3d(pose_params[2], pose_params[3], pose_params[4]));

        std::cout << "Final cost: " << summary.final_cost << std::endl;
        std::cout << "Iterations: " << summary.iterations.size() << std::endl;
        std::cout << "Optimization " << (summary.termination_type == ceres::CONVERGENCE ? "成功" : "失败") << std::endl;

        // 清理内存
        for (size_t i = 0; i < num_points; ++i)
        {
            delete[] point3d_params[i];
        }

        return summary.termination_type == ceres::CONVERGENCE;
    }

} // namespace PoSDK
