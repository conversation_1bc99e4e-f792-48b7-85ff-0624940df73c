# 项目结构
project_root/
├── include/                    # 暴露给外部使用的API
│   ├── plugins/              # 插件接口定义
│   │   ├── data/            # 数据插件接口
│   │   │   └── *.hpp       # 数据插件头文件
│   │   ├── methods/         # 方法插件接口
│   │   │   └── *.hpp       # 方法插件头文件
│   │   └── behaviors/       # 行为插件接口
│   │       └── *.hpp       # 行为插件头文件
│   └── po_core/             # 核心功能模块
│       └── *.hpp           # 其他API头文件
├── common/                    # 公共头文件
│   ├── converter/            # 类型转换工具
│   │   └── *.hpp/*.cpp       # 转换器定义
│   └── image_viewer/         # 图像显示组件
│       └── *.hpp/*.cpp      # 显示器定义
├── po_core/                    # 纯位姿视觉核心库
│   └── CMakeLists.txt        # 独立project：生成po_core库
│   └── src/              # 核心源码
│       └── CMakeLists.txt     # add_subdirectory
│       └── internal/              # 核心架构
│           ├── CMakeLists.txt     # pomvg_factory库
│           ├── behaviors/       # 行为实现
│           │   └── *.hpp/*.cpp # 行为类定义
│           ├── methods/         # 方法实现
│           │   └── *.hpp/*.cpp # 方法类定义
│           ├── data/           # 内部数据结构
│           │   └── *.hpp/*.cpp # 数据类定义
│           ├── factory/        # 工厂模式实现
│           │   ├── factory.cpp # 工厂类实现
│           │   └── *.hpp/*.cpp # 工厂相关定义
│           ├── file_io/        # 文件读写模块
│           │   └── *.hpp/*.cpp # IO操作定义
│           └── *.hpp/*.cpp     # 基本实现
├── plugins/                 # 插件模块实现
│   ├── CMakeLists.txt        # 独立project：生成插件库
│   ├── data/               # 数据插件
│   │   ├── *.hpp/*.cpp     # 插件头文件
│   │   ├── CMakeLists.txt   # 生成各种Data插件库
│   ├── methods/            # 方法插件
│   │   ├── *.hpp/*.cpp     # 插件头文件
│   │   ├── CMakeLists.txt   # 生成各种Method插件库
│   └── behaviors/          # 行为插件（预留）
│       ├── *.hpp/*.cpp    # 插件头文件
│       └── CMakeLists.txt  # 生成各种Behavior插件库
├── tests/                  # 测试代码
│   └── *.cpp               # 测试用例
│   └── CMakeLists.txt      # 生成测试程序
├── CMakeLists.txt          # 项目CMake配置文件
```


# 项目构建目录

```
project_root/
├── ${BUILD_DIR}/
│   ├── ${OUTPUT_BASE_DIR}
│   │   ├── bin //${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
│   │   ├── lib //${CMAKE_LIBRARY_OUTPUT_DIRECTORY}, {CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
│   │   ├── plugins //${PLUGIN_DIR}
│   │   ├── include //${OUTPUT_INCLUDE_DIR}
│   │   │   ├── po_core //${OUTPUT_PO_CORE_INCLUDE_DIR}
│   │   │   ├── plugins //${OUTPUT_PLUGINS_INCLUDE_DIR}
│   │   │   │   ├── data //${DATA_PLUGIN_DIR}
│   │   │   │   └── methods //${METHOD_PLUGIN_DIR}
│   │
``` 


project_root/
├── ${BUILD_DIR}/
│   ├── ${OUTPUT_BASE_DIR}
│   │   ├── bin/                  // ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
│   │   ├── lib/                  // ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}, ${CMAKE_ARCHIVE_OUTPUT_DIRECTORY}
│   │   ├── plugins/              // ${PLUGIN_DIR}
│   │   ├── common/               // ${COMMON_DIR}
│   │   ├── include/             // ${OUTPUT_INCLUDE_DIR}
│   │   │   ├── po_core/         // ${OUTPUT_PO_CORE_INCLUDE_DIR}
│   │   │   ├── plugins/         // ${OUTPUT_PLUGINS_INCLUDE_DIR}
│   │   │   │   ├── data/        // ${DATA_PLUGIN_DIR}
│   │   │   │   └── methods/     // ${METHOD_PLUGIN_DIR}
│   │   ├── configs/             // ${CONFIG_DIR}
│   │   │   └── methods/         // ${METHOD_CONFIG_DIR}


