# INI配置文件注释功能优化

## 概述

优化了`ConfigurationTools`类，增加了对INI配置文件中`#`和`;`两种注释格式的完整支持。

## 新增功能

### 1. 支持两种注释格式

- **井号注释 (`#`)**：传统的注释格式
- **分号注释 (`;`)**：常用的ini文件注释格式

### 2. 注释类型支持

#### 整行注释
```ini
# 这是一个完整的注释行
; 这也是一个完整的注释行
```

#### 行内注释
```ini
option1=value1 # 这是行内注释
option2=value2 ; 这是行内分号注释
```

### 3. 智能处理

- 自动忽略空行和只包含空白字符的行
- 自动修剪配置值的前后空白字符
- 正确处理注释符号后的空格

## 使用示例

### 配置文件示例 (`example_config.ini`)

```ini
# PoMVG 配置文件示例
# 支持 # 和 ; 两种注释格式

; 这也是注释行，使用分号

[camera_params]
# 相机内参配置
focal_length=800.0          # 焦距 (像素)
cx=320.0                    ; 主点x坐标
cy=240.0                    ; 主点y坐标
; k3=0.0                    # 径向畸变系数3 (已禁用)

[feature_extraction]
# 特征提取参数
method=SIFT                 ; 使用SIFT特征
max_features=1000           # 最大特征点数量
# min_hessian=400           # 最小Hessian阈值 (已注释)
```

### 代码使用示例

```cpp
#include "internal/inifile.hpp"

int main()
{
    // 创建配置工具实例
    PoSDK::ConfigurationTools config;
    
    // 加载配置文件（注释会被自动处理）
    if (config.Init("example_config.ini") != 0)
    {
        std::cerr << "配置文件加载失败" << std::endl;
        return -1;
    }
    
    std::string value;
    
    // 读取有效配置项
    config.ReadItem("camera_params", "focal_length", "0", value);
    std::cout << "焦距: " << value << std::endl;  // 输出: 800.0
    
    // 尝试读取被注释的配置项
    if (config.ReadItem("camera_params", "k3", "未找到", value) != 0)
    {
        std::cout << "k3被正确忽略: " << value << std::endl;  // 输出: 未找到
    }
    
    return 0;
}
```

## 测试

### 运行测试

```bash
# 编译并运行测试
cd po_core
mkdir -p build && cd build
cmake ..
make test_ini_comments
./test_ini_comments
```

### 测试覆盖

测试文件 `test_ini_comments.cpp` 包含以下测试用例：

1. **TestFileCreationAndLoading** - 文件创建和加载测试
2. **TestHashCommentLines** - 井号注释行测试
3. **TestSemicolonCommentLines** - 分号注释行测试
4. **TestInlineHashComments** - 行内井号注释测试
5. **TestInlineSemicolonComments** - 行内分号注释测试
6. **TestValidOptions** - 有效配置项测试
7. **TestBoundaryCase** - 边界情况测试
8. **TestConfigDump** - 配置转储测试
9. **TestWriteAndReload** - 写入和重新加载测试
10. **TestMultipleCommentFormats** - 混合注释格式测试

## 技术实现

### 核心改进

1. **预处理机制**：在调用boost::property_tree之前，预处理ini文件内容
2. **注释检测**：识别以`#`或`;`开头的注释行
3. **行内注释处理**：移除配置行中`#`或`;`后的注释内容
4. **字符串修剪**：自动清理前后空白字符

### 新增方法

- `PreprocessIniContent()` - 预处理ini文件内容
- `TrimString()` - 字符串修剪函数
- `RemoveInlineComments()` - 移除行内注释

### 兼容性

- 完全向后兼容原有API
- 保持线程安全特性
- 维持原有的错误处理机制

## 性能考虑

- 预处理只在加载时执行一次
- 使用字符串流避免临时文件
- 保持原有的线程安全锁机制

## 注意事项

1. **字符转义**：如果配置值中需要包含`#`或`;`字符，目前不支持转义
2. **编码**：默认使用UTF-8编码
3. **内存**：预处理会将整个文件内容加载到内存中

## 示例文件

- `example_config.ini` - 演示各种注释用法的配置文件
- `example_usage.cpp` - 完整的使用示例代码
- `test_ini_comments.cpp` - 全面的测试用例

## 更新日志

- **v1.0** - 初始实现，支持`#`和`;`注释格式
- 增加了预处理机制和完整的测试覆盖 