# ==============================================================================
# 基础项目配置
# ==============================================================================
cmake_minimum_required(VERSION 3.10)
if(CMAKE_VERSION VERSION_GREATER_EQUAL "3.31")
    cmake_policy(SET CMP0167 NEW)
endif()

# 在project命令之前设置默认构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Choose the type of build" FORCE)
endif()
set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")

project(po_core_lib 
    VERSION ******** 
    DESCRIPTION "Pose-only Multiple View Geometry Library"
    LANGUAGES CXX
)
 
# ==============================================================================
# 编译选项和平台检测
# ==============================================================================
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)

# 检测操作系统和处理器架构
if(APPLE)
    set(MACOSX TRUE)
    if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm64")
        set(APPLE_M1 TRUE)
        add_compile_definitions(APPLE_M1)
    endif()
    # macOS特定优化
    set(CMAKE_MACOSX_RPATH ON)
elseif(WIN32)
    set(WINDOWS TRUE)
    # Windows特定设置
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-D_WIN32_WINNT=0x0601)
elseif(UNIX AND NOT APPLE)
    set(LINUX TRUE)
    # Linux特定优化
    set(CMAKE_BUILD_RPATH_USE_ORIGIN ON)
endif()

message(STATUS "Configuring for platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "Processor: ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")

# 防止源码目录内构建
if(${CMAKE_SOURCE_DIR} STREQUAL ${CMAKE_BINARY_DIR})
    message(FATAL_ERROR 
        "In-source builds are not allowed.\n"
        "Please create a separate build directory and run CMake from there.\n"
        "You may need to remove CMakeCache.txt and CMakeFiles directory."
    )
endif()


# ==============================================================================
# 构建选项
# ==============================================================================
option(BUILD_SHARED_LIBS "Build shared libraries" ON)
option(BUILD_TESTS "Build test programs" ON)
option(BUILD_WITH_OPENMP "Build with OpenMP support" ON)
option(ENABLE_SANITIZERS "Enable sanitizers in debug build" OFF)

# ------------------------------------------------------------------------------
# OpenMP 配置
# ------------------------------------------------------------------------------
# 确保线程库可用
set(THREADS_PREFER_PTHREAD_FLAG ON)
find_package(Threads REQUIRED)

# 寻找 OpenMP
if(BUILD_WITH_OPENMP)
    # macOS特定的OpenMP配置
    if(APPLE)
        message(STATUS "Detected macOS. Configuring OpenMP with Homebrew libomp...")
        
        # 检查是否有Homebrew
        find_program(BREW_COMMAND brew)
        if(BREW_COMMAND)
            # 获取libomp路径
            execute_process(
                COMMAND ${BREW_COMMAND} --prefix libomp
                OUTPUT_VARIABLE LIBOMP_PREFIX
                OUTPUT_STRIP_TRAILING_WHITESPACE
                ERROR_QUIET
            )
            
            if(LIBOMP_PREFIX AND EXISTS "${LIBOMP_PREFIX}")
                message(STATUS "Found Homebrew libomp at: ${LIBOMP_PREFIX}")
                
                # 设置OpenMP相关变量
                set(OpenMP_C_FLAGS "-Xpreprocessor -fopenmp -I${LIBOMP_PREFIX}/include")
                set(OpenMP_CXX_FLAGS "-Xpreprocessor -fopenmp -I${LIBOMP_PREFIX}/include")
                set(OpenMP_C_LIB_NAMES "omp")
                set(OpenMP_CXX_LIB_NAMES "omp")
                set(OpenMP_omp_LIBRARY "${LIBOMP_PREFIX}/lib/libomp.dylib")
                set(OpenMP_EXE_LINKER_FLAGS "-L${LIBOMP_PREFIX}/lib -lomp")
                
                # 检查库文件是否存在
                if(EXISTS "${OpenMP_omp_LIBRARY}")
                    set(OpenMP_CXX_FOUND TRUE)
                    set(OpenMP_FOUND TRUE)
                    
                    # 设置全局编译和链接标志
                    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
                    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
                    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
                    set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
                    
                    # 创建OpenMP目标
                    if(NOT TARGET OpenMP::OpenMP_CXX)
                        add_library(OpenMP::OpenMP_CXX INTERFACE IMPORTED)
                        set_property(TARGET OpenMP::OpenMP_CXX
                            PROPERTY INTERFACE_COMPILE_OPTIONS "-Xpreprocessor" "-fopenmp")
                        set_property(TARGET OpenMP::OpenMP_CXX
                            PROPERTY INTERFACE_INCLUDE_DIRECTORIES "${LIBOMP_PREFIX}/include")
                        set_property(TARGET OpenMP::OpenMP_CXX
                            PROPERTY INTERFACE_LINK_LIBRARIES "${OpenMP_omp_LIBRARY}")
                        
                        message(STATUS "Created OpenMP::OpenMP_CXX target with Homebrew libomp")
                    endif()
                    
                    add_definitions(-DUSE_OPENMP)
                    message(STATUS "OpenMP support enabled with Homebrew libomp - USE_OPENMP macro defined")
                else()
                    message(WARNING "libomp library not found at: ${OpenMP_omp_LIBRARY}")
                    message(WARNING "Please install libomp with: brew install libomp")
                endif()
            else()
                message(WARNING "Homebrew libomp not found. Please install with: brew install libomp")
            endif()
        else()
            message(WARNING "Homebrew not found. Please install Homebrew and then: brew install libomp")
        endif()
    else()
        # 非macOS系统的标准OpenMP配置
        find_package(OpenMP)
        
        # 检查 OpenMP 是否找到 - 在 Ubuntu 上可能需要安装 libomp-dev
        if(OpenMP_FOUND OR OpenMP_CXX_FOUND)
            # 设置全局 OpenMP 标志
            set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} ${OpenMP_C_FLAGS}")
            set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
            set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")

            # 在 CMake 3.9+ 上，为保险起见仍然手动创建 OpenMP 目标
            if(NOT TARGET OpenMP::OpenMP_CXX AND CMAKE_VERSION VERSION_GREATER_EQUAL 3.9)
                if(OpenMP_CXX_FOUND)
                    add_library(OpenMP::OpenMP_CXX INTERFACE IMPORTED)
                    set_property(TARGET OpenMP::OpenMP_CXX
                        PROPERTY INTERFACE_COMPILE_OPTIONS ${OpenMP_CXX_FLAGS})

                    # 对于 INTERFACE_LINK_LIBRARIES，我们分别处理库和链接标志
                    if(OpenMP_CXX_LIBRARIES)
                        set_property(TARGET OpenMP::OpenMP_CXX
                            PROPERTY INTERFACE_LINK_LIBRARIES ${OpenMP_CXX_LIBRARIES})
                    endif()

                    message(STATUS "Manually created OpenMP::OpenMP_CXX target with flags: ${OpenMP_CXX_FLAGS}")
                endif()
            endif()
            
            # 添加OpenMP编译定义
            add_definitions(-DUSE_OPENMP)
            message(STATUS "OpenMP support enabled - USE_OPENMP macro defined")
        else()
            message(WARNING "OpenMP not found. This may cause problems with parallel processing. You might need to install libomp-dev package.")
        endif()
    endif()
endif()

# 编译器特定选项
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    # 基础警告
    add_compile_options(-Wall -Wextra -Wpedantic)
    
    # Debug模式下的优化
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        # 在Debug模式下定义_DEBUG宏
        add_definitions(-D_DEBUG)
        
        if(ENABLE_SANITIZERS)
            add_compile_options(-fsanitize=address -fsanitize=undefined)
            add_link_options(-fsanitize=address -fsanitize=undefined)
        endif()
    endif()
    
    # Release模式下的优化
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        add_compile_options(-O3 -march=native)
    endif()
elseif(MSVC)
    # MSVC特定选项
    add_compile_options(/W4 /MP)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
    
    # MSVC在Debug模式下自动定义_DEBUG，但为了确保一致性，这里也显式定义
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_definitions(-D_DEBUG)
    endif()
endif()

# ==============================================================================
# 输出目录配置
# ==============================================================================
# 设置基础输出目录
set(OUTPUT_BASE_DIR "${CMAKE_BINARY_DIR}/po_core_lib")

# 设置各类文件的输出目录
set(OUTPUT_BIN_DIR "${OUTPUT_BASE_DIR}/bin")
set(OUTPUT_LIB_DIR "${OUTPUT_BASE_DIR}/lib")
set(OUTPUT_INCLUDE_DIR "${OUTPUT_BASE_DIR}/include")
set(OUTPUT_CMAKE_DIR "${OUTPUT_LIB_DIR}/cmake/po_core")  

set(OUTPUT_PO_CORE_INCLUDE_DIR "${OUTPUT_INCLUDE_DIR}/po_core")
set(OUTPUT_PROTO_INCLUDE_DIR "${OUTPUT_INCLUDE_DIR}/proto")

# 设置临时文件目录
set(TEMP_FILES_DIR "${CMAKE_BINARY_DIR}/temp_files")
set(GENERATED_FILES_DIR "${TEMP_FILES_DIR}/generated")

# 设置Python drawer目录
set(OUTPUT_PYTHON_DIR "${OUTPUT_BASE_DIR}/python")

# 确保所有必要的目录存在
file(MAKE_DIRECTORY ${OUTPUT_BIN_DIR})
file(MAKE_DIRECTORY ${OUTPUT_LIB_DIR})
file(MAKE_DIRECTORY ${OUTPUT_INCLUDE_DIR})
file(MAKE_DIRECTORY ${OUTPUT_CMAKE_DIR})

file(MAKE_DIRECTORY ${OUTPUT_PO_CORE_INCLUDE_DIR})
file(MAKE_DIRECTORY ${OUTPUT_PROTO_INCLUDE_DIR})

file(MAKE_DIRECTORY ${TEMP_FILES_DIR})
file(MAKE_DIRECTORY ${GENERATED_FILES_DIR})
file(MAKE_DIRECTORY ${OUTPUT_PYTHON_DIR})

# 设置CMake的标准输出目录变量
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${OUTPUT_BIN_DIR})
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${OUTPUT_LIB_DIR})
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${OUTPUT_LIB_DIR})

# ==============================================================================
# 依赖项检查
# =============================================================================

find_package(Eigen3 REQUIRED)
if(NOT Eigen3_FOUND)
    message(FATAL_ERROR "Eigen3 is required but not found!")
endif()

find_package(Boost REQUIRED COMPONENTS system filesystem)
if(NOT Boost_FOUND)
    message(FATAL_ERROR "Boost is required but not found!")
endif()

find_package(Protobuf REQUIRED)
if(NOT Protobuf_FOUND)
    message(FATAL_ERROR "Protobuf is required but not found!")
endif()
# 根据操作系统设置Spectra库路径
if(UNIX AND NOT APPLE)
    # Ubuntu系统
    set(SPECTRA_INCLUDE_DIR "/media/psf/Home/Documents/spectra-master")
else()
    # macOS系统
    set(SPECTRA_INCLUDE_DIR "/Users/<USER>/Documents/spectra-master")
endif()


# ------------------------------------------------------------------------------
# Abseil库配置
# ------------------------------------------------------------------------------

# 查找Abseil库 - 根据平台设置搜索路径并检查版本
set(ABSEIL_MIN_VERSION "20240722")
set(ABSEIL_SEARCH_PATHS "")

# 根据平台设置搜索路径
if(APPLE)
    # macOS可能的Abseil库位置
    list(APPEND ABSEIL_SEARCH_PATHS 
        "/usr/local/lib/cmake/absl"
        "/opt/homebrew/lib/cmake/absl"
    )
elseif(UNIX AND NOT APPLE)
    # Linux可能的Abseil库位置
    list(APPEND ABSEIL_SEARCH_PATHS 
        "/usr/local/lib/cmake/absl"  # 优先搜索/usr/local
    )
    # 之后才搜索系统路径
    list(APPEND ABSEIL_SEARCH_PATHS
        "/usr/lib/${CMAKE_SYSTEM_PROCESSOR}-linux-gnu/cmake/absl"
    )
endif()

# 添加自定义搜索路径
if(DEFINED ENV{ABSEIL_DIR})
    list(APPEND ABSEIL_SEARCH_PATHS "$ENV{ABSEIL_DIR}")
endif()

# 先尝试直接在/usr/local查找
find_package(absl CONFIG PATHS "/usr/local/lib/cmake/absl" NO_DEFAULT_PATH)

# 如果找不到，再尝试使用搜索路径列表
if(NOT absl_FOUND)
    # 尝试查找Abseil
    find_package(absl CONFIG PATHS ${ABSEIL_SEARCH_PATHS})
endif()

# 检查版本
if(absl_FOUND)
    message(STATUS "Found Abseil version: ${absl_VERSION} at ${absl_DIR}")
    
    # 检查版本是否满足要求
    if(absl_VERSION VERSION_LESS ABSEIL_MIN_VERSION)
        message(WARNING "Found Abseil version ${absl_VERSION} is older than required version ${ABSEIL_MIN_VERSION}")
        message(STATUS "Searching for newer version...")
        
        # 尝试明确指定使用/usr/local的版本
        find_package(absl CONFIG PATHS "/usr/local/lib/cmake/absl" NO_DEFAULT_PATH)
        
        if(absl_FOUND AND NOT absl_VERSION VERSION_LESS ABSEIL_MIN_VERSION)
            message(STATUS "Found suitable Abseil version: ${absl_VERSION} at ${absl_DIR}")
        else()
            message(FATAL_ERROR "Cannot find Abseil version >= ${ABSEIL_MIN_VERSION}. Please install a newer version.")
        endif()
    endif()
else()
    message(FATAL_ERROR "Abseil not found. Please install Abseil.")
endif()

# ==============================================================================
# 版本信息配置
# ==============================================================================
# 生成版本头文件到 po_core 子目录
configure_file(
    ${CMAKE_SOURCE_DIR}/version.hpp.in
    ${OUTPUT_INCLUDE_DIR}/po_core/version.hpp
    @ONLY
)

# ==============================================================================
# 子项目构建
# ==============================================================================
# 添加子目录构建（顺序很重要）
add_subdirectory(src/proto)      # 首先构建proto，因为其他模块依赖它
add_subdirectory(src/internal)
add_subdirectory(src/fileIO)


# ==============================================================================
# po_core 主库构建
# ==============================================================================
# 定义po_core库
# 创建po_core共享库或静态库,具体取决于BUILD_SHARED_LIBS选项
add_library(po_core po_core.cpp)

# 设置库属性
# VERSION: 设置库的版本号,使用项目版本号
# SOVERSION: 设置库的ABI版本号,使用主版本号
# EXPORT_NAME: 导出时使用的目标名称
set_target_properties(po_core PROPERTIES
    VERSION ${PROJECT_VERSION}          # 例如 1.2.3
    SOVERSION ${PROJECT_VERSION_MAJOR}  # 例如 1
    EXPORT_NAME po_core                    # 导出名称为 core
    LIBRARY_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"     # 动态库输出路径
    ARCHIVE_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"     # 静态库输出路径
)

# 链接依赖库
# PUBLIC: 依赖项会传递给使用po_core的目标
# pomvg_file_io: 文件IO操作库
# pomvg_factory: 工厂模式实现库  
# pomvg_proto: Protocol Buffers库
target_link_libraries(po_core
    PUBLIC
        pomvg_file_io    # 提供文件读写功能
        pomvg_factory    # 提供工厂类实现
        pomvg_proto      # 提供protobuf相关功能
        
)

# 配置头文件包含路径
# PRIVATE: 仅在编译po_core时使用的头文件路径
# PUBLIC: 编译和使用po_core时都需要的头文件路径
# BUILD_INTERFACE: 构建时的头文件路径
# INSTALL_INTERFACE: 安装后的头文件路径
target_include_directories(po_core
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}/src/internal    # 内部实现头文件
    PUBLIC
        $<BUILD_INTERFACE:${OUTPUT_INCLUDE_DIR}>    # 修改为使用OUTPUT_INCLUDE_DIR
        $<INSTALL_INTERFACE:include>                      # 安装后的头文件路径
)

# ==============================================================================
# 设置所有目标的通用属性
# ==============================================================================
foreach(target
    pomvg_proto    # 注意这里不包含 po_core，因为它已经设置过了
    pomvg_file_io
    pomvg_factory
)
    if(TARGET ${target})
        set_target_properties(${target} PROPERTIES
            VERSION ${PROJECT_VERSION}
            SOVERSION ${PROJECT_VERSION_MAJOR}
            POSITION_INDEPENDENT_CODE ON
            LIBRARY_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
            ARCHIVE_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
            RUNTIME_OUTPUT_DIRECTORY "${OUTPUT_BIN_DIR}"
        )
    endif()
endforeach()


# ==============================================================================
# 主头文件生成和头文件复制 （暴露给外部使用的头文件）
# ==============================================================================
# 生成主头文件
configure_file(
    ${CMAKE_SOURCE_DIR}/po_core.hpp.in
    ${OUTPUT_INCLUDE_DIR}/po_core.hpp
    @ONLY
)

# 复制接口头文件到输出目录
set(INTERFACE_HEADERS
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/interfaces.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/interfaces_preset.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/interfaces_preset_profiler.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/interfaces_robust_estimator.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/factory/factory.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/fileIO/file_io.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/fileIO/g2o_io.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/pomvg_plugin_register.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/types.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/ransac_estimator.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/inifile.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/pomvg_plugin.hpp" 
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/gnc_irls.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/pb_dataio.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/evaluator.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/internal/executable_path.hpp"
)

# 创建po_core目录
file(MAKE_DIRECTORY "${OUTPUT_INCLUDE_DIR}/po_core")

# 添加调试信息
message(STATUS "Copying interface headers to: ${OUTPUT_INCLUDE_DIR}/po_core")

# 验证并复制每个头文件
foreach(header ${INTERFACE_HEADERS})
    if(NOT EXISTS ${header})
        message(FATAL_ERROR "Source header not found: ${header}")
    endif()
    get_filename_component(filename ${header} NAME)
    message(STATUS "Copying ${filename}")
    file(COPY ${header} DESTINATION "${OUTPUT_INCLUDE_DIR}/po_core")
endforeach()

# ==============================================================================
# 导出配置
# ==============================================================================
include(CMakePackageConfigHelpers)

# 使用 configure_file 生成 po_core-targets.cmake
# 注意：PROJECT_VERSION 和 PROJECT_VERSION_MAJOR 将在 configure_file 时被替换
# 定义要替换的版本变量
set(PO_CORE_VERSION ${PROJECT_VERSION})
set(PO_CORE_VERSION_MAJOR ${PROJECT_VERSION_MAJOR})
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/po_core-targets.cmake.in"
    "${OUTPUT_CMAKE_DIR}/po_core-targets.cmake"
    @ONLY # 只替换 @VAR@ 形式的变量
)

# 生成配置文件
configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/po_core-config.cmake.in"
    "${OUTPUT_CMAKE_DIR}/po_core-config.cmake"
    INSTALL_DESTINATION "lib/cmake/po_core"
)

# 生成版本文件
write_basic_package_version_file(
    "${OUTPUT_CMAKE_DIR}/po_core-config-version.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

# ==============================================================================
# 测试配置
# ==============================================================================
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# ==============================================================================
# Python Drawer 配置
# ==============================================================================
# 复制po_core/drawer中的Python脚本到po_core_lib/python目录
set(DRAWER_SOURCE_DIR "${CMAKE_SOURCE_DIR}/drawer")

if(EXISTS "${DRAWER_SOURCE_DIR}")
    # 获取drawer目录中的各类文件
    file(GLOB DRAWER_PYTHON_SCRIPTS "${DRAWER_SOURCE_DIR}/*.py")
    file(GLOB DRAWER_LAUNCHER_SCRIPTS "${DRAWER_SOURCE_DIR}/*.sh" "${DRAWER_SOURCE_DIR}/*.bat")
    file(GLOB DRAWER_DOCS "${DRAWER_SOURCE_DIR}/*.md")
    file(GLOB DRAWER_CONFIGS "${DRAWER_SOURCE_DIR}/*.txt")
    
    # 拷贝Python脚本
    if(DRAWER_PYTHON_SCRIPTS)
        file(COPY ${DRAWER_PYTHON_SCRIPTS} DESTINATION ${OUTPUT_PYTHON_DIR})
        message(STATUS "Copied Python drawer scripts to: ${OUTPUT_PYTHON_DIR}")
    endif()
    
    # 拷贝启动脚本
    if(DRAWER_LAUNCHER_SCRIPTS)
        file(COPY ${DRAWER_LAUNCHER_SCRIPTS} DESTINATION ${OUTPUT_PYTHON_DIR})
        message(STATUS "Copied drawer launcher scripts to: ${OUTPUT_PYTHON_DIR}")
        
        # 在Unix系统上给shell脚本添加执行权限
        if(UNIX)
            file(GLOB SHELL_SCRIPTS "${OUTPUT_PYTHON_DIR}/*.sh")
            foreach(SCRIPT ${SHELL_SCRIPTS})
                execute_process(COMMAND chmod +x "${SCRIPT}")
            endforeach()
            message(STATUS "Set executable permissions for drawer shell scripts")
        endif()
    endif()
    
    # 拷贝文档文件
    if(DRAWER_DOCS)
        file(COPY ${DRAWER_DOCS} DESTINATION ${OUTPUT_PYTHON_DIR})
        message(STATUS "Copied drawer documentation to: ${OUTPUT_PYTHON_DIR}")
    endif()
    
    # 拷贝配置文件
    if(DRAWER_CONFIGS)
        file(COPY ${DRAWER_CONFIGS} DESTINATION ${OUTPUT_PYTHON_DIR})
        message(STATUS "Copied drawer config files to: ${OUTPUT_PYTHON_DIR}")
    endif()
    
    # 拷贝子目录（如果存在conda_env等）
    file(GLOB DRAWER_SUBDIRS "${DRAWER_SOURCE_DIR}/*/")
    foreach(SUBDIR ${DRAWER_SUBDIRS})
        get_filename_component(SUBDIR_NAME ${SUBDIR} NAME)
        file(COPY ${SUBDIR} DESTINATION ${OUTPUT_PYTHON_DIR})
        message(STATUS "Copied drawer subdirectory ${SUBDIR_NAME} to: ${OUTPUT_PYTHON_DIR}")
    endforeach()
    
    message(STATUS "Python Drawer integration completed successfully")
else()
    message(WARNING "Drawer source directory not found at: ${DRAWER_SOURCE_DIR}")
endif()
