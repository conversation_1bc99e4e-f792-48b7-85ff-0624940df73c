# PoMVG 配置文件示例
# 支持 # 和 ; 两种注释格式
# 
# 这个文件演示了各种注释的使用方式

; 这也是注释行，使用分号
; 分号注释在某些场合比较常用

[camera_params]
# 相机内参配置
focal_length=800.0          # 焦距 (像素)
cx=320.0                    ; 主点x坐标
cy=240.0                    ; 主点y坐标
k1=-0.1                     # 径向畸变系数1
k2=0.05                     # 径向畸变系数2
; k3=0.0                    # 径向畸变系数3 (已禁用)

[feature_extraction]
# 特征提取参数
method=SIFT                 ; 使用SIFT特征
max_features=1000           # 最大特征点数量
# min_hessian=400           # 最小Hessian阈值 (SURF专用，已注释)
octave_layers=3             ; 金字塔层数
contrast_threshold=0.04     # 对比度阈值
edge_threshold=10           ; 边缘阈值

[matching]
; 特征匹配参数
ratio_threshold=0.8         # Lowe比率测试阈值
; cross_check=true          # 交叉检查 (已禁用)
max_distance=100            ; 最大匹配距离

# Bundle Adjustment 配置
[bundle_adjustment]
max_iterations=50           # 最大迭代次数
function_tolerance=1e-6     ; 函数收敛容差
gradient_tolerance=1e-10    # 梯度收敛容差
parameter_tolerance=1e-8    ; 参数收敛容差
; use_robust_loss=true      # 使用鲁棒损失函数 (已禁用)
; robust_loss_scale=1.0     # 鲁棒损失尺度

[output]
# 输出设置
save_intermediate=true      ; 保存中间结果
output_format=PLY           # 输出格式: PLY, OBJ, etc.
; debug_mode=false          # 调试模式 (已禁用)
verbose_level=2             # 详细程度: 0-静默, 1-正常, 2-详细

# 高级设置
[advanced]
# 线程和性能相关
num_threads=8               ; 使用的线程数 (-1表示自动检测)
memory_limit=4096           # 内存限制 (MB)
; use_gpu=false             # 使用GPU加速 (实验性功能，已禁用)

# 临时文件设置
temp_directory=./temp       # 临时文件目录
; cleanup_temp=true         # 自动清理临时文件

# 质量控制
[quality_control]
min_track_length=3          ; 最小轨迹长度
max_reprojection_error=4.0  # 最大重投影误差
; outlier_ratio=0.1         # 异常值比率阈值 (已禁用)
confidence_level=0.99       # 置信度水平

# 这是文件末尾的注释
; 感谢使用 PoMVG! 