# 多类型Note系统使用指南

## 概述
增强版的Note系统支持为每个评估结果添加多种类型的备注信息，使数据分析更加灵活和详细。

## 主要特性

### 1. 多类型Note支持
- 每个评估结果可以包含多种类型的note
- note类型可以自定义，如: `view_pairs`, `match_num`, `algorithm_params`等
- 支持灵活的CSV导出，可选择特定note类型

### 2. 向后兼容
- 保持与旧版本代码的兼容性
- 旧的`SubmitNoteMsg(string)`方法仍然可用，会自动归类为`default`类型

## 使用示例

### 3.1 在EvaluatorStatus中添加多类型Note

```cpp
// 方式1：先添加结果，后添加note
eval_status.AddResult("rotation_error_deg", rotation_error);
eval_status.SubmitNoteMsg("view_pairs", "(1,3)");
eval_status.SubmitNoteMsg("match_num", "256");
eval_status.SubmitNoteMsg("algorithm_type", "RANSAC");

// 方式2：直接添加结果和多类型note
std::unordered_map<std::string, std::string> notes;
notes["view_pairs"] = "(1,3)";
notes["match_num"] = "256"; 
notes["algorithm_type"] = "RANSAC";
eval_status.AddResult("rotation_error_deg", rotation_error, notes);
```

### 3.2 CSV导出时选择特定Note类型

```cpp
// 导出所有指标，不包含note信息
EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", output_dir);

// 只导出view_pairs类型的note
EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", output_dir, "view_pairs");

// 导出多种note类型，用"|"分隔
EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", output_dir, "view_pairs|match_num|algorithm_type");
```

### 3.3 生成的CSV格式示例

#### 不包含note的CSV：
```csv
Algorithm,EvalCommit,Value
method_LiRP,noise_0.1,1.234
method_LiRP,noise_0.2,2.345
```

#### 包含单一note类型的CSV：
```csv
Algorithm,EvalCommit,Value,view_pairs
method_LiRP,noise_0.1,1.234,"(1,3)"
method_LiRP,noise_0.2,2.345,"(2,4)"
```

#### 包含多种note类型的CSV：
```csv
Algorithm,EvalCommit,Value,view_pairs,match_num,algorithm_type
method_LiRP,noise_0.1,1.234,"(1,3)","256","RANSAC"
method_LiRP,noise_0.2,2.345,"(2,4)","128","RANSAC"
```

## 实际应用案例

### 4.1 相对位姿评估中的应用

```cpp
// DataRelativePoses::Evaluate()方法中的实现
for (size_t i = 0; i < rotation_errors.size(); i++) {
    // 准备多类型note数据
    std::unordered_map<std::string, std::string> note_data;
    note_data["view_pairs"] = "(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")";
    note_data["view_i"] = std::to_string(relative_poses_[i].i);
    note_data["view_j"] = std::to_string(relative_poses_[i].j);
    
    // 添加误差及其note
    eval_status.AddResult("rotation_error_deg", rotation_errors[i], note_data);
    eval_status.AddResult("translation_error_deg", translation_errors[i], note_data);
}
```

### 4.2 导出特定分析需求的数据

```cpp
// 只导出视图对信息，用于分析不同视图对的性能
EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", "./analysis/", "view_pairs");

// 导出视图对和匹配数量信息，用于分析匹配质量对精度的影响
EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", "./analysis/", "view_pairs|match_num");

// 导出所有note信息，用于综合分析
EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", "./analysis/", "view_pairs|view_i|view_j");
```

## 优势

1. **灵活性**：可以根据分析需求选择导出特定类型的note
2. **扩展性**：轻松添加新的note类型而不影响现有代码
3. **兼容性**：与现有代码完全兼容
4. **分析友好**：生成的CSV格式便于数据分析和可视化

## 数据验证机制

系统会自动验证note数据的一致性：

### 验证规则
1. **size匹配**：`note_data`的大小必须等于`eval_results`的大小
2. **note类型一致性**：每个note类型的出现次数要么是1（所有结果共享），要么等于eval_results的长度（一对一对应）

### 验证示例

```cpp
// 正确的使用方式1：所有结果共享相同的note
eval_status.AddResult("error1", 1.0);
eval_status.SubmitNoteMsg("algorithm", "RANSAC");
eval_status.AddResult("error2", 2.0);
eval_status.SubmitNoteMsg("algorithm", "RANSAC"); // 相同的note类型和值

// 正确的使用方式2：每个结果有对应的note
eval_status.AddResult("error1", 1.0);
eval_status.SubmitNoteMsg("view_pair", "(1,2)");
eval_status.AddResult("error2", 2.0);
eval_status.SubmitNoteMsg("view_pair", "(3,4)"); // 不同的值但相同的note类型

// 错误的使用方式：note类型出现次数不一致
eval_status.AddResult("error1", 1.0);
eval_status.SubmitNoteMsg("view_pair", "(1,2)");
eval_status.AddResult("error2", 2.0);
eval_status.AddResult("error3", 3.0);
eval_status.SubmitNoteMsg("view_pair", "(3,4)"); // 只有2个view_pair，但有3个results
```

## 注意事项

1. note类型名称建议使用有意义的命名，如`view_pairs`, `match_num`等
2. 避免在note类型名称中使用特殊字符，建议使用下划线连接
3. 如果某个评估结果没有指定类型的note，CSV中会显示为空字符串
4. 多种note类型用"|"分隔时，类型名称不能包含"|"字符
5. **重要**：系统会自动验证note数据一致性，如果验证失败，`ProcessEvaluationResults`会返回false

## 迁移指南

### 从旧版本迁移
旧代码：
```cpp
eval_status.AddResult("error", value);
eval_status.SubmitNoteMsg("some_note");
```

新代码（推荐）：
```cpp
std::unordered_map<std::string, std::string> notes;
notes["category"] = "some_note";
eval_status.AddResult("error", value, notes);
```

或者保持兼容（会自动归类为default类型）：
```cpp
eval_status.AddResult("error", value);
eval_status.SubmitNoteMsg("some_note"); // 自动归类为"default"类型
``` 