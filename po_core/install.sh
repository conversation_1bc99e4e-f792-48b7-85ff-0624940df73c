#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 定义安装目录
INSTALL_PREFIX="/usr/local"
LIB_DIR="${INSTALL_PREFIX}/lib"
INCLUDE_DIR="${INSTALL_PREFIX}/include"
CMAKE_DIR="${LIB_DIR}/cmake/po_core"

# 打印标题
echo -e "${GREEN}PO-Core Library Installer${NC}"
echo "=============================="

# 检查是否有root权限
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}Error: Please run as root (use sudo)${NC}"
    exit 1
fi

# 检查必要目录是否存在
if [ ! -d "lib" ] || [ ! -d "include" ]; then
    echo -e "${RED}Error: Required directories not found. Please ensure you're running this script from the correct directory.${NC}"
    exit 1
fi

# 创建安装目录
echo -e "${YELLOW}Creating installation directories...${NC}"
mkdir -p "${LIB_DIR}"
mkdir -p "${INCLUDE_DIR}"
mkdir -p "${CMAKE_DIR}"

# 复制库文件
echo -e "${YELLOW}Installing library files...${NC}"
cp -P lib/lib*.so* "${LIB_DIR}/"
cp -P lib/lib*.a "${LIB_DIR}/" 2>/dev/null || true  # 静态库可能不存在

# 复制头文件
echo -e "${YELLOW}Installing header files...${NC}"
cp -r include/* "${INCLUDE_DIR}/"

# 复制CMake配置文件
echo -e "${YELLOW}Installing CMake configuration files...${NC}"
if [ -d "lib/cmake/po_core" ]; then
    cp -r lib/cmake/po_core/* "${CMAKE_DIR}/"
fi

# 复制许可证文件（如果存在）
if [ -f "LICENSE" ]; then
    echo -e "${YELLOW}Installing license file...${NC}"
    cp LICENSE "${INSTALL_PREFIX}/share/doc/po_core/"
fi

# 更新动态库缓存
echo -e "${YELLOW}Updating library cache...${NC}"
ldconfig

# 设置适当的权限
echo -e "${YELLOW}Setting permissions...${NC}"
chmod 644 "${LIB_DIR}"/libpomvg_*.so*
chmod 644 "${CMAKE_DIR}"/*
find "${INCLUDE_DIR}/po_core" -type f -exec chmod 644 {} \;

# 如果许可证文件存在，也设置其权限
if [ -f "${INSTALL_PREFIX}/share/doc/po_core/LICENSE" ]; then
    chmod 644 "${INSTALL_PREFIX}/share/doc/po_core/LICENSE"
fi

echo -e "${GREEN}Installation completed successfully!${NC}"
echo ""
echo "Library files installed to: ${LIB_DIR}"
echo "Header files installed to: ${INCLUDE_DIR}"
echo "CMake files installed to: ${CMAKE_DIR}"
echo ""
echo -e "${YELLOW}To use the library in your CMake project:${NC}"
echo "find_package(po_core REQUIRED)"
echo "target_link_libraries(your_target PRIVATE PoMVG::po_core)"