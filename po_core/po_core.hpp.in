// This file is auto-generated by CMake
// Copyright (c) 2024 PoSDK Project

#ifndef POMVG_CORE_HPP
#define POMVG_CORE_HPP

// Core interfaces
#include <po_core/interfaces.hpp>
#include <po_core/interfaces_preset.hpp>
#include <po_core/interfaces_preset_profiler.hpp>
#include <po_core/interfaces_robust_estimator.hpp>
#include <po_core/ransac_estimator.hpp>
#include <po_core/gnc_irls.hpp>
// Factory and IO
#include <po_core/factory.hpp>
#include <po_core/file_io.hpp>

// Plugin system
#include <po_core/pomvg_plugin_register.hpp>

// Common types and version
#include <po_core/types.hpp>
#include <po_core/version.hpp>

namespace PoSDK
{
    // 可以在这里添加一些公共函数或别名
}

#endif // POMVG_CORE_HPP