# Ubuntu上使用Qt Creator编译PoMVG项目指南

## 前期准备

确保安装以下依赖库：

```bash
# 安装基本构建工具
sudo apt update
sudo apt install build-essential cmake

# 安装依赖库
sudo apt install libboost-all-dev libgflags-dev libgoogle-glog-dev libprotobuf-dev protobuf-compiler
sudo apt install libeigen3-dev libsuitesparse-dev libceres-dev libatlas-base-dev libmetis-dev

# 安装GoogleTest (如果需要运行测试)
sudo apt install libgtest-dev
```

## 配置项目

1. 打开Qt Creator
2. 选择"文件" -> "打开文件或项目"，选择项目根目录中的`CMakeLists.txt`文件
3. 在配置项目时，建议使用单独的构建目录，例如`build-ubuntu`
4. 构建类型选择`Debug`或`Release`

## 可能的问题与解决方案

### gflags库未找到

如果遇到类似以下错误：
```
No rule to make target '/opt/homebrew/lib/libgflags.dylib', needed by '...'
```

这是因为项目中引用了macOS特定的库路径。我们已经修改了`src/relative_process/CMakeLists.txt`文件以兼容Ubuntu，但如果还有其他位置引用了macOS路径，可能需要类似的修改。

### 找不到特定的头文件

如果编译时报错找不到某些头文件，可以检查对应库是否正确安装，并确保CMake能够找到它们。例如，对于Spectra库：

```bash
# 下载Spectra库
git clone https://github.com/yixuan/spectra.git
cd spectra
# 安装到系统
sudo cp -r include/* /usr/local/include/
```

## 调试

1. 在Qt Creator中点击左侧的"调试"按钮(或按F5)开始调试
2. 设置断点：在代码编辑器左侧边栏点击行号处
3. 查看变量：使用调试窗口中的"局部变量"和"观察"面板

## 提示

- 首次构建项目时，确保使用`-j`参数限制并行编译的线程数，避免内存不足：
  ```bash
  make -j4  # 使用4个线程编译
  ```
- 如果遇到链接错误，尝试清理构建目录并重新构建：
  ```bash
  rm -rf build-ubuntu
  mkdir build-ubuntu
  cd build-ubuntu
  cmake ..
  make -j4
  ``` 