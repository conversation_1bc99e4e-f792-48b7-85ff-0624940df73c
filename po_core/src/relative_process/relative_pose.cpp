#include "relative_pose.hpp"

namespace PoSDK
{

    inline std::vector<int> find_two_smallest_indices(const Eigen::VectorXd &vec)
    {
        int index1 = -1, index2 = -1;
        double smallest = std::numeric_limits<double>::max();
        double secondSmallest = std::numeric_limits<double>::max();

        // Find the index of the smallest and second smallest value
        for (int i = 0; i < vec.size(); ++i)
        {
            if (vec[i] < smallest)
            {
                secondSmallest = smallest;
                index2 = index1;

                smallest = vec[i];
                index1 = i;
            }
            else if (vec[i] < secondSmallest)
            {
                secondSmallest = vec[i];
                index2 = i;
            }
        }

        std::vector<int> indices = {index1, index2};
        return indices;
    }

    inline double find_median_relative_pose(VectorXd a)
    {
        int n = a.size();

        // 创建一个临时向量并复制数据
        VectorXd temp = a;

        // 排序临时向量
        std::sort(temp.data(), temp.data() + temp.size());

        // 如果数组大小是偶数
        if (n % 2 == 0)
        {
            return (temp[(n - 1) / 2] + temp[n / 2]) / 2.0;
        }
        // 如果数组大小是奇数
        else
        {
            return temp[n / 2];
        }
    }

    double find_median_relative_pose(vector<double> a)
    {

        int n = a.size();
        // If size of the arr[] is even
        if (n % 2 == 0)
        {

            // Applying nth_element
            // on n/2th index
            nth_element(a.begin(),
                        a.begin() + n / 2,
                        a.end());

            // Applying nth_element
            // on (n-1)/2 th index
            nth_element(a.begin(),
                        a.begin() + (n - 1) / 2,
                        a.end());

            // Find the average of value at
            // index N/2 and (N-1)/2
            return (double)(a[(n - 1) / 2] + a[n / 2]) / 2.0;
        }

        // If size of the arr[] is odd
        else
        {

            // Applying nth_element
            // on n/2
            nth_element(a.begin(),
                        a.begin() + n / 2,
                        a.end());

            // Value at index (N/2)th
            // is the median
            return (double)a[n / 2];
        }
    }

    Eigen::Vector3d triangulate2(const Eigen::Matrix3d &R,
                                 const Eigen::Vector3d &t,
                                 const Eigen::Vector3d &point1,
                                 const Eigen::Vector3d &point2)
    {
        // 将第二个点的方向向量旋转到第一个相机的坐标系下
        Eigen::Vector3d point2_unrotated = R * point2;

        // 构造矩阵 A 和向量 b
        Eigen::Vector2d b;
        b[0] = t.dot(point1);
        b[1] = t.dot(point2_unrotated);

        Eigen::Matrix2d A;
        A(0, 0) = point1.dot(point1);
        A(1, 0) = point1.dot(point2_unrotated);
        A(0, 1) = -A(1, 0);
        A(1, 1) = -point2_unrotated.dot(point2_unrotated);

        // 计算拉格朗日乘数
        Eigen::Vector2d lambda = A.inverse() * b;

        // 计算在各自相机坐标系下的3D点
        Eigen::Vector3d xm = lambda[0] * point1;
        Eigen::Vector3d xn = t + lambda[1] * point2_unrotated;

        // 返回世界坐标系下的3D点
        Eigen::Vector3d worldPoint = (xm + xn) / 2;
        return worldPoint;
    }

    Eigen::Vector3d triangulateOnePoint(const Eigen::Matrix3d &R,
                                        const Eigen::Vector3d &t,
                                        const Eigen::Vector3d &point1,
                                        const Eigen::Vector3d &point2)
    {
        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        MatrixXd A(4, 4);
        A.row(0) = point1(0) * P1.row(2) - point1(2) * P1.row(0);
        A.row(1) = point1(1) * P1.row(2) - point1(2) * P1.row(1);
        A.row(2) = point2(0) * P2.row(2) - point2(2) * P2.row(0);
        A.row(3) = point2(1) * P2.row(2) - point2(2) * P2.row(1);

        Eigen::JacobiSVD<MatrixXd> svd(A, Eigen::ComputeFullV);
        VectorXd X = svd.matrixV().col(3);
        Eigen::Vector3d worldPoint = X.head<3>() / X(3);

        return worldPoint;
    }

    /**
     * @brief 处理本质矩阵的特征向量解
     */
    Matrix<double, 3, 4> process_Evec_opt(const MatrixXd &Evec_sols,
                                          const BearingVectors &v1,
                                          const BearingVectors &v2,
                                          const MatrixXd &A,
                                          const VectorXd *ptr_weights,
                                          const Eval_Residual_func &residual_func,
                                          bool compute_mode,
                                          bool use_median,
                                          RTCheckType rt_check_type)
    {
        try
        {
            // 参数检查
            if (Evec_sols.rows() != 9)
            {
                throw std::invalid_argument("Evec_sols must have 9 rows");
            }
            if (v1.cols() != v2.cols() || v1.rows() != 3 || v2.rows() != 3)
            {
                throw std::invalid_argument("Invalid bearing vector dimensions");
            }

            const int num_matches = v1.cols();

            // 过滤掉含NaN的列
            std::vector<int> valid_indices;
            valid_indices.reserve(Evec_sols.cols());
            for (int i = 0; i < Evec_sols.cols(); ++i)
            {
                if (!Evec_sols.col(i).array().isNaN().any())
                {
                    valid_indices.push_back(i);
                }
            }

            // 构建有效的Evec矩阵
            Matrix<double, 9, Dynamic> Evec(9, valid_indices.size());
            for (size_t i = 0; i < valid_indices.size(); ++i)
            {
                Evec.col(i) = Evec_sols.col(valid_indices[i]);
            }

            // 处理无有效解的情况
            if (valid_indices.empty())
            {
                std::cerr << "Warning: No valid solutions found in Evec_sols" << std::endl;
                return Matrix<double, 3, 4>::Constant(std::numeric_limits<double>::quiet_NaN());
            }

            // 预分配存储空间
            std::vector<Matrix<double, 3, 4>> solutions;
            solutions.reserve(4); // 固定4个解
            Vector4d solution_costs;

            Matrix<double, 3, 4> tmp_solution;

            // 计算不同解的代价
            if (compute_mode)
            {
                for (int i = 0; i < 4; ++i)
                {
                    const int cols = (i < 2) ? 6 : 3;
                    const int offset = (i < 2) ? i * 6 : 12 + (i - 2) * 3;

                    // 对齐processEvec2：前两组(6+6)用evec_mode=false，后两组(3+3)用evec_mode=true
                    const bool evec_mode = (i >= 2); // i=0,1时false; i=2,3时true

                    solution_costs(i) = compute_cost(
                        Evec.middleCols(offset, cols),
                        A, v1, v2, evec_mode, ptr_weights,
                        residual_func, tmp_solution, use_median, rt_check_type);

                    solutions.push_back(tmp_solution);
                }
            }
            else
            {
                for (int i = 0; i < 4; ++i)
                {
                    const int cols = (i < 2) ? 6 : 3;
                    const int offset = (i < 2) ? i * 6 : 12 + (i - 2) * 3;

                    solution_costs(i) = compute_cost_rt(
                        Evec.middleCols(offset, cols),
                        A, v1, v2, true, ptr_weights,
                        residual_func, tmp_solution, use_median);

                    solutions.push_back(tmp_solution);
                }
            }

            // 选择最优解
            int best_solution_idx;
            solution_costs.minCoeff(&best_solution_idx);

            return solutions[best_solution_idx];
        }
        catch (const std::exception &e)
        {
            std::cerr << "Error in ProcessEvec2: " << e.what() << std::endl;
            return Matrix<double, 3, 4>::Constant(std::numeric_limits<double>::quiet_NaN());
        }
    }

    Matrix<double, 3, 4> process_Evec(const MatrixXd &Evec_sols,
                                      const BearingVectors &v1,
                                      const BearingVectors &v2,
                                      const MatrixXd &A,
                                      const VectorXd *ptr_weigths,
                                      const Eval_Residual_func &residual_func,
                                      bool compute_mode,
                                      bool use_median,
                                      RTCheckType rt_check_type)
    {
        int num_matches = v1.cols();
        // Filter out columns in Evec with NaN
        vector<int> I;
        for (int i = 0; i < Evec_sols.cols(); ++i)
        {
            if (!isnan(Evec_sols.col(i).sum()))
            { // Check if no element is NaN
                I.push_back(i);
            }
        }

        // Select valid columns only
        Matrix<double, 9, Dynamic> Evec(9, I.size());
        for (size_t i = 0; i < I.size(); ++i)
        {
            Evec.col(i) = Evec_sols.col(I[i]);
        }

        int numSolutions = Evec.cols();
        if (numSolutions == 0)
        {
            Matrix<double, 3, 4> out = Matrix<double, 3, 4>::Constant(numeric_limits<double>::quiet_NaN());
            // Assuming 'residual' is a vector that should be returned or modified as well. Its declaration is needed outside this code.
            // residual.clear();
            return out;
        }

        vector<Matrix3d> R_sol(numSolutions);
        vector<Vector3d> t_sol(numSolutions);
        vector<double> cost;
        cost.resize(num_matches);

        VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
        vector<RelativePose> right_Rts;

        VectorXd ratios(numSolutions);

        // solutions: 6, 6, 3, 3 = 18

        vector<Matrix<double, 3, 4>> outs;
        Vector4d selected_cost;

        Matrix<double, 3, 4> tmp_Rt;

        if (compute_mode)
        {
            // 对齐processEvec2：前两组(6+6)用evec_mode=false，后两组(3+3)用evec_mode=true
            selected_cost(0) = compute_cost(Evec.middleCols<6>(0), A, v1, v2, false, ptr_weigths, residual_func, tmp_Rt, use_median, rt_check_type);
            outs.emplace_back(tmp_Rt);

            selected_cost(1) = compute_cost(Evec.middleCols<6>(6), A, v1, v2, false, ptr_weigths, residual_func, tmp_Rt, use_median, rt_check_type);
            outs.emplace_back(tmp_Rt);

            selected_cost(2) = compute_cost(Evec.middleCols<3>(12), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median, rt_check_type);
            outs.emplace_back(tmp_Rt);

            selected_cost(3) = compute_cost(Evec.middleCols<3>(15), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median, rt_check_type);
            outs.emplace_back(tmp_Rt);
        }
        else
        {
            selected_cost(0) = compute_cost_rt(Evec.middleCols<6>(0), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median);
            outs.emplace_back(tmp_Rt);

            selected_cost(1) = compute_cost_rt(Evec.middleCols<6>(6), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median);
            outs.emplace_back(tmp_Rt);

            selected_cost(2) = compute_cost_rt(Evec.middleCols<3>(12), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median);
            outs.emplace_back(tmp_Rt);

            selected_cost(3) = compute_cost_rt(Evec.middleCols<3>(15), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median);
            outs.emplace_back(tmp_Rt);
        }

        int min_id;
        selected_cost.minCoeff(&min_id);

        Matrix<double, 3, 4> est_Rt;
        est_Rt = outs[min_id];
        return est_Rt;
    }

    double compute_cost(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                        const MatrixXd &A,
                        const BearingVectors &v1,
                        const BearingVectors &v2,
                        const bool &evec_mode,
                        const VectorXd *ptr_weights,
                        const Eval_Residual_func &residual_func,
                        Matrix<double, 3, 4> &out,
                        bool use_median,
                        RTCheckType rt_check_type)
    {
        int numSolutions = Evec.cols();
        int num_matches = v1.cols();

        double selected_cost = 0;
        vector<double> cost(num_matches);
        VectorXd ratios(numSolutions);
        vector<Matrix3d> R_sol(numSolutions);
        vector<Vector3d> t_sol(numSolutions);
        VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
        vector<RelativePose> right_Rts;

        // 遍历所有解
        for (int i = 0; i < numSolutions; ++i)
        {
            // 构建本质矩阵
            Matrix3d E = Matrix3d::Zero();
            for (int col = 0; col < 3; ++col)
            {
                for (int row = 0; row < 3; ++row)
                {
                    E(row, col) = Evec(row + 3 * col, i);
                }
            }

            // 检查矩阵是否有效
            if (E.norm() < 1e-10)
            {
                ratios(i) = 0;
                total_cost[i] = 1e10;
                continue;
            }

            // 归一化本质矩阵
            E /= E.norm();

            // 从本质矩阵恢复R和t
            essential2RT(E, R_sol, t_sol);

            // 选择最佳RT组合 - 根据rt_check_type动态选择RT_Check函数
            double ratio = 0;
            RelativePose right_pose;

            switch (rt_check_type)
            {
            case RTCheckType::RT_CHECK2:
                right_pose = RT_Check2(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_SUM_OPTIMIZED:
                right_pose = RT_Check2_sum_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_STACK_OPTIMIZED:
                right_pose = RT_Check2_stack_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_DIRECT_OPTIMIZED:
                right_pose = RT_Check2_direct_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_PRECOMPUTED_SUM:
            {
                // 预计算sum_A
                Vector<double, 9> sum_A = Vector<double, 9>::Zero();
                for (int j = 0; j < v1.cols(); ++j)
                {
                    const Vector3d &p1 = v1.col(j);
                    const Vector3d &p2 = v2.col(j);
                    const double w = ptr_weights ? (*ptr_weights)(j) : 1.0;

                    for (int k = 0; k < 3; ++k)
                    {
                        for (int l = 0; l < 3; ++l)
                        {
                            sum_A(k * 3 + l) += w * p2(k) * p1(l);
                        }
                    }
                }
                right_pose = RT_Check2_precomputed_sum(sum_A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
            }
            break;
            case RTCheckType::RT_CHECK2_SIMD_OPTIMIZED:
                right_pose = RT_Check2_simd_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_ULTRA_OPTIMIZED:
                right_pose = RT_Check2_ultra_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_MATRIX_FREE_OPTIMIZED:
                right_pose = RT_Check2_matrix_free_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_BARE_METAL_OPTIMIZED:
                right_pose = RT_Check2_bare_metal_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED:
                right_pose = RT_Check2_bare_metal_optimized_corrected(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_SAFE_ULTRA_OPTIMIZED:
                right_pose = RT_Check2_safe_ultra_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            case RTCheckType::RT_CHECK2_FINAL_OPTIMIZED:
                right_pose = RT_Check2_final_optimized(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            default:
                right_pose = RT_Check2(A, v1, v2, R_sol, t_sol, ptr_weights, ratio);
                break;
            }
            ratios(i) = ratio;

            // 归一化平移向量
            right_pose.tij /= right_pose.tij.norm();

            // 计算残差
            auto tmp_cost = residual_func(v1, v2, right_pose, ptr_weights);

            // 应用权重
            for (int k = 0; k < num_matches; ++k)
            {
                cost[k] = ptr_weights ? (*ptr_weights)(k)*tmp_cost[k] : tmp_cost[k];
            }

            // 计算代价
            double med_cost = find_median_relative_pose(cost);
            double sum_cost = tmp_cost.sum();
            total_cost[i] = use_median ? med_cost : sum_cost;

            right_Rts.emplace_back(right_pose);
        }

        // 选择最优解
        int selected_id;
        if (evec_mode)
        {
            total_cost.minCoeff(&selected_id);
        }
        else
        {
            vector<int> candidate_ids = find_two_smallest_indices(total_cost);
            double max_value = -1e5;
            for (int j = 0; j < candidate_ids.size(); ++j)
            {
                int id = candidate_ids[j];
                if (ratios(id) > max_value)
                {
                    selected_id = id;
                    max_value = ratios(id);
                }
            }
        }

        selected_cost = total_cost(selected_id);

        // 设置输出
        out.block<3, 3>(0, 0) = right_Rts[selected_id].Rij;
        out.col(3) = right_Rts[selected_id].tij;

        return selected_cost;
    }

    double compute_cost_rt(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                           const MatrixXd &A,
                           const BearingVectors &v1,
                           const BearingVectors &v2,
                           const bool &evec_mode,
                           const VectorXd *ptr_weights,
                           const Eval_Residual_func &residual_func,
                           Matrix<double, 3, 4> &out,
                           bool use_median)
    {
        int numSolutions = Evec.cols();
        int num_matches = v1.cols();

        double selected_cost = 0;
        vector<double> cost(num_matches);
        vector<Matrix3d> R_sol(numSolutions);
        vector<Vector3d> t_sol(numSolutions);
        VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
        vector<RelativePose> right_Rts(numSolutions);

        // 遍历所有解
        for (int i = 0; i < numSolutions; ++i)
        {
            // 构建本质矩阵
            Matrix3d E = Matrix3d::Zero();
            for (int col = 0; col < 3; ++col)
            {
                for (int row = 0; row < 3; ++row)
                {
                    E(row, col) = Evec(row + 3 * col, i);
                }
            }

            // 检查矩阵是否有效
            if (E.norm() < 1e-10)
            {
                total_cost[i] = 1e10;
                continue;
            }

            // 归一化本质矩阵
            E /= E.norm();

            // 从本质矩阵恢复R和t
            essential2RT(E, R_sol, t_sol);

            // 遍历所有可能的R和t组合
            double min_Rt_costs = 1e10;
            for (Matrix3d &R_candidate : R_sol)
            {
                for (Vector3d &t_candidate : t_sol)
                {
                    t_candidate = t_candidate / t_candidate.norm();
                    RelativePose tmp_pose;
                    tmp_pose.Rij = R_candidate;
                    tmp_pose.tij = t_candidate;

                    // 计算残差
                    auto tmp_cost = residual_func(v1, v2, tmp_pose, ptr_weights);

                    // 应用权重
                    for (int k = 0; k < num_matches; ++k)
                    {
                        cost[k] = ptr_weights ? (*ptr_weights)(k)*tmp_cost[k] : tmp_cost[k];
                    }

                    // 计算代价
                    double med_cost = find_median_relative_pose(cost);
                    double sum_cost = tmp_cost.sum();
                    double Rt_cost = use_median ? med_cost : sum_cost;

                    // 更新最小代价解
                    if (Rt_cost < min_Rt_costs)
                    {
                        min_Rt_costs = Rt_cost;
                        right_Rts[i] = tmp_pose;
                    }
                }
            }

            total_cost[i] = min_Rt_costs;
        }

        // 选择最优解
        int selected_id;
        total_cost.minCoeff(&selected_id);
        selected_cost = total_cost(selected_id);

        // 设置输出
        out.block<3, 3>(0, 0) = right_Rts[selected_id].Rij;
        out.col(3) = right_Rts[selected_id].tij;

        return selected_cost;
    }

    Matrix<double, 3, 4> process_Evec(const Matrix<double, 9, 9> &Evec,
                                      const BearingVectors &v1,
                                      const BearingVectors &v2,
                                      const VectorXd *ptr_weigths,
                                      const Eval_Residual_func &residual_func,
                                      bool compute_mode,
                                      bool use_median,
                                      RTCheckType rt_check_type)
    {
        int num_matches = v1.cols();

        // solutions: 6, 3 = 9

        vector<Matrix<double, 3, 4>> outs;
        Vector2d selected_cost;

        Matrix<double, 3, 4> tmp_Rt;

        if (compute_mode)
        {
            // 对齐processEvec2：前两组(6)用evec_mode=false，后两组(3)用evec_mode=true
            selected_cost(0) = compute_cost(Evec.middleCols<6>(0), v1, v2, false, ptr_weigths, residual_func, tmp_Rt, use_median, rt_check_type);
            outs.emplace_back(tmp_Rt);

            selected_cost(1) = compute_cost(Evec.middleCols<3>(6), v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median, rt_check_type);
            outs.emplace_back(tmp_Rt);
        }
        else
        {
            selected_cost(0) = compute_cost_rt(Evec.middleCols<6>(0), v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median);
            outs.emplace_back(tmp_Rt);

            selected_cost(1) = compute_cost_rt(Evec.middleCols<3>(6), v1, v2, true, ptr_weigths, residual_func, tmp_Rt, use_median);
            outs.emplace_back(tmp_Rt);
        }

        int min_id;
        selected_cost.minCoeff(&min_id);

        Matrix<double, 3, 4> est_Rt;
        est_Rt = outs[min_id];
        return est_Rt;
    }

    double compute_cost(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                        const BearingVectors &v1,
                        const BearingVectors &v2,
                        const bool &evec_mode,
                        const VectorXd *ptr_weights,
                        const Eval_Residual_func &residual_func,
                        Matrix<double, 3, 4> &out,
                        bool use_median,
                        RTCheckType rt_check_type)
    {
        int numSolutions = Evec.cols();
        int num_matches = v1.cols();

        double selected_cost = 0;
        vector<double> cost(num_matches);
        VectorXd ratios(numSolutions);
        vector<Matrix3d> R_sol(numSolutions);
        vector<Vector3d> t_sol(numSolutions);
        VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
        vector<RelativePose> right_Rts;

        // 遍历所有解
        for (int i = 0; i < numSolutions; ++i)
        {
            // 构建本质矩阵
            Matrix3d E = Matrix3d::Zero();
            for (int col = 0; col < 3; ++col)
            {
                for (int row = 0; row < 3; ++row)
                {
                    E(row, col) = Evec(row + 3 * col, i);
                }
            }

            // 检查矩阵是否有效
            if (E.norm() < 1e-10)
            {
                ratios(i) = 0;
                total_cost[i] = 1e10;
                continue;
            }

            // 归一化本质矩阵
            E /= E.norm();

            // 从本质矩阵恢复R和t
            essential2RT(E, R_sol, t_sol);

            // 选择最佳RT组合 - 根据rt_check_type动态选择RT_Check函数
            double ratio = 0;
            RelativePose right_pose;

            right_pose = RT_Check2_bare_metal_optimized_corrected(v1, v2, R_sol, t_sol, ptr_weights, ratio);
            ratios(i) = ratio;

            // 归一化平移向量
            right_pose.tij /= right_pose.tij.norm();

            // 计算残差
            auto tmp_cost = residual_func(v1, v2, right_pose, ptr_weights);

            // 应用权重
            for (int k = 0; k < num_matches; ++k)
            {
                cost[k] = ptr_weights ? (*ptr_weights)(k)*tmp_cost[k] : tmp_cost[k];
            }

            // 计算代价
            double med_cost = find_median_relative_pose(cost);
            double sum_cost = tmp_cost.sum();
            total_cost[i] = use_median ? med_cost : sum_cost;

            right_Rts.emplace_back(right_pose);
        }

        // 选择最优解
        int selected_id;
        if (evec_mode)
        {
            total_cost.minCoeff(&selected_id);
        }
        else
        {
            vector<int> candidate_ids = find_two_smallest_indices(total_cost);
            double max_value = -1e5;
            for (int j = 0; j < candidate_ids.size(); ++j)
            {
                int id = candidate_ids[j];
                if (ratios(id) > max_value)
                {
                    selected_id = id;
                    max_value = ratios(id);
                }
            }
        }

        selected_cost = total_cost(selected_id);

        // 设置输出
        out.block<3, 3>(0, 0) = right_Rts[selected_id].Rij;
        out.col(3) = right_Rts[selected_id].tij;

        return selected_cost;
    }

    double compute_cost_rt(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                           const BearingVectors &v1,
                           const BearingVectors &v2,
                           const bool &evec_mode,
                           const VectorXd *ptr_weights,
                           const Eval_Residual_func &residual_func,
                           Matrix<double, 3, 4> &out,
                           bool use_median)
    {
        int numSolutions = Evec.cols();
        int num_matches = v1.cols();

        double selected_cost = 0;
        vector<double> cost(num_matches);
        vector<Matrix3d> R_sol(numSolutions);
        vector<Vector3d> t_sol(numSolutions);
        VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
        vector<RelativePose> right_Rts(numSolutions);

        // 遍历所有解
        for (int i = 0; i < numSolutions; ++i)
        {
            // 构建本质矩阵
            Matrix3d E = Matrix3d::Zero();
            for (int col = 0; col < 3; ++col)
            {
                for (int row = 0; row < 3; ++row)
                {
                    E(row, col) = Evec(row + 3 * col, i);
                }
            }

            // 检查矩阵是否有效
            if (E.norm() < 1e-10)
            {
                total_cost[i] = 1e10;
                continue;
            }

            // 归一化本质矩阵
            E /= E.norm();

            // 从本质矩阵恢复R和t
            essential2RT(E, R_sol, t_sol);

            // 遍历所有可能的R和t组合
            double min_Rt_costs = 1e10;
            for (Matrix3d &R_candidate : R_sol)
            {
                for (Vector3d &t_candidate : t_sol)
                {
                    t_candidate = t_candidate / t_candidate.norm();
                    RelativePose tmp_pose;
                    tmp_pose.Rij = R_candidate;
                    tmp_pose.tij = t_candidate;

                    // 计算残差
                    auto tmp_cost = residual_func(v1, v2, tmp_pose, ptr_weights);

                    // 应用权重
                    for (int k = 0; k < num_matches; ++k)
                    {
                        cost[k] = ptr_weights ? (*ptr_weights)(k)*tmp_cost[k] : tmp_cost[k];
                    }

                    // 计算代价
                    double med_cost = find_median_relative_pose(cost);
                    double sum_cost = tmp_cost.sum();
                    double Rt_cost = use_median ? med_cost : sum_cost;

                    // 更新最小代价解
                    if (Rt_cost < min_Rt_costs)
                    {
                        min_Rt_costs = Rt_cost;
                        right_Rts[i] = tmp_pose;
                    }
                }
            }

            total_cost[i] = min_Rt_costs;
        }

        // 选择最优解
        int selected_id;
        total_cost.minCoeff(&selected_id);
        selected_cost = total_cost(selected_id);

        // 设置输出
        out.block<3, 3>(0, 0) = right_Rts[selected_id].Rij;
        out.col(3) = right_Rts[selected_id].tij;

        return selected_cost;
    }

    void essential2RT(const Matrix3d &E,
                      vector<Matrix3d> &R,
                      vector<Vector3d> &t)
    {
        Matrix3d normalizedE = E / E.norm();

        Matrix3d W1;
        W1 << 0, -1, 0,
            1, 0, 0,
            0, 0, 1;

        Matrix3d W2;
        W2 << 0, -1, 0,
            1, 0, 0,
            0, 0, -1;

        JacobiSVD<Matrix3d> svd(normalizedE, ComputeFullU | ComputeFullV);
        Matrix3d U = svd.matrixU();
        Matrix3d V = svd.matrixV();

        R.resize(2);
        if (U.determinant() * V.determinant() > 0)
        {
            R[0] = U * W1 * V.transpose();
            R[1] = U * W1.transpose() * V.transpose();
        }
        else
        {
            R[0] = U * W2 * V.transpose();
            R[1] = U * W2.transpose() * V.transpose();
        }

        t.resize(2);
        t[0] = U.col(2);
        t[1] = -U.col(2);
    }

    /**
     * @brief 从本质矩阵恢复旋转矩阵和平移向量
     */
    void essential2RT_opt(const Matrix3d &E,
                          vector<Matrix3d> &R,
                          vector<Vector3d> &t)
    {
        try
        {
            const Matrix3d W1 = (Matrix3d() << 0, -1, 0,
                                 1, 0, 0,
                                 0, 0, 1)
                                    .finished();

            const Matrix3d W2 = (Matrix3d() << 0, -1, 0,
                                 1, 0, 0,
                                 0, 0, -1)
                                    .finished();

            // SVD分解
            JacobiSVD<Matrix3d> svd(E.normalized(), ComputeFullU | ComputeFullV);
            const Matrix3d &U = svd.matrixU();
            const Matrix3d &V = svd.matrixV();

            // 检查奇异值
            if (svd.singularValues().tail<1>()[0] / svd.singularValues()[0] > 1e-2)
            {
                std::cerr << "Warning: Essential matrix might be ill-conditioned" << std::endl;
            }

            // 根据行列式积的符号选择合适的W矩阵
            R.resize(2);
            const double det_UV = U.determinant() * V.determinant();
            if (det_UV > 0)
            {
                R[0] = U * W1 * V.transpose();
                R[1] = U * W1.transpose() * V.transpose();
            }
            else
            {
                R[0] = U * W2 * V.transpose();
                R[1] = U * W2.transpose() * V.transpose();
            }

            // 提取平移向量
            t.resize(2);
            t[0] = U.col(2);
            t[1] = -U.col(2);

            // 验证结果
            for (const auto &Ri : R)
            {
                if (std::abs(Ri.determinant() - 1.0) > 1e-5)
                {
                    std::cerr << "Warning: Recovered rotation matrix is not orthogonal" << std::endl;
                }
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "Error in Essential2RT: " << e.what() << std::endl;
            R.clear();
            t.clear();
        }
    }

}
