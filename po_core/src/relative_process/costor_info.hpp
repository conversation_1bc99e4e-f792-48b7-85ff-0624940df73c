#ifndef COSTOR_INFO_HPP
#define COSTOR_INFO_HPP

#pragma once
#include "po_core/types.hpp"
#include "relative_residuals.hpp"
#include <string>
#include <functional>
#include <unordered_map>
#include <boost/algorithm/string.hpp>

namespace PoSDK
{
    using namespace Eigen;
    using namespace types;

    // 残差误差形式类型
    enum class ErrorFormType
    {
        REPROJ_PIXEL, // 重投影像素误差
        REPROJ_ANGLE, // 重投影角度误差
        ALGEBRA       // 代数误差
    };

    // 残差函数指针类型
    using CostorFunctionPtr = std::function<VectorXd(const BearingVectors &, const BearingVectors &, const RelativePose &, const VectorXd *)>;

    // 残差信息结构体
    struct CostorInfo
    {
        CostorFunctionPtr function; // 残差函数指针
        int dimension_per_point;    // 每个点的残差维度
        ErrorFormType error_form;   // 错误形式类型
        std::string description;    // 描述信息

        CostorInfo() = default;

        CostorInfo(CostorFunctionPtr func, int dim, ErrorFormType form, const std::string &desc)
            : function(func), dimension_per_point(dim), error_form(form), description(desc) {}
    };

    // 残差信息管理器
    class CostorInfoManager
    {
    public:
        static CostorInfoManager &GetInstance()
        {
            static CostorInfoManager instance;
            return instance;
        }

        // 获取残差信息
        const CostorInfo *GetCostorInfo(const std::string &residual_type) const
        {
            auto it = costor_map_.find(boost::to_lower_copy(residual_type));
            if (it != costor_map_.end())
            {
                return &it->second;
            }
            return nullptr;
        }

        // 计算残差维度
        int GetResidualDimension(const std::string &residual_type, int num_points) const
        {
            const CostorInfo *info = GetCostorInfo(residual_type);
            if (info)
            {
                return info->dimension_per_point * num_points;
            }
            return num_points; // 默认为标量残差
        }

        // 判断是否为3D残差类型
        bool Is3DResidual(const std::string &residual_type) const
        {
            const CostorInfo *info = GetCostorInfo(residual_type);
            return info && info->dimension_per_point == 3;
        }

        // 判断是否为6D残差类型
        bool Is6DResidual(const std::string &residual_type) const
        {
            const CostorInfo *info = GetCostorInfo(residual_type);
            return info && info->dimension_per_point == 6;
        }

        // 判断是否为标量残差类型
        bool IsScalarResidual(const std::string &residual_type) const
        {
            const CostorInfo *info = GetCostorInfo(residual_type);
            return info && info->dimension_per_point == 1;
        }

        // 获取错误形式类型
        ErrorFormType GetErrorFormType(const std::string &residual_type) const
        {
            const CostorInfo *info = GetCostorInfo(residual_type);
            if (info)
            {
                return info->error_form;
            }
            return ErrorFormType::ALGEBRA; // 默认为代数误差
        }

        // 计算残差
        VectorXd ComputeResidual(const std::string &residual_type,
                                 const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *weights = nullptr) const
        {
            const CostorInfo *info = GetCostorInfo(residual_type);
            if (info && info->function)
            {
                return info->function(v1, v2, pose, weights);
            }

            // 默认回退到ppo_opengv
            std::cout << "\033[34mWarning: Unknown residual type '" << residual_type
                      << "', falling back to ppo_opengv\033[0m" << std::endl;
            return residual_ppo_opengv(v1, v2, pose, weights);
        }

    private:
        CostorInfoManager()
        {
            InitializeCostorMap();
        }

        void InitializeCostorMap()
        {
            // 6D残差类型
            costor_map_["ppo_opengv"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_ppo_opengv(v1, v2, pose, weights);
                },
                6, ErrorFormType::REPROJ_ANGLE, "PPO OpenGV残差（6D向量）");

            // 3D残差类型
            costor_map_["ppo_3dvec"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO_3dvec(v1, v2, pose, weights);
                },
                3, ErrorFormType::REPROJ_PIXEL, "PPO残差（3D向量）");
            costor_map_["ligt_3dvec"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_LiGT_3dvec(v1, v2, pose, weights);
                },
                3, ErrorFormType::ALGEBRA, "LiGT残差（3D向量）");
            costor_map_["lirt_3dvec"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_LiRT_3dvec(v1, v2, pose, weights);
                },
                3, ErrorFormType::ALGEBRA, "LiRT残差（3D向量）");
            costor_map_["ppog_3dvec"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPOG_3dvec(v1, v2, pose, weights);
                },
                3, ErrorFormType::REPROJ_PIXEL, "PPOG残差（3D向量）");

            // 标量残差类型
            costor_map_["sampson"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_sampson(v1, v2, pose, weights);
                },
                1, ErrorFormType::ALGEBRA, "Sampson残差");
            costor_map_["coplanar"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_coplanar(v1, v2, pose, weights);
                },
                1, ErrorFormType::ALGEBRA, "共面残差");
            costor_map_["kneip"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_Kneip(v1, v2, pose, weights);
                },
                1, ErrorFormType::ALGEBRA, "Kneip残差");
            costor_map_["ba"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_BA(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_PIXEL, "Bundle Adjustment残差");
            costor_map_["opengv"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_opengv(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_ANGLE, "OpenGV残差");
            costor_map_["ppo_angle"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO_angle(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_ANGLE, "PPO角度残差");
            costor_map_["ppo_angle_rad"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO_angle_rad(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_ANGLE, "PPO角度残差（弧度形式）");
            costor_map_["ppo_angle_sin"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO_angle_sin(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_ANGLE, "PPO角度残差（正弦形式）");
            costor_map_["ppo_angle_2sin"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO_angle_2sin(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_ANGLE, "PPO角度残差（2sin形式）");
            costor_map_["ppo_angle_sqrt"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO_angle_sqrt(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_ANGLE, "PPO角度残差（sqrt形式，更鲁棒）");
            costor_map_["ppo_invd"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO_invd(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_PIXEL, "PPO逆深度残差");
            costor_map_["ligt_direct"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_LiGT_direct(v1, v2, pose, weights);
                },
                1, ErrorFormType::ALGEBRA, "LiGT直接残差");
            costor_map_["ligt_d3"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_LiGT_d3(v1, v2, pose, weights);
                },
                1, ErrorFormType::ALGEBRA, "LiGT D3残差");

            // 标量版本的LiGT、LiRT、PPOG（保持原来的命名）
            costor_map_["ligt"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_LiGT(v1, v2, pose, weights);
                },
                1, ErrorFormType::ALGEBRA, "LiGT残差");
            costor_map_["lirt"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_LiRT(v1, v2, pose, weights);
                },
                1, ErrorFormType::ALGEBRA, "LiRT残差");
            costor_map_["ppog"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPOG(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_PIXEL, "PPOG残差");
            costor_map_["ppo"] = CostorInfo(
                [](const BearingVectors &v1, const BearingVectors &v2, const RelativePose &pose, const VectorXd *weights)
                {
                    return residual_PPO(v1, v2, pose, weights);
                },
                1, ErrorFormType::REPROJ_PIXEL, "PPO残差");
        }

        std::unordered_map<std::string, CostorInfo> costor_map_;
    };

} // namespace PoSDK

#endif // COSTOR_INFO_HPP