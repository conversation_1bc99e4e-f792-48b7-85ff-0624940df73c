# ==============================================================================
# pomvg_relative_process 库构建配置
# ==============================================================================

# 查找源文件
file(GLOB_RECURSE RELATIVE_PROCESS_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
)

file(GLOB_RECURSE RELATIVE_PROCESS_HEADERS 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.hpp"
)

# 创建库目标
add_library(pomvg_relative_process ${RELATIVE_PROCESS_SOURCES} ${RELATIVE_PROCESS_HEADERS})


# 配置编译选项
target_compile_options(pomvg_relative_process
    PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/W4 /wd4251>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# 配置头文件包含路径
target_include_directories(pomvg_relative_process
    PUBLIC
        # 构建时使用的头文件路径
        $<BUILD_INTERFACE:${OUTPUT_INCLUDE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        # 安装后使用的头文件路径
        $<INSTALL_INTERFACE:include>
    PRIVATE
        # 内部实现头文件路径
        ${CMAKE_CURRENT_SOURCE_DIR}
        # Spectra库头文件路径
        ${SPECTRA_INCLUDE_DIR}
        ${SPECTRA_INCLUDE_DIR}/include
)

# 查找gflags库 - 跨平台方式
if(APPLE)
    # MacOS特定路径
    set(GFLAGS_LIB "/opt/homebrew/lib/libgflags.dylib")
    if(NOT EXISTS ${GFLAGS_LIB})
        set(GFLAGS_LIB "-lgflags")
    endif()
else()
    # Linux和其他平台
    find_library(GFLAGS_LIB gflags)
    if(NOT GFLAGS_LIB)
        set(GFLAGS_LIB "-lgflags")
    endif()
endif()

# 链接依赖库
target_link_libraries(pomvg_relative_process
    PUBLIC
        Eigen3::Eigen
        ${GFLAGS_LIB}
)

# ==============================================================================
# 安装配置
# ==============================================================================


# 安装头文件
install(
    FILES ${RELATIVE_PROCESS_HEADERS}
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/po_core/relative_process
)


