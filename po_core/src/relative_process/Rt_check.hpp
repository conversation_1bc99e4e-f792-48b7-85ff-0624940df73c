#ifndef RT_CHECK_HPP
#define RT_CHECK_HPP

#include "po_core/types.hpp"

namespace PoSDK
{
    using namespace Eigen;
    using namespace types;
    using namespace std;

    inline Matrix3d cross_matrix(const Vector3d &x)
    {
        Eigen::Matrix3d y;

        y << 0, -x(2), x(1),
            x(2), 0, -x(0),
            -x(1), x(0), 0;

        return y;
    }

    /**
     * @brief RT_Check函数类型枚举
     */
    enum class RTCheckType
    {
        RT_CHECK2,                                // 原始RT_Check2函数
        RT_CHECK2_SUM_OPTIMIZED,                  // 优化版本1：直接累加
        RT_CHECK2_STACK_OPTIMIZED,                // 优化版本2：栈内存优化
        RT_CHECK2_DIRECT_OPTIMIZED,               // 优化版本3：直接计算
        RT_CHECK2_PRECOMPUTED_SUM,                // 超级优化版本：预计算sum_A
        RT_CHECK2_SIMD_OPTIMIZED,                 // 超级深度优化版本1：SIMD + 内联
        RT_CHECK2_ULTRA_OPTIMIZED,                // 超级深度优化版本2：预计算 + 展开循环
        RT_CHECK2_MATRIX_FREE_OPTIMIZED,          // 极致优化版本：完全去除H矩阵构建
        RT_CHECK2_BARE_METAL_OPTIMIZED,           // 裸机优化版本：最小内存分配
        RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED, // 裸机优化版本：最小内存分配（修正）
        RT_CHECK2_SAFE_ULTRA_OPTIMIZED,           // 安全极致优化版本：确保算法正确性
        RT_CHECK2_FINAL_OPTIMIZED                 // 终极优化版本：在确保正确性基础上的最大优化
    };

    /**
     * @brief RT_Check函数类型与字符串的转换函数
     */
    std::string RTCheckTypeToString(RTCheckType type);
    RTCheckType StringToRTCheckType(const std::string &str);

    /**
     * @brief 检查并选择最优的R和t
     * @param A 约束矩阵
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param R_sols 旋转矩阵候选集
     * @param t_sols 平移向量候选集
     * @param ptr_weights 特征点权重向量指针
     * @param ratio 输出的比率值
     * @return 最优的相对位姿
     */
    RelativePose RT_Check2(const MatrixXd &A,
                           const BearingVectors &v1,
                           const BearingVectors &v2,
                           const vector<Matrix3d> &R_sols,
                           const vector<Vector3d> &t_sols,
                           const VectorXd *ptr_weights,
                           double &ratio);
    RelativePose RT_Check2_opt(const MatrixXd &A,
                               const BearingVectors &v1,
                               const BearingVectors &v2,
                               const vector<Matrix3d> &R_sols,
                               const vector<Vector3d> &t_sols,
                               const VectorXd *ptr_weights,
                               double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 优化版本1：直接累加，不判断>0
     * @param A 约束矩阵
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param R_sols 旋转矩阵候选集
     * @param t_sols 平移向量候选集
     * @param ptr_weights 特征点权重向量指针
     * @param ratio 输出的比率值
     * @return 最优的相对位姿
     */
    RelativePose RT_Check2_sum_optimized(const MatrixXd &A,
                                         const BearingVectors &v1,
                                         const BearingVectors &v2,
                                         const vector<Matrix3d> &R_sols,
                                         const vector<Vector3d> &t_sols,
                                         const VectorXd *ptr_weights,
                                         double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 优化版本2：栈内存优化
     * @param A 约束矩阵
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param R_sols 旋转矩阵候选集
     * @param t_sols 平移向量候选集
     * @param ptr_weights 特征点权重向量指针
     * @param ratio 输出的比率值
     * @return 最优的相对位姿
     */
    RelativePose RT_Check2_stack_optimized(const MatrixXd &A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 优化版本3：直接计算，不用A矩阵
     * @param A 约束矩阵
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param R_sols 旋转矩阵候选集
     * @param t_sols 平移向量候选集
     * @param ptr_weights 特征点权重向量指针
     * @param ratio 输出的比率值
     * @return 最优的相对位姿
     */
    RelativePose RT_Check2_direct_optimized(const MatrixXd &A,
                                            const BearingVectors &v1,
                                            const BearingVectors &v2,
                                            const vector<Matrix3d> &R_sols,
                                            const vector<Vector3d> &t_sols,
                                            const VectorXd *ptr_weights,
                                            double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 使用预计算sum_A的超级优化版本
     * @param sum_A 预计算的A矩阵行和向量(9x1)
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param R_sols 旋转矩阵候选集
     * @param t_sols 平移向量候选集
     * @param ptr_weights 特征点权重向量指针
     * @param ratio 输出的比率值
     * @return 最优的相对位姿
     */
    RelativePose RT_Check2_precomputed_sum(const Vector<double, 9> &sum_A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 超级深度优化版本1：SIMD + 内联
     */
    RelativePose RT_Check2_simd_optimized(const MatrixXd &A,
                                          const BearingVectors &v1,
                                          const BearingVectors &v2,
                                          const vector<Matrix3d> &R_sols,
                                          const vector<Vector3d> &t_sols,
                                          const VectorXd *ptr_weights,
                                          double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 超级深度优化版本2：预计算 + 展开循环
     */
    RelativePose RT_Check2_ultra_optimized(const MatrixXd &A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 极致优化版本：完全去除H矩阵构建
     */
    RelativePose RT_Check2_matrix_free_optimized(const MatrixXd &A,
                                                 const BearingVectors &v1,
                                                 const BearingVectors &v2,
                                                 const vector<Matrix3d> &R_sols,
                                                 const vector<Vector3d> &t_sols,
                                                 const VectorXd *ptr_weights,
                                                 double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 裸机优化版本：最小内存分配
     */
    RelativePose RT_Check2_bare_metal_optimized(const MatrixXd &A,
                                                const BearingVectors &v1,
                                                const BearingVectors &v2,
                                                const vector<Matrix3d> &R_sols,
                                                const vector<Vector3d> &t_sols,
                                                const VectorXd *ptr_weights,
                                                double &ratio);

    RelativePose RT_Check2_bare_metal_optimized_corrected(const MatrixXd &A,
                                                          const BearingVectors &v1,
                                                          const BearingVectors &v2,
                                                          const vector<Matrix3d> &R_sols,
                                                          const vector<Vector3d> &t_sols,
                                                          const VectorXd *ptr_weights,
                                                          double &ratio);

    RelativePose RT_Check2_bare_metal_optimized_corrected(const BearingVectors &v1,
                                                          const BearingVectors &v2,
                                                          const vector<Matrix3d> &R_sols,
                                                          const vector<Vector3d> &t_sols,
                                                          const VectorXd *ptr_weights,
                                                          double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 安全极致优化版本：确保算法正确性
     */
    RelativePose RT_Check2_safe_ultra_optimized(const MatrixXd &A,
                                                const BearingVectors &v1,
                                                const BearingVectors &v2,
                                                const vector<Matrix3d> &R_sols,
                                                const vector<Vector3d> &t_sols,
                                                const VectorXd *ptr_weights,
                                                double &ratio);

    /**
     * @brief 检查并选择最优的R和t组合 - 终极优化版本：在确保正确性基础上的最大优化
     */
    RelativePose RT_Check2_final_optimized(const MatrixXd &A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio);

}
#endif