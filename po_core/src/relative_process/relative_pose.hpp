/**
 * @file relative_pose.hpp
 * @brief 相对位姿估计相关的工具函数
 * @copyright Copyright (c) 2024 PoSDK
 */

#pragma once

#include "po_core/types.hpp"
#include "Rt_check.hpp"

namespace PoSDK
{
    using namespace Eigen;
    using namespace types;
    using namespace std;

    /**
     * @brief 双视图位姿估计函数指针类型
     * @param v1 第一视图的归一化特征点(3xN矩阵)
     * @param v2 第二视图的归一化特征点(3xN矩阵)
     * @param ptr_weights 特征点权重向量指针(可选)
     * @return 相对位姿
     */
    using Solve_Relative_Main = RelativePose (*)(const BearingVectors &v1,
                                                 const BearingVectors &v2,
                                                 const VectorXd *ptr_weights);

    /**
     * @brief 初值估计函数指针类型
     */
    using Eval_Rt_func = RelativePose (*)(const BearingVectors &v1,
                                          const BearingVectors &v2,
                                          const VectorXd *ptr_weights);

    /**
     * @brief 残差计算函数指针类型
     */
    using Eval_Residual_func = VectorXd (*)(const BearingVectors &v1,
                                            const BearingVectors &v2,
                                            const RelativePose &pose,
                                            const VectorXd *ptr_weights);

    /**
     * @brief 计算向量的反对称矩阵
     * @param x 3D向量
     * @return 3x3反对称矩阵
     */
    Matrix3d cross_matrix(const Vector3d &x);

    /**
     * @brief 计算向量的中位数
     * @param a 输入向量
     * @return 中位数值
     */
    double find_median_relative_pose(VectorXd a);

    /**
     * @brief 计算向量的中位数（std::vector版本）
     * @param a 输入向量
     * @return 中位数值
     */
    double find_median_relative_pose(vector<double> a);

    /**
     * @brief 处理本质矩阵的特征向量解（优化版本）
     * @param Evec_sols 本质矩阵的特征向量解(9xN矩阵)
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param A 约束矩阵
     * @param ptr_weights 特征点权重向量指针
     * @param residual_func 残差计算函数
     * @param compute_mode 计算模式选择(true: 使用原始模式, false: 使用改进模式)
     * @param use_median 是否使用中位数代价(默认true:使用中位数，false:使用总和)
     * @param rt_check_type RT_Check函数类型选择(默认使用RT_CHECK2)
     * @return 3x4矩阵[R|t]
     */
    Matrix<double, 3, 4> process_Evec_opt(const MatrixXd &Evec_sols,
                                          const BearingVectors &v1,
                                          const BearingVectors &v2,
                                          const MatrixXd &A,
                                          const VectorXd *ptr_weights,
                                          const Eval_Residual_func &residual_func,
                                          bool compute_mode = false,
                                          bool use_median = true,
                                          RTCheckType rt_check_type = RTCheckType::RT_CHECK2);

    /**
     * @brief 处理本质矩阵的特征向量解（基础版本）
     * @param Evec_sols 本质矩阵的特征向量解(9xN矩阵)
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param A 约束矩阵
     * @param ptr_weights 特征点权重向量指针
     * @param residual_func 残差计算函数
     * @param compute_mode 计算模式选择(true: 使用原始模式, false: 使用改进模式)
     * @param use_median 是否使用中位数代价(默认true:使用中位数，false:使用总和)
     * @param rt_check_type RT_Check函数类型选择(默认使用RT_CHECK2)
     * @return 3x4矩阵[R|t]
     */
    Matrix<double, 3, 4> process_Evec(const MatrixXd &Evec_sols,
                                      const BearingVectors &v1,
                                      const BearingVectors &v2,
                                      const MatrixXd &A,
                                      const VectorXd *ptr_weights,
                                      const Eval_Residual_func &residual_func,
                                      bool compute_mode = false,
                                      bool use_median = true,
                                      RTCheckType rt_check_type = RTCheckType::RT_CHECK2);

    /**
     * @brief 处理本质矩阵的特征向量解（基础版本）
     * @param Evec_sols 本质矩阵的特征向量解(9xN矩阵)
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param ptr_weights 特征点权重向量指针
     * @param residual_func 残差计算函数
     * @param compute_mode 计算模式选择(true: 使用原始模式, false: 使用改进模式)
     * @param use_median 是否使用中位数代价(默认true:使用中位数，false:使用总和)
     * @param rt_check_type RT_Check函数类型选择(默认使用RT_CHECK2)
     * @return 3x4矩阵[R|t]
     */
    Matrix<double, 3, 4> process_Evec(const Matrix<double, 9, 9> &Evec_sols,
                                      const BearingVectors &v1,
                                      const BearingVectors &v2,
                                      const VectorXd *ptr_weights,
                                      const Eval_Residual_func &residual_func,
                                      bool compute_mode = false,
                                      bool use_median = true,
                                      RTCheckType rt_check_type = RTCheckType::RT_CHECK2);

    /**
     * @brief 计算解的代价（Rt_Check版本）
     * @param Evec 本质矩阵的特征向量(9xN矩阵)
     * @param A 约束矩阵
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param evec_mode 是否使用特征向量模式
     * @param ptr_weights 特征点权重向量指针
     * @param residual_func 残差计算函数
     * @param use_median 是否使用中位数代价(默认true:使用中位数，false:使用总和)
     * @param out 输出的[R|t]矩阵
     * @param rt_check_type RT_Check函数类型选择(默认使用RT_CHECK2)
     * @return 最优解的代价值
     */
    double compute_cost(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                        const MatrixXd &A,
                        const BearingVectors &v1,
                        const BearingVectors &v2,
                        const bool &evec_mode,
                        const VectorXd *ptr_weights,
                        const Eval_Residual_func &residual_func,
                        Matrix<double, 3, 4> &out,
                        bool use_median = true,
                        RTCheckType rt_check_type = RTCheckType::RT_CHECK2);

    /**
     * @brief 计算解的代价（遍历RT组合版本）
     * @param Evec 本质矩阵的特征向量(9xN矩阵)
     * @param A 约束矩阵
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param evec_mode 是否使用特征向量模式
     * @param ptr_weights 特征点权重向量指针
     * @param residual_func 残差计算函数
     * @param use_median 是否使用中位数代价(默认true:使用中位数，false:使用总和)
     * @param out 输出的[R|t]矩阵
     * @return 最优解的代价值
     */
    double compute_cost_rt(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                           const MatrixXd &A,
                           const BearingVectors &v1,
                           const BearingVectors &v2,
                           const bool &evec_mode,
                           const VectorXd *ptr_weights,
                           const Eval_Residual_func &residual_func,
                           Matrix<double, 3, 4> &out,
                           bool use_median = true);

    /**
     * @brief 计算解的代价（Rt_Check版本）
     * @param Evec 本质矩阵的特征向量(9xN矩阵)
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param evec_mode 是否使用特征向量模式
     * @param ptr_weights 特征点权重向量指针
     * @param residual_func 残差计算函数
     * @param use_median 是否使用中位数代价(默认true:使用中位数，false:使用总和)
     * @param out 输出的[R|t]矩阵
     * @param rt_check_type RT_Check函数类型选择(默认使用RT_CHECK2)
     * @return 最优解的代价值
     */
    double compute_cost(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                        const BearingVectors &v1,
                        const BearingVectors &v2,
                        const bool &evec_mode,
                        const VectorXd *ptr_weights,
                        const Eval_Residual_func &residual_func,
                        Matrix<double, 3, 4> &out,
                        bool use_median = true,
                        RTCheckType rt_check_type = RTCheckType::RT_CHECK2);

    /**
     * @brief 计算解的代价（遍历RT组合版本）
     * @param Evec 本质矩阵的特征向量(9xN矩阵)
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param evec_mode 是否使用特征向量模式
     * @param ptr_weights 特征点权重向量指针
     * @param residual_func 残差计算函数
     * @param use_median 是否使用中位数代价(默认true:使用中位数，false:使用总和)
     * @param out 输出的[R|t]矩阵
     * @return 最优解的代价值
     */
    double compute_cost_rt(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                           const BearingVectors &v1,
                           const BearingVectors &v2,
                           const bool &evec_mode,
                           const VectorXd *ptr_weights,
                           const Eval_Residual_func &residual_func,
                           Matrix<double, 3, 4> &out,
                           bool use_median = true);

    /**
     * @brief 从本质矩阵恢复R和t
     * @param E 本质矩阵
     * @param R 输出的旋转矩阵候选集
     * @param t 输出的平移向量候选集
     */
    void essential2RT(const Matrix3d &E,
                      vector<Matrix3d> &R,
                      vector<Vector3d> &t);

    void essential2RT_opt(const Matrix3d &E,
                          vector<Matrix3d> &R,
                          vector<Vector3d> &t);

    /**
     * @brief 三角化一个3D点
     * @param R 旋转矩阵
     * @param t 平移向量
     * @param point1 第一视图的归一化点
     * @param point2 第二视图的归一化点
     * @return 三角化得到的3D点
     */
    Vector3d triangulate2(const Matrix3d &R,
                          const Vector3d &t,
                          const Vector3d &point1,
                          const Vector3d &point2);

    /**
     * @brief 使用DLT方法三角化一个3D点
     */
    Vector3d triangulateOnePoint(const Matrix3d &R,
                                 const Vector3d &t,
                                 const Vector3d &point1,
                                 const Vector3d &point2);

    /**
     * @brief 快速选择算法计算中位数（O(n)时间复杂度）
     * @param arr 输入数组
     * @param left 左边界
     * @param right 右边界
     * @param k 要找的第k小的元素（从0开始）
     * @return 第k小的元素值
     */
    double quick_select_median(vector<double> &arr, int left, int right, int k);

    /**
     * @brief 专门为residual_PPO优化的sum cost计算函数
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param pose 相对位姿
     * @param ptr_weights 特征点权重向量指针
     * @return 加权残差总和
     */
    double residual_PPO_sum_optimized(const BearingVectors &v1,
                                      const BearingVectors &v2,
                                      const RelativePose &pose,
                                      const VectorXd *ptr_weights);

    /**
     * @brief 专门为residual_PPO优化的median cost计算函数
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param pose 相对位姿
     * @param ptr_weights 特征点权重向量指针
     * @return 加权残差中位数
     */
    double residual_PPO_median_optimized(const BearingVectors &v1,
                                         const BearingVectors &v2,
                                         const RelativePose &pose,
                                         const VectorXd *ptr_weights);

    /**
     * @brief 高效的compute_cost函数（专门针对PPO残差）
     * @param Evec 本质矩阵的特征向量(9xN矩阵)
     * @param v1 第一视图的归一化特征点
     * @param v2 第二视图的归一化特征点
     * @param evec_mode 是否使用特征向量模式
     * @param ptr_weights 特征点权重向量指针
     * @param out 输出的[R|t]矩阵
     * @param use_median 是否使用中位数代价
     * @param rt_check_type RT_Check函数类型选择
     * @return 最优解的代价值
     */
    double compute_cost_PPO_optimized(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                                      const BearingVectors &v1,
                                      const BearingVectors &v2,
                                      const bool &evec_mode,
                                      const VectorXd *ptr_weights,
                                      Matrix<double, 3, 4> &out,
                                      bool use_median = true,
                                      RTCheckType rt_check_type = RTCheckType::RT_CHECK2);

} // namespace PoSDK
