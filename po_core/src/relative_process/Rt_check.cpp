#include "Rt_check.hpp"

namespace PoSDK
{

    std::string RTCheckTypeToString(RTCheckType type)
    {
        switch (type)
        {
        case RTCheckType::RT_CHECK2:
            return "RT_CHECK2";
        case RTCheckType::RT_CHECK2_SUM_OPTIMIZED:
            return "RT_CHECK2_SUM_OPTIMIZED";
        case RTCheckType::RT_CHECK2_STACK_OPTIMIZED:
            return "RT_CHECK2_STACK_OPTIMIZED";
        case RTCheckType::RT_CHECK2_DIRECT_OPTIMIZED:
            return "RT_CHECK2_DIRECT_OPTIMIZED";
        case RTCheckType::RT_CHECK2_PRECOMPUTED_SUM:
            return "RT_CHECK2_PRECOMPUTED_SUM";
        case RTCheckType::RT_CHECK2_SIMD_OPTIMIZED:
            return "RT_CHECK2_SIMD_OPTIMIZED";
        case RTCheckType::RT_CHECK2_ULTRA_OPTIMIZED:
            return "RT_CHECK2_ULTRA_OPTIMIZED";
        case RTCheckType::RT_CHECK2_MATRIX_FREE_OPTIMIZED:
            return "RT_CHECK2_MATRIX_FREE_OPTIMIZED";
        case RTCheckType::RT_CHECK2_BARE_METAL_OPTIMIZED:
            return "RT_CHECK2_BARE_METAL_OPTIMIZED";
        case RTCheckType::RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED:
            return "RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED";
        case RTCheckType::RT_CHECK2_SAFE_ULTRA_OPTIMIZED:
            return "RT_CHECK2_SAFE_ULTRA_OPTIMIZED";
        case RTCheckType::RT_CHECK2_FINAL_OPTIMIZED:
            return "RT_CHECK2_FINAL_OPTIMIZED";
        default:
            return "UNKNOWN";
        }
    }

    RTCheckType StringToRTCheckType(const std::string &str)
    {
        if (str == "RT_CHECK2")
            return RTCheckType::RT_CHECK2;
        else if (str == "RT_CHECK2_SUM_OPTIMIZED")
            return RTCheckType::RT_CHECK2_SUM_OPTIMIZED;
        else if (str == "RT_CHECK2_STACK_OPTIMIZED")
            return RTCheckType::RT_CHECK2_STACK_OPTIMIZED;
        else if (str == "RT_CHECK2_DIRECT_OPTIMIZED")
            return RTCheckType::RT_CHECK2_DIRECT_OPTIMIZED;
        else if (str == "RT_CHECK2_PRECOMPUTED_SUM")
            return RTCheckType::RT_CHECK2_PRECOMPUTED_SUM;
        else if (str == "RT_CHECK2_SIMD_OPTIMIZED")
            return RTCheckType::RT_CHECK2_SIMD_OPTIMIZED;
        else if (str == "RT_CHECK2_ULTRA_OPTIMIZED")
            return RTCheckType::RT_CHECK2_ULTRA_OPTIMIZED;
        else if (str == "RT_CHECK2_MATRIX_FREE_OPTIMIZED")
            return RTCheckType::RT_CHECK2_MATRIX_FREE_OPTIMIZED;
        else if (str == "RT_CHECK2_BARE_METAL_OPTIMIZED")
            return RTCheckType::RT_CHECK2_BARE_METAL_OPTIMIZED;
        else if (str == "RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED")
            return RTCheckType::RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED;
        else if (str == "RT_CHECK2_SAFE_ULTRA_OPTIMIZED")
            return RTCheckType::RT_CHECK2_SAFE_ULTRA_OPTIMIZED;
        else if (str == "RT_CHECK2_FINAL_OPTIMIZED")
            return RTCheckType::RT_CHECK2_FINAL_OPTIMIZED;
        else
            return RTCheckType::RT_CHECK2; // 默认返回原始版本
    }

    // 更新了Rt_check中的ratio部分
    RelativePose RT_Check2(const MatrixXd &A,
                           const BearingVectors &v1,
                           const BearingVectors &v2,
                           const vector<Matrix3d> &R_sols,
                           const vector<Vector3d> &t_sols,
                           const VectorXd *ptr_weights,
                           double &ratio)
    {
        int n = v1.cols();

        // use normalized coordinates [not bearing vec]
        Matrix<double, 3, Dynamic> x, xmatch;
        x.setZero(3, v2.cols());
        xmatch.setZero(3, v1.cols());

        for (int i = 0; i < n; ++i)
        {
            x.col(i) = v2.col(i) / v2(2, i);
            xmatch.col(i) = v1.col(i) / v1(2, i);
        }

        // formulate H matrix
        MatrixXd H(n, 6);
        for (int i = 0; i < n; ++i)
        {
            H.row(i) << xmatch.col(i).norm() * x.col(i).transpose(), x.col(i).norm() * xmatch.col(i).transpose();
            if (ptr_weights)
                H.row(i) *= (*ptr_weights)[i];
        }
        //    cout<<"Hmat:\n"<<H<<endl;

        vector<int> SR(R_sols.size(), 0); // the number of pair points satisfied with M1(R)
        vector<int> ST(t_sols.size(), 0); // the number of pair points satisfied with M2(t)

        // M1(R) constraint
        for (size_t i = 0; i < 2; ++i)
        {
            Matrix3d tx;
            Vector3d t = t_sols[i];

            tx << 0, -t(2), t(1),
                t(2), 0, -t(0),
                -t(1), t(0), 0;

            Matrix3d QtQ = tx.transpose() * tx * R_sols[i];

            // SR[i] = (v1[j]').transpose()*QtQ*v2[j];
            // for (int j = 0; j < n; ++j)
            // {
            //     SR[i] += (v1.col(j).transpose() * QtQ * v2.col(j) > 0) ? 1 : 0;
            // }

            SR[i] = (A * Map<VectorXd>(QtQ.data(), 9)).array().cast<double>().unaryExpr([](double val)
                                                                                        { return val > 0 ? 1 : 0; })
                        .sum();
        }

        int index_R = SR[0] > SR[1] ? 0 : 1;

        RelativePose right_pose;
        right_pose.Rij = R_sols[index_R];

        // M2(t)

        Vector2d tmp_ratio;
        for (int i = 0; i < 2; ++i)
        {
            Matrix<double, 6, 1> combinedVector;
            combinedVector.head(3) = -right_pose.Rij.transpose() * t_sols[i];
            combinedVector.tail(3) = Matrix3d::Identity() * t_sols[i];

            VectorXd result = H * combinedVector;
            //        ST[i] = (result.array() > 0).count();
            //        tmp_ratio[i] = result.sum();
            ST[i] = 0;
            for (int j = 0; j < n; ++j)
            {
                Vector3d tmp_1 = cross_matrix(t_sols[i]) * right_pose.Rij * x.col(j);
                Vector3d tmp_2 = cross_matrix(xmatch.col(j)) * right_pose.Rij * x.col(j);
                Vector3d tmp_3 = cross_matrix(t_sols[i]) * xmatch.col(j);
                double sside1_ratio = tmp_1.transpose() * tmp_2 > 0 ? 1 : 0;
                double sside2_ratio = tmp_2.transpose() * tmp_3 > 0 ? 1 : 0;
                ST[i] += sside1_ratio + sside2_ratio;
            }
        }

        ratio = std::max(ST[0], ST[1]);

        int index_t = ST[0] > ST[1] ? 0 : 1;
        right_pose.tij = t_sols[index_t];
        //    ratio = tmp_ratio[index_t];

        return right_pose;
    }

    /**
     * @brief 检查并选择最优的R和t组合
     */
    RelativePose RT_Check2_opt(const MatrixXd &A,
                               const BearingVectors &v1,
                               const BearingVectors &v2,
                               const vector<Matrix3d> &R_sols,
                               const vector<Vector3d> &t_sols,
                               const VectorXd *ptr_weights,
                               double &ratio)
    {
        try
        {
            // 参数验证
            if (R_sols.size() != 2 || t_sols.size() != 2)
            {
                throw std::invalid_argument("R_sols and t_sols must contain exactly 2 solutions each");
            }

            const int n = v1.cols();
            if (n != v2.cols())
            {
                throw std::invalid_argument("Bearing vectors must have same number of columns");
            }

            // 归一化坐标 - 修改这部分的实现方式
            Matrix<double, 3, Dynamic> x(3, n);
            Matrix<double, 3, Dynamic> xmatch(3, n);

            for (int i = 0; i < n; ++i)
            {
                x.col(i) = v2.col(i) / v2(2, i);
                xmatch.col(i) = v1.col(i) / v1(2, i);
            }

            // 构建H矩阵
            MatrixXd H(n, 6);
            for (int i = 0; i < n; ++i)
            {
                const Vector3d &xi = x.col(i);
                const Vector3d &xmi = xmatch.col(i);

                H.row(i) << xmi.norm() * xi.transpose(),
                    xi.norm() * xmi.transpose();

                if (ptr_weights)
                {
                    H.row(i) *= (*ptr_weights)[i];
                }
            }

            // 评估旋转矩阵
            std::array<int, 2> rotation_scores = {0, 0};
            for (int i = 0; i < 2; ++i)
            {
                const Matrix3d tx = cross_matrix(t_sols[i]);
                Matrix3d Q = tx.transpose() * tx * R_sols[i];

                rotation_scores[i] = (A * Map<VectorXd>(Q.data(), 9))
                                         .array()
                                         .unaryExpr([](double v)
                                                    { return v > 0 ? 1 : 0; })
                                         .sum();
            }

            // 选择最优旋转
            const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
            RelativePose result(0, 1, R_sols[best_R_idx], Vector3d::Zero());

            // 评估平移向量
            std::array<int, 2> translation_scores = {0, 0};
            for (int i = 0; i < 2; ++i)
            {
                for (int j = 0; j < n; ++j)
                {
                    const Vector3d tmp_1 = cross_matrix(t_sols[i]) * result.Rij * x.col(j);
                    const Vector3d tmp_2 = cross_matrix(xmatch.col(j)) * result.Rij * x.col(j);
                    const Vector3d tmp_3 = cross_matrix(t_sols[i]) * xmatch.col(j);

                    translation_scores[i] += (tmp_1.dot(tmp_2) > 0) + (tmp_2.dot(tmp_3) > 0);
                }
            }

            // 选择最优平移
            const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
            result.tij = t_sols[best_t_idx];

            ratio = std::max(translation_scores[0], translation_scores[1]);

            return result;
        }
        catch (const std::exception &e)
        {
            std::cerr << "Error in RT_Check2: " << e.what() << std::endl;
            return RelativePose();
        }
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 优化版本1：使用预计算的sum_A，直接累加
     */
    RelativePose RT_Check2_sum_optimized(const MatrixXd &A,
                                         const BearingVectors &v1,
                                         const BearingVectors &v2,
                                         const vector<Matrix3d> &R_sols,
                                         const vector<Vector3d> &t_sols,
                                         const VectorXd *ptr_weights,
                                         double &ratio)
    {
        const int n = v1.cols();

        // 预计算sum_A向量（1x9）- 考虑权重
        Vector<double, 9> sum_A = Vector<double, 9>::Zero();
        for (int i = 0; i < n; ++i)
        {
            const Vector3d &p1 = v1.col(i);
            const Vector3d &p2 = v2.col(i);
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;

            // 计算kron(p2, p1)并累加到sum_A
            for (int j = 0; j < 3; ++j)
            {
                for (int k = 0; k < 3; ++k)
                {
                    sum_A(j * 3 + k) += w * p2(j) * p1(k);
                }
            }
        }

        // 归一化坐标
        Matrix<double, 3, Dynamic> x(3, n);
        Matrix<double, 3, Dynamic> xmatch(3, n);
        for (int i = 0; i < n; ++i)
        {
            x.col(i) = v2.col(i) / v2(2, i);
            xmatch.col(i) = v1.col(i) / v1(2, i);
        }

        // 评估旋转矩阵 - 使用向量化计算，直接累加不判断>0
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Matrix3d tx = cross_matrix(t_sols[i]);
            const Matrix3d QtQR = tx.transpose() * tx * R_sols[i];

            // 向量化计算：SR[i] = sum_A * vectorized(QtQR)
            rotation_scores[i] = sum_A.dot(Map<const Vector<double, 9>>(QtQR.data()));
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量 - 也使用直接累加
        std::array<double, 2> translation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            for (int j = 0; j < n; ++j)
            {
                const Vector3d tmp_1 = cross_matrix(t_sols[i]) * result.Rij * x.col(j);
                const Vector3d tmp_2 = cross_matrix(xmatch.col(j)) * result.Rij * x.col(j);
                const Vector3d tmp_3 = cross_matrix(t_sols[i]) * xmatch.col(j);

                translation_scores[i] += tmp_1.dot(tmp_2) + tmp_2.dot(tmp_3);
            }
        }

        // 选择最优平移
        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];

        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 优化版本2：栈内存优化
     */
    RelativePose RT_Check2_stack_optimized(const MatrixXd &A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio)
    {
        const int n = v1.cols();

        // 使用栈内存存储sum_A
        double sum_A[9] = {0.0};

        // 预计算sum_A
        for (int i = 0; i < n; ++i)
        {
            const Vector3d &p1 = v1.col(i);
            const Vector3d &p2 = v2.col(i);

            // 直接计算kron(p2, p1)并累加
            sum_A[0] += p2(0) * p1(0);
            sum_A[1] += p2(0) * p1(1);
            sum_A[2] += p2(0) * p1(2);
            sum_A[3] += p2(1) * p1(0);
            sum_A[4] += p2(1) * p1(1);
            sum_A[5] += p2(1) * p1(2);
            sum_A[6] += p2(2) * p1(0);
            sum_A[7] += p2(2) * p1(1);
            sum_A[8] += p2(2) * p1(2);
        }

        // 归一化坐标 - 使用更高效的方式
        Matrix<double, 3, Dynamic> x(3, n);
        Matrix<double, 3, Dynamic> xmatch(3, n);
        for (int i = 0; i < n; ++i)
        {
            const double inv_z1 = 1.0 / v1(2, i);
            const double inv_z2 = 1.0 / v2(2, i);

            xmatch.col(i) = v1.col(i) * inv_z1;
            x.col(i) = v2.col(i) * inv_z2;
        }

        // 评估旋转矩阵 - 使用栈内存和手动点积
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Matrix3d tx = cross_matrix(t_sols[i]);
            const Matrix3d QtQR = tx.transpose() * tx * R_sols[i];

            // 手动点积计算
            double score = 0.0;
            const double *QtQR_data = QtQR.data();
            for (int j = 0; j < 9; ++j)
            {
                score += sum_A[j] * QtQR_data[j];
            }
            rotation_scores[i] = score;
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量
        std::array<double, 2> translation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            double score = 0.0;
            for (int j = 0; j < n; ++j)
            {
                const Vector3d tmp_1 = cross_matrix(t_sols[i]) * result.Rij * x.col(j);
                const Vector3d tmp_2 = cross_matrix(xmatch.col(j)) * result.Rij * x.col(j);
                const Vector3d tmp_3 = cross_matrix(t_sols[i]) * xmatch.col(j);

                score += tmp_1.dot(tmp_2) + tmp_2.dot(tmp_3);
            }
            translation_scores[i] = score;
        }

        // 选择最优平移
        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];

        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 原始版本但不用A矩阵
     */
    RelativePose RT_Check2_direct_optimized(const MatrixXd &A,
                                            const BearingVectors &v1,
                                            const BearingVectors &v2,
                                            const vector<Matrix3d> &R_sols,
                                            const vector<Vector3d> &t_sols,
                                            const VectorXd *ptr_weights,
                                            double &ratio)
    {
        const int n = v1.cols();

        // 归一化坐标
        Matrix<double, 3, Dynamic> x(3, n);
        Matrix<double, 3, Dynamic> xmatch(3, n);
        for (int i = 0; i < n; ++i)
        {
            x.col(i) = v2.col(i) / v2(2, i);
            xmatch.col(i) = v1.col(i) / v1(2, i);
        }

        // 评估旋转矩阵 - 直接计算，不用A矩阵
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Matrix3d tx = cross_matrix(t_sols[i]);
            const Matrix3d QtQR = tx.transpose() * tx * R_sols[i];

            // 直接计算所有点的贡献
            double score = 0.0;
            for (int j = 0; j < n; ++j)
            {
                score += v1.col(j).transpose() * QtQR * v2.col(j);
            }
            rotation_scores[i] = score;
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量
        std::array<double, 2> translation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            double score = 0.0;
            for (int j = 0; j < n; ++j)
            {
                const Vector3d tmp_1 = cross_matrix(t_sols[i]) * result.Rij * x.col(j);
                const Vector3d tmp_2 = cross_matrix(xmatch.col(j)) * result.Rij * x.col(j);
                const Vector3d tmp_3 = cross_matrix(t_sols[i]) * xmatch.col(j);

                score += tmp_1.dot(tmp_2) + tmp_2.dot(tmp_3);
            }
            translation_scores[i] = score;
        }

        // 选择最优平移
        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];

        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 使用预计算sum_A的超级优化版本
     */
    RelativePose RT_Check2_precomputed_sum(const Vector<double, 9> &sum_A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio)
    {
        const int n = v1.cols();

        // 归一化坐标
        Matrix<double, 3, Dynamic> x(3, n);
        Matrix<double, 3, Dynamic> xmatch(3, n);
        for (int i = 0; i < n; ++i)
        {
            x.col(i) = v2.col(i) / v2(2, i);
            xmatch.col(i) = v1.col(i) / v1(2, i);
        }

        // 评估旋转矩阵 - 使用预计算的sum_A，超高效
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Matrix3d tx = cross_matrix(t_sols[i]);
            const Matrix3d QtQR = tx.transpose() * tx * R_sols[i];

            // 向量化计算：SR[i] = sum_A * vectorized(QtQR)
            rotation_scores[i] = sum_A.dot(Map<const Vector<double, 9>>(QtQR.data()));
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量
        std::array<double, 2> translation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            for (int j = 0; j < n; ++j)
            {
                const Vector3d tmp_1 = cross_matrix(t_sols[i]) * result.Rij * x.col(j);
                const Vector3d tmp_2 = cross_matrix(xmatch.col(j)) * result.Rij * x.col(j);
                const Vector3d tmp_3 = cross_matrix(t_sols[i]) * xmatch.col(j);

                translation_scores[i] += tmp_1.dot(tmp_2) + tmp_2.dot(tmp_3);
            }
        }

        // 选择最优平移
        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];

        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 超级深度优化版本1：SIMD + 内联
     */
    RelativePose RT_Check2_simd_optimized(const MatrixXd &A,
                                          const BearingVectors &v1,
                                          const BearingVectors &v2,
                                          const vector<Matrix3d> &R_sols,
                                          const vector<Vector3d> &t_sols,
                                          const VectorXd *ptr_weights,
                                          double &ratio)
    {
        const int n = v1.cols();

        // 使用aligned数组确保SIMD对齐
        alignas(32) double sum_A[9] = {0.0};
        alignas(32) double inv_z1[n], inv_z2[n];

        // 预计算所有逆深度值 - 向量化
        for (int i = 0; i < n; ++i)
        {
            inv_z1[i] = 1.0 / v1(2, i);
            inv_z2[i] = 1.0 / v2(2, i);
        }

        // 预计算sum_A和归一化坐标 - 单次遍历
        Matrix<double, 3, Dynamic> x(3, n);
        Matrix<double, 3, Dynamic> xmatch(3, n);

        for (int i = 0; i < n; ++i)
        {
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const double inv_z1_i = inv_z1[i];
            const double inv_z2_i = inv_z2[i];

            // 同时计算归一化坐标和sum_A
            const double p1_0 = v1(0, i) * inv_z1_i;
            const double p1_1 = v1(1, i) * inv_z1_i;
            const double p1_2 = v1(2, i) * inv_z1_i; // = 1.0

            const double p2_0 = v2(0, i) * inv_z2_i;
            const double p2_1 = v2(1, i) * inv_z2_i;
            const double p2_2 = v2(2, i) * inv_z2_i; // = 1.0

            xmatch(0, i) = p1_0;
            xmatch(1, i) = p1_1;
            xmatch(2, i) = p1_2;
            x(0, i) = p2_0;
            x(1, i) = p2_1;
            x(2, i) = p2_2;

            // 内联计算kron(v2, v1)并累加 - 避免函数调用
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            sum_A[0] += w * v2_i(0) * v1_i(0);
            sum_A[1] += w * v2_i(0) * v1_i(1);
            sum_A[2] += w * v2_i(0) * v1_i(2);
            sum_A[3] += w * v2_i(1) * v1_i(0);
            sum_A[4] += w * v2_i(1) * v1_i(1);
            sum_A[5] += w * v2_i(1) * v1_i(2);
            sum_A[6] += w * v2_i(2) * v1_i(0);
            sum_A[7] += w * v2_i(2) * v1_i(1);
            sum_A[8] += w * v2_i(2) * v1_i(2);
        }

        // 评估旋转矩阵 - 内联叉积矩阵计算
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];

            // 内联叉积矩阵计算，避免函数调用
            // tx = cross_matrix(t)
            const double tx_data[9] = {
                0.0, -t(2), t(1),
                t(2), 0.0, -t(0),
                -t(1), t(0), 0.0};

            // 计算 tx.transpose() * tx 的结果
            double QtQ[9] = {0.0};

            // tx.transpose() * tx - 手动计算避免矩阵创建
            for (int j = 0; j < 3; ++j)
            {
                for (int k = 0; k < 3; ++k)
                {
                    for (int l = 0; l < 3; ++l)
                    {
                        QtQ[j * 3 + k] += tx_data[l * 3 + j] * tx_data[l * 3 + k];
                    }
                }
            }

            // QtQ * R - 手动矩阵乘法
            double QtQR[9] = {0.0};
            const Matrix3d &R = R_sols[i];
            for (int j = 0; j < 3; ++j)
            {
                for (int k = 0; k < 3; ++k)
                {
                    for (int l = 0; l < 3; ++l)
                    {
                        QtQR[j * 3 + k] += QtQ[j * 3 + l] * R(l, k);
                    }
                }
            }

            // 向量点积 - 手动展开循环
            double score = 0.0;
            score += sum_A[0] * QtQR[0];
            score += sum_A[1] * QtQR[1];
            score += sum_A[2] * QtQR[2];
            score += sum_A[3] * QtQR[3];
            score += sum_A[4] * QtQR[4];
            score += sum_A[5] * QtQR[5];
            score += sum_A[6] * QtQR[6];
            score += sum_A[7] * QtQR[7];
            score += sum_A[8] * QtQR[8];

            rotation_scores[i] = score;
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量 - 优化版本
        std::array<double, 2> translation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const Matrix3d &R = result.Rij;

            double score = 0.0;
            for (int j = 0; j < n; ++j)
            {
                // 预计算一些重复使用的值
                const Vector3d Rx = R * x.col(j);
                const Vector3d cross_xmatch_Rx = xmatch.col(j).cross(Rx);
                const Vector3d cross_t_Rx = t.cross(Rx);
                const Vector3d cross_t_xmatch = t.cross(xmatch.col(j));

                score += cross_t_Rx.dot(cross_xmatch_Rx) + cross_xmatch_Rx.dot(cross_t_xmatch);
            }
            translation_scores[i] = score;
        }

        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];
        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 超级深度优化版本2：预计算 + 展开循环
     */
    RelativePose RT_Check2_ultra_optimized(const MatrixXd &A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio)
    {
        const int n = v1.cols();

        // 栈上预分配，确保cache局部性
        alignas(32) double sum_A[9] = {0.0};

        // 预计算和存储所有需要的中间结果
        std::vector<Vector3d> x_vec(n), xmatch_vec(n);

        // 单次遍历计算所有预处理数据
        for (int i = 0; i < n; ++i)
        {
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            // 归一化坐标
            xmatch_vec[i] = v1_i / v1_i(2);
            x_vec[i] = v2_i / v2_i(2);

            // 累加sum_A - 展开循环
            const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
            sum_A[0] += w_v2_0 * v1_i(0);
            sum_A[1] += w_v2_0 * v1_i(1);
            sum_A[2] += w_v2_0 * v1_i(2);
            sum_A[3] += w_v2_1 * v1_i(0);
            sum_A[4] += w_v2_1 * v1_i(1);
            sum_A[5] += w_v2_1 * v1_i(2);
            sum_A[6] += w_v2_2 * v1_i(0);
            sum_A[7] += w_v2_2 * v1_i(1);
            sum_A[8] += w_v2_2 * v1_i(2);
        }

        // 预计算旋转候选的一些不变量
        std::array<std::array<double, 9>, 2> precomputed_QtQ;
        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const double t0 = t(0), t1 = t(1), t2 = t(2);

            // 直接计算 tx.transpose() * tx 的结果
            precomputed_QtQ[i][0] = t1 * t1 + t2 * t2;
            precomputed_QtQ[i][1] = -t0 * t1;
            precomputed_QtQ[i][2] = -t0 * t2;
            precomputed_QtQ[i][3] = -t0 * t1;
            precomputed_QtQ[i][4] = t0 * t0 + t2 * t2;
            precomputed_QtQ[i][5] = -t1 * t2;
            precomputed_QtQ[i][6] = -t0 * t2;
            precomputed_QtQ[i][7] = -t1 * t2;
            precomputed_QtQ[i][8] = t0 * t0 + t1 * t1;
        }

        // 评估旋转矩阵
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Matrix3d &R = R_sols[i];
            const auto &QtQ = precomputed_QtQ[i];

            // 计算 QtQ * R
            double QtQR[9];
            for (int j = 0; j < 9; ++j)
            {
                QtQR[j] = 0.0;
                const int row = j / 3, col = j % 3;
                for (int k = 0; k < 3; ++k)
                {
                    QtQR[j] += QtQ[row * 3 + k] * R(k, col);
                }
            }

            // 计算点积 - 完全展开
            rotation_scores[i] = sum_A[0] * QtQR[0] + sum_A[1] * QtQR[1] + sum_A[2] * QtQR[2] +
                                 sum_A[3] * QtQR[3] + sum_A[4] * QtQR[4] + sum_A[5] * QtQR[5] +
                                 sum_A[6] * QtQR[6] + sum_A[7] * QtQR[7] + sum_A[8] * QtQR[8];
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量 - 向量化处理
        std::array<double, 2> translation_scores = {0.0, 0.0};
        const Matrix3d &R = result.Rij;

        // 预计算所有 R * x[j]
        std::vector<Vector3d> Rx_vec(n);
        for (int j = 0; j < n; ++j)
        {
            Rx_vec[j] = R * x_vec[j];
        }

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            double score = 0.0;

            // 向量化计算
            for (int j = 0; j < n; ++j)
            {
                const Vector3d &Rx = Rx_vec[j];
                const Vector3d &xmatch = xmatch_vec[j];

                // 手动计算叉积，避免函数调用
                const Vector3d tmp_1(t(1) * Rx(2) - t(2) * Rx(1), t(2) * Rx(0) - t(0) * Rx(2), t(0) * Rx(1) - t(1) * Rx(0));
                const Vector3d tmp_2(xmatch(1) * Rx(2) - xmatch(2) * Rx(1), xmatch(2) * Rx(0) - xmatch(0) * Rx(2), xmatch(0) * Rx(1) - xmatch(1) * Rx(0));
                const Vector3d tmp_3(t(1) * xmatch(2) - t(2) * xmatch(1), t(2) * xmatch(0) - t(0) * xmatch(2), t(0) * xmatch(1) - t(1) * xmatch(0));

                score += tmp_1.dot(tmp_2) + tmp_2.dot(tmp_3);
            }
            translation_scores[i] = score;
        }

        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];
        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 极致优化版本：完全去除H矩阵构建
     */
    RelativePose RT_Check2_matrix_free_optimized(const MatrixXd &A,
                                                 const BearingVectors &v1,
                                                 const BearingVectors &v2,
                                                 const vector<Matrix3d> &R_sols,
                                                 const vector<Vector3d> &t_sols,
                                                 const VectorXd *ptr_weights,
                                                 double &ratio)
    {
        const int n = v1.cols();

        // 只预计算sum_A，不构建任何额外矩阵
        alignas(32) double sum_A[9] = {0.0};

        // 单次遍历预计算所有需要的数据
        std::vector<Vector3d> x_vec(n), xmatch_vec(n);

        for (int i = 0; i < n; ++i)
        {
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            // 归一化坐标
            xmatch_vec[i] = v1_i / v1_i(2);
            x_vec[i] = v2_i / v2_i(2);

            // 累加sum_A - 完全展开避免函数调用
            const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
            sum_A[0] += w_v2_0 * v1_i(0);
            sum_A[1] += w_v2_0 * v1_i(1);
            sum_A[2] += w_v2_0 * v1_i(2);
            sum_A[3] += w_v2_1 * v1_i(0);
            sum_A[4] += w_v2_1 * v1_i(1);
            sum_A[5] += w_v2_1 * v1_i(2);
            sum_A[6] += w_v2_2 * v1_i(0);
            sum_A[7] += w_v2_2 * v1_i(1);
            sum_A[8] += w_v2_2 * v1_i(2);
        }

        // 预计算叉积矩阵的关键系数
        std::array<std::array<double, 9>, 2> precomputed_QtQ;
        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const double t0 = t(0), t1 = t(1), t2 = t(2);

            // 直接计算 tx.transpose() * tx 的结果，避免矩阵构建
            precomputed_QtQ[i][0] = t1 * t1 + t2 * t2;
            precomputed_QtQ[i][1] = -t0 * t1;
            precomputed_QtQ[i][2] = -t0 * t2;
            precomputed_QtQ[i][3] = -t0 * t1;
            precomputed_QtQ[i][4] = t0 * t0 + t2 * t2;
            precomputed_QtQ[i][5] = -t1 * t2;
            precomputed_QtQ[i][6] = -t0 * t2;
            precomputed_QtQ[i][7] = -t1 * t2;
            precomputed_QtQ[i][8] = t0 * t0 + t1 * t1;
        }

        // 评估旋转矩阵 - 无矩阵构建版本
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Matrix3d &R = R_sols[i];
            const auto &QtQ = precomputed_QtQ[i];

            // 手动矩阵乘法：QtQ * R
            double QtQR[9];
            for (int j = 0; j < 9; ++j)
            {
                const int row = j / 3, col = j % 3;
                QtQR[j] = QtQ[row * 3] * R(0, col) + QtQ[row * 3 + 1] * R(1, col) + QtQ[row * 3 + 2] * R(2, col);
            }

            // 向量点积 - 完全展开
            rotation_scores[i] = sum_A[0] * QtQR[0] + sum_A[1] * QtQR[1] + sum_A[2] * QtQR[2] +
                                 sum_A[3] * QtQR[3] + sum_A[4] * QtQR[4] + sum_A[5] * QtQR[5] +
                                 sum_A[6] * QtQR[6] + sum_A[7] * QtQR[7] + sum_A[8] * QtQR[8];
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量 - 无H矩阵版本
        std::array<double, 2> translation_scores = {0.0, 0.0};
        const Matrix3d &R = result.Rij;

        // 预计算所有 R * x[j] - 避免重复计算
        std::vector<Vector3d> Rx_vec(n);
        for (int j = 0; j < n; ++j)
        {
            Rx_vec[j] = R * x_vec[j];
        }

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            double score = 0.0;

            // 内联叉积计算，避免cross_matrix函数调用
            for (int j = 0; j < n; ++j)
            {
                const Vector3d &Rx = Rx_vec[j];
                const Vector3d &xmatch = xmatch_vec[j];

                // tmp_1 = t × (R * x[j])
                const Vector3d tmp_1(t(1) * Rx(2) - t(2) * Rx(1),
                                     t(2) * Rx(0) - t(0) * Rx(2),
                                     t(0) * Rx(1) - t(1) * Rx(0));

                // tmp_2 = xmatch[j] × (R * x[j])
                const Vector3d tmp_2(xmatch(1) * Rx(2) - xmatch(2) * Rx(1),
                                     xmatch(2) * Rx(0) - xmatch(0) * Rx(2),
                                     xmatch(0) * Rx(1) - xmatch(1) * Rx(0));

                // tmp_3 = t × xmatch[j]
                const Vector3d tmp_3(t(1) * xmatch(2) - t(2) * xmatch(1),
                                     t(2) * xmatch(0) - t(0) * xmatch(2),
                                     t(0) * xmatch(1) - t(1) * xmatch(0));

                // 累加评分：不用>0判断，直接累加点积
                score += tmp_1.dot(tmp_2) + tmp_2.dot(tmp_3);
            }
            translation_scores[i] = score;
        }

        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];
        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 裸机优化版本：最小内存分配
     */
    RelativePose RT_Check2_bare_metal_optimized(const MatrixXd &A,
                                                const BearingVectors &v1,
                                                const BearingVectors &v2,
                                                const vector<Matrix3d> &R_sols,
                                                const vector<Vector3d> &t_sols,
                                                const VectorXd *ptr_weights,
                                                double &ratio)
    {
        const int n = v1.cols();

        // 栈上内存分配，确保cache对齐
        alignas(32) double sum_A[9] = {0.0};
        std::vector<double> inv_z1(n), inv_z2(n);
        std::vector<double> x_data(3 * n), xmatch_data(3 * n);

        // 预计算逆深度和归一化坐标
        for (int i = 0; i < n; ++i)
        {
            inv_z1[i] = 1.0 / v1(2, i);
            inv_z2[i] = 1.0 / v2(2, i);

            const int idx = i * 3;
            xmatch_data[idx] = v1(0, i) * inv_z1[i];
            xmatch_data[idx + 1] = v1(1, i) * inv_z1[i];
            xmatch_data[idx + 2] = 1.0;

            x_data[idx] = v2(0, i) * inv_z2[i];
            x_data[idx + 1] = v2(1, i) * inv_z2[i];
            x_data[idx + 2] = 1.0;

            // 同时计算sum_A
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
            sum_A[0] += w_v2_0 * v1_i(0);
            sum_A[1] += w_v2_0 * v1_i(1);
            sum_A[2] += w_v2_0 * v1_i(2);
            sum_A[3] += w_v2_1 * v1_i(0);
            sum_A[4] += w_v2_1 * v1_i(1);
            sum_A[5] += w_v2_1 * v1_i(2);
            sum_A[6] += w_v2_2 * v1_i(0);
            sum_A[7] += w_v2_2 * v1_i(1);
            sum_A[8] += w_v2_2 * v1_i(2);
        }

        // 评估旋转矩阵 - 完全内联版本
        double best_rotation_score = -std::numeric_limits<double>::infinity();
        int best_R_idx = 0;

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const Matrix3d &R = R_sols[i];

            // 内联计算 tx.transpose() * tx * R
            const double t0 = t(0), t1 = t(1), t2 = t(2);
            const double t0_sq = t0 * t0, t1_sq = t1 * t1, t2_sq = t2 * t2;

            // QtQ * R 直接计算，避免临时变量
            double score = 0.0;
            score += sum_A[0] * ((t1_sq + t2_sq) * R(0, 0) + (-t0 * t1) * R(1, 0) + (-t0 * t2) * R(2, 0));
            score += sum_A[1] * ((t1_sq + t2_sq) * R(0, 1) + (-t0 * t1) * R(1, 1) + (-t0 * t2) * R(2, 1));
            score += sum_A[2] * ((t1_sq + t2_sq) * R(0, 2) + (-t0 * t1) * R(1, 2) + (-t0 * t2) * R(2, 2));
            score += sum_A[3] * ((-t0 * t1) * R(0, 0) + (t0_sq + t2_sq) * R(1, 0) + (-t1 * t2) * R(2, 0));
            score += sum_A[4] * ((-t0 * t1) * R(0, 1) + (t0_sq + t2_sq) * R(1, 1) + (-t1 * t2) * R(2, 1));
            score += sum_A[5] * ((-t0 * t1) * R(0, 2) + (t0_sq + t2_sq) * R(1, 2) + (-t1 * t2) * R(2, 2));
            score += sum_A[6] * ((-t0 * t2) * R(0, 0) + (-t1 * t2) * R(1, 0) + (t0_sq + t1_sq) * R(2, 0));
            score += sum_A[7] * ((-t0 * t2) * R(0, 1) + (-t1 * t2) * R(1, 1) + (t0_sq + t1_sq) * R(2, 1));
            score += sum_A[8] * ((-t0 * t2) * R(0, 2) + (-t1 * t2) * R(1, 2) + (t0_sq + t1_sq) * R(2, 2));

            if (score > best_rotation_score)
            {
                best_rotation_score = score;
                best_R_idx = i;
            }
        }

        RelativePose result;
        result.Rij = R_sols[best_R_idx];
        const Matrix3d &R = result.Rij;

        // 评估平移向量 - 裸机级优化
        double best_translation_score = -std::numeric_limits<double>::infinity();
        int best_t_idx = 0;

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const double t0 = t(0), t1 = t(1), t2 = t(2);

            double score = 0.0;

            for (int j = 0; j < n; ++j)
            {
                const int idx = j * 3;

                // R * x[j] - 内联矩阵向量乘法
                const double Rx0 = R(0, 0) * x_data[idx] + R(0, 1) * x_data[idx + 1] + R(0, 2) * x_data[idx + 2];
                const double Rx1 = R(1, 0) * x_data[idx] + R(1, 1) * x_data[idx + 1] + R(1, 2) * x_data[idx + 2];
                const double Rx2 = R(2, 0) * x_data[idx] + R(2, 1) * x_data[idx + 1] + R(2, 2) * x_data[idx + 2];

                // t × (R * x[j]) - 内联叉积
                const double tmp1_0 = t1 * Rx2 - t2 * Rx1;
                const double tmp1_1 = t2 * Rx0 - t0 * Rx2;
                const double tmp1_2 = t0 * Rx1 - t1 * Rx0;

                // xmatch[j] × (R * x[j]) - 内联叉积
                const double xm0 = xmatch_data[idx], xm1 = xmatch_data[idx + 1], xm2 = xmatch_data[idx + 2];
                const double tmp2_0 = xm1 * Rx2 - xm2 * Rx1;
                const double tmp2_1 = xm2 * Rx0 - xm0 * Rx2;
                const double tmp2_2 = xm0 * Rx1 - xm1 * Rx0;

                // t × xmatch[j] - 内联叉积
                const double tmp3_0 = t1 * xm2 - t2 * xm1;
                const double tmp3_1 = t2 * xm0 - t0 * xm2;
                const double tmp3_2 = t0 * xm1 - t1 * xm0;

                // 点积计算 - 内联
                const double dot1 = tmp1_0 * tmp2_0 + tmp1_1 * tmp2_1 + tmp1_2 * tmp2_2;
                const double dot2 = tmp2_0 * tmp3_0 + tmp2_1 * tmp3_1 + tmp2_2 * tmp3_2;

                score += dot1 + dot2;
            }

            if (score > best_translation_score)
            {
                best_translation_score = score;
                best_t_idx = i;
            }
        }

        result.tij = t_sols[best_t_idx];
        ratio = best_translation_score;

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 裸机优化版本：最小内存分配 (修正版)
     */
    RelativePose RT_Check2_bare_metal_optimized_corrected(const MatrixXd &A,
                                                          const BearingVectors &v1,
                                                          const BearingVectors &v2,
                                                          const vector<Matrix3d> &R_sols,
                                                          const vector<Vector3d> &t_sols,
                                                          const VectorXd *ptr_weights,
                                                          double &ratio)
    {
        const int n = v1.cols();

        // 栈上内存分配，确保cache对齐
        alignas(32) double sum_A[9] = {0.0};
        std::vector<double> inv_z1(n), inv_z2(n);
        std::vector<double> x_data(3 * n), xmatch_data(3 * n);

        // 预计算逆深度和归一化坐标
        for (int i = 0; i < n; ++i)
        {
            inv_z1[i] = 1.0 / v1(2, i);
            inv_z2[i] = 1.0 / v2(2, i);

            const int idx = i * 3;
            xmatch_data[idx] = v1(0, i) * inv_z1[i];
            xmatch_data[idx + 1] = v1(1, i) * inv_z1[i];
            xmatch_data[idx + 2] = 1.0;

            x_data[idx] = v2(0, i) * inv_z2[i];
            x_data[idx + 1] = v2(1, i) * inv_z2[i];
            x_data[idx + 2] = 1.0;

            // 计算sum_A - 正确的顺序：v2[i] ⊗ v1[i]
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
            sum_A[0] += w_v2_0 * v1_i(0);
            sum_A[1] += w_v2_0 * v1_i(1);
            sum_A[2] += w_v2_0 * v1_i(2);
            sum_A[3] += w_v2_1 * v1_i(0);
            sum_A[4] += w_v2_1 * v1_i(1);
            sum_A[5] += w_v2_1 * v1_i(2);
            sum_A[6] += w_v2_2 * v1_i(0);
            sum_A[7] += w_v2_2 * v1_i(1);
            sum_A[8] += w_v2_2 * v1_i(2);
        }

        // 评估旋转矩阵 - 使用计数而不是直接求和，与原版保持一致
        int best_rotation_count = -1;
        int best_R_idx = 0;

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const Matrix3d &R = R_sols[i];

            // 内联计算 tx.transpose() * tx * R
            const double t0 = t(0), t1 = t(1), t2 = t(2);
            const double t0_sq = t0 * t0, t1_sq = t1 * t1, t2_sq = t2 * t2;

            // QtQ * R 直接计算，避免临时变量
            double QtQR_data[9];
            QtQR_data[0] = (t1_sq + t2_sq) * R(0, 0) + (-t0 * t1) * R(1, 0) + (-t0 * t2) * R(2, 0);
            QtQR_data[1] = (t1_sq + t2_sq) * R(0, 1) + (-t0 * t1) * R(1, 1) + (-t0 * t2) * R(2, 1);
            QtQR_data[2] = (t1_sq + t2_sq) * R(0, 2) + (-t0 * t1) * R(1, 2) + (-t0 * t2) * R(2, 2);
            QtQR_data[3] = (-t0 * t1) * R(0, 0) + (t0_sq + t2_sq) * R(1, 0) + (-t1 * t2) * R(2, 0);
            QtQR_data[4] = (-t0 * t1) * R(0, 1) + (t0_sq + t2_sq) * R(1, 1) + (-t1 * t2) * R(2, 1);
            QtQR_data[5] = (-t0 * t1) * R(0, 2) + (t0_sq + t2_sq) * R(1, 2) + (-t1 * t2) * R(2, 2);
            QtQR_data[6] = (-t0 * t2) * R(0, 0) + (-t1 * t2) * R(1, 0) + (t0_sq + t1_sq) * R(2, 0);
            QtQR_data[7] = (-t0 * t2) * R(0, 1) + (-t1 * t2) * R(1, 1) + (t0_sq + t1_sq) * R(2, 1);
            QtQR_data[8] = (-t0 * t2) * R(0, 2) + (-t1 * t2) * R(1, 2) + (t0_sq + t1_sq) * R(2, 2);

            // 计算 sum_A' * QtQR_data，然后统计>0的个数（与原版保持一致）
            double total_score = 0.0;
            for (int j = 0; j < 9; ++j)
            {
                total_score += sum_A[j] * QtQR_data[j];
            }

            // 使用符号函数统计>0的个数，而不是直接求和
            int count = (total_score > 0) ? 1 : 0;

            if (count > best_rotation_count)
            {
                best_rotation_count = count;
                best_R_idx = i;
            }
        }

        RelativePose result;
        result.Rij = R_sols[best_R_idx];
        const Matrix3d &R = result.Rij;

        // 评估平移向量 - 使用计数而不是直接求和
        int best_translation_count = -1;
        int best_t_idx = 0;

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const double t0 = t(0), t1 = t(1), t2 = t(2);

            int count = 0;

            for (int j = 0; j < n; ++j)
            {
                const int idx = j * 3;

                // R * x[j] - 内联矩阵向量乘法
                const double Rx0 = R(0, 0) * x_data[idx] + R(0, 1) * x_data[idx + 1] + R(0, 2) * x_data[idx + 2];
                const double Rx1 = R(1, 0) * x_data[idx] + R(1, 1) * x_data[idx + 1] + R(1, 2) * x_data[idx + 2];
                const double Rx2 = R(2, 0) * x_data[idx] + R(2, 1) * x_data[idx + 1] + R(2, 2) * x_data[idx + 2];

                // t × (R * x[j]) - 内联叉积
                const double tmp1_0 = t1 * Rx2 - t2 * Rx1;
                const double tmp1_1 = t2 * Rx0 - t0 * Rx2;
                const double tmp1_2 = t0 * Rx1 - t1 * Rx0;

                // xmatch[j] × (R * x[j]) - 内联叉积
                const double xm0 = xmatch_data[idx], xm1 = xmatch_data[idx + 1], xm2 = xmatch_data[idx + 2];
                const double tmp2_0 = xm1 * Rx2 - xm2 * Rx1;
                const double tmp2_1 = xm2 * Rx0 - xm0 * Rx2;
                const double tmp2_2 = xm0 * Rx1 - xm1 * Rx0;

                // t × xmatch[j] - 内联叉积
                const double tmp3_0 = t1 * xm2 - t2 * xm1;
                const double tmp3_1 = t2 * xm0 - t0 * xm2;
                const double tmp3_2 = t0 * xm1 - t1 * xm0;

                // 点积计算 - 内联，并统计>0的个数
                const double dot1 = tmp1_0 * tmp2_0 + tmp1_1 * tmp2_1 + tmp1_2 * tmp2_2;
                const double dot2 = tmp2_0 * tmp3_0 + tmp2_1 * tmp3_1 + tmp2_2 * tmp3_2;

                count += (dot1 > 0 ? 1 : 0) + (dot2 > 0 ? 1 : 0);
            }

            if (count > best_translation_count)
            {
                best_translation_count = count;
                best_t_idx = i;
            }
        }

        result.tij = t_sols[best_t_idx];
        ratio = best_translation_count;

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 裸机优化版本：最小内存分配 (修正版)
     */
    RelativePose RT_Check2_bare_metal_optimized_corrected(const BearingVectors &v1,
                                                          const BearingVectors &v2,
                                                          const vector<Matrix3d> &R_sols,
                                                          const vector<Vector3d> &t_sols,
                                                          const VectorXd *ptr_weights,
                                                          double &ratio)
    {
        const int n = v1.cols();

        // 栈上内存分配，确保cache对齐
        alignas(32) double sum_A[9] = {0.0};
        std::vector<double> inv_z1(n), inv_z2(n);
        std::vector<double> x_data(3 * n), xmatch_data(3 * n);

        // 预计算逆深度和归一化坐标
        for (int i = 0; i < n; ++i)
        {
            inv_z1[i] = 1.0 / v1(2, i);
            inv_z2[i] = 1.0 / v2(2, i);

            const int idx = i * 3;
            xmatch_data[idx] = v1(0, i) * inv_z1[i];
            xmatch_data[idx + 1] = v1(1, i) * inv_z1[i];
            xmatch_data[idx + 2] = 1.0;

            x_data[idx] = v2(0, i) * inv_z2[i];
            x_data[idx + 1] = v2(1, i) * inv_z2[i];
            x_data[idx + 2] = 1.0;

            // 计算sum_A - 正确的顺序：v2[i] ⊗ v1[i]
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
            sum_A[0] += w_v2_0 * v1_i(0);
            sum_A[1] += w_v2_0 * v1_i(1);
            sum_A[2] += w_v2_0 * v1_i(2);
            sum_A[3] += w_v2_1 * v1_i(0);
            sum_A[4] += w_v2_1 * v1_i(1);
            sum_A[5] += w_v2_1 * v1_i(2);
            sum_A[6] += w_v2_2 * v1_i(0);
            sum_A[7] += w_v2_2 * v1_i(1);
            sum_A[8] += w_v2_2 * v1_i(2);
        }

        // 评估旋转矩阵 - 使用计数而不是直接求和，与原版保持一致
        int best_rotation_count = -1;
        int best_R_idx = 0;

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const Matrix3d &R = R_sols[i];

            // 内联计算 tx.transpose() * tx * R
            const double t0 = t(0), t1 = t(1), t2 = t(2);
            const double t0_sq = t0 * t0, t1_sq = t1 * t1, t2_sq = t2 * t2;

            // QtQ * R 直接计算，避免临时变量
            double QtQR_data[9];
            QtQR_data[0] = (t1_sq + t2_sq) * R(0, 0) + (-t0 * t1) * R(1, 0) + (-t0 * t2) * R(2, 0);
            QtQR_data[1] = (t1_sq + t2_sq) * R(0, 1) + (-t0 * t1) * R(1, 1) + (-t0 * t2) * R(2, 1);
            QtQR_data[2] = (t1_sq + t2_sq) * R(0, 2) + (-t0 * t1) * R(1, 2) + (-t0 * t2) * R(2, 2);
            QtQR_data[3] = (-t0 * t1) * R(0, 0) + (t0_sq + t2_sq) * R(1, 0) + (-t1 * t2) * R(2, 0);
            QtQR_data[4] = (-t0 * t1) * R(0, 1) + (t0_sq + t2_sq) * R(1, 1) + (-t1 * t2) * R(2, 1);
            QtQR_data[5] = (-t0 * t1) * R(0, 2) + (t0_sq + t2_sq) * R(1, 2) + (-t1 * t2) * R(2, 2);
            QtQR_data[6] = (-t0 * t2) * R(0, 0) + (-t1 * t2) * R(1, 0) + (t0_sq + t1_sq) * R(2, 0);
            QtQR_data[7] = (-t0 * t2) * R(0, 1) + (-t1 * t2) * R(1, 1) + (t0_sq + t1_sq) * R(2, 1);
            QtQR_data[8] = (-t0 * t2) * R(0, 2) + (-t1 * t2) * R(1, 2) + (t0_sq + t1_sq) * R(2, 2);

            // 计算 sum_A' * QtQR_data，然后统计>0的个数（与原版保持一致）
            double total_score = 0.0;
            for (int j = 0; j < 9; ++j)
            {
                total_score += sum_A[j] * QtQR_data[j];
            }

            // 使用符号函数统计>0的个数，而不是直接求和
            int count = (total_score > 0) ? 1 : 0;

            if (count > best_rotation_count)
            {
                best_rotation_count = count;
                best_R_idx = i;
            }
        }

        RelativePose result;
        result.Rij = R_sols[best_R_idx];
        const Matrix3d &R = result.Rij;

        // 评估平移向量 - 使用计数而不是直接求和
        int best_translation_count = -1;
        int best_t_idx = 0;

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const double t0 = t(0), t1 = t(1), t2 = t(2);

            int count = 0;

            for (int j = 0; j < n; ++j)
            {
                const int idx = j * 3;

                // R * x[j] - 内联矩阵向量乘法
                const double Rx0 = R(0, 0) * x_data[idx] + R(0, 1) * x_data[idx + 1] + R(0, 2) * x_data[idx + 2];
                const double Rx1 = R(1, 0) * x_data[idx] + R(1, 1) * x_data[idx + 1] + R(1, 2) * x_data[idx + 2];
                const double Rx2 = R(2, 0) * x_data[idx] + R(2, 1) * x_data[idx + 1] + R(2, 2) * x_data[idx + 2];

                // t × (R * x[j]) - 内联叉积
                const double tmp1_0 = t1 * Rx2 - t2 * Rx1;
                const double tmp1_1 = t2 * Rx0 - t0 * Rx2;
                const double tmp1_2 = t0 * Rx1 - t1 * Rx0;

                // xmatch[j] × (R * x[j]) - 内联叉积
                const double xm0 = xmatch_data[idx], xm1 = xmatch_data[idx + 1], xm2 = xmatch_data[idx + 2];
                const double tmp2_0 = xm1 * Rx2 - xm2 * Rx1;
                const double tmp2_1 = xm2 * Rx0 - xm0 * Rx2;
                const double tmp2_2 = xm0 * Rx1 - xm1 * Rx0;

                // t × xmatch[j] - 内联叉积
                const double tmp3_0 = t1 * xm2 - t2 * xm1;
                const double tmp3_1 = t2 * xm0 - t0 * xm2;
                const double tmp3_2 = t0 * xm1 - t1 * xm0;

                // 点积计算 - 内联，并统计>0的个数
                const double dot1 = tmp1_0 * tmp2_0 + tmp1_1 * tmp2_1 + tmp1_2 * tmp2_2;
                const double dot2 = tmp2_0 * tmp3_0 + tmp2_1 * tmp3_1 + tmp2_2 * tmp3_2;

                count += (dot1 > 0 ? 1 : 0) + (dot2 > 0 ? 1 : 0);
            }

            if (count > best_translation_count)
            {
                best_translation_count = count;
                best_t_idx = i;
            }
        }

        result.tij = t_sols[best_t_idx];
        ratio = best_translation_count;

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 安全极致优化版本：确保算法正确性
     */
    RelativePose RT_Check2_safe_ultra_optimized(const MatrixXd &A,
                                                const BearingVectors &v1,
                                                const BearingVectors &v2,
                                                const vector<Matrix3d> &R_sols,
                                                const vector<Vector3d> &t_sols,
                                                const VectorXd *ptr_weights,
                                                double &ratio)
    {
        const int n = v1.cols();

        // 预计算sum_A - 与stack_optimized版本相同的逻辑
        alignas(32) double sum_A[9] = {0.0};
        for (int i = 0; i < n; ++i)
        {
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
            sum_A[0] += w_v2_0 * v1_i(0);
            sum_A[1] += w_v2_0 * v1_i(1);
            sum_A[2] += w_v2_0 * v1_i(2);
            sum_A[3] += w_v2_1 * v1_i(0);
            sum_A[4] += w_v2_1 * v1_i(1);
            sum_A[5] += w_v2_1 * v1_i(2);
            sum_A[6] += w_v2_2 * v1_i(0);
            sum_A[7] += w_v2_2 * v1_i(1);
            sum_A[8] += w_v2_2 * v1_i(2);
        }

        // 评估旋转矩阵 - 使用与原始版本相同的逻辑但优化计算
        std::array<double, 2> rotation_scores = {0.0, 0.0};
        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const Matrix3d &R = R_sols[i];

            // 构建叉积矩阵 tx - 保持原始逻辑
            Matrix3d tx;
            tx << 0, -t(2), t(1),
                t(2), 0, -t(0),
                -t(1), t(0), 0;

            // 计算 QtQ = tx.transpose() * tx * R - 保持原始逻辑
            Matrix3d QtQ = tx.transpose() * tx * R;

            // 使用sum_A向量化计算
            rotation_scores[i] = sum_A[0] * QtQ(0, 0) + sum_A[1] * QtQ(0, 1) + sum_A[2] * QtQ(0, 2) +
                                 sum_A[3] * QtQ(1, 0) + sum_A[4] * QtQ(1, 1) + sum_A[5] * QtQ(1, 2) +
                                 sum_A[6] * QtQ(2, 0) + sum_A[7] * QtQ(2, 1) + sum_A[8] * QtQ(2, 2);
        }

        // 选择最优旋转
        const int best_R_idx = rotation_scores[0] > rotation_scores[1] ? 0 : 1;
        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量 - 保持原始算法逻辑，只优化计算
        std::array<double, 2> translation_scores = {0.0, 0.0};

        // 预计算归一化坐标 - 避免重复计算
        std::vector<Vector3d> x_vec(n), xmatch_vec(n);
        for (int i = 0; i < n; ++i)
        {
            x_vec[i] = v2.col(i) / v2(2, i);
            xmatch_vec[i] = v1.col(i) / v1(2, i);
        }

        const Matrix3d &R = result.Rij;
        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            double score = 0.0;

            for (int j = 0; j < n; ++j)
            {
                const Vector3d &x_j = x_vec[j];
                const Vector3d &xmatch_j = xmatch_vec[j];

                // 保持与原始版本完全相同的计算逻辑
                const Vector3d tmp_1 = cross_matrix(t) * R * x_j;
                const Vector3d tmp_2 = cross_matrix(xmatch_j) * R * x_j;
                const Vector3d tmp_3 = cross_matrix(t) * xmatch_j;

                const double sside1_ratio = tmp_1.dot(tmp_2) > 0 ? 1 : 0;
                const double sside2_ratio = tmp_2.dot(tmp_3) > 0 ? 1 : 0;
                score += sside1_ratio + sside2_ratio;
            }
            translation_scores[i] = score;
        }

        const int best_t_idx = translation_scores[0] > translation_scores[1] ? 0 : 1;
        result.tij = t_sols[best_t_idx];
        ratio = std::max(translation_scores[0], translation_scores[1]);

        return result;
    }

    /**
     * @brief 检查并选择最优的R和t组合 - 终极优化版本：在确保正确性基础上的最大优化
     */
    RelativePose RT_Check2_final_optimized(const MatrixXd &A,
                                           const BearingVectors &v1,
                                           const BearingVectors &v2,
                                           const vector<Matrix3d> &R_sols,
                                           const vector<Vector3d> &t_sols,
                                           const VectorXd *ptr_weights,
                                           double &ratio)
    {
        const int n = v1.cols();

        // 预计算sum_A和归一化坐标 - 单次遍历
        alignas(32) double sum_A[9] = {0.0};
        std::vector<Vector3d> x_vec(n), xmatch_vec(n);

        for (int i = 0; i < n; ++i)
        {
            const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
            const Vector3d &v1_i = v1.col(i);
            const Vector3d &v2_i = v2.col(i);

            // 归一化坐标
            xmatch_vec[i] = v1_i / v1_i(2);
            x_vec[i] = v2_i / v2_i(2);

            // 累加sum_A
            const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
            sum_A[0] += w_v2_0 * v1_i(0);
            sum_A[1] += w_v2_0 * v1_i(1);
            sum_A[2] += w_v2_0 * v1_i(2);
            sum_A[3] += w_v2_1 * v1_i(0);
            sum_A[4] += w_v2_1 * v1_i(1);
            sum_A[5] += w_v2_1 * v1_i(2);
            sum_A[6] += w_v2_2 * v1_i(0);
            sum_A[7] += w_v2_2 * v1_i(1);
            sum_A[8] += w_v2_2 * v1_i(2);
        }

        // 评估旋转矩阵 - 优化但保持算法正确性
        double best_rotation_score = -std::numeric_limits<double>::infinity();
        int best_R_idx = 0;

        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const Matrix3d &R = R_sols[i];

            // 快速内联计算 tx.transpose() * tx
            const double t0 = t(0), t1 = t(1), t2 = t(2);
            const double t0_sq = t0 * t0, t1_sq = t1 * t1, t2_sq = t2 * t2;

            // QtQ = (tx.transpose() * tx) * R 的直接计算
            double score = 0.0;
            // 第一行
            score += sum_A[0] * ((t1_sq + t2_sq) * R(0, 0) + (-t0 * t1) * R(1, 0) + (-t0 * t2) * R(2, 0));
            score += sum_A[1] * ((t1_sq + t2_sq) * R(0, 1) + (-t0 * t1) * R(1, 1) + (-t0 * t2) * R(2, 1));
            score += sum_A[2] * ((t1_sq + t2_sq) * R(0, 2) + (-t0 * t1) * R(1, 2) + (-t0 * t2) * R(2, 2));
            // 第二行
            score += sum_A[3] * ((-t0 * t1) * R(0, 0) + (t0_sq + t2_sq) * R(1, 0) + (-t1 * t2) * R(2, 0));
            score += sum_A[4] * ((-t0 * t1) * R(0, 1) + (t0_sq + t2_sq) * R(1, 1) + (-t1 * t2) * R(2, 1));
            score += sum_A[5] * ((-t0 * t1) * R(0, 2) + (t0_sq + t2_sq) * R(1, 2) + (-t1 * t2) * R(2, 2));
            // 第三行
            score += sum_A[6] * ((-t0 * t2) * R(0, 0) + (-t1 * t2) * R(1, 0) + (t0_sq + t1_sq) * R(2, 0));
            score += sum_A[7] * ((-t0 * t2) * R(0, 1) + (-t1 * t2) * R(1, 1) + (t0_sq + t1_sq) * R(2, 1));
            score += sum_A[8] * ((-t0 * t2) * R(0, 2) + (-t1 * t2) * R(1, 2) + (t0_sq + t1_sq) * R(2, 2));

            if (score > best_rotation_score)
            {
                best_rotation_score = score;
                best_R_idx = i;
            }
        }

        RelativePose result;
        result.Rij = R_sols[best_R_idx];

        // 评估平移向量 - 内联优化但保持算法正确性
        double best_translation_score = -std::numeric_limits<double>::infinity();
        int best_t_idx = 0;

        const Matrix3d &R = result.Rij;
        for (int i = 0; i < 2; ++i)
        {
            const Vector3d &t = t_sols[i];
            const double t0 = t(0), t1 = t(1), t2 = t(2);
            double score = 0.0;

            for (int j = 0; j < n; ++j)
            {
                const Vector3d &x_j = x_vec[j];
                const Vector3d &xmatch_j = xmatch_vec[j];

                // R * x_j
                const Vector3d Rx_j = R * x_j;
                const double Rx_j0 = Rx_j(0), Rx_j1 = Rx_j(1), Rx_j2 = Rx_j(2);

                // tmp_1 = t × (R * x_j) - 内联叉积
                const Vector3d tmp_1(t1 * Rx_j2 - t2 * Rx_j1, t2 * Rx_j0 - t0 * Rx_j2, t0 * Rx_j1 - t1 * Rx_j0);

                // tmp_2 = xmatch_j × (R * x_j) - 内联叉积
                const double xm0 = xmatch_j(0), xm1 = xmatch_j(1), xm2 = xmatch_j(2);
                const Vector3d tmp_2(xm1 * Rx_j2 - xm2 * Rx_j1, xm2 * Rx_j0 - xm0 * Rx_j2, xm0 * Rx_j1 - xm1 * Rx_j0);

                // tmp_3 = t × xmatch_j - 内联叉积
                const Vector3d tmp_3(t1 * xm2 - t2 * xm1, t2 * xm0 - t0 * xm2, t0 * xm1 - t1 * xm0);

                // 计算评分 - 保持原始逻辑
                const double sside1_ratio = tmp_1.dot(tmp_2) > 0 ? 1 : 0;
                const double sside2_ratio = tmp_2.dot(tmp_3) > 0 ? 1 : 0;
                score += sside1_ratio + sside2_ratio;
            }

            if (score > best_translation_score)
            {
                best_translation_score = score;
                best_t_idx = i;
            }
        }

        result.tij = t_sols[best_t_idx];
        ratio = best_translation_score;

        return result;
    }
}