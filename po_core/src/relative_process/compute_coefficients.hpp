#include <Eigen/Dense>

using namespace Eigen;
typedef Matrix<double, 3, 9> EigenSols;
typedef Matrix<double, 10, 10> GMatrix;

inline void compute_Gcoefficients2(const EigenSols &eigen_sols, GMatrix &G)
{

    // id = [1,2,4,7,3,5,8,6,9,10];

    // BB = BB(:,id);

    // BB = BB'*BB;

    //[~,~,Vb]=svds(BB,1,'smallest');
    double X1, X2, X3, X4, X5, X6, X7, X8, X9;
    double Y1, Y2, Y3, Y4, Y5, Y6, Y7, Y8, Y9;
    double Z1, Z2, Z3, Z4, Z5, Z6, Z7, Z8, Z9;

    X1 = eigen_sols(0, 0);
    X2 = eigen_sols(0, 1);
    X3 = eigen_sols(0, 2);
    X4 = eigen_sols(0, 3);
    X5 = eigen_sols(0, 4);
    X6 = eigen_sols(0, 5);
    X7 = eigen_sols(0, 6);
    X8 = eigen_sols(0, 7);
    X9 = eigen_sols(0, 8);

    Y1 = eigen_sols(1, 0);
    Y2 = eigen_sols(1, 1);
    Y3 = eigen_sols(1, 2);
    Y4 = eigen_sols(1, 3);
    Y5 = eigen_sols(1, 4);
    Y6 = eigen_sols(1, 5);
    Y7 = eigen_sols(1, 6);
    Y8 = eigen_sols(1, 7);
    Y9 = eigen_sols(1, 8);

    Z1 = eigen_sols(2, 0);
    Z2 = eigen_sols(2, 1);
    Z3 = eigen_sols(2, 2);
    Z4 = eigen_sols(2, 3);
    Z5 = eigen_sols(2, 4);
    Z6 = eigen_sols(2, 5);
    Z7 = eigen_sols(2, 6);
    Z8 = eigen_sols(2, 7);
    Z9 = eigen_sols(2, 8);

    double t2 = X1 * X1;
    double t3 = X2 * X2;
    double t4 = X3 * X3;
    double t5 = X4 * X4;
    double t6 = X5 * X5;
    double t7 = X6 * X6;
    double t8 = X7 * X7;
    double t9 = X8 * X8;
    double t10 = X9 * X9;
    double t11 = Y2 * Y2;
    double t12 = Y3 * Y3;
    double t13 = Y4 * Y4;
    double t14 = Y5 * Y5;
    double t15 = Y6 * Y6;
    double t16 = Y7 * Y7;
    double t17 = Y8 * Y8;
    double t18 = Y9 * Y9;
    double t19 = Y1 * Y1;
    double t20 = Z1 * Z1;
    double t21 = Z2 * Z2;
    double t22 = Z3 * Z3;
    double t23 = Z4 * Z4;
    double t24 = Z5 * Z5;
    double t25 = Z6 * Z6;
    double t26 = Z7 * Z7;
    double t27 = Z8 * Z8;
    double t28 = Z9 * Z9;

    double G0[10 * 10];
    G0[0 + 0 * 9] = X1 * t2 + X1 * t3 + X1 * t4 + X1 * t5 - X1 * t6 - X1 * t7 + X1 * t8 - X1 * t9 - X1 * t10 + X2 * X4 * X5 * 2.0 + X3 * X4 * X6 * 2.0 + X2 * X7 * X8 * 2.0 + X3 * X7 * X9 * 2.0;
    G0[0 + 1 * 9] = Y1 * t2 * 3.0 + Y1 * t3 + Y1 * t4 + Y1 * t5 - Y1 * t6 - Y1 * t7 + Y1 * t8 - Y1 * t9 - Y1 * t10 + X1 * X2 * Y2 * 2.0 + X1 * X3 * Y3 * 2.0 + X1 * X4 * Y4 * 2.0 - X1 * X5 * Y5 * 2.0 + X2 * X4 * Y5 * 2.0 + X2 * X5 * Y4 * 2.0 + X4 * X5 * Y2 * 2.0 - X1 * X6 * Y6 * 2.0 + X3 * X4 * Y6 * 2.0 + X3 * X6 * Y4 * 2.0 + X4 * X6 * Y3 * 2.0 + X1 * X7 * Y7 * 2.0 - X1 * X8 * Y8 * 2.0 + X2 * X7 * Y8 * 2.0 + X2 * X8 * Y7 * 2.0 + X7 * X8 * Y2 * 2.0 - X1 * X9 * Y9 * 2.0 + X3 * X7 * Y9 * 2.0 + X3 * X9 * Y7 * 2.0 + X7 * X9 * Y3 * 2.0;
    G0[0 + 2 * 9] = Z1 * t2 * 3.0 + Z1 * t3 + Z1 * t4 + Z1 * t5 - Z1 * t6 - Z1 * t7 + Z1 * t8 - Z1 * t9 - Z1 * t10 + X1 * X2 * Z2 * 2.0 + X1 * X3 * Z3 * 2.0 + X1 * X4 * Z4 * 2.0 - X1 * X5 * Z5 * 2.0 + X2 * X4 * Z5 * 2.0 + X2 * X5 * Z4 * 2.0 + X4 * X5 * Z2 * 2.0 - X1 * X6 * Z6 * 2.0 + X3 * X4 * Z6 * 2.0 + X3 * X6 * Z4 * 2.0 + X4 * X6 * Z3 * 2.0 + X1 * X7 * Z7 * 2.0 - X1 * X8 * Z8 * 2.0 + X2 * X7 * Z8 * 2.0 + X2 * X8 * Z7 * 2.0 + X7 * X8 * Z2 * 2.0 - X1 * X9 * Z9 * 2.0 + X3 * X7 * Z9 * 2.0 + X3 * X9 * Z7 * 2.0 + X7 * X9 * Z3 * 2.0;
    G0[0 + 3 * 9] = X1 * t11 + X1 * t12 + X1 * t13 - X1 * t14 - X1 * t15 + X1 * t16 - X1 * t17 - X1 * t18 + X1 * t19 * 3.0 + X2 * Y1 * Y2 * 2.0 + X3 * Y1 * Y3 * 2.0 + X4 * Y1 * Y4 * 2.0 + X2 * Y4 * Y5 * 2.0 + X4 * Y2 * Y5 * 2.0 - X5 * Y1 * Y5 * 2.0 + X5 * Y2 * Y4 * 2.0 + X3 * Y4 * Y6 * 2.0 + X4 * Y3 * Y6 * 2.0 - X6 * Y1 * Y6 * 2.0 + X6 * Y3 * Y4 * 2.0 + X7 * Y1 * Y7 * 2.0 + X2 * Y7 * Y8 * 2.0 + X7 * Y2 * Y8 * 2.0 - X8 * Y1 * Y8 * 2.0 + X8 * Y2 * Y7 * 2.0 + X3 * Y7 * Y9 * 2.0 + X7 * Y3 * Y9 * 2.0 - X9 * Y1 * Y9 * 2.0 + X9 * Y3 * Y7 * 2.0;
    G0[0 + 4 * 9] = X1 * Y1 * Z1 * 6.0 + X1 * Y2 * Z2 * 2.0 + X2 * Y1 * Z2 * 2.0 + X2 * Y2 * Z1 * 2.0 + X1 * Y3 * Z3 * 2.0 + X3 * Y1 * Z3 * 2.0 + X3 * Y3 * Z1 * 2.0 + X1 * Y4 * Z4 * 2.0 + X4 * Y1 * Z4 * 2.0 + X4 * Y4 * Z1 * 2.0 - X1 * Y5 * Z5 * 2.0 + X2 * Y4 * Z5 * 2.0 + X2 * Y5 * Z4 * 2.0 + X4 * Y2 * Z5 * 2.0 + X4 * Y5 * Z2 * 2.0 - X5 * Y1 * Z5 * 2.0 + X5 * Y2 * Z4 * 2.0 + X5 * Y4 * Z2 * 2.0 - X5 * Y5 * Z1 * 2.0 - X1 * Y6 * Z6 * 2.0 + X3 * Y4 * Z6 * 2.0 + X3 * Y6 * Z4 * 2.0 + X4 * Y3 * Z6 * 2.0 + X4 * Y6 * Z3 * 2.0 - X6 * Y1 * Z6 * 2.0 + X6 * Y3 * Z4 * 2.0 + X6 * Y4 * Z3 * 2.0 - X6 * Y6 * Z1 * 2.0 + X1 * Y7 * Z7 * 2.0 + X7 * Y1 * Z7 * 2.0 + X7 * Y7 * Z1 * 2.0 - X1 * Y8 * Z8 * 2.0 + X2 * Y7 * Z8 * 2.0 + X2 * Y8 * Z7 * 2.0 + X7 * Y2 * Z8 * 2.0 + X7 * Y8 * Z2 * 2.0 - X8 * Y1 * Z8 * 2.0 + X8 * Y2 * Z7 * 2.0 + X8 * Y7 * Z2 * 2.0 - X8 * Y8 * Z1 * 2.0 - X1 * Y9 * Z9 * 2.0 + X3 * Y7 * Z9 * 2.0 + X3 * Y9 * Z7 * 2.0 + X7 * Y3 * Z9 * 2.0 + X7 * Y9 * Z3 * 2.0 - X9 * Y1 * Z9 * 2.0 + X9 * Y3 * Z7 * 2.0 + X9 * Y7 * Z3 * 2.0 - X9 * Y9 * Z1 * 2.0;
    G0[0 + 5 * 9] = X1 * t20 * 3.0 + X1 * t21 + X1 * t22 + X1 * t23 - X1 * t24 - X1 * t25 + X1 * t26 - X1 * t27 - X1 * t28 + X2 * Z1 * Z2 * 2.0 + X3 * Z1 * Z3 * 2.0 + X4 * Z1 * Z4 * 2.0 + X2 * Z4 * Z5 * 2.0 + X4 * Z2 * Z5 * 2.0 - X5 * Z1 * Z5 * 2.0 + X5 * Z2 * Z4 * 2.0 + X3 * Z4 * Z6 * 2.0 + X4 * Z3 * Z6 * 2.0 - X6 * Z1 * Z6 * 2.0 + X6 * Z3 * Z4 * 2.0 + X7 * Z1 * Z7 * 2.0 + X2 * Z7 * Z8 * 2.0 + X7 * Z2 * Z8 * 2.0 - X8 * Z1 * Z8 * 2.0 + X8 * Z2 * Z7 * 2.0 + X3 * Z7 * Z9 * 2.0 + X7 * Z3 * Z9 * 2.0 - X9 * Z1 * Z9 * 2.0 + X9 * Z3 * Z7 * 2.0;
    G0[0 + 6 * 9] = Y1 * t11 + Y1 * t12 + Y1 * t13 - Y1 * t14 - Y1 * t15 + Y1 * t16 - Y1 * t17 - Y1 * t18 + Y1 * t19 + Y2 * Y4 * Y5 * 2.0 + Y3 * Y4 * Y6 * 2.0 + Y2 * Y7 * Y8 * 2.0 + Y3 * Y7 * Y9 * 2.0;
    G0[0 + 7 * 9] = Z1 * t11 + Z1 * t12 + Z1 * t13 - Z1 * t14 - Z1 * t15 + Z1 * t16 - Z1 * t17 - Z1 * t18 + Z1 * t19 * 3.0 + Y1 * Y2 * Z2 * 2.0 + Y1 * Y3 * Z3 * 2.0 + Y1 * Y4 * Z4 * 2.0 - Y1 * Y5 * Z5 * 2.0 + Y2 * Y4 * Z5 * 2.0 + Y2 * Y5 * Z4 * 2.0 + Y4 * Y5 * Z2 * 2.0 - Y1 * Y6 * Z6 * 2.0 + Y3 * Y4 * Z6 * 2.0 + Y3 * Y6 * Z4 * 2.0 + Y4 * Y6 * Z3 * 2.0 + Y1 * Y7 * Z7 * 2.0 - Y1 * Y8 * Z8 * 2.0 + Y2 * Y7 * Z8 * 2.0 + Y2 * Y8 * Z7 * 2.0 + Y7 * Y8 * Z2 * 2.0 - Y1 * Y9 * Z9 * 2.0 + Y3 * Y7 * Z9 * 2.0 + Y3 * Y9 * Z7 * 2.0 + Y7 * Y9 * Z3 * 2.0;
    G0[0 + 8 * 9] = Y1 * t20 * 3.0 + Y1 * t21 + Y1 * t22 + Y1 * t23 - Y1 * t24 - Y1 * t25 + Y1 * t26 - Y1 * t27 - Y1 * t28 + Y2 * Z1 * Z2 * 2.0 + Y3 * Z1 * Z3 * 2.0 + Y4 * Z1 * Z4 * 2.0 + Y2 * Z4 * Z5 * 2.0 + Y4 * Z2 * Z5 * 2.0 - Y5 * Z1 * Z5 * 2.0 + Y5 * Z2 * Z4 * 2.0 + Y3 * Z4 * Z6 * 2.0 + Y4 * Z3 * Z6 * 2.0 - Y6 * Z1 * Z6 * 2.0 + Y6 * Z3 * Z4 * 2.0 + Y7 * Z1 * Z7 * 2.0 + Y2 * Z7 * Z8 * 2.0 + Y7 * Z2 * Z8 * 2.0 - Y8 * Z1 * Z8 * 2.0 + Y8 * Z2 * Z7 * 2.0 + Y3 * Z7 * Z9 * 2.0 + Y7 * Z3 * Z9 * 2.0 - Y9 * Z1 * Z9 * 2.0 + Y9 * Z3 * Z7 * 2.0;
    G0[0 + 9 * 9] = Z1 * t20 + Z1 * t21 + Z1 * t22 + Z1 * t23 - Z1 * t24 - Z1 * t25 + Z1 * t26 - Z1 * t27 - Z1 * t28 + Z2 * Z4 * Z5 * 2.0 + Z3 * Z4 * Z6 * 2.0 + Z2 * Z7 * Z8 * 2.0 + Z3 * Z7 * Z9 * 2.0;

    G0[1 + 0 * 9] = X2 * t2 + X2 * t3 + X2 * t4 - X2 * t5 + X2 * t6 - X2 * t7 - X2 * t8 + X2 * t9 - X2 * t10 + X1 * X4 * X5 * 2.0 + X3 * X5 * X6 * 2.0 + X1 * X7 * X8 * 2.0 + X3 * X8 * X9 * 2.0;
    G0[1 + 1 * 9] = Y2 * t2 + Y2 * t3 * 3.0 + Y2 * t4 - Y2 * t5 + Y2 * t6 - Y2 * t7 - Y2 * t8 + Y2 * t9 - Y2 * t10 + X1 * X2 * Y1 * 2.0 + X2 * X3 * Y3 * 2.0 + X1 * X4 * Y5 * 2.0 + X1 * X5 * Y4 * 2.0 - X2 * X4 * Y4 * 2.0 + X4 * X5 * Y1 * 2.0 + X2 * X5 * Y5 * 2.0 - X2 * X6 * Y6 * 2.0 + X3 * X5 * Y6 * 2.0 + X3 * X6 * Y5 * 2.0 + X5 * X6 * Y3 * 2.0 + X1 * X7 * Y8 * 2.0 + X1 * X8 * Y7 * 2.0 - X2 * X7 * Y7 * 2.0 + X7 * X8 * Y1 * 2.0 + X2 * X8 * Y8 * 2.0 - X2 * X9 * Y9 * 2.0 + X3 * X8 * Y9 * 2.0 + X3 * X9 * Y8 * 2.0 + X8 * X9 * Y3 * 2.0;
    G0[1 + 2 * 9] = Z2 * t2 + Z2 * t3 * 3.0 + Z2 * t4 - Z2 * t5 + Z2 * t6 - Z2 * t7 - Z2 * t8 + Z2 * t9 - Z2 * t10 + X1 * X2 * Z1 * 2.0 + X2 * X3 * Z3 * 2.0 + X1 * X4 * Z5 * 2.0 + X1 * X5 * Z4 * 2.0 - X2 * X4 * Z4 * 2.0 + X4 * X5 * Z1 * 2.0 + X2 * X5 * Z5 * 2.0 - X2 * X6 * Z6 * 2.0 + X3 * X5 * Z6 * 2.0 + X3 * X6 * Z5 * 2.0 + X5 * X6 * Z3 * 2.0 + X1 * X7 * Z8 * 2.0 + X1 * X8 * Z7 * 2.0 - X2 * X7 * Z7 * 2.0 + X7 * X8 * Z1 * 2.0 + X2 * X8 * Z8 * 2.0 - X2 * X9 * Z9 * 2.0 + X3 * X8 * Z9 * 2.0 + X3 * X9 * Z8 * 2.0 + X8 * X9 * Z3 * 2.0;
    G0[1 + 3 * 9] = X2 * t11 * 3.0 + X2 * t12 - X2 * t13 + X2 * t14 - X2 * t15 - X2 * t16 + X2 * t17 - X2 * t18 + X2 * t19 + X1 * Y1 * Y2 * 2.0 + X3 * Y2 * Y3 * 2.0 + X1 * Y4 * Y5 * 2.0 + X4 * Y1 * Y5 * 2.0 - X4 * Y2 * Y4 * 2.0 + X5 * Y1 * Y4 * 2.0 + X5 * Y2 * Y5 * 2.0 + X3 * Y5 * Y6 * 2.0 + X5 * Y3 * Y6 * 2.0 - X6 * Y2 * Y6 * 2.0 + X6 * Y3 * Y5 * 2.0 + X1 * Y7 * Y8 * 2.0 + X7 * Y1 * Y8 * 2.0 - X7 * Y2 * Y7 * 2.0 + X8 * Y1 * Y7 * 2.0 + X8 * Y2 * Y8 * 2.0 + X3 * Y8 * Y9 * 2.0 + X8 * Y3 * Y9 * 2.0 - X9 * Y2 * Y9 * 2.0 + X9 * Y3 * Y8 * 2.0;
    G0[1 + 4 * 9] = X1 * Y1 * Z2 * 2.0 + X1 * Y2 * Z1 * 2.0 + X2 * Y1 * Z1 * 2.0 + X2 * Y2 * Z2 * 6.0 + X2 * Y3 * Z3 * 2.0 + X3 * Y2 * Z3 * 2.0 + X3 * Y3 * Z2 * 2.0 + X1 * Y4 * Z5 * 2.0 + X1 * Y5 * Z4 * 2.0 - X2 * Y4 * Z4 * 2.0 + X4 * Y1 * Z5 * 2.0 - X4 * Y2 * Z4 * 2.0 - X4 * Y4 * Z2 * 2.0 + X4 * Y5 * Z1 * 2.0 + X5 * Y1 * Z4 * 2.0 + X5 * Y4 * Z1 * 2.0 + X2 * Y5 * Z5 * 2.0 + X5 * Y2 * Z5 * 2.0 + X5 * Y5 * Z2 * 2.0 - X2 * Y6 * Z6 * 2.0 + X3 * Y5 * Z6 * 2.0 + X3 * Y6 * Z5 * 2.0 + X5 * Y3 * Z6 * 2.0 + X5 * Y6 * Z3 * 2.0 - X6 * Y2 * Z6 * 2.0 + X6 * Y3 * Z5 * 2.0 + X6 * Y5 * Z3 * 2.0 - X6 * Y6 * Z2 * 2.0 + X1 * Y7 * Z8 * 2.0 + X1 * Y8 * Z7 * 2.0 - X2 * Y7 * Z7 * 2.0 + X7 * Y1 * Z8 * 2.0 - X7 * Y2 * Z7 * 2.0 - X7 * Y7 * Z2 * 2.0 + X7 * Y8 * Z1 * 2.0 + X8 * Y1 * Z7 * 2.0 + X8 * Y7 * Z1 * 2.0 + X2 * Y8 * Z8 * 2.0 + X8 * Y2 * Z8 * 2.0 + X8 * Y8 * Z2 * 2.0 - X2 * Y9 * Z9 * 2.0 + X3 * Y8 * Z9 * 2.0 + X3 * Y9 * Z8 * 2.0 + X8 * Y3 * Z9 * 2.0 + X8 * Y9 * Z3 * 2.0 - X9 * Y2 * Z9 * 2.0 + X9 * Y3 * Z8 * 2.0 + X9 * Y8 * Z3 * 2.0 - X9 * Y9 * Z2 * 2.0;
    G0[1 + 5 * 9] = X2 * t20 + X2 * t21 * 3.0 + X2 * t22 - X2 * t23 + X2 * t24 - X2 * t25 - X2 * t26 + X2 * t27 - X2 * t28 + X1 * Z1 * Z2 * 2.0 + X3 * Z2 * Z3 * 2.0 + X1 * Z4 * Z5 * 2.0 + X4 * Z1 * Z5 * 2.0 - X4 * Z2 * Z4 * 2.0 + X5 * Z1 * Z4 * 2.0 + X5 * Z2 * Z5 * 2.0 + X3 * Z5 * Z6 * 2.0 + X5 * Z3 * Z6 * 2.0 - X6 * Z2 * Z6 * 2.0 + X6 * Z3 * Z5 * 2.0 + X1 * Z7 * Z8 * 2.0 + X7 * Z1 * Z8 * 2.0 - X7 * Z2 * Z7 * 2.0 + X8 * Z1 * Z7 * 2.0 + X8 * Z2 * Z8 * 2.0 + X3 * Z8 * Z9 * 2.0 + X8 * Z3 * Z9 * 2.0 - X9 * Z2 * Z9 * 2.0 + X9 * Z3 * Z8 * 2.0;
    G0[1 + 6 * 9] = Y2 * t11 + Y2 * t12 - Y2 * t13 + Y2 * t14 - Y2 * t15 - Y2 * t16 + Y2 * t17 - Y2 * t18 + Y2 * t19 + Y1 * Y4 * Y5 * 2.0 + Y3 * Y5 * Y6 * 2.0 + Y1 * Y7 * Y8 * 2.0 + Y3 * Y8 * Y9 * 2.0;
    G0[1 + 7 * 9] = Z2 * t11 * 3.0 + Z2 * t12 - Z2 * t13 + Z2 * t14 - Z2 * t15 - Z2 * t16 + Z2 * t17 - Z2 * t18 + Z2 * t19 + Y1 * Y2 * Z1 * 2.0 + Y2 * Y3 * Z3 * 2.0 + Y1 * Y4 * Z5 * 2.0 + Y1 * Y5 * Z4 * 2.0 - Y2 * Y4 * Z4 * 2.0 + Y4 * Y5 * Z1 * 2.0 + Y2 * Y5 * Z5 * 2.0 - Y2 * Y6 * Z6 * 2.0 + Y3 * Y5 * Z6 * 2.0 + Y3 * Y6 * Z5 * 2.0 + Y5 * Y6 * Z3 * 2.0 + Y1 * Y7 * Z8 * 2.0 + Y1 * Y8 * Z7 * 2.0 - Y2 * Y7 * Z7 * 2.0 + Y7 * Y8 * Z1 * 2.0 + Y2 * Y8 * Z8 * 2.0 - Y2 * Y9 * Z9 * 2.0 + Y3 * Y8 * Z9 * 2.0 + Y3 * Y9 * Z8 * 2.0 + Y8 * Y9 * Z3 * 2.0;
    G0[1 + 8 * 9] = Y2 * t20 + Y2 * t21 * 3.0 + Y2 * t22 - Y2 * t23 + Y2 * t24 - Y2 * t25 - Y2 * t26 + Y2 * t27 - Y2 * t28 + Y1 * Z1 * Z2 * 2.0 + Y3 * Z2 * Z3 * 2.0 + Y1 * Z4 * Z5 * 2.0 + Y4 * Z1 * Z5 * 2.0 - Y4 * Z2 * Z4 * 2.0 + Y5 * Z1 * Z4 * 2.0 + Y5 * Z2 * Z5 * 2.0 + Y3 * Z5 * Z6 * 2.0 + Y5 * Z3 * Z6 * 2.0 - Y6 * Z2 * Z6 * 2.0 + Y6 * Z3 * Z5 * 2.0 + Y1 * Z7 * Z8 * 2.0 + Y7 * Z1 * Z8 * 2.0 - Y7 * Z2 * Z7 * 2.0 + Y8 * Z1 * Z7 * 2.0 + Y8 * Z2 * Z8 * 2.0 + Y3 * Z8 * Z9 * 2.0 + Y8 * Z3 * Z9 * 2.0 - Y9 * Z2 * Z9 * 2.0 + Y9 * Z3 * Z8 * 2.0;
    G0[1 + 9 * 9] = Z2 * t20 + Z2 * t21 + Z2 * t22 - Z2 * t23 + Z2 * t24 - Z2 * t25 - Z2 * t26 + Z2 * t27 - Z2 * t28 + Z1 * Z4 * Z5 * 2.0 + Z3 * Z5 * Z6 * 2.0 + Z1 * Z7 * Z8 * 2.0 + Z3 * Z8 * Z9 * 2.0;

    G0[2 + 0 * 9] = X3 * t2 + X3 * t3 + X3 * t4 - X3 * t5 - X3 * t6 + X3 * t7 - X3 * t8 - X3 * t9 + X3 * t10 + X1 * X4 * X6 * 2.0 + X2 * X5 * X6 * 2.0 + X1 * X7 * X9 * 2.0 + X2 * X8 * X9 * 2.0;
    G0[2 + 1 * 9] = Y3 * t2 + Y3 * t3 + Y3 * t4 * 3.0 - Y3 * t5 - Y3 * t6 + Y3 * t7 - Y3 * t8 - Y3 * t9 + Y3 * t10 + X1 * X3 * Y1 * 2.0 + X2 * X3 * Y2 * 2.0 + X1 * X4 * Y6 * 2.0 + X1 * X6 * Y4 * 2.0 - X3 * X4 * Y4 * 2.0 + X4 * X6 * Y1 * 2.0 + X2 * X5 * Y6 * 2.0 + X2 * X6 * Y5 * 2.0 - X3 * X5 * Y5 * 2.0 + X5 * X6 * Y2 * 2.0 + X3 * X6 * Y6 * 2.0 + X1 * X7 * Y9 * 2.0 + X1 * X9 * Y7 * 2.0 - X3 * X7 * Y7 * 2.0 + X7 * X9 * Y1 * 2.0 + X2 * X8 * Y9 * 2.0 + X2 * X9 * Y8 * 2.0 - X3 * X8 * Y8 * 2.0 + X8 * X9 * Y2 * 2.0 + X3 * X9 * Y9 * 2.0;
    G0[2 + 2 * 9] = Z3 * t2 + Z3 * t3 + Z3 * t4 * 3.0 - Z3 * t5 - Z3 * t6 + Z3 * t7 - Z3 * t8 - Z3 * t9 + Z3 * t10 + X1 * X3 * Z1 * 2.0 + X2 * X3 * Z2 * 2.0 + X1 * X4 * Z6 * 2.0 + X1 * X6 * Z4 * 2.0 - X3 * X4 * Z4 * 2.0 + X4 * X6 * Z1 * 2.0 + X2 * X5 * Z6 * 2.0 + X2 * X6 * Z5 * 2.0 - X3 * X5 * Z5 * 2.0 + X5 * X6 * Z2 * 2.0 + X3 * X6 * Z6 * 2.0 + X1 * X7 * Z9 * 2.0 + X1 * X9 * Z7 * 2.0 - X3 * X7 * Z7 * 2.0 + X7 * X9 * Z1 * 2.0 + X2 * X8 * Z9 * 2.0 + X2 * X9 * Z8 * 2.0 - X3 * X8 * Z8 * 2.0 + X8 * X9 * Z2 * 2.0 + X3 * X9 * Z9 * 2.0;
    G0[2 + 3 * 9] = X3 * t11 + X3 * t12 * 3.0 - X3 * t13 - X3 * t14 + X3 * t15 - X3 * t16 - X3 * t17 + X3 * t18 + X3 * t19 + X1 * Y1 * Y3 * 2.0 + X2 * Y2 * Y3 * 2.0 + X1 * Y4 * Y6 * 2.0 + X4 * Y1 * Y6 * 2.0 - X4 * Y3 * Y4 * 2.0 + X6 * Y1 * Y4 * 2.0 + X2 * Y5 * Y6 * 2.0 + X5 * Y2 * Y6 * 2.0 - X5 * Y3 * Y5 * 2.0 + X6 * Y2 * Y5 * 2.0 + X6 * Y3 * Y6 * 2.0 + X1 * Y7 * Y9 * 2.0 + X7 * Y1 * Y9 * 2.0 - X7 * Y3 * Y7 * 2.0 + X9 * Y1 * Y7 * 2.0 + X2 * Y8 * Y9 * 2.0 + X8 * Y2 * Y9 * 2.0 - X8 * Y3 * Y8 * 2.0 + X9 * Y2 * Y8 * 2.0 + X9 * Y3 * Y9 * 2.0;
    G0[2 + 4 * 9] = X1 * Y1 * Z3 * 2.0 + X1 * Y3 * Z1 * 2.0 + X3 * Y1 * Z1 * 2.0 + X2 * Y2 * Z3 * 2.0 + X2 * Y3 * Z2 * 2.0 + X3 * Y2 * Z2 * 2.0 + X3 * Y3 * Z3 * 6.0 + X1 * Y4 * Z6 * 2.0 + X1 * Y6 * Z4 * 2.0 - X3 * Y4 * Z4 * 2.0 + X4 * Y1 * Z6 * 2.0 - X4 * Y3 * Z4 * 2.0 - X4 * Y4 * Z3 * 2.0 + X4 * Y6 * Z1 * 2.0 + X6 * Y1 * Z4 * 2.0 + X6 * Y4 * Z1 * 2.0 + X2 * Y5 * Z6 * 2.0 + X2 * Y6 * Z5 * 2.0 - X3 * Y5 * Z5 * 2.0 + X5 * Y2 * Z6 * 2.0 - X5 * Y3 * Z5 * 2.0 - X5 * Y5 * Z3 * 2.0 + X5 * Y6 * Z2 * 2.0 + X6 * Y2 * Z5 * 2.0 + X6 * Y5 * Z2 * 2.0 + X3 * Y6 * Z6 * 2.0 + X6 * Y3 * Z6 * 2.0 + X6 * Y6 * Z3 * 2.0 + X1 * Y7 * Z9 * 2.0 + X1 * Y9 * Z7 * 2.0 - X3 * Y7 * Z7 * 2.0 + X7 * Y1 * Z9 * 2.0 - X7 * Y3 * Z7 * 2.0 - X7 * Y7 * Z3 * 2.0 + X7 * Y9 * Z1 * 2.0 + X9 * Y1 * Z7 * 2.0 + X9 * Y7 * Z1 * 2.0 + X2 * Y8 * Z9 * 2.0 + X2 * Y9 * Z8 * 2.0 - X3 * Y8 * Z8 * 2.0 + X8 * Y2 * Z9 * 2.0 - X8 * Y3 * Z8 * 2.0 - X8 * Y8 * Z3 * 2.0 + X8 * Y9 * Z2 * 2.0 + X9 * Y2 * Z8 * 2.0 + X9 * Y8 * Z2 * 2.0 + X3 * Y9 * Z9 * 2.0 + X9 * Y3 * Z9 * 2.0 + X9 * Y9 * Z3 * 2.0;
    G0[2 + 5 * 9] = X3 * t20 + X3 * t21 + X3 * t22 * 3.0 - X3 * t23 - X3 * t24 + X3 * t25 - X3 * t26 - X3 * t27 + X3 * t28 + X1 * Z1 * Z3 * 2.0 + X2 * Z2 * Z3 * 2.0 + X1 * Z4 * Z6 * 2.0 + X4 * Z1 * Z6 * 2.0 - X4 * Z3 * Z4 * 2.0 + X6 * Z1 * Z4 * 2.0 + X2 * Z5 * Z6 * 2.0 + X5 * Z2 * Z6 * 2.0 - X5 * Z3 * Z5 * 2.0 + X6 * Z2 * Z5 * 2.0 + X6 * Z3 * Z6 * 2.0 + X1 * Z7 * Z9 * 2.0 + X7 * Z1 * Z9 * 2.0 - X7 * Z3 * Z7 * 2.0 + X9 * Z1 * Z7 * 2.0 + X2 * Z8 * Z9 * 2.0 + X8 * Z2 * Z9 * 2.0 - X8 * Z3 * Z8 * 2.0 + X9 * Z2 * Z8 * 2.0 + X9 * Z3 * Z9 * 2.0;
    G0[2 + 6 * 9] = Y3 * t11 + Y3 * t12 - Y3 * t13 - Y3 * t14 + Y3 * t15 - Y3 * t16 - Y3 * t17 + Y3 * t18 + Y3 * t19 + Y1 * Y4 * Y6 * 2.0 + Y2 * Y5 * Y6 * 2.0 + Y1 * Y7 * Y9 * 2.0 + Y2 * Y8 * Y9 * 2.0;
    G0[2 + 7 * 9] = Z3 * t11 + Z3 * t12 * 3.0 - Z3 * t13 - Z3 * t14 + Z3 * t15 - Z3 * t16 - Z3 * t17 + Z3 * t18 + Z3 * t19 + Y1 * Y3 * Z1 * 2.0 + Y2 * Y3 * Z2 * 2.0 + Y1 * Y4 * Z6 * 2.0 + Y1 * Y6 * Z4 * 2.0 - Y3 * Y4 * Z4 * 2.0 + Y4 * Y6 * Z1 * 2.0 + Y2 * Y5 * Z6 * 2.0 + Y2 * Y6 * Z5 * 2.0 - Y3 * Y5 * Z5 * 2.0 + Y5 * Y6 * Z2 * 2.0 + Y3 * Y6 * Z6 * 2.0 + Y1 * Y7 * Z9 * 2.0 + Y1 * Y9 * Z7 * 2.0 - Y3 * Y7 * Z7 * 2.0 + Y7 * Y9 * Z1 * 2.0 + Y2 * Y8 * Z9 * 2.0 + Y2 * Y9 * Z8 * 2.0 - Y3 * Y8 * Z8 * 2.0 + Y8 * Y9 * Z2 * 2.0 + Y3 * Y9 * Z9 * 2.0;
    G0[2 + 8 * 9] = Y3 * t20 + Y3 * t21 + Y3 * t22 * 3.0 - Y3 * t23 - Y3 * t24 + Y3 * t25 - Y3 * t26 - Y3 * t27 + Y3 * t28 + Y1 * Z1 * Z3 * 2.0 + Y2 * Z2 * Z3 * 2.0 + Y1 * Z4 * Z6 * 2.0 + Y4 * Z1 * Z6 * 2.0 - Y4 * Z3 * Z4 * 2.0 + Y6 * Z1 * Z4 * 2.0 + Y2 * Z5 * Z6 * 2.0 + Y5 * Z2 * Z6 * 2.0 - Y5 * Z3 * Z5 * 2.0 + Y6 * Z2 * Z5 * 2.0 + Y6 * Z3 * Z6 * 2.0 + Y1 * Z7 * Z9 * 2.0 + Y7 * Z1 * Z9 * 2.0 - Y7 * Z3 * Z7 * 2.0 + Y9 * Z1 * Z7 * 2.0 + Y2 * Z8 * Z9 * 2.0 + Y8 * Z2 * Z9 * 2.0 - Y8 * Z3 * Z8 * 2.0 + Y9 * Z2 * Z8 * 2.0 + Y9 * Z3 * Z9 * 2.0;
    G0[2 + 9 * 9] = Z3 * t20 + Z3 * t21 + Z3 * t22 - Z3 * t23 - Z3 * t24 + Z3 * t25 - Z3 * t26 - Z3 * t27 + Z3 * t28 + Z1 * Z4 * Z6 * 2.0 + Z2 * Z5 * Z6 * 2.0 + Z1 * Z7 * Z9 * 2.0 + Z2 * Z8 * Z9 * 2.0;

    G0[3 + 0 * 9] = X4 * t2 - X4 * t3 - X4 * t4 + X4 * t5 + X4 * t6 + X4 * t7 + X4 * t8 - X4 * t9 - X4 * t10 + X1 * X2 * X5 * 2.0 + X1 * X3 * X6 * 2.0 + X5 * X7 * X8 * 2.0 + X6 * X7 * X9 * 2.0;
    G0[3 + 1 * 9] = Y4 * t2 - Y4 * t3 - Y4 * t4 + Y4 * t5 * 3.0 + Y4 * t6 + Y4 * t7 + Y4 * t8 - Y4 * t9 - Y4 * t10 + X1 * X4 * Y1 * 2.0 + X1 * X2 * Y5 * 2.0 + X1 * X5 * Y2 * 2.0 - X2 * X4 * Y2 * 2.0 + X2 * X5 * Y1 * 2.0 + X1 * X3 * Y6 * 2.0 + X1 * X6 * Y3 * 2.0 - X3 * X4 * Y3 * 2.0 + X3 * X6 * Y1 * 2.0 + X4 * X5 * Y5 * 2.0 + X4 * X6 * Y6 * 2.0 + X4 * X7 * Y7 * 2.0 - X4 * X8 * Y8 * 2.0 + X5 * X7 * Y8 * 2.0 + X5 * X8 * Y7 * 2.0 + X7 * X8 * Y5 * 2.0 - X4 * X9 * Y9 * 2.0 + X6 * X7 * Y9 * 2.0 + X6 * X9 * Y7 * 2.0 + X7 * X9 * Y6 * 2.0;
    G0[3 + 2 * 9] = Z4 * t2 - Z4 * t3 - Z4 * t4 + Z4 * t5 * 3.0 + Z4 * t6 + Z4 * t7 + Z4 * t8 - Z4 * t9 - Z4 * t10 + X1 * X4 * Z1 * 2.0 + X1 * X2 * Z5 * 2.0 + X1 * X5 * Z2 * 2.0 - X2 * X4 * Z2 * 2.0 + X2 * X5 * Z1 * 2.0 + X1 * X3 * Z6 * 2.0 + X1 * X6 * Z3 * 2.0 - X3 * X4 * Z3 * 2.0 + X3 * X6 * Z1 * 2.0 + X4 * X5 * Z5 * 2.0 + X4 * X6 * Z6 * 2.0 + X4 * X7 * Z7 * 2.0 - X4 * X8 * Z8 * 2.0 + X5 * X7 * Z8 * 2.0 + X5 * X8 * Z7 * 2.0 + X7 * X8 * Z5 * 2.0 - X4 * X9 * Z9 * 2.0 + X6 * X7 * Z9 * 2.0 + X6 * X9 * Z7 * 2.0 + X7 * X9 * Z6 * 2.0;
    G0[3 + 3 * 9] = -X4 * t11 - X4 * t12 + X4 * t13 * 3.0 + X4 * t14 + X4 * t15 + X4 * t16 - X4 * t17 - X4 * t18 + X4 * t19 + X1 * Y1 * Y4 * 2.0 + X1 * Y2 * Y5 * 2.0 + X2 * Y1 * Y5 * 2.0 - X2 * Y2 * Y4 * 2.0 + X5 * Y1 * Y2 * 2.0 + X1 * Y3 * Y6 * 2.0 + X3 * Y1 * Y6 * 2.0 - X3 * Y3 * Y4 * 2.0 + X6 * Y1 * Y3 * 2.0 + X5 * Y4 * Y5 * 2.0 + X6 * Y4 * Y6 * 2.0 + X7 * Y4 * Y7 * 2.0 + X5 * Y7 * Y8 * 2.0 + X7 * Y5 * Y8 * 2.0 - X8 * Y4 * Y8 * 2.0 + X8 * Y5 * Y7 * 2.0 + X6 * Y7 * Y9 * 2.0 + X7 * Y6 * Y9 * 2.0 - X9 * Y4 * Y9 * 2.0 + X9 * Y6 * Y7 * 2.0;
    G0[3 + 4 * 9] = X1 * Y1 * Z4 * 2.0 + X1 * Y4 * Z1 * 2.0 + X4 * Y1 * Z1 * 2.0 + X1 * Y2 * Z5 * 2.0 + X1 * Y5 * Z2 * 2.0 + X2 * Y1 * Z5 * 2.0 - X2 * Y2 * Z4 * 2.0 - X2 * Y4 * Z2 * 2.0 + X2 * Y5 * Z1 * 2.0 - X4 * Y2 * Z2 * 2.0 + X5 * Y1 * Z2 * 2.0 + X5 * Y2 * Z1 * 2.0 + X1 * Y3 * Z6 * 2.0 + X1 * Y6 * Z3 * 2.0 + X3 * Y1 * Z6 * 2.0 - X3 * Y3 * Z4 * 2.0 - X3 * Y4 * Z3 * 2.0 + X3 * Y6 * Z1 * 2.0 - X4 * Y3 * Z3 * 2.0 + X6 * Y1 * Z3 * 2.0 + X6 * Y3 * Z1 * 2.0 + X4 * Y4 * Z4 * 6.0 + X4 * Y5 * Z5 * 2.0 + X5 * Y4 * Z5 * 2.0 + X5 * Y5 * Z4 * 2.0 + X4 * Y6 * Z6 * 2.0 + X6 * Y4 * Z6 * 2.0 + X6 * Y6 * Z4 * 2.0 + X4 * Y7 * Z7 * 2.0 + X7 * Y4 * Z7 * 2.0 + X7 * Y7 * Z4 * 2.0 - X4 * Y8 * Z8 * 2.0 + X5 * Y7 * Z8 * 2.0 + X5 * Y8 * Z7 * 2.0 + X7 * Y5 * Z8 * 2.0 + X7 * Y8 * Z5 * 2.0 - X8 * Y4 * Z8 * 2.0 + X8 * Y5 * Z7 * 2.0 + X8 * Y7 * Z5 * 2.0 - X8 * Y8 * Z4 * 2.0 - X4 * Y9 * Z9 * 2.0 + X6 * Y7 * Z9 * 2.0 + X6 * Y9 * Z7 * 2.0 + X7 * Y6 * Z9 * 2.0 + X7 * Y9 * Z6 * 2.0 - X9 * Y4 * Z9 * 2.0 + X9 * Y6 * Z7 * 2.0 + X9 * Y7 * Z6 * 2.0 - X9 * Y9 * Z4 * 2.0;
    G0[3 + 5 * 9] = X4 * t20 - X4 * t21 - X4 * t22 + X4 * t23 * 3.0 + X4 * t24 + X4 * t25 + X4 * t26 - X4 * t27 - X4 * t28 + X1 * Z1 * Z4 * 2.0 + X1 * Z2 * Z5 * 2.0 + X2 * Z1 * Z5 * 2.0 - X2 * Z2 * Z4 * 2.0 + X5 * Z1 * Z2 * 2.0 + X1 * Z3 * Z6 * 2.0 + X3 * Z1 * Z6 * 2.0 - X3 * Z3 * Z4 * 2.0 + X6 * Z1 * Z3 * 2.0 + X5 * Z4 * Z5 * 2.0 + X6 * Z4 * Z6 * 2.0 + X7 * Z4 * Z7 * 2.0 + X5 * Z7 * Z8 * 2.0 + X7 * Z5 * Z8 * 2.0 - X8 * Z4 * Z8 * 2.0 + X8 * Z5 * Z7 * 2.0 + X6 * Z7 * Z9 * 2.0 + X7 * Z6 * Z9 * 2.0 - X9 * Z4 * Z9 * 2.0 + X9 * Z6 * Z7 * 2.0;
    G0[3 + 6 * 9] = -Y4 * t11 - Y4 * t12 + Y4 * t13 + Y4 * t14 + Y4 * t15 + Y4 * t16 - Y4 * t17 - Y4 * t18 + Y4 * t19 + Y1 * Y2 * Y5 * 2.0 + Y1 * Y3 * Y6 * 2.0 + Y5 * Y7 * Y8 * 2.0 + Y6 * Y7 * Y9 * 2.0;
    G0[3 + 7 * 9] = -Z4 * t11 - Z4 * t12 + Z4 * t13 * 3.0 + Z4 * t14 + Z4 * t15 + Z4 * t16 - Z4 * t17 - Z4 * t18 + Z4 * t19 + Y1 * Y4 * Z1 * 2.0 + Y1 * Y2 * Z5 * 2.0 + Y1 * Y5 * Z2 * 2.0 - Y2 * Y4 * Z2 * 2.0 + Y2 * Y5 * Z1 * 2.0 + Y1 * Y3 * Z6 * 2.0 + Y1 * Y6 * Z3 * 2.0 - Y3 * Y4 * Z3 * 2.0 + Y3 * Y6 * Z1 * 2.0 + Y4 * Y5 * Z5 * 2.0 + Y4 * Y6 * Z6 * 2.0 + Y4 * Y7 * Z7 * 2.0 - Y4 * Y8 * Z8 * 2.0 + Y5 * Y7 * Z8 * 2.0 + Y5 * Y8 * Z7 * 2.0 + Y7 * Y8 * Z5 * 2.0 - Y4 * Y9 * Z9 * 2.0 + Y6 * Y7 * Z9 * 2.0 + Y6 * Y9 * Z7 * 2.0 + Y7 * Y9 * Z6 * 2.0;
    G0[3 + 8 * 9] = Y4 * t20 - Y4 * t21 - Y4 * t22 + Y4 * t23 * 3.0 + Y4 * t24 + Y4 * t25 + Y4 * t26 - Y4 * t27 - Y4 * t28 + Y1 * Z1 * Z4 * 2.0 + Y1 * Z2 * Z5 * 2.0 + Y2 * Z1 * Z5 * 2.0 - Y2 * Z2 * Z4 * 2.0 + Y5 * Z1 * Z2 * 2.0 + Y1 * Z3 * Z6 * 2.0 + Y3 * Z1 * Z6 * 2.0 - Y3 * Z3 * Z4 * 2.0 + Y6 * Z1 * Z3 * 2.0 + Y5 * Z4 * Z5 * 2.0 + Y6 * Z4 * Z6 * 2.0 + Y7 * Z4 * Z7 * 2.0 + Y5 * Z7 * Z8 * 2.0 + Y7 * Z5 * Z8 * 2.0 - Y8 * Z4 * Z8 * 2.0 + Y8 * Z5 * Z7 * 2.0 + Y6 * Z7 * Z9 * 2.0 + Y7 * Z6 * Z9 * 2.0 - Y9 * Z4 * Z9 * 2.0 + Y9 * Z6 * Z7 * 2.0;
    G0[3 + 9 * 9] = Z4 * t20 - Z4 * t21 - Z4 * t22 + Z4 * t23 + Z4 * t24 + Z4 * t25 + Z4 * t26 - Z4 * t27 - Z4 * t28 + Z1 * Z2 * Z5 * 2.0 + Z1 * Z3 * Z6 * 2.0 + Z5 * Z7 * Z8 * 2.0 + Z6 * Z7 * Z9 * 2.0;

    G0[4 + 0 * 9] = -X5 * t2 + X5 * t3 - X5 * t4 + X5 * t5 + X5 * t6 + X5 * t7 - X5 * t8 + X5 * t9 - X5 * t10 + X1 * X2 * X4 * 2.0 + X2 * X3 * X6 * 2.0 + X4 * X7 * X8 * 2.0 + X6 * X8 * X9 * 2.0;
    G0[4 + 1 * 9] = -Y5 * t2 + Y5 * t3 - Y5 * t4 + Y5 * t5 + Y5 * t6 * 3.0 + Y5 * t7 - Y5 * t8 + Y5 * t9 - Y5 * t10 + X1 * X2 * Y4 * 2.0 + X1 * X4 * Y2 * 2.0 - X1 * X5 * Y1 * 2.0 + X2 * X4 * Y1 * 2.0 + X2 * X5 * Y2 * 2.0 + X2 * X3 * Y6 * 2.0 + X2 * X6 * Y3 * 2.0 - X3 * X5 * Y3 * 2.0 + X3 * X6 * Y2 * 2.0 + X4 * X5 * Y4 * 2.0 + X5 * X6 * Y6 * 2.0 + X4 * X7 * Y8 * 2.0 + X4 * X8 * Y7 * 2.0 - X5 * X7 * Y7 * 2.0 + X7 * X8 * Y4 * 2.0 + X5 * X8 * Y8 * 2.0 - X5 * X9 * Y9 * 2.0 + X6 * X8 * Y9 * 2.0 + X6 * X9 * Y8 * 2.0 + X8 * X9 * Y6 * 2.0;
    G0[4 + 2 * 9] = -Z5 * t2 + Z5 * t3 - Z5 * t4 + Z5 * t5 + Z5 * t6 * 3.0 + Z5 * t7 - Z5 * t8 + Z5 * t9 - Z5 * t10 + X1 * X2 * Z4 * 2.0 + X1 * X4 * Z2 * 2.0 - X1 * X5 * Z1 * 2.0 + X2 * X4 * Z1 * 2.0 + X2 * X5 * Z2 * 2.0 + X2 * X3 * Z6 * 2.0 + X2 * X6 * Z3 * 2.0 - X3 * X5 * Z3 * 2.0 + X3 * X6 * Z2 * 2.0 + X4 * X5 * Z4 * 2.0 + X5 * X6 * Z6 * 2.0 + X4 * X7 * Z8 * 2.0 + X4 * X8 * Z7 * 2.0 - X5 * X7 * Z7 * 2.0 + X7 * X8 * Z4 * 2.0 + X5 * X8 * Z8 * 2.0 - X5 * X9 * Z9 * 2.0 + X6 * X8 * Z9 * 2.0 + X6 * X9 * Z8 * 2.0 + X8 * X9 * Z6 * 2.0;
    G0[4 + 3 * 9] = X5 * t11 - X5 * t12 + X5 * t13 + X5 * t14 * 3.0 + X5 * t15 - X5 * t16 + X5 * t17 - X5 * t18 - X5 * t19 - X1 * Y1 * Y5 * 2.0 + X1 * Y2 * Y4 * 2.0 + X2 * Y1 * Y4 * 2.0 + X4 * Y1 * Y2 * 2.0 + X2 * Y2 * Y5 * 2.0 + X2 * Y3 * Y6 * 2.0 + X3 * Y2 * Y6 * 2.0 - X3 * Y3 * Y5 * 2.0 + X6 * Y2 * Y3 * 2.0 + X4 * Y4 * Y5 * 2.0 + X6 * Y5 * Y6 * 2.0 + X4 * Y7 * Y8 * 2.0 + X7 * Y4 * Y8 * 2.0 - X7 * Y5 * Y7 * 2.0 + X8 * Y4 * Y7 * 2.0 + X8 * Y5 * Y8 * 2.0 + X6 * Y8 * Y9 * 2.0 + X8 * Y6 * Y9 * 2.0 - X9 * Y5 * Y9 * 2.0 + X9 * Y6 * Y8 * 2.0;
    G0[4 + 4 * 9] = X1 * Y1 * Z5 * -2.0 + X1 * Y2 * Z4 * 2.0 + X1 * Y4 * Z2 * 2.0 - X1 * Y5 * Z1 * 2.0 + X2 * Y1 * Z4 * 2.0 + X2 * Y4 * Z1 * 2.0 + X4 * Y1 * Z2 * 2.0 + X4 * Y2 * Z1 * 2.0 - X5 * Y1 * Z1 * 2.0 + X2 * Y2 * Z5 * 2.0 + X2 * Y5 * Z2 * 2.0 + X5 * Y2 * Z2 * 2.0 + X2 * Y3 * Z6 * 2.0 + X2 * Y6 * Z3 * 2.0 + X3 * Y2 * Z6 * 2.0 - X3 * Y3 * Z5 * 2.0 - X3 * Y5 * Z3 * 2.0 + X3 * Y6 * Z2 * 2.0 - X5 * Y3 * Z3 * 2.0 + X6 * Y2 * Z3 * 2.0 + X6 * Y3 * Z2 * 2.0 + X4 * Y4 * Z5 * 2.0 + X4 * Y5 * Z4 * 2.0 + X5 * Y4 * Z4 * 2.0 + X5 * Y5 * Z5 * 6.0 + X5 * Y6 * Z6 * 2.0 + X6 * Y5 * Z6 * 2.0 + X6 * Y6 * Z5 * 2.0 + X4 * Y7 * Z8 * 2.0 + X4 * Y8 * Z7 * 2.0 - X5 * Y7 * Z7 * 2.0 + X7 * Y4 * Z8 * 2.0 - X7 * Y5 * Z7 * 2.0 - X7 * Y7 * Z5 * 2.0 + X7 * Y8 * Z4 * 2.0 + X8 * Y4 * Z7 * 2.0 + X8 * Y7 * Z4 * 2.0 + X5 * Y8 * Z8 * 2.0 + X8 * Y5 * Z8 * 2.0 + X8 * Y8 * Z5 * 2.0 - X5 * Y9 * Z9 * 2.0 + X6 * Y8 * Z9 * 2.0 + X6 * Y9 * Z8 * 2.0 + X8 * Y6 * Z9 * 2.0 + X8 * Y9 * Z6 * 2.0 - X9 * Y5 * Z9 * 2.0 + X9 * Y6 * Z8 * 2.0 + X9 * Y8 * Z6 * 2.0 - X9 * Y9 * Z5 * 2.0;
    G0[4 + 5 * 9] = -X5 * t20 + X5 * t21 - X5 * t22 + X5 * t23 + X5 * t24 * 3.0 + X5 * t25 - X5 * t26 + X5 * t27 - X5 * t28 - X1 * Z1 * Z5 * 2.0 + X1 * Z2 * Z4 * 2.0 + X2 * Z1 * Z4 * 2.0 + X4 * Z1 * Z2 * 2.0 + X2 * Z2 * Z5 * 2.0 + X2 * Z3 * Z6 * 2.0 + X3 * Z2 * Z6 * 2.0 - X3 * Z3 * Z5 * 2.0 + X6 * Z2 * Z3 * 2.0 + X4 * Z4 * Z5 * 2.0 + X6 * Z5 * Z6 * 2.0 + X4 * Z7 * Z8 * 2.0 + X7 * Z4 * Z8 * 2.0 - X7 * Z5 * Z7 * 2.0 + X8 * Z4 * Z7 * 2.0 + X8 * Z5 * Z8 * 2.0 + X6 * Z8 * Z9 * 2.0 + X8 * Z6 * Z9 * 2.0 - X9 * Z5 * Z9 * 2.0 + X9 * Z6 * Z8 * 2.0;
    G0[4 + 6 * 9] = Y5 * t11 - Y5 * t12 + Y5 * t13 + Y5 * t14 + Y5 * t15 - Y5 * t16 + Y5 * t17 - Y5 * t18 - Y5 * t19 + Y1 * Y2 * Y4 * 2.0 + Y2 * Y3 * Y6 * 2.0 + Y4 * Y7 * Y8 * 2.0 + Y6 * Y8 * Y9 * 2.0;
    G0[4 + 7 * 9] = Z5 * t11 - Z5 * t12 + Z5 * t13 + Z5 * t14 * 3.0 + Z5 * t15 - Z5 * t16 + Z5 * t17 - Z5 * t18 - Z5 * t19 + Y1 * Y2 * Z4 * 2.0 + Y1 * Y4 * Z2 * 2.0 - Y1 * Y5 * Z1 * 2.0 + Y2 * Y4 * Z1 * 2.0 + Y2 * Y5 * Z2 * 2.0 + Y2 * Y3 * Z6 * 2.0 + Y2 * Y6 * Z3 * 2.0 - Y3 * Y5 * Z3 * 2.0 + Y3 * Y6 * Z2 * 2.0 + Y4 * Y5 * Z4 * 2.0 + Y5 * Y6 * Z6 * 2.0 + Y4 * Y7 * Z8 * 2.0 + Y4 * Y8 * Z7 * 2.0 - Y5 * Y7 * Z7 * 2.0 + Y7 * Y8 * Z4 * 2.0 + Y5 * Y8 * Z8 * 2.0 - Y5 * Y9 * Z9 * 2.0 + Y6 * Y8 * Z9 * 2.0 + Y6 * Y9 * Z8 * 2.0 + Y8 * Y9 * Z6 * 2.0;
    G0[4 + 8 * 9] = -Y5 * t20 + Y5 * t21 - Y5 * t22 + Y5 * t23 + Y5 * t24 * 3.0 + Y5 * t25 - Y5 * t26 + Y5 * t27 - Y5 * t28 - Y1 * Z1 * Z5 * 2.0 + Y1 * Z2 * Z4 * 2.0 + Y2 * Z1 * Z4 * 2.0 + Y4 * Z1 * Z2 * 2.0 + Y2 * Z2 * Z5 * 2.0 + Y2 * Z3 * Z6 * 2.0 + Y3 * Z2 * Z6 * 2.0 - Y3 * Z3 * Z5 * 2.0 + Y6 * Z2 * Z3 * 2.0 + Y4 * Z4 * Z5 * 2.0 + Y6 * Z5 * Z6 * 2.0 + Y4 * Z7 * Z8 * 2.0 + Y7 * Z4 * Z8 * 2.0 - Y7 * Z5 * Z7 * 2.0 + Y8 * Z4 * Z7 * 2.0 + Y8 * Z5 * Z8 * 2.0 + Y6 * Z8 * Z9 * 2.0 + Y8 * Z6 * Z9 * 2.0 - Y9 * Z5 * Z9 * 2.0 + Y9 * Z6 * Z8 * 2.0;
    G0[4 + 9 * 9] = -Z5 * t20 + Z5 * t21 - Z5 * t22 + Z5 * t23 + Z5 * t24 + Z5 * t25 - Z5 * t26 + Z5 * t27 - Z5 * t28 + Z1 * Z2 * Z4 * 2.0 + Z2 * Z3 * Z6 * 2.0 + Z4 * Z7 * Z8 * 2.0 + Z6 * Z8 * Z9 * 2.0;

    G0[5 + 0 * 9] = -X6 * t2 - X6 * t3 + X6 * t4 + X6 * t5 + X6 * t6 + X6 * t7 - X6 * t8 - X6 * t9 + X6 * t10 + X1 * X3 * X4 * 2.0 + X2 * X3 * X5 * 2.0 + X4 * X7 * X9 * 2.0 + X5 * X8 * X9 * 2.0;
    G0[5 + 1 * 9] = -Y6 * t2 - Y6 * t3 + Y6 * t4 + Y6 * t5 + Y6 * t6 + Y6 * t7 * 3.0 - Y6 * t8 - Y6 * t9 + Y6 * t10 + X1 * X3 * Y4 * 2.0 + X1 * X4 * Y3 * 2.0 - X1 * X6 * Y1 * 2.0 + X3 * X4 * Y1 * 2.0 + X2 * X3 * Y5 * 2.0 + X2 * X5 * Y3 * 2.0 - X2 * X6 * Y2 * 2.0 + X3 * X5 * Y2 * 2.0 + X3 * X6 * Y3 * 2.0 + X4 * X6 * Y4 * 2.0 + X5 * X6 * Y5 * 2.0 + X4 * X7 * Y9 * 2.0 + X4 * X9 * Y7 * 2.0 - X6 * X7 * Y7 * 2.0 + X7 * X9 * Y4 * 2.0 + X5 * X8 * Y9 * 2.0 + X5 * X9 * Y8 * 2.0 - X6 * X8 * Y8 * 2.0 + X8 * X9 * Y5 * 2.0 + X6 * X9 * Y9 * 2.0;
    G0[5 + 2 * 9] = -Z6 * t2 - Z6 * t3 + Z6 * t4 + Z6 * t5 + Z6 * t6 + Z6 * t7 * 3.0 - Z6 * t8 - Z6 * t9 + Z6 * t10 + X1 * X3 * Z4 * 2.0 + X1 * X4 * Z3 * 2.0 - X1 * X6 * Z1 * 2.0 + X3 * X4 * Z1 * 2.0 + X2 * X3 * Z5 * 2.0 + X2 * X5 * Z3 * 2.0 - X2 * X6 * Z2 * 2.0 + X3 * X5 * Z2 * 2.0 + X3 * X6 * Z3 * 2.0 + X4 * X6 * Z4 * 2.0 + X5 * X6 * Z5 * 2.0 + X4 * X7 * Z9 * 2.0 + X4 * X9 * Z7 * 2.0 - X6 * X7 * Z7 * 2.0 + X7 * X9 * Z4 * 2.0 + X5 * X8 * Z9 * 2.0 + X5 * X9 * Z8 * 2.0 - X6 * X8 * Z8 * 2.0 + X8 * X9 * Z5 * 2.0 + X6 * X9 * Z9 * 2.0;
    G0[5 + 3 * 9] = -X6 * t11 + X6 * t12 + X6 * t13 + X6 * t14 + X6 * t15 * 3.0 - X6 * t16 - X6 * t17 + X6 * t18 - X6 * t19 - X1 * Y1 * Y6 * 2.0 + X1 * Y3 * Y4 * 2.0 + X3 * Y1 * Y4 * 2.0 + X4 * Y1 * Y3 * 2.0 - X2 * Y2 * Y6 * 2.0 + X2 * Y3 * Y5 * 2.0 + X3 * Y2 * Y5 * 2.0 + X5 * Y2 * Y3 * 2.0 + X3 * Y3 * Y6 * 2.0 + X4 * Y4 * Y6 * 2.0 + X5 * Y5 * Y6 * 2.0 + X4 * Y7 * Y9 * 2.0 + X7 * Y4 * Y9 * 2.0 - X7 * Y6 * Y7 * 2.0 + X9 * Y4 * Y7 * 2.0 + X5 * Y8 * Y9 * 2.0 + X8 * Y5 * Y9 * 2.0 - X8 * Y6 * Y8 * 2.0 + X9 * Y5 * Y8 * 2.0 + X9 * Y6 * Y9 * 2.0;
    G0[5 + 4 * 9] = X1 * Y1 * Z6 * -2.0 + X1 * Y3 * Z4 * 2.0 + X1 * Y4 * Z3 * 2.0 - X1 * Y6 * Z1 * 2.0 + X3 * Y1 * Z4 * 2.0 + X3 * Y4 * Z1 * 2.0 + X4 * Y1 * Z3 * 2.0 + X4 * Y3 * Z1 * 2.0 - X6 * Y1 * Z1 * 2.0 - X2 * Y2 * Z6 * 2.0 + X2 * Y3 * Z5 * 2.0 + X2 * Y5 * Z3 * 2.0 - X2 * Y6 * Z2 * 2.0 + X3 * Y2 * Z5 * 2.0 + X3 * Y5 * Z2 * 2.0 + X5 * Y2 * Z3 * 2.0 + X5 * Y3 * Z2 * 2.0 - X6 * Y2 * Z2 * 2.0 + X3 * Y3 * Z6 * 2.0 + X3 * Y6 * Z3 * 2.0 + X6 * Y3 * Z3 * 2.0 + X4 * Y4 * Z6 * 2.0 + X4 * Y6 * Z4 * 2.0 + X6 * Y4 * Z4 * 2.0 + X5 * Y5 * Z6 * 2.0 + X5 * Y6 * Z5 * 2.0 + X6 * Y5 * Z5 * 2.0 + X6 * Y6 * Z6 * 6.0 + X4 * Y7 * Z9 * 2.0 + X4 * Y9 * Z7 * 2.0 - X6 * Y7 * Z7 * 2.0 + X7 * Y4 * Z9 * 2.0 - X7 * Y6 * Z7 * 2.0 - X7 * Y7 * Z6 * 2.0 + X7 * Y9 * Z4 * 2.0 + X9 * Y4 * Z7 * 2.0 + X9 * Y7 * Z4 * 2.0 + X5 * Y8 * Z9 * 2.0 + X5 * Y9 * Z8 * 2.0 - X6 * Y8 * Z8 * 2.0 + X8 * Y5 * Z9 * 2.0 - X8 * Y6 * Z8 * 2.0 - X8 * Y8 * Z6 * 2.0 + X8 * Y9 * Z5 * 2.0 + X9 * Y5 * Z8 * 2.0 + X9 * Y8 * Z5 * 2.0 + X6 * Y9 * Z9 * 2.0 + X9 * Y6 * Z9 * 2.0 + X9 * Y9 * Z6 * 2.0;
    G0[5 + 5 * 9] = -X6 * t20 - X6 * t21 + X6 * t22 + X6 * t23 + X6 * t24 + X6 * t25 * 3.0 - X6 * t26 - X6 * t27 + X6 * t28 - X1 * Z1 * Z6 * 2.0 + X1 * Z3 * Z4 * 2.0 + X3 * Z1 * Z4 * 2.0 + X4 * Z1 * Z3 * 2.0 - X2 * Z2 * Z6 * 2.0 + X2 * Z3 * Z5 * 2.0 + X3 * Z2 * Z5 * 2.0 + X5 * Z2 * Z3 * 2.0 + X3 * Z3 * Z6 * 2.0 + X4 * Z4 * Z6 * 2.0 + X5 * Z5 * Z6 * 2.0 + X4 * Z7 * Z9 * 2.0 + X7 * Z4 * Z9 * 2.0 - X7 * Z6 * Z7 * 2.0 + X9 * Z4 * Z7 * 2.0 + X5 * Z8 * Z9 * 2.0 + X8 * Z5 * Z9 * 2.0 - X8 * Z6 * Z8 * 2.0 + X9 * Z5 * Z8 * 2.0 + X9 * Z6 * Z9 * 2.0;
    G0[5 + 6 * 9] = -Y6 * t11 + Y6 * t12 + Y6 * t13 + Y6 * t14 + Y6 * t15 - Y6 * t16 - Y6 * t17 + Y6 * t18 - Y6 * t19 + Y1 * Y3 * Y4 * 2.0 + Y2 * Y3 * Y5 * 2.0 + Y4 * Y7 * Y9 * 2.0 + Y5 * Y8 * Y9 * 2.0;
    G0[5 + 7 * 9] = -Z6 * t11 + Z6 * t12 + Z6 * t13 + Z6 * t14 + Z6 * t15 * 3.0 - Z6 * t16 - Z6 * t17 + Z6 * t18 - Z6 * t19 + Y1 * Y3 * Z4 * 2.0 + Y1 * Y4 * Z3 * 2.0 - Y1 * Y6 * Z1 * 2.0 + Y3 * Y4 * Z1 * 2.0 + Y2 * Y3 * Z5 * 2.0 + Y2 * Y5 * Z3 * 2.0 - Y2 * Y6 * Z2 * 2.0 + Y3 * Y5 * Z2 * 2.0 + Y3 * Y6 * Z3 * 2.0 + Y4 * Y6 * Z4 * 2.0 + Y5 * Y6 * Z5 * 2.0 + Y4 * Y7 * Z9 * 2.0 + Y4 * Y9 * Z7 * 2.0 - Y6 * Y7 * Z7 * 2.0 + Y7 * Y9 * Z4 * 2.0 + Y5 * Y8 * Z9 * 2.0 + Y5 * Y9 * Z8 * 2.0 - Y6 * Y8 * Z8 * 2.0 + Y8 * Y9 * Z5 * 2.0 + Y6 * Y9 * Z9 * 2.0;
    G0[5 + 8 * 9] = -Y6 * t20 - Y6 * t21 + Y6 * t22 + Y6 * t23 + Y6 * t24 + Y6 * t25 * 3.0 - Y6 * t26 - Y6 * t27 + Y6 * t28 - Y1 * Z1 * Z6 * 2.0 + Y1 * Z3 * Z4 * 2.0 + Y3 * Z1 * Z4 * 2.0 + Y4 * Z1 * Z3 * 2.0 - Y2 * Z2 * Z6 * 2.0 + Y2 * Z3 * Z5 * 2.0 + Y3 * Z2 * Z5 * 2.0 + Y5 * Z2 * Z3 * 2.0 + Y3 * Z3 * Z6 * 2.0 + Y4 * Z4 * Z6 * 2.0 + Y5 * Z5 * Z6 * 2.0 + Y4 * Z7 * Z9 * 2.0 + Y7 * Z4 * Z9 * 2.0 - Y7 * Z6 * Z7 * 2.0 + Y9 * Z4 * Z7 * 2.0 + Y5 * Z8 * Z9 * 2.0 + Y8 * Z5 * Z9 * 2.0 - Y8 * Z6 * Z8 * 2.0 + Y9 * Z5 * Z8 * 2.0 + Y9 * Z6 * Z9 * 2.0;
    G0[5 + 9 * 9] = -Z6 * t20 - Z6 * t21 + Z6 * t22 + Z6 * t23 + Z6 * t24 + Z6 * t25 - Z6 * t26 - Z6 * t27 + Z6 * t28 + Z1 * Z3 * Z4 * 2.0 + Z2 * Z3 * Z5 * 2.0 + Z4 * Z7 * Z9 * 2.0 + Z5 * Z8 * Z9 * 2.0;

    G0[6 + 0 * 9] = X7 * t2 - X7 * t3 - X7 * t4 + X7 * t5 - X7 * t6 - X7 * t7 + X7 * t8 + X7 * t9 + X7 * t10 + X1 * X2 * X8 * 2.0 + X1 * X3 * X9 * 2.0 + X4 * X5 * X8 * 2.0 + X4 * X6 * X9 * 2.0;
    G0[6 + 1 * 9] = Y7 * t2 - Y7 * t3 - Y7 * t4 + Y7 * t5 - Y7 * t6 - Y7 * t7 + Y7 * t8 * 3.0 + Y7 * t9 + Y7 * t10 + X1 * X7 * Y1 * 2.0 + X1 * X2 * Y8 * 2.0 + X1 * X8 * Y2 * 2.0 - X2 * X7 * Y2 * 2.0 + X2 * X8 * Y1 * 2.0 + X1 * X3 * Y9 * 2.0 + X1 * X9 * Y3 * 2.0 - X3 * X7 * Y3 * 2.0 + X3 * X9 * Y1 * 2.0 + X4 * X7 * Y4 * 2.0 + X4 * X5 * Y8 * 2.0 + X4 * X8 * Y5 * 2.0 - X5 * X7 * Y5 * 2.0 + X5 * X8 * Y4 * 2.0 + X4 * X6 * Y9 * 2.0 + X4 * X9 * Y6 * 2.0 - X6 * X7 * Y6 * 2.0 + X6 * X9 * Y4 * 2.0 + X7 * X8 * Y8 * 2.0 + X7 * X9 * Y9 * 2.0;
    G0[6 + 2 * 9] = Z7 * t2 - Z7 * t3 - Z7 * t4 + Z7 * t5 - Z7 * t6 - Z7 * t7 + Z7 * t8 * 3.0 + Z7 * t9 + Z7 * t10 + X1 * X7 * Z1 * 2.0 + X1 * X2 * Z8 * 2.0 + X1 * X8 * Z2 * 2.0 - X2 * X7 * Z2 * 2.0 + X2 * X8 * Z1 * 2.0 + X1 * X3 * Z9 * 2.0 + X1 * X9 * Z3 * 2.0 - X3 * X7 * Z3 * 2.0 + X3 * X9 * Z1 * 2.0 + X4 * X7 * Z4 * 2.0 + X4 * X5 * Z8 * 2.0 + X4 * X8 * Z5 * 2.0 - X5 * X7 * Z5 * 2.0 + X5 * X8 * Z4 * 2.0 + X4 * X6 * Z9 * 2.0 + X4 * X9 * Z6 * 2.0 - X6 * X7 * Z6 * 2.0 + X6 * X9 * Z4 * 2.0 + X7 * X8 * Z8 * 2.0 + X7 * X9 * Z9 * 2.0;
    G0[6 + 3 * 9] = -X7 * t11 - X7 * t12 + X7 * t13 - X7 * t14 - X7 * t15 + X7 * t16 * 3.0 + X7 * t17 + X7 * t18 + X7 * t19 + X1 * Y1 * Y7 * 2.0 + X1 * Y2 * Y8 * 2.0 + X2 * Y1 * Y8 * 2.0 - X2 * Y2 * Y7 * 2.0 + X8 * Y1 * Y2 * 2.0 + X1 * Y3 * Y9 * 2.0 + X3 * Y1 * Y9 * 2.0 - X3 * Y3 * Y7 * 2.0 + X9 * Y1 * Y3 * 2.0 + X4 * Y4 * Y7 * 2.0 + X4 * Y5 * Y8 * 2.0 + X5 * Y4 * Y8 * 2.0 - X5 * Y5 * Y7 * 2.0 + X8 * Y4 * Y5 * 2.0 + X4 * Y6 * Y9 * 2.0 + X6 * Y4 * Y9 * 2.0 - X6 * Y6 * Y7 * 2.0 + X9 * Y4 * Y6 * 2.0 + X8 * Y7 * Y8 * 2.0 + X9 * Y7 * Y9 * 2.0;
    G0[6 + 4 * 9] = X1 * Y1 * Z7 * 2.0 + X1 * Y7 * Z1 * 2.0 + X7 * Y1 * Z1 * 2.0 + X1 * Y2 * Z8 * 2.0 + X1 * Y8 * Z2 * 2.0 + X2 * Y1 * Z8 * 2.0 - X2 * Y2 * Z7 * 2.0 - X2 * Y7 * Z2 * 2.0 + X2 * Y8 * Z1 * 2.0 - X7 * Y2 * Z2 * 2.0 + X8 * Y1 * Z2 * 2.0 + X8 * Y2 * Z1 * 2.0 + X1 * Y3 * Z9 * 2.0 + X1 * Y9 * Z3 * 2.0 + X3 * Y1 * Z9 * 2.0 - X3 * Y3 * Z7 * 2.0 - X3 * Y7 * Z3 * 2.0 + X3 * Y9 * Z1 * 2.0 - X7 * Y3 * Z3 * 2.0 + X9 * Y1 * Z3 * 2.0 + X9 * Y3 * Z1 * 2.0 + X4 * Y4 * Z7 * 2.0 + X4 * Y7 * Z4 * 2.0 + X7 * Y4 * Z4 * 2.0 + X4 * Y5 * Z8 * 2.0 + X4 * Y8 * Z5 * 2.0 + X5 * Y4 * Z8 * 2.0 - X5 * Y5 * Z7 * 2.0 - X5 * Y7 * Z5 * 2.0 + X5 * Y8 * Z4 * 2.0 - X7 * Y5 * Z5 * 2.0 + X8 * Y4 * Z5 * 2.0 + X8 * Y5 * Z4 * 2.0 + X4 * Y6 * Z9 * 2.0 + X4 * Y9 * Z6 * 2.0 + X6 * Y4 * Z9 * 2.0 - X6 * Y6 * Z7 * 2.0 - X6 * Y7 * Z6 * 2.0 + X6 * Y9 * Z4 * 2.0 - X7 * Y6 * Z6 * 2.0 + X9 * Y4 * Z6 * 2.0 + X9 * Y6 * Z4 * 2.0 + X7 * Y7 * Z7 * 6.0 + X7 * Y8 * Z8 * 2.0 + X8 * Y7 * Z8 * 2.0 + X8 * Y8 * Z7 * 2.0 + X7 * Y9 * Z9 * 2.0 + X9 * Y7 * Z9 * 2.0 + X9 * Y9 * Z7 * 2.0;
    G0[6 + 5 * 9] = X7 * t20 - X7 * t21 - X7 * t22 + X7 * t23 - X7 * t24 - X7 * t25 + X7 * t26 * 3.0 + X7 * t27 + X7 * t28 + X1 * Z1 * Z7 * 2.0 + X1 * Z2 * Z8 * 2.0 + X2 * Z1 * Z8 * 2.0 - X2 * Z2 * Z7 * 2.0 + X8 * Z1 * Z2 * 2.0 + X1 * Z3 * Z9 * 2.0 + X3 * Z1 * Z9 * 2.0 - X3 * Z3 * Z7 * 2.0 + X9 * Z1 * Z3 * 2.0 + X4 * Z4 * Z7 * 2.0 + X4 * Z5 * Z8 * 2.0 + X5 * Z4 * Z8 * 2.0 - X5 * Z5 * Z7 * 2.0 + X8 * Z4 * Z5 * 2.0 + X4 * Z6 * Z9 * 2.0 + X6 * Z4 * Z9 * 2.0 - X6 * Z6 * Z7 * 2.0 + X9 * Z4 * Z6 * 2.0 + X8 * Z7 * Z8 * 2.0 + X9 * Z7 * Z9 * 2.0;
    G0[6 + 6 * 9] = -Y7 * t11 - Y7 * t12 + Y7 * t13 - Y7 * t14 - Y7 * t15 + Y7 * t16 + Y7 * t17 + Y7 * t18 + Y7 * t19 + Y1 * Y2 * Y8 * 2.0 + Y1 * Y3 * Y9 * 2.0 + Y4 * Y5 * Y8 * 2.0 + Y4 * Y6 * Y9 * 2.0;
    G0[6 + 7 * 9] = -Z7 * t11 - Z7 * t12 + Z7 * t13 - Z7 * t14 - Z7 * t15 + Z7 * t16 * 3.0 + Z7 * t17 + Z7 * t18 + Z7 * t19 + Y1 * Y7 * Z1 * 2.0 + Y1 * Y2 * Z8 * 2.0 + Y1 * Y8 * Z2 * 2.0 - Y2 * Y7 * Z2 * 2.0 + Y2 * Y8 * Z1 * 2.0 + Y1 * Y3 * Z9 * 2.0 + Y1 * Y9 * Z3 * 2.0 - Y3 * Y7 * Z3 * 2.0 + Y3 * Y9 * Z1 * 2.0 + Y4 * Y7 * Z4 * 2.0 + Y4 * Y5 * Z8 * 2.0 + Y4 * Y8 * Z5 * 2.0 - Y5 * Y7 * Z5 * 2.0 + Y5 * Y8 * Z4 * 2.0 + Y4 * Y6 * Z9 * 2.0 + Y4 * Y9 * Z6 * 2.0 - Y6 * Y7 * Z6 * 2.0 + Y6 * Y9 * Z4 * 2.0 + Y7 * Y8 * Z8 * 2.0 + Y7 * Y9 * Z9 * 2.0;
    G0[6 + 8 * 9] = Y7 * t20 - Y7 * t21 - Y7 * t22 + Y7 * t23 - Y7 * t24 - Y7 * t25 + Y7 * t26 * 3.0 + Y7 * t27 + Y7 * t28 + Y1 * Z1 * Z7 * 2.0 + Y1 * Z2 * Z8 * 2.0 + Y2 * Z1 * Z8 * 2.0 - Y2 * Z2 * Z7 * 2.0 + Y8 * Z1 * Z2 * 2.0 + Y1 * Z3 * Z9 * 2.0 + Y3 * Z1 * Z9 * 2.0 - Y3 * Z3 * Z7 * 2.0 + Y9 * Z1 * Z3 * 2.0 + Y4 * Z4 * Z7 * 2.0 + Y4 * Z5 * Z8 * 2.0 + Y5 * Z4 * Z8 * 2.0 - Y5 * Z5 * Z7 * 2.0 + Y8 * Z4 * Z5 * 2.0 + Y4 * Z6 * Z9 * 2.0 + Y6 * Z4 * Z9 * 2.0 - Y6 * Z6 * Z7 * 2.0 + Y9 * Z4 * Z6 * 2.0 + Y8 * Z7 * Z8 * 2.0 + Y9 * Z7 * Z9 * 2.0;
    G0[6 + 9 * 9] = Z7 * t20 - Z7 * t21 - Z7 * t22 + Z7 * t23 - Z7 * t24 - Z7 * t25 + Z7 * t26 + Z7 * t27 + Z7 * t28 + Z1 * Z2 * Z8 * 2.0 + Z1 * Z3 * Z9 * 2.0 + Z4 * Z5 * Z8 * 2.0 + Z4 * Z6 * Z9 * 2.0;

    G0[7 + 0 * 9] = -X8 * t2 + X8 * t3 - X8 * t4 - X8 * t5 + X8 * t6 - X8 * t7 + X8 * t8 + X8 * t9 + X8 * t10 + X1 * X2 * X7 * 2.0 + X2 * X3 * X9 * 2.0 + X4 * X5 * X7 * 2.0 + X5 * X6 * X9 * 2.0;
    G0[7 + 1 * 9] = -Y8 * t2 + Y8 * t3 - Y8 * t4 - Y8 * t5 + Y8 * t6 - Y8 * t7 + Y8 * t8 + Y8 * t9 * 3.0 + Y8 * t10 + X1 * X2 * Y7 * 2.0 + X1 * X7 * Y2 * 2.0 - X1 * X8 * Y1 * 2.0 + X2 * X7 * Y1 * 2.0 + X2 * X8 * Y2 * 2.0 + X2 * X3 * Y9 * 2.0 + X2 * X9 * Y3 * 2.0 - X3 * X8 * Y3 * 2.0 + X3 * X9 * Y2 * 2.0 + X4 * X5 * Y7 * 2.0 + X4 * X7 * Y5 * 2.0 - X4 * X8 * Y4 * 2.0 + X5 * X7 * Y4 * 2.0 + X5 * X8 * Y5 * 2.0 + X5 * X6 * Y9 * 2.0 + X5 * X9 * Y6 * 2.0 - X6 * X8 * Y6 * 2.0 + X6 * X9 * Y5 * 2.0 + X7 * X8 * Y7 * 2.0 + X8 * X9 * Y9 * 2.0;
    G0[7 + 2 * 9] = -Z8 * t2 + Z8 * t3 - Z8 * t4 - Z8 * t5 + Z8 * t6 - Z8 * t7 + Z8 * t8 + Z8 * t9 * 3.0 + Z8 * t10 + X1 * X2 * Z7 * 2.0 + X1 * X7 * Z2 * 2.0 - X1 * X8 * Z1 * 2.0 + X2 * X7 * Z1 * 2.0 + X2 * X8 * Z2 * 2.0 + X2 * X3 * Z9 * 2.0 + X2 * X9 * Z3 * 2.0 - X3 * X8 * Z3 * 2.0 + X3 * X9 * Z2 * 2.0 + X4 * X5 * Z7 * 2.0 + X4 * X7 * Z5 * 2.0 - X4 * X8 * Z4 * 2.0 + X5 * X7 * Z4 * 2.0 + X5 * X8 * Z5 * 2.0 + X5 * X6 * Z9 * 2.0 + X5 * X9 * Z6 * 2.0 - X6 * X8 * Z6 * 2.0 + X6 * X9 * Z5 * 2.0 + X7 * X8 * Z7 * 2.0 + X8 * X9 * Z9 * 2.0;
    G0[7 + 3 * 9] = X8 * t11 - X8 * t12 - X8 * t13 + X8 * t14 - X8 * t15 + X8 * t16 + X8 * t17 * 3.0 + X8 * t18 - X8 * t19 - X1 * Y1 * Y8 * 2.0 + X1 * Y2 * Y7 * 2.0 + X2 * Y1 * Y7 * 2.0 + X7 * Y1 * Y2 * 2.0 + X2 * Y2 * Y8 * 2.0 + X2 * Y3 * Y9 * 2.0 + X3 * Y2 * Y9 * 2.0 - X3 * Y3 * Y8 * 2.0 + X9 * Y2 * Y3 * 2.0 - X4 * Y4 * Y8 * 2.0 + X4 * Y5 * Y7 * 2.0 + X5 * Y4 * Y7 * 2.0 + X7 * Y4 * Y5 * 2.0 + X5 * Y5 * Y8 * 2.0 + X5 * Y6 * Y9 * 2.0 + X6 * Y5 * Y9 * 2.0 - X6 * Y6 * Y8 * 2.0 + X9 * Y5 * Y6 * 2.0 + X7 * Y7 * Y8 * 2.0 + X9 * Y8 * Y9 * 2.0;
    G0[7 + 4 * 9] = X1 * Y1 * Z8 * -2.0 + X1 * Y2 * Z7 * 2.0 + X1 * Y7 * Z2 * 2.0 - X1 * Y8 * Z1 * 2.0 + X2 * Y1 * Z7 * 2.0 + X2 * Y7 * Z1 * 2.0 + X7 * Y1 * Z2 * 2.0 + X7 * Y2 * Z1 * 2.0 - X8 * Y1 * Z1 * 2.0 + X2 * Y2 * Z8 * 2.0 + X2 * Y8 * Z2 * 2.0 + X8 * Y2 * Z2 * 2.0 + X2 * Y3 * Z9 * 2.0 + X2 * Y9 * Z3 * 2.0 + X3 * Y2 * Z9 * 2.0 - X3 * Y3 * Z8 * 2.0 - X3 * Y8 * Z3 * 2.0 + X3 * Y9 * Z2 * 2.0 - X8 * Y3 * Z3 * 2.0 + X9 * Y2 * Z3 * 2.0 + X9 * Y3 * Z2 * 2.0 - X4 * Y4 * Z8 * 2.0 + X4 * Y5 * Z7 * 2.0 + X4 * Y7 * Z5 * 2.0 - X4 * Y8 * Z4 * 2.0 + X5 * Y4 * Z7 * 2.0 + X5 * Y7 * Z4 * 2.0 + X7 * Y4 * Z5 * 2.0 + X7 * Y5 * Z4 * 2.0 - X8 * Y4 * Z4 * 2.0 + X5 * Y5 * Z8 * 2.0 + X5 * Y8 * Z5 * 2.0 + X8 * Y5 * Z5 * 2.0 + X5 * Y6 * Z9 * 2.0 + X5 * Y9 * Z6 * 2.0 + X6 * Y5 * Z9 * 2.0 - X6 * Y6 * Z8 * 2.0 - X6 * Y8 * Z6 * 2.0 + X6 * Y9 * Z5 * 2.0 - X8 * Y6 * Z6 * 2.0 + X9 * Y5 * Z6 * 2.0 + X9 * Y6 * Z5 * 2.0 + X7 * Y7 * Z8 * 2.0 + X7 * Y8 * Z7 * 2.0 + X8 * Y7 * Z7 * 2.0 + X8 * Y8 * Z8 * 6.0 + X8 * Y9 * Z9 * 2.0 + X9 * Y8 * Z9 * 2.0 + X9 * Y9 * Z8 * 2.0;
    G0[7 + 5 * 9] = -X8 * t20 + X8 * t21 - X8 * t22 - X8 * t23 + X8 * t24 - X8 * t25 + X8 * t26 + X8 * t27 * 3.0 + X8 * t28 - X1 * Z1 * Z8 * 2.0 + X1 * Z2 * Z7 * 2.0 + X2 * Z1 * Z7 * 2.0 + X7 * Z1 * Z2 * 2.0 + X2 * Z2 * Z8 * 2.0 + X2 * Z3 * Z9 * 2.0 + X3 * Z2 * Z9 * 2.0 - X3 * Z3 * Z8 * 2.0 + X9 * Z2 * Z3 * 2.0 - X4 * Z4 * Z8 * 2.0 + X4 * Z5 * Z7 * 2.0 + X5 * Z4 * Z7 * 2.0 + X7 * Z4 * Z5 * 2.0 + X5 * Z5 * Z8 * 2.0 + X5 * Z6 * Z9 * 2.0 + X6 * Z5 * Z9 * 2.0 - X6 * Z6 * Z8 * 2.0 + X9 * Z5 * Z6 * 2.0 + X7 * Z7 * Z8 * 2.0 + X9 * Z8 * Z9 * 2.0;
    G0[7 + 6 * 9] = Y8 * t11 - Y8 * t12 - Y8 * t13 + Y8 * t14 - Y8 * t15 + Y8 * t16 + Y8 * t17 + Y8 * t18 - Y8 * t19 + Y1 * Y2 * Y7 * 2.0 + Y2 * Y3 * Y9 * 2.0 + Y4 * Y5 * Y7 * 2.0 + Y5 * Y6 * Y9 * 2.0;
    G0[7 + 7 * 9] = Z8 * t11 - Z8 * t12 - Z8 * t13 + Z8 * t14 - Z8 * t15 + Z8 * t16 + Z8 * t17 * 3.0 + Z8 * t18 - Z8 * t19 + Y1 * Y2 * Z7 * 2.0 + Y1 * Y7 * Z2 * 2.0 - Y1 * Y8 * Z1 * 2.0 + Y2 * Y7 * Z1 * 2.0 + Y2 * Y8 * Z2 * 2.0 + Y2 * Y3 * Z9 * 2.0 + Y2 * Y9 * Z3 * 2.0 - Y3 * Y8 * Z3 * 2.0 + Y3 * Y9 * Z2 * 2.0 + Y4 * Y5 * Z7 * 2.0 + Y4 * Y7 * Z5 * 2.0 - Y4 * Y8 * Z4 * 2.0 + Y5 * Y7 * Z4 * 2.0 + Y5 * Y8 * Z5 * 2.0 + Y5 * Y6 * Z9 * 2.0 + Y5 * Y9 * Z6 * 2.0 - Y6 * Y8 * Z6 * 2.0 + Y6 * Y9 * Z5 * 2.0 + Y7 * Y8 * Z7 * 2.0 + Y8 * Y9 * Z9 * 2.0;
    G0[7 + 8 * 9] = -Y8 * t20 + Y8 * t21 - Y8 * t22 - Y8 * t23 + Y8 * t24 - Y8 * t25 + Y8 * t26 + Y8 * t27 * 3.0 + Y8 * t28 - Y1 * Z1 * Z8 * 2.0 + Y1 * Z2 * Z7 * 2.0 + Y2 * Z1 * Z7 * 2.0 + Y7 * Z1 * Z2 * 2.0 + Y2 * Z2 * Z8 * 2.0 + Y2 * Z3 * Z9 * 2.0 + Y3 * Z2 * Z9 * 2.0 - Y3 * Z3 * Z8 * 2.0 + Y9 * Z2 * Z3 * 2.0 - Y4 * Z4 * Z8 * 2.0 + Y4 * Z5 * Z7 * 2.0 + Y5 * Z4 * Z7 * 2.0 + Y7 * Z4 * Z5 * 2.0 + Y5 * Z5 * Z8 * 2.0 + Y5 * Z6 * Z9 * 2.0 + Y6 * Z5 * Z9 * 2.0 - Y6 * Z6 * Z8 * 2.0 + Y9 * Z5 * Z6 * 2.0 + Y7 * Z7 * Z8 * 2.0 + Y9 * Z8 * Z9 * 2.0;
    G0[7 + 9 * 9] = -Z8 * t20 + Z8 * t21 - Z8 * t22 - Z8 * t23 + Z8 * t24 - Z8 * t25 + Z8 * t26 + Z8 * t27 + Z8 * t28 + Z1 * Z2 * Z7 * 2.0 + Z2 * Z3 * Z9 * 2.0 + Z4 * Z5 * Z7 * 2.0 + Z5 * Z6 * Z9 * 2.0;

    G0[8 + 0 * 9] = -X9 * t2 - X9 * t3 + X9 * t4 - X9 * t5 - X9 * t6 + X9 * t7 + X9 * t8 + X9 * t9 + X9 * t10 + X1 * X3 * X7 * 2.0 + X2 * X3 * X8 * 2.0 + X4 * X6 * X7 * 2.0 + X5 * X6 * X8 * 2.0;
    G0[8 + 1 * 9] = -Y9 * t2 - Y9 * t3 + Y9 * t4 - Y9 * t5 - Y9 * t6 + Y9 * t7 + Y9 * t8 + Y9 * t9 + Y9 * t10 * 3.0 + X1 * X3 * Y7 * 2.0 + X1 * X7 * Y3 * 2.0 - X1 * X9 * Y1 * 2.0 + X3 * X7 * Y1 * 2.0 + X2 * X3 * Y8 * 2.0 + X2 * X8 * Y3 * 2.0 - X2 * X9 * Y2 * 2.0 + X3 * X8 * Y2 * 2.0 + X3 * X9 * Y3 * 2.0 + X4 * X6 * Y7 * 2.0 + X4 * X7 * Y6 * 2.0 - X4 * X9 * Y4 * 2.0 + X6 * X7 * Y4 * 2.0 + X5 * X6 * Y8 * 2.0 + X5 * X8 * Y6 * 2.0 - X5 * X9 * Y5 * 2.0 + X6 * X8 * Y5 * 2.0 + X6 * X9 * Y6 * 2.0 + X7 * X9 * Y7 * 2.0 + X8 * X9 * Y8 * 2.0;
    G0[8 + 2 * 9] = -Z9 * t2 - Z9 * t3 + Z9 * t4 - Z9 * t5 - Z9 * t6 + Z9 * t7 + Z9 * t8 + Z9 * t9 + Z9 * t10 * 3.0 + X1 * X3 * Z7 * 2.0 + X1 * X7 * Z3 * 2.0 - X1 * X9 * Z1 * 2.0 + X3 * X7 * Z1 * 2.0 + X2 * X3 * Z8 * 2.0 + X2 * X8 * Z3 * 2.0 - X2 * X9 * Z2 * 2.0 + X3 * X8 * Z2 * 2.0 + X3 * X9 * Z3 * 2.0 + X4 * X6 * Z7 * 2.0 + X4 * X7 * Z6 * 2.0 - X4 * X9 * Z4 * 2.0 + X6 * X7 * Z4 * 2.0 + X5 * X6 * Z8 * 2.0 + X5 * X8 * Z6 * 2.0 - X5 * X9 * Z5 * 2.0 + X6 * X8 * Z5 * 2.0 + X6 * X9 * Z6 * 2.0 + X7 * X9 * Z7 * 2.0 + X8 * X9 * Z8 * 2.0;
    G0[8 + 3 * 9] = -X9 * t11 + X9 * t12 - X9 * t13 - X9 * t14 + X9 * t15 + X9 * t16 + X9 * t17 + X9 * t18 * 3.0 - X9 * t19 - X1 * Y1 * Y9 * 2.0 + X1 * Y3 * Y7 * 2.0 + X3 * Y1 * Y7 * 2.0 + X7 * Y1 * Y3 * 2.0 - X2 * Y2 * Y9 * 2.0 + X2 * Y3 * Y8 * 2.0 + X3 * Y2 * Y8 * 2.0 + X8 * Y2 * Y3 * 2.0 + X3 * Y3 * Y9 * 2.0 - X4 * Y4 * Y9 * 2.0 + X4 * Y6 * Y7 * 2.0 + X6 * Y4 * Y7 * 2.0 + X7 * Y4 * Y6 * 2.0 - X5 * Y5 * Y9 * 2.0 + X5 * Y6 * Y8 * 2.0 + X6 * Y5 * Y8 * 2.0 + X8 * Y5 * Y6 * 2.0 + X6 * Y6 * Y9 * 2.0 + X7 * Y7 * Y9 * 2.0 + X8 * Y8 * Y9 * 2.0;
    G0[8 + 4 * 9] = X1 * Y1 * Z9 * -2.0 + X1 * Y3 * Z7 * 2.0 + X1 * Y7 * Z3 * 2.0 - X1 * Y9 * Z1 * 2.0 + X3 * Y1 * Z7 * 2.0 + X3 * Y7 * Z1 * 2.0 + X7 * Y1 * Z3 * 2.0 + X7 * Y3 * Z1 * 2.0 - X9 * Y1 * Z1 * 2.0 - X2 * Y2 * Z9 * 2.0 + X2 * Y3 * Z8 * 2.0 + X2 * Y8 * Z3 * 2.0 - X2 * Y9 * Z2 * 2.0 + X3 * Y2 * Z8 * 2.0 + X3 * Y8 * Z2 * 2.0 + X8 * Y2 * Z3 * 2.0 + X8 * Y3 * Z2 * 2.0 - X9 * Y2 * Z2 * 2.0 + X3 * Y3 * Z9 * 2.0 + X3 * Y9 * Z3 * 2.0 + X9 * Y3 * Z3 * 2.0 - X4 * Y4 * Z9 * 2.0 + X4 * Y6 * Z7 * 2.0 + X4 * Y7 * Z6 * 2.0 - X4 * Y9 * Z4 * 2.0 + X6 * Y4 * Z7 * 2.0 + X6 * Y7 * Z4 * 2.0 + X7 * Y4 * Z6 * 2.0 + X7 * Y6 * Z4 * 2.0 - X9 * Y4 * Z4 * 2.0 - X5 * Y5 * Z9 * 2.0 + X5 * Y6 * Z8 * 2.0 + X5 * Y8 * Z6 * 2.0 - X5 * Y9 * Z5 * 2.0 + X6 * Y5 * Z8 * 2.0 + X6 * Y8 * Z5 * 2.0 + X8 * Y5 * Z6 * 2.0 + X8 * Y6 * Z5 * 2.0 - X9 * Y5 * Z5 * 2.0 + X6 * Y6 * Z9 * 2.0 + X6 * Y9 * Z6 * 2.0 + X9 * Y6 * Z6 * 2.0 + X7 * Y7 * Z9 * 2.0 + X7 * Y9 * Z7 * 2.0 + X9 * Y7 * Z7 * 2.0 + X8 * Y8 * Z9 * 2.0 + X8 * Y9 * Z8 * 2.0 + X9 * Y8 * Z8 * 2.0 + X9 * Y9 * Z9 * 6.0;
    G0[8 + 5 * 9] = -X9 * t20 - X9 * t21 + X9 * t22 - X9 * t23 - X9 * t24 + X9 * t25 + X9 * t26 + X9 * t27 + X9 * t28 * 3.0 - X1 * Z1 * Z9 * 2.0 + X1 * Z3 * Z7 * 2.0 + X3 * Z1 * Z7 * 2.0 + X7 * Z1 * Z3 * 2.0 - X2 * Z2 * Z9 * 2.0 + X2 * Z3 * Z8 * 2.0 + X3 * Z2 * Z8 * 2.0 + X8 * Z2 * Z3 * 2.0 + X3 * Z3 * Z9 * 2.0 - X4 * Z4 * Z9 * 2.0 + X4 * Z6 * Z7 * 2.0 + X6 * Z4 * Z7 * 2.0 + X7 * Z4 * Z6 * 2.0 - X5 * Z5 * Z9 * 2.0 + X5 * Z6 * Z8 * 2.0 + X6 * Z5 * Z8 * 2.0 + X8 * Z5 * Z6 * 2.0 + X6 * Z6 * Z9 * 2.0 + X7 * Z7 * Z9 * 2.0 + X8 * Z8 * Z9 * 2.0;
    G0[8 + 6 * 9] = -Y9 * t11 + Y9 * t12 - Y9 * t13 - Y9 * t14 + Y9 * t15 + Y9 * t16 + Y9 * t17 + Y9 * t18 - Y9 * t19 + Y1 * Y3 * Y7 * 2.0 + Y2 * Y3 * Y8 * 2.0 + Y4 * Y6 * Y7 * 2.0 + Y5 * Y6 * Y8 * 2.0;
    G0[8 + 7 * 9] = -Z9 * t11 + Z9 * t12 - Z9 * t13 - Z9 * t14 + Z9 * t15 + Z9 * t16 + Z9 * t17 + Z9 * t18 * 3.0 - Z9 * t19 + Y1 * Y3 * Z7 * 2.0 + Y1 * Y7 * Z3 * 2.0 - Y1 * Y9 * Z1 * 2.0 + Y3 * Y7 * Z1 * 2.0 + Y2 * Y3 * Z8 * 2.0 + Y2 * Y8 * Z3 * 2.0 - Y2 * Y9 * Z2 * 2.0 + Y3 * Y8 * Z2 * 2.0 + Y3 * Y9 * Z3 * 2.0 + Y4 * Y6 * Z7 * 2.0 + Y4 * Y7 * Z6 * 2.0 - Y4 * Y9 * Z4 * 2.0 + Y6 * Y7 * Z4 * 2.0 + Y5 * Y6 * Z8 * 2.0 + Y5 * Y8 * Z6 * 2.0 - Y5 * Y9 * Z5 * 2.0 + Y6 * Y8 * Z5 * 2.0 + Y6 * Y9 * Z6 * 2.0 + Y7 * Y9 * Z7 * 2.0 + Y8 * Y9 * Z8 * 2.0;
    G0[8 + 8 * 9] = -Y9 * t20 - Y9 * t21 + Y9 * t22 - Y9 * t23 - Y9 * t24 + Y9 * t25 + Y9 * t26 + Y9 * t27 + Y9 * t28 * 3.0 - Y1 * Z1 * Z9 * 2.0 + Y1 * Z3 * Z7 * 2.0 + Y3 * Z1 * Z7 * 2.0 + Y7 * Z1 * Z3 * 2.0 - Y2 * Z2 * Z9 * 2.0 + Y2 * Z3 * Z8 * 2.0 + Y3 * Z2 * Z8 * 2.0 + Y8 * Z2 * Z3 * 2.0 + Y3 * Z3 * Z9 * 2.0 - Y4 * Z4 * Z9 * 2.0 + Y4 * Z6 * Z7 * 2.0 + Y6 * Z4 * Z7 * 2.0 + Y7 * Z4 * Z6 * 2.0 - Y5 * Z5 * Z9 * 2.0 + Y5 * Z6 * Z8 * 2.0 + Y6 * Z5 * Z8 * 2.0 + Y8 * Z5 * Z6 * 2.0 + Y6 * Z6 * Z9 * 2.0 + Y7 * Z7 * Z9 * 2.0 + Y8 * Z8 * Z9 * 2.0;
    G0[8 + 9 * 9] = -Z9 * t20 - Z9 * t21 + Z9 * t22 - Z9 * t23 - Z9 * t24 + Z9 * t25 + Z9 * t26 + Z9 * t27 + Z9 * t28 + Z1 * Z3 * Z7 * 2.0 + Z2 * Z3 * Z8 * 2.0 + Z4 * Z6 * Z7 * 2.0 + Z5 * Z6 * Z8 * 2.0;

    G0[9 + 0 * 9] = X1 * X5 * X9 - X1 * X6 * X8 - X2 * X4 * X9 + X2 * X6 * X7 + X3 * X4 * X8 - X3 * X5 * X7;
    G0[9 + 1 * 9] = X1 * X5 * Y9 - X1 * X6 * Y8 - X1 * X8 * Y6 + X1 * X9 * Y5 - X2 * X4 * Y9 + X2 * X6 * Y7 + X2 * X7 * Y6 - X2 * X9 * Y4 + X3 * X4 * Y8 - X3 * X5 * Y7 - X3 * X7 * Y5 + X3 * X8 * Y4 + X4 * X8 * Y3 - X4 * X9 * Y2 - X5 * X7 * Y3 + X5 * X9 * Y1 + X6 * X7 * Y2 - X6 * X8 * Y1;
    G0[9 + 2 * 9] = X1 * X5 * Z9 - X1 * X6 * Z8 - X1 * X8 * Z6 + X1 * X9 * Z5 - X2 * X4 * Z9 + X2 * X6 * Z7 + X2 * X7 * Z6 - X2 * X9 * Z4 + X3 * X4 * Z8 - X3 * X5 * Z7 - X3 * X7 * Z5 + X3 * X8 * Z4 + X4 * X8 * Z3 - X4 * X9 * Z2 - X5 * X7 * Z3 + X5 * X9 * Z1 + X6 * X7 * Z2 - X6 * X8 * Z1;
    G0[9 + 3 * 9] = X1 * Y5 * Y9 - X1 * Y6 * Y8 - X2 * Y4 * Y9 + X2 * Y6 * Y7 + X3 * Y4 * Y8 - X3 * Y5 * Y7 - X4 * Y2 * Y9 + X4 * Y3 * Y8 + X5 * Y1 * Y9 - X5 * Y3 * Y7 - X6 * Y1 * Y8 + X6 * Y2 * Y7 + X7 * Y2 * Y6 - X7 * Y3 * Y5 - X8 * Y1 * Y6 + X8 * Y3 * Y4 + X9 * Y1 * Y5 - X9 * Y2 * Y4;
    G0[9 + 4 * 9] = X1 * Y5 * Z9 - X1 * Y6 * Z8 - X1 * Y8 * Z6 + X1 * Y9 * Z5 - X2 * Y4 * Z9 + X2 * Y6 * Z7 + X2 * Y7 * Z6 - X2 * Y9 * Z4 + X3 * Y4 * Z8 - X3 * Y5 * Z7 - X3 * Y7 * Z5 + X3 * Y8 * Z4 - X4 * Y2 * Z9 + X4 * Y3 * Z8 + X4 * Y8 * Z3 - X4 * Y9 * Z2 + X5 * Y1 * Z9 - X5 * Y3 * Z7 - X5 * Y7 * Z3 + X5 * Y9 * Z1 - X6 * Y1 * Z8 + X6 * Y2 * Z7 + X6 * Y7 * Z2 - X6 * Y8 * Z1 + X7 * Y2 * Z6 - X7 * Y3 * Z5 - X7 * Y5 * Z3 + X7 * Y6 * Z2 - X8 * Y1 * Z6 + X8 * Y3 * Z4 + X8 * Y4 * Z3 - X8 * Y6 * Z1 + X9 * Y1 * Z5 - X9 * Y2 * Z4 - X9 * Y4 * Z2 + X9 * Y5 * Z1;
    G0[9 + 5 * 9] = X1 * Z5 * Z9 - X1 * Z6 * Z8 - X2 * Z4 * Z9 + X2 * Z6 * Z7 + X3 * Z4 * Z8 - X3 * Z5 * Z7 - X4 * Z2 * Z9 + X4 * Z3 * Z8 + X5 * Z1 * Z9 - X5 * Z3 * Z7 - X6 * Z1 * Z8 + X6 * Z2 * Z7 + X7 * Z2 * Z6 - X7 * Z3 * Z5 - X8 * Z1 * Z6 + X8 * Z3 * Z4 + X9 * Z1 * Z5 - X9 * Z2 * Z4;
    G0[9 + 6 * 9] = Y1 * Y5 * Y9 - Y1 * Y6 * Y8 - Y2 * Y4 * Y9 + Y2 * Y6 * Y7 + Y3 * Y4 * Y8 - Y3 * Y5 * Y7;
    G0[9 + 7 * 9] = Y1 * Y5 * Z9 - Y1 * Y6 * Z8 - Y1 * Y8 * Z6 + Y1 * Y9 * Z5 - Y2 * Y4 * Z9 + Y2 * Y6 * Z7 + Y2 * Y7 * Z6 - Y2 * Y9 * Z4 + Y3 * Y4 * Z8 - Y3 * Y5 * Z7 - Y3 * Y7 * Z5 + Y3 * Y8 * Z4 + Y4 * Y8 * Z3 - Y4 * Y9 * Z2 - Y5 * Y7 * Z3 + Y5 * Y9 * Z1 + Y6 * Y7 * Z2 - Y6 * Y8 * Z1;
    G0[9 + 8 * 9] = Y1 * Z5 * Z9 - Y1 * Z6 * Z8 - Y2 * Z4 * Z9 + Y2 * Z6 * Z7 + Y3 * Z4 * Z8 - Y3 * Z5 * Z7 - Y4 * Z2 * Z9 + Y4 * Z3 * Z8 + Y5 * Z1 * Z9 - Y5 * Z3 * Z7 - Y6 * Z1 * Z8 + Y6 * Z2 * Z7 + Y7 * Z2 * Z6 - Y7 * Z3 * Z5 - Y8 * Z1 * Z6 + Y8 * Z3 * Z4 + Y9 * Z1 * Z5 - Y9 * Z2 * Z4;
    G0[9 + 9 * 9] = Z1 * Z5 * Z9 - Z1 * Z6 * Z8 - Z2 * Z4 * Z9 + Z2 * Z6 * Z7 + Z3 * Z4 * Z8 - Z3 * Z5 * Z7;

    Map<Matrix<double, 10, 10, ColMajor>> G_map(G0, 10, 10);
    G = G_map;
    //    std::cout<<B<<std::endl;
}

inline void compute_Gcoefficients(const EigenSols &eigen_sols, Matrix<double, 9, 10> &G)
{

    // id = [1,2,4,7,3,5,8,6,9,10];

    // BB = BB(:,id);

    // BB = BB'*BB;

    //[~,~,Vb]=svds(BB,1,'smallest');
    double X1, X2, X3, X4, X5, X6, X7, X8, X9;
    double Y1, Y2, Y3, Y4, Y5, Y6, Y7, Y8, Y9;
    double Z1, Z2, Z3, Z4, Z5, Z6, Z7, Z8, Z9;

    X1 = eigen_sols(0, 0);
    X2 = eigen_sols(0, 1);
    X3 = eigen_sols(0, 2);
    X4 = eigen_sols(0, 3);
    X5 = eigen_sols(0, 4);
    X6 = eigen_sols(0, 5);
    X7 = eigen_sols(0, 6);
    X8 = eigen_sols(0, 7);
    X9 = eigen_sols(0, 8);

    Y1 = eigen_sols(1, 0);
    Y2 = eigen_sols(1, 1);
    Y3 = eigen_sols(1, 2);
    Y4 = eigen_sols(1, 3);
    Y5 = eigen_sols(1, 4);
    Y6 = eigen_sols(1, 5);
    Y7 = eigen_sols(1, 6);
    Y8 = eigen_sols(1, 7);
    Y9 = eigen_sols(1, 8);

    Z1 = eigen_sols(2, 0);
    Z2 = eigen_sols(2, 1);
    Z3 = eigen_sols(2, 2);
    Z4 = eigen_sols(2, 3);
    Z5 = eigen_sols(2, 4);
    Z6 = eigen_sols(2, 5);
    Z7 = eigen_sols(2, 6);
    Z8 = eigen_sols(2, 7);
    Z9 = eigen_sols(2, 8);

    double t2 = X1 * X1;
    double t3 = X2 * X2;
    double t4 = X3 * X3;
    double t5 = X4 * X4;
    double t6 = X5 * X5;
    double t7 = X6 * X6;
    double t8 = X7 * X7;
    double t9 = X8 * X8;
    double t10 = X9 * X9;
    double t11 = Y2 * Y2;
    double t12 = Y3 * Y3;
    double t13 = Y4 * Y4;
    double t14 = Y5 * Y5;
    double t15 = Y6 * Y6;
    double t16 = Y7 * Y7;
    double t17 = Y8 * Y8;
    double t18 = Y9 * Y9;
    double t19 = Y1 * Y1;
    double t20 = Z1 * Z1;
    double t21 = Z2 * Z2;
    double t22 = Z3 * Z3;
    double t23 = Z4 * Z4;
    double t24 = Z5 * Z5;
    double t25 = Z6 * Z6;
    double t26 = Z7 * Z7;
    double t27 = Z8 * Z8;
    double t28 = Z9 * Z9;

    double G0[9 * 10];
    G0[0 + 0 * 9] = X1 * t2 + X1 * t3 + X1 * t4 + X1 * t5 - X1 * t6 - X1 * t7 + X1 * t8 - X1 * t9 - X1 * t10 + X2 * X4 * X5 * 2.0 + X3 * X4 * X6 * 2.0 + X2 * X7 * X8 * 2.0 + X3 * X7 * X9 * 2.0;
    G0[0 + 1 * 9] = Y1 * t2 * 3.0 + Y1 * t3 + Y1 * t4 + Y1 * t5 - Y1 * t6 - Y1 * t7 + Y1 * t8 - Y1 * t9 - Y1 * t10 + X1 * X2 * Y2 * 2.0 + X1 * X3 * Y3 * 2.0 + X1 * X4 * Y4 * 2.0 - X1 * X5 * Y5 * 2.0 + X2 * X4 * Y5 * 2.0 + X2 * X5 * Y4 * 2.0 + X4 * X5 * Y2 * 2.0 - X1 * X6 * Y6 * 2.0 + X3 * X4 * Y6 * 2.0 + X3 * X6 * Y4 * 2.0 + X4 * X6 * Y3 * 2.0 + X1 * X7 * Y7 * 2.0 - X1 * X8 * Y8 * 2.0 + X2 * X7 * Y8 * 2.0 + X2 * X8 * Y7 * 2.0 + X7 * X8 * Y2 * 2.0 - X1 * X9 * Y9 * 2.0 + X3 * X7 * Y9 * 2.0 + X3 * X9 * Y7 * 2.0 + X7 * X9 * Y3 * 2.0;
    G0[0 + 2 * 9] = Z1 * t2 * 3.0 + Z1 * t3 + Z1 * t4 + Z1 * t5 - Z1 * t6 - Z1 * t7 + Z1 * t8 - Z1 * t9 - Z1 * t10 + X1 * X2 * Z2 * 2.0 + X1 * X3 * Z3 * 2.0 + X1 * X4 * Z4 * 2.0 - X1 * X5 * Z5 * 2.0 + X2 * X4 * Z5 * 2.0 + X2 * X5 * Z4 * 2.0 + X4 * X5 * Z2 * 2.0 - X1 * X6 * Z6 * 2.0 + X3 * X4 * Z6 * 2.0 + X3 * X6 * Z4 * 2.0 + X4 * X6 * Z3 * 2.0 + X1 * X7 * Z7 * 2.0 - X1 * X8 * Z8 * 2.0 + X2 * X7 * Z8 * 2.0 + X2 * X8 * Z7 * 2.0 + X7 * X8 * Z2 * 2.0 - X1 * X9 * Z9 * 2.0 + X3 * X7 * Z9 * 2.0 + X3 * X9 * Z7 * 2.0 + X7 * X9 * Z3 * 2.0;
    G0[0 + 3 * 9] = X1 * t11 + X1 * t12 + X1 * t13 - X1 * t14 - X1 * t15 + X1 * t16 - X1 * t17 - X1 * t18 + X1 * t19 * 3.0 + X2 * Y1 * Y2 * 2.0 + X3 * Y1 * Y3 * 2.0 + X4 * Y1 * Y4 * 2.0 + X2 * Y4 * Y5 * 2.0 + X4 * Y2 * Y5 * 2.0 - X5 * Y1 * Y5 * 2.0 + X5 * Y2 * Y4 * 2.0 + X3 * Y4 * Y6 * 2.0 + X4 * Y3 * Y6 * 2.0 - X6 * Y1 * Y6 * 2.0 + X6 * Y3 * Y4 * 2.0 + X7 * Y1 * Y7 * 2.0 + X2 * Y7 * Y8 * 2.0 + X7 * Y2 * Y8 * 2.0 - X8 * Y1 * Y8 * 2.0 + X8 * Y2 * Y7 * 2.0 + X3 * Y7 * Y9 * 2.0 + X7 * Y3 * Y9 * 2.0 - X9 * Y1 * Y9 * 2.0 + X9 * Y3 * Y7 * 2.0;
    G0[0 + 4 * 9] = X1 * Y1 * Z1 * 6.0 + X1 * Y2 * Z2 * 2.0 + X2 * Y1 * Z2 * 2.0 + X2 * Y2 * Z1 * 2.0 + X1 * Y3 * Z3 * 2.0 + X3 * Y1 * Z3 * 2.0 + X3 * Y3 * Z1 * 2.0 + X1 * Y4 * Z4 * 2.0 + X4 * Y1 * Z4 * 2.0 + X4 * Y4 * Z1 * 2.0 - X1 * Y5 * Z5 * 2.0 + X2 * Y4 * Z5 * 2.0 + X2 * Y5 * Z4 * 2.0 + X4 * Y2 * Z5 * 2.0 + X4 * Y5 * Z2 * 2.0 - X5 * Y1 * Z5 * 2.0 + X5 * Y2 * Z4 * 2.0 + X5 * Y4 * Z2 * 2.0 - X5 * Y5 * Z1 * 2.0 - X1 * Y6 * Z6 * 2.0 + X3 * Y4 * Z6 * 2.0 + X3 * Y6 * Z4 * 2.0 + X4 * Y3 * Z6 * 2.0 + X4 * Y6 * Z3 * 2.0 - X6 * Y1 * Z6 * 2.0 + X6 * Y3 * Z4 * 2.0 + X6 * Y4 * Z3 * 2.0 - X6 * Y6 * Z1 * 2.0 + X1 * Y7 * Z7 * 2.0 + X7 * Y1 * Z7 * 2.0 + X7 * Y7 * Z1 * 2.0 - X1 * Y8 * Z8 * 2.0 + X2 * Y7 * Z8 * 2.0 + X2 * Y8 * Z7 * 2.0 + X7 * Y2 * Z8 * 2.0 + X7 * Y8 * Z2 * 2.0 - X8 * Y1 * Z8 * 2.0 + X8 * Y2 * Z7 * 2.0 + X8 * Y7 * Z2 * 2.0 - X8 * Y8 * Z1 * 2.0 - X1 * Y9 * Z9 * 2.0 + X3 * Y7 * Z9 * 2.0 + X3 * Y9 * Z7 * 2.0 + X7 * Y3 * Z9 * 2.0 + X7 * Y9 * Z3 * 2.0 - X9 * Y1 * Z9 * 2.0 + X9 * Y3 * Z7 * 2.0 + X9 * Y7 * Z3 * 2.0 - X9 * Y9 * Z1 * 2.0;
    G0[0 + 5 * 9] = X1 * t20 * 3.0 + X1 * t21 + X1 * t22 + X1 * t23 - X1 * t24 - X1 * t25 + X1 * t26 - X1 * t27 - X1 * t28 + X2 * Z1 * Z2 * 2.0 + X3 * Z1 * Z3 * 2.0 + X4 * Z1 * Z4 * 2.0 + X2 * Z4 * Z5 * 2.0 + X4 * Z2 * Z5 * 2.0 - X5 * Z1 * Z5 * 2.0 + X5 * Z2 * Z4 * 2.0 + X3 * Z4 * Z6 * 2.0 + X4 * Z3 * Z6 * 2.0 - X6 * Z1 * Z6 * 2.0 + X6 * Z3 * Z4 * 2.0 + X7 * Z1 * Z7 * 2.0 + X2 * Z7 * Z8 * 2.0 + X7 * Z2 * Z8 * 2.0 - X8 * Z1 * Z8 * 2.0 + X8 * Z2 * Z7 * 2.0 + X3 * Z7 * Z9 * 2.0 + X7 * Z3 * Z9 * 2.0 - X9 * Z1 * Z9 * 2.0 + X9 * Z3 * Z7 * 2.0;
    G0[0 + 6 * 9] = Y1 * t11 + Y1 * t12 + Y1 * t13 - Y1 * t14 - Y1 * t15 + Y1 * t16 - Y1 * t17 - Y1 * t18 + Y1 * t19 + Y2 * Y4 * Y5 * 2.0 + Y3 * Y4 * Y6 * 2.0 + Y2 * Y7 * Y8 * 2.0 + Y3 * Y7 * Y9 * 2.0;
    G0[0 + 7 * 9] = Z1 * t11 + Z1 * t12 + Z1 * t13 - Z1 * t14 - Z1 * t15 + Z1 * t16 - Z1 * t17 - Z1 * t18 + Z1 * t19 * 3.0 + Y1 * Y2 * Z2 * 2.0 + Y1 * Y3 * Z3 * 2.0 + Y1 * Y4 * Z4 * 2.0 - Y1 * Y5 * Z5 * 2.0 + Y2 * Y4 * Z5 * 2.0 + Y2 * Y5 * Z4 * 2.0 + Y4 * Y5 * Z2 * 2.0 - Y1 * Y6 * Z6 * 2.0 + Y3 * Y4 * Z6 * 2.0 + Y3 * Y6 * Z4 * 2.0 + Y4 * Y6 * Z3 * 2.0 + Y1 * Y7 * Z7 * 2.0 - Y1 * Y8 * Z8 * 2.0 + Y2 * Y7 * Z8 * 2.0 + Y2 * Y8 * Z7 * 2.0 + Y7 * Y8 * Z2 * 2.0 - Y1 * Y9 * Z9 * 2.0 + Y3 * Y7 * Z9 * 2.0 + Y3 * Y9 * Z7 * 2.0 + Y7 * Y9 * Z3 * 2.0;
    G0[0 + 8 * 9] = Y1 * t20 * 3.0 + Y1 * t21 + Y1 * t22 + Y1 * t23 - Y1 * t24 - Y1 * t25 + Y1 * t26 - Y1 * t27 - Y1 * t28 + Y2 * Z1 * Z2 * 2.0 + Y3 * Z1 * Z3 * 2.0 + Y4 * Z1 * Z4 * 2.0 + Y2 * Z4 * Z5 * 2.0 + Y4 * Z2 * Z5 * 2.0 - Y5 * Z1 * Z5 * 2.0 + Y5 * Z2 * Z4 * 2.0 + Y3 * Z4 * Z6 * 2.0 + Y4 * Z3 * Z6 * 2.0 - Y6 * Z1 * Z6 * 2.0 + Y6 * Z3 * Z4 * 2.0 + Y7 * Z1 * Z7 * 2.0 + Y2 * Z7 * Z8 * 2.0 + Y7 * Z2 * Z8 * 2.0 - Y8 * Z1 * Z8 * 2.0 + Y8 * Z2 * Z7 * 2.0 + Y3 * Z7 * Z9 * 2.0 + Y7 * Z3 * Z9 * 2.0 - Y9 * Z1 * Z9 * 2.0 + Y9 * Z3 * Z7 * 2.0;
    G0[0 + 9 * 9] = Z1 * t20 + Z1 * t21 + Z1 * t22 + Z1 * t23 - Z1 * t24 - Z1 * t25 + Z1 * t26 - Z1 * t27 - Z1 * t28 + Z2 * Z4 * Z5 * 2.0 + Z3 * Z4 * Z6 * 2.0 + Z2 * Z7 * Z8 * 2.0 + Z3 * Z7 * Z9 * 2.0;

    G0[1 + 0 * 9] = X2 * t2 + X2 * t3 + X2 * t4 - X2 * t5 + X2 * t6 - X2 * t7 - X2 * t8 + X2 * t9 - X2 * t10 + X1 * X4 * X5 * 2.0 + X3 * X5 * X6 * 2.0 + X1 * X7 * X8 * 2.0 + X3 * X8 * X9 * 2.0;
    G0[1 + 1 * 9] = Y2 * t2 + Y2 * t3 * 3.0 + Y2 * t4 - Y2 * t5 + Y2 * t6 - Y2 * t7 - Y2 * t8 + Y2 * t9 - Y2 * t10 + X1 * X2 * Y1 * 2.0 + X2 * X3 * Y3 * 2.0 + X1 * X4 * Y5 * 2.0 + X1 * X5 * Y4 * 2.0 - X2 * X4 * Y4 * 2.0 + X4 * X5 * Y1 * 2.0 + X2 * X5 * Y5 * 2.0 - X2 * X6 * Y6 * 2.0 + X3 * X5 * Y6 * 2.0 + X3 * X6 * Y5 * 2.0 + X5 * X6 * Y3 * 2.0 + X1 * X7 * Y8 * 2.0 + X1 * X8 * Y7 * 2.0 - X2 * X7 * Y7 * 2.0 + X7 * X8 * Y1 * 2.0 + X2 * X8 * Y8 * 2.0 - X2 * X9 * Y9 * 2.0 + X3 * X8 * Y9 * 2.0 + X3 * X9 * Y8 * 2.0 + X8 * X9 * Y3 * 2.0;
    G0[1 + 2 * 9] = Z2 * t2 + Z2 * t3 * 3.0 + Z2 * t4 - Z2 * t5 + Z2 * t6 - Z2 * t7 - Z2 * t8 + Z2 * t9 - Z2 * t10 + X1 * X2 * Z1 * 2.0 + X2 * X3 * Z3 * 2.0 + X1 * X4 * Z5 * 2.0 + X1 * X5 * Z4 * 2.0 - X2 * X4 * Z4 * 2.0 + X4 * X5 * Z1 * 2.0 + X2 * X5 * Z5 * 2.0 - X2 * X6 * Z6 * 2.0 + X3 * X5 * Z6 * 2.0 + X3 * X6 * Z5 * 2.0 + X5 * X6 * Z3 * 2.0 + X1 * X7 * Z8 * 2.0 + X1 * X8 * Z7 * 2.0 - X2 * X7 * Z7 * 2.0 + X7 * X8 * Z1 * 2.0 + X2 * X8 * Z8 * 2.0 - X2 * X9 * Z9 * 2.0 + X3 * X8 * Z9 * 2.0 + X3 * X9 * Z8 * 2.0 + X8 * X9 * Z3 * 2.0;
    G0[1 + 3 * 9] = X2 * t11 * 3.0 + X2 * t12 - X2 * t13 + X2 * t14 - X2 * t15 - X2 * t16 + X2 * t17 - X2 * t18 + X2 * t19 + X1 * Y1 * Y2 * 2.0 + X3 * Y2 * Y3 * 2.0 + X1 * Y4 * Y5 * 2.0 + X4 * Y1 * Y5 * 2.0 - X4 * Y2 * Y4 * 2.0 + X5 * Y1 * Y4 * 2.0 + X5 * Y2 * Y5 * 2.0 + X3 * Y5 * Y6 * 2.0 + X5 * Y3 * Y6 * 2.0 - X6 * Y2 * Y6 * 2.0 + X6 * Y3 * Y5 * 2.0 + X1 * Y7 * Y8 * 2.0 + X7 * Y1 * Y8 * 2.0 - X7 * Y2 * Y7 * 2.0 + X8 * Y1 * Y7 * 2.0 + X8 * Y2 * Y8 * 2.0 + X3 * Y8 * Y9 * 2.0 + X8 * Y3 * Y9 * 2.0 - X9 * Y2 * Y9 * 2.0 + X9 * Y3 * Y8 * 2.0;
    G0[1 + 4 * 9] = X1 * Y1 * Z2 * 2.0 + X1 * Y2 * Z1 * 2.0 + X2 * Y1 * Z1 * 2.0 + X2 * Y2 * Z2 * 6.0 + X2 * Y3 * Z3 * 2.0 + X3 * Y2 * Z3 * 2.0 + X3 * Y3 * Z2 * 2.0 + X1 * Y4 * Z5 * 2.0 + X1 * Y5 * Z4 * 2.0 - X2 * Y4 * Z4 * 2.0 + X4 * Y1 * Z5 * 2.0 - X4 * Y2 * Z4 * 2.0 - X4 * Y4 * Z2 * 2.0 + X4 * Y5 * Z1 * 2.0 + X5 * Y1 * Z4 * 2.0 + X5 * Y4 * Z1 * 2.0 + X2 * Y5 * Z5 * 2.0 + X5 * Y2 * Z5 * 2.0 + X5 * Y5 * Z2 * 2.0 - X2 * Y6 * Z6 * 2.0 + X3 * Y5 * Z6 * 2.0 + X3 * Y6 * Z5 * 2.0 + X5 * Y3 * Z6 * 2.0 + X5 * Y6 * Z3 * 2.0 - X6 * Y2 * Z6 * 2.0 + X6 * Y3 * Z5 * 2.0 + X6 * Y5 * Z3 * 2.0 - X6 * Y6 * Z2 * 2.0 + X1 * Y7 * Z8 * 2.0 + X1 * Y8 * Z7 * 2.0 - X2 * Y7 * Z7 * 2.0 + X7 * Y1 * Z8 * 2.0 - X7 * Y2 * Z7 * 2.0 - X7 * Y7 * Z2 * 2.0 + X7 * Y8 * Z1 * 2.0 + X8 * Y1 * Z7 * 2.0 + X8 * Y7 * Z1 * 2.0 + X2 * Y8 * Z8 * 2.0 + X8 * Y2 * Z8 * 2.0 + X8 * Y8 * Z2 * 2.0 - X2 * Y9 * Z9 * 2.0 + X3 * Y8 * Z9 * 2.0 + X3 * Y9 * Z8 * 2.0 + X8 * Y3 * Z9 * 2.0 + X8 * Y9 * Z3 * 2.0 - X9 * Y2 * Z9 * 2.0 + X9 * Y3 * Z8 * 2.0 + X9 * Y8 * Z3 * 2.0 - X9 * Y9 * Z2 * 2.0;
    G0[1 + 5 * 9] = X2 * t20 + X2 * t21 * 3.0 + X2 * t22 - X2 * t23 + X2 * t24 - X2 * t25 - X2 * t26 + X2 * t27 - X2 * t28 + X1 * Z1 * Z2 * 2.0 + X3 * Z2 * Z3 * 2.0 + X1 * Z4 * Z5 * 2.0 + X4 * Z1 * Z5 * 2.0 - X4 * Z2 * Z4 * 2.0 + X5 * Z1 * Z4 * 2.0 + X5 * Z2 * Z5 * 2.0 + X3 * Z5 * Z6 * 2.0 + X5 * Z3 * Z6 * 2.0 - X6 * Z2 * Z6 * 2.0 + X6 * Z3 * Z5 * 2.0 + X1 * Z7 * Z8 * 2.0 + X7 * Z1 * Z8 * 2.0 - X7 * Z2 * Z7 * 2.0 + X8 * Z1 * Z7 * 2.0 + X8 * Z2 * Z8 * 2.0 + X3 * Z8 * Z9 * 2.0 + X8 * Z3 * Z9 * 2.0 - X9 * Z2 * Z9 * 2.0 + X9 * Z3 * Z8 * 2.0;
    G0[1 + 6 * 9] = Y2 * t11 + Y2 * t12 - Y2 * t13 + Y2 * t14 - Y2 * t15 - Y2 * t16 + Y2 * t17 - Y2 * t18 + Y2 * t19 + Y1 * Y4 * Y5 * 2.0 + Y3 * Y5 * Y6 * 2.0 + Y1 * Y7 * Y8 * 2.0 + Y3 * Y8 * Y9 * 2.0;
    G0[1 + 7 * 9] = Z2 * t11 * 3.0 + Z2 * t12 - Z2 * t13 + Z2 * t14 - Z2 * t15 - Z2 * t16 + Z2 * t17 - Z2 * t18 + Z2 * t19 + Y1 * Y2 * Z1 * 2.0 + Y2 * Y3 * Z3 * 2.0 + Y1 * Y4 * Z5 * 2.0 + Y1 * Y5 * Z4 * 2.0 - Y2 * Y4 * Z4 * 2.0 + Y4 * Y5 * Z1 * 2.0 + Y2 * Y5 * Z5 * 2.0 - Y2 * Y6 * Z6 * 2.0 + Y3 * Y5 * Z6 * 2.0 + Y3 * Y6 * Z5 * 2.0 + Y5 * Y6 * Z3 * 2.0 + Y1 * Y7 * Z8 * 2.0 + Y1 * Y8 * Z7 * 2.0 - Y2 * Y7 * Z7 * 2.0 + Y7 * Y8 * Z1 * 2.0 + Y2 * Y8 * Z8 * 2.0 - Y2 * Y9 * Z9 * 2.0 + Y3 * Y8 * Z9 * 2.0 + Y3 * Y9 * Z8 * 2.0 + Y8 * Y9 * Z3 * 2.0;
    G0[1 + 8 * 9] = Y2 * t20 + Y2 * t21 * 3.0 + Y2 * t22 - Y2 * t23 + Y2 * t24 - Y2 * t25 - Y2 * t26 + Y2 * t27 - Y2 * t28 + Y1 * Z1 * Z2 * 2.0 + Y3 * Z2 * Z3 * 2.0 + Y1 * Z4 * Z5 * 2.0 + Y4 * Z1 * Z5 * 2.0 - Y4 * Z2 * Z4 * 2.0 + Y5 * Z1 * Z4 * 2.0 + Y5 * Z2 * Z5 * 2.0 + Y3 * Z5 * Z6 * 2.0 + Y5 * Z3 * Z6 * 2.0 - Y6 * Z2 * Z6 * 2.0 + Y6 * Z3 * Z5 * 2.0 + Y1 * Z7 * Z8 * 2.0 + Y7 * Z1 * Z8 * 2.0 - Y7 * Z2 * Z7 * 2.0 + Y8 * Z1 * Z7 * 2.0 + Y8 * Z2 * Z8 * 2.0 + Y3 * Z8 * Z9 * 2.0 + Y8 * Z3 * Z9 * 2.0 - Y9 * Z2 * Z9 * 2.0 + Y9 * Z3 * Z8 * 2.0;
    G0[1 + 9 * 9] = Z2 * t20 + Z2 * t21 + Z2 * t22 - Z2 * t23 + Z2 * t24 - Z2 * t25 - Z2 * t26 + Z2 * t27 - Z2 * t28 + Z1 * Z4 * Z5 * 2.0 + Z3 * Z5 * Z6 * 2.0 + Z1 * Z7 * Z8 * 2.0 + Z3 * Z8 * Z9 * 2.0;

    G0[2 + 0 * 9] = X3 * t2 + X3 * t3 + X3 * t4 - X3 * t5 - X3 * t6 + X3 * t7 - X3 * t8 - X3 * t9 + X3 * t10 + X1 * X4 * X6 * 2.0 + X2 * X5 * X6 * 2.0 + X1 * X7 * X9 * 2.0 + X2 * X8 * X9 * 2.0;
    G0[2 + 1 * 9] = Y3 * t2 + Y3 * t3 + Y3 * t4 * 3.0 - Y3 * t5 - Y3 * t6 + Y3 * t7 - Y3 * t8 - Y3 * t9 + Y3 * t10 + X1 * X3 * Y1 * 2.0 + X2 * X3 * Y2 * 2.0 + X1 * X4 * Y6 * 2.0 + X1 * X6 * Y4 * 2.0 - X3 * X4 * Y4 * 2.0 + X4 * X6 * Y1 * 2.0 + X2 * X5 * Y6 * 2.0 + X2 * X6 * Y5 * 2.0 - X3 * X5 * Y5 * 2.0 + X5 * X6 * Y2 * 2.0 + X3 * X6 * Y6 * 2.0 + X1 * X7 * Y9 * 2.0 + X1 * X9 * Y7 * 2.0 - X3 * X7 * Y7 * 2.0 + X7 * X9 * Y1 * 2.0 + X2 * X8 * Y9 * 2.0 + X2 * X9 * Y8 * 2.0 - X3 * X8 * Y8 * 2.0 + X8 * X9 * Y2 * 2.0 + X3 * X9 * Y9 * 2.0;
    G0[2 + 2 * 9] = Z3 * t2 + Z3 * t3 + Z3 * t4 * 3.0 - Z3 * t5 - Z3 * t6 + Z3 * t7 - Z3 * t8 - Z3 * t9 + Z3 * t10 + X1 * X3 * Z1 * 2.0 + X2 * X3 * Z2 * 2.0 + X1 * X4 * Z6 * 2.0 + X1 * X6 * Z4 * 2.0 - X3 * X4 * Z4 * 2.0 + X4 * X6 * Z1 * 2.0 + X2 * X5 * Z6 * 2.0 + X2 * X6 * Z5 * 2.0 - X3 * X5 * Z5 * 2.0 + X5 * X6 * Z2 * 2.0 + X3 * X6 * Z6 * 2.0 + X1 * X7 * Z9 * 2.0 + X1 * X9 * Z7 * 2.0 - X3 * X7 * Z7 * 2.0 + X7 * X9 * Z1 * 2.0 + X2 * X8 * Z9 * 2.0 + X2 * X9 * Z8 * 2.0 - X3 * X8 * Z8 * 2.0 + X8 * X9 * Z2 * 2.0 + X3 * X9 * Z9 * 2.0;
    G0[2 + 3 * 9] = X3 * t11 + X3 * t12 * 3.0 - X3 * t13 - X3 * t14 + X3 * t15 - X3 * t16 - X3 * t17 + X3 * t18 + X3 * t19 + X1 * Y1 * Y3 * 2.0 + X2 * Y2 * Y3 * 2.0 + X1 * Y4 * Y6 * 2.0 + X4 * Y1 * Y6 * 2.0 - X4 * Y3 * Y4 * 2.0 + X6 * Y1 * Y4 * 2.0 + X2 * Y5 * Y6 * 2.0 + X5 * Y2 * Y6 * 2.0 - X5 * Y3 * Y5 * 2.0 + X6 * Y2 * Y5 * 2.0 + X6 * Y3 * Y6 * 2.0 + X1 * Y7 * Y9 * 2.0 + X7 * Y1 * Y9 * 2.0 - X7 * Y3 * Y7 * 2.0 + X9 * Y1 * Y7 * 2.0 + X2 * Y8 * Y9 * 2.0 + X8 * Y2 * Y9 * 2.0 - X8 * Y3 * Y8 * 2.0 + X9 * Y2 * Y8 * 2.0 + X9 * Y3 * Y9 * 2.0;
    G0[2 + 4 * 9] = X1 * Y1 * Z3 * 2.0 + X1 * Y3 * Z1 * 2.0 + X3 * Y1 * Z1 * 2.0 + X2 * Y2 * Z3 * 2.0 + X2 * Y3 * Z2 * 2.0 + X3 * Y2 * Z2 * 2.0 + X3 * Y3 * Z3 * 6.0 + X1 * Y4 * Z6 * 2.0 + X1 * Y6 * Z4 * 2.0 - X3 * Y4 * Z4 * 2.0 + X4 * Y1 * Z6 * 2.0 - X4 * Y3 * Z4 * 2.0 - X4 * Y4 * Z3 * 2.0 + X4 * Y6 * Z1 * 2.0 + X6 * Y1 * Z4 * 2.0 + X6 * Y4 * Z1 * 2.0 + X2 * Y5 * Z6 * 2.0 + X2 * Y6 * Z5 * 2.0 - X3 * Y5 * Z5 * 2.0 + X5 * Y2 * Z6 * 2.0 - X5 * Y3 * Z5 * 2.0 - X5 * Y5 * Z3 * 2.0 + X5 * Y6 * Z2 * 2.0 + X6 * Y2 * Z5 * 2.0 + X6 * Y5 * Z2 * 2.0 + X3 * Y6 * Z6 * 2.0 + X6 * Y3 * Z6 * 2.0 + X6 * Y6 * Z3 * 2.0 + X1 * Y7 * Z9 * 2.0 + X1 * Y9 * Z7 * 2.0 - X3 * Y7 * Z7 * 2.0 + X7 * Y1 * Z9 * 2.0 - X7 * Y3 * Z7 * 2.0 - X7 * Y7 * Z3 * 2.0 + X7 * Y9 * Z1 * 2.0 + X9 * Y1 * Z7 * 2.0 + X9 * Y7 * Z1 * 2.0 + X2 * Y8 * Z9 * 2.0 + X2 * Y9 * Z8 * 2.0 - X3 * Y8 * Z8 * 2.0 + X8 * Y2 * Z9 * 2.0 - X8 * Y3 * Z8 * 2.0 - X8 * Y8 * Z3 * 2.0 + X8 * Y9 * Z2 * 2.0 + X9 * Y2 * Z8 * 2.0 + X9 * Y8 * Z2 * 2.0 + X3 * Y9 * Z9 * 2.0 + X9 * Y3 * Z9 * 2.0 + X9 * Y9 * Z3 * 2.0;
    G0[2 + 5 * 9] = X3 * t20 + X3 * t21 + X3 * t22 * 3.0 - X3 * t23 - X3 * t24 + X3 * t25 - X3 * t26 - X3 * t27 + X3 * t28 + X1 * Z1 * Z3 * 2.0 + X2 * Z2 * Z3 * 2.0 + X1 * Z4 * Z6 * 2.0 + X4 * Z1 * Z6 * 2.0 - X4 * Z3 * Z4 * 2.0 + X6 * Z1 * Z4 * 2.0 + X2 * Z5 * Z6 * 2.0 + X5 * Z2 * Z6 * 2.0 - X5 * Z3 * Z5 * 2.0 + X6 * Z2 * Z5 * 2.0 + X6 * Z3 * Z6 * 2.0 + X1 * Z7 * Z9 * 2.0 + X7 * Z1 * Z9 * 2.0 - X7 * Z3 * Z7 * 2.0 + X9 * Z1 * Z7 * 2.0 + X2 * Z8 * Z9 * 2.0 + X8 * Z2 * Z9 * 2.0 - X8 * Z3 * Z8 * 2.0 + X9 * Z2 * Z8 * 2.0 + X9 * Z3 * Z9 * 2.0;
    G0[2 + 6 * 9] = Y3 * t11 + Y3 * t12 - Y3 * t13 - Y3 * t14 + Y3 * t15 - Y3 * t16 - Y3 * t17 + Y3 * t18 + Y3 * t19 + Y1 * Y4 * Y6 * 2.0 + Y2 * Y5 * Y6 * 2.0 + Y1 * Y7 * Y9 * 2.0 + Y2 * Y8 * Y9 * 2.0;
    G0[2 + 7 * 9] = Z3 * t11 + Z3 * t12 * 3.0 - Z3 * t13 - Z3 * t14 + Z3 * t15 - Z3 * t16 - Z3 * t17 + Z3 * t18 + Z3 * t19 + Y1 * Y3 * Z1 * 2.0 + Y2 * Y3 * Z2 * 2.0 + Y1 * Y4 * Z6 * 2.0 + Y1 * Y6 * Z4 * 2.0 - Y3 * Y4 * Z4 * 2.0 + Y4 * Y6 * Z1 * 2.0 + Y2 * Y5 * Z6 * 2.0 + Y2 * Y6 * Z5 * 2.0 - Y3 * Y5 * Z5 * 2.0 + Y5 * Y6 * Z2 * 2.0 + Y3 * Y6 * Z6 * 2.0 + Y1 * Y7 * Z9 * 2.0 + Y1 * Y9 * Z7 * 2.0 - Y3 * Y7 * Z7 * 2.0 + Y7 * Y9 * Z1 * 2.0 + Y2 * Y8 * Z9 * 2.0 + Y2 * Y9 * Z8 * 2.0 - Y3 * Y8 * Z8 * 2.0 + Y8 * Y9 * Z2 * 2.0 + Y3 * Y9 * Z9 * 2.0;
    G0[2 + 8 * 9] = Y3 * t20 + Y3 * t21 + Y3 * t22 * 3.0 - Y3 * t23 - Y3 * t24 + Y3 * t25 - Y3 * t26 - Y3 * t27 + Y3 * t28 + Y1 * Z1 * Z3 * 2.0 + Y2 * Z2 * Z3 * 2.0 + Y1 * Z4 * Z6 * 2.0 + Y4 * Z1 * Z6 * 2.0 - Y4 * Z3 * Z4 * 2.0 + Y6 * Z1 * Z4 * 2.0 + Y2 * Z5 * Z6 * 2.0 + Y5 * Z2 * Z6 * 2.0 - Y5 * Z3 * Z5 * 2.0 + Y6 * Z2 * Z5 * 2.0 + Y6 * Z3 * Z6 * 2.0 + Y1 * Z7 * Z9 * 2.0 + Y7 * Z1 * Z9 * 2.0 - Y7 * Z3 * Z7 * 2.0 + Y9 * Z1 * Z7 * 2.0 + Y2 * Z8 * Z9 * 2.0 + Y8 * Z2 * Z9 * 2.0 - Y8 * Z3 * Z8 * 2.0 + Y9 * Z2 * Z8 * 2.0 + Y9 * Z3 * Z9 * 2.0;
    G0[2 + 9 * 9] = Z3 * t20 + Z3 * t21 + Z3 * t22 - Z3 * t23 - Z3 * t24 + Z3 * t25 - Z3 * t26 - Z3 * t27 + Z3 * t28 + Z1 * Z4 * Z6 * 2.0 + Z2 * Z5 * Z6 * 2.0 + Z1 * Z7 * Z9 * 2.0 + Z2 * Z8 * Z9 * 2.0;

    G0[3 + 0 * 9] = X4 * t2 - X4 * t3 - X4 * t4 + X4 * t5 + X4 * t6 + X4 * t7 + X4 * t8 - X4 * t9 - X4 * t10 + X1 * X2 * X5 * 2.0 + X1 * X3 * X6 * 2.0 + X5 * X7 * X8 * 2.0 + X6 * X7 * X9 * 2.0;
    G0[3 + 1 * 9] = Y4 * t2 - Y4 * t3 - Y4 * t4 + Y4 * t5 * 3.0 + Y4 * t6 + Y4 * t7 + Y4 * t8 - Y4 * t9 - Y4 * t10 + X1 * X4 * Y1 * 2.0 + X1 * X2 * Y5 * 2.0 + X1 * X5 * Y2 * 2.0 - X2 * X4 * Y2 * 2.0 + X2 * X5 * Y1 * 2.0 + X1 * X3 * Y6 * 2.0 + X1 * X6 * Y3 * 2.0 - X3 * X4 * Y3 * 2.0 + X3 * X6 * Y1 * 2.0 + X4 * X5 * Y5 * 2.0 + X4 * X6 * Y6 * 2.0 + X4 * X7 * Y7 * 2.0 - X4 * X8 * Y8 * 2.0 + X5 * X7 * Y8 * 2.0 + X5 * X8 * Y7 * 2.0 + X7 * X8 * Y5 * 2.0 - X4 * X9 * Y9 * 2.0 + X6 * X7 * Y9 * 2.0 + X6 * X9 * Y7 * 2.0 + X7 * X9 * Y6 * 2.0;
    G0[3 + 2 * 9] = Z4 * t2 - Z4 * t3 - Z4 * t4 + Z4 * t5 * 3.0 + Z4 * t6 + Z4 * t7 + Z4 * t8 - Z4 * t9 - Z4 * t10 + X1 * X4 * Z1 * 2.0 + X1 * X2 * Z5 * 2.0 + X1 * X5 * Z2 * 2.0 - X2 * X4 * Z2 * 2.0 + X2 * X5 * Z1 * 2.0 + X1 * X3 * Z6 * 2.0 + X1 * X6 * Z3 * 2.0 - X3 * X4 * Z3 * 2.0 + X3 * X6 * Z1 * 2.0 + X4 * X5 * Z5 * 2.0 + X4 * X6 * Z6 * 2.0 + X4 * X7 * Z7 * 2.0 - X4 * X8 * Z8 * 2.0 + X5 * X7 * Z8 * 2.0 + X5 * X8 * Z7 * 2.0 + X7 * X8 * Z5 * 2.0 - X4 * X9 * Z9 * 2.0 + X6 * X7 * Z9 * 2.0 + X6 * X9 * Z7 * 2.0 + X7 * X9 * Z6 * 2.0;
    G0[3 + 3 * 9] = -X4 * t11 - X4 * t12 + X4 * t13 * 3.0 + X4 * t14 + X4 * t15 + X4 * t16 - X4 * t17 - X4 * t18 + X4 * t19 + X1 * Y1 * Y4 * 2.0 + X1 * Y2 * Y5 * 2.0 + X2 * Y1 * Y5 * 2.0 - X2 * Y2 * Y4 * 2.0 + X5 * Y1 * Y2 * 2.0 + X1 * Y3 * Y6 * 2.0 + X3 * Y1 * Y6 * 2.0 - X3 * Y3 * Y4 * 2.0 + X6 * Y1 * Y3 * 2.0 + X5 * Y4 * Y5 * 2.0 + X6 * Y4 * Y6 * 2.0 + X7 * Y4 * Y7 * 2.0 + X5 * Y7 * Y8 * 2.0 + X7 * Y5 * Y8 * 2.0 - X8 * Y4 * Y8 * 2.0 + X8 * Y5 * Y7 * 2.0 + X6 * Y7 * Y9 * 2.0 + X7 * Y6 * Y9 * 2.0 - X9 * Y4 * Y9 * 2.0 + X9 * Y6 * Y7 * 2.0;
    G0[3 + 4 * 9] = X1 * Y1 * Z4 * 2.0 + X1 * Y4 * Z1 * 2.0 + X4 * Y1 * Z1 * 2.0 + X1 * Y2 * Z5 * 2.0 + X1 * Y5 * Z2 * 2.0 + X2 * Y1 * Z5 * 2.0 - X2 * Y2 * Z4 * 2.0 - X2 * Y4 * Z2 * 2.0 + X2 * Y5 * Z1 * 2.0 - X4 * Y2 * Z2 * 2.0 + X5 * Y1 * Z2 * 2.0 + X5 * Y2 * Z1 * 2.0 + X1 * Y3 * Z6 * 2.0 + X1 * Y6 * Z3 * 2.0 + X3 * Y1 * Z6 * 2.0 - X3 * Y3 * Z4 * 2.0 - X3 * Y4 * Z3 * 2.0 + X3 * Y6 * Z1 * 2.0 - X4 * Y3 * Z3 * 2.0 + X6 * Y1 * Z3 * 2.0 + X6 * Y3 * Z1 * 2.0 + X4 * Y4 * Z4 * 6.0 + X4 * Y5 * Z5 * 2.0 + X5 * Y4 * Z5 * 2.0 + X5 * Y5 * Z4 * 2.0 + X4 * Y6 * Z6 * 2.0 + X6 * Y4 * Z6 * 2.0 + X6 * Y6 * Z4 * 2.0 + X4 * Y7 * Z7 * 2.0 + X7 * Y4 * Z7 * 2.0 + X7 * Y7 * Z4 * 2.0 - X4 * Y8 * Z8 * 2.0 + X5 * Y7 * Z8 * 2.0 + X5 * Y8 * Z7 * 2.0 + X7 * Y5 * Z8 * 2.0 + X7 * Y8 * Z5 * 2.0 - X8 * Y4 * Z8 * 2.0 + X8 * Y5 * Z7 * 2.0 + X8 * Y7 * Z5 * 2.0 - X8 * Y8 * Z4 * 2.0 - X4 * Y9 * Z9 * 2.0 + X6 * Y7 * Z9 * 2.0 + X6 * Y9 * Z7 * 2.0 + X7 * Y6 * Z9 * 2.0 + X7 * Y9 * Z6 * 2.0 - X9 * Y4 * Z9 * 2.0 + X9 * Y6 * Z7 * 2.0 + X9 * Y7 * Z6 * 2.0 - X9 * Y9 * Z4 * 2.0;
    G0[3 + 5 * 9] = X4 * t20 - X4 * t21 - X4 * t22 + X4 * t23 * 3.0 + X4 * t24 + X4 * t25 + X4 * t26 - X4 * t27 - X4 * t28 + X1 * Z1 * Z4 * 2.0 + X1 * Z2 * Z5 * 2.0 + X2 * Z1 * Z5 * 2.0 - X2 * Z2 * Z4 * 2.0 + X5 * Z1 * Z2 * 2.0 + X1 * Z3 * Z6 * 2.0 + X3 * Z1 * Z6 * 2.0 - X3 * Z3 * Z4 * 2.0 + X6 * Z1 * Z3 * 2.0 + X5 * Z4 * Z5 * 2.0 + X6 * Z4 * Z6 * 2.0 + X7 * Z4 * Z7 * 2.0 + X5 * Z7 * Z8 * 2.0 + X7 * Z5 * Z8 * 2.0 - X8 * Z4 * Z8 * 2.0 + X8 * Z5 * Z7 * 2.0 + X6 * Z7 * Z9 * 2.0 + X7 * Z6 * Z9 * 2.0 - X9 * Z4 * Z9 * 2.0 + X9 * Z6 * Z7 * 2.0;
    G0[3 + 6 * 9] = -Y4 * t11 - Y4 * t12 + Y4 * t13 + Y4 * t14 + Y4 * t15 + Y4 * t16 - Y4 * t17 - Y4 * t18 + Y4 * t19 + Y1 * Y2 * Y5 * 2.0 + Y1 * Y3 * Y6 * 2.0 + Y5 * Y7 * Y8 * 2.0 + Y6 * Y7 * Y9 * 2.0;
    G0[3 + 7 * 9] = -Z4 * t11 - Z4 * t12 + Z4 * t13 * 3.0 + Z4 * t14 + Z4 * t15 + Z4 * t16 - Z4 * t17 - Z4 * t18 + Z4 * t19 + Y1 * Y4 * Z1 * 2.0 + Y1 * Y2 * Z5 * 2.0 + Y1 * Y5 * Z2 * 2.0 - Y2 * Y4 * Z2 * 2.0 + Y2 * Y5 * Z1 * 2.0 + Y1 * Y3 * Z6 * 2.0 + Y1 * Y6 * Z3 * 2.0 - Y3 * Y4 * Z3 * 2.0 + Y3 * Y6 * Z1 * 2.0 + Y4 * Y5 * Z5 * 2.0 + Y4 * Y6 * Z6 * 2.0 + Y4 * Y7 * Z7 * 2.0 - Y4 * Y8 * Z8 * 2.0 + Y5 * Y7 * Z8 * 2.0 + Y5 * Y8 * Z7 * 2.0 + Y7 * Y8 * Z5 * 2.0 - Y4 * Y9 * Z9 * 2.0 + Y6 * Y7 * Z9 * 2.0 + Y6 * Y9 * Z7 * 2.0 + Y7 * Y9 * Z6 * 2.0;
    G0[3 + 8 * 9] = Y4 * t20 - Y4 * t21 - Y4 * t22 + Y4 * t23 * 3.0 + Y4 * t24 + Y4 * t25 + Y4 * t26 - Y4 * t27 - Y4 * t28 + Y1 * Z1 * Z4 * 2.0 + Y1 * Z2 * Z5 * 2.0 + Y2 * Z1 * Z5 * 2.0 - Y2 * Z2 * Z4 * 2.0 + Y5 * Z1 * Z2 * 2.0 + Y1 * Z3 * Z6 * 2.0 + Y3 * Z1 * Z6 * 2.0 - Y3 * Z3 * Z4 * 2.0 + Y6 * Z1 * Z3 * 2.0 + Y5 * Z4 * Z5 * 2.0 + Y6 * Z4 * Z6 * 2.0 + Y7 * Z4 * Z7 * 2.0 + Y5 * Z7 * Z8 * 2.0 + Y7 * Z5 * Z8 * 2.0 - Y8 * Z4 * Z8 * 2.0 + Y8 * Z5 * Z7 * 2.0 + Y6 * Z7 * Z9 * 2.0 + Y7 * Z6 * Z9 * 2.0 - Y9 * Z4 * Z9 * 2.0 + Y9 * Z6 * Z7 * 2.0;
    G0[3 + 9 * 9] = Z4 * t20 - Z4 * t21 - Z4 * t22 + Z4 * t23 + Z4 * t24 + Z4 * t25 + Z4 * t26 - Z4 * t27 - Z4 * t28 + Z1 * Z2 * Z5 * 2.0 + Z1 * Z3 * Z6 * 2.0 + Z5 * Z7 * Z8 * 2.0 + Z6 * Z7 * Z9 * 2.0;

    G0[4 + 0 * 9] = -X5 * t2 + X5 * t3 - X5 * t4 + X5 * t5 + X5 * t6 + X5 * t7 - X5 * t8 + X5 * t9 - X5 * t10 + X1 * X2 * X4 * 2.0 + X2 * X3 * X6 * 2.0 + X4 * X7 * X8 * 2.0 + X6 * X8 * X9 * 2.0;
    G0[4 + 1 * 9] = -Y5 * t2 + Y5 * t3 - Y5 * t4 + Y5 * t5 + Y5 * t6 * 3.0 + Y5 * t7 - Y5 * t8 + Y5 * t9 - Y5 * t10 + X1 * X2 * Y4 * 2.0 + X1 * X4 * Y2 * 2.0 - X1 * X5 * Y1 * 2.0 + X2 * X4 * Y1 * 2.0 + X2 * X5 * Y2 * 2.0 + X2 * X3 * Y6 * 2.0 + X2 * X6 * Y3 * 2.0 - X3 * X5 * Y3 * 2.0 + X3 * X6 * Y2 * 2.0 + X4 * X5 * Y4 * 2.0 + X5 * X6 * Y6 * 2.0 + X4 * X7 * Y8 * 2.0 + X4 * X8 * Y7 * 2.0 - X5 * X7 * Y7 * 2.0 + X7 * X8 * Y4 * 2.0 + X5 * X8 * Y8 * 2.0 - X5 * X9 * Y9 * 2.0 + X6 * X8 * Y9 * 2.0 + X6 * X9 * Y8 * 2.0 + X8 * X9 * Y6 * 2.0;
    G0[4 + 2 * 9] = -Z5 * t2 + Z5 * t3 - Z5 * t4 + Z5 * t5 + Z5 * t6 * 3.0 + Z5 * t7 - Z5 * t8 + Z5 * t9 - Z5 * t10 + X1 * X2 * Z4 * 2.0 + X1 * X4 * Z2 * 2.0 - X1 * X5 * Z1 * 2.0 + X2 * X4 * Z1 * 2.0 + X2 * X5 * Z2 * 2.0 + X2 * X3 * Z6 * 2.0 + X2 * X6 * Z3 * 2.0 - X3 * X5 * Z3 * 2.0 + X3 * X6 * Z2 * 2.0 + X4 * X5 * Z4 * 2.0 + X5 * X6 * Z6 * 2.0 + X4 * X7 * Z8 * 2.0 + X4 * X8 * Z7 * 2.0 - X5 * X7 * Z7 * 2.0 + X7 * X8 * Z4 * 2.0 + X5 * X8 * Z8 * 2.0 - X5 * X9 * Z9 * 2.0 + X6 * X8 * Z9 * 2.0 + X6 * X9 * Z8 * 2.0 + X8 * X9 * Z6 * 2.0;
    G0[4 + 3 * 9] = X5 * t11 - X5 * t12 + X5 * t13 + X5 * t14 * 3.0 + X5 * t15 - X5 * t16 + X5 * t17 - X5 * t18 - X5 * t19 - X1 * Y1 * Y5 * 2.0 + X1 * Y2 * Y4 * 2.0 + X2 * Y1 * Y4 * 2.0 + X4 * Y1 * Y2 * 2.0 + X2 * Y2 * Y5 * 2.0 + X2 * Y3 * Y6 * 2.0 + X3 * Y2 * Y6 * 2.0 - X3 * Y3 * Y5 * 2.0 + X6 * Y2 * Y3 * 2.0 + X4 * Y4 * Y5 * 2.0 + X6 * Y5 * Y6 * 2.0 + X4 * Y7 * Y8 * 2.0 + X7 * Y4 * Y8 * 2.0 - X7 * Y5 * Y7 * 2.0 + X8 * Y4 * Y7 * 2.0 + X8 * Y5 * Y8 * 2.0 + X6 * Y8 * Y9 * 2.0 + X8 * Y6 * Y9 * 2.0 - X9 * Y5 * Y9 * 2.0 + X9 * Y6 * Y8 * 2.0;
    G0[4 + 4 * 9] = X1 * Y1 * Z5 * -2.0 + X1 * Y2 * Z4 * 2.0 + X1 * Y4 * Z2 * 2.0 - X1 * Y5 * Z1 * 2.0 + X2 * Y1 * Z4 * 2.0 + X2 * Y4 * Z1 * 2.0 + X4 * Y1 * Z2 * 2.0 + X4 * Y2 * Z1 * 2.0 - X5 * Y1 * Z1 * 2.0 + X2 * Y2 * Z5 * 2.0 + X2 * Y5 * Z2 * 2.0 + X5 * Y2 * Z2 * 2.0 + X2 * Y3 * Z6 * 2.0 + X2 * Y6 * Z3 * 2.0 + X3 * Y2 * Z6 * 2.0 - X3 * Y3 * Z5 * 2.0 - X3 * Y5 * Z3 * 2.0 + X3 * Y6 * Z2 * 2.0 - X5 * Y3 * Z3 * 2.0 + X6 * Y2 * Z3 * 2.0 + X6 * Y3 * Z2 * 2.0 + X4 * Y4 * Z5 * 2.0 + X4 * Y5 * Z4 * 2.0 + X5 * Y4 * Z4 * 2.0 + X5 * Y5 * Z5 * 6.0 + X5 * Y6 * Z6 * 2.0 + X6 * Y5 * Z6 * 2.0 + X6 * Y6 * Z5 * 2.0 + X4 * Y7 * Z8 * 2.0 + X4 * Y8 * Z7 * 2.0 - X5 * Y7 * Z7 * 2.0 + X7 * Y4 * Z8 * 2.0 - X7 * Y5 * Z7 * 2.0 - X7 * Y7 * Z5 * 2.0 + X7 * Y8 * Z4 * 2.0 + X8 * Y4 * Z7 * 2.0 + X8 * Y7 * Z4 * 2.0 + X5 * Y8 * Z8 * 2.0 + X8 * Y5 * Z8 * 2.0 + X8 * Y8 * Z5 * 2.0 - X5 * Y9 * Z9 * 2.0 + X6 * Y8 * Z9 * 2.0 + X6 * Y9 * Z8 * 2.0 + X8 * Y6 * Z9 * 2.0 + X8 * Y9 * Z6 * 2.0 - X9 * Y5 * Z9 * 2.0 + X9 * Y6 * Z8 * 2.0 + X9 * Y8 * Z6 * 2.0 - X9 * Y9 * Z5 * 2.0;
    G0[4 + 5 * 9] = -X5 * t20 + X5 * t21 - X5 * t22 + X5 * t23 + X5 * t24 * 3.0 + X5 * t25 - X5 * t26 + X5 * t27 - X5 * t28 - X1 * Z1 * Z5 * 2.0 + X1 * Z2 * Z4 * 2.0 + X2 * Z1 * Z4 * 2.0 + X4 * Z1 * Z2 * 2.0 + X2 * Z2 * Z5 * 2.0 + X2 * Z3 * Z6 * 2.0 + X3 * Z2 * Z6 * 2.0 - X3 * Z3 * Z5 * 2.0 + X6 * Z2 * Z3 * 2.0 + X4 * Z4 * Z5 * 2.0 + X6 * Z5 * Z6 * 2.0 + X4 * Z7 * Z8 * 2.0 + X7 * Z4 * Z8 * 2.0 - X7 * Z5 * Z7 * 2.0 + X8 * Z4 * Z7 * 2.0 + X8 * Z5 * Z8 * 2.0 + X6 * Z8 * Z9 * 2.0 + X8 * Z6 * Z9 * 2.0 - X9 * Z5 * Z9 * 2.0 + X9 * Z6 * Z8 * 2.0;
    G0[4 + 6 * 9] = Y5 * t11 - Y5 * t12 + Y5 * t13 + Y5 * t14 + Y5 * t15 - Y5 * t16 + Y5 * t17 - Y5 * t18 - Y5 * t19 + Y1 * Y2 * Y4 * 2.0 + Y2 * Y3 * Y6 * 2.0 + Y4 * Y7 * Y8 * 2.0 + Y6 * Y8 * Y9 * 2.0;
    G0[4 + 7 * 9] = Z5 * t11 - Z5 * t12 + Z5 * t13 + Z5 * t14 * 3.0 + Z5 * t15 - Z5 * t16 + Z5 * t17 - Z5 * t18 - Z5 * t19 + Y1 * Y2 * Z4 * 2.0 + Y1 * Y4 * Z2 * 2.0 - Y1 * Y5 * Z1 * 2.0 + Y2 * Y4 * Z1 * 2.0 + Y2 * Y5 * Z2 * 2.0 + Y2 * Y3 * Z6 * 2.0 + Y2 * Y6 * Z3 * 2.0 - Y3 * Y5 * Z3 * 2.0 + Y3 * Y6 * Z2 * 2.0 + Y4 * Y5 * Z4 * 2.0 + Y5 * Y6 * Z6 * 2.0 + Y4 * Y7 * Z8 * 2.0 + Y4 * Y8 * Z7 * 2.0 - Y5 * Y7 * Z7 * 2.0 + Y7 * Y8 * Z4 * 2.0 + Y5 * Y8 * Z8 * 2.0 - Y5 * Y9 * Z9 * 2.0 + Y6 * Y8 * Z9 * 2.0 + Y6 * Y9 * Z8 * 2.0 + Y8 * Y9 * Z6 * 2.0;
    G0[4 + 8 * 9] = -Y5 * t20 + Y5 * t21 - Y5 * t22 + Y5 * t23 + Y5 * t24 * 3.0 + Y5 * t25 - Y5 * t26 + Y5 * t27 - Y5 * t28 - Y1 * Z1 * Z5 * 2.0 + Y1 * Z2 * Z4 * 2.0 + Y2 * Z1 * Z4 * 2.0 + Y4 * Z1 * Z2 * 2.0 + Y2 * Z2 * Z5 * 2.0 + Y2 * Z3 * Z6 * 2.0 + Y3 * Z2 * Z6 * 2.0 - Y3 * Z3 * Z5 * 2.0 + Y6 * Z2 * Z3 * 2.0 + Y4 * Z4 * Z5 * 2.0 + Y6 * Z5 * Z6 * 2.0 + Y4 * Z7 * Z8 * 2.0 + Y7 * Z4 * Z8 * 2.0 - Y7 * Z5 * Z7 * 2.0 + Y8 * Z4 * Z7 * 2.0 + Y8 * Z5 * Z8 * 2.0 + Y6 * Z8 * Z9 * 2.0 + Y8 * Z6 * Z9 * 2.0 - Y9 * Z5 * Z9 * 2.0 + Y9 * Z6 * Z8 * 2.0;
    G0[4 + 9 * 9] = -Z5 * t20 + Z5 * t21 - Z5 * t22 + Z5 * t23 + Z5 * t24 + Z5 * t25 - Z5 * t26 + Z5 * t27 - Z5 * t28 + Z1 * Z2 * Z4 * 2.0 + Z2 * Z3 * Z6 * 2.0 + Z4 * Z7 * Z8 * 2.0 + Z6 * Z8 * Z9 * 2.0;

    G0[5 + 0 * 9] = -X6 * t2 - X6 * t3 + X6 * t4 + X6 * t5 + X6 * t6 + X6 * t7 - X6 * t8 - X6 * t9 + X6 * t10 + X1 * X3 * X4 * 2.0 + X2 * X3 * X5 * 2.0 + X4 * X7 * X9 * 2.0 + X5 * X8 * X9 * 2.0;
    G0[5 + 1 * 9] = -Y6 * t2 - Y6 * t3 + Y6 * t4 + Y6 * t5 + Y6 * t6 + Y6 * t7 * 3.0 - Y6 * t8 - Y6 * t9 + Y6 * t10 + X1 * X3 * Y4 * 2.0 + X1 * X4 * Y3 * 2.0 - X1 * X6 * Y1 * 2.0 + X3 * X4 * Y1 * 2.0 + X2 * X3 * Y5 * 2.0 + X2 * X5 * Y3 * 2.0 - X2 * X6 * Y2 * 2.0 + X3 * X5 * Y2 * 2.0 + X3 * X6 * Y3 * 2.0 + X4 * X6 * Y4 * 2.0 + X5 * X6 * Y5 * 2.0 + X4 * X7 * Y9 * 2.0 + X4 * X9 * Y7 * 2.0 - X6 * X7 * Y7 * 2.0 + X7 * X9 * Y4 * 2.0 + X5 * X8 * Y9 * 2.0 + X5 * X9 * Y8 * 2.0 - X6 * X8 * Y8 * 2.0 + X8 * X9 * Y5 * 2.0 + X6 * X9 * Y9 * 2.0;
    G0[5 + 2 * 9] = -Z6 * t2 - Z6 * t3 + Z6 * t4 + Z6 * t5 + Z6 * t6 + Z6 * t7 * 3.0 - Z6 * t8 - Z6 * t9 + Z6 * t10 + X1 * X3 * Z4 * 2.0 + X1 * X4 * Z3 * 2.0 - X1 * X6 * Z1 * 2.0 + X3 * X4 * Z1 * 2.0 + X2 * X3 * Z5 * 2.0 + X2 * X5 * Z3 * 2.0 - X2 * X6 * Z2 * 2.0 + X3 * X5 * Z2 * 2.0 + X3 * X6 * Z3 * 2.0 + X4 * X6 * Z4 * 2.0 + X5 * X6 * Z5 * 2.0 + X4 * X7 * Z9 * 2.0 + X4 * X9 * Z7 * 2.0 - X6 * X7 * Z7 * 2.0 + X7 * X9 * Z4 * 2.0 + X5 * X8 * Z9 * 2.0 + X5 * X9 * Z8 * 2.0 - X6 * X8 * Z8 * 2.0 + X8 * X9 * Z5 * 2.0 + X6 * X9 * Z9 * 2.0;
    G0[5 + 3 * 9] = -X6 * t11 + X6 * t12 + X6 * t13 + X6 * t14 + X6 * t15 * 3.0 - X6 * t16 - X6 * t17 + X6 * t18 - X6 * t19 - X1 * Y1 * Y6 * 2.0 + X1 * Y3 * Y4 * 2.0 + X3 * Y1 * Y4 * 2.0 + X4 * Y1 * Y3 * 2.0 - X2 * Y2 * Y6 * 2.0 + X2 * Y3 * Y5 * 2.0 + X3 * Y2 * Y5 * 2.0 + X5 * Y2 * Y3 * 2.0 + X3 * Y3 * Y6 * 2.0 + X4 * Y4 * Y6 * 2.0 + X5 * Y5 * Y6 * 2.0 + X4 * Y7 * Y9 * 2.0 + X7 * Y4 * Y9 * 2.0 - X7 * Y6 * Y7 * 2.0 + X9 * Y4 * Y7 * 2.0 + X5 * Y8 * Y9 * 2.0 + X8 * Y5 * Y9 * 2.0 - X8 * Y6 * Y8 * 2.0 + X9 * Y5 * Y8 * 2.0 + X9 * Y6 * Y9 * 2.0;
    G0[5 + 4 * 9] = X1 * Y1 * Z6 * -2.0 + X1 * Y3 * Z4 * 2.0 + X1 * Y4 * Z3 * 2.0 - X1 * Y6 * Z1 * 2.0 + X3 * Y1 * Z4 * 2.0 + X3 * Y4 * Z1 * 2.0 + X4 * Y1 * Z3 * 2.0 + X4 * Y3 * Z1 * 2.0 - X6 * Y1 * Z1 * 2.0 - X2 * Y2 * Z6 * 2.0 + X2 * Y3 * Z5 * 2.0 + X2 * Y5 * Z3 * 2.0 - X2 * Y6 * Z2 * 2.0 + X3 * Y2 * Z5 * 2.0 + X3 * Y5 * Z2 * 2.0 + X5 * Y2 * Z3 * 2.0 + X5 * Y3 * Z2 * 2.0 - X6 * Y2 * Z2 * 2.0 + X3 * Y3 * Z6 * 2.0 + X3 * Y6 * Z3 * 2.0 + X6 * Y3 * Z3 * 2.0 + X4 * Y4 * Z6 * 2.0 + X4 * Y6 * Z4 * 2.0 + X6 * Y4 * Z4 * 2.0 + X5 * Y5 * Z6 * 2.0 + X5 * Y6 * Z5 * 2.0 + X6 * Y5 * Z5 * 2.0 + X6 * Y6 * Z6 * 6.0 + X4 * Y7 * Z9 * 2.0 + X4 * Y9 * Z7 * 2.0 - X6 * Y7 * Z7 * 2.0 + X7 * Y4 * Z9 * 2.0 - X7 * Y6 * Z7 * 2.0 - X7 * Y7 * Z6 * 2.0 + X7 * Y9 * Z4 * 2.0 + X9 * Y4 * Z7 * 2.0 + X9 * Y7 * Z4 * 2.0 + X5 * Y8 * Z9 * 2.0 + X5 * Y9 * Z8 * 2.0 - X6 * Y8 * Z8 * 2.0 + X8 * Y5 * Z9 * 2.0 - X8 * Y6 * Z8 * 2.0 - X8 * Y8 * Z6 * 2.0 + X8 * Y9 * Z5 * 2.0 + X9 * Y5 * Z8 * 2.0 + X9 * Y8 * Z5 * 2.0 + X6 * Y9 * Z9 * 2.0 + X9 * Y6 * Z9 * 2.0 + X9 * Y9 * Z6 * 2.0;
    G0[5 + 5 * 9] = -X6 * t20 - X6 * t21 + X6 * t22 + X6 * t23 + X6 * t24 + X6 * t25 * 3.0 - X6 * t26 - X6 * t27 + X6 * t28 - X1 * Z1 * Z6 * 2.0 + X1 * Z3 * Z4 * 2.0 + X3 * Z1 * Z4 * 2.0 + X4 * Z1 * Z3 * 2.0 - X2 * Z2 * Z6 * 2.0 + X2 * Z3 * Z5 * 2.0 + X3 * Z2 * Z5 * 2.0 + X5 * Z2 * Z3 * 2.0 + X3 * Z3 * Z6 * 2.0 + X4 * Z4 * Z6 * 2.0 + X5 * Z5 * Z6 * 2.0 + X4 * Z7 * Z9 * 2.0 + X7 * Z4 * Z9 * 2.0 - X7 * Z6 * Z7 * 2.0 + X9 * Z4 * Z7 * 2.0 + X5 * Z8 * Z9 * 2.0 + X8 * Z5 * Z9 * 2.0 - X8 * Z6 * Z8 * 2.0 + X9 * Z5 * Z8 * 2.0 + X9 * Z6 * Z9 * 2.0;
    G0[5 + 6 * 9] = -Y6 * t11 + Y6 * t12 + Y6 * t13 + Y6 * t14 + Y6 * t15 - Y6 * t16 - Y6 * t17 + Y6 * t18 - Y6 * t19 + Y1 * Y3 * Y4 * 2.0 + Y2 * Y3 * Y5 * 2.0 + Y4 * Y7 * Y9 * 2.0 + Y5 * Y8 * Y9 * 2.0;
    G0[5 + 7 * 9] = -Z6 * t11 + Z6 * t12 + Z6 * t13 + Z6 * t14 + Z6 * t15 * 3.0 - Z6 * t16 - Z6 * t17 + Z6 * t18 - Z6 * t19 + Y1 * Y3 * Z4 * 2.0 + Y1 * Y4 * Z3 * 2.0 - Y1 * Y6 * Z1 * 2.0 + Y3 * Y4 * Z1 * 2.0 + Y2 * Y3 * Z5 * 2.0 + Y2 * Y5 * Z3 * 2.0 - Y2 * Y6 * Z2 * 2.0 + Y3 * Y5 * Z2 * 2.0 + Y3 * Y6 * Z3 * 2.0 + Y4 * Y6 * Z4 * 2.0 + Y5 * Y6 * Z5 * 2.0 + Y4 * Y7 * Z9 * 2.0 + Y4 * Y9 * Z7 * 2.0 - Y6 * Y7 * Z7 * 2.0 + Y7 * Y9 * Z4 * 2.0 + Y5 * Y8 * Z9 * 2.0 + Y5 * Y9 * Z8 * 2.0 - Y6 * Y8 * Z8 * 2.0 + Y8 * Y9 * Z5 * 2.0 + Y6 * Y9 * Z9 * 2.0;
    G0[5 + 8 * 9] = -Y6 * t20 - Y6 * t21 + Y6 * t22 + Y6 * t23 + Y6 * t24 + Y6 * t25 * 3.0 - Y6 * t26 - Y6 * t27 + Y6 * t28 - Y1 * Z1 * Z6 * 2.0 + Y1 * Z3 * Z4 * 2.0 + Y3 * Z1 * Z4 * 2.0 + Y4 * Z1 * Z3 * 2.0 - Y2 * Z2 * Z6 * 2.0 + Y2 * Z3 * Z5 * 2.0 + Y3 * Z2 * Z5 * 2.0 + Y5 * Z2 * Z3 * 2.0 + Y3 * Z3 * Z6 * 2.0 + Y4 * Z4 * Z6 * 2.0 + Y5 * Z5 * Z6 * 2.0 + Y4 * Z7 * Z9 * 2.0 + Y7 * Z4 * Z9 * 2.0 - Y7 * Z6 * Z7 * 2.0 + Y9 * Z4 * Z7 * 2.0 + Y5 * Z8 * Z9 * 2.0 + Y8 * Z5 * Z9 * 2.0 - Y8 * Z6 * Z8 * 2.0 + Y9 * Z5 * Z8 * 2.0 + Y9 * Z6 * Z9 * 2.0;
    G0[5 + 9 * 9] = -Z6 * t20 - Z6 * t21 + Z6 * t22 + Z6 * t23 + Z6 * t24 + Z6 * t25 - Z6 * t26 - Z6 * t27 + Z6 * t28 + Z1 * Z3 * Z4 * 2.0 + Z2 * Z3 * Z5 * 2.0 + Z4 * Z7 * Z9 * 2.0 + Z5 * Z8 * Z9 * 2.0;

    G0[6 + 0 * 9] = X7 * t2 - X7 * t3 - X7 * t4 + X7 * t5 - X7 * t6 - X7 * t7 + X7 * t8 + X7 * t9 + X7 * t10 + X1 * X2 * X8 * 2.0 + X1 * X3 * X9 * 2.0 + X4 * X5 * X8 * 2.0 + X4 * X6 * X9 * 2.0;
    G0[6 + 1 * 9] = Y7 * t2 - Y7 * t3 - Y7 * t4 + Y7 * t5 - Y7 * t6 - Y7 * t7 + Y7 * t8 * 3.0 + Y7 * t9 + Y7 * t10 + X1 * X7 * Y1 * 2.0 + X1 * X2 * Y8 * 2.0 + X1 * X8 * Y2 * 2.0 - X2 * X7 * Y2 * 2.0 + X2 * X8 * Y1 * 2.0 + X1 * X3 * Y9 * 2.0 + X1 * X9 * Y3 * 2.0 - X3 * X7 * Y3 * 2.0 + X3 * X9 * Y1 * 2.0 + X4 * X7 * Y4 * 2.0 + X4 * X5 * Y8 * 2.0 + X4 * X8 * Y5 * 2.0 - X5 * X7 * Y5 * 2.0 + X5 * X8 * Y4 * 2.0 + X4 * X6 * Y9 * 2.0 + X4 * X9 * Y6 * 2.0 - X6 * X7 * Y6 * 2.0 + X6 * X9 * Y4 * 2.0 + X7 * X8 * Y8 * 2.0 + X7 * X9 * Y9 * 2.0;
    G0[6 + 2 * 9] = Z7 * t2 - Z7 * t3 - Z7 * t4 + Z7 * t5 - Z7 * t6 - Z7 * t7 + Z7 * t8 * 3.0 + Z7 * t9 + Z7 * t10 + X1 * X7 * Z1 * 2.0 + X1 * X2 * Z8 * 2.0 + X1 * X8 * Z2 * 2.0 - X2 * X7 * Z2 * 2.0 + X2 * X8 * Z1 * 2.0 + X1 * X3 * Z9 * 2.0 + X1 * X9 * Z3 * 2.0 - X3 * X7 * Z3 * 2.0 + X3 * X9 * Z1 * 2.0 + X4 * X7 * Z4 * 2.0 + X4 * X5 * Z8 * 2.0 + X4 * X8 * Z5 * 2.0 - X5 * X7 * Z5 * 2.0 + X5 * X8 * Z4 * 2.0 + X4 * X6 * Z9 * 2.0 + X4 * X9 * Z6 * 2.0 - X6 * X7 * Z6 * 2.0 + X6 * X9 * Z4 * 2.0 + X7 * X8 * Z8 * 2.0 + X7 * X9 * Z9 * 2.0;
    G0[6 + 3 * 9] = -X7 * t11 - X7 * t12 + X7 * t13 - X7 * t14 - X7 * t15 + X7 * t16 * 3.0 + X7 * t17 + X7 * t18 + X7 * t19 + X1 * Y1 * Y7 * 2.0 + X1 * Y2 * Y8 * 2.0 + X2 * Y1 * Y8 * 2.0 - X2 * Y2 * Y7 * 2.0 + X8 * Y1 * Y2 * 2.0 + X1 * Y3 * Y9 * 2.0 + X3 * Y1 * Y9 * 2.0 - X3 * Y3 * Y7 * 2.0 + X9 * Y1 * Y3 * 2.0 + X4 * Y4 * Y7 * 2.0 + X4 * Y5 * Y8 * 2.0 + X5 * Y4 * Y8 * 2.0 - X5 * Y5 * Y7 * 2.0 + X8 * Y4 * Y5 * 2.0 + X4 * Y6 * Y9 * 2.0 + X6 * Y4 * Y9 * 2.0 - X6 * Y6 * Y7 * 2.0 + X9 * Y4 * Y6 * 2.0 + X8 * Y7 * Y8 * 2.0 + X9 * Y7 * Y9 * 2.0;
    G0[6 + 4 * 9] = X1 * Y1 * Z7 * 2.0 + X1 * Y7 * Z1 * 2.0 + X7 * Y1 * Z1 * 2.0 + X1 * Y2 * Z8 * 2.0 + X1 * Y8 * Z2 * 2.0 + X2 * Y1 * Z8 * 2.0 - X2 * Y2 * Z7 * 2.0 - X2 * Y7 * Z2 * 2.0 + X2 * Y8 * Z1 * 2.0 - X7 * Y2 * Z2 * 2.0 + X8 * Y1 * Z2 * 2.0 + X8 * Y2 * Z1 * 2.0 + X1 * Y3 * Z9 * 2.0 + X1 * Y9 * Z3 * 2.0 + X3 * Y1 * Z9 * 2.0 - X3 * Y3 * Z7 * 2.0 - X3 * Y7 * Z3 * 2.0 + X3 * Y9 * Z1 * 2.0 - X7 * Y3 * Z3 * 2.0 + X9 * Y1 * Z3 * 2.0 + X9 * Y3 * Z1 * 2.0 + X4 * Y4 * Z7 * 2.0 + X4 * Y7 * Z4 * 2.0 + X7 * Y4 * Z4 * 2.0 + X4 * Y5 * Z8 * 2.0 + X4 * Y8 * Z5 * 2.0 + X5 * Y4 * Z8 * 2.0 - X5 * Y5 * Z7 * 2.0 - X5 * Y7 * Z5 * 2.0 + X5 * Y8 * Z4 * 2.0 - X7 * Y5 * Z5 * 2.0 + X8 * Y4 * Z5 * 2.0 + X8 * Y5 * Z4 * 2.0 + X4 * Y6 * Z9 * 2.0 + X4 * Y9 * Z6 * 2.0 + X6 * Y4 * Z9 * 2.0 - X6 * Y6 * Z7 * 2.0 - X6 * Y7 * Z6 * 2.0 + X6 * Y9 * Z4 * 2.0 - X7 * Y6 * Z6 * 2.0 + X9 * Y4 * Z6 * 2.0 + X9 * Y6 * Z4 * 2.0 + X7 * Y7 * Z7 * 6.0 + X7 * Y8 * Z8 * 2.0 + X8 * Y7 * Z8 * 2.0 + X8 * Y8 * Z7 * 2.0 + X7 * Y9 * Z9 * 2.0 + X9 * Y7 * Z9 * 2.0 + X9 * Y9 * Z7 * 2.0;
    G0[6 + 5 * 9] = X7 * t20 - X7 * t21 - X7 * t22 + X7 * t23 - X7 * t24 - X7 * t25 + X7 * t26 * 3.0 + X7 * t27 + X7 * t28 + X1 * Z1 * Z7 * 2.0 + X1 * Z2 * Z8 * 2.0 + X2 * Z1 * Z8 * 2.0 - X2 * Z2 * Z7 * 2.0 + X8 * Z1 * Z2 * 2.0 + X1 * Z3 * Z9 * 2.0 + X3 * Z1 * Z9 * 2.0 - X3 * Z3 * Z7 * 2.0 + X9 * Z1 * Z3 * 2.0 + X4 * Z4 * Z7 * 2.0 + X4 * Z5 * Z8 * 2.0 + X5 * Z4 * Z8 * 2.0 - X5 * Z5 * Z7 * 2.0 + X8 * Z4 * Z5 * 2.0 + X4 * Z6 * Z9 * 2.0 + X6 * Z4 * Z9 * 2.0 - X6 * Z6 * Z7 * 2.0 + X9 * Z4 * Z6 * 2.0 + X8 * Z7 * Z8 * 2.0 + X9 * Z7 * Z9 * 2.0;
    G0[6 + 6 * 9] = -Y7 * t11 - Y7 * t12 + Y7 * t13 - Y7 * t14 - Y7 * t15 + Y7 * t16 + Y7 * t17 + Y7 * t18 + Y7 * t19 + Y1 * Y2 * Y8 * 2.0 + Y1 * Y3 * Y9 * 2.0 + Y4 * Y5 * Y8 * 2.0 + Y4 * Y6 * Y9 * 2.0;
    G0[6 + 7 * 9] = -Z7 * t11 - Z7 * t12 + Z7 * t13 - Z7 * t14 - Z7 * t15 + Z7 * t16 * 3.0 + Z7 * t17 + Z7 * t18 + Z7 * t19 + Y1 * Y7 * Z1 * 2.0 + Y1 * Y2 * Z8 * 2.0 + Y1 * Y8 * Z2 * 2.0 - Y2 * Y7 * Z2 * 2.0 + Y2 * Y8 * Z1 * 2.0 + Y1 * Y3 * Z9 * 2.0 + Y1 * Y9 * Z3 * 2.0 - Y3 * Y7 * Z3 * 2.0 + Y3 * Y9 * Z1 * 2.0 + Y4 * Y7 * Z4 * 2.0 + Y4 * Y5 * Z8 * 2.0 + Y4 * Y8 * Z5 * 2.0 - Y5 * Y7 * Z5 * 2.0 + Y5 * Y8 * Z4 * 2.0 + Y4 * Y6 * Z9 * 2.0 + Y4 * Y9 * Z6 * 2.0 - Y6 * Y7 * Z6 * 2.0 + Y6 * Y9 * Z4 * 2.0 + Y7 * Y8 * Z8 * 2.0 + Y7 * Y9 * Z9 * 2.0;
    G0[6 + 8 * 9] = Y7 * t20 - Y7 * t21 - Y7 * t22 + Y7 * t23 - Y7 * t24 - Y7 * t25 + Y7 * t26 * 3.0 + Y7 * t27 + Y7 * t28 + Y1 * Z1 * Z7 * 2.0 + Y1 * Z2 * Z8 * 2.0 + Y2 * Z1 * Z8 * 2.0 - Y2 * Z2 * Z7 * 2.0 + Y8 * Z1 * Z2 * 2.0 + Y1 * Z3 * Z9 * 2.0 + Y3 * Z1 * Z9 * 2.0 - Y3 * Z3 * Z7 * 2.0 + Y9 * Z1 * Z3 * 2.0 + Y4 * Z4 * Z7 * 2.0 + Y4 * Z5 * Z8 * 2.0 + Y5 * Z4 * Z8 * 2.0 - Y5 * Z5 * Z7 * 2.0 + Y8 * Z4 * Z5 * 2.0 + Y4 * Z6 * Z9 * 2.0 + Y6 * Z4 * Z9 * 2.0 - Y6 * Z6 * Z7 * 2.0 + Y9 * Z4 * Z6 * 2.0 + Y8 * Z7 * Z8 * 2.0 + Y9 * Z7 * Z9 * 2.0;
    G0[6 + 9 * 9] = Z7 * t20 - Z7 * t21 - Z7 * t22 + Z7 * t23 - Z7 * t24 - Z7 * t25 + Z7 * t26 + Z7 * t27 + Z7 * t28 + Z1 * Z2 * Z8 * 2.0 + Z1 * Z3 * Z9 * 2.0 + Z4 * Z5 * Z8 * 2.0 + Z4 * Z6 * Z9 * 2.0;

    G0[7 + 0 * 9] = -X8 * t2 + X8 * t3 - X8 * t4 - X8 * t5 + X8 * t6 - X8 * t7 + X8 * t8 + X8 * t9 + X8 * t10 + X1 * X2 * X7 * 2.0 + X2 * X3 * X9 * 2.0 + X4 * X5 * X7 * 2.0 + X5 * X6 * X9 * 2.0;
    G0[7 + 1 * 9] = -Y8 * t2 + Y8 * t3 - Y8 * t4 - Y8 * t5 + Y8 * t6 - Y8 * t7 + Y8 * t8 + Y8 * t9 * 3.0 + Y8 * t10 + X1 * X2 * Y7 * 2.0 + X1 * X7 * Y2 * 2.0 - X1 * X8 * Y1 * 2.0 + X2 * X7 * Y1 * 2.0 + X2 * X8 * Y2 * 2.0 + X2 * X3 * Y9 * 2.0 + X2 * X9 * Y3 * 2.0 - X3 * X8 * Y3 * 2.0 + X3 * X9 * Y2 * 2.0 + X4 * X5 * Y7 * 2.0 + X4 * X7 * Y5 * 2.0 - X4 * X8 * Y4 * 2.0 + X5 * X7 * Y4 * 2.0 + X5 * X8 * Y5 * 2.0 + X5 * X6 * Y9 * 2.0 + X5 * X9 * Y6 * 2.0 - X6 * X8 * Y6 * 2.0 + X6 * X9 * Y5 * 2.0 + X7 * X8 * Y7 * 2.0 + X8 * X9 * Y9 * 2.0;
    G0[7 + 2 * 9] = -Z8 * t2 + Z8 * t3 - Z8 * t4 - Z8 * t5 + Z8 * t6 - Z8 * t7 + Z8 * t8 + Z8 * t9 * 3.0 + Z8 * t10 + X1 * X2 * Z7 * 2.0 + X1 * X7 * Z2 * 2.0 - X1 * X8 * Z1 * 2.0 + X2 * X7 * Z1 * 2.0 + X2 * X8 * Z2 * 2.0 + X2 * X3 * Z9 * 2.0 + X2 * X9 * Z3 * 2.0 - X3 * X8 * Z3 * 2.0 + X3 * X9 * Z2 * 2.0 + X4 * X5 * Z7 * 2.0 + X4 * X7 * Z5 * 2.0 - X4 * X8 * Z4 * 2.0 + X5 * X7 * Z4 * 2.0 + X5 * X8 * Z5 * 2.0 + X5 * X6 * Z9 * 2.0 + X5 * X9 * Z6 * 2.0 - X6 * X8 * Z6 * 2.0 + X6 * X9 * Z5 * 2.0 + X7 * X8 * Z7 * 2.0 + X8 * X9 * Z9 * 2.0;
    G0[7 + 3 * 9] = X8 * t11 - X8 * t12 - X8 * t13 + X8 * t14 - X8 * t15 + X8 * t16 + X8 * t17 * 3.0 + X8 * t18 - X8 * t19 - X1 * Y1 * Y8 * 2.0 + X1 * Y2 * Y7 * 2.0 + X2 * Y1 * Y7 * 2.0 + X7 * Y1 * Y2 * 2.0 + X2 * Y2 * Y8 * 2.0 + X2 * Y3 * Y9 * 2.0 + X3 * Y2 * Y9 * 2.0 - X3 * Y3 * Y8 * 2.0 + X9 * Y2 * Y3 * 2.0 - X4 * Y4 * Y8 * 2.0 + X4 * Y5 * Y7 * 2.0 + X5 * Y4 * Y7 * 2.0 + X7 * Y4 * Y5 * 2.0 + X5 * Y5 * Y8 * 2.0 + X5 * Y6 * Y9 * 2.0 + X6 * Y5 * Y9 * 2.0 - X6 * Y6 * Y8 * 2.0 + X9 * Y5 * Y6 * 2.0 + X7 * Y7 * Y8 * 2.0 + X9 * Y8 * Y9 * 2.0;
    G0[7 + 4 * 9] = X1 * Y1 * Z8 * -2.0 + X1 * Y2 * Z7 * 2.0 + X1 * Y7 * Z2 * 2.0 - X1 * Y8 * Z1 * 2.0 + X2 * Y1 * Z7 * 2.0 + X2 * Y7 * Z1 * 2.0 + X7 * Y1 * Z2 * 2.0 + X7 * Y2 * Z1 * 2.0 - X8 * Y1 * Z1 * 2.0 + X2 * Y2 * Z8 * 2.0 + X2 * Y8 * Z2 * 2.0 + X8 * Y2 * Z2 * 2.0 + X2 * Y3 * Z9 * 2.0 + X2 * Y9 * Z3 * 2.0 + X3 * Y2 * Z9 * 2.0 - X3 * Y3 * Z8 * 2.0 - X3 * Y8 * Z3 * 2.0 + X3 * Y9 * Z2 * 2.0 - X8 * Y3 * Z3 * 2.0 + X9 * Y2 * Z3 * 2.0 + X9 * Y3 * Z2 * 2.0 - X4 * Y4 * Z8 * 2.0 + X4 * Y5 * Z7 * 2.0 + X4 * Y7 * Z5 * 2.0 - X4 * Y8 * Z4 * 2.0 + X5 * Y4 * Z7 * 2.0 + X5 * Y7 * Z4 * 2.0 + X7 * Y4 * Z5 * 2.0 + X7 * Y5 * Z4 * 2.0 - X8 * Y4 * Z4 * 2.0 + X5 * Y5 * Z8 * 2.0 + X5 * Y8 * Z5 * 2.0 + X8 * Y5 * Z5 * 2.0 + X5 * Y6 * Z9 * 2.0 + X5 * Y9 * Z6 * 2.0 + X6 * Y5 * Z9 * 2.0 - X6 * Y6 * Z8 * 2.0 - X6 * Y8 * Z6 * 2.0 + X6 * Y9 * Z5 * 2.0 - X8 * Y6 * Z6 * 2.0 + X9 * Y5 * Z6 * 2.0 + X9 * Y6 * Z5 * 2.0 + X7 * Y7 * Z8 * 2.0 + X7 * Y8 * Z7 * 2.0 + X8 * Y7 * Z7 * 2.0 + X8 * Y8 * Z8 * 6.0 + X8 * Y9 * Z9 * 2.0 + X9 * Y8 * Z9 * 2.0 + X9 * Y9 * Z8 * 2.0;
    G0[7 + 5 * 9] = -X8 * t20 + X8 * t21 - X8 * t22 - X8 * t23 + X8 * t24 - X8 * t25 + X8 * t26 + X8 * t27 * 3.0 + X8 * t28 - X1 * Z1 * Z8 * 2.0 + X1 * Z2 * Z7 * 2.0 + X2 * Z1 * Z7 * 2.0 + X7 * Z1 * Z2 * 2.0 + X2 * Z2 * Z8 * 2.0 + X2 * Z3 * Z9 * 2.0 + X3 * Z2 * Z9 * 2.0 - X3 * Z3 * Z8 * 2.0 + X9 * Z2 * Z3 * 2.0 - X4 * Z4 * Z8 * 2.0 + X4 * Z5 * Z7 * 2.0 + X5 * Z4 * Z7 * 2.0 + X7 * Z4 * Z5 * 2.0 + X5 * Z5 * Z8 * 2.0 + X5 * Z6 * Z9 * 2.0 + X6 * Z5 * Z9 * 2.0 - X6 * Z6 * Z8 * 2.0 + X9 * Z5 * Z6 * 2.0 + X7 * Z7 * Z8 * 2.0 + X9 * Z8 * Z9 * 2.0;
    G0[7 + 6 * 9] = Y8 * t11 - Y8 * t12 - Y8 * t13 + Y8 * t14 - Y8 * t15 + Y8 * t16 + Y8 * t17 + Y8 * t18 - Y8 * t19 + Y1 * Y2 * Y7 * 2.0 + Y2 * Y3 * Y9 * 2.0 + Y4 * Y5 * Y7 * 2.0 + Y5 * Y6 * Y9 * 2.0;
    G0[7 + 7 * 9] = Z8 * t11 - Z8 * t12 - Z8 * t13 + Z8 * t14 - Z8 * t15 + Z8 * t16 + Z8 * t17 * 3.0 + Z8 * t18 - Z8 * t19 + Y1 * Y2 * Z7 * 2.0 + Y1 * Y7 * Z2 * 2.0 - Y1 * Y8 * Z1 * 2.0 + Y2 * Y7 * Z1 * 2.0 + Y2 * Y8 * Z2 * 2.0 + Y2 * Y3 * Z9 * 2.0 + Y2 * Y9 * Z3 * 2.0 - Y3 * Y8 * Z3 * 2.0 + Y3 * Y9 * Z2 * 2.0 + Y4 * Y5 * Z7 * 2.0 + Y4 * Y7 * Z5 * 2.0 - Y4 * Y8 * Z4 * 2.0 + Y5 * Y7 * Z4 * 2.0 + Y5 * Y8 * Z5 * 2.0 + Y5 * Y6 * Z9 * 2.0 + Y5 * Y9 * Z6 * 2.0 - Y6 * Y8 * Z6 * 2.0 + Y6 * Y9 * Z5 * 2.0 + Y7 * Y8 * Z7 * 2.0 + Y8 * Y9 * Z9 * 2.0;
    G0[7 + 8 * 9] = -Y8 * t20 + Y8 * t21 - Y8 * t22 - Y8 * t23 + Y8 * t24 - Y8 * t25 + Y8 * t26 + Y8 * t27 * 3.0 + Y8 * t28 - Y1 * Z1 * Z8 * 2.0 + Y1 * Z2 * Z7 * 2.0 + Y2 * Z1 * Z7 * 2.0 + Y7 * Z1 * Z2 * 2.0 + Y2 * Z2 * Z8 * 2.0 + Y2 * Z3 * Z9 * 2.0 + Y3 * Z2 * Z9 * 2.0 - Y3 * Z3 * Z8 * 2.0 + Y9 * Z2 * Z3 * 2.0 - Y4 * Z4 * Z8 * 2.0 + Y4 * Z5 * Z7 * 2.0 + Y5 * Z4 * Z7 * 2.0 + Y7 * Z4 * Z5 * 2.0 + Y5 * Z5 * Z8 * 2.0 + Y5 * Z6 * Z9 * 2.0 + Y6 * Z5 * Z9 * 2.0 - Y6 * Z6 * Z8 * 2.0 + Y9 * Z5 * Z6 * 2.0 + Y7 * Z7 * Z8 * 2.0 + Y9 * Z8 * Z9 * 2.0;
    G0[7 + 9 * 9] = -Z8 * t20 + Z8 * t21 - Z8 * t22 - Z8 * t23 + Z8 * t24 - Z8 * t25 + Z8 * t26 + Z8 * t27 + Z8 * t28 + Z1 * Z2 * Z7 * 2.0 + Z2 * Z3 * Z9 * 2.0 + Z4 * Z5 * Z7 * 2.0 + Z5 * Z6 * Z9 * 2.0;

    G0[8 + 0 * 9] = -X9 * t2 - X9 * t3 + X9 * t4 - X9 * t5 - X9 * t6 + X9 * t7 + X9 * t8 + X9 * t9 + X9 * t10 + X1 * X3 * X7 * 2.0 + X2 * X3 * X8 * 2.0 + X4 * X6 * X7 * 2.0 + X5 * X6 * X8 * 2.0;
    G0[8 + 1 * 9] = -Y9 * t2 - Y9 * t3 + Y9 * t4 - Y9 * t5 - Y9 * t6 + Y9 * t7 + Y9 * t8 + Y9 * t9 + Y9 * t10 * 3.0 + X1 * X3 * Y7 * 2.0 + X1 * X7 * Y3 * 2.0 - X1 * X9 * Y1 * 2.0 + X3 * X7 * Y1 * 2.0 + X2 * X3 * Y8 * 2.0 + X2 * X8 * Y3 * 2.0 - X2 * X9 * Y2 * 2.0 + X3 * X8 * Y2 * 2.0 + X3 * X9 * Y3 * 2.0 + X4 * X6 * Y7 * 2.0 + X4 * X7 * Y6 * 2.0 - X4 * X9 * Y4 * 2.0 + X6 * X7 * Y4 * 2.0 + X5 * X6 * Y8 * 2.0 + X5 * X8 * Y6 * 2.0 - X5 * X9 * Y5 * 2.0 + X6 * X8 * Y5 * 2.0 + X6 * X9 * Y6 * 2.0 + X7 * X9 * Y7 * 2.0 + X8 * X9 * Y8 * 2.0;
    G0[8 + 2 * 9] = -Z9 * t2 - Z9 * t3 + Z9 * t4 - Z9 * t5 - Z9 * t6 + Z9 * t7 + Z9 * t8 + Z9 * t9 + Z9 * t10 * 3.0 + X1 * X3 * Z7 * 2.0 + X1 * X7 * Z3 * 2.0 - X1 * X9 * Z1 * 2.0 + X3 * X7 * Z1 * 2.0 + X2 * X3 * Z8 * 2.0 + X2 * X8 * Z3 * 2.0 - X2 * X9 * Z2 * 2.0 + X3 * X8 * Z2 * 2.0 + X3 * X9 * Z3 * 2.0 + X4 * X6 * Z7 * 2.0 + X4 * X7 * Z6 * 2.0 - X4 * X9 * Z4 * 2.0 + X6 * X7 * Z4 * 2.0 + X5 * X6 * Z8 * 2.0 + X5 * X8 * Z6 * 2.0 - X5 * X9 * Z5 * 2.0 + X6 * X8 * Z5 * 2.0 + X6 * X9 * Z6 * 2.0 + X7 * X9 * Z7 * 2.0 + X8 * X9 * Z8 * 2.0;
    G0[8 + 3 * 9] = -X9 * t11 + X9 * t12 - X9 * t13 - X9 * t14 + X9 * t15 + X9 * t16 + X9 * t17 + X9 * t18 * 3.0 - X9 * t19 - X1 * Y1 * Y9 * 2.0 + X1 * Y3 * Y7 * 2.0 + X3 * Y1 * Y7 * 2.0 + X7 * Y1 * Y3 * 2.0 - X2 * Y2 * Y9 * 2.0 + X2 * Y3 * Y8 * 2.0 + X3 * Y2 * Y8 * 2.0 + X8 * Y2 * Y3 * 2.0 + X3 * Y3 * Y9 * 2.0 - X4 * Y4 * Y9 * 2.0 + X4 * Y6 * Y7 * 2.0 + X6 * Y4 * Y7 * 2.0 + X7 * Y4 * Y6 * 2.0 - X5 * Y5 * Y9 * 2.0 + X5 * Y6 * Y8 * 2.0 + X6 * Y5 * Y8 * 2.0 + X8 * Y5 * Y6 * 2.0 + X6 * Y6 * Y9 * 2.0 + X7 * Y7 * Y9 * 2.0 + X8 * Y8 * Y9 * 2.0;
    G0[8 + 4 * 9] = X1 * Y1 * Z9 * -2.0 + X1 * Y3 * Z7 * 2.0 + X1 * Y7 * Z3 * 2.0 - X1 * Y9 * Z1 * 2.0 + X3 * Y1 * Z7 * 2.0 + X3 * Y7 * Z1 * 2.0 + X7 * Y1 * Z3 * 2.0 + X7 * Y3 * Z1 * 2.0 - X9 * Y1 * Z1 * 2.0 - X2 * Y2 * Z9 * 2.0 + X2 * Y3 * Z8 * 2.0 + X2 * Y8 * Z3 * 2.0 - X2 * Y9 * Z2 * 2.0 + X3 * Y2 * Z8 * 2.0 + X3 * Y8 * Z2 * 2.0 + X8 * Y2 * Z3 * 2.0 + X8 * Y3 * Z2 * 2.0 - X9 * Y2 * Z2 * 2.0 + X3 * Y3 * Z9 * 2.0 + X3 * Y9 * Z3 * 2.0 + X9 * Y3 * Z3 * 2.0 - X4 * Y4 * Z9 * 2.0 + X4 * Y6 * Z7 * 2.0 + X4 * Y7 * Z6 * 2.0 - X4 * Y9 * Z4 * 2.0 + X6 * Y4 * Z7 * 2.0 + X6 * Y7 * Z4 * 2.0 + X7 * Y4 * Z6 * 2.0 + X7 * Y6 * Z4 * 2.0 - X9 * Y4 * Z4 * 2.0 - X5 * Y5 * Z9 * 2.0 + X5 * Y6 * Z8 * 2.0 + X5 * Y8 * Z6 * 2.0 - X5 * Y9 * Z5 * 2.0 + X6 * Y5 * Z8 * 2.0 + X6 * Y8 * Z5 * 2.0 + X8 * Y5 * Z6 * 2.0 + X8 * Y6 * Z5 * 2.0 - X9 * Y5 * Z5 * 2.0 + X6 * Y6 * Z9 * 2.0 + X6 * Y9 * Z6 * 2.0 + X9 * Y6 * Z6 * 2.0 + X7 * Y7 * Z9 * 2.0 + X7 * Y9 * Z7 * 2.0 + X9 * Y7 * Z7 * 2.0 + X8 * Y8 * Z9 * 2.0 + X8 * Y9 * Z8 * 2.0 + X9 * Y8 * Z8 * 2.0 + X9 * Y9 * Z9 * 6.0;
    G0[8 + 5 * 9] = -X9 * t20 - X9 * t21 + X9 * t22 - X9 * t23 - X9 * t24 + X9 * t25 + X9 * t26 + X9 * t27 + X9 * t28 * 3.0 - X1 * Z1 * Z9 * 2.0 + X1 * Z3 * Z7 * 2.0 + X3 * Z1 * Z7 * 2.0 + X7 * Z1 * Z3 * 2.0 - X2 * Z2 * Z9 * 2.0 + X2 * Z3 * Z8 * 2.0 + X3 * Z2 * Z8 * 2.0 + X8 * Z2 * Z3 * 2.0 + X3 * Z3 * Z9 * 2.0 - X4 * Z4 * Z9 * 2.0 + X4 * Z6 * Z7 * 2.0 + X6 * Z4 * Z7 * 2.0 + X7 * Z4 * Z6 * 2.0 - X5 * Z5 * Z9 * 2.0 + X5 * Z6 * Z8 * 2.0 + X6 * Z5 * Z8 * 2.0 + X8 * Z5 * Z6 * 2.0 + X6 * Z6 * Z9 * 2.0 + X7 * Z7 * Z9 * 2.0 + X8 * Z8 * Z9 * 2.0;
    G0[8 + 6 * 9] = -Y9 * t11 + Y9 * t12 - Y9 * t13 - Y9 * t14 + Y9 * t15 + Y9 * t16 + Y9 * t17 + Y9 * t18 - Y9 * t19 + Y1 * Y3 * Y7 * 2.0 + Y2 * Y3 * Y8 * 2.0 + Y4 * Y6 * Y7 * 2.0 + Y5 * Y6 * Y8 * 2.0;
    G0[8 + 7 * 9] = -Z9 * t11 + Z9 * t12 - Z9 * t13 - Z9 * t14 + Z9 * t15 + Z9 * t16 + Z9 * t17 + Z9 * t18 * 3.0 - Z9 * t19 + Y1 * Y3 * Z7 * 2.0 + Y1 * Y7 * Z3 * 2.0 - Y1 * Y9 * Z1 * 2.0 + Y3 * Y7 * Z1 * 2.0 + Y2 * Y3 * Z8 * 2.0 + Y2 * Y8 * Z3 * 2.0 - Y2 * Y9 * Z2 * 2.0 + Y3 * Y8 * Z2 * 2.0 + Y3 * Y9 * Z3 * 2.0 + Y4 * Y6 * Z7 * 2.0 + Y4 * Y7 * Z6 * 2.0 - Y4 * Y9 * Z4 * 2.0 + Y6 * Y7 * Z4 * 2.0 + Y5 * Y6 * Z8 * 2.0 + Y5 * Y8 * Z6 * 2.0 - Y5 * Y9 * Z5 * 2.0 + Y6 * Y8 * Z5 * 2.0 + Y6 * Y9 * Z6 * 2.0 + Y7 * Y9 * Z7 * 2.0 + Y8 * Y9 * Z8 * 2.0;
    G0[8 + 8 * 9] = -Y9 * t20 - Y9 * t21 + Y9 * t22 - Y9 * t23 - Y9 * t24 + Y9 * t25 + Y9 * t26 + Y9 * t27 + Y9 * t28 * 3.0 - Y1 * Z1 * Z9 * 2.0 + Y1 * Z3 * Z7 * 2.0 + Y3 * Z1 * Z7 * 2.0 + Y7 * Z1 * Z3 * 2.0 - Y2 * Z2 * Z9 * 2.0 + Y2 * Z3 * Z8 * 2.0 + Y3 * Z2 * Z8 * 2.0 + Y8 * Z2 * Z3 * 2.0 + Y3 * Z3 * Z9 * 2.0 - Y4 * Z4 * Z9 * 2.0 + Y4 * Z6 * Z7 * 2.0 + Y6 * Z4 * Z7 * 2.0 + Y7 * Z4 * Z6 * 2.0 - Y5 * Z5 * Z9 * 2.0 + Y5 * Z6 * Z8 * 2.0 + Y6 * Z5 * Z8 * 2.0 + Y8 * Z5 * Z6 * 2.0 + Y6 * Z6 * Z9 * 2.0 + Y7 * Z7 * Z9 * 2.0 + Y8 * Z8 * Z9 * 2.0;
    G0[8 + 9 * 9] = -Z9 * t20 - Z9 * t21 + Z9 * t22 - Z9 * t23 - Z9 * t24 + Z9 * t25 + Z9 * t26 + Z9 * t27 + Z9 * t28 + Z1 * Z3 * Z7 * 2.0 + Z2 * Z3 * Z8 * 2.0 + Z4 * Z6 * Z7 * 2.0 + Z5 * Z6 * Z8 * 2.0;

    Map<Matrix<double, 9, 10, ColMajor>> G_map(G0, 9, 10);
    G = G_map;
    //    std::cout<<B<<std::endl;
}

inline void compute_Bcoefficients(const EigenSols &eigen_sols, Matrix<double, 9, 10> &B)
{

    // id = [1,2,4,7,3,5,8,6,9,10];

    // BB = BB(:,id);

    // BB = BB'*BB;

    //[~,~,Vb]=svds(BB,1,'smallest');
    double X1, X2, X3, X4, X5, X6, X7, X8, X9;
    double Y1, Y2, Y3, Y4, Y5, Y6, Y7, Y8, Y9;
    double Z1, Z2, Z3, Z4, Z5, Z6, Z7, Z8, Z9;

    X1 = eigen_sols(0, 0);
    X2 = eigen_sols(0, 1);
    X3 = eigen_sols(0, 2);
    X4 = eigen_sols(0, 3);
    X5 = eigen_sols(0, 4);
    X6 = eigen_sols(0, 5);
    X7 = eigen_sols(0, 6);
    X8 = eigen_sols(0, 7);
    X9 = eigen_sols(0, 8);

    Y1 = eigen_sols(1, 0);
    Y2 = eigen_sols(1, 1);
    Y3 = eigen_sols(1, 2);
    Y4 = eigen_sols(1, 3);
    Y5 = eigen_sols(1, 4);
    Y6 = eigen_sols(1, 5);
    Y7 = eigen_sols(1, 6);
    Y8 = eigen_sols(1, 7);
    Y9 = eigen_sols(1, 8);

    Z1 = eigen_sols(2, 0);
    Z2 = eigen_sols(2, 1);
    Z3 = eigen_sols(2, 2);
    Z4 = eigen_sols(2, 3);
    Z5 = eigen_sols(2, 4);
    Z6 = eigen_sols(2, 5);
    Z7 = eigen_sols(2, 6);
    Z8 = eigen_sols(2, 7);
    Z9 = eigen_sols(2, 8);

    double t2 = X1 * X1;
    double t3 = X2 * X2;
    double t4 = X3 * X3;
    double t5 = X4 * X4;
    double t6 = X5 * X5;
    double t7 = X6 * X6;
    double t8 = X7 * X7;
    double t9 = X8 * X8;
    double t10 = X9 * X9;
    double t11 = Y2 * Y2;
    double t12 = Y3 * Y3;
    double t13 = Y4 * Y4;
    double t14 = Y5 * Y5;
    double t15 = Y6 * Y6;
    double t16 = Y7 * Y7;
    double t17 = Y8 * Y8;
    double t18 = Y9 * Y9;
    double t19 = Y1 * Y1;
    double t20 = Z1 * Z1;
    double t21 = Z2 * Z2;
    double t22 = Z3 * Z3;
    double t23 = Z4 * Z4;
    double t24 = Z5 * Z5;
    double t25 = Z6 * Z6;
    double t26 = Z7 * Z7;
    double t27 = Z8 * Z8;
    double t28 = Z9 * Z9;

    double B0[9 * 10];
    B0[0 + 0 * 9] = X1 * t2 + X1 * t3 + X1 * t4 + X1 * t5 - X1 * t6 - X1 * t7 + X1 * t8 - X1 * t9 - X1 * t10 + X2 * X4 * X5 * 2.0 + X3 * X4 * X6 * 2.0 + X2 * X7 * X8 * 2.0 + X3 * X7 * X9 * 2.0;
    B0[0 + 1 * 9] = Y1 * t2 * 3.0 + Y1 * t3 + Y1 * t4 + Y1 * t5 - Y1 * t6 - Y1 * t7 + Y1 * t8 - Y1 * t9 - Y1 * t10 + X1 * X2 * Y2 * 2.0 + X1 * X3 * Y3 * 2.0 + X1 * X4 * Y4 * 2.0 - X1 * X5 * Y5 * 2.0 + X2 * X4 * Y5 * 2.0 + X2 * X5 * Y4 * 2.0 + X4 * X5 * Y2 * 2.0 - X1 * X6 * Y6 * 2.0 + X3 * X4 * Y6 * 2.0 + X3 * X6 * Y4 * 2.0 + X4 * X6 * Y3 * 2.0 + X1 * X7 * Y7 * 2.0 - X1 * X8 * Y8 * 2.0 + X2 * X7 * Y8 * 2.0 + X2 * X8 * Y7 * 2.0 + X7 * X8 * Y2 * 2.0 - X1 * X9 * Y9 * 2.0 + X3 * X7 * Y9 * 2.0 + X3 * X9 * Y7 * 2.0 + X7 * X9 * Y3 * 2.0;
    B0[0 + 2 * 9] = X1 * t11 + X1 * t12 + X1 * t13 - X1 * t14 - X1 * t15 + X1 * t16 - X1 * t17 - X1 * t18 + X1 * t19 * 3.0 + X2 * Y1 * Y2 * 2.0 + X3 * Y1 * Y3 * 2.0 + X4 * Y1 * Y4 * 2.0 + X2 * Y4 * Y5 * 2.0 + X4 * Y2 * Y5 * 2.0 - X5 * Y1 * Y5 * 2.0 + X5 * Y2 * Y4 * 2.0 + X3 * Y4 * Y6 * 2.0 + X4 * Y3 * Y6 * 2.0 - X6 * Y1 * Y6 * 2.0 + X6 * Y3 * Y4 * 2.0 + X7 * Y1 * Y7 * 2.0 + X2 * Y7 * Y8 * 2.0 + X7 * Y2 * Y8 * 2.0 - X8 * Y1 * Y8 * 2.0 + X8 * Y2 * Y7 * 2.0 + X3 * Y7 * Y9 * 2.0 + X7 * Y3 * Y9 * 2.0 - X9 * Y1 * Y9 * 2.0 + X9 * Y3 * Y7 * 2.0;
    B0[0 + 3 * 9] = Y1 * t11 + Y1 * t12 + Y1 * t13 - Y1 * t14 - Y1 * t15 + Y1 * t16 - Y1 * t17 - Y1 * t18 + Y1 * t19 + Y2 * Y4 * Y5 * 2.0 + Y3 * Y4 * Y6 * 2.0 + Y2 * Y7 * Y8 * 2.0 + Y3 * Y7 * Y9 * 2.0;
    B0[0 + 4 * 9] = Z1 * t2 * 3.0 + Z1 * t3 + Z1 * t4 + Z1 * t5 - Z1 * t6 - Z1 * t7 + Z1 * t8 - Z1 * t9 - Z1 * t10 + X1 * X2 * Z2 * 2.0 + X1 * X3 * Z3 * 2.0 + X1 * X4 * Z4 * 2.0 - X1 * X5 * Z5 * 2.0 + X2 * X4 * Z5 * 2.0 + X2 * X5 * Z4 * 2.0 + X4 * X5 * Z2 * 2.0 - X1 * X6 * Z6 * 2.0 + X3 * X4 * Z6 * 2.0 + X3 * X6 * Z4 * 2.0 + X4 * X6 * Z3 * 2.0 + X1 * X7 * Z7 * 2.0 - X1 * X8 * Z8 * 2.0 + X2 * X7 * Z8 * 2.0 + X2 * X8 * Z7 * 2.0 + X7 * X8 * Z2 * 2.0 - X1 * X9 * Z9 * 2.0 + X3 * X7 * Z9 * 2.0 + X3 * X9 * Z7 * 2.0 + X7 * X9 * Z3 * 2.0;
    B0[0 + 5 * 9] = X1 * Y1 * Z1 * 6.0 + X1 * Y2 * Z2 * 2.0 + X2 * Y1 * Z2 * 2.0 + X2 * Y2 * Z1 * 2.0 + X1 * Y3 * Z3 * 2.0 + X3 * Y1 * Z3 * 2.0 + X3 * Y3 * Z1 * 2.0 + X1 * Y4 * Z4 * 2.0 + X4 * Y1 * Z4 * 2.0 + X4 * Y4 * Z1 * 2.0 - X1 * Y5 * Z5 * 2.0 + X2 * Y4 * Z5 * 2.0 + X2 * Y5 * Z4 * 2.0 + X4 * Y2 * Z5 * 2.0 + X4 * Y5 * Z2 * 2.0 - X5 * Y1 * Z5 * 2.0 + X5 * Y2 * Z4 * 2.0 + X5 * Y4 * Z2 * 2.0 - X5 * Y5 * Z1 * 2.0 - X1 * Y6 * Z6 * 2.0 + X3 * Y4 * Z6 * 2.0 + X3 * Y6 * Z4 * 2.0 + X4 * Y3 * Z6 * 2.0 + X4 * Y6 * Z3 * 2.0 - X6 * Y1 * Z6 * 2.0 + X6 * Y3 * Z4 * 2.0 + X6 * Y4 * Z3 * 2.0 - X6 * Y6 * Z1 * 2.0 + X1 * Y7 * Z7 * 2.0 + X7 * Y1 * Z7 * 2.0 + X7 * Y7 * Z1 * 2.0 - X1 * Y8 * Z8 * 2.0 + X2 * Y7 * Z8 * 2.0 + X2 * Y8 * Z7 * 2.0 + X7 * Y2 * Z8 * 2.0 + X7 * Y8 * Z2 * 2.0 - X8 * Y1 * Z8 * 2.0 + X8 * Y2 * Z7 * 2.0 + X8 * Y7 * Z2 * 2.0 - X8 * Y8 * Z1 * 2.0 - X1 * Y9 * Z9 * 2.0 + X3 * Y7 * Z9 * 2.0 + X3 * Y9 * Z7 * 2.0 + X7 * Y3 * Z9 * 2.0 + X7 * Y9 * Z3 * 2.0 - X9 * Y1 * Z9 * 2.0 + X9 * Y3 * Z7 * 2.0 + X9 * Y7 * Z3 * 2.0 - X9 * Y9 * Z1 * 2.0;
    B0[0 + 6 * 9] = Z1 * t11 + Z1 * t12 + Z1 * t13 - Z1 * t14 - Z1 * t15 + Z1 * t16 - Z1 * t17 - Z1 * t18 + Z1 * t19 * 3.0 + Y1 * Y2 * Z2 * 2.0 + Y1 * Y3 * Z3 * 2.0 + Y1 * Y4 * Z4 * 2.0 - Y1 * Y5 * Z5 * 2.0 + Y2 * Y4 * Z5 * 2.0 + Y2 * Y5 * Z4 * 2.0 + Y4 * Y5 * Z2 * 2.0 - Y1 * Y6 * Z6 * 2.0 + Y3 * Y4 * Z6 * 2.0 + Y3 * Y6 * Z4 * 2.0 + Y4 * Y6 * Z3 * 2.0 + Y1 * Y7 * Z7 * 2.0 - Y1 * Y8 * Z8 * 2.0 + Y2 * Y7 * Z8 * 2.0 + Y2 * Y8 * Z7 * 2.0 + Y7 * Y8 * Z2 * 2.0 - Y1 * Y9 * Z9 * 2.0 + Y3 * Y7 * Z9 * 2.0 + Y3 * Y9 * Z7 * 2.0 + Y7 * Y9 * Z3 * 2.0;
    B0[0 + 7 * 9] = X1 * t20 * 3.0 + X1 * t21 + X1 * t22 + X1 * t23 - X1 * t24 - X1 * t25 + X1 * t26 - X1 * t27 - X1 * t28 + X2 * Z1 * Z2 * 2.0 + X3 * Z1 * Z3 * 2.0 + X4 * Z1 * Z4 * 2.0 + X2 * Z4 * Z5 * 2.0 + X4 * Z2 * Z5 * 2.0 - X5 * Z1 * Z5 * 2.0 + X5 * Z2 * Z4 * 2.0 + X3 * Z4 * Z6 * 2.0 + X4 * Z3 * Z6 * 2.0 - X6 * Z1 * Z6 * 2.0 + X6 * Z3 * Z4 * 2.0 + X7 * Z1 * Z7 * 2.0 + X2 * Z7 * Z8 * 2.0 + X7 * Z2 * Z8 * 2.0 - X8 * Z1 * Z8 * 2.0 + X8 * Z2 * Z7 * 2.0 + X3 * Z7 * Z9 * 2.0 + X7 * Z3 * Z9 * 2.0 - X9 * Z1 * Z9 * 2.0 + X9 * Z3 * Z7 * 2.0;
    B0[0 + 8 * 9] = Y1 * t20 * 3.0 + Y1 * t21 + Y1 * t22 + Y1 * t23 - Y1 * t24 - Y1 * t25 + Y1 * t26 - Y1 * t27 - Y1 * t28 + Y2 * Z1 * Z2 * 2.0 + Y3 * Z1 * Z3 * 2.0 + Y4 * Z1 * Z4 * 2.0 + Y2 * Z4 * Z5 * 2.0 + Y4 * Z2 * Z5 * 2.0 - Y5 * Z1 * Z5 * 2.0 + Y5 * Z2 * Z4 * 2.0 + Y3 * Z4 * Z6 * 2.0 + Y4 * Z3 * Z6 * 2.0 - Y6 * Z1 * Z6 * 2.0 + Y6 * Z3 * Z4 * 2.0 + Y7 * Z1 * Z7 * 2.0 + Y2 * Z7 * Z8 * 2.0 + Y7 * Z2 * Z8 * 2.0 - Y8 * Z1 * Z8 * 2.0 + Y8 * Z2 * Z7 * 2.0 + Y3 * Z7 * Z9 * 2.0 + Y7 * Z3 * Z9 * 2.0 - Y9 * Z1 * Z9 * 2.0 + Y9 * Z3 * Z7 * 2.0;
    B0[0 + 9 * 9] = Z1 * t20 + Z1 * t21 + Z1 * t22 + Z1 * t23 - Z1 * t24 - Z1 * t25 + Z1 * t26 - Z1 * t27 - Z1 * t28 + Z2 * Z4 * Z5 * 2.0 + Z3 * Z4 * Z6 * 2.0 + Z2 * Z7 * Z8 * 2.0 + Z3 * Z7 * Z9 * 2.0;

    B0[1 + 0 * 9] = X2 * t2 + X2 * t3 + X2 * t4 - X2 * t5 + X2 * t6 - X2 * t7 - X2 * t8 + X2 * t9 - X2 * t10 + X1 * X4 * X5 * 2.0 + X3 * X5 * X6 * 2.0 + X1 * X7 * X8 * 2.0 + X3 * X8 * X9 * 2.0;
    B0[1 + 1 * 9] = Y2 * t2 + Y2 * t3 * 3.0 + Y2 * t4 - Y2 * t5 + Y2 * t6 - Y2 * t7 - Y2 * t8 + Y2 * t9 - Y2 * t10 + X1 * X2 * Y1 * 2.0 + X2 * X3 * Y3 * 2.0 + X1 * X4 * Y5 * 2.0 + X1 * X5 * Y4 * 2.0 - X2 * X4 * Y4 * 2.0 + X4 * X5 * Y1 * 2.0 + X2 * X5 * Y5 * 2.0 - X2 * X6 * Y6 * 2.0 + X3 * X5 * Y6 * 2.0 + X3 * X6 * Y5 * 2.0 + X5 * X6 * Y3 * 2.0 + X1 * X7 * Y8 * 2.0 + X1 * X8 * Y7 * 2.0 - X2 * X7 * Y7 * 2.0 + X7 * X8 * Y1 * 2.0 + X2 * X8 * Y8 * 2.0 - X2 * X9 * Y9 * 2.0 + X3 * X8 * Y9 * 2.0 + X3 * X9 * Y8 * 2.0 + X8 * X9 * Y3 * 2.0;
    B0[1 + 2 * 9] = X2 * t11 * 3.0 + X2 * t12 - X2 * t13 + X2 * t14 - X2 * t15 - X2 * t16 + X2 * t17 - X2 * t18 + X2 * t19 + X1 * Y1 * Y2 * 2.0 + X3 * Y2 * Y3 * 2.0 + X1 * Y4 * Y5 * 2.0 + X4 * Y1 * Y5 * 2.0 - X4 * Y2 * Y4 * 2.0 + X5 * Y1 * Y4 * 2.0 + X5 * Y2 * Y5 * 2.0 + X3 * Y5 * Y6 * 2.0 + X5 * Y3 * Y6 * 2.0 - X6 * Y2 * Y6 * 2.0 + X6 * Y3 * Y5 * 2.0 + X1 * Y7 * Y8 * 2.0 + X7 * Y1 * Y8 * 2.0 - X7 * Y2 * Y7 * 2.0 + X8 * Y1 * Y7 * 2.0 + X8 * Y2 * Y8 * 2.0 + X3 * Y8 * Y9 * 2.0 + X8 * Y3 * Y9 * 2.0 - X9 * Y2 * Y9 * 2.0 + X9 * Y3 * Y8 * 2.0;
    B0[1 + 3 * 9] = Y2 * t11 + Y2 * t12 - Y2 * t13 + Y2 * t14 - Y2 * t15 - Y2 * t16 + Y2 * t17 - Y2 * t18 + Y2 * t19 + Y1 * Y4 * Y5 * 2.0 + Y3 * Y5 * Y6 * 2.0 + Y1 * Y7 * Y8 * 2.0 + Y3 * Y8 * Y9 * 2.0;
    B0[1 + 4 * 9] = Z2 * t2 + Z2 * t3 * 3.0 + Z2 * t4 - Z2 * t5 + Z2 * t6 - Z2 * t7 - Z2 * t8 + Z2 * t9 - Z2 * t10 + X1 * X2 * Z1 * 2.0 + X2 * X3 * Z3 * 2.0 + X1 * X4 * Z5 * 2.0 + X1 * X5 * Z4 * 2.0 - X2 * X4 * Z4 * 2.0 + X4 * X5 * Z1 * 2.0 + X2 * X5 * Z5 * 2.0 - X2 * X6 * Z6 * 2.0 + X3 * X5 * Z6 * 2.0 + X3 * X6 * Z5 * 2.0 + X5 * X6 * Z3 * 2.0 + X1 * X7 * Z8 * 2.0 + X1 * X8 * Z7 * 2.0 - X2 * X7 * Z7 * 2.0 + X7 * X8 * Z1 * 2.0 + X2 * X8 * Z8 * 2.0 - X2 * X9 * Z9 * 2.0 + X3 * X8 * Z9 * 2.0 + X3 * X9 * Z8 * 2.0 + X8 * X9 * Z3 * 2.0;
    B0[1 + 5 * 9] = X1 * Y1 * Z2 * 2.0 + X1 * Y2 * Z1 * 2.0 + X2 * Y1 * Z1 * 2.0 + X2 * Y2 * Z2 * 6.0 + X2 * Y3 * Z3 * 2.0 + X3 * Y2 * Z3 * 2.0 + X3 * Y3 * Z2 * 2.0 + X1 * Y4 * Z5 * 2.0 + X1 * Y5 * Z4 * 2.0 - X2 * Y4 * Z4 * 2.0 + X4 * Y1 * Z5 * 2.0 - X4 * Y2 * Z4 * 2.0 - X4 * Y4 * Z2 * 2.0 + X4 * Y5 * Z1 * 2.0 + X5 * Y1 * Z4 * 2.0 + X5 * Y4 * Z1 * 2.0 + X2 * Y5 * Z5 * 2.0 + X5 * Y2 * Z5 * 2.0 + X5 * Y5 * Z2 * 2.0 - X2 * Y6 * Z6 * 2.0 + X3 * Y5 * Z6 * 2.0 + X3 * Y6 * Z5 * 2.0 + X5 * Y3 * Z6 * 2.0 + X5 * Y6 * Z3 * 2.0 - X6 * Y2 * Z6 * 2.0 + X6 * Y3 * Z5 * 2.0 + X6 * Y5 * Z3 * 2.0 - X6 * Y6 * Z2 * 2.0 + X1 * Y7 * Z8 * 2.0 + X1 * Y8 * Z7 * 2.0 - X2 * Y7 * Z7 * 2.0 + X7 * Y1 * Z8 * 2.0 - X7 * Y2 * Z7 * 2.0 - X7 * Y7 * Z2 * 2.0 + X7 * Y8 * Z1 * 2.0 + X8 * Y1 * Z7 * 2.0 + X8 * Y7 * Z1 * 2.0 + X2 * Y8 * Z8 * 2.0 + X8 * Y2 * Z8 * 2.0 + X8 * Y8 * Z2 * 2.0 - X2 * Y9 * Z9 * 2.0 + X3 * Y8 * Z9 * 2.0 + X3 * Y9 * Z8 * 2.0 + X8 * Y3 * Z9 * 2.0 + X8 * Y9 * Z3 * 2.0 - X9 * Y2 * Z9 * 2.0 + X9 * Y3 * Z8 * 2.0 + X9 * Y8 * Z3 * 2.0 - X9 * Y9 * Z2 * 2.0;
    B0[1 + 6 * 9] = Z2 * t11 * 3.0 + Z2 * t12 - Z2 * t13 + Z2 * t14 - Z2 * t15 - Z2 * t16 + Z2 * t17 - Z2 * t18 + Z2 * t19 + Y1 * Y2 * Z1 * 2.0 + Y2 * Y3 * Z3 * 2.0 + Y1 * Y4 * Z5 * 2.0 + Y1 * Y5 * Z4 * 2.0 - Y2 * Y4 * Z4 * 2.0 + Y4 * Y5 * Z1 * 2.0 + Y2 * Y5 * Z5 * 2.0 - Y2 * Y6 * Z6 * 2.0 + Y3 * Y5 * Z6 * 2.0 + Y3 * Y6 * Z5 * 2.0 + Y5 * Y6 * Z3 * 2.0 + Y1 * Y7 * Z8 * 2.0 + Y1 * Y8 * Z7 * 2.0 - Y2 * Y7 * Z7 * 2.0 + Y7 * Y8 * Z1 * 2.0 + Y2 * Y8 * Z8 * 2.0 - Y2 * Y9 * Z9 * 2.0 + Y3 * Y8 * Z9 * 2.0 + Y3 * Y9 * Z8 * 2.0 + Y8 * Y9 * Z3 * 2.0;
    B0[1 + 7 * 9] = X2 * t20 + X2 * t21 * 3.0 + X2 * t22 - X2 * t23 + X2 * t24 - X2 * t25 - X2 * t26 + X2 * t27 - X2 * t28 + X1 * Z1 * Z2 * 2.0 + X3 * Z2 * Z3 * 2.0 + X1 * Z4 * Z5 * 2.0 + X4 * Z1 * Z5 * 2.0 - X4 * Z2 * Z4 * 2.0 + X5 * Z1 * Z4 * 2.0 + X5 * Z2 * Z5 * 2.0 + X3 * Z5 * Z6 * 2.0 + X5 * Z3 * Z6 * 2.0 - X6 * Z2 * Z6 * 2.0 + X6 * Z3 * Z5 * 2.0 + X1 * Z7 * Z8 * 2.0 + X7 * Z1 * Z8 * 2.0 - X7 * Z2 * Z7 * 2.0 + X8 * Z1 * Z7 * 2.0 + X8 * Z2 * Z8 * 2.0 + X3 * Z8 * Z9 * 2.0 + X8 * Z3 * Z9 * 2.0 - X9 * Z2 * Z9 * 2.0 + X9 * Z3 * Z8 * 2.0;
    B0[1 + 8 * 9] = Y2 * t20 + Y2 * t21 * 3.0 + Y2 * t22 - Y2 * t23 + Y2 * t24 - Y2 * t25 - Y2 * t26 + Y2 * t27 - Y2 * t28 + Y1 * Z1 * Z2 * 2.0 + Y3 * Z2 * Z3 * 2.0 + Y1 * Z4 * Z5 * 2.0 + Y4 * Z1 * Z5 * 2.0 - Y4 * Z2 * Z4 * 2.0 + Y5 * Z1 * Z4 * 2.0 + Y5 * Z2 * Z5 * 2.0 + Y3 * Z5 * Z6 * 2.0 + Y5 * Z3 * Z6 * 2.0 - Y6 * Z2 * Z6 * 2.0 + Y6 * Z3 * Z5 * 2.0 + Y1 * Z7 * Z8 * 2.0 + Y7 * Z1 * Z8 * 2.0 - Y7 * Z2 * Z7 * 2.0 + Y8 * Z1 * Z7 * 2.0 + Y8 * Z2 * Z8 * 2.0 + Y3 * Z8 * Z9 * 2.0 + Y8 * Z3 * Z9 * 2.0 - Y9 * Z2 * Z9 * 2.0 + Y9 * Z3 * Z8 * 2.0;
    B0[1 + 9 * 9] = Z2 * t20 + Z2 * t21 + Z2 * t22 - Z2 * t23 + Z2 * t24 - Z2 * t25 - Z2 * t26 + Z2 * t27 - Z2 * t28 + Z1 * Z4 * Z5 * 2.0 + Z3 * Z5 * Z6 * 2.0 + Z1 * Z7 * Z8 * 2.0 + Z3 * Z8 * Z9 * 2.0;

    B0[2 + 0 * 9] = X3 * t2 + X3 * t3 + X3 * t4 - X3 * t5 - X3 * t6 + X3 * t7 - X3 * t8 - X3 * t9 + X3 * t10 + X1 * X4 * X6 * 2.0 + X2 * X5 * X6 * 2.0 + X1 * X7 * X9 * 2.0 + X2 * X8 * X9 * 2.0;
    B0[2 + 1 * 9] = Y3 * t2 + Y3 * t3 + Y3 * t4 * 3.0 - Y3 * t5 - Y3 * t6 + Y3 * t7 - Y3 * t8 - Y3 * t9 + Y3 * t10 + X1 * X3 * Y1 * 2.0 + X2 * X3 * Y2 * 2.0 + X1 * X4 * Y6 * 2.0 + X1 * X6 * Y4 * 2.0 - X3 * X4 * Y4 * 2.0 + X4 * X6 * Y1 * 2.0 + X2 * X5 * Y6 * 2.0 + X2 * X6 * Y5 * 2.0 - X3 * X5 * Y5 * 2.0 + X5 * X6 * Y2 * 2.0 + X3 * X6 * Y6 * 2.0 + X1 * X7 * Y9 * 2.0 + X1 * X9 * Y7 * 2.0 - X3 * X7 * Y7 * 2.0 + X7 * X9 * Y1 * 2.0 + X2 * X8 * Y9 * 2.0 + X2 * X9 * Y8 * 2.0 - X3 * X8 * Y8 * 2.0 + X8 * X9 * Y2 * 2.0 + X3 * X9 * Y9 * 2.0;
    B0[2 + 2 * 9] = X3 * t11 + X3 * t12 * 3.0 - X3 * t13 - X3 * t14 + X3 * t15 - X3 * t16 - X3 * t17 + X3 * t18 + X3 * t19 + X1 * Y1 * Y3 * 2.0 + X2 * Y2 * Y3 * 2.0 + X1 * Y4 * Y6 * 2.0 + X4 * Y1 * Y6 * 2.0 - X4 * Y3 * Y4 * 2.0 + X6 * Y1 * Y4 * 2.0 + X2 * Y5 * Y6 * 2.0 + X5 * Y2 * Y6 * 2.0 - X5 * Y3 * Y5 * 2.0 + X6 * Y2 * Y5 * 2.0 + X6 * Y3 * Y6 * 2.0 + X1 * Y7 * Y9 * 2.0 + X7 * Y1 * Y9 * 2.0 - X7 * Y3 * Y7 * 2.0 + X9 * Y1 * Y7 * 2.0 + X2 * Y8 * Y9 * 2.0 + X8 * Y2 * Y9 * 2.0 - X8 * Y3 * Y8 * 2.0 + X9 * Y2 * Y8 * 2.0 + X9 * Y3 * Y9 * 2.0;
    B0[2 + 3 * 9] = Y3 * t11 + Y3 * t12 - Y3 * t13 - Y3 * t14 + Y3 * t15 - Y3 * t16 - Y3 * t17 + Y3 * t18 + Y3 * t19 + Y1 * Y4 * Y6 * 2.0 + Y2 * Y5 * Y6 * 2.0 + Y1 * Y7 * Y9 * 2.0 + Y2 * Y8 * Y9 * 2.0;
    B0[2 + 4 * 9] = Z3 * t2 + Z3 * t3 + Z3 * t4 * 3.0 - Z3 * t5 - Z3 * t6 + Z3 * t7 - Z3 * t8 - Z3 * t9 + Z3 * t10 + X1 * X3 * Z1 * 2.0 + X2 * X3 * Z2 * 2.0 + X1 * X4 * Z6 * 2.0 + X1 * X6 * Z4 * 2.0 - X3 * X4 * Z4 * 2.0 + X4 * X6 * Z1 * 2.0 + X2 * X5 * Z6 * 2.0 + X2 * X6 * Z5 * 2.0 - X3 * X5 * Z5 * 2.0 + X5 * X6 * Z2 * 2.0 + X3 * X6 * Z6 * 2.0 + X1 * X7 * Z9 * 2.0 + X1 * X9 * Z7 * 2.0 - X3 * X7 * Z7 * 2.0 + X7 * X9 * Z1 * 2.0 + X2 * X8 * Z9 * 2.0 + X2 * X9 * Z8 * 2.0 - X3 * X8 * Z8 * 2.0 + X8 * X9 * Z2 * 2.0 + X3 * X9 * Z9 * 2.0;
    B0[2 + 5 * 9] = X1 * Y1 * Z3 * 2.0 + X1 * Y3 * Z1 * 2.0 + X3 * Y1 * Z1 * 2.0 + X2 * Y2 * Z3 * 2.0 + X2 * Y3 * Z2 * 2.0 + X3 * Y2 * Z2 * 2.0 + X3 * Y3 * Z3 * 6.0 + X1 * Y4 * Z6 * 2.0 + X1 * Y6 * Z4 * 2.0 - X3 * Y4 * Z4 * 2.0 + X4 * Y1 * Z6 * 2.0 - X4 * Y3 * Z4 * 2.0 - X4 * Y4 * Z3 * 2.0 + X4 * Y6 * Z1 * 2.0 + X6 * Y1 * Z4 * 2.0 + X6 * Y4 * Z1 * 2.0 + X2 * Y5 * Z6 * 2.0 + X2 * Y6 * Z5 * 2.0 - X3 * Y5 * Z5 * 2.0 + X5 * Y2 * Z6 * 2.0 - X5 * Y3 * Z5 * 2.0 - X5 * Y5 * Z3 * 2.0 + X5 * Y6 * Z2 * 2.0 + X6 * Y2 * Z5 * 2.0 + X6 * Y5 * Z2 * 2.0 + X3 * Y6 * Z6 * 2.0 + X6 * Y3 * Z6 * 2.0 + X6 * Y6 * Z3 * 2.0 + X1 * Y7 * Z9 * 2.0 + X1 * Y9 * Z7 * 2.0 - X3 * Y7 * Z7 * 2.0 + X7 * Y1 * Z9 * 2.0 - X7 * Y3 * Z7 * 2.0 - X7 * Y7 * Z3 * 2.0 + X7 * Y9 * Z1 * 2.0 + X9 * Y1 * Z7 * 2.0 + X9 * Y7 * Z1 * 2.0 + X2 * Y8 * Z9 * 2.0 + X2 * Y9 * Z8 * 2.0 - X3 * Y8 * Z8 * 2.0 + X8 * Y2 * Z9 * 2.0 - X8 * Y3 * Z8 * 2.0 - X8 * Y8 * Z3 * 2.0 + X8 * Y9 * Z2 * 2.0 + X9 * Y2 * Z8 * 2.0 + X9 * Y8 * Z2 * 2.0 + X3 * Y9 * Z9 * 2.0 + X9 * Y3 * Z9 * 2.0 + X9 * Y9 * Z3 * 2.0;
    B0[2 + 6 * 9] = Z3 * t11 + Z3 * t12 * 3.0 - Z3 * t13 - Z3 * t14 + Z3 * t15 - Z3 * t16 - Z3 * t17 + Z3 * t18 + Z3 * t19 + Y1 * Y3 * Z1 * 2.0 + Y2 * Y3 * Z2 * 2.0 + Y1 * Y4 * Z6 * 2.0 + Y1 * Y6 * Z4 * 2.0 - Y3 * Y4 * Z4 * 2.0 + Y4 * Y6 * Z1 * 2.0 + Y2 * Y5 * Z6 * 2.0 + Y2 * Y6 * Z5 * 2.0 - Y3 * Y5 * Z5 * 2.0 + Y5 * Y6 * Z2 * 2.0 + Y3 * Y6 * Z6 * 2.0 + Y1 * Y7 * Z9 * 2.0 + Y1 * Y9 * Z7 * 2.0 - Y3 * Y7 * Z7 * 2.0 + Y7 * Y9 * Z1 * 2.0 + Y2 * Y8 * Z9 * 2.0 + Y2 * Y9 * Z8 * 2.0 - Y3 * Y8 * Z8 * 2.0 + Y8 * Y9 * Z2 * 2.0 + Y3 * Y9 * Z9 * 2.0;
    B0[2 + 7 * 9] = X3 * t20 + X3 * t21 + X3 * t22 * 3.0 - X3 * t23 - X3 * t24 + X3 * t25 - X3 * t26 - X3 * t27 + X3 * t28 + X1 * Z1 * Z3 * 2.0 + X2 * Z2 * Z3 * 2.0 + X1 * Z4 * Z6 * 2.0 + X4 * Z1 * Z6 * 2.0 - X4 * Z3 * Z4 * 2.0 + X6 * Z1 * Z4 * 2.0 + X2 * Z5 * Z6 * 2.0 + X5 * Z2 * Z6 * 2.0 - X5 * Z3 * Z5 * 2.0 + X6 * Z2 * Z5 * 2.0 + X6 * Z3 * Z6 * 2.0 + X1 * Z7 * Z9 * 2.0 + X7 * Z1 * Z9 * 2.0 - X7 * Z3 * Z7 * 2.0 + X9 * Z1 * Z7 * 2.0 + X2 * Z8 * Z9 * 2.0 + X8 * Z2 * Z9 * 2.0 - X8 * Z3 * Z8 * 2.0 + X9 * Z2 * Z8 * 2.0 + X9 * Z3 * Z9 * 2.0;
    B0[2 + 8 * 9] = Y3 * t20 + Y3 * t21 + Y3 * t22 * 3.0 - Y3 * t23 - Y3 * t24 + Y3 * t25 - Y3 * t26 - Y3 * t27 + Y3 * t28 + Y1 * Z1 * Z3 * 2.0 + Y2 * Z2 * Z3 * 2.0 + Y1 * Z4 * Z6 * 2.0 + Y4 * Z1 * Z6 * 2.0 - Y4 * Z3 * Z4 * 2.0 + Y6 * Z1 * Z4 * 2.0 + Y2 * Z5 * Z6 * 2.0 + Y5 * Z2 * Z6 * 2.0 - Y5 * Z3 * Z5 * 2.0 + Y6 * Z2 * Z5 * 2.0 + Y6 * Z3 * Z6 * 2.0 + Y1 * Z7 * Z9 * 2.0 + Y7 * Z1 * Z9 * 2.0 - Y7 * Z3 * Z7 * 2.0 + Y9 * Z1 * Z7 * 2.0 + Y2 * Z8 * Z9 * 2.0 + Y8 * Z2 * Z9 * 2.0 - Y8 * Z3 * Z8 * 2.0 + Y9 * Z2 * Z8 * 2.0 + Y9 * Z3 * Z9 * 2.0;
    B0[2 + 9 * 9] = Z3 * t20 + Z3 * t21 + Z3 * t22 - Z3 * t23 - Z3 * t24 + Z3 * t25 - Z3 * t26 - Z3 * t27 + Z3 * t28 + Z1 * Z4 * Z6 * 2.0 + Z2 * Z5 * Z6 * 2.0 + Z1 * Z7 * Z9 * 2.0 + Z2 * Z8 * Z9 * 2.0;

    B0[3 + 0 * 9] = X4 * t2 - X4 * t3 - X4 * t4 + X4 * t5 + X4 * t6 + X4 * t7 + X4 * t8 - X4 * t9 - X4 * t10 + X1 * X2 * X5 * 2.0 + X1 * X3 * X6 * 2.0 + X5 * X7 * X8 * 2.0 + X6 * X7 * X9 * 2.0;
    B0[3 + 1 * 9] = Y4 * t2 - Y4 * t3 - Y4 * t4 + Y4 * t5 * 3.0 + Y4 * t6 + Y4 * t7 + Y4 * t8 - Y4 * t9 - Y4 * t10 + X1 * X4 * Y1 * 2.0 + X1 * X2 * Y5 * 2.0 + X1 * X5 * Y2 * 2.0 - X2 * X4 * Y2 * 2.0 + X2 * X5 * Y1 * 2.0 + X1 * X3 * Y6 * 2.0 + X1 * X6 * Y3 * 2.0 - X3 * X4 * Y3 * 2.0 + X3 * X6 * Y1 * 2.0 + X4 * X5 * Y5 * 2.0 + X4 * X6 * Y6 * 2.0 + X4 * X7 * Y7 * 2.0 - X4 * X8 * Y8 * 2.0 + X5 * X7 * Y8 * 2.0 + X5 * X8 * Y7 * 2.0 + X7 * X8 * Y5 * 2.0 - X4 * X9 * Y9 * 2.0 + X6 * X7 * Y9 * 2.0 + X6 * X9 * Y7 * 2.0 + X7 * X9 * Y6 * 2.0;
    B0[3 + 2 * 9] = -X4 * t11 - X4 * t12 + X4 * t13 * 3.0 + X4 * t14 + X4 * t15 + X4 * t16 - X4 * t17 - X4 * t18 + X4 * t19 + X1 * Y1 * Y4 * 2.0 + X1 * Y2 * Y5 * 2.0 + X2 * Y1 * Y5 * 2.0 - X2 * Y2 * Y4 * 2.0 + X5 * Y1 * Y2 * 2.0 + X1 * Y3 * Y6 * 2.0 + X3 * Y1 * Y6 * 2.0 - X3 * Y3 * Y4 * 2.0 + X6 * Y1 * Y3 * 2.0 + X5 * Y4 * Y5 * 2.0 + X6 * Y4 * Y6 * 2.0 + X7 * Y4 * Y7 * 2.0 + X5 * Y7 * Y8 * 2.0 + X7 * Y5 * Y8 * 2.0 - X8 * Y4 * Y8 * 2.0 + X8 * Y5 * Y7 * 2.0 + X6 * Y7 * Y9 * 2.0 + X7 * Y6 * Y9 * 2.0 - X9 * Y4 * Y9 * 2.0 + X9 * Y6 * Y7 * 2.0;
    B0[3 + 3 * 9] = -Y4 * t11 - Y4 * t12 + Y4 * t13 + Y4 * t14 + Y4 * t15 + Y4 * t16 - Y4 * t17 - Y4 * t18 + Y4 * t19 + Y1 * Y2 * Y5 * 2.0 + Y1 * Y3 * Y6 * 2.0 + Y5 * Y7 * Y8 * 2.0 + Y6 * Y7 * Y9 * 2.0;
    B0[3 + 4 * 9] = Z4 * t2 - Z4 * t3 - Z4 * t4 + Z4 * t5 * 3.0 + Z4 * t6 + Z4 * t7 + Z4 * t8 - Z4 * t9 - Z4 * t10 + X1 * X4 * Z1 * 2.0 + X1 * X2 * Z5 * 2.0 + X1 * X5 * Z2 * 2.0 - X2 * X4 * Z2 * 2.0 + X2 * X5 * Z1 * 2.0 + X1 * X3 * Z6 * 2.0 + X1 * X6 * Z3 * 2.0 - X3 * X4 * Z3 * 2.0 + X3 * X6 * Z1 * 2.0 + X4 * X5 * Z5 * 2.0 + X4 * X6 * Z6 * 2.0 + X4 * X7 * Z7 * 2.0 - X4 * X8 * Z8 * 2.0 + X5 * X7 * Z8 * 2.0 + X5 * X8 * Z7 * 2.0 + X7 * X8 * Z5 * 2.0 - X4 * X9 * Z9 * 2.0 + X6 * X7 * Z9 * 2.0 + X6 * X9 * Z7 * 2.0 + X7 * X9 * Z6 * 2.0;
    B0[3 + 5 * 9] = X1 * Y1 * Z4 * 2.0 + X1 * Y4 * Z1 * 2.0 + X4 * Y1 * Z1 * 2.0 + X1 * Y2 * Z5 * 2.0 + X1 * Y5 * Z2 * 2.0 + X2 * Y1 * Z5 * 2.0 - X2 * Y2 * Z4 * 2.0 - X2 * Y4 * Z2 * 2.0 + X2 * Y5 * Z1 * 2.0 - X4 * Y2 * Z2 * 2.0 + X5 * Y1 * Z2 * 2.0 + X5 * Y2 * Z1 * 2.0 + X1 * Y3 * Z6 * 2.0 + X1 * Y6 * Z3 * 2.0 + X3 * Y1 * Z6 * 2.0 - X3 * Y3 * Z4 * 2.0 - X3 * Y4 * Z3 * 2.0 + X3 * Y6 * Z1 * 2.0 - X4 * Y3 * Z3 * 2.0 + X6 * Y1 * Z3 * 2.0 + X6 * Y3 * Z1 * 2.0 + X4 * Y4 * Z4 * 6.0 + X4 * Y5 * Z5 * 2.0 + X5 * Y4 * Z5 * 2.0 + X5 * Y5 * Z4 * 2.0 + X4 * Y6 * Z6 * 2.0 + X6 * Y4 * Z6 * 2.0 + X6 * Y6 * Z4 * 2.0 + X4 * Y7 * Z7 * 2.0 + X7 * Y4 * Z7 * 2.0 + X7 * Y7 * Z4 * 2.0 - X4 * Y8 * Z8 * 2.0 + X5 * Y7 * Z8 * 2.0 + X5 * Y8 * Z7 * 2.0 + X7 * Y5 * Z8 * 2.0 + X7 * Y8 * Z5 * 2.0 - X8 * Y4 * Z8 * 2.0 + X8 * Y5 * Z7 * 2.0 + X8 * Y7 * Z5 * 2.0 - X8 * Y8 * Z4 * 2.0 - X4 * Y9 * Z9 * 2.0 + X6 * Y7 * Z9 * 2.0 + X6 * Y9 * Z7 * 2.0 + X7 * Y6 * Z9 * 2.0 + X7 * Y9 * Z6 * 2.0 - X9 * Y4 * Z9 * 2.0 + X9 * Y6 * Z7 * 2.0 + X9 * Y7 * Z6 * 2.0 - X9 * Y9 * Z4 * 2.0;
    B0[3 + 6 * 9] = -Z4 * t11 - Z4 * t12 + Z4 * t13 * 3.0 + Z4 * t14 + Z4 * t15 + Z4 * t16 - Z4 * t17 - Z4 * t18 + Z4 * t19 + Y1 * Y4 * Z1 * 2.0 + Y1 * Y2 * Z5 * 2.0 + Y1 * Y5 * Z2 * 2.0 - Y2 * Y4 * Z2 * 2.0 + Y2 * Y5 * Z1 * 2.0 + Y1 * Y3 * Z6 * 2.0 + Y1 * Y6 * Z3 * 2.0 - Y3 * Y4 * Z3 * 2.0 + Y3 * Y6 * Z1 * 2.0 + Y4 * Y5 * Z5 * 2.0 + Y4 * Y6 * Z6 * 2.0 + Y4 * Y7 * Z7 * 2.0 - Y4 * Y8 * Z8 * 2.0 + Y5 * Y7 * Z8 * 2.0 + Y5 * Y8 * Z7 * 2.0 + Y7 * Y8 * Z5 * 2.0 - Y4 * Y9 * Z9 * 2.0 + Y6 * Y7 * Z9 * 2.0 + Y6 * Y9 * Z7 * 2.0 + Y7 * Y9 * Z6 * 2.0;
    B0[3 + 7 * 9] = X4 * t20 - X4 * t21 - X4 * t22 + X4 * t23 * 3.0 + X4 * t24 + X4 * t25 + X4 * t26 - X4 * t27 - X4 * t28 + X1 * Z1 * Z4 * 2.0 + X1 * Z2 * Z5 * 2.0 + X2 * Z1 * Z5 * 2.0 - X2 * Z2 * Z4 * 2.0 + X5 * Z1 * Z2 * 2.0 + X1 * Z3 * Z6 * 2.0 + X3 * Z1 * Z6 * 2.0 - X3 * Z3 * Z4 * 2.0 + X6 * Z1 * Z3 * 2.0 + X5 * Z4 * Z5 * 2.0 + X6 * Z4 * Z6 * 2.0 + X7 * Z4 * Z7 * 2.0 + X5 * Z7 * Z8 * 2.0 + X7 * Z5 * Z8 * 2.0 - X8 * Z4 * Z8 * 2.0 + X8 * Z5 * Z7 * 2.0 + X6 * Z7 * Z9 * 2.0 + X7 * Z6 * Z9 * 2.0 - X9 * Z4 * Z9 * 2.0 + X9 * Z6 * Z7 * 2.0;
    B0[3 + 8 * 9] = Y4 * t20 - Y4 * t21 - Y4 * t22 + Y4 * t23 * 3.0 + Y4 * t24 + Y4 * t25 + Y4 * t26 - Y4 * t27 - Y4 * t28 + Y1 * Z1 * Z4 * 2.0 + Y1 * Z2 * Z5 * 2.0 + Y2 * Z1 * Z5 * 2.0 - Y2 * Z2 * Z4 * 2.0 + Y5 * Z1 * Z2 * 2.0 + Y1 * Z3 * Z6 * 2.0 + Y3 * Z1 * Z6 * 2.0 - Y3 * Z3 * Z4 * 2.0 + Y6 * Z1 * Z3 * 2.0 + Y5 * Z4 * Z5 * 2.0 + Y6 * Z4 * Z6 * 2.0 + Y7 * Z4 * Z7 * 2.0 + Y5 * Z7 * Z8 * 2.0 + Y7 * Z5 * Z8 * 2.0 - Y8 * Z4 * Z8 * 2.0 + Y8 * Z5 * Z7 * 2.0 + Y6 * Z7 * Z9 * 2.0 + Y7 * Z6 * Z9 * 2.0 - Y9 * Z4 * Z9 * 2.0 + Y9 * Z6 * Z7 * 2.0;
    B0[3 + 9 * 9] = Z4 * t20 - Z4 * t21 - Z4 * t22 + Z4 * t23 + Z4 * t24 + Z4 * t25 + Z4 * t26 - Z4 * t27 - Z4 * t28 + Z1 * Z2 * Z5 * 2.0 + Z1 * Z3 * Z6 * 2.0 + Z5 * Z7 * Z8 * 2.0 + Z6 * Z7 * Z9 * 2.0;

    B0[4 + 0 * 9] = -X5 * t2 + X5 * t3 - X5 * t4 + X5 * t5 + X5 * t6 + X5 * t7 - X5 * t8 + X5 * t9 - X5 * t10 + X1 * X2 * X4 * 2.0 + X2 * X3 * X6 * 2.0 + X4 * X7 * X8 * 2.0 + X6 * X8 * X9 * 2.0;
    B0[4 + 1 * 9] = -Y5 * t2 + Y5 * t3 - Y5 * t4 + Y5 * t5 + Y5 * t6 * 3.0 + Y5 * t7 - Y5 * t8 + Y5 * t9 - Y5 * t10 + X1 * X2 * Y4 * 2.0 + X1 * X4 * Y2 * 2.0 - X1 * X5 * Y1 * 2.0 + X2 * X4 * Y1 * 2.0 + X2 * X5 * Y2 * 2.0 + X2 * X3 * Y6 * 2.0 + X2 * X6 * Y3 * 2.0 - X3 * X5 * Y3 * 2.0 + X3 * X6 * Y2 * 2.0 + X4 * X5 * Y4 * 2.0 + X5 * X6 * Y6 * 2.0 + X4 * X7 * Y8 * 2.0 + X4 * X8 * Y7 * 2.0 - X5 * X7 * Y7 * 2.0 + X7 * X8 * Y4 * 2.0 + X5 * X8 * Y8 * 2.0 - X5 * X9 * Y9 * 2.0 + X6 * X8 * Y9 * 2.0 + X6 * X9 * Y8 * 2.0 + X8 * X9 * Y6 * 2.0;
    B0[4 + 2 * 9] = X5 * t11 - X5 * t12 + X5 * t13 + X5 * t14 * 3.0 + X5 * t15 - X5 * t16 + X5 * t17 - X5 * t18 - X5 * t19 - X1 * Y1 * Y5 * 2.0 + X1 * Y2 * Y4 * 2.0 + X2 * Y1 * Y4 * 2.0 + X4 * Y1 * Y2 * 2.0 + X2 * Y2 * Y5 * 2.0 + X2 * Y3 * Y6 * 2.0 + X3 * Y2 * Y6 * 2.0 - X3 * Y3 * Y5 * 2.0 + X6 * Y2 * Y3 * 2.0 + X4 * Y4 * Y5 * 2.0 + X6 * Y5 * Y6 * 2.0 + X4 * Y7 * Y8 * 2.0 + X7 * Y4 * Y8 * 2.0 - X7 * Y5 * Y7 * 2.0 + X8 * Y4 * Y7 * 2.0 + X8 * Y5 * Y8 * 2.0 + X6 * Y8 * Y9 * 2.0 + X8 * Y6 * Y9 * 2.0 - X9 * Y5 * Y9 * 2.0 + X9 * Y6 * Y8 * 2.0;
    B0[4 + 3 * 9] = Y5 * t11 - Y5 * t12 + Y5 * t13 + Y5 * t14 + Y5 * t15 - Y5 * t16 + Y5 * t17 - Y5 * t18 - Y5 * t19 + Y1 * Y2 * Y4 * 2.0 + Y2 * Y3 * Y6 * 2.0 + Y4 * Y7 * Y8 * 2.0 + Y6 * Y8 * Y9 * 2.0;
    B0[4 + 4 * 9] = -Z5 * t2 + Z5 * t3 - Z5 * t4 + Z5 * t5 + Z5 * t6 * 3.0 + Z5 * t7 - Z5 * t8 + Z5 * t9 - Z5 * t10 + X1 * X2 * Z4 * 2.0 + X1 * X4 * Z2 * 2.0 - X1 * X5 * Z1 * 2.0 + X2 * X4 * Z1 * 2.0 + X2 * X5 * Z2 * 2.0 + X2 * X3 * Z6 * 2.0 + X2 * X6 * Z3 * 2.0 - X3 * X5 * Z3 * 2.0 + X3 * X6 * Z2 * 2.0 + X4 * X5 * Z4 * 2.0 + X5 * X6 * Z6 * 2.0 + X4 * X7 * Z8 * 2.0 + X4 * X8 * Z7 * 2.0 - X5 * X7 * Z7 * 2.0 + X7 * X8 * Z4 * 2.0 + X5 * X8 * Z8 * 2.0 - X5 * X9 * Z9 * 2.0 + X6 * X8 * Z9 * 2.0 + X6 * X9 * Z8 * 2.0 + X8 * X9 * Z6 * 2.0;
    B0[4 + 5 * 9] = X1 * Y1 * Z5 * -2.0 + X1 * Y2 * Z4 * 2.0 + X1 * Y4 * Z2 * 2.0 - X1 * Y5 * Z1 * 2.0 + X2 * Y1 * Z4 * 2.0 + X2 * Y4 * Z1 * 2.0 + X4 * Y1 * Z2 * 2.0 + X4 * Y2 * Z1 * 2.0 - X5 * Y1 * Z1 * 2.0 + X2 * Y2 * Z5 * 2.0 + X2 * Y5 * Z2 * 2.0 + X5 * Y2 * Z2 * 2.0 + X2 * Y3 * Z6 * 2.0 + X2 * Y6 * Z3 * 2.0 + X3 * Y2 * Z6 * 2.0 - X3 * Y3 * Z5 * 2.0 - X3 * Y5 * Z3 * 2.0 + X3 * Y6 * Z2 * 2.0 - X5 * Y3 * Z3 * 2.0 + X6 * Y2 * Z3 * 2.0 + X6 * Y3 * Z2 * 2.0 + X4 * Y4 * Z5 * 2.0 + X4 * Y5 * Z4 * 2.0 + X5 * Y4 * Z4 * 2.0 + X5 * Y5 * Z5 * 6.0 + X5 * Y6 * Z6 * 2.0 + X6 * Y5 * Z6 * 2.0 + X6 * Y6 * Z5 * 2.0 + X4 * Y7 * Z8 * 2.0 + X4 * Y8 * Z7 * 2.0 - X5 * Y7 * Z7 * 2.0 + X7 * Y4 * Z8 * 2.0 - X7 * Y5 * Z7 * 2.0 - X7 * Y7 * Z5 * 2.0 + X7 * Y8 * Z4 * 2.0 + X8 * Y4 * Z7 * 2.0 + X8 * Y7 * Z4 * 2.0 + X5 * Y8 * Z8 * 2.0 + X8 * Y5 * Z8 * 2.0 + X8 * Y8 * Z5 * 2.0 - X5 * Y9 * Z9 * 2.0 + X6 * Y8 * Z9 * 2.0 + X6 * Y9 * Z8 * 2.0 + X8 * Y6 * Z9 * 2.0 + X8 * Y9 * Z6 * 2.0 - X9 * Y5 * Z9 * 2.0 + X9 * Y6 * Z8 * 2.0 + X9 * Y8 * Z6 * 2.0 - X9 * Y9 * Z5 * 2.0;
    B0[4 + 6 * 9] = Z5 * t11 - Z5 * t12 + Z5 * t13 + Z5 * t14 * 3.0 + Z5 * t15 - Z5 * t16 + Z5 * t17 - Z5 * t18 - Z5 * t19 + Y1 * Y2 * Z4 * 2.0 + Y1 * Y4 * Z2 * 2.0 - Y1 * Y5 * Z1 * 2.0 + Y2 * Y4 * Z1 * 2.0 + Y2 * Y5 * Z2 * 2.0 + Y2 * Y3 * Z6 * 2.0 + Y2 * Y6 * Z3 * 2.0 - Y3 * Y5 * Z3 * 2.0 + Y3 * Y6 * Z2 * 2.0 + Y4 * Y5 * Z4 * 2.0 + Y5 * Y6 * Z6 * 2.0 + Y4 * Y7 * Z8 * 2.0 + Y4 * Y8 * Z7 * 2.0 - Y5 * Y7 * Z7 * 2.0 + Y7 * Y8 * Z4 * 2.0 + Y5 * Y8 * Z8 * 2.0 - Y5 * Y9 * Z9 * 2.0 + Y6 * Y8 * Z9 * 2.0 + Y6 * Y9 * Z8 * 2.0 + Y8 * Y9 * Z6 * 2.0;
    B0[4 + 7 * 9] = -X5 * t20 + X5 * t21 - X5 * t22 + X5 * t23 + X5 * t24 * 3.0 + X5 * t25 - X5 * t26 + X5 * t27 - X5 * t28 - X1 * Z1 * Z5 * 2.0 + X1 * Z2 * Z4 * 2.0 + X2 * Z1 * Z4 * 2.0 + X4 * Z1 * Z2 * 2.0 + X2 * Z2 * Z5 * 2.0 + X2 * Z3 * Z6 * 2.0 + X3 * Z2 * Z6 * 2.0 - X3 * Z3 * Z5 * 2.0 + X6 * Z2 * Z3 * 2.0 + X4 * Z4 * Z5 * 2.0 + X6 * Z5 * Z6 * 2.0 + X4 * Z7 * Z8 * 2.0 + X7 * Z4 * Z8 * 2.0 - X7 * Z5 * Z7 * 2.0 + X8 * Z4 * Z7 * 2.0 + X8 * Z5 * Z8 * 2.0 + X6 * Z8 * Z9 * 2.0 + X8 * Z6 * Z9 * 2.0 - X9 * Z5 * Z9 * 2.0 + X9 * Z6 * Z8 * 2.0;
    B0[4 + 8 * 9] = -Y5 * t20 + Y5 * t21 - Y5 * t22 + Y5 * t23 + Y5 * t24 * 3.0 + Y5 * t25 - Y5 * t26 + Y5 * t27 - Y5 * t28 - Y1 * Z1 * Z5 * 2.0 + Y1 * Z2 * Z4 * 2.0 + Y2 * Z1 * Z4 * 2.0 + Y4 * Z1 * Z2 * 2.0 + Y2 * Z2 * Z5 * 2.0 + Y2 * Z3 * Z6 * 2.0 + Y3 * Z2 * Z6 * 2.0 - Y3 * Z3 * Z5 * 2.0 + Y6 * Z2 * Z3 * 2.0 + Y4 * Z4 * Z5 * 2.0 + Y6 * Z5 * Z6 * 2.0 + Y4 * Z7 * Z8 * 2.0 + Y7 * Z4 * Z8 * 2.0 - Y7 * Z5 * Z7 * 2.0 + Y8 * Z4 * Z7 * 2.0 + Y8 * Z5 * Z8 * 2.0 + Y6 * Z8 * Z9 * 2.0 + Y8 * Z6 * Z9 * 2.0 - Y9 * Z5 * Z9 * 2.0 + Y9 * Z6 * Z8 * 2.0;
    B0[4 + 9 * 9] = -Z5 * t20 + Z5 * t21 - Z5 * t22 + Z5 * t23 + Z5 * t24 + Z5 * t25 - Z5 * t26 + Z5 * t27 - Z5 * t28 + Z1 * Z2 * Z4 * 2.0 + Z2 * Z3 * Z6 * 2.0 + Z4 * Z7 * Z8 * 2.0 + Z6 * Z8 * Z9 * 2.0;

    B0[5 + 0 * 9] = -X6 * t2 - X6 * t3 + X6 * t4 + X6 * t5 + X6 * t6 + X6 * t7 - X6 * t8 - X6 * t9 + X6 * t10 + X1 * X3 * X4 * 2.0 + X2 * X3 * X5 * 2.0 + X4 * X7 * X9 * 2.0 + X5 * X8 * X9 * 2.0;
    B0[5 + 1 * 9] = -Y6 * t2 - Y6 * t3 + Y6 * t4 + Y6 * t5 + Y6 * t6 + Y6 * t7 * 3.0 - Y6 * t8 - Y6 * t9 + Y6 * t10 + X1 * X3 * Y4 * 2.0 + X1 * X4 * Y3 * 2.0 - X1 * X6 * Y1 * 2.0 + X3 * X4 * Y1 * 2.0 + X2 * X3 * Y5 * 2.0 + X2 * X5 * Y3 * 2.0 - X2 * X6 * Y2 * 2.0 + X3 * X5 * Y2 * 2.0 + X3 * X6 * Y3 * 2.0 + X4 * X6 * Y4 * 2.0 + X5 * X6 * Y5 * 2.0 + X4 * X7 * Y9 * 2.0 + X4 * X9 * Y7 * 2.0 - X6 * X7 * Y7 * 2.0 + X7 * X9 * Y4 * 2.0 + X5 * X8 * Y9 * 2.0 + X5 * X9 * Y8 * 2.0 - X6 * X8 * Y8 * 2.0 + X8 * X9 * Y5 * 2.0 + X6 * X9 * Y9 * 2.0;
    B0[5 + 2 * 9] = -X6 * t11 + X6 * t12 + X6 * t13 + X6 * t14 + X6 * t15 * 3.0 - X6 * t16 - X6 * t17 + X6 * t18 - X6 * t19 - X1 * Y1 * Y6 * 2.0 + X1 * Y3 * Y4 * 2.0 + X3 * Y1 * Y4 * 2.0 + X4 * Y1 * Y3 * 2.0 - X2 * Y2 * Y6 * 2.0 + X2 * Y3 * Y5 * 2.0 + X3 * Y2 * Y5 * 2.0 + X5 * Y2 * Y3 * 2.0 + X3 * Y3 * Y6 * 2.0 + X4 * Y4 * Y6 * 2.0 + X5 * Y5 * Y6 * 2.0 + X4 * Y7 * Y9 * 2.0 + X7 * Y4 * Y9 * 2.0 - X7 * Y6 * Y7 * 2.0 + X9 * Y4 * Y7 * 2.0 + X5 * Y8 * Y9 * 2.0 + X8 * Y5 * Y9 * 2.0 - X8 * Y6 * Y8 * 2.0 + X9 * Y5 * Y8 * 2.0 + X9 * Y6 * Y9 * 2.0;
    B0[5 + 3 * 9] = -Y6 * t11 + Y6 * t12 + Y6 * t13 + Y6 * t14 + Y6 * t15 - Y6 * t16 - Y6 * t17 + Y6 * t18 - Y6 * t19 + Y1 * Y3 * Y4 * 2.0 + Y2 * Y3 * Y5 * 2.0 + Y4 * Y7 * Y9 * 2.0 + Y5 * Y8 * Y9 * 2.0;
    B0[5 + 4 * 9] = -Z6 * t2 - Z6 * t3 + Z6 * t4 + Z6 * t5 + Z6 * t6 + Z6 * t7 * 3.0 - Z6 * t8 - Z6 * t9 + Z6 * t10 + X1 * X3 * Z4 * 2.0 + X1 * X4 * Z3 * 2.0 - X1 * X6 * Z1 * 2.0 + X3 * X4 * Z1 * 2.0 + X2 * X3 * Z5 * 2.0 + X2 * X5 * Z3 * 2.0 - X2 * X6 * Z2 * 2.0 + X3 * X5 * Z2 * 2.0 + X3 * X6 * Z3 * 2.0 + X4 * X6 * Z4 * 2.0 + X5 * X6 * Z5 * 2.0 + X4 * X7 * Z9 * 2.0 + X4 * X9 * Z7 * 2.0 - X6 * X7 * Z7 * 2.0 + X7 * X9 * Z4 * 2.0 + X5 * X8 * Z9 * 2.0 + X5 * X9 * Z8 * 2.0 - X6 * X8 * Z8 * 2.0 + X8 * X9 * Z5 * 2.0 + X6 * X9 * Z9 * 2.0;
    B0[5 + 5 * 9] = X1 * Y1 * Z6 * -2.0 + X1 * Y3 * Z4 * 2.0 + X1 * Y4 * Z3 * 2.0 - X1 * Y6 * Z1 * 2.0 + X3 * Y1 * Z4 * 2.0 + X3 * Y4 * Z1 * 2.0 + X4 * Y1 * Z3 * 2.0 + X4 * Y3 * Z1 * 2.0 - X6 * Y1 * Z1 * 2.0 - X2 * Y2 * Z6 * 2.0 + X2 * Y3 * Z5 * 2.0 + X2 * Y5 * Z3 * 2.0 - X2 * Y6 * Z2 * 2.0 + X3 * Y2 * Z5 * 2.0 + X3 * Y5 * Z2 * 2.0 + X5 * Y2 * Z3 * 2.0 + X5 * Y3 * Z2 * 2.0 - X6 * Y2 * Z2 * 2.0 + X3 * Y3 * Z6 * 2.0 + X3 * Y6 * Z3 * 2.0 + X6 * Y3 * Z3 * 2.0 + X4 * Y4 * Z6 * 2.0 + X4 * Y6 * Z4 * 2.0 + X6 * Y4 * Z4 * 2.0 + X5 * Y5 * Z6 * 2.0 + X5 * Y6 * Z5 * 2.0 + X6 * Y5 * Z5 * 2.0 + X6 * Y6 * Z6 * 6.0 + X4 * Y7 * Z9 * 2.0 + X4 * Y9 * Z7 * 2.0 - X6 * Y7 * Z7 * 2.0 + X7 * Y4 * Z9 * 2.0 - X7 * Y6 * Z7 * 2.0 - X7 * Y7 * Z6 * 2.0 + X7 * Y9 * Z4 * 2.0 + X9 * Y4 * Z7 * 2.0 + X9 * Y7 * Z4 * 2.0 + X5 * Y8 * Z9 * 2.0 + X5 * Y9 * Z8 * 2.0 - X6 * Y8 * Z8 * 2.0 + X8 * Y5 * Z9 * 2.0 - X8 * Y6 * Z8 * 2.0 - X8 * Y8 * Z6 * 2.0 + X8 * Y9 * Z5 * 2.0 + X9 * Y5 * Z8 * 2.0 + X9 * Y8 * Z5 * 2.0 + X6 * Y9 * Z9 * 2.0 + X9 * Y6 * Z9 * 2.0 + X9 * Y9 * Z6 * 2.0;
    B0[5 + 6 * 9] = -Z6 * t11 + Z6 * t12 + Z6 * t13 + Z6 * t14 + Z6 * t15 * 3.0 - Z6 * t16 - Z6 * t17 + Z6 * t18 - Z6 * t19 + Y1 * Y3 * Z4 * 2.0 + Y1 * Y4 * Z3 * 2.0 - Y1 * Y6 * Z1 * 2.0 + Y3 * Y4 * Z1 * 2.0 + Y2 * Y3 * Z5 * 2.0 + Y2 * Y5 * Z3 * 2.0 - Y2 * Y6 * Z2 * 2.0 + Y3 * Y5 * Z2 * 2.0 + Y3 * Y6 * Z3 * 2.0 + Y4 * Y6 * Z4 * 2.0 + Y5 * Y6 * Z5 * 2.0 + Y4 * Y7 * Z9 * 2.0 + Y4 * Y9 * Z7 * 2.0 - Y6 * Y7 * Z7 * 2.0 + Y7 * Y9 * Z4 * 2.0 + Y5 * Y8 * Z9 * 2.0 + Y5 * Y9 * Z8 * 2.0 - Y6 * Y8 * Z8 * 2.0 + Y8 * Y9 * Z5 * 2.0 + Y6 * Y9 * Z9 * 2.0;
    B0[5 + 7 * 9] = -X6 * t20 - X6 * t21 + X6 * t22 + X6 * t23 + X6 * t24 + X6 * t25 * 3.0 - X6 * t26 - X6 * t27 + X6 * t28 - X1 * Z1 * Z6 * 2.0 + X1 * Z3 * Z4 * 2.0 + X3 * Z1 * Z4 * 2.0 + X4 * Z1 * Z3 * 2.0 - X2 * Z2 * Z6 * 2.0 + X2 * Z3 * Z5 * 2.0 + X3 * Z2 * Z5 * 2.0 + X5 * Z2 * Z3 * 2.0 + X3 * Z3 * Z6 * 2.0 + X4 * Z4 * Z6 * 2.0 + X5 * Z5 * Z6 * 2.0 + X4 * Z7 * Z9 * 2.0 + X7 * Z4 * Z9 * 2.0 - X7 * Z6 * Z7 * 2.0 + X9 * Z4 * Z7 * 2.0 + X5 * Z8 * Z9 * 2.0 + X8 * Z5 * Z9 * 2.0 - X8 * Z6 * Z8 * 2.0 + X9 * Z5 * Z8 * 2.0 + X9 * Z6 * Z9 * 2.0;
    B0[5 + 8 * 9] = -Y6 * t20 - Y6 * t21 + Y6 * t22 + Y6 * t23 + Y6 * t24 + Y6 * t25 * 3.0 - Y6 * t26 - Y6 * t27 + Y6 * t28 - Y1 * Z1 * Z6 * 2.0 + Y1 * Z3 * Z4 * 2.0 + Y3 * Z1 * Z4 * 2.0 + Y4 * Z1 * Z3 * 2.0 - Y2 * Z2 * Z6 * 2.0 + Y2 * Z3 * Z5 * 2.0 + Y3 * Z2 * Z5 * 2.0 + Y5 * Z2 * Z3 * 2.0 + Y3 * Z3 * Z6 * 2.0 + Y4 * Z4 * Z6 * 2.0 + Y5 * Z5 * Z6 * 2.0 + Y4 * Z7 * Z9 * 2.0 + Y7 * Z4 * Z9 * 2.0 - Y7 * Z6 * Z7 * 2.0 + Y9 * Z4 * Z7 * 2.0 + Y5 * Z8 * Z9 * 2.0 + Y8 * Z5 * Z9 * 2.0 - Y8 * Z6 * Z8 * 2.0 + Y9 * Z5 * Z8 * 2.0 + Y9 * Z6 * Z9 * 2.0;
    B0[5 + 9 * 9] = -Z6 * t20 - Z6 * t21 + Z6 * t22 + Z6 * t23 + Z6 * t24 + Z6 * t25 - Z6 * t26 - Z6 * t27 + Z6 * t28 + Z1 * Z3 * Z4 * 2.0 + Z2 * Z3 * Z5 * 2.0 + Z4 * Z7 * Z9 * 2.0 + Z5 * Z8 * Z9 * 2.0;

    B0[6 + 0 * 9] = X7 * t2 - X7 * t3 - X7 * t4 + X7 * t5 - X7 * t6 - X7 * t7 + X7 * t8 + X7 * t9 + X7 * t10 + X1 * X2 * X8 * 2.0 + X1 * X3 * X9 * 2.0 + X4 * X5 * X8 * 2.0 + X4 * X6 * X9 * 2.0;
    B0[6 + 1 * 9] = Y7 * t2 - Y7 * t3 - Y7 * t4 + Y7 * t5 - Y7 * t6 - Y7 * t7 + Y7 * t8 * 3.0 + Y7 * t9 + Y7 * t10 + X1 * X7 * Y1 * 2.0 + X1 * X2 * Y8 * 2.0 + X1 * X8 * Y2 * 2.0 - X2 * X7 * Y2 * 2.0 + X2 * X8 * Y1 * 2.0 + X1 * X3 * Y9 * 2.0 + X1 * X9 * Y3 * 2.0 - X3 * X7 * Y3 * 2.0 + X3 * X9 * Y1 * 2.0 + X4 * X7 * Y4 * 2.0 + X4 * X5 * Y8 * 2.0 + X4 * X8 * Y5 * 2.0 - X5 * X7 * Y5 * 2.0 + X5 * X8 * Y4 * 2.0 + X4 * X6 * Y9 * 2.0 + X4 * X9 * Y6 * 2.0 - X6 * X7 * Y6 * 2.0 + X6 * X9 * Y4 * 2.0 + X7 * X8 * Y8 * 2.0 + X7 * X9 * Y9 * 2.0;
    B0[6 + 2 * 9] = -X7 * t11 - X7 * t12 + X7 * t13 - X7 * t14 - X7 * t15 + X7 * t16 * 3.0 + X7 * t17 + X7 * t18 + X7 * t19 + X1 * Y1 * Y7 * 2.0 + X1 * Y2 * Y8 * 2.0 + X2 * Y1 * Y8 * 2.0 - X2 * Y2 * Y7 * 2.0 + X8 * Y1 * Y2 * 2.0 + X1 * Y3 * Y9 * 2.0 + X3 * Y1 * Y9 * 2.0 - X3 * Y3 * Y7 * 2.0 + X9 * Y1 * Y3 * 2.0 + X4 * Y4 * Y7 * 2.0 + X4 * Y5 * Y8 * 2.0 + X5 * Y4 * Y8 * 2.0 - X5 * Y5 * Y7 * 2.0 + X8 * Y4 * Y5 * 2.0 + X4 * Y6 * Y9 * 2.0 + X6 * Y4 * Y9 * 2.0 - X6 * Y6 * Y7 * 2.0 + X9 * Y4 * Y6 * 2.0 + X8 * Y7 * Y8 * 2.0 + X9 * Y7 * Y9 * 2.0;
    B0[6 + 3 * 9] = -Y7 * t11 - Y7 * t12 + Y7 * t13 - Y7 * t14 - Y7 * t15 + Y7 * t16 + Y7 * t17 + Y7 * t18 + Y7 * t19 + Y1 * Y2 * Y8 * 2.0 + Y1 * Y3 * Y9 * 2.0 + Y4 * Y5 * Y8 * 2.0 + Y4 * Y6 * Y9 * 2.0;
    B0[6 + 4 * 9] = Z7 * t2 - Z7 * t3 - Z7 * t4 + Z7 * t5 - Z7 * t6 - Z7 * t7 + Z7 * t8 * 3.0 + Z7 * t9 + Z7 * t10 + X1 * X7 * Z1 * 2.0 + X1 * X2 * Z8 * 2.0 + X1 * X8 * Z2 * 2.0 - X2 * X7 * Z2 * 2.0 + X2 * X8 * Z1 * 2.0 + X1 * X3 * Z9 * 2.0 + X1 * X9 * Z3 * 2.0 - X3 * X7 * Z3 * 2.0 + X3 * X9 * Z1 * 2.0 + X4 * X7 * Z4 * 2.0 + X4 * X5 * Z8 * 2.0 + X4 * X8 * Z5 * 2.0 - X5 * X7 * Z5 * 2.0 + X5 * X8 * Z4 * 2.0 + X4 * X6 * Z9 * 2.0 + X4 * X9 * Z6 * 2.0 - X6 * X7 * Z6 * 2.0 + X6 * X9 * Z4 * 2.0 + X7 * X8 * Z8 * 2.0 + X7 * X9 * Z9 * 2.0;
    B0[6 + 5 * 9] = X1 * Y1 * Z7 * 2.0 + X1 * Y7 * Z1 * 2.0 + X7 * Y1 * Z1 * 2.0 + X1 * Y2 * Z8 * 2.0 + X1 * Y8 * Z2 * 2.0 + X2 * Y1 * Z8 * 2.0 - X2 * Y2 * Z7 * 2.0 - X2 * Y7 * Z2 * 2.0 + X2 * Y8 * Z1 * 2.0 - X7 * Y2 * Z2 * 2.0 + X8 * Y1 * Z2 * 2.0 + X8 * Y2 * Z1 * 2.0 + X1 * Y3 * Z9 * 2.0 + X1 * Y9 * Z3 * 2.0 + X3 * Y1 * Z9 * 2.0 - X3 * Y3 * Z7 * 2.0 - X3 * Y7 * Z3 * 2.0 + X3 * Y9 * Z1 * 2.0 - X7 * Y3 * Z3 * 2.0 + X9 * Y1 * Z3 * 2.0 + X9 * Y3 * Z1 * 2.0 + X4 * Y4 * Z7 * 2.0 + X4 * Y7 * Z4 * 2.0 + X7 * Y4 * Z4 * 2.0 + X4 * Y5 * Z8 * 2.0 + X4 * Y8 * Z5 * 2.0 + X5 * Y4 * Z8 * 2.0 - X5 * Y5 * Z7 * 2.0 - X5 * Y7 * Z5 * 2.0 + X5 * Y8 * Z4 * 2.0 - X7 * Y5 * Z5 * 2.0 + X8 * Y4 * Z5 * 2.0 + X8 * Y5 * Z4 * 2.0 + X4 * Y6 * Z9 * 2.0 + X4 * Y9 * Z6 * 2.0 + X6 * Y4 * Z9 * 2.0 - X6 * Y6 * Z7 * 2.0 - X6 * Y7 * Z6 * 2.0 + X6 * Y9 * Z4 * 2.0 - X7 * Y6 * Z6 * 2.0 + X9 * Y4 * Z6 * 2.0 + X9 * Y6 * Z4 * 2.0 + X7 * Y7 * Z7 * 6.0 + X7 * Y8 * Z8 * 2.0 + X8 * Y7 * Z8 * 2.0 + X8 * Y8 * Z7 * 2.0 + X7 * Y9 * Z9 * 2.0 + X9 * Y7 * Z9 * 2.0 + X9 * Y9 * Z7 * 2.0;
    B0[6 + 6 * 9] = -Z7 * t11 - Z7 * t12 + Z7 * t13 - Z7 * t14 - Z7 * t15 + Z7 * t16 * 3.0 + Z7 * t17 + Z7 * t18 + Z7 * t19 + Y1 * Y7 * Z1 * 2.0 + Y1 * Y2 * Z8 * 2.0 + Y1 * Y8 * Z2 * 2.0 - Y2 * Y7 * Z2 * 2.0 + Y2 * Y8 * Z1 * 2.0 + Y1 * Y3 * Z9 * 2.0 + Y1 * Y9 * Z3 * 2.0 - Y3 * Y7 * Z3 * 2.0 + Y3 * Y9 * Z1 * 2.0 + Y4 * Y7 * Z4 * 2.0 + Y4 * Y5 * Z8 * 2.0 + Y4 * Y8 * Z5 * 2.0 - Y5 * Y7 * Z5 * 2.0 + Y5 * Y8 * Z4 * 2.0 + Y4 * Y6 * Z9 * 2.0 + Y4 * Y9 * Z6 * 2.0 - Y6 * Y7 * Z6 * 2.0 + Y6 * Y9 * Z4 * 2.0 + Y7 * Y8 * Z8 * 2.0 + Y7 * Y9 * Z9 * 2.0;
    B0[6 + 7 * 9] = X7 * t20 - X7 * t21 - X7 * t22 + X7 * t23 - X7 * t24 - X7 * t25 + X7 * t26 * 3.0 + X7 * t27 + X7 * t28 + X1 * Z1 * Z7 * 2.0 + X1 * Z2 * Z8 * 2.0 + X2 * Z1 * Z8 * 2.0 - X2 * Z2 * Z7 * 2.0 + X8 * Z1 * Z2 * 2.0 + X1 * Z3 * Z9 * 2.0 + X3 * Z1 * Z9 * 2.0 - X3 * Z3 * Z7 * 2.0 + X9 * Z1 * Z3 * 2.0 + X4 * Z4 * Z7 * 2.0 + X4 * Z5 * Z8 * 2.0 + X5 * Z4 * Z8 * 2.0 - X5 * Z5 * Z7 * 2.0 + X8 * Z4 * Z5 * 2.0 + X4 * Z6 * Z9 * 2.0 + X6 * Z4 * Z9 * 2.0 - X6 * Z6 * Z7 * 2.0 + X9 * Z4 * Z6 * 2.0 + X8 * Z7 * Z8 * 2.0 + X9 * Z7 * Z9 * 2.0;
    B0[6 + 8 * 9] = Y7 * t20 - Y7 * t21 - Y7 * t22 + Y7 * t23 - Y7 * t24 - Y7 * t25 + Y7 * t26 * 3.0 + Y7 * t27 + Y7 * t28 + Y1 * Z1 * Z7 * 2.0 + Y1 * Z2 * Z8 * 2.0 + Y2 * Z1 * Z8 * 2.0 - Y2 * Z2 * Z7 * 2.0 + Y8 * Z1 * Z2 * 2.0 + Y1 * Z3 * Z9 * 2.0 + Y3 * Z1 * Z9 * 2.0 - Y3 * Z3 * Z7 * 2.0 + Y9 * Z1 * Z3 * 2.0 + Y4 * Z4 * Z7 * 2.0 + Y4 * Z5 * Z8 * 2.0 + Y5 * Z4 * Z8 * 2.0 - Y5 * Z5 * Z7 * 2.0 + Y8 * Z4 * Z5 * 2.0 + Y4 * Z6 * Z9 * 2.0 + Y6 * Z4 * Z9 * 2.0 - Y6 * Z6 * Z7 * 2.0 + Y9 * Z4 * Z6 * 2.0 + Y8 * Z7 * Z8 * 2.0 + Y9 * Z7 * Z9 * 2.0;
    B0[6 + 9 * 9] = Z7 * t20 - Z7 * t21 - Z7 * t22 + Z7 * t23 - Z7 * t24 - Z7 * t25 + Z7 * t26 + Z7 * t27 + Z7 * t28 + Z1 * Z2 * Z8 * 2.0 + Z1 * Z3 * Z9 * 2.0 + Z4 * Z5 * Z8 * 2.0 + Z4 * Z6 * Z9 * 2.0;

    B0[7 + 0 * 9] = -X8 * t2 + X8 * t3 - X8 * t4 - X8 * t5 + X8 * t6 - X8 * t7 + X8 * t8 + X8 * t9 + X8 * t10 + X1 * X2 * X7 * 2.0 + X2 * X3 * X9 * 2.0 + X4 * X5 * X7 * 2.0 + X5 * X6 * X9 * 2.0;
    B0[7 + 1 * 9] = -Y8 * t2 + Y8 * t3 - Y8 * t4 - Y8 * t5 + Y8 * t6 - Y8 * t7 + Y8 * t8 + Y8 * t9 * 3.0 + Y8 * t10 + X1 * X2 * Y7 * 2.0 + X1 * X7 * Y2 * 2.0 - X1 * X8 * Y1 * 2.0 + X2 * X7 * Y1 * 2.0 + X2 * X8 * Y2 * 2.0 + X2 * X3 * Y9 * 2.0 + X2 * X9 * Y3 * 2.0 - X3 * X8 * Y3 * 2.0 + X3 * X9 * Y2 * 2.0 + X4 * X5 * Y7 * 2.0 + X4 * X7 * Y5 * 2.0 - X4 * X8 * Y4 * 2.0 + X5 * X7 * Y4 * 2.0 + X5 * X8 * Y5 * 2.0 + X5 * X6 * Y9 * 2.0 + X5 * X9 * Y6 * 2.0 - X6 * X8 * Y6 * 2.0 + X6 * X9 * Y5 * 2.0 + X7 * X8 * Y7 * 2.0 + X8 * X9 * Y9 * 2.0;
    B0[7 + 2 * 9] = X8 * t11 - X8 * t12 - X8 * t13 + X8 * t14 - X8 * t15 + X8 * t16 + X8 * t17 * 3.0 + X8 * t18 - X8 * t19 - X1 * Y1 * Y8 * 2.0 + X1 * Y2 * Y7 * 2.0 + X2 * Y1 * Y7 * 2.0 + X7 * Y1 * Y2 * 2.0 + X2 * Y2 * Y8 * 2.0 + X2 * Y3 * Y9 * 2.0 + X3 * Y2 * Y9 * 2.0 - X3 * Y3 * Y8 * 2.0 + X9 * Y2 * Y3 * 2.0 - X4 * Y4 * Y8 * 2.0 + X4 * Y5 * Y7 * 2.0 + X5 * Y4 * Y7 * 2.0 + X7 * Y4 * Y5 * 2.0 + X5 * Y5 * Y8 * 2.0 + X5 * Y6 * Y9 * 2.0 + X6 * Y5 * Y9 * 2.0 - X6 * Y6 * Y8 * 2.0 + X9 * Y5 * Y6 * 2.0 + X7 * Y7 * Y8 * 2.0 + X9 * Y8 * Y9 * 2.0;
    B0[7 + 3 * 9] = Y8 * t11 - Y8 * t12 - Y8 * t13 + Y8 * t14 - Y8 * t15 + Y8 * t16 + Y8 * t17 + Y8 * t18 - Y8 * t19 + Y1 * Y2 * Y7 * 2.0 + Y2 * Y3 * Y9 * 2.0 + Y4 * Y5 * Y7 * 2.0 + Y5 * Y6 * Y9 * 2.0;
    B0[7 + 4 * 9] = -Z8 * t2 + Z8 * t3 - Z8 * t4 - Z8 * t5 + Z8 * t6 - Z8 * t7 + Z8 * t8 + Z8 * t9 * 3.0 + Z8 * t10 + X1 * X2 * Z7 * 2.0 + X1 * X7 * Z2 * 2.0 - X1 * X8 * Z1 * 2.0 + X2 * X7 * Z1 * 2.0 + X2 * X8 * Z2 * 2.0 + X2 * X3 * Z9 * 2.0 + X2 * X9 * Z3 * 2.0 - X3 * X8 * Z3 * 2.0 + X3 * X9 * Z2 * 2.0 + X4 * X5 * Z7 * 2.0 + X4 * X7 * Z5 * 2.0 - X4 * X8 * Z4 * 2.0 + X5 * X7 * Z4 * 2.0 + X5 * X8 * Z5 * 2.0 + X5 * X6 * Z9 * 2.0 + X5 * X9 * Z6 * 2.0 - X6 * X8 * Z6 * 2.0 + X6 * X9 * Z5 * 2.0 + X7 * X8 * Z7 * 2.0 + X8 * X9 * Z9 * 2.0;
    B0[7 + 5 * 9] = X1 * Y1 * Z8 * -2.0 + X1 * Y2 * Z7 * 2.0 + X1 * Y7 * Z2 * 2.0 - X1 * Y8 * Z1 * 2.0 + X2 * Y1 * Z7 * 2.0 + X2 * Y7 * Z1 * 2.0 + X7 * Y1 * Z2 * 2.0 + X7 * Y2 * Z1 * 2.0 - X8 * Y1 * Z1 * 2.0 + X2 * Y2 * Z8 * 2.0 + X2 * Y8 * Z2 * 2.0 + X8 * Y2 * Z2 * 2.0 + X2 * Y3 * Z9 * 2.0 + X2 * Y9 * Z3 * 2.0 + X3 * Y2 * Z9 * 2.0 - X3 * Y3 * Z8 * 2.0 - X3 * Y8 * Z3 * 2.0 + X3 * Y9 * Z2 * 2.0 - X8 * Y3 * Z3 * 2.0 + X9 * Y2 * Z3 * 2.0 + X9 * Y3 * Z2 * 2.0 - X4 * Y4 * Z8 * 2.0 + X4 * Y5 * Z7 * 2.0 + X4 * Y7 * Z5 * 2.0 - X4 * Y8 * Z4 * 2.0 + X5 * Y4 * Z7 * 2.0 + X5 * Y7 * Z4 * 2.0 + X7 * Y4 * Z5 * 2.0 + X7 * Y5 * Z4 * 2.0 - X8 * Y4 * Z4 * 2.0 + X5 * Y5 * Z8 * 2.0 + X5 * Y8 * Z5 * 2.0 + X8 * Y5 * Z5 * 2.0 + X5 * Y6 * Z9 * 2.0 + X5 * Y9 * Z6 * 2.0 + X6 * Y5 * Z9 * 2.0 - X6 * Y6 * Z8 * 2.0 - X6 * Y8 * Z6 * 2.0 + X6 * Y9 * Z5 * 2.0 - X8 * Y6 * Z6 * 2.0 + X9 * Y5 * Z6 * 2.0 + X9 * Y6 * Z5 * 2.0 + X7 * Y7 * Z8 * 2.0 + X7 * Y8 * Z7 * 2.0 + X8 * Y7 * Z7 * 2.0 + X8 * Y8 * Z8 * 6.0 + X8 * Y9 * Z9 * 2.0 + X9 * Y8 * Z9 * 2.0 + X9 * Y9 * Z8 * 2.0;
    B0[7 + 6 * 9] = Z8 * t11 - Z8 * t12 - Z8 * t13 + Z8 * t14 - Z8 * t15 + Z8 * t16 + Z8 * t17 * 3.0 + Z8 * t18 - Z8 * t19 + Y1 * Y2 * Z7 * 2.0 + Y1 * Y7 * Z2 * 2.0 - Y1 * Y8 * Z1 * 2.0 + Y2 * Y7 * Z1 * 2.0 + Y2 * Y8 * Z2 * 2.0 + Y2 * Y3 * Z9 * 2.0 + Y2 * Y9 * Z3 * 2.0 - Y3 * Y8 * Z3 * 2.0 + Y3 * Y9 * Z2 * 2.0 + Y4 * Y5 * Z7 * 2.0 + Y4 * Y7 * Z5 * 2.0 - Y4 * Y8 * Z4 * 2.0 + Y5 * Y7 * Z4 * 2.0 + Y5 * Y8 * Z5 * 2.0 + Y5 * Y6 * Z9 * 2.0 + Y5 * Y9 * Z6 * 2.0 - Y6 * Y8 * Z6 * 2.0 + Y6 * Y9 * Z5 * 2.0 + Y7 * Y8 * Z7 * 2.0 + Y8 * Y9 * Z9 * 2.0;
    B0[7 + 7 * 9] = -X8 * t20 + X8 * t21 - X8 * t22 - X8 * t23 + X8 * t24 - X8 * t25 + X8 * t26 + X8 * t27 * 3.0 + X8 * t28 - X1 * Z1 * Z8 * 2.0 + X1 * Z2 * Z7 * 2.0 + X2 * Z1 * Z7 * 2.0 + X7 * Z1 * Z2 * 2.0 + X2 * Z2 * Z8 * 2.0 + X2 * Z3 * Z9 * 2.0 + X3 * Z2 * Z9 * 2.0 - X3 * Z3 * Z8 * 2.0 + X9 * Z2 * Z3 * 2.0 - X4 * Z4 * Z8 * 2.0 + X4 * Z5 * Z7 * 2.0 + X5 * Z4 * Z7 * 2.0 + X7 * Z4 * Z5 * 2.0 + X5 * Z5 * Z8 * 2.0 + X5 * Z6 * Z9 * 2.0 + X6 * Z5 * Z9 * 2.0 - X6 * Z6 * Z8 * 2.0 + X9 * Z5 * Z6 * 2.0 + X7 * Z7 * Z8 * 2.0 + X9 * Z8 * Z9 * 2.0;
    B0[7 + 8 * 9] = -Y8 * t20 + Y8 * t21 - Y8 * t22 - Y8 * t23 + Y8 * t24 - Y8 * t25 + Y8 * t26 + Y8 * t27 * 3.0 + Y8 * t28 - Y1 * Z1 * Z8 * 2.0 + Y1 * Z2 * Z7 * 2.0 + Y2 * Z1 * Z7 * 2.0 + Y7 * Z1 * Z2 * 2.0 + Y2 * Z2 * Z8 * 2.0 + Y2 * Z3 * Z9 * 2.0 + Y3 * Z2 * Z9 * 2.0 - Y3 * Z3 * Z8 * 2.0 + Y9 * Z2 * Z3 * 2.0 - Y4 * Z4 * Z8 * 2.0 + Y4 * Z5 * Z7 * 2.0 + Y5 * Z4 * Z7 * 2.0 + Y7 * Z4 * Z5 * 2.0 + Y5 * Z5 * Z8 * 2.0 + Y5 * Z6 * Z9 * 2.0 + Y6 * Z5 * Z9 * 2.0 - Y6 * Z6 * Z8 * 2.0 + Y9 * Z5 * Z6 * 2.0 + Y7 * Z7 * Z8 * 2.0 + Y9 * Z8 * Z9 * 2.0;
    B0[7 + 9 * 9] = -Z8 * t20 + Z8 * t21 - Z8 * t22 - Z8 * t23 + Z8 * t24 - Z8 * t25 + Z8 * t26 + Z8 * t27 + Z8 * t28 + Z1 * Z2 * Z7 * 2.0 + Z2 * Z3 * Z9 * 2.0 + Z4 * Z5 * Z7 * 2.0 + Z5 * Z6 * Z9 * 2.0;

    B0[8 + 0 * 9] = -X9 * t2 - X9 * t3 + X9 * t4 - X9 * t5 - X9 * t6 + X9 * t7 + X9 * t8 + X9 * t9 + X9 * t10 + X1 * X3 * X7 * 2.0 + X2 * X3 * X8 * 2.0 + X4 * X6 * X7 * 2.0 + X5 * X6 * X8 * 2.0;
    B0[8 + 1 * 9] = -Y9 * t2 - Y9 * t3 + Y9 * t4 - Y9 * t5 - Y9 * t6 + Y9 * t7 + Y9 * t8 + Y9 * t9 + Y9 * t10 * 3.0 + X1 * X3 * Y7 * 2.0 + X1 * X7 * Y3 * 2.0 - X1 * X9 * Y1 * 2.0 + X3 * X7 * Y1 * 2.0 + X2 * X3 * Y8 * 2.0 + X2 * X8 * Y3 * 2.0 - X2 * X9 * Y2 * 2.0 + X3 * X8 * Y2 * 2.0 + X3 * X9 * Y3 * 2.0 + X4 * X6 * Y7 * 2.0 + X4 * X7 * Y6 * 2.0 - X4 * X9 * Y4 * 2.0 + X6 * X7 * Y4 * 2.0 + X5 * X6 * Y8 * 2.0 + X5 * X8 * Y6 * 2.0 - X5 * X9 * Y5 * 2.0 + X6 * X8 * Y5 * 2.0 + X6 * X9 * Y6 * 2.0 + X7 * X9 * Y7 * 2.0 + X8 * X9 * Y8 * 2.0;
    B0[8 + 2 * 9] = -X9 * t11 + X9 * t12 - X9 * t13 - X9 * t14 + X9 * t15 + X9 * t16 + X9 * t17 + X9 * t18 * 3.0 - X9 * t19 - X1 * Y1 * Y9 * 2.0 + X1 * Y3 * Y7 * 2.0 + X3 * Y1 * Y7 * 2.0 + X7 * Y1 * Y3 * 2.0 - X2 * Y2 * Y9 * 2.0 + X2 * Y3 * Y8 * 2.0 + X3 * Y2 * Y8 * 2.0 + X8 * Y2 * Y3 * 2.0 + X3 * Y3 * Y9 * 2.0 - X4 * Y4 * Y9 * 2.0 + X4 * Y6 * Y7 * 2.0 + X6 * Y4 * Y7 * 2.0 + X7 * Y4 * Y6 * 2.0 - X5 * Y5 * Y9 * 2.0 + X5 * Y6 * Y8 * 2.0 + X6 * Y5 * Y8 * 2.0 + X8 * Y5 * Y6 * 2.0 + X6 * Y6 * Y9 * 2.0 + X7 * Y7 * Y9 * 2.0 + X8 * Y8 * Y9 * 2.0;
    B0[8 + 3 * 9] = -Y9 * t11 + Y9 * t12 - Y9 * t13 - Y9 * t14 + Y9 * t15 + Y9 * t16 + Y9 * t17 + Y9 * t18 - Y9 * t19 + Y1 * Y3 * Y7 * 2.0 + Y2 * Y3 * Y8 * 2.0 + Y4 * Y6 * Y7 * 2.0 + Y5 * Y6 * Y8 * 2.0;
    B0[8 + 4 * 9] = -Z9 * t2 - Z9 * t3 + Z9 * t4 - Z9 * t5 - Z9 * t6 + Z9 * t7 + Z9 * t8 + Z9 * t9 + Z9 * t10 * 3.0 + X1 * X3 * Z7 * 2.0 + X1 * X7 * Z3 * 2.0 - X1 * X9 * Z1 * 2.0 + X3 * X7 * Z1 * 2.0 + X2 * X3 * Z8 * 2.0 + X2 * X8 * Z3 * 2.0 - X2 * X9 * Z2 * 2.0 + X3 * X8 * Z2 * 2.0 + X3 * X9 * Z3 * 2.0 + X4 * X6 * Z7 * 2.0 + X4 * X7 * Z6 * 2.0 - X4 * X9 * Z4 * 2.0 + X6 * X7 * Z4 * 2.0 + X5 * X6 * Z8 * 2.0 + X5 * X8 * Z6 * 2.0 - X5 * X9 * Z5 * 2.0 + X6 * X8 * Z5 * 2.0 + X6 * X9 * Z6 * 2.0 + X7 * X9 * Z7 * 2.0 + X8 * X9 * Z8 * 2.0;
    B0[8 + 5 * 9] = X1 * Y1 * Z9 * -2.0 + X1 * Y3 * Z7 * 2.0 + X1 * Y7 * Z3 * 2.0 - X1 * Y9 * Z1 * 2.0 + X3 * Y1 * Z7 * 2.0 + X3 * Y7 * Z1 * 2.0 + X7 * Y1 * Z3 * 2.0 + X7 * Y3 * Z1 * 2.0 - X9 * Y1 * Z1 * 2.0 - X2 * Y2 * Z9 * 2.0 + X2 * Y3 * Z8 * 2.0 + X2 * Y8 * Z3 * 2.0 - X2 * Y9 * Z2 * 2.0 + X3 * Y2 * Z8 * 2.0 + X3 * Y8 * Z2 * 2.0 + X8 * Y2 * Z3 * 2.0 + X8 * Y3 * Z2 * 2.0 - X9 * Y2 * Z2 * 2.0 + X3 * Y3 * Z9 * 2.0 + X3 * Y9 * Z3 * 2.0 + X9 * Y3 * Z3 * 2.0 - X4 * Y4 * Z9 * 2.0 + X4 * Y6 * Z7 * 2.0 + X4 * Y7 * Z6 * 2.0 - X4 * Y9 * Z4 * 2.0 + X6 * Y4 * Z7 * 2.0 + X6 * Y7 * Z4 * 2.0 + X7 * Y4 * Z6 * 2.0 + X7 * Y6 * Z4 * 2.0 - X9 * Y4 * Z4 * 2.0 - X5 * Y5 * Z9 * 2.0 + X5 * Y6 * Z8 * 2.0 + X5 * Y8 * Z6 * 2.0 - X5 * Y9 * Z5 * 2.0 + X6 * Y5 * Z8 * 2.0 + X6 * Y8 * Z5 * 2.0 + X8 * Y5 * Z6 * 2.0 + X8 * Y6 * Z5 * 2.0 - X9 * Y5 * Z5 * 2.0 + X6 * Y6 * Z9 * 2.0 + X6 * Y9 * Z6 * 2.0 + X9 * Y6 * Z6 * 2.0 + X7 * Y7 * Z9 * 2.0 + X7 * Y9 * Z7 * 2.0 + X9 * Y7 * Z7 * 2.0 + X8 * Y8 * Z9 * 2.0 + X8 * Y9 * Z8 * 2.0 + X9 * Y8 * Z8 * 2.0 + X9 * Y9 * Z9 * 6.0;
    B0[8 + 6 * 9] = -Z9 * t11 + Z9 * t12 - Z9 * t13 - Z9 * t14 + Z9 * t15 + Z9 * t16 + Z9 * t17 + Z9 * t18 * 3.0 - Z9 * t19 + Y1 * Y3 * Z7 * 2.0 + Y1 * Y7 * Z3 * 2.0 - Y1 * Y9 * Z1 * 2.0 + Y3 * Y7 * Z1 * 2.0 + Y2 * Y3 * Z8 * 2.0 + Y2 * Y8 * Z3 * 2.0 - Y2 * Y9 * Z2 * 2.0 + Y3 * Y8 * Z2 * 2.0 + Y3 * Y9 * Z3 * 2.0 + Y4 * Y6 * Z7 * 2.0 + Y4 * Y7 * Z6 * 2.0 - Y4 * Y9 * Z4 * 2.0 + Y6 * Y7 * Z4 * 2.0 + Y5 * Y6 * Z8 * 2.0 + Y5 * Y8 * Z6 * 2.0 - Y5 * Y9 * Z5 * 2.0 + Y6 * Y8 * Z5 * 2.0 + Y6 * Y9 * Z6 * 2.0 + Y7 * Y9 * Z7 * 2.0 + Y8 * Y9 * Z8 * 2.0;
    B0[8 + 7 * 9] = -X9 * t20 - X9 * t21 + X9 * t22 - X9 * t23 - X9 * t24 + X9 * t25 + X9 * t26 + X9 * t27 + X9 * t28 * 3.0 - X1 * Z1 * Z9 * 2.0 + X1 * Z3 * Z7 * 2.0 + X3 * Z1 * Z7 * 2.0 + X7 * Z1 * Z3 * 2.0 - X2 * Z2 * Z9 * 2.0 + X2 * Z3 * Z8 * 2.0 + X3 * Z2 * Z8 * 2.0 + X8 * Z2 * Z3 * 2.0 + X3 * Z3 * Z9 * 2.0 - X4 * Z4 * Z9 * 2.0 + X4 * Z6 * Z7 * 2.0 + X6 * Z4 * Z7 * 2.0 + X7 * Z4 * Z6 * 2.0 - X5 * Z5 * Z9 * 2.0 + X5 * Z6 * Z8 * 2.0 + X6 * Z5 * Z8 * 2.0 + X8 * Z5 * Z6 * 2.0 + X6 * Z6 * Z9 * 2.0 + X7 * Z7 * Z9 * 2.0 + X8 * Z8 * Z9 * 2.0;
    B0[8 + 8 * 9] = -Y9 * t20 - Y9 * t21 + Y9 * t22 - Y9 * t23 - Y9 * t24 + Y9 * t25 + Y9 * t26 + Y9 * t27 + Y9 * t28 * 3.0 - Y1 * Z1 * Z9 * 2.0 + Y1 * Z3 * Z7 * 2.0 + Y3 * Z1 * Z7 * 2.0 + Y7 * Z1 * Z3 * 2.0 - Y2 * Z2 * Z9 * 2.0 + Y2 * Z3 * Z8 * 2.0 + Y3 * Z2 * Z8 * 2.0 + Y8 * Z2 * Z3 * 2.0 + Y3 * Z3 * Z9 * 2.0 - Y4 * Z4 * Z9 * 2.0 + Y4 * Z6 * Z7 * 2.0 + Y6 * Z4 * Z7 * 2.0 + Y7 * Z4 * Z6 * 2.0 - Y5 * Z5 * Z9 * 2.0 + Y5 * Z6 * Z8 * 2.0 + Y6 * Z5 * Z8 * 2.0 + Y8 * Z5 * Z6 * 2.0 + Y6 * Z6 * Z9 * 2.0 + Y7 * Z7 * Z9 * 2.0 + Y8 * Z8 * Z9 * 2.0;
    B0[8 + 9 * 9] = -Z9 * t20 - Z9 * t21 + Z9 * t22 - Z9 * t23 - Z9 * t24 + Z9 * t25 + Z9 * t26 + Z9 * t27 + Z9 * t28 + Z1 * Z3 * Z7 * 2.0 + Z2 * Z3 * Z8 * 2.0 + Z4 * Z6 * Z7 * 2.0 + Z5 * Z6 * Z8 * 2.0;

    Map<Matrix<double, 9, 10, ColMajor>> B_map(B0, 9, 10);
    B = B_map;
    //    std::cout<<B<<std::endl;
}

inline void compute_Dcoefficients(const EigenSols &eigen_sols, Matrix<double, 1, 4> &D)
{

    double X1, X2, X3, X4, X5, X6, X7, X8, X9;
    double Y1, Y2, Y3, Y4, Y5, Y6, Y7, Y8, Y9;

    X1 = eigen_sols(0, 0);
    X2 = eigen_sols(0, 1);
    X3 = eigen_sols(0, 2);
    X4 = eigen_sols(0, 3);
    X5 = eigen_sols(0, 4);
    X6 = eigen_sols(0, 5);
    X7 = eigen_sols(0, 6);
    X8 = eigen_sols(0, 7);
    X9 = eigen_sols(0, 8);

    Y1 = eigen_sols(1, 0);
    Y2 = eigen_sols(1, 1);
    Y3 = eigen_sols(1, 2);
    Y4 = eigen_sols(1, 3);
    Y5 = eigen_sols(1, 4);
    Y6 = eigen_sols(1, 5);
    Y7 = eigen_sols(1, 6);
    Y8 = eigen_sols(1, 7);
    Y9 = eigen_sols(1, 8);

    D[0] = X1 * X5 * X9 - X1 * X6 * X8 - X2 * X4 * X9 + X2 * X6 * X7 + X3 * X4 * X8 - X3 * X5 * X7;
    D[1] = X1 * X5 * Y9 - X1 * X6 * Y8 - X1 * X8 * Y6 + X1 * X9 * Y5 - X2 * X4 * Y9 + X2 * X6 * Y7 + X2 * X7 * Y6 - X2 * X9 * Y4 + X3 * X4 * Y8 - X3 * X5 * Y7 - X3 * X7 * Y5 + X3 * X8 * Y4 + X4 * X8 * Y3 - X4 * X9 * Y2 - X5 * X7 * Y3 + X5 * X9 * Y1 + X6 * X7 * Y2 - X6 * X8 * Y1;
    D[2] = X1 * Y5 * Y9 - X1 * Y6 * Y8 - X2 * Y4 * Y9 + X2 * Y6 * Y7 + X3 * Y4 * Y8 - X3 * Y5 * Y7 - X4 * Y2 * Y9 + X4 * Y3 * Y8 + X5 * Y1 * Y9 - X5 * Y3 * Y7 - X6 * Y1 * Y8 + X6 * Y2 * Y7 + X7 * Y2 * Y6 - X7 * Y3 * Y5 - X8 * Y1 * Y6 + X8 * Y3 * Y4 + X9 * Y1 * Y5 - X9 * Y2 * Y4;
    D[3] = Y1 * Y5 * Y9 - Y1 * Y6 * Y8 - Y2 * Y4 * Y9 + Y2 * Y6 * Y7 + Y3 * Y4 * Y8 - Y3 * Y5 * Y7;
}