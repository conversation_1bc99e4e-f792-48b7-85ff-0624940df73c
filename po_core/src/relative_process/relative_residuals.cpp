// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <fstream>
#include <iomanip>
#include <cstdio>

#include "relative_residuals.hpp"
#include "relative_pose.hpp"
#include <Eigen/Dense>

#include <algorithm>
#include <numeric>
#include <vector>
#include <cmath>

using namespace std;

#define PI 3.141592653589793
namespace PoSDK
{
    Eigen::Matrix3d crossMatrix(const Eigen::Vector3d &x);

    double findMedian(VectorXd a);
    double findMedian(vector<double> a);

    //// Reshape a vector into a matrix
    Eigen::MatrixXd reshape(const Eigen::VectorXd &v, int rows, int cols)
    {
        Eigen::MatrixXd result(rows, cols);
        for (int i = 0; i < rows; ++i)
        {
            for (int j = 0; j < cols; ++j)
            {
                result(i, j) = v(i * cols + j);
            }
        }
        return result;
    }

    // // Function to compute the Kronecker product of two matrices
    // Eigen::MatrixXd kroneckerProduct(const Eigen::MatrixXd &A, const Eigen::MatrixXd &B) {
    //     Eigen::MatrixXd Kronecker(A.rows()*B.rows(), A.cols()*B.cols());
    //     for(int i = 0; i < A.rows(); ++i) {
    //         for(int j = 0; j < A.cols(); ++j) {
    //             Kronecker.block(i*B.rows(), j*B.cols(), B.rows(), B.cols()) = A(i, j) * B;
    //         }
    //     }
    //     return Kronecker;
    // }

    VectorXd residual_BA(const BearingVectors &v1,
                         const BearingVectors &v2,
                         const RelativePose &pose,
                         const VectorXd *ptr_weigths)
    {
        int num_points = v1.cols();
        MatrixXd points3D(3, num_points);

        Matrix3d R = pose.Rij;
        Vector3d t = pose.tij;

        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            points3D.col(i) = triangulateOnePoint(R, t, point1, point2);
        }

        MatrixXd reproj_v1 = points3D;
        MatrixXd reproj_v2 = R.transpose() * (points3D.colwise() - t);

        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1(2, i);
            reproj_v2.col(i) = reproj_v2.col(i) / reproj_v2(2, i);
        }

        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            Vector3d v1_norm = v1.col(i) / v1(2, i);
            Vector3d v2_norm = v2.col(i) / v2(2, i);
            Vector3d diff1 = v1_norm - reproj_v1.col(i);
            Vector3d diff2 = v2_norm - reproj_v2.col(i);

            errors(i) = diff1.squaredNorm() + diff2.squaredNorm();
        }

        VectorXd result = errors.array().sqrt();
        return result;
    }

    VectorXd residual_BA(const BearingVectors &v1,
                         const BearingVectors &v2,
                         const RelativePose &pose,
                         const Matrix3d &Kmat,
                         const VectorXd *ptr_weights)
    {
        int num_points = v1.cols();
        MatrixXd points3D(3, num_points);

        Matrix3d R = pose.Rij;
        Vector3d t = pose.tij;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            // 使用三角测量计算三维点
            points3D.col(i) = triangulateOnePoint(R, t, point1, point2);
        }

        // 使用内参数矩阵将三维点投影回图像平面
        MatrixXd reproj_v1 = Kmat * points3D;
        MatrixXd reproj_v2 = Kmat * (R.transpose() * (points3D.colwise() - t));

        // 归一化投影点，使其第三个坐标为 1
        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) /= reproj_v1(2, i);
            reproj_v2.col(i) /= reproj_v2(2, i);
        }

        // 计算像素级别的重投影误差
        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            // 从同质坐标归一化到二维坐标
            Vector2d original_v1 = v1.block<2, 1>(0, i) / v1(2, i);
            Vector2d original_v2 = v2.block<2, 1>(0, i) / v2(2, i);

            // 计算重投影误差
            Vector2d diff1 = original_v1 - reproj_v1.block<2, 1>(0, i);
            Vector2d diff2 = original_v2 - reproj_v2.block<2, 1>(0, i);
            errors(i) = diff1.squaredNorm() + diff2.squaredNorm();
        }

        VectorXd result = errors.array().sqrt();
        return result;
    }

    VectorXd residual_opengv(const BearingVectors &v1,
                             const BearingVectors &v2,
                             const RelativePose &pose,
                             const VectorXd *ptr_weights)
    {
        int num_points = v1.cols();
        MatrixXd points3D(3, num_points);

        Matrix3d R = pose.Rij;
        Vector3d t = pose.tij;

        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            points3D.col(i) = triangulate2(R, t, point1, point2);
        }

        MatrixXd reproj_v1 = points3D;
        MatrixXd reproj_v2 = R.transpose() * (points3D.colwise() - t);

        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1.col(i).norm();
            reproj_v2.col(i) = reproj_v2.col(i) / reproj_v2.col(i).norm();
        }

        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            double dot1 = v1.col(i).dot(reproj_v1.col(i));
            double dot2 = v2.col(i).dot(reproj_v2.col(i));
            errors(i) = (1 - dot1) + (1 - dot2);
        }

        return errors;
    }

    VectorXd residual_LiGT_Lmat(const BearingVectors &v1,
                                const BearingVectors &v2,
                                const RelativePose &pose,
                                Matrix3d &Lmat)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        Lmat.setZero();

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            double tmp5 = tmp4 * t.norm() + tmp2.dot(tmp3) * (R * X2).norm();

            Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
            Matrix3d tmp_L = tmp4 * crossMatrix(X1) + crossMatrix(X1) * (R * X2) * tmp2.transpose() * crossMatrix(X1);
            Lmat += tmp_L.transpose() * tmp_L;
            residual(k) = cost_vec.dot(cost_vec);
        }

        JacobiSVD<Matrix3d> svd(Lmat, Eigen::ComputeFullU | Eigen::ComputeFullV);
        MatrixXd U = svd.matrixU();
        MatrixXd S = svd.singularValues().asDiagonal();
        MatrixXd V = svd.matrixV();

        // 获取并打印最小奇异值
        double minSingularValue = svd.singularValues().minCoeff();
        cout << "Lmat Singular Values: " << svd.singularValues().transpose() << endl;

        return residual.array().sqrt();
    }

    VectorXd residual_LiRT(const BearingVectors &v1,
                           const BearingVectors &v2,
                           const RelativePose &pose,
                           const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp = X1.cross(R * X2);

            Matrix3d tmp1 = X1 * tmp.transpose() * crossMatrix(R * X2);
            Matrix3d tmp2 = (R * X2) * tmp.transpose() * crossMatrix(X1);
            Matrix3d tmp3 = tmp.squaredNorm() * Matrix3d::Identity();
            Vector3d cost_vec = (tmp1 - tmp2 + tmp3) * t;

            residual(k) = cost_vec.dot(cost_vec);
        }

        return residual.array().sqrt();
    }
    VectorXd residual_LiGT(const BearingVectors &v1,
                           const BearingVectors &v2,
                           const RelativePose &pose,
                           const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            double tmp5 = tmp4 * t.norm() + tmp2.dot(tmp3) * (R * X2).norm();

            Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
            cost_vec = cost_vec / tmp3.squaredNorm() * tmp2.norm(); // matlab LiGTlr,best
            residual(k) = cost_vec.dot(cost_vec);
        }

        return residual.array().sqrt();
    }

    VectorXd residual_Kneip(const BearingVectors &v1,
                            const BearingVectors &v2,
                            const RelativePose &pose,
                            const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp = X1.cross(R * X2);

            // 修复数学错误：使用Kneip残差的正确公式
            // Kneip残差基于平移向量与交叉乘积的夹角
            double cross_norm = tmp.norm();
            if (cross_norm > 1e-12) // 避免除零
            {
                // 使用归一化的交叉乘积与平移向量的内积
                residual(k) = std::abs(tmp.dot(t)) / cross_norm;
            }
            else
            {
                residual(k) = 0.0; // 如果交叉乘积为零，残差为零
            }
        }

        return residual;
    }

    VectorXd residual_opengv(const BearingVectors &v1,
                             const BearingVectors &v2,
                             const RelativePose &pose,
                             Matrix<double, 3, Dynamic> &points3D,
                             const VectorXd *ptr_weights)
    {
        int num_points = v1.cols();
        points3D.setZero(3, num_points);

        Matrix3d R = pose.Rij;
        Vector3d t = pose.tij;

        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            points3D.col(i) = triangulateOnePoint(R, t, point1, point2);
        }

        MatrixXd reproj_v1 = points3D;
        MatrixXd reproj_v2 = R.transpose() * (points3D.colwise() - t);

        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1.col(i).norm();
            reproj_v2.col(i) = reproj_v2.col(i) / reproj_v2.col(i).norm();
        }

        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            errors(i) = (1 - v1.col(i).dot(reproj_v1.col(i))) + (1 - v2.col(i).dot(reproj_v2.col(i)));
        }

        return errors;
    }

    VectorXd residual_LiGT_d3(const BearingVectors &v1,
                              const BearingVectors &v2,
                              const RelativePose &pose,
                              const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            double tmp5 = tmp4 * t.norm() + tmp2.dot(tmp3) * (R * X2).norm();

            Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
            cost_vec = cost_vec / tmp3.norm();
            residual(k) = cost_vec.norm();
        }

        return residual;
    }

    Eigen::Matrix3d crossMatrix(const Eigen::Vector3d &x)
    {
        Eigen::Matrix3d y;

        y << 0, -x(2), x(1),
            x(2), 0, -x(0),
            -x(1), x(0), 0;

        return y;
    }

    // VectorXd residual_LiGT_direct(const BearingVectors& v1,
    //                        const BearingVectors& v2,
    //                        const Pose& pose,
    //                        const VectorXd* ptr_weigths = nullptr) {
    //     int num_matches = v1.cols();
    //     VectorXd residual(num_matches);

    //    //随机数生成
    //    std::vector<int> indices(5);

    //    for (int k = 0; k < num_matches; ++k) {

    //        Matrix3d L = Matrix3d::Zero();

    //        const Vector3d& X1 = v1.col(k);
    //        const Vector3d& X2 = v2.col(k);

    //        const Matrix3d& R = pose.rotations;
    //        const Vector3d& t = pose.translations;

    //        Vector3d tmp1 = X1.cross(R * X2);
    //        Vector3d tmp2 = (R * X2).cross(X1);
    //        Vector3d tmp3 = X1.cross(t);
    //        double tmp4 = tmp2.norm() * tmp2.norm();

    //        RowVector3d h1 = tmp2.transpose() * crossMatrix(R * X2);
    //        RowVector3d h2 = tmp2.transpose() * crossMatrix(X1);

    //        h1 /= tmp4;
    //        h2 /= tmp4;

    //        L = X1 * h1 - R*X2 * h2 - Matrix3d::Identity();
    //        residual(k) = (L * t).squaredNorm();

    //    }
    //    std::cout<<"residuals:"<<residual.transpose()<<endl;
    //    return residual.array().sqrt();
    //}

    VectorXd residual_LiGT_direct(const BearingVectors &v1,
                                  const BearingVectors &v2,
                                  const RelativePose &pose,
                                  const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            Vector3d y = tmp4 * t + tmp2.dot(tmp3) * (R * X2);
            y = y / y.norm();

            residual(k) = 1 - X1.transpose() * y;
            //        residual(k) = cost_vec.dot(cost_vec) * tmp4;
        }
        return residual.array().sqrt();
    }

    VectorXd residual_PPO(const BearingVectors &v1,
                          const BearingVectors &v2,
                          const RelativePose &pose,
                          const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            //        residual(k) = ((reproj_coord/reproj_coord.norm() - X1)/ tmp3.squaredNorm() * tmp2.norm()).norm();
            residual(k) = (reproj_coord / reproj_coord.norm() - X1).norm();
        }
        return residual.array();
    }

    VectorXd residual_PPO_3dvec(const BearingVectors &v1,
                                const BearingVectors &v2,
                                const RelativePose &pose,
                                const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(3 * num_matches); // 每个点3个残差分量

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            // 计算3D残差向量：reproj_coord/reproj_coord.norm() - X1
            Vector3d diff = reproj_coord / reproj_coord.norm() - X1;

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            // 存储3D残差向量
            residual.segment<3>(3 * k) = factor * diff;
        }
        return residual;
    }

    VectorXd residual_LiGT_3dvec(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(3 * num_matches); // 每个点3个残差分量

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
            cost_vec = cost_vec / tmp3.squaredNorm() * tmp2.norm(); // matlab LiGTlr,best

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            // 存储3D残差向量
            residual.segment<3>(3 * k) = factor * cost_vec;
        }
        return residual;
    }

    VectorXd residual_LiRT_3dvec(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(3 * num_matches); // 每个点3个残差分量

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp = X1.cross(R * X2);

            Matrix3d tmp1 = X1 * tmp.transpose() * crossMatrix(R * X2);
            Matrix3d tmp2 = (R * X2) * tmp.transpose() * crossMatrix(X1);
            Matrix3d tmp3 = tmp.squaredNorm() * Matrix3d::Identity();
            Vector3d cost_vec = (tmp1 - tmp2 + tmp3) * t;

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            // 存储3D残差向量
            residual.segment<3>(3 * k) = factor * cost_vec;
        }
        return residual;
    }

    VectorXd residual_PPOG_3dvec(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(3 * num_matches); // 每个点3个残差分量

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            // 计算3D残差向量：reproj_coord - X1 * (t.cross(R * X2)).norm()
            Vector3d diff = reproj_coord - X1 * (t.cross(R * X2)).norm();

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            // 存储3D残差向量
            residual.segment<3>(3 * k) = factor * diff;
        }
        return residual;
    }

    VectorXd residual_PPO_angle(const BearingVectors &v1,
                                const BearingVectors &v2,
                                const RelativePose &pose,
                                const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            // 归一化重投影坐标和真实观测
            Vector3d reproj_normalized = reproj_coord.normalized();
            Vector3d X1_normalized = X1.normalized();

            // 计算角度误差：1 - cos(angle) = 1 - dot(v1, v2)
            // 这种形式数值稳定且优化友好
            double dot_product = reproj_normalized.dot(X1_normalized);

            // 确保点积在有效范围内（数值稳定性）
            dot_product = std::max(-1.0, std::min(1.0, dot_product));

            double angle_error = 1.0 - dot_product;

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            residual(k) = factor * angle_error;
        }
        return residual;
    }

    // 新增：PPO角度残差（弧度形式）
    VectorXd residual_PPO_angle_rad(const BearingVectors &v1,
                                    const BearingVectors &v2,
                                    const RelativePose &pose,
                                    const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            // 归一化重投影坐标和真实观测
            Vector3d reproj_normalized = reproj_coord.normalized();
            Vector3d X1_normalized = X1.normalized();

            // 计算角度误差：直接使用弧度值
            double dot_product = reproj_normalized.dot(X1_normalized);
            dot_product = std::max(-1.0, std::min(1.0, dot_product));

            double angle_rad = std::acos(dot_product);

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            residual(k) = factor * angle_rad;
        }
        return residual;
    }

    // 新增：PPO角度残差（正弦形式）
    VectorXd residual_PPO_angle_sin(const BearingVectors &v1,
                                    const BearingVectors &v2,
                                    const RelativePose &pose,
                                    const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            // 归一化重投影坐标和真实观测
            Vector3d reproj_normalized = reproj_coord.normalized();
            Vector3d X1_normalized = X1.normalized();

            // 计算角度误差：使用正弦值 sin(angle) = ||cross(v1, v2)||
            Vector3d cross_product = reproj_normalized.cross(X1_normalized);
            double angle_sin = cross_product.norm();

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            residual(k) = factor * angle_sin;
        }
        return residual;
    }

    // 新增：PPO角度残差（2sin(angle/2)形式）
    VectorXd residual_PPO_angle_2sin(const BearingVectors &v1,
                                     const BearingVectors &v2,
                                     const RelativePose &pose,
                                     const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            // 归一化重投影坐标和真实观测
            Vector3d reproj_normalized = reproj_coord.normalized();
            Vector3d X1_normalized = X1.normalized();

            // 计算角度误差：使用2sin(angle/2) = ||v1 - v2||（当v1、v2为单位向量时）
            Vector3d diff = reproj_normalized - X1_normalized;
            double angle_2sin = diff.norm();

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            residual(k) = factor * angle_2sin;
        }
        return residual;
    }

    VectorXd residual_ppo_opengv(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *weights)
    {
        const size_t num_points = v1.cols();
        VectorXd residuals(6 * num_points); // 每个点6个残差分量

        const Matrix3d &rotation = pose.Rij;
        const Vector3d &translation = pose.tij;

        for (size_t i = 0; i < num_points; ++i)
        {
            Vector3d X1 = v1.col(i);
            Vector3d X2 = v2.col(i);
            Vector3d rotated_X2 = rotation * X2;

            // Compute cross products and their norms
            Vector3d tmp2 = rotated_X2.cross(X1);
            Vector3d tmp3 = X1.cross(translation);
            Vector3d tmp4 = rotated_X2.cross(translation);

            double tmp2_norm = tmp2.norm();
            double tmp3_norm = tmp3.norm();
            double tmp4_norm = tmp4.norm();

            // 避免除零错误
            const double epsilon = 1e-12;
            if (tmp2_norm < epsilon || tmp3_norm < epsilon || tmp4_norm < epsilon)
            {
                // 对于无效点，设置大的残差值
                residuals.segment<6>(6 * i).setConstant(1e3);
                continue;
            }

            // Compute reprojection coordinates
            Vector3d reproj_coord1 = tmp2_norm * translation + tmp3_norm * rotated_X2;
            Vector3d reproj_coord2 = tmp4_norm * X1 - tmp2_norm * translation;

            // Normalize reprojection coordinates
            reproj_coord1.normalize();
            reproj_coord2.normalize();

            // Compute the differences (这就是我们要优化的6D残差向量)
            Vector3d diff1 = reproj_coord1 - X1;
            Vector3d diff2 = reproj_coord2 - rotated_X2;

            // 应用权重
            double factor = (weights != nullptr) ? (*weights)(i) : 1.0;

            // 存储6D残差向量 [diff1; diff2]
            residuals.segment<3>(6 * i) = factor * diff1;
            residuals.segment<3>(6 * i + 3) = factor * diff2;
        }

        return residuals;
    }

    VectorXd residual_PPO_bvc_invd(const BearingVectors &v1,
                                   const BearingVectors &v2,
                                   const RelativePose &pose,
                                   const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp1 = (R * X2).cross(t);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            //        residual(k) = ((reproj_coord/reproj_coord.norm() - X1)/ tmp3.squaredNorm() * tmp2.norm()).norm();
            residual(k) = (reproj_coord / reproj_coord.norm() - X1).norm();
            residual(k) = residual(k) / tmp1.norm() / tmp3.norm() * tmp2.norm();
        }
        return residual.array();
    }

    VectorXd residual_PPOG(const BearingVectors &v1,
                           const BearingVectors &v2,
                           const RelativePose &pose,
                           const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            //        residual(k) = reproj_coord.cross(X1).norm();
            residual(k) = (reproj_coord - X1 * (t.cross(R * X2)).norm()).norm();
        }
        return residual.array();
    }

    VectorXd residual_PPO_invd(const BearingVectors &v1,
                               const BearingVectors &v2,
                               const RelativePose &pose,
                               const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d tmp4 = (R * X2).cross(t);
            double inv_depth = std::max(tmp2.norm() / tmp3.norm(), 1e-6);
            Vector3d reproj_coord = inv_depth * t + (R * X2);
            residual(k) = (reproj_coord.normalized() - X1.normalized()).squaredNorm();
        }
        return residual.array().sqrt();
    }

    VectorXd residual_PPO_bva_invd(const BearingVectors &v1,
                                   const BearingVectors &v2,
                                   const RelativePose &pose,
                                   const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);
            reproj_coord = reproj_coord.normalized();

            residual(k) = (1 - reproj_coord.transpose() * X1);
            double eps_limit = 1e-6;
            double tmp_3_norm = (tmp3.norm() > eps_limit) ? tmp3.norm() : eps_limit;
            double tmp_2_norm2 = (tmp2.norm() * tmp2.norm() > eps_limit) ? tmp2.norm() * tmp2.norm() : eps_limit;
            residual(k) = residual(k) / tmp_2_norm2;
            double a = 0;
            if (std::isnan(residual(k)))
            {
                cout << "nan" << endl;
                a = 1;
            }
        }
        return residual;
    }

    VectorXd residual_coplanar(const BearingVectors &v1,
                               const BearingVectors &v2,
                               const RelativePose &pose,
                               const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        const Matrix3d &R = pose.Rij;
        const Vector3d &t = pose.tij;

        Matrix3d E = crossMatrix(t) * R;
        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);

            residual(k) = abs(X1.transpose() * E * X2);
        }
        return residual;
    }

    VectorXd residual_sampson(const BearingVectors &v1,
                              const BearingVectors &v2,
                              const RelativePose &pose,
                              const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        const Matrix3d &R = pose.Rij;
        const Vector3d &t = pose.tij;

        Matrix3d E = crossMatrix(t) * R;
        for (int k = 0; k < num_matches; ++k)
        {

            // 获取并标准化 v1 和 v2
            Vector3d X1 = v1.col(k) / v1(2, k);
            Vector3d X2 = v2.col(k) / v2(2, k);

            // 计算 tmp1 和 tmp2，只取前两行
            double tmp1 = (E.block<2, 3>(0, 0) * X2).squaredNorm();
            double tmp2 = (E.transpose().block<2, 3>(0, 0) * X1).squaredNorm();

            //        const Vector3d& X1 = v1.col(k);
            //        const Vector3d& X2 = v2.col(k);

            //        double tmp1 = (E * X2).squaredNorm();
            //        double tmp2 = (E.transpose() * X1).squaredNorm();
            residual(k) = (X1.transpose() * E * X2).squaredNorm() / (tmp1 + tmp2);
        }

        return residual;
    }

    // 新增：PPO角度残差（sqrt形式，更鲁棒）
    VectorXd residual_PPO_angle_sqrt(const BearingVectors &v1,
                                     const BearingVectors &v2,
                                     const RelativePose &pose,
                                     const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.Rij;
            const Vector3d &t = pose.tij;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            // 归一化重投影坐标和真实观测
            Vector3d reproj_normalized = reproj_coord.normalized();
            Vector3d X1_normalized = X1.normalized();

            // 计算角度误差：sqrt(1 - cos(angle))
            // 这样做的好处：
            // 1. 对小角度更敏感（小角度时 sqrt(1-cos(x)) ≈ x/√2）
            // 2. 对大角度更鲁棒（避免二次项的过度惩罚）
            // 3. 数学上更合理（直接优化 sum(1-cos(angle))）
            double dot_product = reproj_normalized.dot(X1_normalized);

            // 确保点积在有效范围内（数值稳定性）
            dot_product = std::max(-1.0, std::min(1.0, dot_product));

            double cos_error = 1.0 - dot_product;
            double angle_error_sqrt = std::sqrt(std::max(0.0, cos_error)); // 确保非负

            // 应用权重
            double factor = (ptr_weigths != nullptr) ? (*ptr_weigths)(k) : 1.0;

            residual(k) = factor * angle_error_sqrt;
        }
        return residual;
    }

}
