#ifndef RELATIVE_RESIDUALS_H
#define RELATIVE_RESIDUALS_H

#pragma once
#include "po_core/types.hpp"
#include <string>
#include <fstream>
#include <sstream>
#include <utility>
#include <iostream>
#include <memory>

namespace PoSDK
{
    using namespace Eigen;
    using namespace types;

    VectorXd residual_coplanar(const BearingVectors &v1,
                               const BearingVectors &v2,
                               const RelativePose &pose,
                               const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_sampson(const BearingVectors &v1,
                              const BearingVectors &v2,
                              const RelativePose &pose,
                              const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_Kneip(const BearingVectors &v1,
                            const BearingVectors &v2,
                            const RelativePose &pose,
                            const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_BA(const BearingVectors &v1,
                         const BearingVectors &v2,
                         const RelativePose &pose,
                         const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_BA(const BearingVectors &v1,
                         const BearingVectors &v2,
                         const RelativePose &pose,
                         const Matrix3d &Kmat,
                         const VectorXd *ptr_weights = nullptr);

    VectorXd residual_opengv(const BearingVectors &v1,
                             const BearingVectors &v2,
                             const RelativePose &pose,
                             Matrix<double, 3, Dynamic> &points3D,
                             const VectorXd *ptr_weights = nullptr);

    VectorXd residual_opengv(const BearingVectors &v1,
                             const BearingVectors &v2,
                             const RelativePose &pose,
                             const VectorXd *ptr_weights = nullptr);

    VectorXd residual_PPO(const BearingVectors &v1,
                          const BearingVectors &v2,
                          const RelativePose &pose,
                          const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_3dvec(const BearingVectors &v1,
                                const BearingVectors &v2,
                                const RelativePose &pose,
                                const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_angle(const BearingVectors &v1,
                                const BearingVectors &v2,
                                const RelativePose &pose,
                                const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_angle_rad(const BearingVectors &v1,
                                    const BearingVectors &v2,
                                    const RelativePose &pose,
                                    const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_angle_sin(const BearingVectors &v1,
                                    const BearingVectors &v2,
                                    const RelativePose &pose,
                                    const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_angle_2sin(const BearingVectors &v1,
                                     const BearingVectors &v2,
                                     const RelativePose &pose,
                                     const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_angle_sqrt(const BearingVectors &v1,
                                     const BearingVectors &v2,
                                     const RelativePose &pose,
                                     const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_ppo_opengv(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *weights = nullptr);

    VectorXd residual_PPOG(const BearingVectors &v1,
                           const BearingVectors &v2,
                           const RelativePose &pose,
                           const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPOG_3dvec(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_invd(const BearingVectors &v1,
                               const BearingVectors &v2,
                               const RelativePose &pose,
                               const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_bvc_invd(const BearingVectors &v1,
                                   const BearingVectors &v2,
                                   const RelativePose &pose,
                                   const VectorXd *ptr_weigths = nullptr);
    VectorXd residual_PPO_cross(const BearingVectors &v1,
                                const BearingVectors &v2,
                                const RelativePose &pose,
                                const VectorXd *ptr_weigths = nullptr);
    VectorXd residual_PPO_bva_invd(const BearingVectors &v1,
                                   const BearingVectors &v2,
                                   const RelativePose &pose,
                                   const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT_direct(const BearingVectors &v1,
                                  const BearingVectors &v2,
                                  const RelativePose &pose,
                                  const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT_d3(const BearingVectors &v1,
                              const BearingVectors &v2,
                              const RelativePose &pose,
                              const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT(const BearingVectors &v1,
                           const BearingVectors &v2,
                           const RelativePose &pose,
                           const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT_3dvec(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT_Lmat(const BearingVectors &v1,
                                const BearingVectors &v2,
                                const RelativePose &pose,
                                Matrix3d &Lmat);

    VectorXd residual_LiRT(const BearingVectors &v1,
                           const BearingVectors &v2,
                           const RelativePose &pose,
                           const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiRT_3dvec(const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const RelativePose &pose,
                                 const VectorXd *ptr_weigths = nullptr);

} // namespace PoSDK

#endif // RELATIVE_RESIDUALS_H
