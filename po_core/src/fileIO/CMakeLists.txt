# ==============================================================================
# pomvg_file_io 库构建配置
# ==============================================================================

# 查找源文件
file(GLOB_RECURSE FILE_IO_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
)

file(GLOB_RECURSE FILE_IO_HEADERS 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.hpp"
)

# 创建库目标
add_library(pomvg_file_io ${FILE_IO_SOURCES} ${FILE_IO_HEADERS})

# 设置库属性
set_target_properties(pomvg_file_io PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    POSITION_INDEPENDENT_CODE ON
    LIBRARY_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    ARCHIVE_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    RUNTIME_OUTPUT_DIRECTORY "${OUTPUT_BIN_DIR}"
)

# 配置编译选项
target_compile_options(pomvg_file_io
    PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/W4 /wd4251>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# 配置头文件包含路径
target_include_directories(pomvg_file_io
    PUBLIC
        $<BUILD_INTERFACE:${OUTPUT_INCLUDE_DIR}>
        $<INSTALL_INTERFACE:include>
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_CURRENT_SOURCE_DIR}/../internal
)

# 链接依赖库
target_link_libraries(pomvg_file_io
    PUBLIC
        Eigen3::Eigen
        pomvg_internal
)

# ==============================================================================
# 安装配置
# ==============================================================================


