#include "g2o_io.hpp"

#include <iostream>
#include <memory>
#include <string>
#include <fstream>
#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <chrono>
#include <map>
#include <vector>
#include <iomanip>
#include <random>
#include <set>

namespace PoSDK
{
    namespace file
    {

        //-------------------- G2OData ------------
        bool G2OData::LoadFromG2O(const std::string &path)
        {
            std::ifstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file: " << path << std::endl;
                return false;
            }

            std::string line;
            size_t max_id = 0;
            std::map<int, Vector3d> points_3d;
            std::map<int, std::vector<ObsInfo>> point_observations;

            // 第一遍扫描，确定需要的容器大小
            while (std::getline(file, line))
            {
                std::istringstream iss(line);
                std::string tag;
                iss >> tag;

                if (tag == "VERTEX_SE3:QUAT")
                {
                    if (rotations || translations)
                    {
                        int id;
                        iss >> id;
                        max_id = std::max(max_id, static_cast<size_t>(id));
                    }
                }
                else if (tag == "EDGE_SE3:QUAT")
                {
                    if (relative_poses)
                    {
                        relative_poses->push_back(RelativePose());
                    }
                }
                else if (tag == "VERTEX_POINT_XYZ")
                {
                    if (tracks)
                    {
                        int point_id;
                        double x, y, z;
                        iss >> point_id >> x >> y >> z;
                        points_3d[point_id] = Vector3d(x, y, z);
                    }
                }
                else if (tag == "EDGE_PROJECT_P2MC")
                {
                    if (tracks)
                    {
                        int cam_id, point_id;
                        double u, v;
                        iss >> cam_id >> point_id >> u >> v;

                        ObsInfo obs;
                        obs.view_id = cam_id;
                        obs.pts_id = point_id;
                        obs.coord = Vector2d(u, v);
                        point_observations[point_id].push_back(obs);
                    }
                }
            }

            // 初始化容器
            if (rotations)
            {
                rotations->resize(max_id + 1);
            }
            if (translations)
            {
                translations->resize(max_id + 1);
            }
            if (relative_poses)
                relative_poses->clear(); // 确保清空相对位姿容器

            // 重置文件指针
            file.clear();
            file.seekg(0);

            // 第二遍读取数据
            while (std::getline(file, line))
            {
                std::istringstream iss(line);
                std::string tag;
                iss >> tag;

                if (tag == "VERTEX_SE3:QUAT" && (rotations || translations))
                {
                    int id;
                    double x, y, z, qx, qy, qz, qw;
                    iss >> id >> x >> y >> z >> qx >> qy >> qz >> qw;

                    if (rotations)
                    {
                        Eigen::Quaterniond q(qw, qx, qy, qz);
                        (*rotations)[id] = q.normalized().toRotationMatrix();
                    }
                    if (translations)
                    {
                        (*translations)[id] = Vector3d(x, y, z);
                    }
                }
                else if (tag == "EDGE_SE3:QUAT" && relative_poses)
                {
                    int id1, id2;
                    double dx, dy, dz, qx, qy, qz, qw;
                    iss >> id1 >> id2 >> dx >> dy >> dz >> qx >> qy >> qz >> qw;

                    // 跳过信息矩阵
                    double info;
                    for (int i = 0; i < 21; ++i)
                        iss >> info;

                    Eigen::Quaterniond q(qw, qx, qy, qz);
                    q.normalize(); // 确保四元数归一化
                    Matrix3d R = q.toRotationMatrix();
                    Vector3d t(dx, dy, dz);

                    relative_poses->emplace_back(
                        id1, id2,
                        R,
                        t,
                        info // 使用第一个信息矩阵元素作为权重
                    );
                }
            }

            // 构建Tracks
            if (tracks)
            {
                tracks->clear();
                for (const auto &point_obs : point_observations)
                {
                    if (!point_obs.second.empty())
                    {
                        Track track;
                        for (const auto &obs : point_obs.second)
                        {
                            track.push_back(obs);
                        }
                        tracks->emplace_back(TrackInfo(track));
                    }
                }
            }

            file.close();
            return true;
        }

        bool G2OData::SaveToG2O(const std::string &path) const
        {
            std::ofstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file for writing: " << path << std::endl;
                return false;
            }

            file << std::setprecision(16);

            // 写入相机位姿顶点
            if (rotations && translations)
            {
                for (size_t i = 0; i < rotations->size(); ++i)
                {
                    Eigen::Quaterniond q((*rotations)[i]);
                    file << "VERTEX_SE3:QUAT " << i << " "
                         << (*translations)[i].x() << " "
                         << (*translations)[i].y() << " "
                         << (*translations)[i].z() << " "
                         << q.x() << " " << q.y() << " " << q.z() << " " << q.w() << "\n";
                }
            }

            // 写入相对位姿边
            if (relative_poses)
            {
                for (const auto &pose : *relative_poses)
                {
                    Eigen::Quaterniond q(pose.Rij);
                    file << "EDGE_SE3:QUAT " << pose.i << " " << pose.j << " "
                         << pose.tij.x() << " " << pose.tij.y() << " " << pose.tij.z() << " "
                         << q.x() << " " << q.y() << " " << q.z() << " " << q.w();

                    // 添加信息矩阵
                    for (int i = 0; i < 21; ++i)
                    {
                        file << " " << (i == 0 ? pose.weight : 1.0);
                    }
                    file << "\n";
                }
            }

            // 写入特征点和观测
            if (tracks)
            {
                // 写入3D点顶点
                int point_id = 0;
                std::map<int, int> pts_id_map;

                for (const auto &track : *tracks)
                {
                    if (!track.is_used)
                        continue;

                    file << "VERTEX_POINT_XYZ " << point_id << " 0 0 0\n";
                    pts_id_map[track.track[0].pts_id] = point_id;
                    point_id++;
                }

                // 写入观测边
                for (const auto &track : *tracks)
                {
                    if (!track.is_used)
                        continue;

                    int curr_point_id = pts_id_map[track.track[0].pts_id];
                    for (const auto &obs : track.track)
                    {
                        file << "EDGE_PROJECT_P2MC "
                             << obs.view_id << " "
                             << curr_point_id << " "
                             << obs.coord.x() << " "
                             << obs.coord.y()
                             << " 1.0 0.0 0.0 1.0\n";
                    }
                }
            }

            file.close();
            return true;
        }

        //-------------------- 全局函数 ------------

        bool LoadFromG2O(const std::string &path,
                         GlobalRotations &global_rotations,
                         GlobalTranslations &global_translations)
        {
            std::ifstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file: " << path << std::endl;
                return false;
            }

            std::string line;
            size_t max_id = 0;

            // 第一遍扫描确定最大ID
            while (std::getline(file, line))
            {
                std::istringstream iss(line);
                std::string tag;
                iss >> tag;

                if (tag == "VERTEX_SE3:QUAT")
                {
                    int id;
                    iss >> id;
                    max_id = std::max(max_id, static_cast<size_t>(id));
                }
                else if (tag == "EDGE_SE3:QUAT")
                {
                    int id1, id2;
                    iss >> id1 >> id2;
                    max_id = std::max(max_id, static_cast<size_t>(id1));
                    max_id = std::max(max_id, static_cast<size_t>(id2));
                }
            }

            // 重置文件指针到开始位置
            file.clear();
            file.seekg(0);

            // 初始化容器大小
            global_rotations.resize(max_id + 1);
            global_translations.resize(max_id + 1);

            // 第二遍读取数据
            while (std::getline(file, line))
            {
                std::istringstream iss(line);
                std::string tag;
                iss >> tag;

                if (tag == "VERTEX_SE3:QUAT")
                {
                    int id;
                    // g2o格式：VERTEX_SE3:QUAT id x y z qx qy qz qw
                    double x, y, z, qx, qy, qz, qw;
                    iss >> id >> x >> y >> z >> qx >> qy >> qz >> qw;

                    // 四元数转旋转矩阵
                    Eigen::Quaterniond q(qw, qx, qy, qz);
                    Matrix3d R = q.normalized().toRotationMatrix();
                    Vector3d t(x, y, z);

                    global_rotations[id] = R;
                    global_translations[id] = t;
                }
            }

            file.close();
            return true;
        }

        bool LoadFromG2O(const std::string &path,
                         GlobalPoses &global_poses)
        {
            // 直接使用global_poses中的rotations和translations
            if (!LoadFromG2O(path, global_poses.rotations, global_poses.translations))
            {
                return false;
            }

            return true;
        }

        bool LoadFromG2O(const std::string &path,
                         GlobalPoses &global_poses,
                         Tracks &tracks)
        {
            std::ifstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file: " << path << std::endl;
                return false;
            }

            std::string line;
            size_t max_id = 0;
            tracks.clear();

            // 用于存储3D点信息
            std::map<int, Vector3d> points_3d;
            // 用于存储点的观测信息
            std::map<int, std::vector<ObsInfo>> point_observations;

            // 第一遍扫描，读取相机位姿和3D点
            while (std::getline(file, line))
            {
                std::istringstream iss(line);
                std::string tag;
                iss >> tag;

                if (tag == "VERTEX_SE3:QUAT")
                {
                    // 读取相机位姿
                    int id;
                    iss >> id;
                    max_id = std::max(max_id, static_cast<size_t>(id));
                }
                else if (tag == "EDGE_SE3:QUAT")
                {
                    // 读取相机相对位姿
                    int id1, id2;
                    iss >> id1 >> id2;
                    max_id = std::max(max_id, static_cast<size_t>(id1));
                    max_id = std::max(max_id, static_cast<size_t>(id2));
                }
                else if (tag == "VERTEX_POINT_XYZ")
                {
                    // 读取3D点信息
                    int point_id;
                    double x, y, z;
                    iss >> point_id >> x >> y >> z;
                    points_3d[point_id] = Vector3d(x, y, z);
                }
                else if (tag == "EDGE_PROJECT_P2MC")
                {
                    // 读取投影观测信息
                    int cam_id, point_id;
                    double u, v;
                    iss >> cam_id >> point_id >> u >> v;

                    // 创建观测信息
                    ObsInfo obs;
                    obs.view_id = cam_id;
                    obs.pts_id = point_id;
                    obs.coord = Vector2d(u, v);

                    // 添加到临时存储
                    point_observations[point_id].push_back(obs);
                }
            }

            // 重置文件指针
            file.clear();
            file.seekg(0);

            // 初始化GlobalPoses
            global_poses.Init(max_id + 1);

            // 第二遍读取相机位姿
            while (std::getline(file, line))
            {
                std::istringstream iss(line);
                std::string tag;
                iss >> tag;

                if (tag == "VERTEX_SE3:QUAT")
                {
                    int id;
                    double x, y, z, qx, qy, qz, qw;
                    iss >> id >> x >> y >> z >> qx >> qy >> qz >> qw;

                    Eigen::Quaterniond q(qw, qx, qy, qz);
                    Matrix3d R = q.normalized().toRotationMatrix();
                    Vector3d t(x, y, z);

                    global_poses.SetRotation(id, R);
                    global_poses.SetTranslation(id, t);
                }
            }

            // 构建Tracks
            for (const auto &point_obs : point_observations)
            {
                if (!point_obs.second.empty())
                {
                    Track track;
                    for (const auto &obs : point_obs.second)
                    {
                        track.push_back(obs);
                    }
                    tracks.emplace_back(TrackInfo(track));
                }
            }

            file.close();
            return true;
        }

        bool LoadFromG2O(const std::string &path,
                         RelativePoses &relative_poses)
        {
            std::ifstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file: " << path << std::endl;
                return false;
            }

            // 设置输入流的精度
            file.precision(std::numeric_limits<double>::max_digits10);

            std::string line;
            relative_poses.clear();
            bool has_content = false;

            while (std::getline(file, line))
            {
                has_content = true;
                if (line.empty() || line[0] == '#')
                    continue;

                std::istringstream iss(line);
                // 设置字符串流的精度
                iss.precision(std::numeric_limits<double>::max_digits10);
                std::string tag;
                iss >> tag;

                if (tag == "EDGE_SE3:QUAT")
                {
                    // EDGE_SE3:QUAT id1 id2 dx dy dz dqx dqy dqz dqw [information matrix]
                    int id1, id2;
                    double dx, dy, dz, dqx, dqy, dqz, dqw;
                    iss >> id1 >> id2 >> dx >> dy >> dz >> dqx >> dqy >> dqz >> dqw;

                    // 构造相对位姿，注意四元数的顺序(w,x,y,z)
                    Eigen::Quaterniond q(dqw, dqx, dqy, dqz);
                    q.normalize(); // 确保四元数归一化
                    Matrix3d R = q.toRotationMatrix();
                    Vector3d t(dx, dy, dz);

                    // 读取信息矩阵（可选）
                    double weight = 1.0; // 默认权重为1.0
                    if (!iss.eof())
                    {
                        double info_value;
                        if (iss >> info_value)
                        {
                            weight = std::sqrt(info_value); // 使用第一个信息矩阵值的平方根作为权重
                        }
                    }

                    // 添加到相对位姿集合
                    RelativePose relative_pose(id1, id2, R, t, weight);
                    relative_poses.push_back(relative_pose);
                }
            }

            file.close();
            return has_content; // 只要文件有内容就返回true
        }

        bool SaveToG2O(const std::string &path,
                       const GlobalRotations &global_rotations,
                       const GlobalTranslations &global_translations)
        {
            std::ofstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file for writing: " << path << std::endl;
                return false;
            }

            file << std::setprecision(16);

            // 写入相机位姿顶点
            for (size_t i = 0; i < global_rotations.size(); ++i)
            {
                Eigen::Quaterniond q(global_rotations[i]);
                file << "VERTEX_SE3:QUAT " << i << " "
                     << global_translations[i].x() << " "
                     << global_translations[i].y() << " "
                     << global_translations[i].z() << " "
                     << q.x() << " " << q.y() << " " << q.z() << " " << q.w() << "\n";
            }

            file.close();
            return true;
        }

        bool SaveToG2O(const std::string &path,
                       const GlobalPoses &global_poses)
        {
            return SaveToG2O(path, global_poses.rotations, global_poses.translations);
        }

        bool SaveToG2O(const std::string &path,
                       const GlobalPoses &global_poses,
                       const Tracks &tracks)
        {
            std::ofstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file for writing: " << path << std::endl;
                return false;
            }

            file << std::setprecision(16);

            // 写入相机位姿顶点
            for (size_t i = 0; i < global_poses.Size(); ++i)
            {
                Eigen::Quaterniond q(global_poses.GetRotation(i));
                const auto &t = global_poses.GetTranslation(i);
                file << "VERTEX_SE3:QUAT " << i << " "
                     << t.x() << " " << t.y() << " " << t.z() << " "
                     << q.x() << " " << q.y() << " " << q.z() << " " << q.w() << "\n";
            }

            // 写入3D点顶点
            std::map<int, int> pts_id_map; // 用于映射原始点ID到文件中的ID
            int point_id = 0;
            for (const auto &track : tracks)
            {
                if (!track.is_used)
                    continue;

                // 为每个轨迹创建一个3D点顶点
                file << "VERTEX_POINT_XYZ " << point_id << " "
                     << "0 0 0\n"; // 由于Tracks中可能没有3D点信息，暂时用零坐标

                // 记录点ID映射
                pts_id_map[track.track[0].pts_id] = point_id;
                point_id++;
            }

            // 写入投影观测边
            for (const auto &track : tracks)
            {
                if (!track.is_used)
                    continue;

                int curr_point_id = pts_id_map[track.track[0].pts_id];
                for (const auto &obs : track.track)
                {
                    file << "EDGE_PROJECT_P2MC "
                         << obs.view_id << " "
                         << curr_point_id << " "
                         << obs.coord.x() << " "
                         << obs.coord.y()
                         << " 1.0 0.0 0.0 1.0\n"; // 添加单位信息矩阵
                }
            }

            file.close();
            return true;
        }

        bool SaveToG2O(const std::string &path,
                       const RelativePoses &relative_poses)
        {
            std::ofstream file(path);
            if (!file.is_open())
            {
                std::cerr << "Error: Cannot open file for writing: " << path << std::endl;
                return false;
            }

            // 设置最大精度
            file.precision(std::numeric_limits<double>::max_digits10);
            file << std::scientific; // 使用科学计数法

            // 收集所有唯一的顶点ID
            std::set<int> vertex_ids;
            for (const auto &pose : relative_poses)
            {
                vertex_ids.insert(pose.i);
                vertex_ids.insert(pose.j);
            }

            // 写入所有顶点（初始值设为零）
            for (int id : vertex_ids)
            {
                file << "VERTEX_SE3:QUAT " << id
                     << " 0 0 0"      // 位置 (x, y, z)
                     << " 0 0 0 1\n"; // 四元数 (qx, qy, qz, qw)，表示单位旋转
            }
            file << "\n";

            // 写入所有边（相对位姿）
            for (const auto &pose : relative_poses)
            {
                // 转换旋转矩阵为四元数，保持最高精度
                Eigen::Quaterniond q(pose.Rij);
                q.normalize(); // 确保四元数归一化

                // 写入位姿数据
                file << "EDGE_SE3:QUAT "
                     << pose.i << " " << pose.j << " "
                     << pose.tij.x() << " " << pose.tij.y() << " " << pose.tij.z() << " "
                     << q.x() << " " << q.y() << " " << q.z() << " " << q.w();

                // 写入信息矩阵（使用权重的平方作为对角线元素）
                double info_value = pose.weight * pose.weight;
                for (int i = 0; i < 6; ++i)
                {
                    for (int j = i; j < 6; ++j)
                    {
                        file << " " << ((i == j) ? info_value : 0.0);
                    }
                }
                file << "\n";
            }

            file.close();
            return true;
        }

        G2OSimData GenerateG2OSimData(const G2OSimConfig &config)
        {
            G2OSimData sim_data;
            std::random_device rd;
            std::mt19937 gen(rd());

            // 1. 生成相机位姿
            std::uniform_real_distribution<double> pos_dist(-config.cam_position_range, config.cam_position_range);
            std::uniform_real_distribution<double> angle_dist(-M_PI, M_PI);

            sim_data.global_poses.Init(config.num_cameras);
            for (size_t i = 0; i < config.num_cameras; ++i)
            {
                // 生成随机旋转
                double rx = angle_dist(gen);
                double ry = angle_dist(gen);
                double rz = angle_dist(gen);
                Matrix3d R = (Eigen::AngleAxisd(rx, Vector3d::UnitX()) *
                              Eigen::AngleAxisd(ry, Vector3d::UnitY()) *
                              Eigen::AngleAxisd(rz, Vector3d::UnitZ()))
                                 .matrix();

                // 生成随机平移
                Vector3d t(pos_dist(gen), pos_dist(gen), pos_dist(gen));

                sim_data.global_poses.SetRotation(i, R);
                sim_data.global_poses.SetTranslation(i, t);
            }

            // 2. 生成3D点
            std::uniform_real_distribution<double> pts_pos_dist(-config.point_position_range, config.point_position_range);
            std::vector<Vector3d> points_3d;
            for (size_t i = 0; i < config.num_points; ++i)
            {
                points_3d.emplace_back(pts_pos_dist(gen), pts_pos_dist(gen), pts_pos_dist(gen));
            }

            // 3. 生成观测
            std::normal_distribution<double> noise_dist(0.0, config.noise_sigma);
            std::uniform_real_distribution<double> outlier_dist(0.0, 1.0);
            std::uniform_int_distribution<size_t> cam_dist(0, config.num_cameras - 1);
            std::uniform_int_distribution<size_t> pts_id_dist(0, config.num_points - 1);

            std::map<size_t, std::vector<ObsInfo>> point_observations;
            for (size_t i = 0; i < config.num_observations; ++i)
            {
                size_t cam_id = cam_dist(gen);
                size_t point_id = pts_id_dist(gen);

                // 计算投影点
                Vector3d P = sim_data.global_poses.GetRotation(cam_id).transpose() *
                             (points_3d[point_id] - sim_data.global_poses.GetTranslation(cam_id));
                Vector2d p(P.x() / P.z(), P.y() / P.z());

                // 添加噪声
                if (config.noise_sigma > 0)
                {
                    p.x() += noise_dist(gen);
                    p.y() += noise_dist(gen);
                }

                // 创建观测
                ObsInfo obs;
                obs.view_id = cam_id;
                obs.pts_id = point_id;
                obs.coord = p;
                obs.is_used = (outlier_dist(gen) > config.outlier_ratio);

                point_observations[point_id].push_back(obs);
            }

            // 4. 构建特征轨迹
            for (const auto &[point_id, observations] : point_observations)
            {
                if (observations.size() >= 2)
                { // 至少2个观测构成轨迹
                    TrackInfo track;
                    track.track.assign(observations.begin(), observations.end()); // 使用assign替代直接赋值
                    track.is_used = true;
                    sim_data.tracks.push_back(track);
                }
            }

            // 5. 生成相对位姿（如果需要）
            if (config.add_relative_motion)
            {
                for (size_t i = 0; i < config.num_cameras; ++i)
                {
                    for (size_t j = i + 1; j < config.num_cameras; ++j)
                    {
                        Matrix3d Ri = sim_data.global_poses.GetRotation(i);
                        Matrix3d Rj = sim_data.global_poses.GetRotation(j);
                        Vector3d ti = sim_data.global_poses.GetTranslation(i);
                        Vector3d tj = sim_data.global_poses.GetTranslation(j);

                        // 计算相对位姿
                        Matrix3d Rij = Rj * Ri.transpose();
                        Vector3d tij = Rj * Ri.transpose() * (ti - tj);

                        RelativePose pose(i, j, Rij, tij, 1.0f);
                        sim_data.relative_poses.push_back(pose);
                    }
                }

                // 保存相对位姿到g2o文件
                if (!config.output_path.empty())
                {
                    std::ofstream out(config.output_path);
                    if (!out.is_open())
                    {
                        std::cerr << "Failed to open file: " << config.output_path << std::endl;
                        sim_data.success = false;
                        return sim_data;
                    }

                    // 设置最高精度
                    out.precision(std::numeric_limits<double>::max_digits10);
                    out << std::scientific; // 使用科学计数法

                    // 写入相对位姿
                    for (const auto &pose : sim_data.relative_poses)
                    {
                        out << "EDGE_SE3:QUAT " << pose.i << " " << pose.j << " ";

                        // 转换旋转矩阵为四元数
                        Eigen::Quaterniond q(pose.Rij);
                        q.normalize(); // 确保四元数归一化

                        out << pose.tij.x() << " " << pose.tij.y() << " " << pose.tij.z() << " "
                            << q.x() << " " << q.y() << " " << q.z() << " " << q.w();

                        // 写入信息矩阵（使用单位矩阵）
                        double info_value = pose.weight * pose.weight;
                        for (int i = 0; i < 6; ++i)
                        {
                            for (int j = i; j < 6; ++j)
                            {
                                out << " " << ((i == j) ? info_value : 0.0);
                            }
                        }
                        out << "\n";
                    }
                    out.close();
                }
            }

            sim_data.success = true;
            return sim_data;
        }

    }
}
