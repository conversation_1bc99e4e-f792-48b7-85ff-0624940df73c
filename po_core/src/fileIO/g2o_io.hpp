#ifndef G2O_IO_HPP
#define G2O_IO_HPP

#include <string>
#include <vector>
#include <Eigen/Dense>
#include "types.hpp"

namespace PoSDK
{
     using namespace types;
     namespace file
     {

          /**
           * @brief G2O数据包装类
           * @details 用于统一管理g2o文件的读写操作
           */
          class G2OData
          {
          public:
               TracksPtr tracks = nullptr;                   // 特征点轨迹数据
               RelativePosesPtr relative_poses = nullptr;    // 相对位姿数据
               GlobalRotationsPtr rotations = nullptr;       // 全局旋转数据
               GlobalTranslationsPtr translations = nullptr; // 全局平移数据

               /**
                * @brief 从g2o文件加载数据
                * @param path g2o文件路径
                * @return 是否加载成功
                */
               bool LoadFromG2O(const std::string &path);

               /**
                * @brief 保存数据到g2o文件
                * @param path g2o文件路径
                * @return 是否保存成功
                */
               bool SaveToG2O(const std::string &path) const;
          };
          // ---------------------- g2o file IO operation ----------------------
          /**
           * @brief 从g2o文件加载位姿数据（基础版本）
           * @param path g2o文件路径
           * @param global_rotations 旋转矩阵集合
           * @param global_translations 平移向量集合
           * @return 是否加载成功
           */
          bool LoadFromG2O(const std::string &path,
                           GlobalRotations &global_rotations,
                           GlobalTranslations &global_translations);

          /**
           * @brief 从g2o文件加载位姿数据到GlobalPoses
           * @param path g2o文件路径
           * @param global_poses 全局位姿数据
           * @return 是否加载成功
           */
          bool LoadFromG2O(const std::string &path,
                           GlobalPoses &global_poses);

          /**
           * @brief 从g2o文件加载位姿和轨迹数据
           * @param path g2o文件路径
           * @param global_poses 全局位姿数据
           * @param tracks 特征点轨迹数据
           * @return 是否加载成功
           */
          bool LoadFromG2O(const std::string &path,
                           GlobalPoses &global_poses,
                           Tracks &tracks);

          /**
           * @brief 从g2o文件加载相对位姿数据
           * @param path g2o文件路径
           * @param relative_poses 相对位姿数据
           * @return 是否加载成功
           */
          bool LoadFromG2O(const std::string &path,
                           RelativePoses &relative_poses);

          /**
           * @brief 将位姿数据保存为g2o文件（基础版本）
           * @param path g2o文件路径
           * @param global_rotations 旋转矩阵集合
           * @param global_translations 平移向量集合
           * @return 是否保存成功
           */
          bool SaveToG2O(const std::string &path,
                         const GlobalRotations &global_rotations,
                         const GlobalTranslations &global_translations);

          /**
           * @brief 将GlobalPoses数据保存为g2o文件
           * @param path g2o文件路径
           * @param global_poses 全局位姿数据
           * @return 是否保存成功
           */
          bool SaveToG2O(const std::string &path,
                         const GlobalPoses &global_poses);

          /**
           * @brief 将位姿和轨迹数据保存为g2o文件
           * @param path g2o文件路径
           * @param global_poses 全局位姿数据
           * @param tracks 特征点轨迹数据
           * @return 是否保存成功
           */
          bool SaveToG2O(const std::string &path,
                         const GlobalPoses &global_poses,
                         const Tracks &tracks);

          /**
           * @brief 将相对位姿数据保存为g2o文件
           * @param path g2o文件路径
           * @param relative_poses 相对位姿数据
           * @return 是否保存成功
           */
          bool SaveToG2O(const std::string &path,
                         const RelativePoses &relative_poses);

          /**
           * @brief G2O仿真数据生成器的配置
           */
          struct G2OSimConfig
          {
               size_t num_cameras{5};             // 相机数量
               size_t num_points{100};            // 3D点数量
               size_t num_observations{300};      // 观测数量
               double noise_sigma{0.0};           // 观测噪声标准差
               double outlier_ratio{0.0};         // 外点比例
               double cam_position_range{10.0};   // 相机位置范围
               double point_position_range{20.0}; // 3D点位置范围
               bool add_relative_motion{true};    // 是否添加相对运动约束
               std::string output_path;           // 输出文件路径
          };

          /**
           * @brief 生成G2O仿真数据
           * @param config 仿真配置
           * @return 包含生成数据的结构体
           */
          struct G2OSimData
          {
               GlobalPoses global_poses;     // 全局位姿
               RelativePoses relative_poses; // 相对位姿
               Tracks tracks;                // 特征轨迹
               bool success{false};          // 生成是否成功
          };

          /**
           * @brief 生成G2O仿真数据并保存到文件
           * @param config 仿真配置
           * @return 生成的数据
           */
          G2OSimData GenerateG2OSimData(const G2OSimConfig &config);

     }
}
#endif
