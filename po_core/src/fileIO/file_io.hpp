//=========================== BehaviorPrest Section ==================================
// # Copyright (c) 2021 PO tools author: <PERSON>.
// common tools for transformation between openMVG's and PoSDK's data type
//====================================================================================

#ifndef _POMVG_FILE_IO_
#define _POMVG_FILE_IO_
#include "types.hpp"
#include "g2o_io.hpp"
#include "interfaces_preset.hpp"
#include <filesystem>

namespace PoSDK
{
     using namespace PoSDK::types;
     namespace file
     {

          // BAL file IO operation
          /**
           * @brief 写入BAL格式文件
           * @param filename 文件名
           * @param tracks 特征点跟踪信息
           * @param rotations 旋转矩阵集合
           * @param translations 平移向量集合
           * @return 是否写入成功
           */
          bool WriteBalFile(
              const std::string &filename,
              const types::Tracks &tracks,
              const types::GlobalRotations &rotations,
              const types::GlobalTranslations &translations);

          /**
           * @brief 读取BAL格式文件
           * @param filename 文件名
           * @param tracks 特征点跟踪信息
           * @param rotations 旋转矩阵集合
           * @param translations 平移向量集合
           * @return 是否读取成功
           */
          bool LoadBalFile(const std::string &path,
                           Tracks &tracks,
                           GlobalRotations &global_rotations,
                           GlobalTranslations &global_translations);

          // Tracks file IO operation
          bool LoadTracks(const std::string &filename, Tracks &tracks);
          bool SaveTracks(const std::string &path, const Tracks &tracks);

          bool SetupOrientation(const std::string &filename, GlobalRotations &global_R);

          // PLY file IO operation for 3D points
          /**
           * @brief 写入PLY格式文件
           * @param filename PLY文件路径
           * @param points3D 3D点云数据 (3xN矩阵)
           * @return 是否写入成功
           */
          bool WritePlyFile(const std::string &filename, const Points3d &points3D);

          /**
           * @brief 读取PLY格式文件
           * @param filename PLY文件路径
           * @param points3D 输出的3D点云数据 (3xN矩阵)
           * @return 是否读取成功
           */
          bool LoadPlyFile(const std::string &filename, Points3d &points3D);

          // Meshlab project export operation
          /**
           * @brief 导出仿真数据到Meshlab工程文件（PoSDK格式）
           * @param export_path 导出路径
           * @param global_poses 全局位姿信息
           * @param camera_models 相机模型集合
           * @param world_points 世界坐标点信息
           * @param image_filename 图像文件名（默认为"simulate_pic.tiff"）
           * @param ply_filename PLY文件名（默认为"points.ply"）
           * @param project_filename 工程文件名（默认为"scene_posdk.mlp"）
           * @return 是否导出成功
           */
          bool ExportSimuDataToMeshLab(
              const std::string &export_path,
              const types::GlobalPoses &global_poses,
              const types::CameraModels &camera_models,
              const types::WorldPointInfo &world_points,
              const std::string &image_filename = "simulate_pic.tiff",
              const std::string &ply_filename = "points.ply",
              const std::string &project_filename = "scene_posdk.mlp");

          /**
           * @brief 导出数据到Meshlab工程文件（支持真实图像）
           * @param export_path 导出路径
           * @param global_poses 全局位姿信息
           * @param camera_models 相机模型集合
           * @param world_points 世界坐标点信息
           * @param image_paths 图像路径集合，如果为nullptr则生成模拟图像
           * @param ply_filename PLY文件名（默认为"points.ply"）
           * @param project_filename 工程文件名（默认为"scene_posdk.mlp"）
           * @return 是否导出成功
           */
          bool ExportToMeshLab(
              const std::string &export_path,
              const types::GlobalPoses &global_poses,
              const types::CameraModels &camera_models,
              const types::WorldPointInfo &world_points,
              const types::ImagePathsPtr &image_paths,
              const std::string &ply_filename = "points.ply",
              const std::string &project_filename = "scene_posdk.mlp");

          /**
           * @brief 导出数据到Meshlab工程文件（DataPtr参数重载版本）
           * @param export_path 导出路径
           * @param data_global_poses_ptr 全局位姿数据指针
           * @param data_camera_models_ptr 相机模型数据指针
           * @param data_world_points_ptr 世界坐标点数据指针
           * @param data_images_ptr 图像数据指针，如果为nullptr则生成模拟图像
           * @param ply_filename PLY文件名（默认为"points.ply"）
           * @param project_filename 工程文件名（默认为"scene_posdk.mlp"）
           * @return 是否导出成功
           */
          bool ExportToMeshLab(
              const std::string &export_path,
              const Interface::DataPtr &data_global_poses_ptr,
              const Interface::DataPtr &data_camera_models_ptr,
              const Interface::DataPtr &data_world_points_ptr,
              const Interface::DataPtr &data_images_ptr,
              const std::string &ply_filename = "points.ply",
              const std::string &project_filename = "scene_posdk.mlp");

     }

}

#endif
