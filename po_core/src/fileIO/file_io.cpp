// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "file_io.hpp"

#include <iostream>
#include <memory>
#include <string>
#include <fstream>
#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <chrono>
#include <map>
#include <vector>
#include <iomanip>
#include <sstream>
#include <filesystem>

using namespace std;
using namespace chrono;
using namespace PoSDK::Interface;

namespace PoSDK
{
    namespace file
    {
        // 仿照OpenMVG的RQ分解函数，用于从投影矩阵分解K,R,t
        void KRt_From_P(const types::MatrixXd &P, types::Matrix3d *Kp, types::Matrix3d *Rp, types::Vector3d *tp)
        {
            // 使用RQ分解 P = K[R|t] 中的K和R部分
            // 提取左上角3x3子矩阵
            types::Matrix3d K = P.block(0, 0, 3, 3);

            types::Matrix3d Q;
            Q.setIdentity();

            // RQ分解过程：将K分解为上三角矩阵和正交矩阵的乘积
            // 设K(2,1)为0
            if (K(2, 1) != 0)
            {
                double c = -K(2, 2);
                double s = K(2, 1);
                double l = std::sqrt(c * c + s * s);
                c /= l;
                s /= l;
                types::Matrix3d Qx;
                Qx << 1, 0, 0,
                    0, c, -s,
                    0, s, c;
                K = K * Qx;
                Q = Qx.transpose() * Q;
            }

            // 设K(2,0)为0
            if (K(2, 0) != 0)
            {
                double c = K(2, 2);
                double s = K(2, 0);
                double l = std::sqrt(c * c + s * s);
                c /= l;
                s /= l;
                types::Matrix3d Qy;
                Qy << c, 0, s,
                    0, 1, 0,
                    -s, 0, c;
                K = K * Qy;
                Q = Qy.transpose() * Q;
            }

            // 设K(1,0)为0
            if (K(1, 0) != 0)
            {
                double c = -K(1, 1);
                double s = K(1, 0);
                double l = std::sqrt(c * c + s * s);
                c /= l;
                s /= l;
                types::Matrix3d Qz;
                Qz << c, -s, 0,
                    s, c, 0,
                    0, 0, 1;
                K = K * Qz;
                Q = Qz.transpose() * Q;
            }

            types::Matrix3d R = Q;

            // 确保对角线为正，并且R的行列式为1
            if (K(2, 2) < 0)
            {
                K = -K;
                R = -R;
            }
            if (K(1, 1) < 0)
            {
                types::Matrix3d S;
                S << 1, 0, 0,
                    0, -1, 0,
                    0, 0, 1;
                K = K * S;
                R = S * R;
            }
            if (K(0, 0) < 0)
            {
                types::Matrix3d S;
                S << -1, 0, 0,
                    0, 1, 0,
                    0, 0, 1;
                K = K * S;
                R = S * R;
            }

            // 计算平移向量
            Eigen::PartialPivLU<types::Matrix3d> lu(K);
            types::Vector3d t = lu.solve(P.col(3));

            if (R.determinant() < 0)
            {
                R = -R;
                t = -t;
            }

            // 缩放K使得K(2,2) = 1
            K = K / K(2, 2);

            *Kp = K;
            *Rp = R;
            *tp = t;
        }

        // 仿照OpenMVG的方式转换相机姿态格式
        void ConvertToMeshLabFormat(const types::Matrix3d &R_our, const types::Vector3d &t_our,
                                    const types::Matrix3d &K,
                                    types::Matrix3d *R_meshlab, types::Vector3d *optical_center_meshlab)
        {
            // 步骤1: 构建投影矩阵 P = K[R|t]
            // 注意：我们的格式是RwTw，即 x = R(X-t)，所以投影矩阵是 P = K[R|-Rt]
            types::Vector3d t_camera = -R_our * t_our; // 转换为相机坐标系下的平移

            Eigen::Matrix<double, 3, 4> P;
            P.block(0, 0, 3, 3) = K * R_our;
            P.block(0, 3, 3, 1) = K * t_camera;

            // 步骤2: 仿照OpenMVG，对P的第2和第3行取负号（翻转Y和Z轴）
            for (int i = 1; i < 3; ++i)
            {
                for (int j = 0; j < 4; ++j)
                {
                    P(i, j) *= -1.0;
                }
            }

            // 步骤3: 分解修正后的投影矩阵
            types::Matrix3d K_new, R_new;
            types::Vector3d t_new;
            KRt_From_P(P, &K_new, &R_new, &t_new);

            // 步骤4: 计算光心（仿照OpenMVG）
            types::Vector3d optical_center = R_new.transpose() * t_new;

            *R_meshlab = R_new;
            *optical_center_meshlab = optical_center;
        }

        bool WriteBalFile(
            const std::string &filename,
            const types::Tracks &tracks,
            const types::GlobalRotations &rotations,
            const types::GlobalTranslations &translations)
        {
            std::cout << "<WriteBalFile> msg: path = " << filename << std::endl;
            return true;
        }

        bool LoadBalFile(const std::string &path,
                         Tracks &tracks,
                         GlobalRotations &global_rotations,
                         GlobalTranslations &global_translations)
        {

            fstream tracks_file(path, ios::in);
            if (!tracks_file.is_open())
            {
                std::cout << "bal file cannot load" << std::endl;
                return false;
            }

            Size num_view, num_pts, num_obs;
            tracks_file >> num_view >> num_pts >> num_obs;

            int record_pts_id = -1;
            TrackInfo track_info;
            ObsInfo tmp_obs;

            tracks.reserve(num_pts);
            for (int i = 0; i < num_obs; i++)
            {
                tracks_file >> tmp_obs.view_id >> tmp_obs.pts_id >> tmp_obs.coord(0) >> tmp_obs.coord(1);
                tmp_obs.coord(0) = -tmp_obs.coord(0);
                tmp_obs.coord(1) = -tmp_obs.coord(1);
                tmp_obs.coord(2) = 1;

                if (tmp_obs.pts_id != record_pts_id)
                {
                    if (i > 0)
                    {
                        if (track_info.track.size() < 3)
                        {
                            track_info.is_used = 0; // only need use observation of tracking length>2
                        }
                        else
                        {
                            track_info.is_used = 1;
                        }
                        tracks.emplace_back(track_info);
                    }
                    track_info.track.clear();
                }
                track_info.track.emplace_back(tmp_obs);
                record_pts_id = tmp_obs.pts_id;
            }
            tracks.emplace_back(track_info);

            // load poses and intrinsics
            for (int i = 0; i < num_view; ++i)
            {

                // load rotation

                Eigen::Vector3d angle_axis;
                tracks_file >> angle_axis[0] >> angle_axis[1] >> angle_axis[2];

                global_rotations[i] = Eigen::AngleAxisd(angle_axis.norm(), angle_axis.normalized());

                // load translation
                Eigen::Vector3d tmp_translation;
                tracks_file >> tmp_translation[0] >>
                    tmp_translation[1] >>
                    tmp_translation[2];

                global_translations[i] = -global_rotations[i].transpose() * tmp_translation;

                // load intrinsics
                double tmp_intrinsics[3];
                tracks_file >> tmp_intrinsics[0] >> tmp_intrinsics[1] >> tmp_intrinsics[2];
            }
            tracks_file.close();
        }

        bool SetupOrientation(const std::string &filename, GlobalRotations &global_R)
        {

            auto start_time = steady_clock::now();

            fstream cq_in;
            cq_in.open(filename, std::ios::in);
            if (!cq_in.is_open())
            {
                cout << "gR_file cannot open" << endl;
                return false;
            }

            int cq_i = 0;
            Size num_view = 0;
            cq_in >> num_view;
            global_R.resize(num_view);
            for (int cq_i = 0; cq_i < num_view; cq_i++)
            {
                Eigen::Matrix3d tmp_R;
                cq_in >> tmp_R(0, 0) >> tmp_R(1, 0) >> tmp_R(2, 0);
                cq_in >> tmp_R(0, 1) >> tmp_R(1, 1) >> tmp_R(2, 1);
                cq_in >> tmp_R(0, 2) >> tmp_R(1, 2) >> tmp_R(2, 2);

                // global_R.emplace_back(tmp_R);
                global_R[cq_i] = (tmp_R);
            }
            cq_in.close();
            auto end_time = steady_clock::now();
            auto duration = duration_cast<microseconds>(end_time - start_time);
            cout << "time for loading global rotation: "
                 << double(duration.count()) * microseconds::period::num / microseconds::period::den
                 << "s" << endl;

            return true;
        }

        //-------------- PLY file operations -------------------

        bool WritePlyFile(const std::string &filename, const Points3d &points3D)
        {
            std::ofstream outFile(filename);

            if (!outFile.is_open())
            {
                std::cerr << "Error: Unable to open PLY file for writing: " << filename << std::endl;
                return false;
            }

            // 写入PLY头部
            outFile << "ply\n";
            outFile << "format ascii 1.0\n";
            outFile << "element vertex " << points3D.cols() << "\n";
            outFile << "property float x\n";
            outFile << "property float y\n";
            outFile << "property float z\n";
            outFile << "end_header\n";

            // 写入点数据
            for (int i = 0; i < points3D.cols(); ++i)
            {
                outFile << std::setprecision(16)
                        << points3D(0, i) << " "
                        << points3D(1, i) << " "
                        << points3D(2, i) << "\n";
            }

            outFile.close();
            std::cout << "PLY file written successfully: " << filename << " ("
                      << points3D.cols() << " points)" << std::endl;
            return true;
        }

        bool LoadPlyFile(const std::string &filename, Points3d &points3D)
        {
            std::ifstream inFile(filename);

            if (!inFile.is_open())
            {
                std::cerr << "Error: Unable to open PLY file for reading: " << filename << std::endl;
                return false;
            }

            std::string line;
            int vertex_count = 0;
            bool header_complete = false;

            // 解析PLY头部
            while (std::getline(inFile, line))
            {
                if (line.find("element vertex") != std::string::npos)
                {
                    std::istringstream iss(line);
                    std::string element, vertex;
                    iss >> element >> vertex >> vertex_count;
                }
                else if (line == "end_header")
                {
                    header_complete = true;
                    break;
                }
            }

            if (!header_complete || vertex_count <= 0)
            {
                std::cerr << "Error: Invalid PLY file format or no vertices found" << std::endl;
                return false;
            }

            // 分配内存并读取点数据
            points3D.resize(3, vertex_count);

            for (int i = 0; i < vertex_count; ++i)
            {
                if (!std::getline(inFile, line))
                {
                    std::cerr << "Error: Unexpected end of file while reading vertex data" << std::endl;
                    return false;
                }

                std::istringstream iss(line);
                double x, y, z;
                if (!(iss >> x >> y >> z))
                {
                    std::cerr << "Error: Cannot parse vertex " << i << " from line: " << line << std::endl;
                    return false;
                }

                points3D(0, i) = x;
                points3D(1, i) = y;
                points3D(2, i) = z;
            }

            inFile.close();
            std::cout << "PLY file loaded successfully: " << filename << " ("
                      << vertex_count << " points)" << std::endl;
            return true;
        }

        // Tracks file IO operations - 基于data_tracks.cpp的实现
        bool LoadTracks(const std::string &filename, Tracks &tracks)
        {
            auto start_time = std::chrono::steady_clock::now();

            // 打开tracks文件
            std::fstream tracks_file(filename, std::ios::in);
            if (!tracks_file.is_open())
            {
                std::cerr << "[LoadTracks] Error: Cannot open tracks file: " << filename << std::endl;
                return false;
            }

            // 读取文件头信息
            Size num_view, num_pts, num_obs;
            int is_normalized;
            tracks_file >> num_view >> num_pts >> num_obs >> is_normalized;

            std::cout << "[LoadTracks] Loading tracks from: " << filename << std::endl;
            std::cout << "[LoadTracks] Number of views: " << num_view
                      << ", Points: " << num_pts
                      << ", Observations: " << num_obs
                      << ", Normalized: " << (is_normalized ? "Yes" : "No") << std::endl;

            // 清除现有数据并预分配空间
            tracks.clear();
            tracks.reserve(num_pts);
            tracks.SetNormalized(is_normalized == 1); // 根据标识位设置normalized状态

            // 读取tracks数据
            int record_pts_id = -1;
            TrackInfo track_info;
            ObsInfo tmp_obs;
            IndexT obs_id_counter = 0; // 用于生成唯一的obs_id

            for (Size i = 0; i < num_obs; i++)
            {
                // 读取观测数据
                tracks_file >> tmp_obs.view_id >> tmp_obs.pts_id >> tmp_obs.coord[0] >> tmp_obs.coord[1];

                // 坐标转换（符合原始格式要求）
                tmp_obs.coord[0] = -tmp_obs.coord[0];
                tmp_obs.coord[1] = -tmp_obs.coord[1];

                // 设置观测ID
                tmp_obs.obs_id = obs_id_counter++;

                // 处理新的track
                if (tmp_obs.pts_id != record_pts_id)
                {
                    if (i > 0)
                    {
                        // 设置track使用状态（观测数量少于2的track标记为不使用）
                        if (track_info.track.size() < 2)
                        {
                            track_info.is_used = false;
                        }
                        else
                        {
                            track_info.is_used = true;
                        }
                        tracks.push_back(track_info);
                    }
                    track_info.track.clear(); // 清空track准备新的track
                }

                // 添加观测到当前track
                track_info.track.push_back(tmp_obs);
                record_pts_id = tmp_obs.pts_id;
            }

            // 添加最后一个track（仅当track非空时）
            tracks_file.close();
            if (!track_info.track.empty())
            {
                if (track_info.track.size() < 2)
                {
                    track_info.is_used = false;
                }
                else
                {
                    track_info.is_used = true;
                }
                tracks.push_back(track_info);
            }

            // 输出加载时间
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
            std::cout << "[LoadTracks] Loading completed in "
                      << double(duration.count()) * std::chrono::microseconds::period::num / std::chrono::microseconds::period::den
                      << "s, loaded " << tracks.size() << " tracks" << std::endl;

            return true;
        }

        bool SaveTracks(const std::string &path, const Tracks &tracks)
        {
            auto start_time = std::chrono::steady_clock::now();

            // 创建输出文件
            std::fstream tracks_file(path, std::ios::out | std::ios::trunc);
            if (!tracks_file.is_open())
            {
                std::cerr << "[SaveTracks] Error: Cannot create tracks file: " << path << std::endl;
                return false;
            }

            std::cout << "[SaveTracks] Saving tracks to: " << path << std::endl;

            // 计算观测数量
            Size num_obs = 0;
            for (const auto &track_info : tracks)
            {
                num_obs += track_info.track.size();
            }

            // 计算点数量（tracks数量）
            Size num_pts = tracks.size();

            // 计算视图数量
            Size num_views = 0;
            std::set<ViewId> views_id;
            for (const auto &track_info : tracks)
            {
                for (const auto &track_data : track_info.track)
                {
                    views_id.insert(track_data.view_id);
                }
            }
            num_views = views_id.size();

            // 写入文件头
            tracks_file << num_views << " "
                        << num_pts << " "
                        << num_obs << " "
                        << (tracks.IsNormalized() ? 1 : 0) << std::endl;

            std::cout << "[SaveTracks] Writing " << num_views << " views, "
                      << num_pts << " points, "
                      << num_obs << " observations"
                      << ", Normalized: " << (tracks.IsNormalized() ? "Yes" : "No") << std::endl;

            // 写入tracks数据
            for (const auto &track_info : tracks)
            {
                for (const auto &track_data : track_info.track)
                {
                    tracks_file << track_data.view_id << " ";
                    tracks_file << track_data.pts_id << " ";
                    // 坐标转换（恢复原始格式）并设置高精度
                    tracks_file << std::setprecision(16) << -track_data.coord[0] << " ";
                    tracks_file << std::setprecision(16) << -track_data.coord[1] << std::endl;
                }
            }
            tracks_file.close();

            // 输出保存时间
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
            std::cout << "[SaveTracks] Saving completed in "
                      << double(duration.count()) * std::chrono::microseconds::period::num / std::chrono::microseconds::period::den
                      << "s" << std::endl;

            return true;
        }

        bool ExportSimuDataToMeshLab(
            const std::string &export_path,
            const types::GlobalPoses &global_poses,
            const types::CameraModels &camera_models,
            const types::WorldPointInfo &world_points,
            const std::string &image_filename,
            const std::string &ply_filename,
            const std::string &project_filename)
        {
            if (export_path.empty())
            {
                return true; // 空路径，不需要导出
            }

            if (global_poses.Size() == 0 || camera_models.empty() || world_points.size() == 0)
            {
                std::cerr << "[ExportSimuDataToMeshLab] 错误：数据未初始化或为空" << std::endl;
                return false;
            }

            try
            {
                // 检查并转换位姿格式为RwTw
                types::GlobalPoses poses_rwtw = global_poses;
                if (poses_rwtw.GetPoseFormat() != types::PoseFormat::RwTw)
                {
                    std::cout << "[ExportSimuDataToMeshLab] 位姿格式为 "
                              << (poses_rwtw.GetPoseFormat() == types::PoseFormat::RwTc ? "RwTc" : "Unknown")
                              << "，转换为RwTw格式进行导出" << std::endl;

                    bool conversion_success = poses_rwtw.ConvertPoseFormat(types::PoseFormat::RwTw);
                    if (!conversion_success)
                    {
                        std::cerr << "[ExportSimuDataToMeshLab] 错误：无法将位姿转换为RwTw格式" << std::endl;
                        return false;
                    }
                    std::cout << "[ExportSimuDataToMeshLab] 位姿格式转换成功" << std::endl;
                }
                else
                {
                    std::cout << "[ExportSimuDataToMeshLab] 位姿格式已为RwTw，无需转换" << std::endl;
                }

                // 创建导出目录
                std::filesystem::create_directories(export_path);
                std::cout << "[ExportSimuDataToMeshLab] 开始导出OpenMVG风格meshlab工程到: " << export_path << std::endl;

                // 获取全局位姿和相机参数
                size_t num_img = poses_rwtw.Size();

                // 获取相机模型
                const types::CameraModel *camera_model = types::GetCameraModel(camera_models, 0);
                if (!camera_model)
                {
                    std::cerr << "[ExportSimuDataToMeshLab] 错误：无法获取相机模型" << std::endl;
                    return false;
                }

                // 使用真实的相机内参
                const types::CameraIntrinsics &camera_intrinsics = camera_model->intrinsics;
                int img_width = static_cast<int>(camera_intrinsics.width);
                int img_height = static_cast<int>(camera_intrinsics.height);
                double focal = camera_intrinsics.fx; // 使用真实焦距
                double center_x = camera_intrinsics.cx;
                double center_y = camera_intrinsics.cy;

                std::cout << "[ExportSimuDataToMeshLab] 使用真实相机内参: " << std::endl;
                std::cout << "  - 图像尺寸: " << img_width << "x" << img_height << std::endl;
                std::cout << "  - 焦距: " << focal << std::endl;
                std::cout << "  - 主点: (" << center_x << ", " << center_y << ")" << std::endl;

                // 1. 生成模拟图像文件
                std::string image_path = std::filesystem::path(export_path) / image_filename;

                // 生成简单的白色图像（PPM格式）
                std::ofstream image_file(image_path);
                if (image_file.is_open())
                {
                    image_file << "P3\n";
                    image_file << img_width << " " << img_height << "\n";
                    image_file << "255\n";

                    for (int y = 0; y < img_height; ++y)
                    {
                        for (int x = 0; x < img_width; ++x)
                        {
                            image_file << "255 255 255 "; // 白色像素
                        }
                        image_file << "\n";
                    }
                    image_file.close();
                    std::cout << "[ExportSimuDataToMeshLab] 创建模拟图像: " << image_path << std::endl;
                }
                else
                {
                    std::cerr << "[ExportSimuDataToMeshLab] 错误：无法创建图像文件: " << image_path << std::endl;
                    return false;
                }

                // 2. 导出点云为PLY格式
                std::string ply_path = std::filesystem::path(export_path) / ply_filename;

                // 使用WritePlyFile函数代替手动写PLY文件
                // 创建Points3d矩阵，包含有效点（OpenMVG风格转换后不需要翻转坐标）
                size_t valid_points_count = 0;
                for (size_t i = 0; i < world_points.size(); ++i)
                {
                    if (world_points.isUsed(i))
                    {
                        valid_points_count++;
                    }
                }

                types::Points3d points3d_openmvg(3, valid_points_count);
                size_t idx = 0;
                for (size_t i = 0; i < world_points.size(); ++i)
                {
                    if (world_points.isUsed(i))
                    {
                        types::Vector3d point = world_points.getPoint(i);
                        points3d_openmvg.col(idx) = types::Vector3d(point.x(), point.y(), point.z()); // 保持原坐标
                        idx++;
                    }
                }

                // 使用WritePlyFile函数写入PLY文件
                if (!WritePlyFile(ply_path, points3d_openmvg))
                {
                    std::cerr << "[ExportSimuDataToMeshLab] 错误：无法创建PLY文件: " << ply_path << std::endl;
                    return false;
                }
                std::cout << "[ExportSimuDataToMeshLab] 导出点云（OpenMVG风格坐标系）: " << ply_path << std::endl;

                // 3. 生成meshlab工程文件
                std::string mlp_path = std::filesystem::path(export_path) / project_filename;
                std::ofstream outfile(mlp_path);

                if (!outfile.is_open())
                {
                    std::cerr << "[ExportSimuDataToMeshLab] 错误：无法创建meshlab工程文件: " << mlp_path << std::endl;
                    return false;
                }

                // 写入文件头
                outfile << "<!DOCTYPE MeshLabDocument>" << "\n"
                        << "<MeshLabProject>" << "\n";

                // 写入MeshGroup
                outfile << " <MeshGroup>" << "\n"
                        << "  <MLMesh label=\"" << ply_filename << "\" filename=\"" << ply_filename << "\">" << "\n"
                        << "   <MLMatrix44>" << "\n"
                        << "1.000000 0.000000 0.000000 0.000000 " << "\n"
                        << "0.000000 1.000000 0.000000 0.000000 " << "\n"
                        << "0.000000 0.000000 1.000000 0.000000 " << "\n"
                        << "0.000000 0.000000 0.000000 1.000000 " << "\n"
                        << "</MLMatrix44>" << "\n"
                        << "  </MLMesh>" << "\n"
                        << " </MeshGroup>" << "\n";

                // 写入RasterGroup
                outfile << " <RasterGroup>" << "\n";

                // 为每个相机写入MLRaster
                for (size_t i = 0; i < num_img; ++i)
                {
                    // 获取位姿
                    types::Matrix3d R_our = poses_rwtw.GetRotation(i);
                    types::Vector3d t_our = poses_rwtw.GetTranslation(i);

                    // 构建相机内参矩阵
                    types::Matrix3d K;
                    K << focal, 0, center_x,
                        0, focal, center_y,
                        0, 0, 1;

                    // 使用OpenMVG风格的姿态转换
                    types::Matrix3d R_meshlab;
                    types::Vector3d optical_center_meshlab;
                    ConvertToMeshLabFormat(R_our, t_our, K, &R_meshlab, &optical_center_meshlab);

                    // 相机标签
                    std::string raster_label = "camera_" + std::to_string(i) + "_" + image_filename;

                    outfile << "  <MLRaster label=\"" << raster_label << "\">" << "\n";

                    // VCGCamera参数
                    outfile << "   <VCGCamera"
                            << " TranslationVector=\"" << optical_center_meshlab(0) << " " << optical_center_meshlab(1) << " " << optical_center_meshlab(2) << " 1\""
                            << " LensDistortion=\"0.000000 0.000000\""
                            << " ViewportPx=\"" << img_width << " " << img_height << "\""
                            << " PixelSizeMm=\"1.000000 1.000000\""
                            << " CenterPx=\"" << center_x << " " << center_y << "\""
                            << " FocalMm=\"" << focal << "\""
                            << " RotationMatrix=\""
                            << R_meshlab(0, 0) << " " << R_meshlab(0, 1) << " " << R_meshlab(0, 2) << " 0 "
                            << R_meshlab(1, 0) << " " << R_meshlab(1, 1) << " " << R_meshlab(1, 2) << " 0 "
                            << R_meshlab(2, 0) << " " << R_meshlab(2, 1) << " " << R_meshlab(2, 2) << " 0 "
                            << "0 0 0 1 \"/>" << "\n";

                    // Plane标签（图像文件关联）
                    outfile << "   <Plane semantic=\"\" fileName=\"" << image_filename << "\"/>" << "\n";
                    outfile << "  </MLRaster>" << "\n";
                }

                // 写入文件尾部
                outfile << " </RasterGroup>" << "\n"
                        << "</MeshLabProject>" << "\n";

                outfile.close();

                std::cout << "[ExportSimuDataToMeshLab] 成功导出OpenMVG风格meshlab工程文件: " << mlp_path << std::endl;
                std::cout << "  - 点云文件: " << ply_filename << std::endl;
                std::cout << "  - 图像文件: " << image_filename << std::endl;
                std::cout << "  - 相机数量: " << num_img << std::endl;
                std::cout << "  - 图像尺寸: " << img_width << "x" << img_height << " (真实内参)" << std::endl;
                std::cout << "  - 焦距: " << focal << " (真实内参)" << std::endl;
                std::cout << "  - 主点: (" << center_x << ", " << center_y << ") (真实内参)" << std::endl;
                std::cout << "  - 相机姿态: 使用OpenMVG风格转换，已修正Z轴朝向" << std::endl;

                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[ExportSimuDataToMeshLab] 导出OpenMVG风格meshlab工程时发生错误: " << e.what() << std::endl;
                return false;
            }
        }

        bool ExportToMeshLab(
            const std::string &export_path,
            const types::GlobalPoses &global_poses,
            const types::CameraModels &camera_models,
            const types::WorldPointInfo &world_points,
            const types::ImagePathsPtr &image_paths,
            const std::string &ply_filename,
            const std::string &project_filename)
        {
            if (export_path.empty())
            {
                return true; // 空路径，不需要导出
            }

            if (global_poses.Size() == 0 || camera_models.empty() || world_points.size() == 0)
            {
                std::cerr << "[ExportToMeshLab] 错误：数据未初始化或为空" << std::endl;
                return false;
            }

            try
            {
                // 检查并转换位姿格式为RwTw
                types::GlobalPoses poses_rwtw = global_poses;
                if (poses_rwtw.GetPoseFormat() != types::PoseFormat::RwTw)
                {
                    std::cout << "[ExportToMeshLab] 位姿格式为 "
                              << (poses_rwtw.GetPoseFormat() == types::PoseFormat::RwTc ? "RwTc" : "Unknown")
                              << "，转换为RwTw格式进行导出" << std::endl;

                    bool conversion_success = poses_rwtw.ConvertPoseFormat(types::PoseFormat::RwTw);
                    if (!conversion_success)
                    {
                        std::cerr << "[ExportToMeshLab] 错误：无法将位姿转换为RwTw格式" << std::endl;
                        return false;
                    }
                    std::cout << "[ExportToMeshLab] 位姿格式转换成功" << std::endl;
                }
                else
                {
                    std::cout << "[ExportToMeshLab] 位姿格式已为RwTw，无需转换" << std::endl;
                }

                // 创建导出目录
                std::filesystem::create_directories(export_path);
                std::cout << "[ExportToMeshLab] 开始导出OpenMVG风格meshlab工程到: " << export_path << std::endl;

                // 获取全局位姿和相机参数
                size_t num_img = poses_rwtw.Size();

                // 获取相机模型
                const types::CameraModel *camera_model = types::GetCameraModel(camera_models, 0);
                if (!camera_model)
                {
                    std::cerr << "[ExportToMeshLab] 错误：无法获取相机模型" << std::endl;
                    return false;
                }

                // 使用真实的相机内参
                const types::CameraIntrinsics &camera_intrinsics = camera_model->intrinsics;
                int img_width = static_cast<int>(camera_intrinsics.width);
                int img_height = static_cast<int>(camera_intrinsics.height);
                double focal = camera_intrinsics.fx;
                double center_x = camera_intrinsics.cx;
                double center_y = camera_intrinsics.cy;

                std::cout << "[ExportToMeshLab] 使用相机内参: " << std::endl;
                std::cout << "  - 图像尺寸: " << img_width << "x" << img_height << std::endl;
                std::cout << "  - 焦距: " << focal << std::endl;
                std::cout << "  - 主点: (" << center_x << ", " << center_y << ")" << std::endl;

                // 1. 处理图像文件
                bool use_real_images = (image_paths != nullptr && !image_paths->empty());
                std::string default_image_filename = "simulate_pic.tiff";

                if (use_real_images)
                {
                    std::cout << "[ExportToMeshLab] 使用真实图像，图像数量: " << image_paths->size() << std::endl;
                    std::cout << "[ExportToMeshLab] 图像将使用绝对路径引用，无需复制文件" << std::endl;

                    // 调试信息：显示当前工作目录和第一个图像路径
                    std::cout << "[ExportToMeshLab] 当前工作目录: " << std::filesystem::current_path() << std::endl;
                    if (!image_paths->empty())
                    {
                        std::cout << "[ExportToMeshLab] 第一个图像原始路径: " << (*image_paths)[0].first << std::endl;
                        std::filesystem::path first_path((*image_paths)[0].first);
                        std::cout << "[ExportToMeshLab] 第一个图像是否为绝对路径: " << (first_path.is_absolute() ? "是" : "否") << std::endl;
                        if (first_path.is_absolute())
                        {
                            std::cout << "[ExportToMeshLab] 第一个图像文件是否存在: " << (std::filesystem::exists(first_path) ? "是" : "否") << std::endl;
                        }
                    }
                }
                else
                {
                    // 生成模拟图像
                    std::string image_path = std::filesystem::path(export_path) / default_image_filename;

                    std::ofstream image_file(image_path);
                    if (image_file.is_open())
                    {
                        image_file << "P3\n";
                        image_file << img_width << " " << img_height << "\n";
                        image_file << "255\n";

                        for (int y = 0; y < img_height; ++y)
                        {
                            for (int x = 0; x < img_width; ++x)
                            {
                                image_file << "255 255 255 ";
                            }
                            image_file << "\n";
                        }
                        image_file.close();
                        std::cout << "[ExportToMeshLab] 创建模拟图像: " << image_path << std::endl;
                    }
                    else
                    {
                        std::cerr << "[ExportToMeshLab] 错误：无法创建模拟图像文件: " << image_path << std::endl;
                        return false;
                    }
                }

                // 2. 导出点云为PLY格式
                std::string ply_path = std::filesystem::path(export_path) / ply_filename;

                // 创建Points3d矩阵，包含有效点（OpenMVG风格转换后不需要翻转坐标）
                size_t valid_points_count = 0;
                for (size_t i = 0; i < world_points.size(); ++i)
                {
                    if (world_points.isUsed(i))
                    {
                        valid_points_count++;
                    }
                }

                types::Points3d points3d_openmvg(3, valid_points_count);
                size_t idx = 0;
                for (size_t i = 0; i < world_points.size(); ++i)
                {
                    if (world_points.isUsed(i))
                    {
                        types::Vector3d point = world_points.getPoint(i);
                        points3d_openmvg.col(idx) = types::Vector3d(point.x(), point.y(), point.z()); // 保持原坐标
                        idx++;
                    }
                }

                if (!WritePlyFile(ply_path, points3d_openmvg))
                {
                    std::cerr << "[ExportToMeshLab] 错误：无法创建PLY文件: " << ply_path << std::endl;
                    return false;
                }
                std::cout << "[ExportToMeshLab] 导出点云（OpenMVG风格坐标系）: " << ply_path << std::endl;

                // 3. 生成meshlab工程文件
                std::string mlp_path = std::filesystem::path(export_path) / project_filename;
                std::ofstream outfile(mlp_path);

                if (!outfile.is_open())
                {
                    std::cerr << "[ExportToMeshLab] 错误：无法创建meshlab工程文件: " << mlp_path << std::endl;
                    return false;
                }

                // 写入文件头
                outfile << "<!DOCTYPE MeshLabDocument>" << "\n"
                        << "<MeshLabProject>" << "\n";

                // 写入MeshGroup
                outfile << " <MeshGroup>" << "\n"
                        << "  <MLMesh label=\"" << ply_filename << "\" filename=\"" << ply_filename << "\">" << "\n"
                        << "   <MLMatrix44>" << "\n"
                        << "1.000000 0.000000 0.000000 0.000000 " << "\n"
                        << "0.000000 1.000000 0.000000 0.000000 " << "\n"
                        << "0.000000 0.000000 1.000000 0.000000 " << "\n"
                        << "0.000000 0.000000 0.000000 1.000000 " << "\n"
                        << "</MLMatrix44>" << "\n"
                        << "  </MLMesh>" << "\n"
                        << " </MeshGroup>" << "\n";

                // 写入RasterGroup
                outfile << " <RasterGroup>" << "\n";

                // 为每个相机写入MLRaster
                for (size_t i = 0; i < num_img; ++i)
                {
                    // 获取位姿
                    types::Matrix3d R_our = poses_rwtw.GetRotation(i);
                    types::Vector3d t_our = poses_rwtw.GetTranslation(i);

                    // 构建相机内参矩阵
                    types::Matrix3d K;
                    K << focal, 0, center_x,
                        0, focal, center_y,
                        0, 0, 1;

                    // 使用OpenMVG风格的姿态转换
                    types::Matrix3d R_meshlab;
                    types::Vector3d optical_center_meshlab;
                    ConvertToMeshLabFormat(R_our, t_our, K, &R_meshlab, &optical_center_meshlab);

                    // 确定当前相机使用的图像文件路径（绝对路径）
                    std::string current_image_filepath;
                    std::string current_image_filename;

                    if (use_real_images && i < image_paths->size())
                    {
                        // 获取图像的绝对路径
                        std::filesystem::path original_image_path((*image_paths)[i].first);

                        // 检查原始路径是否已经是绝对路径
                        if (original_image_path.is_absolute())
                        {
                            current_image_filepath = original_image_path.string();
                        }
                        else
                        {
                            // 如果是相对路径，需要正确地转换为绝对路径
                            current_image_filepath = std::filesystem::absolute(original_image_path).string();
                        }

                        current_image_filename = original_image_path.filename().string();

                        // 验证文件是否存在
                        if (!std::filesystem::exists(current_image_filepath))
                        {
                            std::cerr << "[ExportToMeshLab] 警告：图像文件不存在: " << current_image_filepath << std::endl;
                            std::cerr << "  原始路径: " << (*image_paths)[i].first << std::endl;
                            std::cerr << "  转换后路径: " << current_image_filepath << std::endl;
                        }
                    }
                    else
                    {
                        // 使用模拟图像的相对路径（因为模拟图像在export_path中）
                        current_image_filepath = default_image_filename;
                        current_image_filename = default_image_filename;
                    }

                    // 相机标签
                    std::string raster_label = "camera_" + std::to_string(i) + "_" + current_image_filename;

                    outfile << "  <MLRaster label=\"" << raster_label << "\">" << "\n";

                    // VCGCamera参数
                    outfile << "   <VCGCamera"
                            << " TranslationVector=\"" << optical_center_meshlab(0) << " " << optical_center_meshlab(1) << " " << optical_center_meshlab(2) << " 1\""
                            << " LensDistortion=\"0.000000 0.000000\""
                            << " ViewportPx=\"" << img_width << " " << img_height << "\""
                            << " PixelSizeMm=\"1.000000 1.000000\""
                            << " CenterPx=\"" << center_x << " " << center_y << "\""
                            << " FocalMm=\"" << focal << "\""
                            << " RotationMatrix=\""
                            << R_meshlab(0, 0) << " " << R_meshlab(0, 1) << " " << R_meshlab(0, 2) << " 0 "
                            << R_meshlab(1, 0) << " " << R_meshlab(1, 1) << " " << R_meshlab(1, 2) << " 0 "
                            << R_meshlab(2, 0) << " " << R_meshlab(2, 1) << " " << R_meshlab(2, 2) << " 0 "
                            << "0 0 0 1 \"/>" << "\n";

                    // Plane标签（使用绝对路径引用图像文件）
                    outfile << "   <Plane semantic=\"\" fileName=\"" << current_image_filepath << "\"/>" << "\n";
                    outfile << "  </MLRaster>" << "\n";
                }

                // 写入文件尾部
                outfile << " </RasterGroup>" << "\n"
                        << "</MeshLabProject>" << "\n";

                outfile.close();

                std::cout << "[ExportToMeshLab] 成功导出OpenMVG风格meshlab工程文件: " << mlp_path << std::endl;
                std::cout << "  - 点云文件: " << ply_filename << std::endl;
                std::cout << "  - 图像类型: " << (use_real_images ? "真实图像（绝对路径引用）" : "模拟图像") << std::endl;
                std::cout << "  - 相机数量: " << num_img << std::endl;
                std::cout << "  - 相机姿态: 使用OpenMVG风格转换，已修正Z轴朝向" << std::endl;
                if (use_real_images)
                {
                    std::cout << "  - 注意: 使用绝对路径引用原始图像，无需复制图像文件" << std::endl;
                }

                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[ExportToMeshLab] 导出OpenMVG风格meshlab工程时发生错误: " << e.what() << std::endl;
                return false;
            }
        }

        bool ExportToMeshLab(
            const std::string &export_path,
            const DataPtr &data_global_poses_ptr,
            const DataPtr &data_camera_models_ptr,
            const DataPtr &data_world_points_ptr,
            const DataPtr &data_images_ptr,
            const std::string &ply_filename,
            const std::string &project_filename)
        {
            try
            {
                // 从DataPtr获取具体的数据
                auto global_poses_ptr = GetDataPtr<types::GlobalPoses>(data_global_poses_ptr);
                if (!global_poses_ptr)
                {
                    std::cerr << "[ExportToMeshLab] 错误：无法获取GlobalPoses数据" << std::endl;
                    return false;
                }

                auto camera_models_ptr = GetDataPtr<types::CameraModels>(data_camera_models_ptr);
                if (!camera_models_ptr)
                {
                    std::cerr << "[ExportToMeshLab] 错误：无法获取CameraModels数据" << std::endl;
                    return false;
                }

                auto world_points_ptr = GetDataPtr<types::WorldPointInfo>(data_world_points_ptr);
                if (!world_points_ptr)
                {
                    std::cerr << "[ExportToMeshLab] 错误：无法获取WorldPointInfo数据" << std::endl;
                    return false;
                }

                // 尝试获取图像路径数据（可选）
                types::ImagePathsPtr image_paths_ptr = nullptr;
                if (data_images_ptr)
                {
                    image_paths_ptr = GetDataPtr<types::ImagePaths>(data_images_ptr);
                    if (!image_paths_ptr)
                    {
                        std::cout << "[ExportToMeshLab] 注意：无法获取ImagePaths数据，将使用模拟图像" << std::endl;
                    }
                }

                // 调用具体实现函数
                return ExportToMeshLab(
                    export_path,
                    *global_poses_ptr,
                    *camera_models_ptr,
                    *world_points_ptr,
                    image_paths_ptr,
                    ply_filename,
                    project_filename);
            }
            catch (const std::exception &e)
            {
                std::cerr << "[ExportToMeshLab] DataPtr版本导出时发生错误: " << e.what() << std::endl;
                return false;
            }
        }

    }
}
