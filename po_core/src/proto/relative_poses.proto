syntax = "proto3";

package pomvg.proto;

// 相对位姿数据
message RelativePose {
    int64 i = 1;              // 源视图索引
    int64 j = 2;              // 目标视图索引
    repeated double rij = 3;   // 旋转矩阵(9个元素,按列存储)
    double tij_x = 4;         // 平移向量x分量
    double tij_y = 5;         // 平移向量y分量
    double tij_z = 6;         // 平移向量z分量
    float weight = 7;         // 权重因子
}

// 相对位姿集合
message RelativePoses {
    repeated RelativePose poses = 1;  // 所有相对位姿
}