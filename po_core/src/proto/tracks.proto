syntax = "proto3";

package pomvg.proto;

// 单个观测点信息
message Observation {
    uint32 view_id = 1;       // 视图ID
    uint32 pts_id = 2;        // 3D点ID
    double coord_x = 3;       // 图像坐标x
    double coord_y = 4;       // 图像坐标y
    bool is_used = 5;         // 是否使用该观测点
    uint32 obs_id = 6;        // 观测ID
}

// 单个Track信息
message Track {
    repeated Observation observations = 1;  // 观测点列表
    bool is_used = 2;                      // 是否使用该Track
}

// 所有Tracks数据
message TracksData {
    repeated Track tracks = 1;             // Track列表
    bool is_normalized = 2;                // 是否已经归一化
}
