syntax = "proto3";

package PoSDK.proto;

// 单个特征点信息
message FeaturePoint {
    // 位置坐标
    double coord_x = 1;
    double coord_y = 2;
    // 特征点大小
    float size = 3;
    // 特征点方向
    float angle = 4;
    // 描述子已移除，将单独处理
    // 是否使用
    bool is_used = 6;
}

// 单张图片的特征信息
message ImageFeatures {
    // 图像路径
    string image_path = 1;
    // 特征点列表
    repeated FeaturePoint features = 2;
    // 是否使用该图像
    bool is_used = 3;
}

// 所有图像的特征信息
message FeaturesData {
    // 图像特征列表
    repeated ImageFeatures image_features = 1;
}
