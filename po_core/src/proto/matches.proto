syntax = "proto3";

package pomvg.proto;

// 特征点匹配对
message IdMatch {
    uint32 i = 1;  // 第一个特征点的索引
    uint32 j = 2;  // 第二个特征点的索引
    bool is_inlier = 3;  // RANSAC内点标记
}

// 视图对之间的匹配
message ViewMatches {
    uint32 view_i = 1;  // 第一个视图的索引
    uint32 view_j = 2;  // 第二个视图的索引
    repeated IdMatch matches = 3;  // 匹配对列表
}

// 所有视图间的匹配信息
message MatchesData {
    repeated ViewMatches view_matches = 1;
} 