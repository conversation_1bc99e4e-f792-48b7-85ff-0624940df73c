syntax = "proto3";

package pomvg.proto;

// 相机畸变类型枚举
enum DistortionType {
    NO_DISTORTION = 0;    // 无畸变
    RADIAL_K1 = 1;       // 一阶径向畸变
    RADIAL_K3 = 2;       // 三阶径向畸变
    BROWN_CONRADY = 3;   // Brown-Conrady畸变模型
}

// 相机模型类型枚举
enum CameraModelType {
    PINHOLE = 0;         // 针孔相机
    FISHEYE = 1;         // 鱼眼相机
    SPHERICAL = 2;       // 球面相机
}

// 相机内参数据
message CameraModel {
    // 内参基本参数
    double fx = 1;           // x方向焦距
    double fy = 2;           // y方向焦距
    double cx = 3;           // x方向主点偏移
    double cy = 4;           // y方向主点偏移
    uint32 width = 5;        // 图像宽度
    uint32 height = 6;       // 图像高度
    
    // 相机类型
    CameraModelType model_type = 7;      // 相机模型类型
    DistortionType distortion_type = 8;  // 畸变类型
    
    // 畸变参数
    repeated double radial_distortion = 9;      // 径向畸变参数
    repeated double tangential_distortion = 10;  // 切向畸变参数
    
    // 相机信息
    string camera_make = 11;     // 相机制造商
    string camera_model = 12;    // 相机型号
    string serial_number = 13;   // 序列号
}

// 相机模型数据集合
message CameraModelsData {
    repeated CameraModel camera_models = 1;
}
