# ==============================================================================
# Copyright (c) 2024 PoSDK Project
# 文件: po_core/src/proto/CMakeLists.txt
# 描述: Protocol Buffers 核心库构建配置
# 作者: Qi Cai
# 日期: 2024-01
# 功能: 负责构建和配置Protocol Buffers相关的核心库组件
# ==============================================================================

# ------------------------------------------------------------------------------
# 输出目录配置
# ------------------------------------------------------------------------------
# 设置最终输出目录
set(OUTPUT_PROTO_INCLUDE_DIR "${OUTPUT_BASE_DIR}/include/proto")
# 设置临时生成文件目录
set(PROTO_GENERATED_DIR "${TEMP_FILES_DIR}/generated/proto")

# 确保目录存在
file(MAKE_DIRECTORY ${OUTPUT_PROTO_INCLUDE_DIR})
file(MAKE_DIRECTORY ${PROTO_GENERATED_DIR})

# ------------------------------------------------------------------------------
# Proto文件配置
# ------------------------------------------------------------------------------
# 定义proto文件列表（使用绝对路径）
set(PROTO_FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/features.proto
    ${CMAKE_CURRENT_SOURCE_DIR}/matches.proto
    ${CMAKE_CURRENT_SOURCE_DIR}/tracks.proto
    ${CMAKE_CURRENT_SOURCE_DIR}/camera_model.proto
    ${CMAKE_CURRENT_SOURCE_DIR}/relative_poses.proto

)
message(STATUS "PROTO_FILES: ${PROTO_FILES}")

# ------------------------------------------------------------------------------
# 创建库目标（必须在生成之前创建）
# ------------------------------------------------------------------------------
add_library(pomvg_proto SHARED ${PROTO_SRCS} ${PROTO_HDRS})
add_library(PoSDK::pomvg_proto ALIAS pomvg_proto)

# 设置库属性
set_target_properties(pomvg_proto PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    POSITION_INDEPENDENT_CODE ON
    LIBRARY_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    ARCHIVE_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    RUNTIME_OUTPUT_DIRECTORY "${OUTPUT_BIN_DIR}"
)

# ------------------------------------------------------------------------------
# Protobuf 生成配置
# ------------------------------------------------------------------------------
# 使用 protobuf_generate 添加源文件到目标
protobuf_generate(
    TARGET pomvg_proto
    LANGUAGE cpp
    PROTOC_OUT_DIR "${OUTPUT_PROTO_INCLUDE_DIR}"
    PROTOS ${PROTO_FILES}
)

# ------------------------------------------------------------------------------
# 配置目标
# ------------------------------------------------------------------------------
target_include_directories(pomvg_proto
    PUBLIC
        $<BUILD_INTERFACE:${OUTPUT_PROTO_INCLUDE_DIR}>
        $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
    PRIVATE
        ${Protobuf_INCLUDE_DIRS}
)


target_link_libraries(pomvg_proto
    PUBLIC
        protobuf::libprotobuf
        absl::log
        absl::log_internal_check_op
        absl::log_internal_message
        absl::base
        absl::strings
        absl::synchronization
)

# ------------------------------------------------------------------------------
# 安装配置
# ------------------------------------------------------------------------------
