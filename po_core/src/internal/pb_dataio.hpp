#ifndef _POMVG_PB_DATAIO_HPP_
#define _POMVG_PB_DATAIO_HPP_

#include <memory>
#include <vector>
#include <iostream>
#include <filesystem>
#include <fstream>
#include <string>
#include <cstddef>

#include "interfaces.hpp"
#include <google/protobuf/message.h>

namespace PoSDK
{
    namespace Interface
    {

// ================ Protocol Buffers Macro section ======================
// Copyright: author <PERSON>
// Readme:
// ===============================================================
// 基础类型序列化宏
// 支持类型: int8_t, uint8_t, int16_t, uint16_t, int32_t, uint32_t, int64_t, uint64_t, float, double, bool, string
#define PROTO_SET_BASIC(proto_msg, field, value) \
    proto_msg->set_##field(value)

#define PROTO_GET_BASIC(proto_msg, field, value) \
    value = proto_msg.field()

// Eigen Vector2 序列化宏
// 支持类型: Vector2f(float) | Vector2d(double)
// proto字段: float/double field_x, field_y
#define PROTO_SET_VECTOR2F(proto_msg, field, vec) \
    proto_msg->set_##field##_x(vec.x());          \
    proto_msg->set_##field##_y(vec.y())

#define PROTO_GET_VECTOR2F(proto_msg, field, vec) \
    vec = Vector2f(proto_msg.field##_x(), proto_msg.field##_y())

#define PROTO_SET_VECTOR2D(proto_msg, field, vec) \
    proto_msg->set_##field##_x(vec.x());          \
    proto_msg->set_##field##_y(vec.y())

#define PROTO_GET_VECTOR2D(proto_msg, field, vec) \
    vec = Vector2d(proto_msg.field##_x(), proto_msg.field##_y())

// Eigen Vector3 序列化宏
// 支持类型: Vector3f(float) | Vector3d(double)
// proto字段: float/double field_x, field_y, field_z
#define PROTO_SET_VECTOR3D(proto_msg, field, vec) \
    proto_msg->set_##field##_x(vec.x());          \
    proto_msg->set_##field##_y(vec.y());          \
    proto_msg->set_##field##_z(vec.z())

#define PROTO_GET_VECTOR3D(proto_msg, field, vec) \
    vec = Vector3d(proto_msg.field##_x(), proto_msg.field##_y(), proto_msg.field##_z())

// Eigen Matrix3 序列化宏
// 支持类型: Matrix3f(float) | Matrix3d(double)
// proto字段: repeated float/double field
// 存储顺序: 按列优先顺序存储9个元素
#define PROTO_SET_MATRIX3D(proto_msg, field, mat) \
    for (int i = 0; i < 9; ++i)                   \
    {                                             \
        proto_msg->add_##field(mat.data()[i]);    \
    }

#define PROTO_GET_MATRIX3D(proto_msg, field, mat) \
    for (int i = 0; i < 9; ++i)                   \
    {                                             \
        mat.data()[i] = proto_msg.field(i);       \
    }

// 数组序列化宏
// 支持类型: vector<T>, 其中T为基础类型
// proto字段: repeated T field
#define PROTO_SET_ARRAY(proto_msg, field, array) \
    for (const auto &value : array)              \
    {                                            \
        proto_msg->add_##field(value);           \
    }

#define PROTO_GET_ARRAY(proto_msg, field, array) \
    array.clear();                               \
    array.reserve(proto_msg.field##_size());     \
    for (const auto &value : proto_msg.field())  \
    {                                            \
        array.push_back(value);                  \
    }

// 添加枚举类型序列化宏
#define PROTO_SET_ENUM(proto_msg, field, value) \
    proto_msg->set_##field(static_cast<decltype(proto_msg->field())>(value))

#define PROTO_GET_ENUM(proto_msg, field, value) \
    value = static_cast<decltype(value)>(proto_msg.field())

        //=========================== PbDataIO ==================================
        // # Copyright (c) 2024 PO tools author: Qi Cai.
        //  PbDataIO is a base class for DataIO that supports protobuf serialization.
        //==========================================================================
        /**
         * @brief 支持protobuf序列化的DataIO基类
         * @details 用于支持protobuf序列化的DataIO基类
         */
        class PbDataIO : public DataIO
        {
        public:
            virtual ~PbDataIO() = default;

            // 设置默认存储目录
            void SetStorageFolder(const std::string &path)
            {
                storage_dir_ = path; // 改名以更好地表达其作为目录的本质
            }

            // 获取当前存储目录
            const std::string &GetStorageFolder() const
            {
                return storage_dir_;
            }

            // 扩展的Save函数，增加文件名和扩展名参数
            bool Save(const std::string &folder = "",
                      const std::string &filename = "",
                      const std::string &extension = ".pb") override
            {
                try
                {
                    std::filesystem::path file_path;

                    // 1. 处理存储目录
                    if (folder.empty() && storage_dir_.empty())
                    {
                        std::cerr << "No storage directory specified" << std::endl;
                        return false;
                    }

                    // 2. 确定基础目录
                    file_path = folder.empty() ? storage_dir_ : folder;

                    // 3. 转换为绝对路径
                    if (file_path.is_relative())
                    {
                        file_path = std::filesystem::absolute(file_path);
                    }

                    // 4. 添加文件名
                    if (filename.empty())
                    {
                        file_path /= (GetType() + "_default");
                    }
                    else
                    {
                        // 移除可能存在的扩展名，以确保使用指定的扩展名
                        std::string base_name = filename;
                        size_t ext_pos = base_name.find_last_of('.');
                        if (ext_pos != std::string::npos)
                        {
                            base_name = base_name.substr(0, ext_pos);
                        }
                        file_path /= base_name;
                    }

                    // 5. 确保添加扩展名（如果没有提供，使用默认的 .pb）
                    std::string ext = extension.empty() ? ".pb" : extension;
                    if (ext[0] != '.')
                    {
                        ext = "." + ext;
                    }
                    file_path.replace_extension(ext);

                    // 6. 创建目录
                    auto directory = file_path.parent_path();
                    if (!directory.empty())
                    {
                        if (std::filesystem::exists(directory))
                        {
                            if (!std::filesystem::is_directory(directory))
                            {
                                std::cerr << "Path exists but is not a directory: " << directory << std::endl;
                                return false;
                            }
                        }
                        else
                        {
                            std::cout << "Creating directory: " << directory << std::endl;
                            if (!std::filesystem::create_directories(directory))
                            {
                                std::cerr << "Failed to create directory: " << directory << std::endl;
                                return false;
                            }
                        }
                    }

                    // 7. 序列化并保存
                    auto message = ToProto();
                    if (!message)
                        return false;

                    bool success = SaveProtoToFile(*message, file_path.string());
                    if (success)
                    {
                        std::cout << "Saved to: " << file_path << std::endl;
                    }
                    return success;
                }
                catch (const std::filesystem::filesystem_error &e)
                {
                    std::cerr << "Filesystem error: " << e.what() << std::endl;
                    return false;
                }
            }

            bool Load(const std::string &filepath = "",
                      const std::string &file_type = "pb") override
            {
                try
                {
                    // 1. 处理文件路径
                    std::filesystem::path actual_path;
                    if (filepath.empty())
                    {
                        if (storage_dir_.empty())
                        {
                            std::cerr << "No storage directory or file path specified" << std::endl;
                            return false;
                        }
                        // 使用默认路径和文件名，确保使用一致的扩展名处理
                        actual_path = std::filesystem::path(storage_dir_) /
                                      (GetType() + "_default");
                        actual_path.replace_extension(".pb");
                    }
                    else
                    {
                        actual_path = filepath;
                        // 如果提供的路径没有扩展名，添加默认扩展名
                        if (actual_path.extension().empty())
                        {
                            actual_path.replace_extension(".pb");
                        }
                    }

                    // 2. 检查文件是否存在
                    if (!std::filesystem::exists(actual_path))
                    {
                        std::cerr << "File does not exist: " << actual_path << std::endl;
                        return false;
                    }

                    // 3. 加载并反序列化
                    auto message = CreateProtoMessage();
                    if (!message)
                        return false;

                    if (!LoadProtoFromFile(actual_path.string(), *message))
                    {
                        return false;
                    }

                    return FromProto(*message);
                }
                catch (const std::filesystem::filesystem_error &e)
                {
                    std::cerr << "Filesystem error: " << e.what() << std::endl;
                    return false;
                }
            }

        protected:
            // 派生类需要实现的接口
            virtual std::unique_ptr<google::protobuf::Message> CreateProtoMessage() const = 0;
            virtual std::unique_ptr<google::protobuf::Message> ToProto() const = 0;
            virtual bool FromProto(const google::protobuf::Message &message) = 0;

        private:
            std::string storage_dir_; // 改名为 storage_dir_ 以更好地表达其作为目录的本质
            // 工具函数
            bool SaveProtoToFile(const google::protobuf::Message &message,
                                 const std::string &path) const
            {
                // 创建输出文件流
                std::ofstream ofs(path, std::ios::binary);
                if (!ofs)
                {
                    std::cerr << "Failed to open file for writing: " << path << std::endl;
                    return false;
                }

                std::cout << "SaveProtoToFile: " << path << std::endl;

                // 序列化到字符串
                std::string serialized;
                if (!message.SerializeToString(&serialized))
                {
                    std::cerr << "Failed to serialize message" << std::endl;
                    return false;
                }

                // 写入文件
                ofs.write(serialized.data(), serialized.size());
                return ofs.good();
            }

            bool LoadProtoFromFile(const std::string &path,
                                   google::protobuf::Message &message) const
            {
                // 创建输入文件流
                std::ifstream ifs(path, std::ios::binary);
                if (!ifs)
                {
                    std::cerr << "Failed to open file for reading: " << path << std::endl;
                    return false;
                }

                // 读取整个文件内容
                std::string serialized((std::istreambuf_iterator<char>(ifs)),
                                       std::istreambuf_iterator<char>());

                // 从字符串解析
                if (!message.ParseFromString(serialized))
                {
                    std::cerr << "Failed to parse message from file: " << path << std::endl;
                    return false;
                }

                return true;
            }
        };

    } // namespace Interface
} // namespace PoSDK

#endif // _POMVG_PB_DATAIO_HPP_
