#ifndef _POMVG_EVALUATOR_HPP_
#define _POMVG_EVALUATOR_HPP_

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <tuple>
#include <map>
#include <boost/regex.hpp>
#include <boost/lexical_cast.hpp>
#include "interfaces.hpp"

namespace PoSDK
{
    namespace Interface
    {
        /**
         * @brief 评估信息结构体 - 增强版本，支持多类型Note
         *
         * 存储单个指标的评估结果信息
         */
        struct EvaluationData
        {
            std::unordered_map<std::string, std::vector<double>> eval_commit_data; // <eval_commit, values>

            // 多类型Note数据：<eval_commit, <note_type, note_values>>
            std::unordered_map<std::string, std::unordered_map<std::string, std::vector<std::string>>> note_data;

            // 清空评估信息
            void Clear()
            {
                eval_commit_data.clear();
                note_data.clear();
            }

            // 添加评估结果（单个note，兼容旧版本）
            void AddResult(const std::string &eval_commit, double value, const std::string &note = "")
            {
                eval_commit_data[eval_commit].emplace_back(value);
                if (!note.empty())
                {
                    note_data[eval_commit]["default"].emplace_back(note);
                }
            }

            // 添加评估结果（多类型note，从EvaluatorStatus批量添加）
            void AddResult(const std::string &eval_commit, double value,
                           const std::unordered_map<std::string, std::vector<std::string>> &eval_status_note_data,
                           size_t result_index)
            {
                eval_commit_data[eval_commit].emplace_back(value);

                // 从EvaluatorStatus的note_data中获取对应索引的note值
                for (const auto &[note_type, note_values] : eval_status_note_data)
                {
                    if (!note_values.empty())
                    {
                        if (note_values.size() == 1)
                        {
                            // 所有结果共享同一个note值
                            note_data[eval_commit][note_type].emplace_back(note_values[0]);
                        }
                        else if (result_index < note_values.size())
                        {
                            // 一对一对应的note值
                            note_data[eval_commit][note_type].emplace_back(note_values[result_index]);
                        }
                        else
                        {
                            // 索引超出范围，使用空字符串
                            note_data[eval_commit][note_type].emplace_back("");
                        }
                    }
                }
            }

            // 添加评估结果（兼容旧版本的多类型note）
            void AddResult(const std::string &eval_commit, double value,
                           const std::unordered_map<std::string, std::string> &notes)
            {
                eval_commit_data[eval_commit].emplace_back(value);
                for (const auto &[note_type, note_value] : notes)
                {
                    note_data[eval_commit][note_type].emplace_back(note_value);
                }
            }

            // 获取统计信息
            struct Statistics
            {
                double mean = 0.0;
                double median = 0.0;
                double max = 0.0;
                double min = 0.0;
                double std_dev = 0.0;
                size_t count = 0;
            };

            Statistics GetStatistics(const std::string &eval_commit) const
            {
                Statistics stats;
                auto it = eval_commit_data.find(eval_commit);
                if (it == eval_commit_data.end() || it->second.empty())
                {
                    return stats;
                }

                const auto &values = it->second;
                stats.count = values.size();

                // 计算基本统计量
                double sum = std::accumulate(values.begin(), values.end(), 0.0);
                stats.mean = sum / values.size();

                stats.min = *std::min_element(values.begin(), values.end());
                stats.max = *std::max_element(values.begin(), values.end());

                // 计算中位数
                std::vector<double> sorted_values = values;
                std::sort(sorted_values.begin(), sorted_values.end());
                size_t n = sorted_values.size();
                if (n % 2 == 0)
                {
                    stats.median = (sorted_values[n / 2 - 1] + sorted_values[n / 2]) / 2.0;
                }
                else
                {
                    stats.median = sorted_values[n / 2];
                }

                // 计算标准差
                double variance = 0.0;
                for (double value : values)
                {
                    variance += (value - stats.mean) * (value - stats.mean);
                }
                stats.std_dev = std::sqrt(variance / values.size());

                return stats;
            }

            // 获取所有eval_commit名称
            std::vector<std::string> GetAllEvalCommits() const
            {
                std::vector<std::string> commits;
                for (const auto &[eval_commit, values] : eval_commit_data)
                {
                    if (!values.empty())
                    {
                        commits.push_back(eval_commit);
                    }
                }
                return commits;
            }

            // 获取所有note类型
            std::vector<std::string> GetAllNoteTypes() const
            {
                std::set<std::string> note_types_set;
                for (const auto &[eval_commit, note_types_map] : note_data)
                {
                    for (const auto &[note_type, note_values] : note_types_map)
                    {
                        if (!note_values.empty())
                        {
                            note_types_set.insert(note_type);
                        }
                    }
                }
                return std::vector<std::string>(note_types_set.begin(), note_types_set.end());
            }

            // 获取指定eval_commit和note_type的note值
            std::vector<std::string> GetNotes(const std::string &eval_commit, const std::string &note_type = "default") const
            {
                auto commit_it = note_data.find(eval_commit);
                if (commit_it != note_data.end())
                {
                    auto type_it = commit_it->second.find(note_type);
                    if (type_it != commit_it->second.end())
                    {
                        return type_it->second;
                    }
                }
                return std::vector<std::string>();
            }
        };

        // 新的全局评估器类型定义
        // 键: (EvalType, AlgorithmName, MetricName)
        using EvaluationKey = std::tuple<std::string, std::string, std::string>;
        using EvaluationDataPtr = std::shared_ptr<EvaluationData>;

        // 为tuple提供hash函数
        struct EvaluationKeyHash
        {
            std::size_t operator()(const EvaluationKey &k) const
            {
                auto h1 = std::hash<std::string>{}(std::get<0>(k));
                auto h2 = std::hash<std::string>{}(std::get<1>(k));
                auto h3 = std::hash<std::string>{}(std::get<2>(k));
                return h1 ^ (h2 << 1) ^ (h3 << 2);
            }
        };

        using GlobalEvaluator = std::unordered_map<EvaluationKey, EvaluationDataPtr, EvaluationKeyHash>;

        /**
         * @brief 智能解析EvalCommit字符串的结构体
         *
         * 使用boost::regex提取前缀和数值，支持多种格式：
         * - 标准格式: "prefix_number" (如: points_100, noise_0.01)
         * - 多下划线格式: "prefix1_prefix2_number" (如: outlier_ratio_0.15)
         * - 含单位格式: "prefix_numberunit" (如: time_100ms, size_50kb)
         */
        struct ParsedEvalCommit
        {
            std::string prefix;     // 提取的前缀部分
            double value;           // 提取的数值部分
            std::string original;   // 原始字符串
            bool has_numeric_value; // 是否包含有效数值

            /**
             * @brief 构造函数，自动解析EvalCommit字符串
             * @param eval_commit 要解析的EvalCommit字符串
             */
            ParsedEvalCommit(const std::string &eval_commit);

            /**
             * @brief 获取显示名称（用于CSV输出）
             * @return 有数值时返回前缀，无数值时返回原始字符串
             */
            std::string GetDisplayName() const
            {
                return has_numeric_value ? prefix : original;
            }

            /**
             * @brief 排序操作符（有数值时按数值排序，无数值时按字符串排序）
             */
            bool operator<(const ParsedEvalCommit &other) const;
        };

        /**
         * @brief 全局评估器管理类
         *
         * 提供全局评估数据的管理功能
         */
        class EvaluatorManager
        {
        public:
            /**
             * @brief 设置conda环境名称（用于Python绘图）
             * @param conda_env_name conda环境名称，如果为空则使用系统默认Python
             */
            static void SetCondaEnv(const std::string &conda_env_name);

            /**
             * @brief 获取当前设置的conda环境名称
             * @return conda环境名称
             */
            static std::string GetCondaEnv();

            /**
             * @brief 清空conda环境设置，使用系统默认Python
             */
            static void ClearCondaEnv();

            /**
             * @brief 设置CSV导出数值精度
             * @param precision 精度位数，例如6表示保留到1e-6，小于该精度的数值将被置为0
             */
            static void SetCSVPrecision(int precision);

            /**
             * @brief 获取当前CSV导出数值精度
             * @return 当前精度位数
             */
            static int GetCSVPrecision();

            /**
             * @brief 重置CSV精度为默认值(6)
             */
            static void ResetCSVPrecision();

            /**
             * @brief 获取全局评估器实例
             */
            static GlobalEvaluator &GetGlobalEvaluator();

            /**
             * @brief 获取或创建指定类型的评估器
             * @param eval_type 评估类型（如"RelativePoses"）
             * @param algorithm_name 算法名称（如"method_LiRP"）
             * @param metric_name 指标名称（如"rotation_error"）
             * @return 评估器指针
             */
            static EvaluationDataPtr GetOrCreateEvaluator(const std::string &eval_type,
                                                          const std::string &algorithm_name,
                                                          const std::string &metric_name);

            /**
             * @brief 清空指定类型的评估内容
             * @param eval_type 评估类型
             * @return 是否成功
             */
            static bool ClearEvaluator(const std::string &eval_type);

            /**
             * @brief 清空指定算法的评估内容
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @return 是否成功
             */
            static bool ClearAlgorithm(const std::string &eval_type, const std::string &algorithm_name);

            /**
             * @brief 清空所有评估内容
             */
            static void ClearAllEvaluators();

            /**
             * @brief 添加评估结果到指定类型（单一note）
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @param eval_commit 评估提交信息（控制变量）
             * @param metric_name 指标名称
             * @param value 评估值
             * @param note 备注信息（可选）
             * @return 是否成功
             */
            static bool AddEvaluationResult(const std::string &eval_type,
                                            const std::string &algorithm_name,
                                            const std::string &eval_commit,
                                            const std::string &metric_name,
                                            double value,
                                            const std::string &note = "");

            /**
             * @brief 添加评估结果到指定类型（多类型note）
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @param eval_commit 评估提交信息（控制变量）
             * @param metric_name 指标名称
             * @param value 评估值
             * @param notes 多类型备注信息
             * @return 是否成功
             */
            static bool AddEvaluationResult(const std::string &eval_type,
                                            const std::string &algorithm_name,
                                            const std::string &eval_commit,
                                            const std::string &metric_name,
                                            double value,
                                            const std::unordered_map<std::string, std::string> &notes);

            /**
             * @brief 从EvaluatorStatus批量添加评估结果（支持新的note数据结构）
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @param eval_commit 评估提交信息（控制变量）
             * @param eval_status 评估状态对象
             * @return 是否成功
             */
            static bool AddEvaluationResultsFromStatus(const std::string &eval_type,
                                                       const std::string &algorithm_name,
                                                       const std::string &eval_commit,
                                                       const EvaluatorStatus &eval_status);

            /**
             * @brief 获取指定指标的统计信息
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @param metric_name 指标名称
             * @param eval_commit 评估提交信息
             * @return 统计信息
             */
            static EvaluationData::Statistics GetStatistics(const std::string &eval_type,
                                                            const std::string &algorithm_name,
                                                            const std::string &metric_name,
                                                            const std::string &eval_commit);

            /**
             * @brief 输出指定类型的评估结果到CSV文件（算法对比格式）
             * @param eval_type 评估类型
             * @param metric_name 指标名称
             * @param output_path 输出文件路径
             * @param stat_type 统计类型: "mean", "max", "min", "median", "std_dev"
             * @return 是否成功
             */
            static bool ExportAlgorithmComparisonToCSV(const std::string &eval_type,
                                                       const std::string &metric_name,
                                                       const std::filesystem::path &output_path,
                                                       const std::string &stat_type = "mean");

            /**
             * @brief 输出指定类型下所有指标的对比到CSV文件
             * @param eval_type 评估类型
             * @param output_dir 输出目录
             * @return 是否成功
             */
            static bool ExportAllMetricsToCSV(const std::string &eval_type,
                                              const std::filesystem::path &output_dir);

            /**
             * @brief 输出某个指标的所有统计类型到CSV文件（统一智能解析格式）
             * @param eval_type 评估类型
             * @param metric_name 指标名称
             * @param output_path 输出文件路径
             * @return 是否成功
             */
            static bool ExportMetricAllStatsToCSV(const std::string &eval_type,
                                                  const std::string &metric_name,
                                                  const std::filesystem::path &output_path);

            /**
             * @brief 输出详细统计结果到CSV文件
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @param output_path 输出文件路径
             * @return 是否成功
             */
            static bool ExportDetailedStatsToCSV(const std::string &eval_type,
                                                 const std::string &algorithm_name,
                                                 const std::filesystem::path &output_path);

            /**
             * @brief 输出指定类型下所有指标的原始评估值到CSV文件
             * @details 为每个metric生成一个{metric_name}_all_evaluated_values.csv文件
             * @param eval_type 评估类型
             * @param output_dir 输出目录
             * @param note_types 要导出的note类型，用"|"分隔，如"view_pairs|match_num"，空字符串表示不导出note
             * @return 是否成功
             */
            static bool ExportAllRawValuesToCSV(const std::string &eval_type,
                                                const std::filesystem::path &output_dir,
                                                const std::string &note_types = "");

            /**
             * @brief 获取指定类型下的所有算法名称
             * @param eval_type 评估类型
             * @return 算法名称列表
             */
            static std::vector<std::string> GetAllAlgorithms(const std::string &eval_type);

            /**
             * @brief 获取指定类型和算法下的所有指标名称
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @return 指标名称列表
             */
            static std::vector<std::string> GetAllMetrics(const std::string &eval_type,
                                                          const std::string &algorithm_name);

            /**
             * @brief 获取指定指标下的所有评估提交名称
             * @param eval_type 评估类型
             * @param algorithm_name 算法名称
             * @param metric_name 指标名称
             * @return 评估提交名称列表
             */
            static std::vector<std::string> GetAllEvalCommits(const std::string &eval_type,
                                                              const std::string &algorithm_name,
                                                              const std::string &metric_name);

            /**
             * @brief 打印指定类型的评估报告
             * @param eval_type 评估类型
             */
            static void PrintEvaluationReport(const std::string &eval_type);

            /**
             * @brief 打印算法对比报告
             * @param eval_type 评估类型
             * @param metric_name 指标名称
             */
            static void PrintAlgorithmComparison(const std::string &eval_type, const std::string &metric_name);

            /**
             * @brief 打印所有类型的评估报告
             */
            static void PrintAllEvaluationReports();

            /**
             * @brief 获取当前所有评估类型列表
             */
            static std::vector<std::string> GetAllEvaluationTypes();

            /**
             * @brief 打印指定算法在特定评估类型中的最新评估结果
             * @param eval_type 评估类型（如"RelativePoses"）
             * @param algorithm_name 算法名称
             * @param note_types 要显示的note类型，用"|"分隔（如"view_pairs|match_num"），空字符串表示不显示note
             * @details 显示每个指标的最新评估值，格式为: metric1=value, metric2=value, note1=value, note2=value
             */
            static void PrintLatestEvaluationResults(const std::string &eval_type,
                                                     const std::string &algorithm_name,
                                                     const std::string &note_types = "");

            /**
             * @brief 生成详细统计的CSV行格式
             */
            static std::string FormatDetailedStatsToCSVRow(const std::string &metric_name,
                                                           const std::string &eval_commit,
                                                           const EvaluationData::Statistics &stats);

            /**
             * @brief 生成包含智能解析信息的详细统计CSV行格式
             */
            static std::string FormatDetailedStatsWithParsingToCSVRow(const std::string &metric_name,
                                                                      const ParsedEvalCommit &parsed_commit,
                                                                      const EvaluationData::Statistics &stats);

            /**
             * @brief 生成智能表格格式的CSV（前缀作为列名）
             */
            static void GenerateSmartCSVTable(std::ofstream &csv_file,
                                              const std::string &eval_type,
                                              const std::string &metric_name,
                                              const std::string &stat_type,
                                              const std::vector<std::string> &algorithms,
                                              const std::map<std::string, std::vector<ParsedEvalCommit>> &grouped_commits,
                                              GlobalEvaluator &global_evaluator);

            /**
             * @brief 生成传统表格格式的CSV（每个EvalCommit作为一列）
             */
            static void GenerateTraditionalCSVTable(std::ofstream &csv_file,
                                                    const std::string &eval_type,
                                                    const std::string &metric_name,
                                                    const std::string &stat_type,
                                                    const std::vector<std::string> &algorithms,
                                                    const std::vector<ParsedEvalCommit> &all_parsed_commits,
                                                    GlobalEvaluator &global_evaluator);

            /**
             * @brief 从统计数据中获取指定类型的值
             */
            static double GetStatValue(const EvaluationData::Statistics &stats, const std::string &stat_type);

            /**
             * @brief 判断某个值是否比另一个值更好（根据指标类型决定）
             */
            static bool IsBetterValue(double new_value, double current_value, const std::string &metric_name);

            /**
             * @brief 生成统一的所有统计类型表格（智能解析EvalCommit排序）
             */
            static void GenerateUnifiedAllStatsTable(std::ofstream &csv_file,
                                                     const std::string &eval_type,
                                                     const std::string &metric_name,
                                                     const std::vector<std::string> &algorithms,
                                                     const std::vector<ParsedEvalCommit> &all_parsed_commits,
                                                     GlobalEvaluator &global_evaluator);

            /**
             * @brief 生成指标的可视化图表（基于CSV数据）
             * @param eval_type 评估类型
             * @param metric_name 指标名称
             * @param csv_file_path CSV文件路径
             * @param output_dir 图表输出目录
             * @param stat_types 要绘制的统计类型列表，如{"mean", "median"}
             * @param style_file 样式文件路径（可选，用于算法样式批处理）
             * @return 是否成功
             */
            static bool GenerateMetricVisualization(const std::string &eval_type,
                                                    const std::string &metric_name,
                                                    const std::filesystem::path &csv_file_path,
                                                    const std::filesystem::path &output_dir,
                                                    const std::vector<std::string> &stat_types = {"mean", "median"},
                                                    const std::string &style_file = "");

            /**
             * @brief 为所有指标生成可视化图表
             * @param eval_type 评估类型
             * @param csv_output_dir CSV文件所在目录
             * @param plot_output_dir 图表输出目录
             * @param stat_types 要绘制的统计类型列表
             * @param style_file 样式文件路径（可选，用于算法样式批处理）
             * @return 是否成功
             */
            static bool GenerateAllMetricsVisualization(const std::string &eval_type,
                                                        const std::filesystem::path &csv_output_dir,
                                                        const std::filesystem::path &plot_output_dir,
                                                        const std::vector<std::string> &stat_types = {"mean", "median"},
                                                        const std::string &style_file = "");

            /**
             * @brief 导出时间统计表格到CSV文件
             * @param eval_type 评估类型
             * @param output_path 输出文件路径
             * @return 是否成功
             * @details 检查是否有time note，如果有则生成时间统计表格
             *          表格格式：Algorithm	EvalCommit(智能解析后)	Mean	Median	Min	Max
             */
            static bool ExportTimeStatisticsToCSV(const std::string &eval_type,
                                                  const std::filesystem::path &output_path);

            /**
             * @brief 生成时间统计可视化图表
             * @param eval_type 评估类型
             * @param csv_output_dir CSV文件所在目录
             * @param plot_output_dir 图表输出目录
             * @param stat_types 要绘制的统计类型列表
             * @return 是否成功
             */
            static bool GenerateTimeStatisticsVisualization(const std::string &eval_type,
                                                            const std::filesystem::path &csv_output_dir,
                                                            const std::filesystem::path &plot_output_dir,
                                                            const std::vector<std::string> &stat_types = {"Mean", "Median"});

            /**
             * @brief 导出时间统计并生成可视化图表
             * @param eval_type 评估类型
             * @param output_dir 输出目录
             * @param stat_types 要绘制的统计类型列表
             * @return 是否成功
             */
            static bool ExportTimeStatisticsWithVisualization(const std::string &eval_type,
                                                              const std::filesystem::path &output_dir,
                                                              const std::vector<std::string> &stat_types = {"Mean", "Median"});

            /**
             * @brief 导出CSV并自动生成可视化图表
             * @param eval_type 评估类型
             * @param metric_name 指标名称
             * @param output_dir 输出目录（同时用于CSV和图表）
             * @param stat_types 要绘制的统计类型列表
             * @param style_file 样式文件路径（可选，用于算法样式批处理）
             * @return 是否成功
             */
            static bool ExportMetricWithVisualization(const std::string &eval_type,
                                                      const std::string &metric_name,
                                                      const std::filesystem::path &output_dir,
                                                      const std::vector<std::string> &stat_types = {"mean", "median"},
                                                      const std::string &style_file = "");

        private:
            // 禁用构造函数，使用静态方法
            EvaluatorManager() = delete;

            /**
             * @brief conda环境名称（用于Python绘图）
             */
            static std::string conda_env_;

            /**
             * @brief CSV导出数值精度位数
             */
            static int csv_precision_;

            /**
             * @brief 格式化数值为字符串，应用精度控制
             * @param value 要格式化的数值
             * @return 格式化后的字符串
             */
            static std::string FormatNumber(double value);

            /**
             * @brief 生成算法对比格式的CSV表头
             * @param eval_commits 评估提交名称列表
             */
            static std::string GenerateAlgorithmComparisonCSVHeader(const std::vector<std::string> &eval_commits);

            /**
             * @brief 生成详细统计格式的CSV表头
             */
            static std::string GenerateDetailedStatsCSVHeader();

            /**
             * @brief 格式化算法对比数据为CSV行
             * @param algorithm_name 算法名称
             * @param eval_commits 评估提交名称列表
             * @param evaluation_data 评估数据
             * @param stat_type 统计类型
             */
            static std::string FormatAlgorithmComparisonToCSVRow(const std::string &algorithm_name,
                                                                 const std::vector<std::string> &eval_commits,
                                                                 const EvaluationDataPtr &evaluation_data,
                                                                 const std::string &stat_type);

            /**
             * @brief 调用Python脚本生成图表
             * @param python_script_path Python脚本路径
             * @param csv_file_path CSV文件路径
             * @param output_path 图表输出路径
             * @param additional_args 额外参数
             * @return 是否成功
             */
            static bool CallPythonPlotScript(const std::filesystem::path &python_script_path,
                                             const std::filesystem::path &csv_file_path,
                                             const std::filesystem::path &output_path,
                                             const std::vector<std::string> &additional_args = {});
        };

    } // namespace Interface
} // namespace PoSDK

#endif // _POMVG_EVALUATOR_HPP_
