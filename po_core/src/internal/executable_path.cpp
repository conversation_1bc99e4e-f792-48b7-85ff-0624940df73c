#include "executable_path.hpp"

#include <iostream>
#include <stdexcept>

// 平台特定的头文件
#ifdef _WIN32
#include <windows.h>
#elif defined(__APPLE__)
#include <mach-o/dyld.h>
#include <climits>
#else
#include <unistd.h>
#include <climits>
#endif

namespace PoSDK
{

    std::filesystem::path ExecutablePath::GetExecutablePathImpl()
    {
        try
        {
#ifdef _WIN32
            char path_buffer[MAX_PATH] = {0};
            DWORD result = GetModuleFileNameA(NULL, path_buffer, MAX_PATH);
            if (result == 0 || result == MAX_PATH)
            {
                std::cerr << "[ExecutablePath] Failed to get executable path on Windows" << std::endl;
                return std::filesystem::path();
            }
            return std::filesystem::path(path_buffer);

#elif defined(__APPLE__)
            char path_buffer[PATH_MAX];
            uint32_t size = sizeof(path_buffer);
            if (_NSGetExecutablePath(path_buffer, &size) != 0)
            {
                std::cerr << "[ExecutablePath] Failed to get executable path on macOS" << std::endl;
                return std::filesystem::path();
            }
            return std::filesystem::path(path_buffer);

#else // Linux
            char path_buffer[PATH_MAX];
            ssize_t count = readlink("/proc/self/exe", path_buffer, PATH_MAX);
            if (count == -1)
            {
                std::cerr << "[ExecutablePath] Failed to get executable path on Linux" << std::endl;
                return std::filesystem::path();
            }
            path_buffer[count] = '\0';
            return std::filesystem::path(path_buffer);
#endif
        }
        catch (const std::exception &e)
        {
            std::cerr << "[ExecutablePath] Exception in GetExecutablePathImpl: " << e.what() << std::endl;
            return std::filesystem::path();
        }
    }

    std::string ExecutablePath::GetExecutableFilePath()
    {
        std::filesystem::path exe_path = GetExecutablePathImpl();
        if (exe_path.empty())
        {
            return "";
        }

        try
        {
            return exe_path.string();
        }
        catch (const std::exception &e)
        {
            std::cerr << "[ExecutablePath] Failed to convert path to string: " << e.what() << std::endl;
            return "";
        }
    }

    std::string ExecutablePath::GetExecutableDirectory()
    {
        std::filesystem::path exe_path = GetExecutablePathImpl();
        if (exe_path.empty())
        {
            return "";
        }

        try
        {
            return exe_path.parent_path().string();
        }
        catch (const std::exception &e)
        {
            std::cerr << "[ExecutablePath] Failed to get executable directory: " << e.what() << std::endl;
            return "";
        }
    }

    std::string ExecutablePath::GetRelativeToExecutable(const std::string &relative_path)
    {
        if (relative_path.empty())
        {
            return "";
        }

        std::filesystem::path exe_path = GetExecutablePathImpl();
        if (exe_path.empty())
        {
            return "";
        }

        try
        {
            // 构建相对于可执行文件目录的路径
            std::filesystem::path result_path = exe_path.parent_path() / relative_path;

            // 规范化路径（解析 .. 和 . 等）
            result_path = result_path.lexically_normal();

            return result_path.string();
        }
        catch (const std::exception &e)
        {
            std::cerr << "[ExecutablePath] Failed to resolve relative path '"
                      << relative_path << "': " << e.what() << std::endl;
            return "";
        }
    }

    bool ExecutablePath::ExistsRelativeToExecutable(const std::string &relative_path)
    {
        std::string absolute_path = GetRelativeToExecutable(relative_path);
        if (absolute_path.empty())
        {
            return false;
        }

        try
        {
            return std::filesystem::exists(absolute_path);
        }
        catch (const std::exception &e)
        {
            std::cerr << "[ExecutablePath] Failed to check existence of '"
                      << absolute_path << "': " << e.what() << std::endl;
            return false;
        }
    }

} // namespace PoSDK