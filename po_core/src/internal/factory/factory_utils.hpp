#pragma once
#include <iostream>
#include <regex>
#include <string>
#include "pomvg_plugin.hpp" // 包含 PluginManager, GetPlugin 等函数
#include "interfaces.hpp"   // 包含 DataPtr / MethodPtr / BehaviorPtr 等
#include "types.hpp"        // MapPlugin typedef

/**
 * @file factory_utils.hpp
 * @brief 工厂类的工具函数实现
 * @details 本文件提供了插件管理和对象创建的底层实现,主要包括:
 *          - 插件加载与管理功能
 *          - 基于插件的对象创建功能
 *          这些功能被 FactoryData/FactoryMethod/FactoryBehavior 等工厂类调用
 */

// 仅供 factory.cpp 内部使用的命名空间
namespace PoSDK
{
    namespace factory_utils
    {

        /**
         * @brief 加载并管理插件
         * @param pluginPath 插件目录路径
         * @param regexPattern 用于匹配插件文件名的正则表达式
         * @param mapPlugin 存储加载的插件的映射表
         * @details 该函数会:
         *          1. 检查插件是否已加载
         *          2. 遍历指定目录下的插件文件
         *          3. 根据正则表达式匹配插件文件
         *          4. 将匹配的插件加载到内存并存储在映射表中
         */
        inline void ManagePluginsImpl(const std::string &pluginPath,
                                      const std::string &regexPattern,
                                      MapPlugin &mapPlugin)
        {
            if (!mapPlugin.empty())
            {
                std::cout << "##warning: map_plugin already built.\n";
                return;
            }
            std::cout << "<ManagePluginsImpl> loading path=" << pluginPath
                      << ", pattern=" << regexPattern << std::endl;
            PluginManager(pluginPath, regexPattern, mapPlugin);
        }

        /**
         * @brief 通过插件创建对象 (DataIO/Method/Behavior)
         * @tparam T 要创建的对象类型
         * @param mapPlugin 存储插件的映射表
         * @param userType 用户指定的类型名称
         * @param loggerName 用于日志输出的组件名称
         * @return std::shared_ptr<T> 创建的对象指针,创建失败返回nullptr
         * @details 该函数会:
         *          1. 根据用户指定的类型名在插件映射表中查找
         *          2. 如果找到插件,将其转换为指定类型的对象
         *          3. 进行类型安全检查
         *          4. 返回创建的对象指针或nullptr
         */
        template <typename T>
        inline std::shared_ptr<T> CreateFromPluginImpl(MapPlugin &mapPlugin,
                                                       const std::string &userType,
                                                       const std::string &loggerName)
        {
            std::cout << "<" << loggerName << "::CreateFromPlugin> type=" << userType << std::endl;

            VoidPtr void_ptr;
            if (GetPlugin(mapPlugin, userType, void_ptr))
            {
                auto typed_ptr = std::static_pointer_cast<T>(void_ptr);
                if (!typed_ptr)
                {
                    std::cerr << "##warning: cast to " << loggerName
                              << " failed for type=" << userType << std::endl;
                }
                return typed_ptr;
            }
            else
            {
                std::cerr << "##warning: plugin not found for type=" << userType << std::endl;
                return nullptr;
            }
        }

        template <typename T>
        bool ValidateType(const std::string &expectedType,
                          const std::string &userType,
                          const std::string &loggerName)
        {
            // ...
        }

    } // namespace factory_utils
} // namespace PoSDK
