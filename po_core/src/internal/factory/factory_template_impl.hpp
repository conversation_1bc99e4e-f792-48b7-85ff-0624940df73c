#ifndef _POMVG_FACTORY_TEMPLATE_IMPL_
#define _POMVG_FACTORY_TEMPLATE_IMPL_

#include "factory.hpp"
#include "interfaces_robust_estimator.hpp"
#include <algorithm>
#include <memory>

namespace PoSDK
{
    // =============================================================================
    // 模板函数实现 - 包含完整的类定义
    // =============================================================================

    template <typename TSample>
    Interface::MethodPtr FactoryMethod::Create(const std::string &type)
    {
        // 将输入类型转换为小写
        std::string lowercase_type = type;
        std::transform(lowercase_type.begin(), lowercase_type.end(),
                       lowercase_type.begin(), ::tolower);

        // 检查是否是支持的模板类型
        if (lowercase_type == "ransac_estimator")
        {
            return std::make_shared<Interface::RANSACEstimator<TSample>>();
        }
        else if (lowercase_type == "gnc_irls_estimator" || lowercase_type == "gncirls_estimator")
        {
            return std::make_shared<Interface::GNCIRLSEstimator<TSample>>();
        }

        // 如果不是模板类型，回退到普通的Create方法
        return Create(type);
    }

} // namespace PoSDK

#endif // _POMVG_FACTORY_TEMPLATE_IMPL_