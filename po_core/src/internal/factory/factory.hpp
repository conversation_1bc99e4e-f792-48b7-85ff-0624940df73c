#ifndef _POMVG_FACTORY_
#define _POMVG_FACTORY_

#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include "interfaces.hpp"
#include "pomvg_plugin.hpp"
#include "types.hpp"

using namespace PoSDK::Plugin;

//==================== Factory Log Macro section ======================
// 工厂专用日志宏定义 - 简化版本，参考 interfaces.hpp 中的 LOG 方式
//=======================================================================

#ifdef _DEBUG
#define FACTORY_LOG(factory_class, level)   \
    if (factory_class::log_level_ >= level) \
    std::cout << "[PoSDK | " << #factory_class << "] >>> " << PoSDK::Interface::FileNameOnly(__FILE__) << " line" << std::to_string(__LINE__) << ": "
#else
#define FACTORY_LOG(factory_class, level)   \
    if (factory_class::log_level_ >= level) \
    std::cout << "[PoSDK | " << #factory_class << "] >>> "
#endif

// 工厂错误日志宏（始终输出）
#define FACTORY_LOG_ERR(factory_class) \
    std::cerr << "\033[31m[PoSDK | " << #factory_class << "] ERROR >>> \033[0m"

namespace PoSDK
{
    using namespace types;

    class FactoryBehavior
    {
    public:
        FactoryBehavior(const std::string &plugin_path);
        FactoryBehavior() {};

        static Interface::BehaviorPtr Create(const std::string &type);
        static Interface::BehaviorPtr CreateBehaviorFromPlugin(const std::string &type);

        /// @brief 手动管理插件目录（支持动态重新指定）
        /// @details 允许运行时动态指定新的插件目录，会清空现有插件并重新加载
        /// @note 该函数与懒加载机制独立，可以覆盖默认插件路径
        /// @param plugin_path 新的插件目录路径
        static void ManagePlugins(const std::string &plugin_path);

        // 显示所有可用类型（内置+插件）
        static std::vector<std::string> DispAllTypes();
        // 仅显示来自插件的类型
        static std::vector<std::string> DispPluginTypes();

    public:
        static MapPlugin map_plugin_;
        static std::string regex_pattern;
        static int log_level_; ///< 日志等级（内部管理，不暴露接口）
    };

    class FactoryMethod
    {
    public:
        FactoryMethod(const std::string &plugin_path);
        FactoryMethod() {};

        // 原有的非模板接口（保持向后兼容）
        static Interface::MethodPtr Create(const std::string &type);
        static Interface::MethodPtr CreateMethodFromPlugin(const std::string &type);

        /// @brief 模板化创建接口 - 支持带模板参数的方法创建
        /// @tparam TSample 样本数据类型
        /// @param type 方法类型名称
        /// @return 创建的方法指针，失败返回nullptr
        /// @details 支持创建模板化的方法类型，如RANSACEstimator<TSample>、GNCIRLSEstimator<TSample>等
        /// @example auto ransac = FactoryMethod::Create<std::vector<Eigen::Vector2d>>("ransac_estimator");
        template <typename TSample>
        static Interface::MethodPtr Create(const std::string &type);

        /// @brief 手动管理插件目录（支持动态重新指定）
        /// @details 允许运行时动态指定新的插件目录，会清空现有插件并重新加载
        /// @note 该函数与懒加载机制独立，可以覆盖默认插件路径
        /// @param plugin_path 新的插件目录路径
        static void ManagePlugins(const std::string &plugin_path);

        // 显示所有可用类型（内置+插件）
        static std::vector<std::string> DispAllTypes();
        // 仅显示来自插件的类型
        static std::vector<std::string> DispPluginTypes();

    public:
        static MapPlugin map_plugin_;
        static std::string regex_pattern;
        static int log_level_; ///< 日志等级（内部管理，不暴露接口）
    };

    class FactoryData
    {
    public:
        FactoryData(const std::string &plugin_path);
        FactoryData() {};
        static Interface::DataPtr Create(const std::string &type);
        static Interface::DataPtr CreateDataFromPlugin(const std::string &type);

        /// @brief 手动管理插件目录（支持动态重新指定）
        /// @details 允许运行时动态指定新的插件目录，会清空现有插件并重新加载
        /// @note 该函数与懒加载机制独立，可以覆盖默认插件路径
        /// @param plugin_path 新的插件目录路径
        static void ManagePlugins(const std::string &plugin_path);

        // 显示所有可用类型（内置+插件）
        static std::vector<std::string> DispAllTypes();
        // 仅显示来自插件的类型
        static std::vector<std::string> DispPluginTypes();

    public:
        static MapPlugin map_plugin_;
        static std::string regex_pattern;
        static int log_level_; ///< 日志等级（内部管理，不暴露接口）
    };

    // =============================================================================
    // 模板函数声明 - 实现在单独的头文件中
    // =============================================================================

    // 注意：模板函数的实现在 factory_template_impl.hpp 中
    // 用户需要包含该文件才能使用模板功能：
    // #include "factory_template_impl.hpp"

}

#endif
