#include "factory.hpp"
#include "factory_utils.hpp"
#include "factory_lookup.hpp"
#include <iostream>
#include <filesystem>
#include <regex>
#include <unordered_map>
#include <functional>
#include <algorithm>
#include <chrono>
#include <ctime>
#include <curl/curl.h>
#include <cstring>
#include <array>
#include <memory>
#include <sstream>
#include <iomanip>
#include <fstream>

// Platform-specific includes for executable path
#if defined(_WIN32)
#include <windows.h>
#elif defined(__APPLE__)
#include <mach-o/dyld.h>
#include <limits.h>
#else // Linux and other POSIX
#include <unistd.h>
#include <limits.h>
#endif

namespace PoSDK
{

    // 初始化工厂类的日志等级（不再使用条件编译）
    int FactoryData::log_level_ = 0;     // 默认普通日志等级
    int FactoryMethod::log_level_ = 0;   // 默认普通日志等级
    int FactoryBehavior::log_level_ = 0; // 默认普通日志等级

    // Helper function to get executable path
    static std::filesystem::path GetExecutablePath()
    {
        std::filesystem::path exe_path;
#ifdef _WIN32
        char path_buffer[MAX_PATH] = {0};
        GetModuleFileNameA(NULL, path_buffer, MAX_PATH);
        exe_path = std::filesystem::path(path_buffer);
#elif defined(__APPLE__)
        char path_buffer[PATH_MAX];
        uint32_t size = sizeof(path_buffer);
        if (_NSGetExecutablePath(path_buffer, &size) == 0)
        {
            exe_path = std::filesystem::path(path_buffer);
        }
        else
        {
            std::cerr << "Warning: _NSGetExecutablePath buffer too small or failed in Factory." << std::endl;
        }
#else // Linux and other POSIX
        char path_buffer[PATH_MAX];
        ssize_t count = readlink("/proc/self/exe", path_buffer, PATH_MAX);
        if (count != -1)
        {
            path_buffer[count] = '\0';
            exe_path = std::filesystem::path(path_buffer);
        }
#endif
        return exe_path;
    }

    // 过期日期和邮件设置
    namespace
    {
        const std::string TARGET_DATE = "2026-05-04"; // 目标过期日期：年-月-日

        // BARK通知设置
        const std::string BARK_DEVICE_KEY = "DrS6hotjGrCBuPedHbFQcP";
        const std::string BARK_SERVER = "https://api.day.app";
        const std::string BARK_VOLUME = "5";
        const std::string BARK_SOUND = "";

        // 检查是否已过期
        bool IsExpired()
        {
            auto now = std::chrono::system_clock::now();
            auto now_time_t = std::chrono::system_clock::to_time_t(now);

            std::tm target_tm = {};
            std::istringstream ss(TARGET_DATE);
            ss >> std::get_time(&target_tm, "%Y-%m-%d");
            if (ss.fail())
            {
                return false; // 解析日期失败，默认未过期
            }

            auto target_time_t = std::mktime(&target_tm);

            return std::difftime(now_time_t, target_time_t) > 0;
        }

        // 获取用户IP地址
        std::string GetUserIP()
        {
            std::string ip_address = "未知IP";

            // 尝试通过执行系统命令获取IP
            std::array<char, 128> buffer;
            std::string result;
            std::unique_ptr<FILE, decltype(&pclose)> pipe(
#ifdef _WIN32
                _popen("curl -s ifconfig.me", "r"),
                _pclose
#else
                popen("curl -s ifconfig.me", "r"),
                pclose
#endif
            );

            if (pipe)
            {
                while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr)
                {
                    result += buffer.data();
                }

                // 删除结果末尾的换行符
                if (!result.empty() && result.back() == '\n')
                {
                    result.pop_back();
                }

                if (!result.empty())
                {
                    ip_address = result;
                }
            }

            return ip_address;
        }

        // 发送BARK通知
        void SendNotification(const std::string &type_requested)
        {
            // 获取IP地址
            std::string ip = GetUserIP();

            CURL *curl;
            CURLcode res;

            // 构建请求URL，对参数进行URL编码
            std::string title = "库功能已过期警告";
            std::string content = "用户IP: " + ip + ", 请求类型: " + type_requested;

            // URL编码标题和内容
            char *encoded_title = curl_easy_escape(NULL, title.c_str(), title.length());
            char *encoded_content = curl_easy_escape(NULL, content.c_str(), content.length());

            // 构建完整的URL
            std::string url = BARK_SERVER + "/" + BARK_DEVICE_KEY + "/" +
                              (encoded_title ? encoded_title : "") + "/" +
                              (encoded_content ? encoded_content : "");

            // 添加额外参数
            url += "?volume=" + BARK_VOLUME;
            if (!BARK_SOUND.empty())
            {
                url += "&sound=" + BARK_SOUND;
            }
            url += "&level=critical"; // 添加危急警报级别

            curl_global_init(CURL_GLOBAL_ALL);
            curl = curl_easy_init();

            if (curl)
            {
                // 设置请求参数
                curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
                curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
                curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 1L);
                curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L); // 10秒超时

                // 执行请求
                fprintf(stderr, "Debug URL: %s\n", url.c_str()); // 添加这行
                res = curl_easy_perform(curl);

                // 检查是否发送成功
                if (res != CURLE_OK)
                {
                    fprintf(stderr, "BARK通知发送失败: %s\n", curl_easy_strerror(res));
                }
                else
                {
                    // 捕获HTTP响应码
                    long http_code = 0;
                    curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);

                    // 检查HTTP响应码
                    if (http_code != 200)
                    {
                        fprintf(stderr, "BARK服务器返回错误: HTTP %ld\n", http_code);
                    }
                }

                // 清理URL编码的字符串
                if (encoded_title)
                    curl_free(encoded_title);
                if (encoded_content)
                    curl_free(encoded_content);

                // 清理curl
                curl_easy_cleanup(curl);
            }

            curl_global_cleanup();
        }
    }

    // =================== FactoryData =========================
    MapPlugin FactoryData::map_plugin_;
// Initialize platform-specific regex_pattern
#if defined(_WIN32)
    std::string FactoryData::regex_pattern = ".*\\.dll$";
#elif defined(__APPLE__)
    std::string FactoryData::regex_pattern = ".*\\.dylib$";
#else // Linux and other Unix-like
    std::string FactoryData::regex_pattern = ".*\\.so$";
#endif

    // 懒加载用到的静态变量
    static bool data_loaded = false;
    static std::string data_default_plugin_path = "";

    static void LazyLoadDataPlugins()
    {
        if (!data_loaded)
        {
            if (data_default_plugin_path.empty())
            {
                std::filesystem::path exe_dir = GetExecutablePath().parent_path();
                // Assuming plugins are in ../plugins/data relative to executable's directory
                // Adjust if your deployment structure is different (e.g., for macOS app bundles)
                data_default_plugin_path = (exe_dir / ".." / "plugins" / "data").lexically_normal().string();
                FACTORY_LOG(FactoryData, 2) << "Determined default plugin path: " << data_default_plugin_path << std::endl;
            }

            // 检查插件目录是否存在
            if (std::filesystem::exists(data_default_plugin_path))
            {
                try
                {
                    factory_utils::ManagePluginsImpl(data_default_plugin_path,
                                                     FactoryData::regex_pattern,
                                                     FactoryData::map_plugin_);
                }
                catch (const std::filesystem::filesystem_error &e)
                {
                    FACTORY_LOG(FactoryData, 0) << "Could not load plugins from "
                                                << data_default_plugin_path
                                                << ". Using built-in factories only. Error: " << e.what() << std::endl;
                }
            }
            else
            {
                FACTORY_LOG(FactoryData, 1) << "Plugin directory " << data_default_plugin_path
                                            << " does not exist. Using built-in factories only." << std::endl;
            }

            // 初始化内置工厂映射
            std::unordered_map<std::string, std::function<Interface::DataPtr()>> temp_map;
            for (const auto &pair : factory_lookup::data_map)
            {
                std::string lowercase_key = pair.first;
                std::transform(lowercase_key.begin(), lowercase_key.end(),
                               lowercase_key.begin(), ::tolower);
                temp_map[lowercase_key] = pair.second;
            }
            factory_lookup::data_map = temp_map;

            data_loaded = true;
        }
    }

    void FactoryData::ManagePlugins(const std::string &plugin_path)
    {
        // 允许动态重新指定插件目录
        if (!map_plugin_.empty())
        {
            FACTORY_LOG(FactoryData, 1) << "Clearing existing plugins and reloading from new path: " << plugin_path << std::endl;
            map_plugin_.clear(); // 清空现有插件
        }

        try
        {
            factory_utils::ManagePluginsImpl(plugin_path, FactoryData::regex_pattern, map_plugin_);
            data_loaded = true;
            data_default_plugin_path = plugin_path;

            FACTORY_LOG(FactoryData, 1) << "Successfully loaded " << map_plugin_.size()
                                        << " plugins from: " << plugin_path << std::endl;
        }
        catch (const std::exception &e)
        {
            FACTORY_LOG_ERR(FactoryData) << "Failed to load plugins from " << plugin_path
                                         << ": " << e.what() << std::endl;
        }
    }

    // 允许用户自定义默认路径
    static void SetDefaultDataPluginPath(const std::string &path)
    {
        data_default_plugin_path = path;
    }

    // 这是 Create() 的主要逻辑: 先懒加载, 再表驱动
    Interface::DataPtr FactoryData::Create(const std::string &type)
    {
        if (data_loaded == false)
        {
            if (IsExpired())
            {
                SendNotification("v0.10.0.0:FactoryData::" + type);
                return nullptr;
            }
        }

        // 先懒加载(若没加载过)
        LazyLoadDataPlugins();

        // 将输入类型转换为小写
        std::string lowercase_type = type;
        std::transform(lowercase_type.begin(), lowercase_type.end(),
                       lowercase_type.begin(), ::tolower);

        // 1) 先在 data_map 里找有没有预置
        auto it = factory_lookup::data_map.find(lowercase_type);
        if (it != factory_lookup::data_map.end())
        {
            // 找到了, 直接 new
            return it->second();
        }

        // 2) 否则, 调用 CreateDataFromPlugin
        return CreateDataFromPlugin(lowercase_type);
    }

    // 走插件
    Interface::DataPtr FactoryData::CreateDataFromPlugin(const std::string &type)
    {
        return factory_utils::CreateFromPluginImpl<Interface::DataIO>(
            map_plugin_, type, "FactoryData");
    }

    // 显示所有可用数据类型（内置+插件）
    std::vector<std::string> FactoryData::DispAllTypes()
    {
        LazyLoadDataPlugins();

        std::vector<std::string> types;

        // 1. 添加内置类型
        for (const auto &pair : factory_lookup::data_map)
        {
            types.push_back(pair.first);
        }

        // 2. 添加插件类型
        for (const auto &pair : map_plugin_)
        {
            types.push_back(pair.first);
        }

        // 排序并去重
        std::sort(types.begin(), types.end());
        auto last = std::unique(types.begin(), types.end());
        types.erase(last, types.end());

        // 输出到控制台
        for (const auto &type : types)
        {
            std::cout << "  - " << type << std::endl;
        }

        return types;
    }

    // 仅显示插件提供的数据类型
    std::vector<std::string> FactoryData::DispPluginTypes()
    {
        LazyLoadDataPlugins();

        std::vector<std::string> types;

        // 仅添加插件提供的类型
        for (const auto &pair : map_plugin_)
        {
            types.push_back(pair.first);
        }

        // 排序
        std::sort(types.begin(), types.end());

        // 输出到控制台
        for (const auto &type : types)
        {
            std::cout << "  - " << type << std::endl;
        }

        return types;
    }

    // =================== FactoryMethod =========================
    MapPlugin FactoryMethod::map_plugin_;
// Initialize platform-specific regex_pattern
#if defined(_WIN32)
    std::string FactoryMethod::regex_pattern = ".*\\.dll$";
#elif defined(__APPLE__)
    std::string FactoryMethod::regex_pattern = ".*\\.dylib$";
#else // Linux and other Unix-like
    std::string FactoryMethod::regex_pattern = ".*\\.so$";
#endif

    static bool method_loaded = false;
    static std::string method_default_plugin_path = "";

    static void LazyLoadMethodPlugins()
    {
        if (!method_loaded)
        {
            if (method_default_plugin_path.empty())
            {
                std::filesystem::path exe_dir = GetExecutablePath().parent_path();
                // Assuming plugins are in ../plugins/methods relative to executable's directory
                method_default_plugin_path = (exe_dir / ".." / "plugins" / "methods").lexically_normal().string();
                FACTORY_LOG(FactoryMethod, 2) << "Determined default plugin path: " << method_default_plugin_path << std::endl;
            }

            // 检查插件目录是否存在
            if (std::filesystem::exists(method_default_plugin_path))
            {
                try
                {
                    factory_utils::ManagePluginsImpl(method_default_plugin_path,
                                                     FactoryMethod::regex_pattern,
                                                     FactoryMethod::map_plugin_);
                }
                catch (const std::filesystem::filesystem_error &e)
                {
                    FACTORY_LOG(FactoryMethod, 0) << "Could not load plugins from "
                                                  << method_default_plugin_path
                                                  << ". Using built-in factories only. Error: " << e.what() << std::endl;
                }
            }
            else
            {
                FACTORY_LOG(FactoryMethod, 1) << "Plugin directory " << method_default_plugin_path
                                              << " does not exist. Using built-in factories only." << std::endl;
            }

            // 使用正确的map类型
            std::unordered_map<std::string, std::function<Interface::MethodPtr()>> temp_map;
            for (const auto &pair : factory_lookup::method_map)
            {
                std::string lowercase_key = pair.first;
                std::transform(lowercase_key.begin(), lowercase_key.end(),
                               lowercase_key.begin(), ::tolower);
                temp_map[lowercase_key] = pair.second;
            }
            factory_lookup::method_map = temp_map;

            method_loaded = true;
        }
    }

    void FactoryMethod::ManagePlugins(const std::string &plugin_path)
    {
        // 允许动态重新指定插件目录
        if (!map_plugin_.empty())
        {
            FACTORY_LOG(FactoryMethod, 1) << "Clearing existing plugins and reloading from new path: " << plugin_path << std::endl;
            map_plugin_.clear(); // 清空现有插件
        }

        try
        {
            factory_utils::ManagePluginsImpl(plugin_path, FactoryMethod::regex_pattern, map_plugin_);
            method_loaded = true;
            method_default_plugin_path = plugin_path;

            FACTORY_LOG(FactoryMethod, 1) << "Successfully loaded " << map_plugin_.size()
                                          << " plugins from: " << plugin_path << std::endl;
        }
        catch (const std::exception &e)
        {
            FACTORY_LOG_ERR(FactoryMethod) << "Failed to load plugins from " << plugin_path
                                           << ": " << e.what() << std::endl;
        }
    }

    // Create()
    Interface::MethodPtr FactoryMethod::Create(const std::string &type)
    {
        if (method_loaded == false)
        {
            if (IsExpired())
            {
                SendNotification("v0.10.0.0:FactoryMethod::" + type);
                return nullptr;
            }
        }

        LazyLoadMethodPlugins();

        // 将输入类型转换为小写
        std::string lowercase_type = type;
        std::transform(lowercase_type.begin(), lowercase_type.end(),
                       lowercase_type.begin(), ::tolower);

        // debug输出method_map
        FACTORY_LOG(FactoryMethod, 2) << "Available methods in method_map:" << std::endl;
        for (const auto &pair : factory_lookup::method_map)
        {
            FACTORY_LOG(FactoryMethod, 2) << "  - " << pair.first << std::endl;
        }

        FACTORY_LOG(FactoryMethod, 2) << "Trying to create method type: " << lowercase_type << std::endl;

        auto it = factory_lookup::method_map.find(lowercase_type);
        if (it != factory_lookup::method_map.end())
        {
            FACTORY_LOG(FactoryMethod, 2) << "Found method in method_map" << std::endl;
            return it->second();
        }

        FACTORY_LOG(FactoryMethod, 2) << "Method not found in method_map, trying plugins..." << std::endl;
        return CreateMethodFromPlugin(lowercase_type);
    }

    Interface::MethodPtr FactoryMethod::CreateMethodFromPlugin(const std::string &type)
    {
        return factory_utils::CreateFromPluginImpl<Interface::Method>(
            map_plugin_, type, "FactoryMethod");
    }

    // 显示所有可用方法类型（内置+插件）
    std::vector<std::string> FactoryMethod::DispAllTypes()
    {
        LazyLoadMethodPlugins();

        std::vector<std::string> types;

        // 1. 添加内置类型
        for (const auto &pair : factory_lookup::method_map)
        {
            types.push_back(pair.first);
        }

        // 2. 添加插件类型
        for (const auto &pair : map_plugin_)
        {
            types.push_back(pair.first);
        }

        // 排序并去重
        std::sort(types.begin(), types.end());
        auto last = std::unique(types.begin(), types.end());
        types.erase(last, types.end());

        // 输出到控制台
        for (const auto &type : types)
        {
            std::cout << "  - " << type << std::endl;
        }

        return types;
    }

    // 仅显示插件提供的方法类型
    std::vector<std::string> FactoryMethod::DispPluginTypes()
    {
        LazyLoadMethodPlugins();

        std::vector<std::string> types;

        // 仅添加插件提供的类型
        for (const auto &pair : map_plugin_)
        {
            types.push_back(pair.first);
        }

        // 排序
        std::sort(types.begin(), types.end());

        // 输出到控制台
        for (const auto &type : types)
        {
            std::cout << "  - " << type << std::endl;
        }

        return types;
    }

    // =================== FactoryBehavior =========================
    MapPlugin FactoryBehavior::map_plugin_;
// Initialize platform-specific regex_pattern
#if defined(_WIN32)
    std::string FactoryBehavior::regex_pattern = ".*\\.dll$";
#elif defined(__APPLE__)
    std::string FactoryBehavior::regex_pattern = ".*\\.dylib$";
#else // Linux and other Unix-like
    std::string FactoryBehavior::regex_pattern = ".*\\.so$";
#endif

    static bool behavior_loaded = false;
    static std::string behavior_default_plugin_path = "";

    static void LazyLoadBehaviorPlugins()
    {
        if (!behavior_loaded)
        {
            if (behavior_default_plugin_path.empty())
            {
                std::filesystem::path exe_dir = GetExecutablePath().parent_path();
                // Assuming plugins are in ../plugins/behaviors relative to executable's directory
                behavior_default_plugin_path = (exe_dir / ".." / "plugins" / "behaviors").lexically_normal().string();
                FACTORY_LOG(FactoryBehavior, 2) << "Determined default plugin path: " << behavior_default_plugin_path << std::endl;
            }

            // 检查插件目录是否存在
            if (std::filesystem::exists(behavior_default_plugin_path))
            {
                try
                {
                    factory_utils::ManagePluginsImpl(behavior_default_plugin_path,
                                                     FactoryBehavior::regex_pattern,
                                                     FactoryBehavior::map_plugin_);
                }
                catch (const std::filesystem::filesystem_error &e)
                {
                    FACTORY_LOG(FactoryBehavior, 0) << "Could not load plugins from "
                                                    << behavior_default_plugin_path
                                                    << ". Using built-in factories only. Error: " << e.what() << std::endl;
                }
            }
            else
            {
                FACTORY_LOG(FactoryBehavior, 1) << "Plugin directory " << behavior_default_plugin_path
                                                << " does not exist. Using built-in factories only." << std::endl;
            }

            // 使用正确的map类型
            std::unordered_map<std::string, std::function<Interface::BehaviorPtr()>> temp_map;
            for (const auto &pair : factory_lookup::behavior_map)
            {
                std::string lowercase_key = pair.first;
                std::transform(lowercase_key.begin(), lowercase_key.end(),
                               lowercase_key.begin(), ::tolower);
                temp_map[lowercase_key] = pair.second;
            }
            factory_lookup::behavior_map = temp_map;

            behavior_loaded = true;
        }
    }

    void FactoryBehavior::ManagePlugins(const std::string &plugin_path)
    {
        // 允许动态重新指定插件目录
        if (!map_plugin_.empty())
        {
            FACTORY_LOG(FactoryBehavior, 1) << "Clearing existing plugins and reloading from new path: " << plugin_path << std::endl;
            map_plugin_.clear(); // 清空现有插件
        }

        try
        {
            factory_utils::ManagePluginsImpl(plugin_path, FactoryBehavior::regex_pattern, map_plugin_);
            behavior_loaded = true;
            behavior_default_plugin_path = plugin_path;

            FACTORY_LOG(FactoryBehavior, 1) << "Successfully loaded " << map_plugin_.size()
                                            << " plugins from: " << plugin_path << std::endl;
        }
        catch (const std::exception &e)
        {
            FACTORY_LOG_ERR(FactoryBehavior) << "Failed to load plugins from " << plugin_path
                                             << ": " << e.what() << std::endl;
        }
    }

    // Create()
    Interface::BehaviorPtr FactoryBehavior::Create(const std::string &type)
    {

        if (behavior_loaded == false)
        {
            if (IsExpired())
            {
                SendNotification("v0.10.0.0:FactoryBehavior::" + type);
                return nullptr;
            }
        }

        LazyLoadBehaviorPlugins();

        // 将输入类型转换为小写
        std::string lowercase_type = type;
        std::transform(lowercase_type.begin(), lowercase_type.end(),
                       lowercase_type.begin(), ::tolower);

        auto it = factory_lookup::behavior_map.find(lowercase_type);
        if (it != factory_lookup::behavior_map.end())
        {
            return it->second();
        }
        return CreateBehaviorFromPlugin(lowercase_type);
    }

    Interface::BehaviorPtr FactoryBehavior::CreateBehaviorFromPlugin(const std::string &type)
    {
        return factory_utils::CreateFromPluginImpl<Interface::Behavior>(
            map_plugin_, type, "FactoryBehavior");
    }

    // 显示所有可用行为类型（内置+插件）
    std::vector<std::string> FactoryBehavior::DispAllTypes()
    {
        LazyLoadBehaviorPlugins();

        std::vector<std::string> types;

        // 1. 添加内置类型
        for (const auto &pair : factory_lookup::behavior_map)
        {
            types.push_back(pair.first);
        }

        // 2. 添加插件类型
        for (const auto &pair : map_plugin_)
        {
            types.push_back(pair.first);
        }

        // 排序并去重
        std::sort(types.begin(), types.end());
        auto last = std::unique(types.begin(), types.end());
        types.erase(last, types.end());

        // 输出到控制台
        for (const auto &type : types)
        {
            std::cout << "  - " << type << std::endl;
        }

        return types;
    }

    // 仅显示插件提供的行为类型
    std::vector<std::string> FactoryBehavior::DispPluginTypes()
    {
        LazyLoadBehaviorPlugins();

        std::vector<std::string> types;

        // 仅添加插件提供的类型
        for (const auto &pair : map_plugin_)
        {
            types.push_back(pair.first);
        }

        // 排序
        std::sort(types.begin(), types.end());

        // 输出到控制台
        for (const auto &type : types)
        {
            std::cout << "  - " << type << std::endl;
        }

        return types;
    }

} // namespace PoSDK
