#pragma once
#include <unordered_map>
#include <functional>
#include "interfaces.hpp"

#include "data/data_images.hpp"
#include "data/data_matches.hpp"
#include "data/data_tracks.hpp"
#include "data/data_relative_rotations.hpp"
#include "data/data_global_rotations.hpp"
#include "data/data_global_translations.hpp"
#include "data/data_camera_models.hpp"
#include "data/data_global_poses.hpp"
#include "data/data_relative_poses.hpp"
#include "data/data_points_3d.hpp"
#include "data/data_features.hpp"

#include "methods/method_LiRP.hpp"
#include "methods/method_LiRPFast.hpp"
#include "methods/method_LiGT.hpp"
#include "methods/method_matches2tracks.hpp"
#include "methods/method_PA.hpp"
#include "methods/method_relative_cost.hpp"
#include "methods/method_robustLiRP.hpp"
#include "methods/opengv_simulator.hpp"
#include "methods/visual_simulator.hpp"
#include "methods/method_global_outlier_removal.hpp"
#include "methods/analytical_reconstruction.hpp"
#include "methods/method_povgSixPoint.hpp"
#include "methods/TwoViewOptimizer/method_TwoViewOptimizer.hpp"

namespace PoSDK
{
    namespace factory_lookup
    {

        /**
         * @brief Data 预置类型对应表: type string -> 返回一个 DataPtr（默认构造）
         */
        static std::unordered_map<std::string, std::function<Interface::DataPtr()>> data_map = {
            {"data_images", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataImages>();
             }},
            {"data_matches", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataMatches>();
             }},
            {"data_tracks", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataTracks>();
             }},
            {"data_relative_rotations", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataRelativeRotations>();
             }},
            {"data_global_rotations", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataGlobalRotations>();
             }},
            {"data_global_translations", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataGlobalTranslations>();
             }},
            {"data_camera_models", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataCameraModels>();
             }},
            {"data_global_poses", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataGlobalPoses>();
             }},
            {"data_relative_poses", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataRelativePoses>();
             }},
            {"data_points_3d", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataPoints3D>();
             }},
            {"data_features", []() -> Interface::DataPtr
             {
                 return std::make_shared<DataFeatures>();
             }},
            // ... 如还有其他类型 继续添加
        };

        /**
         * @brief Method 预置类型对应表: type string -> 返回一个 MethodPtr
         */
        static std::unordered_map<std::string, std::function<Interface::MethodPtr()>> method_map = {
            {"method_LiRP", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodLiRP>();
             }},
            {"method_LiRPFast", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodLiRPFast>();
             }},
            {"method_matches2tracks", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodMatches2Tracks>();
             }},
            {"method_LiGT", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodLiGT>();
             }},
            {"method_PA", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodPA>();
             }},
            {"method_relative_cost", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodRelativeCost>();
             }},
            {"method_robustLiRP", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodRobustLiRP>();
             }},
            {"visual_simulator", []() -> Interface::MethodPtr
             {
                 return std::make_shared<VisualSimulator>();
             }},
            {"method_global_outlier_removal", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodGlobalOutlierRemoval>();
             }},
            {"analytical_reconstruction", []() -> Interface::MethodPtr
             {
                 return std::make_shared<methods::AnalyticalReconstruction>();
             }},
            {"opengv_simulator", []() -> Interface::MethodPtr
             {
                 return std::make_shared<OpenGVSimulator>();
             }},
            {"method_povgSixPoint", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodPovgSixPoint>();
             }},
            {"method_TwoViewOptimizer", []() -> Interface::MethodPtr
             {
                 return std::make_shared<MethodTwoViewOptimizer>();
             }},
            // ...
        };

        /**
         * @brief Behavior 预置类型对应表: type string -> 返回一个 BehaviorPtr
         */
        static std::unordered_map<std::string, std::function<Interface::BehaviorPtr()>> behavior_map = {

        };

    } // namespace factory_lookup
} // namespace PoSDK
