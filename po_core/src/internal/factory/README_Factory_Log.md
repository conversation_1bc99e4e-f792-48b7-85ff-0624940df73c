# 工厂日志系统使用指南

## 概述

新的工厂日志系统简化了日志管理，参考了 `interfaces.hpp` 中的 LOG 方式，提供统一的日志接口。

## 特性

- **简化设计**：只有一个主要的日志宏 `FACTORY_LOG`
- **级别控制**：支持不同的日志级别（0, 1, 2）
- **调试兼容**：DEBUG 和非 DEBUG 模式下都可以使用
- **内部管理**：日志级别不暴露在头文件接口中

## 使用方法

### 基本用法

```cpp
// 基本日志输出
FACTORY_LOG(FactoryData, 0) << "这是一条基本日志" << std::endl;
FACTORY_LOG(FactoryMethod, 1) << "这是一条详细日志" << std::endl;
FACTORY_LOG(FactoryBehavior, 2) << "这是一条调试日志" << std::endl;

// 错误日志（始终输出，红色显示）
FACTORY_LOG_ERR(FactoryData) << "这是错误信息" << std::endl;
```

### 日志级别

- **级别 0**：基本日志，重要信息
- **级别 1**：详细日志，一般信息
- **级别 2**：调试日志，详细调试信息

### 级别控制

```cpp
// 设置日志级别（内部使用，不暴露接口）
FactoryData::log_level_ = 1;    // 显示级别 0 和 1 的日志
FactoryMethod::log_level_ = 2;  // 显示所有级别的日志
FactoryBehavior::log_level_ = 0; // 只显示级别 0 的日志
```

## DEBUG 模式差异

### DEBUG 模式
```
[PoSDK | FactoryData] >>> factory.cpp line123: 这是一条日志
```

### 非 DEBUG 模式
```
[PoSDK | FactoryData] >>> 这是一条日志
```

## 支持的工厂类

- `FactoryData`：数据工厂
- `FactoryMethod`：方法工厂  
- `FactoryBehavior`：行为工厂

## 迁移指南

### 旧的日志宏 → 新的日志宏

```cpp
// 旧的方式
FACTORY_LOG_INFO(FactoryData) << "信息" << std::endl;
FACTORY_LOG_VERBOSE(FactoryData) << "详细信息" << std::endl;
FACTORY_LOG_WARNING(FactoryData) << "警告" << std::endl;

// 新的方式
FACTORY_LOG(FactoryData, 1) << "信息" << std::endl;
FACTORY_LOG(FactoryData, 2) << "详细信息" << std::endl;
FACTORY_LOG(FactoryData, 0) << "警告" << std::endl;
```

### 级别映射

- `FACTORY_LOG_INFO` → `FACTORY_LOG(factory_class, 1)`
- `FACTORY_LOG_VERBOSE` → `FACTORY_LOG(factory_class, 2)`
- `FACTORY_LOG_WARNING` → `FACTORY_LOG(factory_class, 0)`

## 实现细节

- 日志级别变量 `log_level_` 在每个工厂类中都有独立的实例
- 默认日志级别为 0
- 不再使用条件编译来控制日志级别的可用性
- 保持与 `interfaces.hpp` 中 LOG 宏的一致性

## 测试

可以使用 `test_factory_log.cpp` 来测试日志系统的功能：

```bash
cd po_core/src/internal/factory
g++ -I. -I../.. test_factory_log.cpp factory.cpp -o test_factory_log
./test_factory_log
``` 