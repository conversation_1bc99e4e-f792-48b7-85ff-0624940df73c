# 查找 Boost 库
find_package(Boost REQUIRED COMPONENTS system filesystem)

# 查找Ceres库
find_package(Ceres REQUIRED)

# 查找CURL库
find_package(CURL REQUIRED)

# 查找gflags库 - 跨平台方式
if(APPLE)
    # MacOS特定路径
    set(GFLAGS_LIB "/opt/homebrew/lib/libgflags.dylib")
    if(NOT EXISTS ${GFLAGS_LIB})
        set(GFLAGS_LIB "-lgflags")
    endif()
else()
    # Linux和其他平台
    find_library(GFLAGS_LIB gflags)
    if(NOT GFLAGS_LIB)
        set(GFLAGS_LIB "-lgflags")
    endif()
endif()

# ==============================================================================
# pomvg_internal 库构建配置
# ==============================================================================

# 创建内部库，包含types.cpp
add_library(pomvg_internal 
    "${CMAKE_CURRENT_SOURCE_DIR}/types.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/types.hpp"
)

# 设置库属性
set_target_properties(pomvg_internal PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    POSITION_INDEPENDENT_CODE ON
    LIBRARY_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    ARCHIVE_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    RUNTIME_OUTPUT_DIRECTORY "${OUTPUT_BIN_DIR}"
)

# 配置编译选项
target_compile_options(pomvg_internal
    PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/W4 /wd4251>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# 配置头文件包含路径
target_include_directories(pomvg_internal
    PUBLIC
        # 构建时使用的头文件路径
        $<BUILD_INTERFACE:${OUTPUT_INCLUDE_DIR}>
        # 安装后使用的头文件路径
        $<INSTALL_INTERFACE:include>
        # Ceres库头文件路径
        ${CERES_INCLUDE_DIRS}
    PRIVATE
        # 内部实现头文件路径
        ${CMAKE_CURRENT_SOURCE_DIR}
)

# 链接依赖库
target_link_libraries(pomvg_internal
    PUBLIC
        Eigen3::Eigen
        ${CERES_LIBRARIES}
)

# ==============================================================================
# pomvg_factory 库构建配置
# ==============================================================================

# 查找源文件
file(GLOB_RECURSE FACTORY_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cc"
    "${CMAKE_CURRENT_SOURCE_DIR}/../relative_process/*.cpp"
)

file(GLOB_RECURSE FACTORY_HEADERS 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.hpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.h"  # 添加.h文件
    "${CMAKE_CURRENT_SOURCE_DIR}/../relative_process/*.hpp"
)

# 移除types.cpp，避免重复编译
list(REMOVE_ITEM FACTORY_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/types.cpp")

# 创建库目标
add_library(pomvg_factory ${FACTORY_SOURCES} ${FACTORY_HEADERS})

# 设置库属性
set_target_properties(pomvg_factory PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION ${PROJECT_VERSION_MAJOR}
    POSITION_INDEPENDENT_CODE ON
    LIBRARY_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    ARCHIVE_OUTPUT_DIRECTORY "${OUTPUT_LIB_DIR}"
    RUNTIME_OUTPUT_DIRECTORY "${OUTPUT_BIN_DIR}"
)

# 配置编译选项
target_compile_options(pomvg_factory
    PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/W4 /wd4251>
        $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# 配置头文件包含路径
target_include_directories(pomvg_factory
    PUBLIC
        # 构建时使用的头文件路径
        $<BUILD_INTERFACE:${OUTPUT_INCLUDE_DIR}>
        # 安装后使用的头文件路径
        $<INSTALL_INTERFACE:include>
    PRIVATE
        # 内部实现头文件路径
        ${CMAKE_CURRENT_SOURCE_DIR}
        # file IO头文件路径
        ${CMAKE_CURRENT_SOURCE_DIR}/../fileIO
        # factory目录头文件路径
        ${CMAKE_CURRENT_SOURCE_DIR}/factory
        # relative_process目录头文件路径
        ${CMAKE_CURRENT_SOURCE_DIR}/../relative_process
        # Boost库头文件路径
        ${Boost_INCLUDE_DIRS}
        # Spectra库头文件路径
        ${SPECTRA_INCLUDE_DIR}
        ${SPECTRA_INCLUDE_DIR}/include
        # Ceres库头文件路径
        ${CERES_INCLUDE_DIRS}
        # CURL头文件路径
        ${CURL_INCLUDE_DIRS}
)

# 链接依赖库
target_link_libraries(pomvg_factory
    PUBLIC
        pomvg_proto
        pomvg_file_io
        pomvg_internal
    PRIVATE
        Boost::system
        Boost::filesystem
        ${CURL_LIBRARIES}
        ${GFLAGS_LIB}
)

# ==============================================================================
# 配置文件处理
# ==============================================================================
# 设置配置文件输出目录
set(METHOD_CONFIG_OUTPUT_DIR "${OUTPUT_BASE_DIR}/configs/methods")
file(MAKE_DIRECTORY ${METHOD_CONFIG_OUTPUT_DIR})

# 复制配置文件到构建目录
file(GLOB METHOD_CONFIG_FILES "${CMAKE_CURRENT_SOURCE_DIR}/methods/configs/*.ini")
foreach(config_file ${METHOD_CONFIG_FILES})
    get_filename_component(filename ${config_file} NAME)
    configure_file(
        ${config_file}
        "${METHOD_CONFIG_OUTPUT_DIR}/${filename}"
        COPYONLY
    )
endforeach()

# ==============================================================================
# 编译定义配置
# ==============================================================================
# 添加编译定义
target_compile_definitions(pomvg_factory 
    PRIVATE
        POMVG_BUILD_CONFIG_DIR="${METHOD_CONFIG_OUTPUT_DIR}"
        POMVG_INSTALL_CONFIG_DIR="${CMAKE_INSTALL_FULL_DATADIR}/pomvg/methods/configs"
)

# ==============================================================================
# 安装配置
# ==============================================================================

