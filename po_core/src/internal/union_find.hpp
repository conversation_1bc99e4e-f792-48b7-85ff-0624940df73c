/**
 * @file union_find.hpp
 * @brief 并查集数据结构实现
 * @details 用于高效地合并特征轨迹
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _POMVG_UNION_FIND_HPP_
#define _POMVG_UNION_FIND_HPP_

#include <vector>
#include "types.hpp"

namespace PoSDK
{

    /**
     * @brief 并查集数据结构
     * @details 使用路径压缩和按秩合并优化的并查集实现
     */
    class UnionFind
    {
    public:
        /**
         * @brief 构造函数
         * @param n 元素数量
         */
        explicit UnionFind(Size n) : parent_(n), rank_(n, 0)
        {
            for (Size i = 0; i < n; ++i)
            {
                parent_[i] = i;
            }
        }

        /**
         * @brief 查找元素所属的集合（带路径压缩）
         * @param x 要查找的元素
         * @return 元素所属集合的代表元素
         */
        Size Find(Size x)
        {
            if (parent_[x] != x)
            {
                parent_[x] = Find(parent_[x]); // 路径压缩
            }
            return parent_[x];
        }

        /**
         * @brief 合并两个元素所属的集合（按秩合并）
         * @param x 第一个元素
         * @param y 第二个元素
         */
        void Union(Size x, Size y)
        {
            Size px = Find(x);
            Size py = Find(y);

            if (px == py)
                return;

            // 按秩合并
            if (rank_[px] < rank_[py])
            {
                parent_[px] = py;
            }
            else if (rank_[px] > rank_[py])
            {
                parent_[py] = px;
            }
            else
            {
                parent_[py] = px;
                rank_[px]++;
            }
        }

        /**
         * @brief 检查两个元素是否属于同一个集合
         * @param x 第一个元素
         * @param y 第二个元素
         * @return 如果属于同一个集合返回true
         */
        bool Connected(Size x, Size y)
        {
            return Find(x) == Find(y);
        }

        /**
         * @brief 获取集合的数量
         * @return 当前的集合数量
         */
        Size GetSetCount() const
        {
            Size count = 0;
            for (Size i = 0; i < parent_.size(); ++i)
            {
                if (parent_[i] == i)
                    count++;
            }
            return count;
        }

    private:
        std::vector<Size> parent_; ///< 父节点数组
        std::vector<Size> rank_;   ///< 秩数组（用于优化合并）
    };

} // namespace PoSDK

#endif // _POMVG_UNION_FIND_HPP_
