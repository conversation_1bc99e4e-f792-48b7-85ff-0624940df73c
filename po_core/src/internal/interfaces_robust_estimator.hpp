// Copyright (c) 2024, POMVGTeam. All rights reserved.
// This source code is licensed under the CC-BY-SA 4.0 license.

#ifndef POMVG_INTERFACES_ROBUST_ESTIMATOR_HPP_
#define POMVG_INTERFACES_ROBUST_ESTIMATOR_HPP_

#include "interfaces_preset.hpp"
#include "interfaces_preset_profiler.hpp"
#include "factory.hpp"
#include <memory>
#include <vector>
#include <numeric>
#include <random>
#include <boost/algorithm/string.hpp>
#include <Eigen/Dense>

namespace PoSDK
{
    namespace Interface
    {

        //------------------------------------------------------------------------------
        // DataSample
        //------------------------------------------------------------------------------

        /**
         * @brief 采样策略枚举
         */
        enum class SamplingStrategy
        {
            UNIFORM_RANDOM,    ///< 均匀随机采样（默认）
            SPHERICAL_UNIFORM, ///< 球面均匀采样（适用于BearingPairs）
            STRATIFIED,        ///< 分层采样
            ADAPTIVE           ///< 自适应采样
        };

        /**
         * @brief 球面采样缓存结构
         */
        struct SphericalSamplingCache
        {
            std::vector<std::vector<size_t>> region_indices; ///< 每个区域的索引
            std::vector<Eigen::Vector3d> region_centers;     ///< 区域中心点
            size_t num_regions{0};                           ///< 区域数量
            bool is_valid{false};                            ///< 缓存是否有效
            std::mt19937 rng;                                ///< 随机数生成器

            SphericalSamplingCache() : rng(std::random_device{}()) {}

            void Clear()
            {
                region_indices.clear();
                region_centers.clear();
                num_regions = 0;
                is_valid = false;
            }
        };

        /**
         * @brief 通用采样缓存结构
         */
        struct SamplingCache
        {
            SamplingStrategy strategy{SamplingStrategy::UNIFORM_RANDOM};
            std::mt19937 rng;                       ///< 随机数生成器
            SphericalSamplingCache spherical_cache; ///< 球面采样专用缓存

            // 通用缓存参数
            size_t last_data_size{0};   ///< 上次数据大小
            size_t last_subset_size{0}; ///< 上次子集大小

            SamplingCache() : rng(std::random_device{}()) {}

            void InvalidateCache()
            {
                spherical_cache.Clear();
                last_data_size = 0;
                last_subset_size = 0;
            }
        };

        /**
         * @brief 样本数据基类,用于存储和管理样本数据
         * @tparam TSample 样本数据类型,必须是std::vector类型
         * @details 该类提供了样本数据的存储、访问和随机采样等功能,采用智能指针实现零拷贝
         */

        template <typename TSample>
        class DataSample : public DataMap<TSample>
        {
            static_assert(std::is_same<std::vector<typename TSample::value_type>, TSample>::value,
                          "TSample must be a std::vector type");

            // -----------------------------------------------------------------------------
            // DataIO 多态实现 (公开给用户)
            // -----------------------------------------------------------------------------
        public:
            // 数据类型和访问
            const std::string &GetType() const override
            {
                static const std::string type = "data_sample";
                return type;
            }

            void *GetData() override
            {
                return this->GetMapPtr().get();
            }

            // -----------------------------------------------------------------------------
            // 样本数据和方法定义 (内部)
            // -----------------------------------------------------------------------------
        public:
            using SampleType = TSample;
            using ValueType = typename TSample::value_type;
            using BaseType = DataMap<TSample>;

            // 构造函数
            using DataMap<TSample>::DataMap;
            DataSample() = default;
            virtual ~DataSample() = default;
            explicit DataSample(Ptr<TSample> samples_ptr)
            {
                this->data_map_ptr_ = std::move(samples_ptr);
            }

            // 增加构造函数支持直接传入vector
            explicit DataSample(const TSample &data)
            {
                this->data_map_ptr_ = std::make_shared<TSample>(data);
            }

            /**
             * @brief 设置总体样本数据
             * @param population_ptr 总体样本数据指针
             */
            void SetPopulationPtr(const TSample &data)
            {
                this->data_map_ptr_ = std::make_shared<TSample>(data);
                InvalidateSamplingCache();
            }

            void SetPopulationPtr(Ptr<TSample> population_ptr)
            {
                this->data_map_ptr_ = std::move(population_ptr);
                InvalidateSamplingCache();
            }

            /**
             * @brief 获取总体样本数据指针
             * @return 返回总体样本数据指针
             */
            const std::shared_ptr<TSample> &GetPopulationPtr() const
            {
                return this->GetMapPtr();
            }

            /**
             * @brief 获取总体样本数量
             * @return 返回总体样本数量
             */
            size_t GetPopulationSize() const
            {
                return this->GetMapPtr() ? this->GetMapPtr()->size() : 0;
            }

            /**
             * @brief 设置采样策略
             * @param strategy 采样策略
             */
            void SetSamplingStrategy(SamplingStrategy strategy)
            {
                if (sampling_cache_.strategy != strategy)
                {
                    sampling_cache_.strategy = strategy;
                    sampling_cache_.InvalidateCache();
                }
            }

            /**
             * @brief 获取当前采样策略
             * @return 当前采样策略
             */
            SamplingStrategy GetSamplingStrategy() const
            {
                return sampling_cache_.strategy;
            }

            /**
             * @brief 生成随机的子样本索引（智能采样）
             * @param subset_size 需要的子集大小
             * @return 返回随机选择的子样本索引数组
             */
            std::vector<size_t> GenerateRandomIndices(size_t subset_size) const
            {
                if (!this->GetMapPtr() || subset_size > this->GetMapPtr()->size())
                {
                    return std::vector<size_t>();
                }

                const auto &data = *this->GetMapPtr();

                switch (sampling_cache_.strategy)
                {
                case SamplingStrategy::SPHERICAL_UNIFORM:
                    return GenerateSphericalUniformIndices(data, subset_size);
                case SamplingStrategy::STRATIFIED:
                    return GenerateStratifiedIndices(data, subset_size);
                case SamplingStrategy::ADAPTIVE:
                    return GenerateAdaptiveIndices(data, subset_size);
                case SamplingStrategy::UNIFORM_RANDOM:
                default:
                    return GenerateUniformRandomIndices(data, subset_size);
                }
            }

            /**
             * @brief 获取随机子样本
             * @param size 子样本大小
             * @return 返回子样本的DataPtr
             */
            Ptr<DataSample<TSample>> GetRandomSubset(size_t size) const
            {
                if (!this->GetMapPtr())
                    return nullptr;

                auto subset = std::make_shared<DataSample<TSample>>();
                subset->data_map_ptr_ = this->GetMapPtr(); // 共享原始数据
                subset->subset_indices_ = GenerateRandomIndices(size);
                subset->is_subset_ = true;

                return subset;
            }

            /**
             * @brief 获取指定索引的子样本
             * @param indices 子样本索引数组
             * @return 返回子样本的DataPtr
             */
            Ptr<DataSample<TSample>> GetSubset(const std::vector<size_t> &indices) const
            {
                if (!this->GetMapPtr())
                    return nullptr;

                auto subset = std::make_shared<DataSample<TSample>>();
                subset->data_map_ptr_ = this->GetMapPtr();
                subset->subset_indices_ = indices;
                subset->is_subset_ = true;

                return subset;
            }

            /**
             * @brief 获取内点子样本
             * @param inlier_indices 内点索引数组
             * @return 返回内点子样本的DataPtr
             */
            DataPtr GetInlierSubset(const std::vector<size_t> &inlier_indices) const
            {
                return GetSubset(inlier_indices);
            }

            bool IsSubset() const { return is_subset_; }

            /**
             * @brief 创建当前对象的深拷贝
             * @return 返回拷贝后的新对象指针
             * @note 会正确复制数据和子集索引信息
             */
            DataPtr CopyData() const override
            {
                // 如果数据指针无效，返回nullptr
                if (!this->data_map_ptr_)
                {
                    return nullptr;
                }

                try
                {
                    // 创建一个新的DataSample对象
                    auto cloned = std::make_shared<DataSample<TSample>>();

                    // 复制内部数据（深拷贝）
                    cloned->data_map_ptr_ = std::make_shared<TSample>(*this->data_map_ptr_);

                    // 复制子集信息
                    cloned->subset_indices_ = this->subset_indices_;
                    cloned->is_subset_ = this->is_subset_;

                    return cloned;
                }
                catch (const std::exception &e)
                {
                    // 捕获并记录拷贝过程中的异常
                    std::cerr << "[DataSample::CopyData] Error: Failed to clone DataSample: " << e.what() << std::endl;
                    return nullptr;
                }
            }

        private:
            /**
             * @brief 使缓存失效
             */
            void InvalidateSamplingCache() const
            {
                sampling_cache_.InvalidateCache();
            }

            /**
             * @brief 传统均匀随机采样
             */
            std::vector<size_t> GenerateUniformRandomIndices(const TSample &data, size_t subset_size) const
            {
                std::vector<size_t> indices(data.size());
                std::iota(indices.begin(), indices.end(), 0);

                for (size_t i = 0; i < subset_size; ++i)
                {
                    std::uniform_int_distribution<size_t> dis(i, indices.size() - 1);
                    size_t j = dis(sampling_cache_.rng);
                    std::swap(indices[i], indices[j]);
                }

                indices.resize(subset_size);
                return indices;
            }

            /**
             * @brief 球面均匀采样（适用于BearingPairs）
             */
            std::vector<size_t> GenerateSphericalUniformIndices(const TSample &data, size_t subset_size) const
            {
                // 检查是否为BearingPairs类型
                if constexpr (std::is_same_v<TSample, std::vector<Eigen::Matrix<double, 6, 1>>>)
                {
                    return GenerateSphericalUniformIndicesForBearingPairs(data, subset_size);
                }
                else
                {
                    // 对于非BearingPairs类型，回退到均匀随机采样
                    return GenerateUniformRandomIndices(data, subset_size);
                }
            }

            /**
             * @brief 专门为BearingPairs实现的球面均匀采样
             */
            std::vector<size_t> GenerateSphericalUniformIndicesForBearingPairs(
                const std::vector<Eigen::Matrix<double, 6, 1>> &data, size_t subset_size) const
            {
                if (data.empty() || subset_size == 0)
                    return {};

                auto &cache = sampling_cache_.spherical_cache;

                // 检查缓存是否需要重建
                bool need_rebuild = !cache.is_valid ||
                                    cache.num_regions != subset_size ||
                                    sampling_cache_.last_data_size != data.size();

                if (need_rebuild)
                {
                    BuildSphericalRegions(data, subset_size, cache);
                    sampling_cache_.last_data_size = data.size();
                    sampling_cache_.last_subset_size = subset_size;
                }

                return SampleFromSphericalRegions(cache, subset_size);
            }

            /**
             * @brief 构建球面区域
             */
            void BuildSphericalRegions(const std::vector<Eigen::Matrix<double, 6, 1>> &data,
                                       size_t num_regions, SphericalSamplingCache &cache) const
            {
                cache.Clear();
                cache.num_regions = num_regions;

                // 提取第一个观测向量（前3个分量）
                std::vector<Eigen::Vector3d> bearing_vectors;
                bearing_vectors.reserve(data.size());

                for (const auto &bearing_pair : data)
                {
                    Eigen::Vector3d bearing = bearing_pair.head<3>().normalized();
                    bearing_vectors.push_back(bearing);
                }

                // 使用K-means++算法初始化区域中心
                cache.region_centers = InitializeSphericalCenters(bearing_vectors, num_regions);

                // 分配每个点到最近的区域
                cache.region_indices.resize(num_regions);
                for (size_t i = 0; i < bearing_vectors.size(); ++i)
                {
                    size_t closest_region = FindClosestSphericalRegion(bearing_vectors[i], cache.region_centers);
                    cache.region_indices[closest_region].push_back(i);
                }

                // 确保每个区域至少有一个点
                for (size_t region = 0; region < num_regions; ++region)
                {
                    if (cache.region_indices[region].empty())
                    {
                        // 找到最大的区域并分割
                        size_t largest_region = 0;
                        for (size_t r = 1; r < num_regions; ++r)
                        {
                            if (cache.region_indices[r].size() > cache.region_indices[largest_region].size())
                            {
                                largest_region = r;
                            }
                        }

                        if (!cache.region_indices[largest_region].empty())
                        {
                            size_t moved_idx = cache.region_indices[largest_region].back();
                            cache.region_indices[largest_region].pop_back();
                            cache.region_indices[region].push_back(moved_idx);
                        }
                    }
                }

                cache.is_valid = true;
            }

            /**
             * @brief 使用K-means++算法初始化球面中心点
             */
            std::vector<Eigen::Vector3d> InitializeSphericalCenters(
                const std::vector<Eigen::Vector3d> &points, size_t num_centers) const
            {
                if (points.empty() || num_centers == 0)
                    return {};

                std::vector<Eigen::Vector3d> centers;
                centers.reserve(num_centers);

                // 随机选择第一个中心
                std::uniform_int_distribution<size_t> dis(0, points.size() - 1);
                centers.push_back(points[dis(sampling_cache_.rng)]);

                // K-means++选择后续中心
                for (size_t i = 1; i < num_centers && i < points.size(); ++i)
                {
                    std::vector<double> distances;
                    distances.reserve(points.size());

                    // 计算每个点到最近中心的距离
                    for (const auto &point : points)
                    {
                        double min_dist = std::numeric_limits<double>::max();
                        for (const auto &center : centers)
                        {
                            // 使用球面距离（角度距离）
                            double dot_product = std::clamp(point.dot(center), -1.0, 1.0);
                            double angular_dist = std::acos(std::abs(dot_product));
                            min_dist = std::min(min_dist, angular_dist);
                        }
                        distances.push_back(min_dist * min_dist); // 平方距离用于概率权重
                    }

                    // 根据距离权重随机选择下一个中心
                    std::discrete_distribution<size_t> weighted_dis(distances.begin(), distances.end());
                    size_t next_center_idx = weighted_dis(sampling_cache_.rng);
                    centers.push_back(points[next_center_idx]);
                }

                return centers;
            }

            /**
             * @brief 找到最接近的球面区域
             */
            size_t FindClosestSphericalRegion(const Eigen::Vector3d &point,
                                              const std::vector<Eigen::Vector3d> &centers) const
            {
                if (centers.empty())
                    return 0;

                size_t closest = 0;
                double min_angular_dist = std::numeric_limits<double>::max();

                for (size_t i = 0; i < centers.size(); ++i)
                {
                    double dot_product = std::clamp(point.dot(centers[i]), -1.0, 1.0);
                    double angular_dist = std::acos(std::abs(dot_product));

                    if (angular_dist < min_angular_dist)
                    {
                        min_angular_dist = angular_dist;
                        closest = i;
                    }
                }

                return closest;
            }

            /**
             * @brief 从球面区域中采样
             */
            std::vector<size_t> SampleFromSphericalRegions(const SphericalSamplingCache &cache,
                                                           size_t subset_size) const
            {
                std::vector<size_t> selected_indices;
                selected_indices.reserve(subset_size);

                // 从每个区域中随机选择一个点
                for (size_t region = 0; region < cache.num_regions && selected_indices.size() < subset_size; ++region)
                {
                    const auto &region_indices = cache.region_indices[region];
                    if (!region_indices.empty())
                    {
                        std::uniform_int_distribution<size_t> dis(0, region_indices.size() - 1);
                        size_t random_idx = dis(sampling_cache_.rng);
                        selected_indices.push_back(region_indices[random_idx]);
                    }
                }

                // 如果还需要更多样本，从剩余的点中随机选择
                if (selected_indices.size() < subset_size)
                {
                    std::vector<size_t> remaining_indices;
                    for (size_t region = 0; region < cache.num_regions; ++region)
                    {
                        const auto &region_indices = cache.region_indices[region];
                        for (size_t idx : region_indices)
                        {
                            if (std::find(selected_indices.begin(), selected_indices.end(), idx) == selected_indices.end())
                            {
                                remaining_indices.push_back(idx);
                            }
                        }
                    }

                    if (!remaining_indices.empty())
                    {
                        std::shuffle(remaining_indices.begin(), remaining_indices.end(), sampling_cache_.rng);
                        size_t needed = std::min(subset_size - selected_indices.size(), remaining_indices.size());
                        selected_indices.insert(selected_indices.end(),
                                                remaining_indices.begin(),
                                                remaining_indices.begin() + needed);
                    }
                }

                // 如果仍然不足，重复选择（理论上不应该发生）
                while (selected_indices.size() < subset_size && !cache.region_indices.empty())
                {
                    for (size_t region = 0; region < cache.num_regions && selected_indices.size() < subset_size; ++region)
                    {
                        const auto &region_indices = cache.region_indices[region];
                        if (!region_indices.empty())
                        {
                            std::uniform_int_distribution<size_t> dis(0, region_indices.size() - 1);
                            selected_indices.push_back(region_indices[dis(sampling_cache_.rng)]);
                        }
                    }
                }

                return selected_indices;
            }

            /**
             * @brief 分层采样
             */
            std::vector<size_t> GenerateStratifiedIndices(const TSample &data, size_t subset_size) const
            {
                // 简单的分层采样实现：将数据分成几个层，每层采样相同数量
                size_t num_strata = std::min(subset_size, static_cast<size_t>(std::sqrt(data.size())));
                size_t stratum_size = data.size() / num_strata;
                size_t samples_per_stratum = subset_size / num_strata;
                size_t extra_samples = subset_size % num_strata;

                std::vector<size_t> selected_indices;
                selected_indices.reserve(subset_size);

                for (size_t stratum = 0; stratum < num_strata; ++stratum)
                {
                    size_t start_idx = stratum * stratum_size;
                    size_t end_idx = (stratum == num_strata - 1) ? data.size() : (stratum + 1) * stratum_size;

                    size_t current_samples = samples_per_stratum + (stratum < extra_samples ? 1 : 0);

                    std::uniform_int_distribution<size_t> dis(start_idx, end_idx - 1);
                    for (size_t i = 0; i < current_samples && start_idx + i < end_idx; ++i)
                    {
                        size_t selected_idx = dis(sampling_cache_.rng);
                        selected_indices.push_back(selected_idx);
                    }
                }

                return selected_indices;
            }

            /**
             * @brief 自适应采样
             */
            std::vector<size_t> GenerateAdaptiveIndices(const TSample &data, size_t subset_size) const
            {
                // 自适应采样：根据数据特性选择最合适的采样策略
                if constexpr (std::is_same_v<TSample, std::vector<Eigen::Matrix<double, 6, 1>>>)
                {
                    // 对于BearingPairs，使用球面均匀采样
                    return GenerateSphericalUniformIndicesForBearingPairs(data, subset_size);
                }
                else if (data.size() > 1000)
                {
                    // 对于大数据集，使用分层采样
                    return GenerateStratifiedIndices(data, subset_size);
                }
                else
                {
                    // 对于小数据集，使用均匀随机采样
                    return GenerateUniformRandomIndices(data, subset_size);
                }
            }

            //------------------------------------------------------------------------------
            // STL兼容接口 (公开给用户)
            //------------------------------------------------------------------------------
        public:
            using value_type = ValueType;
            using size_type = typename TSample::size_type;
            using reference = typename TSample::reference;
            using const_reference = typename TSample::const_reference;

            // 容器大小操作
            size_type size() const noexcept
            {
                return this->GetMapPtr() ? (is_subset_ ? subset_indices_.size() : this->GetMapPtr()->size()) : 0;
            }

            bool empty() const noexcept
            {
                return size() == 0;
            }

            // 元素访问
            const_reference operator[](size_type pos) const
            {
                assert(this->GetMapPtr());
                return is_subset_ ? (*this->GetMapPtr())[subset_indices_[pos]] : (*this->GetMapPtr())[pos];
            }

            reference operator[](size_type pos)
            {
                assert(this->GetMapPtr());
                return is_subset_ ? (*this->GetMapPtr())[subset_indices_[pos]] : (*this->GetMapPtr())[pos];
            }

            const_reference at(size_type pos) const
            {
                if (pos >= size())
                    throw std::out_of_range("DataSample: index out of range");
                return (*this)[pos];
            }

            reference at(size_type pos)
            {
                if (pos >= size())
                    throw std::out_of_range("DataSample: index out of range");
                return (*this)[pos];
            }

            // 迭代器支持
            class iterator
            {
            public:
                using iterator_category = std::random_access_iterator_tag;
                using value_type = typename TSample::value_type;
                using difference_type = std::ptrdiff_t;
                using pointer = value_type *;
                using reference = value_type &;

                iterator(TSample *data, const std::vector<size_t> *indices, size_t pos)
                    : data_(data), indices_(indices), pos_(pos) {}

                reference operator*()
                {
                    return indices_ ? (*data_)[(*indices_)[pos_]] : (*data_)[pos_];
                }

                pointer operator->()
                {
                    return &(this->operator*());
                }

                iterator &operator++()
                {
                    ++pos_;
                    return *this;
                }
                iterator operator++(int)
                {
                    iterator tmp = *this;
                    ++pos_;
                    return tmp;
                }
                iterator &operator--()
                {
                    --pos_;
                    return *this;
                }
                iterator operator--(int)
                {
                    iterator tmp = *this;
                    --pos_;
                    return tmp;
                }
                iterator &operator+=(difference_type n)
                {
                    pos_ += n;
                    return *this;
                }
                iterator &operator-=(difference_type n)
                {
                    pos_ -= n;
                    return *this;
                }
                iterator operator+(difference_type n) const { return iterator(data_, indices_, pos_ + n); }
                iterator operator-(difference_type n) const { return iterator(data_, indices_, pos_ - n); }
                difference_type operator-(const iterator &other) const { return pos_ - other.pos_; }

                bool operator==(const iterator &other) const { return pos_ == other.pos_; }
                bool operator!=(const iterator &other) const { return !(*this == other); }
                bool operator<(const iterator &other) const { return pos_ < other.pos_; }
                bool operator>(const iterator &other) const { return pos_ > other.pos_; }
                bool operator<=(const iterator &other) const { return pos_ <= other.pos_; }
                bool operator>=(const iterator &other) const { return pos_ >= other.pos_; }

                reference operator[](difference_type n)
                {
                    return indices_ ? (*data_)[(*indices_)[pos_ + n]] : (*data_)[pos_ + n];
                }

            private:
                TSample *data_;
                const std::vector<size_t> *indices_;
                size_t pos_;
            };

            iterator begin()
            {
                if (!this->GetMapPtr())
                    return iterator(nullptr, nullptr, 0);
                return iterator(
                    this->GetMapPtr().get(),
                    is_subset_ ? &subset_indices_ : nullptr,
                    0);
            }

            iterator end()
            {
                if (!this->GetMapPtr())
                    return iterator(nullptr, nullptr, 0);
                return iterator(
                    this->GetMapPtr().get(),
                    is_subset_ ? &subset_indices_ : nullptr,
                    size());
            }

            // 增加const迭代器支持
            class const_iterator
            {
            public:
                using iterator_category = std::random_access_iterator_tag;
                using value_type = typename TSample::value_type;
                using difference_type = std::ptrdiff_t;
                using pointer = const value_type *;
                using reference = const value_type &;

                const_iterator(const TSample *data, const std::vector<size_t> *indices, size_t pos)
                    : data_(data), indices_(indices), pos_(pos) {}

                reference operator*() const
                {
                    return indices_ ? (*data_)[(*indices_)[pos_]] : (*data_)[pos_];
                }

                pointer operator->() const
                {
                    return &(this->operator*());
                }

                const_iterator &operator++()
                {
                    ++pos_;
                    return *this;
                }
                const_iterator operator++(int)
                {
                    const_iterator tmp = *this;
                    ++pos_;
                    return tmp;
                }
                const_iterator &operator--()
                {
                    --pos_;
                    return *this;
                }
                const_iterator operator--(int)
                {
                    const_iterator tmp = *this;
                    --pos_;
                    return tmp;
                }
                const_iterator &operator+=(difference_type n)
                {
                    pos_ += n;
                    return *this;
                }
                const_iterator &operator-=(difference_type n)
                {
                    pos_ -= n;
                    return *this;
                }
                const_iterator operator+(difference_type n) const { return const_iterator(data_, indices_, pos_ + n); }
                const_iterator operator-(difference_type n) const { return const_iterator(data_, indices_, pos_ - n); }
                difference_type operator-(const const_iterator &other) const { return pos_ - other.pos_; }

                bool operator==(const const_iterator &other) const { return pos_ == other.pos_; }
                bool operator!=(const const_iterator &other) const { return !(*this == other); }
                bool operator<(const const_iterator &other) const { return pos_ < other.pos_; }
                bool operator>(const const_iterator &other) const { return pos_ > other.pos_; }
                bool operator<=(const const_iterator &other) const { return pos_ <= other.pos_; }
                bool operator>=(const const_iterator &other) const { return pos_ >= other.pos_; }

                reference operator[](difference_type n) const
                {
                    return indices_ ? (*data_)[(*indices_)[pos_ + n]] : (*data_)[pos_ + n];
                }

            private:
                const TSample *data_;
                const std::vector<size_t> *indices_;
                size_t pos_;
            };

            // const迭代器接口
            const_iterator begin() const
            {
                if (!this->GetMapPtr())
                    return const_iterator(nullptr, nullptr, 0);
                return const_iterator(
                    this->GetMapPtr().get(),
                    is_subset_ ? &subset_indices_ : nullptr,
                    0);
            }

            const_iterator end() const
            {
                if (!this->GetMapPtr())
                    return const_iterator(nullptr, nullptr, 0);
                return const_iterator(
                    this->GetMapPtr().get(),
                    is_subset_ ? &subset_indices_ : nullptr,
                    size());
            }

            const_iterator cbegin() const { return begin(); }
            const_iterator cend() const { return end(); }

            // 增加其他vector-like接口
            const_reference front() const
            {
                assert(!empty());
                return (*this)[0];
            }

            const_reference back() const
            {
                assert(!empty());
                return (*this)[size() - 1];
            }

        public:
            std::vector<size_t> subset_indices_; // 子样本索引
            bool is_subset_ = false;             // 是否是子集

        private:
            std::shared_ptr<std::vector<size_t>> best_inliers_ptr_; ///< 最佳内点索引的共享指针
            mutable SamplingCache sampling_cache_;                  ///< 采样缓存（mutable允许在const方法中修改）

        public:
            /**
             * @brief 获取最佳内点索引
             * @return 最佳内点索引的共享指针
             */
            std::shared_ptr<std::vector<size_t>> GetBestInliers() const
            {
                return best_inliers_ptr_;
            }

            /**
             * @brief 设置最佳内点索引
             * @param inliers_ptr 最佳内点索引的共享指针
             */
            void SetBestInliers(std::shared_ptr<std::vector<size_t>> inliers_ptr)
            {
                best_inliers_ptr_ = inliers_ptr;
            }

            /**
             * @brief 检查是否有最佳内点索引
             * @return 是否有最佳内点索引
             */
            bool HasBestInliers() const
            {
                return best_inliers_ptr_ != nullptr && !best_inliers_ptr_->empty();
            }
        };

        // 辅助函数定义
        template <typename T>
        using DataSamplePtr = Ptr<DataSample<T>>;

        template <typename T>
        DataSamplePtr<T> CastToSample(const DataPtr &data)
        {
            return std::dynamic_pointer_cast<DataSample<T>>(data);
        }

        //------------------------------------------------------------------------------
        // DataCosts
        //------------------------------------------------------------------------------
        /**
         * @brief 代价值数据类，用于存储模型评估的代价值
         * @details 提供类似 std::vector 的接口，实现零拷贝访问
         */
        class DataCosts : public DataIO
        {
        public:
            using value_type = double;
            using size_type = std::size_t;
            using reference = value_type &;
            using const_reference = const value_type &;
            using iterator = std::vector<value_type>::iterator;
            using const_iterator = std::vector<value_type>::const_iterator;

            DataCosts() = default;
            virtual ~DataCosts() = default;

            /**
             * @brief 获取数据类型名称
             */
            const std::string &GetType() const override
            {
                static const std::string type = "data_costs";
                return type;
            }

            /**
             * @brief 获取原始数据指针
             */
            void *GetData() override
            {
                return static_cast<void *>(&costs_);
            }

            // Vector-like 接口
            reference operator[](size_type pos) { return costs_[pos]; }
            const_reference operator[](size_type pos) const { return costs_[pos]; }

            void push_back(const value_type &value) { costs_.push_back(value); }
            void push_back(value_type &&value) { costs_.push_back(std::move(value)); }

            template <typename... Args>
            reference emplace_back(Args &&...args)
            {
                return costs_.emplace_back(std::forward<Args>(args)...);
            }

            // 迭代器支持
            iterator begin() noexcept { return costs_.begin(); }
            const_iterator begin() const noexcept { return costs_.begin(); }
            iterator end() noexcept { return costs_.end(); }
            const_iterator end() const noexcept { return costs_.end(); }
            const_iterator cbegin() const noexcept { return costs_.cbegin(); }
            const_iterator cend() const noexcept { return costs_.cend(); }

            // 容量操作
            bool empty() const noexcept { return costs_.empty(); }
            size_type size() const noexcept { return costs_.size(); }
            void reserve(size_type new_cap) { costs_.reserve(new_cap); }
            void resize(size_type count) { costs_.resize(count); }
            void clear() noexcept { costs_.clear(); }

            // 获取底层vector的引用（如果需要）
            const std::vector<value_type> &data() const noexcept { return costs_; }
            std::vector<value_type> &data() noexcept { return costs_; }

        private:
            std::vector<value_type> costs_; ///< 存储代价值的容器
        };

        /** @brief DataCosts类的智能指针类型定义 */
        using DataCostsPtr = Ptr<DataCosts>;

        //------------------------------------------------------------------------------
        // RobustEstimator （鲁棒估计器基类，不同框架通过后续派生实现）
        //------------------------------------------------------------------------------
        /**
         * @brief 鲁棒估计器基类
         * @tparam TSample 样本数据类型
         */
        class RobustEstimator : public MethodPresetProfiler
        {

        public:
            RobustEstimator()
            {
                // 1. 设置默认的方法类型
                method_options_["model_estimator_type"] = "user_defined_model_estimator";
                method_options_["cost_evaluator_type"] = "user_defined_cost_evaluator";

                required_package_["data_sample"] = nullptr;
            }

            void Initialize()
            {
                // 获取当前配置的方法类型
                const std::string &model_estimator_type = method_options_["model_estimator_type"];
                const std::string &cost_evaluator_type = method_options_["cost_evaluator_type"];

                // 1. 分别创建估计器和评估器
                if (model_estimator_ptr_ == nullptr)
                {
                    model_estimator_ptr_ = std::dynamic_pointer_cast<MethodPreset>(FactoryMethod::Create(model_estimator_type));
                }

                // 应用存储的配置（如果有）
                if (method_configs_.find(model_estimator_type) != method_configs_.end())
                {
                    model_estimator_ptr_->SetMethodOptions(method_configs_[model_estimator_type]);
                }

                if (cost_evaluator_ptr_ == nullptr)
                {
                    cost_evaluator_ptr_ = std::dynamic_pointer_cast<MethodPreset>(FactoryMethod::Create(cost_evaluator_type));
                }

                // 应用存储的配置（如果有）
                if (method_configs_.find(cost_evaluator_type) != method_configs_.end())
                {
                    cost_evaluator_ptr_->SetMethodOptions(method_configs_[cost_evaluator_type]);
                }
            }

            void SetModelEstimatorOptions(const MethodOptions &options)
            {
                // 获取当前配置的模型估计器类型
                const std::string &model_estimator_type = method_options_["model_estimator_type"];

                // 存储选项，即使model_estimator_ptr_尚未创建
                method_configs_[model_estimator_type] = options;

                // 如果model_estimator_ptr_已经创建，也立即应用选项
                if (model_estimator_ptr_ != nullptr)
                {
                    model_estimator_ptr_->SetMethodOptions(options);
                }
            }

            void SetCostEvaluatorOptions(const MethodOptions &options)
            {
                // 获取当前配置的代价评估器类型
                const std::string &cost_evaluator_type = method_options_["cost_evaluator_type"];

                // 存储选项，即使cost_evaluator_ptr_尚未创建
                method_configs_[cost_evaluator_type] = options;

                // 如果cost_evaluator_ptr_已经创建，也立即应用选项
                if (cost_evaluator_ptr_ != nullptr)
                {
                    cost_evaluator_ptr_->SetMethodOptions(options);
                }
            }

            /**
             * @brief 简化后的构建函数
             * @details 初始化估计器和直接调用Run
             */
            DataPtr Build(const DataPtr &material_ptr = nullptr) override
            {
                try
                {
                    // 1. 创建估计器和评估器(如果没有提前创建，则初始化创建)
                    Initialize();

                    // 2. 运行前的参数一致性检查
                    CheckParameterConsistency();

                    // 3. 使用现有的CheckInput机制检查和分配数据
                    if (!CheckInput(material_ptr))
                    {
                        return nullptr;
                    }

                    // 4. 调用实际的Run函数
                    return MethodPresetProfiler::Build(material_ptr);
                }
                catch (const std::exception &e)
                {
#ifdef _DEBUG
                    std::cerr << "RobustEstimator::Build failed: " << e.what() << std::endl;
#else
                    std::cerr << "RobustEstimator::Build failed" << std::endl;
#endif
                    return nullptr;
                }
            }

        protected:
            /**
             * @brief 从required_package_中获取总体样本数据
             * @return 样本数据指针
             * @throws std::runtime_error 如果找不到样本数据或存在多个样本数据
             */
            DataPtr GetPopulationPtr() const
            {
                const std::string type_name = "data_sample";
                const auto &required_package = GetRequiredPackage();

                // 查找样本数据
                auto it = required_package.find(type_name);
                if (it == required_package.end() || !it->second)
                {
                    std::cerr << "No valid sample data found in required_package" << std::endl;
                    throw std::runtime_error("No valid sample data found in required_package");
                }

                return it->second;
            }

            /**
             * @brief 检查模型估计器和代价评估器的参数一致性
             * @details 检查identify_mode和residual_type参数是否一致，不一致时发出警告
             */
            void CheckParameterConsistency()
            {
                try
                {
                    if (!model_estimator_ptr_ || !cost_evaluator_ptr_)
                    {
                        return; // 如果估计器未初始化，跳过检查
                    }

                    // 获取模型估计器的identify_mode参数
                    const auto &model_options = model_estimator_ptr_->GetMethodOptions();
                    auto identify_it = model_options.find("identify_mode");

                    // 获取代价评估器的residual_type参数
                    const auto &cost_options = cost_evaluator_ptr_->GetMethodOptions();
                    auto residual_it = cost_options.find("residual_type");

                    // 如果两个参数都存在，检查一致性
                    if (identify_it != model_options.end() && residual_it != cost_options.end())
                    {
                        const std::string &identify_mode = identify_it->second;
                        const std::string &residual_type = residual_it->second;

                        // 使用boost::iequals进行忽略大小写的比较
                        if (!boost::iequals(identify_mode, residual_type))
                        {
                            PO_LOG_WARNING << "[" << GetType() << "] 参数不一致警告: "
                                           << "identify_mode='" << identify_mode
                                           << "' 与 residual_type='" << residual_type
                                           << "' 不匹配，可能导致精度下降" << std::endl;
                        }
                        else if (log_level_ >= PO_LOG_VERBOSE)
                        {
                            PO_LOG(PO_LOG_VERBOSE) << "[" << GetType() << "] 参数一致性检查通过: "
                                                   << "identify_mode=residual_type='" << identify_mode << "'" << std::endl;
                        }
                    }
                    else if (log_level_ >= PO_LOG_VERBOSE)
                    {
                        // 输出调试信息，说明哪些参数不存在
                        if (identify_it == model_options.end())
                        {
                            PO_LOG(PO_LOG_VERBOSE) << "[" << GetType() << "] 模型估计器未配置identify_mode参数" << std::endl;
                        }
                        if (residual_it == cost_options.end())
                        {
                            PO_LOG(PO_LOG_VERBOSE) << "[" << GetType() << "] 代价评估器未配置residual_type参数" << std::endl;
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    PO_LOG_WARNING << "[" << GetType() << "] 参数一致性检查失败: " << e.what() << std::endl;
                }
            }

            /**
             * @brief 获取最佳内点索引
             * @return 最佳内点索引的共享指针
             */
            std::shared_ptr<std::vector<size_t>> GetBestInliers() const
            {
                return std::make_shared<std::vector<size_t>>(best_inliers_);
            }

        protected:
            // 存储内点索引
            std::vector<size_t> best_inliers_;

            // 存储模型估计器和评估器
            MethodPresetPtr model_estimator_ptr_{nullptr};
            MethodPresetPtr cost_evaluator_ptr_{nullptr};

            // 存储估计器的参数配置[model_estimator_type, cost_evaluator_type]
            MethodsConfig method_configs_;
        };

        /** @brief RobustEstimator类的智能指针类型定义 */
        using RobustEstimatorPtr = std::shared_ptr<RobustEstimator>;

    } // namespace Interface
} // namespace PoSDK

#endif // POMVG_INTERFACES_ROBUST_ESTIMATOR_HPP_
