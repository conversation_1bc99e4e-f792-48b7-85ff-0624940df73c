/**
 * @file method_relative_cost.hpp
 * @brief 相对位姿残差评估器
 * @details 支持多种残差计算方法，用于评估相对位姿估计的质量
 *
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _METHOD_RELATIVE_COST_HPP_
#define _METHOD_RELATIVE_COST_HPP_

#include "interfaces_preset_profiler.hpp"
// #include "relative_residuals.hpp"
#include "types.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;
    using namespace Eigen;

    /**
     * @brief 相对位姿残差评估器
     * @details 提供多种残差计算方法，支持通过配置文件选择使用哪种残差函数
     */
    class MethodRelativeCost : public Interface::MethodPresetProfiler
    {
    public:
        MethodRelativeCost();
        ~MethodRelativeCost() override = default;

        DataPtr Run() override;

        const std::string &GetType() const override
        {
            static const std::string type = "method_relative_cost";
            return type;
        }

    private:
        /**
         * @brief 根据方法名称选择并调用相应的残差函数
         * @param v1 第一组视图的单位向量
         * @param v2 第二组视图的单位向量
         * @param pose 相对位姿
         * @param weights 可选的权重向量
         * @return 每对点的残差值
         */
        VectorXd ComputeResiduals(
            const BearingVectors &v1,
            const BearingVectors &v2,
            const RelativePose &pose,
            const VectorXd *weights = nullptr);

        /**
         * @brief 从BearingPairs中提取两组BearingVectors
         * @param points1 输出的第一组向量
         * @param points2 输出的第二组向量
         * @return 是否成功提取
         */
        bool ExtractBearingVectors(
            BearingVectors &points1,
            BearingVectors &points2);
    };

} // namespace PoSDK

#endif // _METHOD_RELATIVE_COST_HPP_
