[method_relative_cost]
# 性能分析说明
ProfileCommit=相对位姿残差评估配置       # 配置修改说明

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估

# 日志和调试选项
log_level=0              # 日志级别: 2=详细, 1=正常, 0=无

# 残差计算方法
# 支持以下残差类型：
# - sampson: Sampson误差(默认)
# - coplanar: 共面误差
# - kneip: Kneip残差
# - ba: 捆绑调整残差
# - opengv: OpenGV残差
# - ppo: PPO残差
# - ppog: PPOG残差
# - ppo_invd: PPO反深度残差
# - ppo_bvc_invd: PPO BVC反深度残差
# - ppo_cross: PPO叉积残差
# - ppo_bva_invd: PPO BVA反深度残差
# - ligt_direct: LiGT直接残差
# - ligt_d3: LiGT D3残差
# - ligt: LiGT残差
# - lirt: LiRT残差
residual_type=ppo

# 权重设置
use_weights=false        # 是否使用权重向量


# 视图对信息
view_i=0                 # 第一个视图的索引
view_j=1                 # 第二个视图的索引
