[visual_simulator]
# 性能分析说明
ProfileCommit=视觉模拟器配置，用于生成合成多视图数据

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估
log_level=1         # 日志级别0;1;2

disp_summary=false      # 是否显示仿真摘要

# 场景参数
num_views=3              # 视图数量
num_points=1000          # 3D点数量
noise_level=1.0          # 像素噪声水平
image_width=1920         # 图像宽度
image_height=1080        # 图像高度
focal_length=1000        # 焦距
max_points_per_view=5000 # 每个视图最大点数
closest_points=500       # 选择最近点的数量

# 噪声设置
gaussian_noise=false     # 是否使用高斯噪声(true: 高斯噪声, false: 方向性噪声)
PRO_ratio=0.0            # 纯旋转异常值比例，范围[0,1] - Pure Rotational Outlier ratio
obs_outlier_ratio=0.0    # 观测点异常值比例，范围[0,1] - 向观测点坐标添加噪声的比例

# 结构类型设置
structure_type=close_loop_circle  # 结构类型(close_loop_circle或其他工厂创建的类型)
num_circles=1            # 环形数量(仅用于close_loop_circle结构)
dt_setting=5.0           # 位移量(仅用于close_loop_circle结构)
baseline_z_bias=5.0      # 基线Z轴偏移量(仅用于close_loop_circle结构)

# 输出设置
save_gt_info=false       # 是否保存真值信息
simu_folder=simulated_data  # 模拟数据存储目录
export_meshlab_path=     # meshlab工程导出路径，为空则不导出

# 相机信息
camera_make=PoSDK        # 相机制造商
camera_model=Simulator   # 相机型号

# 其他设置
random_seed=0            # 随机种子
