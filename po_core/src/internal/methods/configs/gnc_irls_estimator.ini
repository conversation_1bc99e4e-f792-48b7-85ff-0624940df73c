[gnc_irls_estimator]

# 性能分析说明
ProfileCommit=全局特征观测异常处理配置

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估
log_level=1              # 日志级别: 2=详细, 1=正常, 0=无

# GNC-IRLS算法最大迭代次数
max_iterations = 20
# 噪声边界系数
noise_scale = 5.54
# 收敛阈值
convergence_threshold = 1e-10
# GNC参数增长率
gamma = 1.4
# 是否使用Majorize优化
use_majorization = true
# 是否使用超线性更新
use_superlinear = true

# 缩放参数计算模式
sigma_mode = 2
# 内点判定阈值
inlier_threshold = 5*1e-3

# 质量验证控制
enable_quality_validation = true  # 是否启用基于GNC指示器的质量验证

# 调试输出控制
echo_on_costdiff = false  # 是否输出代价差异调试信息

# 模型估计器类型
model_estimator_type = method_model_estimator
# 代价评估器类型
cost_evaluator_type = method_cost_evaluator 
