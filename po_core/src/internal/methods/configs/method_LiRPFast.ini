[method_LiRPFast]
# 性能分析说明
ProfileCommit=LiRP相对位姿估计配置       # 配置修改说明
# 性能分析选项
enable_profiling=false    # 是否启用性能分析
enable_evaluator=true    # 是否启用评估

# 日志和调试选项
log_level=2             # 日志级别: 2=详细, 1=正常, 0=无

# 计算模式选项
compute_mode=true         # 解选择策略(true: 智能混合策略, false: 全局穷举策略)
                         # true: 前两组解(6+6)用RT_Check选择策略(evec_mode=false)，后两组解(3+3)用最小值策略(evec_mode=true)
                         # false: 所有解都用穷举所有R|t组合找最小残差策略(evec_mode=true)，计算量更大但更彻底

use_median_cost=true     # 代价函数选择(true:使用中位数，false:使用总和)
                         # 中位数对外点更鲁棒，总和对内点精度更敏感

# 残差计算方法
identify_mode=opengv_c        # 残差计算方法选择: PPO, PPOG, PPObvcInvd, opengv_c, sampson
use_fast_mode=false       # 是否使用快速模式(true: 使用快速模式, false: 使用标准模式)
rt_check_pts=20          # RT检查时使用的点数，默认值将在ConfigureAlgorithmParams中设置

# RT_Check函数类型选择
rt_check_type=RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED #SOTA：RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED
  # RT_Check函数类型选择: RT_CHECK2, RT_CHECK2_SUM_OPTIMIZED, RT_CHECK2_STACK_OPTIMIZED, RT_CHECK2_DIRECT_OPTIMIZED, RT_CHECK2_PRECOMPUTED_SUM, RT_CHECK2_SIMD_OPTIMIZED, RT_CHECK2_ULTRA_OPTIMIZED, RT_CHECK2_MATRIX_FREE_OPTIMIZED, RT_CHECK2_BARE_METAL_OPTIMIZED, RT_CHECK2_SAFE_ULTRA_OPTIMIZED, RT_CHECK2_FINAL_OPTIMIZED

# 视图对信息
view_i=0                 # 第一个视图的索引
view_j=1                 # 第二个视图的索引


