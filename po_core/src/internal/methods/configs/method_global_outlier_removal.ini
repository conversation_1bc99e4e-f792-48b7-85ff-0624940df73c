[method_global_outlier_removal]
# 性能分析说明
ProfileCommit=全局特征观测异常处理配置

# 性能分析选项
enable_profiling=true    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估

# 日志和调试选项
log_level=2              # 日志级别: 2=详细, 1=正常, 0=无
debug_output=false       # 是否输出调试信息

# 异常值检测方法
detection_method=threshold   # 检测方法: sigma(3sigma法则), mad(中位数绝对偏差), chi2(自适应卡方检验), threshold(直接阈值检测)
threshold_multiplier=2 # 阈值系数：用于 sigma 和 mad 法则，默认为3.0
standard_chi2_quantile=7.8147 # 标准卡方分位数：用于 chi2 法则，默认为3自由度/p=0.95对应值

# 直接阈值检测配置
direct_threshold_angle_error=1    # 角度误差模式下的阈值（度），调整后：1.0度 (原5.0)
direct_threshold_ligt_algebraic=1e-5 # LiGT代数误差模式下的阈值，调整后：1e-5 (原1e-4)
direct_threshold_medligt_algebraic=1e-5 # MedLiGT代数误差模式下的阈值，调整后：5e-6 (原1e-5)

# 残差计算方法
residual_method=MedLiGT     # 残差计算方法: LiGT(基于单对最佳基准), MedLiGT(基于每个观测作为基准的中位数残差范数)

# 异常值处理选项
remove_outliers=false     # 是否删除异常值(true)或仅标记它们(false)

# 残差输出选项
output_residuals_path="" # 残差输出文件路径(CSV格式,包含索引,残差,是否异常点标记)。为空则不输出。

# 角度误差模式
angle_error_mode=true      # 是否使用角度误差模式（true）还是代数误差模式（false，默认）

