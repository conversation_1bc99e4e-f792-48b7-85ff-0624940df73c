[ransac_estimator]
# 性能分析说明
ProfileCommit=RANSAC估计器配置       # 配置修改说明

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估
log_level=2              # 日志级别: 2=详细, 1=正常, 0=无


; RANSAC算法最大迭代次数
max_iterations = 50000


; 所需置信度 (0-1之间)
confidence = 0.99

; 内点判定阈值
inlier_threshold = 5*1e-3

; 最小样本集大小
min_sample_size = 8

; 模型估计器类型
model_estimator_type = method_LiRP

; 代价评估器类型
cost_evaluator_type = method_relative_cost

# ==================== 智能采样配置 ====================
; 采样策略选择:
; - uniform_random: 传统均匀随机采样（默认）
; - spherical_uniform: 球面均匀采样（适用于BearingPairs）
; - stratified: 分层采样（适用于大数据集）
; - adaptive: 自适应采样（根据数据类型自动选择）
sampling_strategy = spherical_uniform

; 球面采样参数（仅当sampling_strategy=spherical_uniform或adaptive时生效）
spherical_enable_cache = true        # 是否启用球面采样缓存
spherical_min_angular_separation = 0.1  # 最小角度间隔（弧度）

; 分层采样参数（仅当sampling_strategy=stratified时生效）
stratified_num_strata = auto         # 分层数量（auto=自动计算，或指定数值）
stratified_samples_per_stratum = auto # 每层采样数量（auto=均匀分配）

; 采样质量控制
enable_sampling_validation = true    # 是否启用采样质量验证
min_sample_diversity = 0.8          # 最小样本多样性（0-1）
max_sampling_attempts = 10          # 最大采样尝试次数
