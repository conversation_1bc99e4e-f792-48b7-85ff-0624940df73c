[method_robustLiRP]
# 性能分析说明
ProfileCommit=鲁棒LiRP相对位姿估计配置       # 配置修改说明

# 性能分析选项
enable_profiling=false    # 是否启用性能分析 
enable_evaluator=false    # 是否启用评估

# 日志和调试选项
log_level=2              # 日志级别: 2=详细, 1=正常, 0=无

# 鲁棒估计器选项
robust_type=ransac       # 鲁棒估计器类型: ransac 或 gnc_irls
cost_evaluator_type=method_relative_cost  # 代价评估器类型

# 两阶段优化选项
enable_two_stage_refinement=false  # 是否启用两阶段优化：RANSAC + GNC-IRLS
# 注意：仅当robust_type=ransac时生效，先用RANSAC筛选内点，再用GNC-IRLS精细优化

# 视图对信息
view_i=0                 # 第一个视图的索引
view_j=1                 # 第二个视图的索引


# LiRP参数 (作为模型估计器的参数)
lirp_compute_mode=true          # LiRP解选择策略(默认true: 智能混合策略, false: 全局穷举策略)
                                # true: 前两组解(6+6)用RT_Check选择策略(evec_mode=false)，后两组解(3+3)用最小值策略(evec_mode=true)
                                # false: 所有解都用穷举所有R|t组合找最小残差策略(evec_mode=true)，计算量更大但更彻底
lirp_use_opt_mode=false         # LiRP ProcessEvec版本选择(true: 优化版本, false: 标准版本)
                                
lirp_use_median_cost=true       # LiRP代价函数选择(true:使用中位数，false:使用总和)
                                # 中位数对外点更鲁棒，总和对内点精度更敏感
identify_mode=sampson         # 识别方法（相当于残差函数）: PPO, PPOG, PPObvcInvd, opengv_c, sampson, LiGT, LiRT


# 残差计算方法
# 支持以下残差类型：
# - sampson: Sampson误差(默认)
# - coplanar: 共面误差
# - kneip: Kneip残差
# - ba: 捆绑调整残差
# - opengv: OpenGV残差
# - ppo: PPO残差
# - ppog: PPOG残差
# - ppo_invd: PPO反深度残差
# - ppo_bvc_invd: PPO BVC反深度残差
# - ppo_cross: PPO叉积残差
# - ppo_bva_invd: PPO BVA反深度残差
# - ligt_direct: LiGT直接残差
# - ligt_d3: LiGT D3残差
# - ligt: LiGT残差
# - lirt: LiRT残差
residual_type=sampson

# ==================== 鲁棒估计器配置 ====================

[ransac_estimator]
# RANSAC估计器配置 （参数详情见ransac_estimator.ini）
max_iterations = 50000 # RANSAC最大迭代次数
min_iterations = 5 # RANSAC最小迭代次数
confidence = 0.99 # RANSAC置信度
inlier_threshold = 5*1e-3 # RANSAC内点阈值
min_sample_size = 6 # RANSAC最小样本数量

# 模型估计器和代价评估器类型
model_estimator_type = method_LiRP
cost_evaluator_type = method_relative_cost

[gnc_irls_estimator]
# GNC-IRLS估计器配置 (参数详情见gnc_irls_estimator.ini)
max_iterations = 20 # GNC-IRLS最大迭代次数
min_iterations = 1 # GNC-IRLS最小迭代次数
noise_scale = 1 # GNC-IRLS噪声尺度
convergence_threshold = 1e-10 # GNC-IRLS收敛阈值
gamma = 1.4 # GNC-IRLS gamma参数
use_majorization = false # GNC-IRLS是否使用主化函数
use_superlinear = false # GNC-IRLS是否使用超线性
sigma_mode = 0 # GNC-IRLS sigma模式
inlier_threshold = 5*1e-3 # GNC-IRLS内点阈值
enable_quality_validation = true # GNC-IRLS是否启用质量验证
track_best_model = false

# 模型估计器和代价评估器类型
model_estimator_type = method_LiRP
cost_evaluator_type = method_relative_cost 
