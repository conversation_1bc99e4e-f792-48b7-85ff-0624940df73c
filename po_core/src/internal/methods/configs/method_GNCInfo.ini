[method_GNCInfo]
# 性能分析说明
ProfileCommit=GNCInfo全局非凸优化相对位姿估计配置       # 配置修改说明
# 性能分析选项
enable_profiling=false    # 是否启用性能分析
enable_evaluator=false    # 是否启用评估

# 日志和调试选项
log_level=0              # 日志级别: 2=详细, 1=正常, 0=无
debug_info=false         # 是否输出调试信息
echo_on=false           # 是否回显处理信息

# ===== 核心算法选择 =====
# GNC算法选择 (核心功能)
gnc_method=GNC_RANSAC    # GNC方法: GNC_RANSAC, SHUFFLE_RANSAC, GNC_IRLS, MULTIPLE_GNC
                         # GNC_RANSAC: girls_6pts_LiGT标准GNC-RANSAC算法(核心)
                         # SHUFFLE_RANSAC: shuffle_ransac随机打乱RANSAC算法(核心)
                         # GNC_IRLS: GNC迭代重加权最小二乘
                         # MULTIPLE_GNC: 多重GNC优化

# 残差函数选择
residual_function=LIGT_RESIDUAL  # 残差函数: LIGT_RESIDUAL, ANGULAR_RESIDUAL, SAMPSON_RESIDUAL, SYMMETRIC_RESIDUAL
                                 # LIGT_RESIDUAL: LiGT残差函数(推荐用于girls_6pts_LiGT)
                                 # ANGULAR_RESIDUAL: 角度残差函数
                                 # SAMPSON_RESIDUAL: Sampson残差函数
                                 # SYMMETRIC_RESIDUAL: 对称残差函数

# 距离函数选择
gnc_dist_func=residual_PPO       # 距离函数: residual_PPO, opengv_c, PPOG

# ===== girls_6pts_LiGT 核心参数 =====
# 基础算法参数
inlier_threshold=0.005   # 内点阈值(弧度), 默认5e-3, girls_6pts_LiGT关键参数
max_gncransac_niter=50   # GNC-RANSAC最大迭代次数, girls_6pts_LiGT核心参数
gnc_samplesize=30        # GNC采样大小, girls_6pts_LiGT核心参数
confidence=0.99          # 置信度

# 噪声模式设置
sigma_mode=0             # 噪声模式: 0=自动, 1=手动, 2=自适应, girls_6pts_LiGT重要参数

# 求解模式
solve_mode=eigen_solve   # 求解模式: eigen_solve, pizarro, pizarro2等
identify_mode=PPO        # 识别模式: PPO, opengv_c, PPOG
compute_mode=true        # 计算模式开关

# ===== shuffle_ransac 核心参数 =====
# Shuffle RANSAC特定参数
num_shuffle_attempts=100 # Shuffle尝试次数, shuffle_ransac核心参数
shuffle_sample_ratio=0.8 # Shuffle采样比例
num_min_score_ids=20     # 最小得分ID数量, shuffle_ransac重要参数

# ===== GNC优化参数 =====
# GNC特定参数
gnc_mu_initial=1.0       # GNC初始μ值
gnc_mu_step=1.4          # GNC μ步长
gnc_max_mu=1e6           # GNC最大μ值
gnc_convergence_th=1e-6  # GNC收敛阈值

# IRLS参数
irls_max_iterations=20   # IRLS最大迭代次数
irls_convergence_th=1e-6 # IRLS收敛阈值

# ===== 质量控制参数 =====
# 质量控制参数
enable_quality_check=true    # 是否启用质量检查
min_inlier_ratio=0.1        # 最小内点比例
max_reprojection_error=1.0  # 最大重投影误差
med_or_sum=true             # 中位数或求和模式: true=中位数, false=求和

# ===== 高级参数 =====
# 采样控制
max_sample_checks=10     # 最大采样检查次数
prefix_3dpts=0          # 3D点前缀数量

# 缓冲区设置
num_buffer_ids=20       # 缓冲区ID数量

# TLS参数
tls_max_iter=10         # TLS最大迭代次数
tls_bounder=3.0         # TLS边界值
tls_stop_threshold=1e-10 # TLS停止阈值
tls_superlinear=1       # TLS超线性模式
tls_majorize=1          # TLS主要化模式

# 视图对信息
view_i=0                # 第一个视图的索引
view_j=1                # 第二个视图的索引

# 退化检查
enable_degeneracy_check=true    # 是否启用退化检查
enable_final_refinement=true    # 是否启用最终精化
refinement_iterations=5         # 精化迭代次数 