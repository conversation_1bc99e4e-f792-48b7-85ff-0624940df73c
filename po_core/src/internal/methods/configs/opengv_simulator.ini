[opengv_simulator]
# 性能分析说明
ProfileCommit=OpenGV双视图仿真器配置       # 配置修改说明
# 性能分析选项
enable_profiling=false    # 是否启用性能分析
enable_evaluator=false    # 是否启用评估

# 日志和调试选项
debug_output=false        # 是否输出调试信息
log_level=1              # 日志级别: 2=详细, 1=正常, 0=无

# 场景生成参数
max_parallax=2.0         # 最大视差(米)
max_rotation=0.5         # 最大旋转角度(弧度)
num_points=100           # 生成的特征点数量
noise_level=0.0          # 噪声水平(像素)
outlier_fraction=0.0     # 外点比例(0.0-1.0)

# 相机参数
focal_length=1000.0      # 焦距(像素)
image_width=640.0        # 图像宽度(像素)
image_height=480.0       # 图像高度(像素)

# 随机数种子
random_seed=42           # 随机数种子，用于可重复的仿真结果

# 视图对信息
view_i=0                 # 第一个视图的索引
view_j=1                 # 第二个视图的索引
