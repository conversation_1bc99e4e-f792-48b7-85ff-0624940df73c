[TwoViewOptimizer]
# ==============================================
# 双视图位姿优化器配置
# ==============================================
ProfileCommit=TwoViewOptimizer双视图位姿精细优化配置

# ==============================================
# 性能分析和日志配置
# ==============================================
enable_profiling=false    # 性能分析开关：true=启用详细性能统计，false=关闭
enable_evaluator=false    # 结果评估开关：true=启用误差评估和质量检查，false=关闭
log_level=0               # 日志详细级别：0=无输出，1=基本信息，2=详细调试信息
iter_echo_on=false # 迭代信息显示：true=显示每次迭代的残差和cost信息，false=关闭

# ==============================================
# 优化器配置
# ==============================================
optimizer_type=eigen_lm   # 优化器类型：eigen_lm（推荐），gauss_newton，dog_leg，ceres
max_iterations=100       # 优化最大迭代次数（增加以便充分优化）
convergence_threshold=1e-8 # 收敛判定阈值（更严格的阈值以获得更好的优化效果）

# ==============================================
# 残差函数和损失函数配置
# ==============================================
residual_type=ppo_opengv  # 残差函数类型：ppo_opengv（推荐），ppo，sampson，coplanar，kneip，ba等
loss_type=l2          # 鲁棒损失函数：l2（L2范数），huber（Huber损失），cauchy（Cauchy损失）

# ==============================================
# 损失函数参数
# ==============================================
reproj_theta_threshold_deg=0.57  # 重投影角度误差阈值（度），用于自动计算损失函数参数

# 显式损失函数阈值（可选，如果设置则覆盖自动计算值）
huber_threshold_explicit=-1      # Huber损失函数显式阈值（-1表示使用自动计算）
cauchy_threshold_explicit=-1     # Cauchy损失函数显式阈值（-1表示使用自动计算）

# 兼容性参数（已废弃，推荐使用上述显式参数或自动计算）
huber_threshold=0.01     # Huber损失函数阈值（已废弃，建议使用huber_threshold_explicit）

# ==============================================
# Eigen LevenbergMarquardt优化器专用参数（针对5参数优化调整）
# ==============================================
eigen_lm_ftol=1e-8       # 函数值收敛容忍度（更严格以获得更好的优化效果）
eigen_lm_xtol=1e-8       # 参数收敛容忍度（更严格以获得更好的优化效果）
eigen_lm_maxfev=2000     # 最大函数评估次数（增加以充分优化）

# ==============================================
# 视图对配置
# ==============================================
view_i=0                 # 参考视图索引（通常为0）
view_j=1                 # 目标视图索引（通常为1）

# ==============================================
# 算法说明
# ==============================================
# 
# *** 5参数位姿优化 ***
# 本优化器使用5参数位姿表示：
#   - 3个Cayley参数表示旋转
#   - 2个平移参数（t1, t2），第三个分量t3通过||t||=1约束计算
# 这种参数化避免了平移向量尺度不可观测性，提高了优化稳定性。
#
# *** 重投影角度阈值说明 ***
# reproj_theta_threshold_deg 参数控制损失函数的鲁棒性阈值：
#   - 该参数表示容忍的重投影角度误差（度），超过此阈值的点将被损失函数降权
#   - 基于此角度阈值，系统会自动计算对应残差函数的具体阈值：
#     * ppo_opengv: 使用弦距离 2*sin(θ/2)
#     * ppo_angle: 使用角度误差 1-cos(θ)
#     * ligt: 使用正弦函数 sin(θ)
#   - 推荐值：0.57度（对应约0.01的弦距离误差）
#   - 较小值：更严格的异常点检测（如0.3度）
#   - 较大值：更宽松的异常点容忍（如1.0度）
#
# *** 收敛状态码说明 ***
# Eigen LM优化器的成功收敛包括以下状态：
#   - RelativeReductionTooSmall (1): 相对误差减少太小
#   - RelativeErrorTooSmall (2): 相对误差太小  
#   - RelativeErrorAndReductionTooSmall (3): 相对误差和减少都太小
#   - CosinusTooSmall (4): 梯度太小
#   - FtolTooSmall (6): 函数容忍度太小
#   - XtolTooSmall (7): 参数容忍度太小
# 状态码7 (XtolTooSmall) 是正常的成功收敛状态！
#
# ==============================================
# residual_type可选值说明：
#   推荐选项：
#   - ppo_opengv: PPO OpenGV残差（6D残差向量，专为精细优化设计，推荐）
#   - ppo: 标准PPO残差（标量残差）
#   其他选项：
#   - sampson: Sampson残差（基于本质矩阵的几何残差）
#   - coplanar: 共面残差（基于本质矩阵约束）  
#   - kneip: Kneip残差（基于角度几何的残差）
#   - ba: Bundle Adjustment残差（重投影误差，需要pose+3D点联合优化，推荐ceres）
#   - opengv: OpenGV标准残差（角度误差，需要pose+3D点联合优化，推荐ceres）
#   - ligt: Linear Global Translation残差
#   - lirt: Linear Rotation Translation残差
#   - ppog: PPOG残差
#   - ppo_invd: PPO逆深度残差
#   - ligt_direct: LiGT直接残差
#   - ligt_d3: LiGT 3D残差
#
# *** 重要说明 ***：
# ba和opengv残差函数需要进行pose+3D点的联合优化，因为它们的参数空间
# 包括相机位姿和三维点坐标。对于这两种残差：
#   - ceres优化器：支持完整的Bundle Adjustment优化
#   - eigen_lm优化器：暂时使用pose-only优化（可能精度有限）
#
# optimizer_type可选值说明：
#   - eigen_lm: Eigen LevenbergMarquardt优化器（推荐，速度快且稳定）
#     * 支持pose-only优化，适合大部分残差类型
#     * 对于ba/opengv残差会给出警告并使用pose-only模式
#   - ceres: Ceres优化器（完整实现，支持Bundle Adjustment）
#     * 自动检测ba/opengv残差并启用Bundle Adjustment模式
#     * 支持pose+3D点联合优化，精度更高
#     * 推荐用于ba和opengv残差类型
#   - gauss_newton: Gauss-Newton优化器（TODO: 待实现）
#   - dog_leg: Dog Leg优化器（TODO: 待实现）
#
# loss_type可选值说明：
#   - l2: 标准L2范数损失（对异常值敏感）
#   - huber: Huber损失（推荐，对异常值鲁棒）
#   - cauchy: Cauchy损失（对严重异常值鲁棒） 