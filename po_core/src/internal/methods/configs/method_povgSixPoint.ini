[method_povgSixPoint]
# ==============================================
# PoVG六点算法相对位姿估计配置
# ==============================================
ProfileCommit=PoVG六点算法+TwoViewOptimizer双阶段相对位姿估计配置

# ==============================================
# 性能分析和日志配置
# ==============================================
enable_profiling=false    # 性能分析开关：true=启用详细性能统计，false=关闭
enable_evaluator=true     # 结果评估开关：true=启用误差评估和质量检查，false=关闭
log_level=2               # 日志详细级别：0=无输出，1=基本信息，2=详细调试信息

# ==============================================
# PoVG六点算法可执行文件配置（核心配置，请勿修改）
# ==============================================
povg_bin_path=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/Darwin-arm64-Release/povg_sixpt_algorithm
# PoVG六点算法可执行文件完整路径（必填项）
# 注意：此路径需要根据实际编译输出目录调整

temp_dir=/tmp             # 临时文件存储目录
                         # macOS/Linux推荐：/tmp
                         # Windows推荐：C:/temp

# ==============================================
# PoVG六点算法核心参数（保持不变，后续用户自行调整）
# ==============================================
solve_mode=eigen_solve_main  # 求解算法模式（详见文档末尾可选值说明）
inlier_threshold=5e-3     # 内点判定阈值（单位：弧度或归一化距离）
sigma_mode=0              # 噪声模型选择：0=固定阈值，1=自适应MAD，2=自适应中位数
max_gncransac_niter=50    # GNC-RANSAC最大迭代次数
gnc_samplesize=20         # GNC采样大小（每次迭代的样本数量）
computeMode=true          # 计算模式：true=高效模式，false=彻底模式
med_or_sum=true           # 残差聚合方式：true=中位数（鲁棒），false=总和（精确）
identify_mode=PPO         # 残差计算方法：PPO, PPOG, opengv_c等
gnc_residual_func=residual_PPO   # GNC使用的残差函数类型
gnc_dist_func=residual_PPO       # GNC使用的距离函数类型

# ==============================================
# 视图对配置
# ==============================================
view_i=0                  # 参考视图索引（通常为0）
view_j=1                  # 目标视图索引（通常为1）

# ==============================================
# 两阶段优化配置
# ==============================================
enable_two_stage_refinement=false  # 两阶段优化开关
                                   # false: 仅使用PoVG六点算法结果
                                   # true: PoVG六点算法 + TwoViewOptimizer精细优化

# ==============================================
# TwoViewOptimizer精细优化参数（仅当两阶段优化启用时生效）
# ==============================================
optimizer_type=eigen_lm   # 优化器类型：eigen_lm（推荐），gauss_newton，dog_leg，ceres
max_iterations=50         # 优化最大迭代次数
convergence_threshold=1e-8 # 收敛判定阈值（参数变化量）
optimizer_coster=ppo      # 精细优化使用的残差函数：ppo, ppo_opengv（推荐）, ba, sampson
loss_type=l2              # 鲁棒损失函数：l2（L2范数），huber（Huber损失），cauchy（Cauchy损失）
huber_threshold=0.01      # Huber损失函数阈值（仅当loss_type=huber时生效）

# Eigen LevenbergMarquardt优化器专用参数
eigen_lm_ftol=1e-8       # 函数值收敛容忍度
eigen_lm_xtol=1e-8       # 参数收敛容忍度  
eigen_lm_maxfev=500      # 最大函数评估次数

# ==============================================
# 系统和调试配置
# ==============================================
cleanup_temp_files=true  # 自动清理临时文件：true=执行后删除，false=保留用于调试
output_precision=16      # 数值输出精度（小数点后位数）

# ==============================================
# 算法选项详细说明
# ==============================================
# solve_mode可选值（基于sixpt_algorithm.cpp）：
#   推荐使用：
#   - gncransac_6pts_LiGT: GNC-RANSAC六点LiGT算法（默认推荐）
#   - girls_6pts_LiGT: GNC六点LiGT算法
#   其他可选：
#   - eigen_solve: 特征值求解方法
#   - pizarro/pizarro2/pizarro_real: Pizarro系列方法
#   - lirp_6pts_esols: LiRP六点特征解
#   - girls_6pts_BA/girls_6pts_opengv/girls_6pts_PPO: GNC六点变种
#   - girls_6pts_coplanar: GNC六点共面算法
#   - mms_tls_LiRP: MMS-TLS LiRP方法
#
# optimizer_coster可选值（用于TwoViewOptimizer）：
#   - ppo_opengv: PPO OpenGV残差（推荐用于精细优化）
#   - ppo/ppog: 标准PPO系列残差
#   - sampson: Sampson残差
#   - coplanar: 共面残差
#   - kneip: Kneip残差
#   - ba: Bundle Adjustment残差 