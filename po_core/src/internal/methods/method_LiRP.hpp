/**
 * @file method_LiRP.hpp
 * @brief LiRP相对位姿估计器
 * @details 使用LiRP(Linear Rotation and Position)算法实现相对位姿估计
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _METHOD_LIRP_HPP_
#define _METHOD_LIRP_HPP_

#include "interfaces_preset_profiler.hpp"
#include "interfaces_robust_estimator.hpp"

#include "types.hpp"
#include "relative_pose.hpp"
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <unsupported/Eigen/Polynomials>

#include "Spectra/SymEigsShiftSolver.h"
#include "Spectra/MatOp/SparseSymShiftSolve.h"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;
    using namespace Eigen;
    using namespace Spectra;

    // 定义BearingPairs类型
    using BearingPairs = std::vector<Matrix<double, 6, 1>>;

    class MethodLiRP : public Interface::MethodPresetProfiler
    {
    public:
        MethodLiRP();
        ~MethodLiRP() override = default;

        DataPtr Run() override;

        const std::string &GetType() const override
        {
            static const std::string type = "method_LiRP";
            return type;
        }

    private:
        // 主要求解函数
        bool SolveLiRPMain(
            const BearingVectors &points1,
            const BearingVectors &points2,
            Matrix3d &R,
            Vector3d &t,
            const VectorXd *weights = nullptr);

        // 辅助函数
        bool CheckRotation(const Matrix3d &R) const;
        bool CheckTranslation(const Vector3d &t) const;

        // 使用固定大小的eigen_sols类型
        using EigenSols = Matrix<double, 3, 9>;

        bool Solve(const EigenSols &eigen_sols,
                   const Matrix<double, 9, 10> &B,
                   MatrixXd &Evec_real,
                   bool flags_keep_real = false);

        // 从本质矩阵恢复R,t
        void essential2RT(const Matrix3d &E,
                          std::vector<Matrix3d> &R_candidates,
                          std::vector<Vector3d> &t_candidates) const;

        /**
         * @brief 将bearing pairs转换为两组bearing vectors 【注意：在PoSDK中，Xj对应的是points1，Xi是points2，跟opengv顺序是反的】
         * @param[out] points1 第一组bearing vectors
         * @param[out] points2 第二组bearing vectors
         * @return 转换是否成功
         */
        bool ConvertBearingPairsToBearingVectors(
            BearingVectors &points1,
            BearingVectors &points2) const;

    private:
        // 内部参数
        static constexpr double kEpsilon = 1e-10; // 数值精度阈值
        static constexpr int kMinNumPoints = 6;   // 最少需要的点对数

        // 预分配的内存缓冲区（避免动态分配）
        mutable Matrix<double, 9, 9> ATA_buffer_;
        mutable Matrix<double, 9, 9> eigenvectors_buffer_;
        mutable Vector<double, 9> eigenvalues_buffer_;
        mutable Matrix<double, 9, 3> null_space_buffer_;

        // 求解过程中的固定大小矩阵缓冲区
        mutable Matrix<double, 6, 6, ColMajor> C1_buffer_;
        mutable Matrix<double, 6, 6, ColMajor> C2_buffer_;
        mutable Matrix<double, 3, 3, ColMajor> C3_buffer_;
        mutable Matrix<double, 4, 6, ColMajor> M_buffer_;
        mutable Matrix<double, 9, 15> Evec_buffer_;
        mutable Matrix<double, 9, 10> coeff_mat_buffer_;
        mutable Matrix<double, 1, 4> coeff_D_mat_buffer_;

        // 优化的核心计算函数
        bool SolveLiRPMainOptimized(
            const BearingVectors &points1,
            const BearingVectors &points2,
            Matrix3d &R,
            Vector3d &t,
            const VectorXd *weights = nullptr) const;

        // 直接计算ATA矩阵，避免存储A矩阵
        void ComputeATADirectly(
            const BearingVectors &points1,
            const BearingVectors &points2,
            const VectorXd *weights,
            Matrix<double, 9, 9> &ATA) const;

        // 优化的特征值求解
        bool SolveEigenOptimized(
            const Matrix<double, 9, 9> &ATA,
            Matrix<double, 9, 3> &null_space) const;

        // 优化版本的Solve函数
        bool SolveOptimized(const EigenSols &eigen_sols,
                            const Matrix<double, 9, 10> &B,
                            MatrixXd &Evec_real,
                            bool flags_keep_real = false) const;
    };

} // namespace PoSDK

#endif // _METHOD_LIRP_HPP_
