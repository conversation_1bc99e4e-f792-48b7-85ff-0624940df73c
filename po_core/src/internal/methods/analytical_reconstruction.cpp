/**
 * @file analytical_reconstruction.cpp
 * @brief 解析式三角化重建方法实现
 * @details 基于全局位姿进行快速三角化重建3D点云
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#include "analytical_reconstruction.hpp"
#include "pb_dataio.hpp"
#include <iostream>
#include <fstream>
#include <iomanip>
#include <chrono>
#include <algorithm>
#include <cmath>
#include <interfaces.hpp>

namespace PoSDK
{
    namespace methods
    {
        using namespace std::chrono;

        void AnalyticalReconstruction::LoadParameters()
        {
            PO_LOG(PO_LOG_NORMAL) << "加载解析式重建配置参数" << std::endl;

            // 从配置中读取参数
            m3_ratio_ = GetOptionAsFloat("m3_ratio", 0.0);
            min_num_observations_per_point_ = GetOptionAsIndexT("min_num_observations_per_point", 2);

            // 重建模式
            std::string mode_str = GetOptionAsString("reconstruction_mode", "normal");
            if (mode_str == "normal")
            {
                reconstruction_mode_ = ReconstructionMode::NORMAL;
            }
            else if (mode_str == "remove_outliers")
            {
                reconstruction_mode_ = ReconstructionMode::REMOVE_OUTLIERS;
            }
            else if (mode_str == "robust_reconstruction")
            {
                reconstruction_mode_ = ReconstructionMode::ROBUST_RECONSTRUCTION;
            }

            // 文件路径
            tracks_file_ = GetOptionAsString("tracks_file", "");
            global_rotation_file_ = GetOptionAsString("global_rotation_file", "");
            global_translation_file_ = GetOptionAsString("global_translation_file", "");
            points_output_file_ = GetOptionAsString("points_output_file", "reconstructed_points.txt");
            outliers_output_file_ = GetOptionAsString("outliers_output_file", "outlier_ids.txt");

            PO_LOG(PO_LOG_VERBOSE) << "配置参数加载完成:" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "  m3_ratio: " << m3_ratio_ << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "  min_num_observations_per_point: " << min_num_observations_per_point_ << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "  reconstruction_mode: " << mode_str << std::endl;
        }

        DataPtr AnalyticalReconstruction::Run()
        {
            // 在运行开始时加载参数
            LoadParameters();

            PO_LOG(PO_LOG_NORMAL) << "开始解析式三角化重建" << std::endl;
            auto start_time = steady_clock::now();

            try
            {
                // 获取输入数据，参考method_LiGT的方式
                auto tracks_ptr = GetDataPtr<Tracks>(required_package_["data_tracks"]);
                auto global_poses_ptr = GetDataPtr<GlobalPoses>(required_package_["data_global_poses"]);
                auto camera_models_ptr = GetDataPtr<CameraModels>(required_package_["data_camera_models"]);

                if (!tracks_ptr || !global_poses_ptr)
                {
                    PO_LOG_ERR << "缺少必要的输入数据" << std::endl;
                    return nullptr;
                }

                // 确保位姿格式为RwTc（解析重建算法要求）
                if (global_poses_ptr->GetPoseFormat() != PoseFormat::RwTc)
                {
                    PO_LOG(PO_LOG_NORMAL) << "将位姿格式从 "
                                          << (global_poses_ptr->GetPoseFormat() == PoseFormat::RwTw ? "RwTw" : "其他")
                                          << " 转换为 RwTc" << std::endl;

                    if (!global_poses_ptr->ConvertPoseFormat(PoseFormat::RwTc))
                    {
                        PO_LOG_ERR << "位姿格式转换失败" << std::endl;
                        return nullptr;
                    }
                }

                PO_LOG(PO_LOG_NORMAL) << "输入数据统计:" << std::endl;
                PO_LOG(PO_LOG_NORMAL) << "  跟踪点数量: " << tracks_ptr->size() << std::endl;
                PO_LOG(PO_LOG_NORMAL) << "  视图数量: " << global_poses_ptr->Size() << std::endl;
                PO_LOG(PO_LOG_NORMAL) << "  位姿格式: RwTc" << std::endl;

                // 检查轨迹坐标是否已经归一化，如果没有，则使用相机模型进行归一化
                if (!tracks_ptr->IsNormalized() && camera_models_ptr)
                {
                    PO_LOG(PO_LOG_NORMAL) << "轨迹坐标未归一化，使用相机模型进行归一化处理..." << std::endl;

                    const CameraModels &camera_models = *camera_models_ptr;
                    size_t processed_obs_count = 0;

                    // 遍历每个Track
                    for (auto &track_info : *tracks_ptr)
                    {
                        if (!track_info.is_used)
                            continue;

                        // 遍历Track中的每个观测
                        for (auto &obs : track_info.track)
                        {
                            if (!obs.is_used)
                                continue;

                            // 获取对应视图的相机模型
                            const CameraModel *camera_model = GetCameraModel(camera_models, obs.view_id);
                            if (!camera_model)
                            {
                                PO_LOG_WARNING << "视图 " << obs.view_id << " 没有对应的相机模型" << std::endl;
                                continue;
                            }

                            // 将像素坐标转换为归一化坐标
                            Vector2d normalized_coord = camera_model->PixelToNormalized(obs.coord);
                            obs.coord = normalized_coord;
                            processed_obs_count++;
                        }
                    }

                    // 标记轨迹已归一化
                    tracks_ptr->SetNormalized(true);
                    PO_LOG(PO_LOG_NORMAL) << "坐标归一化完成，处理观测数: " << processed_obs_count << std::endl;
                }
                else if (!tracks_ptr->IsNormalized())
                {
                    PO_LOG_WARNING << "轨迹坐标未归一化，但未提供相机模型，可能影响重建精度" << std::endl;
                }

                // 根据模式执行不同的处理
                DataPtr result_data;

                switch (reconstruction_mode_)
                {
                case ReconstructionMode::NORMAL:
                {
                    // 普通重建模式
                    auto points_3d = std::make_shared<DataPoints3D>();
                    auto world_point_info = static_cast<WorldPointInfo *>(points_3d->GetData());
                    world_point_info->resize(tracks_ptr->size());

                    IndexT reconstructed_count = 0;
                    const IndexT total_points = tracks_ptr->size();

                    for (IndexT i = 0; i < total_points; ++i)
                    {
                        Point3d single_point;

                        if (ReconstructSinglePoint(*tracks_ptr, global_poses_ptr->rotations,
                                                   global_poses_ptr->translations, single_point, i))
                        {
                            world_point_info->setPoint(i, single_point);
                            world_point_info->setUsed(i, true);
                            ++reconstructed_count;
                        }
                        else
                        {
                            world_point_info->setUsed(i, false);
                        }

                        // 每1000个点输出一次进度
                        if ((i + 1) % 1000 == 0)
                        {
                            PO_LOG(PO_LOG_NORMAL) << "重建进度: " << (i + 1) << " / " << total_points << std::endl;
                        }
                    }

                    PO_LOG(PO_LOG_NORMAL) << "重建完成，成功重建点数: " << reconstructed_count
                                          << " / " << tracks_ptr->size() << std::endl;

                    result_data = points_3d;
                    break;
                }

                case ReconstructionMode::REMOVE_OUTLIERS:
                {
                    // 移除异常值模式
                    std::vector<IndexT> outlier_ids;
                    bool success = RemoveOutliers(*tracks_ptr, global_poses_ptr->rotations, outlier_ids);

                    if (success)
                    {
                        PO_LOG(PO_LOG_NORMAL) << "异常值检测完成，发现异常值: " << outlier_ids.size()
                                              << " / " << tracks_ptr->size() << std::endl;

                        // 保存异常值ID
                        if (!outliers_output_file_.empty())
                        {
                            std::ofstream outlier_file(outliers_output_file_);
                            for (IndexT id : outlier_ids)
                            {
                                outlier_file << id << std::endl;
                            }
                            outlier_file.close();
                            PO_LOG(PO_LOG_NORMAL) << "异常值ID已保存到: " << outliers_output_file_ << std::endl;
                        }
                    }

                    // 返回异常值ID列表（作为数据包装）
                    auto outlier_data = std::make_shared<DataMap<std::vector<IndexT>>>();
                    *static_cast<std::vector<IndexT> *>(outlier_data->GetData()) = outlier_ids;
                    result_data = outlier_data;
                    break;
                }

                case ReconstructionMode::ROBUST_RECONSTRUCTION:
                {
                    // 鲁棒重建模式
                    auto points_3d = std::make_shared<DataPoints3D>();
                    auto world_point_info = static_cast<WorldPointInfo *>(points_3d->GetData());
                    world_point_info->resize(tracks_ptr->size());

                    IndexT reconstructed_count = 0;
                    for (IndexT i = 0; i < tracks_ptr->size(); ++i)
                    {
                        Point3d single_point;
                        if (ReconstructSinglePoint(*tracks_ptr, global_poses_ptr->rotations,
                                                   global_poses_ptr->translations, single_point, i))
                        {
                            // 检查点是否可重建
                            bool is_valid = (single_point.norm() > 1e-10);
                            if (m3_ratio_ > 0.0)
                            {
                                is_valid = is_valid && CheckPointReconstructable(*tracks_ptr, global_poses_ptr->rotations, i);
                            }

                            world_point_info->setPoint(i, single_point);
                            world_point_info->setUsed(i, is_valid);

                            if (is_valid)
                            {
                                ++reconstructed_count;
                            }
                        }
                        else
                        {
                            world_point_info->setUsed(i, false);
                        }
                    }

                    PO_LOG(PO_LOG_NORMAL) << "鲁棒重建完成，成功重建点数: " << reconstructed_count
                                          << " / " << tracks_ptr->size() << std::endl;

                    result_data = points_3d;
                    break;
                }
                }

                auto end_time = steady_clock::now();
                auto duration = duration_cast<microseconds>(end_time - start_time);
                double time_cost = double(duration.count()) * microseconds::period::num / microseconds::period::den;

                PO_LOG(PO_LOG_NORMAL) << "解析式三角化重建完成，耗时: " << time_cost << "秒" << std::endl;

                return result_data;
            }
            catch (const std::exception &e)
            {
                PO_LOG_ERR << "解析式重建过程中发生异常: " << e.what() << std::endl;
                return nullptr;
            }
        }

        bool AnalyticalReconstruction::ReconstructSinglePoint(
            const Tracks &tracks,
            const GlobalRotations &global_rotations,
            const GlobalTranslations &global_translations,
            Point3d &world_point,
            const IndexT track_id) const
        {
            // 严格的输入验证
            if (track_id >= tracks.size())
            {
                PO_LOG_ERR << "ReconstructSinglePoint: track_id " << track_id << " >= tracks.size() " << tracks.size() << std::endl;
                return false;
            }

            if (global_rotations.empty() || global_translations.empty())
            {
                PO_LOG_ERR << "ReconstructSinglePoint: 空的位姿数据" << std::endl;
                return false;
            }

            const auto &track = tracks[track_id];
            const auto &observations = track.track;

            if (observations.size() < min_num_observations_per_point_)
            {
                world_point.setZero();
                return false;
            }

            // 验证观测中的view_id是否都在有效范围内
            for (const auto &obs : observations)
            {
                if (obs.view_id >= global_rotations.size() || obs.view_id >= global_translations.size())
                {
                    PO_LOG_ERR << "ReconstructSinglePoint: 观测view_id " << obs.view_id
                               << " 超出位姿数据范围 (" << global_rotations.size() << ", "
                               << global_translations.size() << ")" << std::endl;
                    world_point.setZero();
                    return false;
                }
            }

            world_point.setZero();
            double sum_s = 0.0;
            double max_s = 0.0;
            double scale = 1.0;

            try
            {
                // 对每对观测进行三角化（严格按照原始代码实现）
                for (size_t j = 0; j < observations.size() - 1; ++j)
                {
                    const ViewId left_view = observations[j].view_id;
                    const Vector3d x_l = observations[j].GetHomoCoord();
                    const Matrix3d &R_l = global_rotations[left_view];
                    const Vector3d &t_l = global_translations[left_view];

                    Vector3d Rl_xl = R_l.transpose() * x_l;
                    Vector3d Rl_tl = R_l.transpose() * t_l;

                    for (size_t k = j + 1; k < observations.size(); ++k)
                    {
                        const ViewId right_view = observations[k].view_id;
                        const Matrix3d &R_r = global_rotations[right_view];
                        const Vector3d &t_r = global_translations[right_view];
                        const Vector3d x_r = observations[k].GetHomoCoord();

                        Vector3d r_t = t_r - R_r * Rl_tl;

                        // 计算s（视差角）
                        Vector3d Rx = R_r * Rl_xl;
                        Vector3d xRx = x_r.cross(Rx);
                        double s = xRx.norm();
                        sum_s += s * s;

                        // 计算深度相关量
                        Vector3d tx_xr = r_t.cross(x_r);
                        double norm_tx_xr = tx_xr.norm();

                        // 按原始代码的方式处理scale和depth
                        s = s * scale;
                        double tmp_s = s;
                        max_s = std::max(max_s, tmp_s);

                        norm_tx_xr = norm_tx_xr * scale;
                        double depth = 1.0;

                        // 获取相机坐标点
                        Vector3d cam_coord = x_l * depth;

                        // 获取世界坐标点（严格按照原始公式）
                        world_point += R_l.transpose() * s * (norm_tx_xr * cam_coord - s * t_l);
                    }
                }

                // 检查m3_ratio条件（如果设置了的话）
                if (m3_ratio_ > 0.0 && m3_ratio_ > max_s)
                {
                    world_point.setZero();
                    return false;
                }

                // 最终归一化（严格按照原始代码）
                if (sum_s > 1e-10)
                {
                    world_point = world_point / sum_s / scale;
                    return true;
                }

                world_point.setZero();
                return false;
            }
            catch (const std::exception &e)
            {
                PO_LOG_ERR << "ReconstructSinglePoint: 重建过程中发生异常: " << e.what() << std::endl;
                return false;
            }
        }

        bool AnalyticalReconstruction::ReconstructSinglePointRobust(
            const Tracks &tracks,
            const GlobalRotations &global_rotations,
            const GlobalTranslations &global_translations,
            WorldPointInfo &world_point_info,
            const IndexT track_id) const
        {
            if (track_id >= tracks.size() || track_id >= world_point_info.size())
            {
                return false;
            }

            const auto &track = tracks[track_id];
            const auto &observations = track.track;

            if (observations.size() < min_num_observations_per_point_)
            {
                world_point_info.setUsed(track_id, false);
                return false;
            }

            // 先检查点是否可重建
            if (m3_ratio_ > 0.0 && !CheckPointReconstructable(tracks, global_rotations, track_id))
            {
                world_point_info.setUsed(track_id, false);
                return false;
            }

            // 执行重建
            Point3d single_point;
            bool success = ReconstructSinglePoint(tracks, global_rotations, global_translations, single_point, track_id);

            world_point_info.setPoint(track_id, single_point);
            bool is_valid = success && (single_point.norm() > 1e-10);
            world_point_info.setUsed(track_id, is_valid);

            return success;
        }

        bool AnalyticalReconstruction::CheckPointReconstructable(
            const Tracks &tracks,
            const GlobalRotations &global_rotations,
            const IndexT track_id) const
        {
            if (track_id >= tracks.size())
            {
                return false;
            }

            const auto &track = tracks[track_id];
            const auto &observations = track.track;
            double max_s = 0.0;

            // 检查所有观测对的视差角（严格按照原始代码实现）
            for (size_t j = 0; j < observations.size() - 1; ++j)
            {
                const ViewId left_view = observations[j].view_id;
                const Vector3d x_l = observations[j].GetHomoCoord();
                const Matrix3d &R_l = global_rotations[left_view];

                Vector3d Rl_xl = R_l.transpose() * x_l;

                for (size_t k = j + 1; k < observations.size(); ++k)
                {
                    const ViewId right_view = observations[k].view_id;
                    const Matrix3d &R_r = global_rotations[right_view];
                    const Vector3d x_r = observations[k].GetHomoCoord();

                    // 计算归一化的视差角（严格按照原始代码）
                    Vector3d Rx = R_r * Rl_xl;
                    Vector3d xRx = x_r.cross(Rx);
                    double s = xRx.norm();

                    // 关键：原始代码中有这个归一化步骤
                    s = s / x_r.norm() / Rx.norm();

                    max_s = std::max(max_s, s);
                }
            }

            return max_s > m3_ratio_;
        }

        bool AnalyticalReconstruction::RemoveOutliers(
            const Tracks &tracks,
            const GlobalRotations &global_rotations,
            std::vector<IndexT> &outlier_track_ids) const
        {
            outlier_track_ids.clear();

            for (IndexT i = 0; i < tracks.size(); ++i)
            {
                if (!CheckPointReconstructable(tracks, global_rotations, i))
                {
                    outlier_track_ids.push_back(i);
                }
            }

            PO_LOG(PO_LOG_VERBOSE) << "检测到异常值跟踪点数量: " << outlier_track_ids.size()
                                   << " / " << tracks.size() << std::endl;

            return true;
        }

    } // namespace methods
} // namespace PoSDK
