/******************************************************************************
 * Author:   <PERSON>                                                    *
 * Contact:  <EMAIL>                                          *
 * License:  Copyright (c) 2013 <PERSON>, ANU. All rights reserved.      *
 *                                                                            *
 * Redistribution and use in source and binary forms, with or without         *
 * modification, are permitted provided that the following conditions         *
 * are met:                                                                   *
 * * Redistributions of source code must retain the above copyright           *
 *   notice, this list of conditions and the following disclaimer.            *
 * * Redistributions in binary form must reproduce the above copyright        *
 *   notice, this list of conditions and the following disclaimer in the      *
 *   documentation and/or other materials provided with the distribution.     *
 * * Neither the name of ANU nor the names of its contributors may be         *
 *   used to endorse or promote products derived from this software without   *
 *   specific prior written permission.                                       *
 *                                                                            *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"*
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE  *
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE *
 * ARE DISCLAIMED. IN NO EVENT SHALL ANU OR THE CONTRIBUTORS BE LIABLE        *
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL *
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR *
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER *
 * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT         *
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY  *
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF     *
 * SUCH DAMAGE.                                                               *
 ******************************************************************************/

#ifndef OPENGV_RANDOM_GENERATORS_HPP_
#define OPENGV_RANDOM_GENERATORS_HPP_

#include <stdlib.h>
#include <vector>
#include <Eigen/Eigen>

namespace opengv
{

void initializeRandomSeed();
Eigen::Vector3d generateRandomPoint( double maximumDepth, double minimumDepth );
Eigen::Vector3d generateRandomPointPlane();
Eigen::Vector3d addNoise( double noiseLevel, Eigen::Vector3d cleanPoint );
Eigen::Vector3d generateRandomTranslation( double maximumParallax );
Eigen::Vector3d generateRandomDirectionTranslation( double parallax );
Eigen::Matrix3d generateRandomRotation( double maxAngle );
Eigen::Matrix3d generateRandomRotation();

}

#endif /* OPENGV_RANDOM_GENERATORS_HPP_ */
