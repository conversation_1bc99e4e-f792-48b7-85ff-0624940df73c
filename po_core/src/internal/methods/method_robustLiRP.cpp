/**
 * @file method_robustLiRP.cpp
 * @brief 鲁棒LiRP相对位姿估计器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "method_robustLiRP.hpp"
#include "relative_pose.hpp"
#include <boost/algorithm/string.hpp>

namespace PoSDK
{

    MethodRobustLiRP::MethodRobustLiRP()
    {
        // 设置需要的输入数据包
        required_package_["data_sample"] = nullptr; // DataSample<BearingPairs>

        // 初始化默认配置路径
        InitializeDefaultConfigPath();
    }

    DataPtr MethodRobustLiRP::Run()
    {
        DisplayConfigInfo();
        // if (log_level_ >= PO_LOG_NORMAL)
        // {
        //     PO_LOG(PO_LOG_NORMAL) << "Run called. Displaying config if log_level is appropriate." << std::endl;
        //     DisplayConfigInfo();
        // }

        // 获取输入数据
        auto sample_ptr = required_package_.at("data_sample");
        if (!sample_ptr)
        {
            PO_LOG_ERR << "Invalid data sample" << std::endl;
            return nullptr;
        }

        // 检查匹配点对数量是否满足最低要求
        auto bearing_pairs_ptr = GetDataPtr<BearingPairs>(sample_ptr);
        if (!bearing_pairs_ptr || bearing_pairs_ptr->empty())
        {
            PO_LOG_ERR << "Empty bearing pairs data" << std::endl;
            return nullptr;
        }

        size_t total_matches = bearing_pairs_ptr->size();

        // 获取LiRP算法所需的最小样本数
        size_t min_samples = GetMinimumSamplesForLiRP();

        if (total_matches < min_samples)
        {
            PO_LOG_ERR << "Insufficient matches for LiRP algorithm: got "
                       << total_matches << ", need at least " << min_samples << std::endl;

            // 注意：BearingPairs中的元素是Eigen::Matrix<double, 6, 1>，没有is_inlier字段
            // 这里我们不能直接设置is_inlier，而是通过其他方式处理
            return nullptr;
        }

        // 注意：质量控制检查已统一由TwoViewEstimator的ValidateEstimationQuality处理
        // 这里只进行算法最基本的可行性检查

        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "LiRP algorithm check passed: "
                                  << "Total matches: " << total_matches
                                  << ", Min required: " << min_samples << std::endl;
        }

        // 从配置中获取鲁棒估计器类型
        const std::string robust_type_str = GetOptionAsString("robust_type");

        // 创建鲁棒估计器
        auto robust_estimator = CreateRobustEstimator(robust_type_str);
        if (!robust_estimator)
        {
            PO_LOG_ERR << "Failed to create robust estimator of type: " << robust_type_str << std::endl;
            return nullptr;
        }

        // 设置鲁棒估计器的输入数据
        robust_estimator->SetRequiredData(sample_ptr);

        // 使用PassingMethodOptions自动传递参数
        {
            PassingMethodOptions(*robust_estimator);

            // 设置模型估计器和代价评估器的选项
            auto lirp_options = ConfigureLiRPOptions();
            robust_estimator->SetModelEstimatorOptions(lirp_options);

            MethodOptions cost_options{
                {"residual_type", GetOptionAsString("residual_type")}};
            robust_estimator->SetCostEvaluatorOptions(cost_options);

            if (log_level_ >= PO_LOG_VERBOSE)
            {
                PO_LOG(PO_LOG_VERBOSE) << "使用PassingMethodOptions配置 " << robust_type_str
                                       << " 估计器完成" << std::endl;
            }
        }

        // 运行鲁棒估计
        PO_LOG(PO_LOG_NONE) << "Running robust estimation..." << std::endl;

        auto result = robust_estimator->Build();
        if (!result)
        {
            PO_LOG_ERR << "Robust estimation failed" << std::endl;
            return nullptr;
        }

        // 检查是否需要进行两阶段优化：RANSAC + GNC-IRLS
        bool enable_two_stage = GetOptionAsBool("enable_two_stage_refinement", false);

        if (enable_two_stage && boost::iequals(robust_type_str, "ransac"))
        {
            PO_LOG(PO_LOG_NORMAL) << "启用两阶段优化：RANSAC + GNC-IRLS..." << std::endl;

            // 获取RANSAC筛选出的内点
            auto sample_ptr_cast = std::dynamic_pointer_cast<DataSample<BearingPairs>>(sample_ptr);
            if (sample_ptr_cast && sample_ptr_cast->HasBestInliers())
            {
                auto ransac_inliers = sample_ptr_cast->GetBestInliers();
                if (ransac_inliers && ransac_inliers->size() >= 6) // GNC-IRLS至少需要6个点
                {
                    PO_LOG(PO_LOG_NORMAL) << "RANSAC找到 " << ransac_inliers->size()
                                          << " 个内点，开始GNC-IRLS精细优化..." << std::endl;

                    // 创建内点子集
                    auto inlier_subset = sample_ptr_cast->GetInlierSubset(*ransac_inliers);

                    // 创建GNC-IRLS估计器进行精细优化
                    auto gnc_estimator = CreateRobustEstimator("gnc_irls");
                    gnc_estimator->SetRequiredData(inlier_subset);

                    // 使用PassingMethodOptions传递参数
                    {
                        PassingMethodOptions(*gnc_estimator);

                        // 设置模型估计器和代价评估器的选项
                        auto lirp_options = ConfigureLiRPOptions();
                        gnc_estimator->SetModelEstimatorOptions(lirp_options);

                        MethodOptions cost_options{
                            {"residual_type", GetOptionAsString("residual_type")}};
                        gnc_estimator->SetCostEvaluatorOptions(cost_options);
                    }

                    // 运行GNC-IRLS优化
                    auto refined_result = gnc_estimator->Build();
                    if (refined_result)
                    {
                        PO_LOG(PO_LOG_NORMAL) << "两阶段优化完成：使用GNC-IRLS精细优化了RANSAC结果" << std::endl;
                        result = refined_result; // 使用精细优化的结果

                        // 获取GNC-IRLS优化后的内点信息并更新到原始DataSample
                        auto gnc_inlier_subset = std::dynamic_pointer_cast<DataSample<BearingPairs>>(inlier_subset);
                        if (gnc_inlier_subset && gnc_inlier_subset->HasBestInliers())
                        {
                            auto gnc_inliers = gnc_inlier_subset->GetBestInliers();
                            if (gnc_inliers && !gnc_inliers->empty())
                            {
                                // 将GNC-IRLS的内点索引映射回原始数据集
                                auto mapped_inliers = std::make_shared<std::vector<size_t>>();
                                for (size_t gnc_idx : *gnc_inliers)
                                {
                                    if (gnc_idx < ransac_inliers->size())
                                    {
                                        mapped_inliers->push_back((*ransac_inliers)[gnc_idx]);
                                    }
                                }

                                // 更新原始DataSample的内点信息为GNC-IRLS的结果
                                sample_ptr_cast->SetBestInliers(mapped_inliers);

                                PO_LOG(PO_LOG_NORMAL) << "GNC-IRLS找到 " << mapped_inliers->size()
                                                      << " 个精细优化内点" << std::endl;
                            }
                        }
                    }
                    else
                    {
                        PO_LOG_ERR << "GNC-IRLS精细优化失败，两阶段优化失败" << std::endl;
                        return nullptr; // 双视图估计失败，返回空结果
                    }
                }
                else
                {
                    PO_LOG_ERR << "RANSAC内点数量不足("
                               << (ransac_inliers ? ransac_inliers->size() : 0)
                               << ")，两阶段优化失败" << std::endl;
                    return nullptr; // 双视图估计失败，返回空结果
                }
            }
            else
            {
                PO_LOG_ERR << "未找到RANSAC内点信息，两阶段优化失败" << std::endl;
                return nullptr; // 双视图估计失败，返回空结果
            }
        }

        // 确保内点信息已经传递到原始DataSample
        auto sample_ptr_cast = std::dynamic_pointer_cast<DataSample<BearingPairs>>(sample_ptr);
        if (sample_ptr_cast && sample_ptr_cast->HasBestInliers())
        {
            auto best_inliers = sample_ptr_cast->GetBestInliers();
            PO_LOG(PO_LOG_VERBOSE) << "Robust estimation completed with "
                                   << (best_inliers ? best_inliers->size() : 0)
                                   << " inliers" << std::endl;
        }

        // 注意：质量验证已统一由TwoViewEstimator的ValidateEstimationQuality处理
        // 这里不再进行重复的质量检查，保持架构一致性

        // 处理并返回结果
        auto pose = GetDataPtr<RelativePose>(result);
        if (!pose)
        {
            PO_LOG_ERR << "Failed to get relative pose from result" << std::endl;
            return nullptr;
        }

        PO_LOG(PO_LOG_NONE) << "Robust estimation completed successfully" << std::endl;
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "Rotation matrix: \n"
                                  << pose->Rij << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Translation vector: \n"
                                  << pose->tij << std::endl;
        }

        return result;
    }

    RobustEstimatorPtr MethodRobustLiRP::CreateRobustEstimator(const std::string &robust_type_str)
    {
        RobustEstimatorPtr estimator = nullptr;

        if (boost::iequals(robust_type_str, "ransac"))
        {
            estimator = std::make_shared<RANSACEstimator<BearingPairs>>();
        }
        else if (boost::iequals(robust_type_str, "gnc_irls"))
        {
            estimator = std::make_shared<GNCIRLSEstimator<BearingPairs>>();
        }
        else
        {
            std::cerr << "[MethodRobustLiRP] Unsupported robust estimator type: " << robust_type_str << std::endl;
            return nullptr;
        }

        // 初始化鲁棒估计器配置路径
        InitializeDefaultConfigPath(estimator->GetType());
        return estimator;
    }

    MethodOptions MethodRobustLiRP::ConfigureLiRPOptions()
    {
        // 从配置中获取LiRP相关选项
        MethodOptions lirp_options{
            {"compute_mode", GetOptionAsString("lirp_compute_mode")},
            {"use_opt_mode", GetOptionAsString("lirp_use_opt_mode")},
            {"identify_mode", GetOptionAsString("identify_mode")},
            {"enable_profiling", GetOptionAsString("enable_profiling")},
            {"view_i", GetOptionAsString("view_i")},
            {"view_j", GetOptionAsString("view_j")}};
        return lirp_options;
    }

    // 辅助函数：获取LiRP算法所需的最小样本数
    size_t MethodRobustLiRP::GetMinimumSamplesForLiRP() const
    {
        // LiRP (Linear Relative Pose) 算法通常需要至少6个点对来求解相对位姿
        // 这是因为相对位姿有6个自由度（3个旋转 + 3个平移）
        // 但考虑到鲁棒估计的需要，我们设置更高的最小要求

        std::string robust_type = GetOptionAsString("robust_type", "ransac");

        if (boost::iequals(robust_type, "ransac"))
        {
            // RANSAC模式下，考虑到需要多次采样，设置较高的最小要求
            size_t ransac_min_sample_size = GetOptionAsIndexT("min_sample_size", 6);
            // 至少需要 min_sample_size * 3 个点来保证RANSAC有足够的采样空间
            return std::max(static_cast<size_t>(6), ransac_min_sample_size * 3);
        }
        else if (boost::iequals(robust_type, "gnc_irls"))
        {
            // GNC-IRLS模式下，需要足够的点来进行迭代优化
            return 12; // 至少12个点对
        }
        else
        {
            // 默认情况下，LiRP至少需要6个点对
            return 6;
        }
    }

} // namespace PoSDK
