#ifndef _METHOD_LIGT_
#define _METHOD_LIGT_

#include "interfaces_preset_profiler.hpp"
#include <iostream>
#include <stdlib.h>
#include "types.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    class MethodLiGT : public MethodPresetProfiler
    {
    public:
        const std::string &GetType() const override
        {
            static const std::string &type = "method_LiGT";
            return type;
        }

        MethodLiGT()
        {
            required_package_.insert({"data_tracks", nullptr});
            required_package_.insert({"data_global_poses", nullptr});
            required_package_.insert({"data_camera_models", nullptr});
        }

        virtual DataPtr Run() override;

    public:
        //    Est_Info est_info;
    };

}

#endif
