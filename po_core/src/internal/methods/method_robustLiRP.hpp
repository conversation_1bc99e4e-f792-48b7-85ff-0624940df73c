/**
 * @file method_robustLiRP.hpp
 * @brief 鲁棒LiRP相对位姿估计器
 * @details 使用RANSAC或GNC-IRLS结合LiRP算法进行鲁棒相对位姿估计
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _METHOD_ROBUST_LIRP_HPP_
#define _METHOD_ROBUST_LIRP_HPP_

#include "interfaces_preset_profiler.hpp"
#include "interfaces_robust_estimator.hpp"
#include "method_LiRP.hpp"
#include "ransac_estimator.hpp"
#include "gnc_irls.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    /**
     * @class MethodRobustLiRP
     * @brief 鲁棒LiRP方法，支持RANSAC和GNC-IRLS估计器
     * @details 该方法使用LiRP作为模型估计器，结合RANSAC或GNC-IRLS进行鲁棒相对位姿估计
     */
    class MethodRobustLiRP : public Interface::MethodPresetProfiler
    {
    public:
        MethodRobustLiRP();
        ~MethodRobustLiRP() override = default;

        /**
         * @brief 执行鲁棒LiRP估计
         * @return 估计结果，包含相对位姿
         */
        DataPtr Run() override;

        /**
         * @brief 获取方法类型
         * @return 方法类型字符串
         */
        const std::string &GetType() const override
        {
            static const std::string type = "method_robustLiRP";
            return type;
        }

    private:
        /**
         * @brief 创建鲁棒估计器
         * @param robust_type 鲁棒估计器类型
         * @return 创建的鲁棒估计器
         */
        RobustEstimatorPtr CreateRobustEstimator(const std::string &robust_type);

        /**
         * @brief 配置LiRP模型估计器选项
         * @return LiRP模型估计器选项
         */
        MethodOptions ConfigureLiRPOptions();

        /**
         * @brief 获取LiRP算法所需的最小样本数
         * @return 最小样本数
         */
        size_t GetMinimumSamplesForLiRP() const;
    };

} // namespace PoSDK

#endif // _METHOD_ROBUST_LIRP_HPP_