/**
 * @file method_LiRP.hpp
 * @brief LiRP相对位姿估计器
 * @details 使用LiRP(Linear Rotation and Position)算法实现相对位姿估计
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _METHOD_LIRPFAST_HPP_
#define _METHOD_LIRPFAST_HPP_

#include "interfaces_preset_profiler.hpp"
#include "interfaces_robust_estimator.hpp"

#include "types.hpp"
#include "relative_pose.hpp"
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <unsupported/Eigen/Polynomials>

#include "Spectra/SymEigsShiftSolver.h"
#include "Spectra/MatOp/SparseSymShiftSolve.h"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;
    using namespace Eigen;
    using namespace Spectra;

    // 定义BearingPairs类型
    using BearingPairs = std::vector<Matrix<double, 6, 1>>;

    class MethodLiRPFast : public Interface::MethodPresetProfiler
    {
    public:
        MethodLiRPFast();
        ~MethodLiRPFast() override = default;

        DataPtr Run() override;

        const std::string &GetType() const override
        {
            static const std::string type = "method_LiRPFast";
            return type;
        }

    private:
        // 辅助函数
        bool CheckRotation(const Matrix3d &R);
        bool CheckTranslation(const Vector3d &t);

        // 使用固定大小的eigen_sols类型
        using EigenSols = Matrix<double, 3, 9>;

        // 从本质矩阵恢复R,t
        void essential2RT(const Matrix3d &E,
                          vector<Matrix3d> &R,
                          vector<Vector3d> &t)
        {
            Matrix3d normalizedE = E / E.norm();

            Matrix3d W1;
            W1 << 0, -1, 0,
                1, 0, 0,
                0, 0, 1;

            Matrix3d W2;
            W2 << 0, -1, 0,
                1, 0, 0,
                0, 0, -1;

            JacobiSVD<Matrix3d> svd(normalizedE, ComputeFullU | ComputeFullV);
            Matrix3d U = svd.matrixU();
            Matrix3d V = svd.matrixV();

            R.resize(2);
            if (U.determinant() * V.determinant() > 0)
            {
                R[0] = U * W1 * V.transpose();
                R[1] = U * W1.transpose() * V.transpose();
            }
            else
            {
                R[0] = U * W2 * V.transpose();
                R[1] = U * W2.transpose() * V.transpose();
            }

            t.resize(2);
            t[0] = U.col(2);
            t[1] = -U.col(2);
        }

        /**
         * @brief 将bearing pairs转换为两组bearing vectors 【注意：在PoSDK中，Xj对应的是points1，Xi是points2，跟opengv顺序是反的】
         * @param[out] points1 第一组bearing vectors
         * @param[out] points2 第二组bearing vectors
         * @return 转换是否成功
         */
        bool ConvertBearingPairsToBearingVectors(
            BearingVectors &points1,
            BearingVectors &points2);

    private:
        // 算法参数结构体
        struct AlgorithmParams
        {
            std::string identify_mode;
            bool compute_mode;
            bool use_opt_mode;
            bool use_median_cost;
            bool use_fast_mode;
            std::string rt_check_type_str;
            RTCheckType rt_check_type;
            Eval_Residual_func residual_func;
            int rt_check_pts; // RT检查时使用的点数，默认值将在ConfigureAlgorithmParams中设置
        };

        // 内部参数
        static constexpr double kEpsilon = 1e-10; // 数值精度阈值
        static constexpr int kMinNumPoints = 6;   // 最少需要的点对数

        // 预分配的内存缓冲区（避免动态分配）
        mutable Matrix<double, 9, 9> ATA_buffer_;
        mutable double sum_A_[9] = {0.0};

        mutable Matrix<double, 9, 3> null_space_buffer_;

        // 求解过程中的固定大小矩阵缓冲区
        mutable Matrix<double, 6, 6, ColMajor> C1_buffer_;
        mutable Matrix<double, 6, 6, ColMajor> C2_buffer_;
        mutable Matrix<double, 3, 3, ColMajor> C3_buffer_;
        mutable Matrix<double, 4, 6, ColMajor> M_buffer_;
        mutable Matrix<double, 9, 6> Evec_buffer_; // 改为9x6矩阵
        mutable Matrix<double, 9, 10> coeff_mat_buffer_;
        mutable Matrix<double, 1, 4> coeff_D_mat_buffer_;

        // 算法参数（运行时配置）
        mutable AlgorithmParams params_;

        // 参数配置函数
        void ConfigureAlgorithmParams();

        // 优化的核心计算函数
        bool SolveLiRPMainOptimized(
            const BearingVectors &points1,
            const BearingVectors &points2,
            Matrix3d &R,
            Vector3d &t,
            const VectorXd *weights = nullptr);

        // 直接计算ATA矩阵，避免存储A矩阵
        void ComputeATADirectly(
            const BearingVectors &points1,
            const BearingVectors &points2,
            const VectorXd *weights,
            Matrix<double, 9, 9> &ATA);

        // 优化的特征值求解
        bool SolveEigenOptimized(
            const Matrix<double, 9, 9> &ATA,
            Matrix<double, 9, 3> &null_space);

        // 优化版本的Solve函数（直接保存到Evec_buffer_成员变量）
        bool SolveOptimized(const EigenSols &eigen_sols,
                            const Matrix<double, 9, 10> &B);

        // 辅助函数：找到VectorXd中最小的两个元素的索引
        vector<int> find_two_smallest_indices(const VectorXd &values)
        {
            vector<pair<double, int>> value_index_pairs;
            for (int i = 0; i < values.size(); ++i)
            {
                value_index_pairs.emplace_back(values(i), i);
            }

            // 按值排序，取前两个
            sort(value_index_pairs.begin(), value_index_pairs.end());

            vector<int> result;
            result.push_back(value_index_pairs[0].second);
            if (value_index_pairs.size() > 1)
            {
                result.push_back(value_index_pairs[1].second);
            }
            return result;
        }

        void ComputeSumA(const BearingVectors &v1,
                         const BearingVectors &v2,
                         const VectorXd *ptr_weights)
        {
            sum_A_[0] = 0.0;
            sum_A_[1] = 0.0;
            sum_A_[2] = 0.0;
            sum_A_[3] = 0.0;
            sum_A_[4] = 0.0;
            sum_A_[5] = 0.0;
            sum_A_[6] = 0.0;
            sum_A_[7] = 0.0;
            sum_A_[8] = 0.0;

            const int n = v1.cols();
            for (int i = 0; i < n; ++i)
            {

                // 计算sum_A - 正确的顺序：v2[i] ⊗ v1[i]
                const double w = ptr_weights ? (*ptr_weights)(i) : 1.0;
                const Vector3d &v1_i = v1.col(i);
                const Vector3d &v2_i = v2.col(i);

                const double w_v2_0 = w * v2_i(0), w_v2_1 = w * v2_i(1), w_v2_2 = w * v2_i(2);
                sum_A_[0] += w_v2_0 * v1_i(0);
                sum_A_[1] += w_v2_0 * v1_i(1);
                sum_A_[2] += w_v2_0 * v1_i(2);
                sum_A_[3] += w_v2_1 * v1_i(0);
                sum_A_[4] += w_v2_1 * v1_i(1);
                sum_A_[5] += w_v2_1 * v1_i(2);
                sum_A_[6] += w_v2_2 * v1_i(0);
                sum_A_[7] += w_v2_2 * v1_i(1);
                sum_A_[8] += w_v2_2 * v1_i(2);
            }
        }
        Matrix<double, 3, 4> process_Evec(const Matrix<double, 9, 9> &Evec,
                                          const BearingVectors &v1,
                                          const BearingVectors &v2,
                                          const VectorXd *ptr_weigths);

        RelativePose RT_Check2_bare_metal_optimized_corrected(const BearingVectors &v1,
                                                              const BearingVectors &v2,
                                                              const vector<Matrix3d> &R_sols,
                                                              const vector<Vector3d> &t_sols,
                                                              const VectorXd *ptr_weights,
                                                              double &ratio)
        {
            const int n = v1.cols();
            const int n_R = R_sols.size();

            // 评估旋转矩阵 - 使用计数而不是直接求和，与原版保持一致
            int best_rotation_count = -1;
            int best_R_idx = 0;

            for (int i = 0; i < n_R; ++i)
            {
                const Matrix3d &R = R_sols[i];
                const Vector3d &t = t_sols[i];

                // 内联计算 tx.transpose() * tx * R
                const double t0 = t(0), t1 = t(1), t2 = t(2);
                const double t0_sq = t0 * t0, t1_sq = t1 * t1, t2_sq = t2 * t2;

                // QtQ * R 直接计算，避免临时变量
                double QtQR_data[9];
                QtQR_data[0] = (t1_sq + t2_sq) * R(0, 0) + (-t0 * t1) * R(1, 0) + (-t0 * t2) * R(2, 0);
                QtQR_data[1] = (t1_sq + t2_sq) * R(0, 1) + (-t0 * t1) * R(1, 1) + (-t0 * t2) * R(2, 1);
                QtQR_data[2] = (t1_sq + t2_sq) * R(0, 2) + (-t0 * t1) * R(1, 2) + (-t0 * t2) * R(2, 2);
                QtQR_data[3] = (-t0 * t1) * R(0, 0) + (t0_sq + t2_sq) * R(1, 0) + (-t1 * t2) * R(2, 0);
                QtQR_data[4] = (-t0 * t1) * R(0, 1) + (t0_sq + t2_sq) * R(1, 1) + (-t1 * t2) * R(2, 1);
                QtQR_data[5] = (-t0 * t1) * R(0, 2) + (t0_sq + t2_sq) * R(1, 2) + (-t1 * t2) * R(2, 2);
                QtQR_data[6] = (-t0 * t2) * R(0, 0) + (-t1 * t2) * R(1, 0) + (t0_sq + t1_sq) * R(2, 0);
                QtQR_data[7] = (-t0 * t2) * R(0, 1) + (-t1 * t2) * R(1, 1) + (t0_sq + t1_sq) * R(2, 1);
                QtQR_data[8] = (-t0 * t2) * R(0, 2) + (-t1 * t2) * R(1, 2) + (t0_sq + t1_sq) * R(2, 2);

                // 计算 sum_A' * QtQR_data，然后统计>0的个数（与原版保持一致）
                double total_score = 0.0;
                for (int j = 0; j < 9; ++j)
                {
                    total_score += sum_A_[j] * QtQR_data[j];
                }

                // 使用符号函数统计>0的个数，而不是直接求和
                int count = (total_score > 0) ? 1 : 0;

                if (count > best_rotation_count)
                {
                    best_rotation_count = count;
                    best_R_idx = i;
                }
            }

            RelativePose result;
            result.Rij = R_sols[best_R_idx];
            const Matrix3d &R = result.Rij;

            // 评估平移向量 - 使用计数而不是直接求和
            int best_translation_count = -1;
            int best_t_idx = 0;

            for (int i = 0; i < 2; ++i)
            {
                const Vector3d &t = t_sols[i];
                const double t0 = t(0), t1 = t(1), t2 = t(2);

                int count = 0;

                for (int j = 0; j < n; ++j)
                {
                    const int idx = j * 3;

                    // R * x[j] - 内联矩阵向量乘法
                    const double Rx0 = R(0, 0) * v2(0, j) + R(0, 1) * v2(1, j) + R(0, 2) * v2(2, j);
                    const double Rx1 = R(1, 0) * v2(0, j) + R(1, 1) * v2(1, j) + R(1, 2) * v2(2, j);
                    const double Rx2 = R(2, 0) * v2(0, j) + R(2, 1) * v2(1, j) + R(2, 2) * v2(2, j);

                    // t × (R * x[j]) - 内联叉积
                    const double tmp1_0 = t1 * Rx2 - t2 * Rx1;
                    const double tmp1_1 = t2 * Rx0 - t0 * Rx2;
                    const double tmp1_2 = t0 * Rx1 - t1 * Rx0;

                    // xmatch[j] × (R * x[j]) - 内联叉积
                    const double xm0 = v1(0, j), xm1 = v1(1, j), xm2 = v1(2, j);
                    const double tmp2_0 = xm1 * Rx2 - xm2 * Rx1;
                    const double tmp2_1 = xm2 * Rx0 - xm0 * Rx2;
                    const double tmp2_2 = xm0 * Rx1 - xm1 * Rx0;

                    // t × xmatch[j] - 内联叉积
                    const double tmp3_0 = t1 * xm2 - t2 * xm1;
                    const double tmp3_1 = t2 * xm0 - t0 * xm2;
                    const double tmp3_2 = t0 * xm1 - t1 * xm0;

                    // 点积计算 - 内联，并统计>0的个数
                    const double dot1 = tmp1_0 * tmp2_0 + tmp1_1 * tmp2_1 + tmp1_2 * tmp2_2;
                    const double dot2 = tmp2_0 * tmp3_0 + tmp2_1 * tmp3_1 + tmp2_2 * tmp3_2;

                    count += (dot1 > 0 ? 1 : 0) + (dot2 > 0 ? 1 : 0);
                }

                if (count > best_translation_count)
                {
                    best_translation_count = count;
                    best_t_idx = i;
                }
            }

            result.tij = t_sols[best_t_idx];
            ratio = best_translation_count;

            return result;
        }

        double compute_cost(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                            const BearingVectors &v1,
                            const BearingVectors &v2,
                            const bool &evec_mode,
                            const VectorXd *ptr_weights,
                            Matrix<double, 3, 4> &out)
        {
            int numSolutions = Evec.cols();
            int num_matches = v1.cols();

            double selected_cost = 0;
            vector<double> cost(num_matches);
            VectorXd ratios(numSolutions);
            vector<Matrix3d> R_sol(numSolutions);
            vector<Vector3d> t_sol(numSolutions);
            VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
            vector<RelativePose> right_Rts;

            // 获取真值数据用于精度评估
            DataPtr gt_data = GetGTData();
            bool has_gt_data = (gt_data != nullptr);
            PO_LOG(PO_LOG_VERBOSE) << "[StandardModeAnalysis] has_gt_data " << has_gt_data << std::endl;
            if (log_level_ >= PO_LOG_VERBOSE && has_gt_data)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[StandardModeAnalysis] 开始E解精度分析 (E解数: " << numSolutions << ")" << std::endl;
            }

            // 遍历所有解
            for (int i = 0; i < numSolutions; ++i)
            {
                // 构建本质矩阵
                Matrix3d E = Matrix3d::Zero();
                for (int col = 0; col < 3; ++col)
                {
                    for (int row = 0; row < 3; ++row)
                    {
                        E(row, col) = Evec(row + 3 * col, i);
                    }
                }

                // 检查矩阵是否有效
                if (E.norm() < 1e-10)
                {
                    ratios(i) = 0;
                    total_cost[i] = 1e10;
                    continue;
                }

                // 归一化本质矩阵
                E /= E.norm();

                // 从本质矩阵恢复R和t
                essential2RT(E, R_sol, t_sol);

                // 选择最佳RT组合 - 根据rt_check_type动态选择RT_Check函数
                double ratio = 0;
                RelativePose right_pose;

                right_pose = RT_Check2_bare_metal_optimized_corrected(v1, v2, R_sol, t_sol, ptr_weights, ratio);
                ratios(i) = ratio;

                // 归一化平移向量
                right_pose.tij /= right_pose.tij.norm();

                // 计算残差
                auto tmp_cost = params_.residual_func(v1, v2, right_pose, ptr_weights);

                // 应用权重
                for (int k = 0; k < num_matches; ++k)
                {
                    cost[k] = ptr_weights ? (*ptr_weights)(k)*tmp_cost[k] : tmp_cost[k];
                }

                // 计算代价
                double med_cost = find_median_relative_pose(cost);
                double sum_cost = tmp_cost.sum();
                total_cost[i] = params_.use_median_cost ? med_cost : sum_cost;

                right_Rts.emplace_back(right_pose);

                // 精度评估（如果有真值数据）
                if (has_gt_data && log_level_ >= PO_LOG_VERBOSE)
                {
                    // 为评估设置视图对ID
                    right_pose.i = GetOptionAsIndexT("view_i", 0);
                    right_pose.j = GetOptionAsIndexT("view_j", 1);

                    auto temp_result = std::make_shared<DataMap<RelativePose>>(right_pose);
                    auto eval_status = temp_result->EvaluateRelativePose(gt_data);

                    double rotation_error = -1.0, translation_error = -1.0;
                    if (eval_status.is_successful)
                    {
                        auto rotation_errors = eval_status.GetResults("rotation_error");
                        auto translation_errors = eval_status.GetResults("translation_error");

                        if (!rotation_errors.empty() && !translation_errors.empty())
                        {
                            rotation_error = rotation_errors[0];
                            translation_error = translation_errors[0];
                        }
                    }

                    PO_LOG(PO_LOG_VERBOSE) << "  " << i << "-th E_sol, R err(deg)="
                                           << std::fixed << std::setprecision(6) << rotation_error
                                           << ", t err=" << std::fixed << std::setprecision(6) << translation_error
                                           << ", ratio=" << std::fixed << std::setprecision(4) << ratios(i)
                                           << ", cost=" << std::scientific << std::setprecision(4)
                                           << total_cost[i] << std::endl;
                }
            }

            // 选择最优解
            int selected_id;
            if (evec_mode)
            {
                total_cost.minCoeff(&selected_id);
            }
            else
            {
                vector<int> candidate_ids = find_two_smallest_indices(total_cost);
                double max_value = -1e5;
                for (int j = 0; j < candidate_ids.size(); ++j)
                {
                    int id = candidate_ids[j];
                    if (ratios(id) > max_value)
                    {
                        selected_id = id;
                        max_value = ratios(id);
                    }
                }
            }

            selected_cost = total_cost(selected_id);

            // 显示总结信息
            if (log_level_ >= PO_LOG_VERBOSE && has_gt_data)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[StandardModeAnalysis] summary: best_Esol_id = " << selected_id
                                       << ", best_cost = " << std::scientific << std::setprecision(4)
                                       << selected_cost << std::endl;
            }

            // 设置输出
            out.block<3, 3>(0, 0) = right_Rts[selected_id].Rij;
            out.col(3) = right_Rts[selected_id].tij;

            return selected_cost;
        }

        double compute_cost_fast(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                                 const BearingVectors &v1,
                                 const BearingVectors &v2,
                                 const VectorXd *ptr_weights,
                                 Matrix<double, 3, 4> &out)
        {
            const int numSolutions = Evec.cols();
            const int num_matches = v1.cols();

            double best_overall_cost = 1e10;
            RelativePose best_overall_pose;

            // 获取真值数据用于精度评估
            DataPtr gt_data = GetGTData();
            bool has_gt_data = (gt_data != nullptr);

            // 存储最佳解的标识
            int best_Esol_id = -1, best_Rsol_id = -1, best_Tsol_id = -1;

            vector<Matrix3d> R_sol(2); // 固定大小，essential2RT总是返回2个解
            vector<Vector3d> t_sol(2);

            if (log_level_ >= PO_LOG_VERBOSE && has_gt_data)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[FastModeAnalysis] 开始详细精度分析 (E解数: " << numSolutions << ")" << std::endl;
            }

            // 遍历所有解
            for (int i = 0; i < numSolutions; ++i)
            {
                // 构建本质矩阵
                Matrix3d E = Matrix3d::Zero();
                for (int col = 0; col < 3; ++col)
                {
                    for (int row = 0; row < 3; ++row)
                    {
                        E(row, col) = Evec(row + 3 * col, i);
                    }
                }

                // 检查矩阵是否有效
                if (E.norm() < 1e-10)
                {
                    if (log_level_ >= PO_LOG_VERBOSE)
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "  " << i << "-th E_sol: 无效矩阵 (norm < 1e-10)" << std::endl;
                    }
                    continue;
                }

                // 归一化本质矩阵
                E /= E.norm();

                // 从本质矩阵恢复R和t
                essential2RT(E, R_sol, t_sol);

                // 遍历所有可能的R和t组合 (2x2=4种组合)
                for (int j = 0; j < 2; ++j)
                {
                    const Matrix3d &R = R_sol[j];
                    for (int k = 0; k < 2; ++k)
                    {
                        Vector3d t = t_sol[k].normalized(); // 归一化平移向量

                        // 降采样优化：根据配置的rt_check_pts决定检查点数
                        const int effective_check_pts = std::min(params_.rt_check_pts, num_matches);
                        const int sampling_step = std::max(1, num_matches / effective_check_pts);

                        double total_cost = 0.0;
                        int checked_points = 0;

                        for (int idx = 0; idx < num_matches && checked_points < effective_check_pts; idx += sampling_step)
                        {
                            const Vector3d &X1 = v1.col(idx);
                            const Vector3d &X2 = v2.col(idx);

                            // 快速几何残差计算
                            Vector3d tmp2 = (R * X2).cross(X1);
                            Vector3d tmp3 = X1.cross(t);
                            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

                            double point_cost = min(0.01, (reproj_coord.normalized() - X1).norm());

                            // 应用权重（如果提供）
                            if (ptr_weights)
                            {
                                point_cost *= (*ptr_weights)(idx);
                            }

                            total_cost += point_cost;
                            ++checked_points;
                        }

                        // 归一化cost：按照实际检查的点数归一化，使其与全量计算可比较
                        if (checked_points > 0)
                        {
                            total_cost = total_cost * static_cast<double>(num_matches) / checked_points;
                        }

                        // 精度评估（如果有真值数据）
                        double rotation_error = -1.0, translation_error = -1.0;
                        if (has_gt_data && log_level_ >= PO_LOG_VERBOSE)
                        {
                            // 构建临时相对位姿对象用于评估
                            RelativePose temp_pose;
                            temp_pose.Rij = R;
                            temp_pose.tij = t;
                            temp_pose.i = GetOptionAsIndexT("view_i", 0);
                            temp_pose.j = GetOptionAsIndexT("view_j", 1);
                            temp_pose.weight = 1.0f;

                            auto temp_result = std::make_shared<DataMap<RelativePose>>(temp_pose);
                            auto eval_status = temp_result->EvaluateRelativePose(gt_data);

                            if (eval_status.is_successful)
                            {
                                auto rotation_errors = eval_status.GetResults("rotation_error");
                                auto translation_errors = eval_status.GetResults("translation_error");

                                if (!rotation_errors.empty() && !translation_errors.empty())
                                {
                                    rotation_error = rotation_errors[0];
                                    translation_error = translation_errors[0];
                                }
                            }

                            PO_LOG(PO_LOG_VERBOSE) << "  " << i << "-th E_sol, " << j << "-th Rsol, " << k
                                                   << "-th Tsol, R err(deg)=" << std::fixed << std::setprecision(6)
                                                   << rotation_error << ", t err=" << std::fixed << std::setprecision(6)
                                                   << translation_error << ", cost=" << std::scientific << std::setprecision(4)
                                                   << total_cost << std::endl;
                        }

                        // 更新全局最佳解
                        if (total_cost < best_overall_cost)
                        {
                            best_overall_cost = total_cost;
                            best_Esol_id = i;
                            best_Rsol_id = j;
                            best_Tsol_id = k;
                            out.block<3, 3>(0, 0) = R;
                            out.col(3) = t;
                        }
                    }
                }
            }

            // 显示总结信息
            if (log_level_ >= PO_LOG_VERBOSE && has_gt_data)
            {
                PO_LOG(PO_LOG_VERBOSE) << "[FastModeAnalysis] summary: best_Esol_id = " << best_Esol_id
                                       << ", best_Rsol_id = " << best_Rsol_id << ", best_Tsol_id = " << best_Tsol_id
                                       << ", best_cost = " << std::scientific << std::setprecision(4)
                                       << best_overall_cost << std::endl;
            }

            return best_overall_cost;
        }

        double compute_cost_rt(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                               const BearingVectors &v1,
                               const BearingVectors &v2,
                               const bool &evec_mode,
                               const VectorXd *ptr_weights,
                               Matrix<double, 3, 4> &out)
        {
            int numSolutions = Evec.cols();
            int num_matches = v1.cols();

            double selected_cost = 0;
            vector<double> cost(num_matches);
            vector<Matrix3d> R_sol(numSolutions);
            vector<Vector3d> t_sol(numSolutions);
            VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
            vector<RelativePose> right_Rts(numSolutions);

            // 遍历所有解
            for (int i = 0; i < numSolutions; ++i)
            {
                // 构建本质矩阵
                Matrix3d E = Matrix3d::Zero();
                for (int col = 0; col < 3; ++col)
                {
                    for (int row = 0; row < 3; ++row)
                    {
                        E(row, col) = Evec(row + 3 * col, i);
                    }
                }

                // 检查矩阵是否有效
                if (E.norm() < 1e-10)
                {
                    total_cost[i] = 1e10;
                    continue;
                }

                // 归一化本质矩阵
                E /= E.norm();

                // 从本质矩阵恢复R和t
                essential2RT(E, R_sol, t_sol);

                // 遍历所有可能的R和t组合
                double min_Rt_costs = 1e10;
                for (Matrix3d &R_candidate : R_sol)
                {
                    for (Vector3d &t_candidate : t_sol)
                    {
                        t_candidate = t_candidate / t_candidate.norm();
                        RelativePose tmp_pose;
                        tmp_pose.Rij = R_candidate;
                        tmp_pose.tij = t_candidate;

                        // 计算残差
                        auto tmp_cost = params_.residual_func(v1, v2, tmp_pose, ptr_weights);

                        // 应用权重
                        for (int k = 0; k < num_matches; ++k)
                        {
                            cost[k] = ptr_weights ? (*ptr_weights)(k)*tmp_cost[k] : tmp_cost[k];
                        }

                        // 计算代价
                        double med_cost = find_median_relative_pose(cost);
                        double sum_cost = tmp_cost.sum();
                        double Rt_cost = params_.use_median_cost ? med_cost : sum_cost;

                        // 更新最小代价解
                        if (Rt_cost < min_Rt_costs)
                        {
                            min_Rt_costs = Rt_cost;
                            right_Rts[i] = tmp_pose;
                        }
                    }
                }

                total_cost[i] = min_Rt_costs;
            }

            // 选择最优解
            int selected_id;
            total_cost.minCoeff(&selected_id);
            selected_cost = total_cost(selected_id);

            // 设置输出
            out.block<3, 3>(0, 0) = right_Rts[selected_id].Rij;
            out.col(3) = right_Rts[selected_id].tij;

            return selected_cost;
        }
    };

} // namespace PoSDK

#endif // _METHOD_LIRP_HPP_
