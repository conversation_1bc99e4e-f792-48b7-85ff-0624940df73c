/**
 * @file CeresOptimizer.hpp
 * @brief Ceres优化器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#ifndef PO_CORE_CERES_OPTIMIZER_HPP
#define PO_CORE_CERES_OPTIMIZER_HPP

#include "TwoViewOptimizerBase.hpp"
#include <ceres/ceres.h>
#include <ceres/rotation.h>

namespace PoSDK
{
    /**
     * @brief Ceres优化器
     */
    class CeresOptimizer : public TwoViewOptimizerBase
    {
    public:
        CeresOptimizer() = default;
        virtual ~CeresOptimizer() = default;

        /**
         * @brief 执行位姿优化
         * @param points1 第一个视图的观测向量
         * @param points2 第二个视图的观测向量
         * @param pose 输入初始位姿，输出优化后位姿
         * @param weights 权重向量（可为nullptr）
         * @param residual_type 残差函数类型
         * @param loss_type 损失函数类型
         * @return 是否优化成功
         */
        bool Optimize(
            const BearingVectors &points1,
            const BearingVectors &points2,
            RelativePose &pose,
            const VectorXd *weights = nullptr,
            const std::string &residual_type = "ppo_opengv",
            const std::string &loss_type = "huber") override;

    private:
        /**
         * @brief 仅优化位姿的方法
         */
        bool OptimizePoseOnly(
            const BearingVectors &points1,
            const BearingVectors &points2,
            const RelativePose &initial_pose,
            const VectorXd *weights,
            const std::string &residual_type,
            const std::string &loss_type,
            RelativePose &optimized_pose) const;

        /**
         * @brief 联合优化位姿和3D点的方法（BA和OpenGV）
         */
        bool OptimizePoseWith3DPoints(
            const BearingVectors &points1,
            const BearingVectors &points2,
            const RelativePose &initial_pose,
            const VectorXd *weights,
            const std::string &residual_type,
            const std::string &loss_type,
            RelativePose &optimized_pose) const;

        /**
         * @brief 获取Huber损失函数阈值
         */
        double GetHuberThreshold(const std::string &residual_type) const;

        /**
         * @brief 获取Cauchy损失函数阈值
         */
        double GetCauchyThreshold(const std::string &residual_type) const;

        /**
         * @brief 根据角度计算Huber阈值
         */
        double ComputeHuberThresholdFromAngle(const std::string &residual_type, double angle_deg) const;

        /**
         * @brief 根据角度计算Cauchy阈值
         */
        double ComputeCauchyThresholdFromAngle(const std::string &residual_type, double angle_deg) const;
    };

} // namespace PoSDK

#endif // PO_CORE_CERES_OPTIMIZER_HPP