/**
 * @file EigenLMOptimizer.cpp
 * @brief Eigen Levenberg-Marquardt优化器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "EigenLMOptimizer.hpp"
#include "relative_residuals.hpp"
#include "costor_info.hpp"
#include <iostream>
#include <limits>
#include <algorithm>
#include <cmath>
#include <unsupported/Eigen/LevenbergMarquardt>
#include <unsupported/Eigen/NumericalDiff>

// 三角化函数声明
extern Eigen::Vector3d triangulateOnePoint(const Eigen::Matrix3d &R, const Eigen::Vector3d &t,
                                           const Eigen::Vector3d &point1, const Eigen::Vector3d &point2);
extern Eigen::Vector3d triangulate2(const Eigen::Matrix3d &R, const Eigen::Vector3d &t,
                                    const Eigen::Vector3d &point1, const Eigen::Vector3d &point2);

// 注意：已移除huber_loss函数，现在直接使用Ceres标准损失函数实现

namespace PoSDK
{
    using types::CayleyToRotation;
    using types::RotationToCayley;

    // -----------------------------------------------------------------------------
    // EigenLMOptimizer实现
    // -----------------------------------------------------------------------------

    EigenLMOptimizer::EigenLMOptimizer()
    {
    }

    bool EigenLMOptimizer::Optimize(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "\n[TwoViewOptimizer] Starting Eigen LM optimization..." << std::endl;
            std::cout << "Residual type: " << residual_type << ", Loss type: " << loss_type << std::endl;
        }

        // 检查是否为BA或OpenGV残差（需要pose+3D点联合优化）
        bool use_bundle_adjustment = (residual_type == "ba" || residual_type == "opengv");

        if (use_bundle_adjustment)
        {
            if (log_level_ >= 1) // PO_LOG_NORMAL
            {
                std::cout << "错误: EigenLM优化器不支持Bundle Adjustment类型的残差优化" << std::endl;
                std::cout << "残差类型 '" << residual_type << "' 需要联合优化位姿和3D点" << std::endl;
                std::cout << "EigenLM没有针对稀疏Schur分解的实现，无法高效处理BA问题" << std::endl;
                std::cout << "请使用Ceres优化器进行Bundle Adjustment优化" << std::endl;
            }
            // 对于ba/opengv+eigenlm组合，直接返回失败
            return false;
        }
        else
        {
            if (log_level_ >= 1) // PO_LOG_NORMAL
            {
                std::cout << "Using Pose-only optimization mode" << std::endl;
            }
            return OptimizePoseOnly(points1, points2, pose, weights, residual_type, loss_type);
        }
    }

    // -----------------------------------------------------------------------------
    // TwoViewResidualFunctor实现
    // -----------------------------------------------------------------------------

    namespace
    {
        // 简化的残差函数计算器
        struct TwoViewResidualFunctor
        {
            typedef double Scalar;
            typedef Eigen::VectorXd InputType;
            typedef Eigen::VectorXd ValueType;
            typedef Eigen::MatrixXd JacobianType;
            typedef Eigen::ColPivHouseholderQR<JacobianType> QRSolver;

            enum
            {
                InputsAtCompileTime = Eigen::Dynamic,
                ValuesAtCompileTime = Eigen::Dynamic
            };

            int m_inputs, m_values;
            mutable int iteration_count_;
            mutable VectorXd prev_x_;
            mutable bool prev_x_initialized_;
            int log_level_;
            bool iter_echo_on_;

            int inputs() const { return m_inputs; }
            int values() const { return m_values; }

            TwoViewResidualFunctor(const BearingVectors &points1, const BearingVectors &points2,
                                   const VectorXd *weights, const std::string &residual_type,
                                   const std::string &loss_type, double reproj_theta_threshold_deg,
                                   double huber_threshold_explicit, double cauchy_threshold_explicit,
                                   bool iter_echo_on)
                : points1_(points1), points2_(points2), weights_(weights),
                  residual_type_(residual_type), loss_type_(loss_type),
                  reproj_theta_threshold_deg_(reproj_theta_threshold_deg),
                  huber_threshold_explicit_(huber_threshold_explicit),
                  cauchy_threshold_explicit_(cauchy_threshold_explicit),
                  iteration_count_(0), prev_x_initialized_(false),
                  iter_echo_on_(iter_echo_on)
            {
                // 获取残差信息
                const CostorInfoManager &manager = CostorInfoManager::GetInstance();
                int num_points = points1_.cols();
                int residual_dim = manager.GetResidualDimension(residual_type_, num_points);

                m_inputs = 5; // 5D参数空间
                m_values = residual_dim;

                // 计算损失函数参数
                huber_threshold_ = GetHuberThreshold();
                cauchy_threshold_ = GetCauchyThreshold();

                if (iter_echo_on_)
                {
                    std::cout << "残差类型: " << residual_type_ << std::endl;
                    std::cout << "损失类型: " << loss_type_ << std::endl;

                    // 安全地获取残差信息
                    const CostorInfo *info = manager.GetCostorInfo(residual_type_);
                    if (info)
                    {
                        std::cout << "残差维度: " << residual_dim << " (每点" << info->dimension_per_point << "维)" << std::endl;
                    }
                    else
                    {
                        std::cout << "残差维度: " << residual_dim << " (未知残差类型，将使用默认值)" << std::endl;
                    }

                    std::cout << "损失函数阈值: Huber=" << huber_threshold_ << ", Cauchy=" << cauchy_threshold_ << std::endl;
                }
            }

            int operator()(const VectorXd &x, VectorXd &fvec) const
            {
                iteration_count_++;

                // 从5个参数向量恢复位姿（球坐标系）
                double theta = x(0); // 天顶角
                double phi = x(1);   // 方位角

                // 球坐标系转换为笛卡尔坐标系
                double sin_theta = std::sin(theta);
                double cos_theta = std::cos(theta);
                double sin_phi = std::sin(phi);
                double cos_phi = std::cos(phi);

                Vector3d translation(sin_theta * cos_phi, sin_theta * sin_phi, cos_theta);
                Vector3d cayley = x.segment<3>(2);

                // 检查Cayley参数是否合理
                double cayley_norm = cayley.norm();
                if (cayley_norm > 3.14159)
                {
                    cayley = cayley * (3.14159 / cayley_norm);
                }

                Matrix3d rotation = CayleyToRotation(cayley);

                RelativePose pose;
                pose.Rij = rotation;
                pose.tij = translation;

                // 使用残差信息管理器计算残差
                const CostorInfoManager &manager = CostorInfoManager::GetInstance();
                VectorXd residuals = manager.ComputeResidual(residual_type_, points1_, points2_, pose, weights_);

                // 应用损失函数
                ApplyLossFunction(residuals, fvec);

                // 计算残差统计信息（启用迭代显示时输出）
                if (iter_echo_on_)
                {
                    double cost = fvec.squaredNorm();
                    double max_residual = residuals.cwiseAbs().maxCoeff();
                    double mean_residual = residuals.cwiseAbs().mean();

                    std::cout << "EigenLM迭代 " << iteration_count_ << ": cost=" << std::scientific << std::setprecision(6) << cost
                              << ", max_res=" << std::scientific << std::setprecision(4) << max_residual
                              << ", mean_res=" << std::scientific << std::setprecision(4) << mean_residual << std::endl;
                }

                return 0;
            }

            void ApplyLossFunction(const VectorXd &residuals, VectorXd &fvec) const
            {
                fvec.resize(residuals.size());
                const CostorInfoManager &manager = CostorInfoManager::GetInstance();

                if (boost::iequals(loss_type_, "huber"))
                {
                    // Ceres标准Huber损失函数实现
                    // rho(s) = s                    for s <= a^2
                    // rho(s) = 2*a*sqrt(s) - a^2    for s > a^2
                    // rho'(s) = 1                   for s <= a^2
                    // rho'(s) = a/sqrt(s)           for s > a^2
                    double a = huber_threshold_;
                    double a_sq = a * a;

                    if (manager.Is6DResidual(residual_type_))
                    {
                        // 6D残差：对每个3D分量分别应用Huber损失
                        size_t num_points = residuals.size() / 6;
                        for (size_t i = 0; i < num_points; ++i)
                        {
                            Vector3d diff1 = residuals.segment<3>(6 * i);
                            Vector3d diff2 = residuals.segment<3>(6 * i + 3);

                            double norm1_sq = diff1.squaredNorm();
                            double norm2_sq = diff2.squaredNorm();

                            double rho_prime1 = (norm1_sq <= a_sq) ? 1.0 : (a / std::sqrt(norm1_sq));
                            double rho_prime2 = (norm2_sq <= a_sq) ? 1.0 : (a / std::sqrt(norm2_sq));

                            double weight_factor1 = std::sqrt(rho_prime1);
                            double weight_factor2 = std::sqrt(rho_prime2);

                            fvec.segment<3>(6 * i) = weight_factor1 * diff1;
                            fvec.segment<3>(6 * i + 3) = weight_factor2 * diff2;
                        }
                    }
                    else if (manager.Is3DResidual(residual_type_))
                    {
                        // 3D残差向量：对每个3D分量应用Huber损失
                        size_t num_points = residuals.size() / 3;
                        for (size_t i = 0; i < num_points; ++i)
                        {
                            Vector3d diff = residuals.segment<3>(3 * i);
                            double norm_sq = diff.squaredNorm();

                            double rho_prime = (norm_sq <= a_sq) ? 1.0 : (a / std::sqrt(norm_sq));
                            double weight_factor = std::sqrt(rho_prime);

                            fvec.segment<3>(3 * i) = weight_factor * diff;
                        }
                    }
                    else
                    {
                        // 标量残差：使用标准Huber损失
                        for (int i = 0; i < residuals.size(); ++i)
                        {
                            double r = residuals(i);
                            double r_sq = r * r;

                            double rho_prime = (r_sq <= a_sq) ? 1.0 : (a / std::abs(r));
                            double weight_factor = std::sqrt(rho_prime);

                            fvec(i) = weight_factor * r;
                        }
                    }
                }
                else if (boost::iequals(loss_type_, "cauchy"))
                {
                    // Ceres标准Cauchy损失函数实现
                    // 在Ceres中，给定参数a，实际使用b = a^2
                    // rho(s) = b * log(1 + s/b) = a^2 * log(1 + s/a^2)
                    // rho'(s) = b / (b + s) = a^2 / (a^2 + s)
                    // 权重因子 = sqrt(rho'(s)) = sqrt(a^2 / (a^2 + s)) = a / sqrt(a^2 + s)
                    double a = cauchy_threshold_;
                    double a_sq = a * a;

                    if (manager.Is6DResidual(residual_type_))
                    {
                        // 6D残差：对每个3D分量分别应用Cauchy损失
                        size_t num_points = residuals.size() / 6;
                        for (size_t i = 0; i < num_points; ++i)
                        {
                            Vector3d diff1 = residuals.segment<3>(6 * i);
                            Vector3d diff2 = residuals.segment<3>(6 * i + 3);

                            double norm1_sq = diff1.squaredNorm();
                            double norm2_sq = diff2.squaredNorm();

                            // Ceres标准：rho'(s) = a^2 / (a^2 + s)
                            double rho_prime1 = a_sq / (a_sq + norm1_sq);
                            double rho_prime2 = a_sq / (a_sq + norm2_sq);

                            // 权重因子 = sqrt(rho'(s))
                            double weight_factor1 = std::sqrt(rho_prime1);
                            double weight_factor2 = std::sqrt(rho_prime2);

                            fvec.segment<3>(6 * i) = weight_factor1 * diff1;
                            fvec.segment<3>(6 * i + 3) = weight_factor2 * diff2;
                        }
                    }
                    else if (manager.Is3DResidual(residual_type_))
                    {
                        // 3D残差向量：对每个3D分量应用Cauchy损失
                        size_t num_points = residuals.size() / 3;
                        for (size_t i = 0; i < num_points; ++i)
                        {
                            Vector3d diff = residuals.segment<3>(3 * i);
                            double norm_sq = diff.squaredNorm();

                            // Ceres标准：rho'(s) = a^2 / (a^2 + s)
                            double rho_prime = a_sq / (a_sq + norm_sq);
                            double weight_factor = std::sqrt(rho_prime);

                            fvec.segment<3>(3 * i) = weight_factor * diff;
                        }
                    }
                    else
                    {
                        // 标量残差：使用Ceres标准Cauchy损失
                        for (int i = 0; i < residuals.size(); ++i)
                        {
                            double r = residuals(i);
                            double r_sq = r * r;

                            // Ceres标准：rho'(s) = a^2 / (a^2 + s)
                            double rho_prime = a_sq / (a_sq + r_sq);
                            double weight_factor = std::sqrt(rho_prime);

                            fvec(i) = weight_factor * r;
                        }
                    }
                }
                else // l2 loss
                {
                    fvec = residuals;
                }
            }

            double GetHuberThreshold() const
            {
                // 优先使用显式设置的阈值
                if (huber_threshold_explicit_ >= 0)
                {
                    return huber_threshold_explicit_;
                }

                // 回退到基于角度的自动计算
                const CostorInfoManager &manager = CostorInfoManager::GetInstance();
                ErrorFormType error_form = manager.GetErrorFormType(residual_type_);

                if (error_form == ErrorFormType::REPROJ_ANGLE)
                {
                    return ComputeHuberThresholdFromAngle(residual_type_, reproj_theta_threshold_deg_);
                }
                else if (error_form == ErrorFormType::REPROJ_PIXEL)
                {
                    return reproj_theta_threshold_deg_; // 直接使用像素阈值
                }
                else
                {
                    return 1e-3; // 代数误差默认阈值
                }
            }

            double GetCauchyThreshold() const
            {
                // 优先使用显式设置的阈值
                if (cauchy_threshold_explicit_ >= 0)
                {
                    return cauchy_threshold_explicit_;
                }

                // 回退到基于角度的自动计算
                const CostorInfoManager &manager = CostorInfoManager::GetInstance();
                ErrorFormType error_form = manager.GetErrorFormType(residual_type_);

                if (error_form == ErrorFormType::REPROJ_ANGLE)
                {
                    return ComputeCauchyThresholdFromAngle(residual_type_, reproj_theta_threshold_deg_);
                }
                else if (error_form == ErrorFormType::REPROJ_PIXEL)
                {
                    return reproj_theta_threshold_deg_; // 直接使用像素阈值
                }
                else
                {
                    return 1e-3; // 代数误差默认阈值
                }
            }

            static double ComputeHuberThresholdFromAngle(const std::string &residual_type, double angle_deg)
            {
                double angle_rad = angle_deg * M_PI / 180.0;

                if (boost::iequals(residual_type, "ppo_opengv"))
                {
                    return 2.0 * (1.0 - std::cos(angle_rad));
                }
                else if (boost::iequals(residual_type, "ppo_angle"))
                {
                    return 1.0 - std::cos(angle_rad);
                }
                else if (boost::iequals(residual_type, "ppo_angle_rad"))
                {
                    return angle_rad;
                }
                else if (boost::iequals(residual_type, "ppo_angle_sin"))
                {
                    return std::sin(angle_rad);
                }
                else if (boost::iequals(residual_type, "ppo_angle_2sin"))
                {
                    return 2.0 * std::sin(angle_rad / 2.0);
                }
                else if (boost::iequals(residual_type, "ppo_angle_sqrt"))
                {
                    return std::sqrt(1.0 - std::cos(angle_rad));
                }
                else
                {
                    return angle_rad; // 其他残差类型使用弧度值
                }
            }

            static double ComputeCauchyThresholdFromAngle(const std::string &residual_type, double angle_deg)
            {
                double angle_rad = angle_deg * M_PI / 180.0;

                if (boost::iequals(residual_type, "ppo_opengv"))
                {
                    return 2.0 * (1.0 - std::cos(angle_rad));
                }
                else if (boost::iequals(residual_type, "ppo_angle"))
                {
                    return 1.0 - std::cos(angle_rad);
                }
                else if (boost::iequals(residual_type, "ppo_angle_rad"))
                {
                    return angle_rad;
                }
                else if (boost::iequals(residual_type, "ppo_angle_sin"))
                {
                    return std::sin(angle_rad);
                }
                else if (boost::iequals(residual_type, "ppo_angle_2sin"))
                {
                    return 2.0 * std::sin(angle_rad / 2.0);
                }
                else if (boost::iequals(residual_type, "ppo_angle_sqrt"))
                {
                    return std::sqrt(1.0 - std::cos(angle_rad));
                }
                else
                {
                    return angle_rad; // 其他残差类型使用弧度值
                }
            }

            const BearingVectors &points1_;
            const BearingVectors &points2_;
            const VectorXd *weights_;
            std::string residual_type_;
            std::string loss_type_;
            double reproj_theta_threshold_deg_; // 角度阈值（度）
            double huber_threshold_explicit_;   // 显式Huber阈值
            double cauchy_threshold_explicit_;  // 显式Cauchy阈值
            double huber_threshold_;
            double cauchy_threshold_;
        };

        // 简化的Bundle Adjustment Functor (已移除，EigenLM不支持BA)
        // EigenLM优化器不支持Bundle Adjustment类型的残差优化
        // 如需Bundle Adjustment，请使用Ceres优化器
    } // anonymous namespace

    bool EigenLMOptimizer::OptimizePoseOnly(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        const size_t num_points = points1.cols();

        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "Initial pose:" << std::endl;
            std::cout << "  Rotation:\n"
                      << pose.Rij << std::endl;
            std::cout << "  Translation: " << pose.tij.transpose() << ", norm=" << pose.tij.norm() << std::endl;
        }

        // 初始化5个参数向量
        VectorXd x(5);

        // 将平移向量转换为球坐标系参数
        Vector3d t_normalized = pose.tij.normalized();
        double theta = std::acos(std::clamp(t_normalized(2), -1.0, 1.0)); // 天顶角 [0, π]
        double phi = std::atan2(t_normalized(1), t_normalized(0));        // 方位角 [-π, π]

        x(0) = theta; // θ
        x(1) = phi;   // φ

        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "Initial 5-parameter vector (spherical):" << std::endl;
            std::cout << "  theta=" << x(0) << " (" << x(0) * 180.0 / M_PI << "°), phi=" << x(1) << " (" << x(1) * 180.0 / M_PI << "°)" << std::endl;
        }

        x.segment<3>(2) = RotationToCayley(pose.Rij); // Cayley参数
        if (log_level_ >= 1)                          // PO_LOG_NORMAL
        {
            std::cout << "  cayley=[" << x(2) << "," << x(3) << "," << x(4) << "]" << std::endl;
        }

        // 计算初始残差以作为基准（静默模式）
        TwoViewResidualFunctor initial_functor(points1, points2, weights, residual_type, loss_type, reproj_theta_threshold_deg_, huber_threshold_explicit_, cauchy_threshold_explicit_, false);
        VectorXd initial_fvec;
        initial_functor(x, initial_fvec);
        double initial_cost = initial_fvec.squaredNorm();

        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "原始残差平方和 (初始): " << std::scientific << initial_cost << std::endl;
        }

        // 创建残差函数对象（根据iter_echo_on_显示调试信息）
        TwoViewResidualFunctor functor(points1, points2, weights, residual_type, loss_type, reproj_theta_threshold_deg_, huber_threshold_explicit_, cauchy_threshold_explicit_, iter_echo_on_);
        Eigen::NumericalDiff<TwoViewResidualFunctor> numDiff(functor);
        Eigen::LevenbergMarquardt<Eigen::NumericalDiff<TwoViewResidualFunctor>> lm(numDiff);

        // 设置LM参数
        lm.resetParameters();
        lm.setFtol(convergence_threshold_);
        lm.setXtol(convergence_threshold_);
        lm.setMaxfev(max_iterations_ * 20);

        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "\n[EigenLM] Starting LM iterations..." << std::endl;
            std::cout << "LM parameters: ftol=" << std::scientific << lm.ftol()
                      << ", xtol=" << lm.xtol()
                      << ", maxfev=" << lm.maxfev()
                      << ", max_iterations=" << max_iterations_ << std::endl;
        }

        // 执行优化
        Eigen::LevenbergMarquardtSpace::Status status = lm.minimize(x);

        // 计算最终残差（静默模式）
        TwoViewResidualFunctor final_functor(points1, points2, weights, residual_type, loss_type, reproj_theta_threshold_deg_, huber_threshold_explicit_, cauchy_threshold_explicit_, false);
        VectorXd final_fvec;
        final_functor(x, final_fvec);
        double final_cost = final_fvec.squaredNorm();

        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "\n[EigenLM] Optimization finished." << std::endl;
            std::cout << "原始残差平方和 (初始): " << std::scientific << initial_cost << std::endl;
            std::cout << "应用损失函数后cost (最终): " << std::scientific << final_cost << " [含" << loss_type << "损失函数]" << std::endl;
            std::cout << "Cost improvement: " << std::scientific << (initial_cost - final_cost) << std::endl;
            std::cout << "Cost ratio (final/initial): " << std::fixed << std::setprecision(6) << (final_cost / initial_cost) << std::endl;
        }

        // 从5个参数更新位姿（球坐标系）
        double final_theta = x(0); // 天顶角
        double final_phi = x(1);   // 方位角

        // 球坐标系转换为笛卡尔坐标系
        double sin_final_theta = std::sin(final_theta);
        double cos_final_theta = std::cos(final_theta);
        double sin_final_phi = std::sin(final_phi);
        double cos_final_phi = std::cos(final_phi);

        pose.tij = Vector3d(sin_final_theta * cos_final_phi, sin_final_theta * sin_final_phi, cos_final_theta);
        pose.Rij = CayleyToRotation(x.segment<3>(2));

        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "Final pose:" << std::endl;
            std::cout << "  Rotation:\n"
                      << pose.Rij << std::endl;
            std::cout << "  Translation: " << pose.tij.transpose() << ", norm=" << pose.tij.norm() << std::endl;
        }

        bool success = (status == Eigen::LevenbergMarquardtSpace::RelativeReductionTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::RelativeErrorTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::RelativeErrorAndReductionTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::XtolTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::FtolTooSmall ||
                        status == Eigen::LevenbergMarquardtSpace::CosinusTooSmall);

        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::string status_desc;
            switch (status)
            {
            case Eigen::LevenbergMarquardtSpace::RelativeReductionTooSmall:
                status_desc = "RelativeReductionTooSmall (收敛成功)";
                break;
            case Eigen::LevenbergMarquardtSpace::RelativeErrorTooSmall:
                status_desc = "RelativeErrorTooSmall (收敛成功)";
                break;
            case Eigen::LevenbergMarquardtSpace::RelativeErrorAndReductionTooSmall:
                status_desc = "RelativeErrorAndReductionTooSmall (收敛成功)";
                break;
            case Eigen::LevenbergMarquardtSpace::XtolTooSmall:
                status_desc = "XtolTooSmall (参数变化太小，收敛成功)";
                break;
            case Eigen::LevenbergMarquardtSpace::FtolTooSmall:
                status_desc = "FtolTooSmall (函数值变化太小，收敛成功)";
                break;
            case Eigen::LevenbergMarquardtSpace::CosinusTooSmall:
                status_desc = "CosinusTooSmall (梯度太小，收敛成功)";
                break;
            case Eigen::LevenbergMarquardtSpace::TooManyFunctionEvaluation:
                status_desc = "TooManyFunctionEvaluation (达到最大迭代次数)";
                break;
            case Eigen::LevenbergMarquardtSpace::ImproperInputParameters:
                status_desc = "ImproperInputParameters (输入参数错误)";
                break;
            default:
                status_desc = "Unknown status";
                break;
            }
            std::cout << "Eigen LM optimization finished with status: " << status
                      << " (" << status_desc << ")"
                      << " after " << lm.iterations() << " iterations" << std::endl;
            std::cout << "Optimization " << (success ? "成功" : "失败") << std::endl;
        }

        return success;
    }

    bool EigenLMOptimizer::OptimizePoseWith3DPoints(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        // EigenLM优化器不支持Bundle Adjustment类型的残差优化
        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "错误: EigenLM优化器不支持Bundle Adjustment优化" << std::endl;
            std::cout << "残差类型 '" << residual_type << "' 需要联合优化位姿和3D点" << std::endl;
            std::cout << "EigenLM没有针对稀疏Schur分解的实现，无法高效处理BA问题" << std::endl;
            std::cout << "请使用Ceres优化器进行Bundle Adjustment优化" << std::endl;
        }

        // 直接返回失败
        return false;
    }

} // namespace PoSDK