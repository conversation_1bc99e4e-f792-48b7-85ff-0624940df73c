/**
 * @file method_TwoViewOptimizer.hpp
 * @brief 双视图位姿优化器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#ifndef PO_CORE_METHOD_TWO_VIEW_OPTIMIZER_HPP
#define PO_CORE_METHOD_TWO_VIEW_OPTIMIZER_HPP

#include "interfaces_preset_profiler.hpp"
#include "relative_pose.hpp"
#include <Eigen/Dense>
#include <memory>

namespace PoSDK
{
    // 前向声明
    class TwoViewOptimizerBase;

    using BearingVectors = Eigen::Matrix<double, 3, Eigen::Dynamic>;
    using BearingPairs = std::vector<Eigen::Matrix<double, 6, 1>>;
    using VectorXd = Eigen::VectorXd;

    /**
     * @brief 双视图位姿优化器方法类
     */
    class MethodTwoViewOptimizer : public Interface::MethodPresetProfiler
    {
    public:
        MethodTwoViewOptimizer();
        virtual ~MethodTwoViewOptimizer() = default;

        /**
         * @brief 获取方法类型名称
         * @return 方法类型名称
         */
        const std::string &GetType() const override
        {
            static const std::string type = "TwoViewOptimizer";
            return type;
        }

        /**
         * @brief 执行位姿优化
         * @return 优化后的位姿数据
         */
        Interface::DataPtr Run() override;

    private:
        /// @brief 最小点数要求
        static constexpr size_t kMinNumPoints = 8;

        /**
         * @brief 验证输入数据
         * @return 输入数据是否有效
         */
        bool ValidateInputData();

        /**
         * @brief 将BearingPairs转换为BearingVectors
         * @param points1 输出第一视图的观测向量
         * @param points2 输出第二视图的观测向量
         * @return 转换是否成功
         */
        bool ConvertBearingPairsToBearingVectors(
            BearingVectors &points1,
            BearingVectors &points2) const;

        /**
         * @brief 执行位姿优化
         * @param points1 第一视图的观测向量
         * @param points2 第二视图的观测向量
         * @param pose 输入初始位姿，输出优化后位姿
         * @param weights 权重向量（可为nullptr）
         * @return 优化是否成功
         */
        bool OptimizeRelativePose(
            const BearingVectors &points1,
            const BearingVectors &points2,
            RelativePose &pose,
            const VectorXd *weights);

        /**
         * @brief 获取残差类型
         * @return 残差类型字符串
         */
        std::string GetResidualType() const;

        /**
         * @brief 获取损失函数类型
         * @return 损失函数类型字符串
         */
        std::string GetLossType() const;

        /**
         * @brief 获取重投影角度阈值
         * @return 重投影角度阈值（度）
         */
        double GetReprojThetaThresholdDeg() const;

        /**
         * @brief 获取显式Huber损失阈值
         * @return Huber损失阈值（-1表示使用自动计算）
         */
        double GetHuberThresholdExplicit() const;

        /**
         * @brief 获取显式Cauchy损失阈值
         * @return Cauchy损失阈值（-1表示使用自动计算）
         */
        double GetCauchyThresholdExplicit() const;

        /**
         * @brief 创建优化器
         * @param optimizer_type 优化器类型
         * @return 优化器实例
         */
        std::unique_ptr<TwoViewOptimizerBase> CreateOptimizer(const std::string &optimizer_type);
    };

} // namespace PoSDK

#endif // PO_CORE_METHOD_TWO_VIEW_OPTIMIZER_HPP