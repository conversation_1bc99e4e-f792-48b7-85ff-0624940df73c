/**
 * @file CeresOptimizer.cpp
 * @brief Ceres优化器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "CeresOptimizer.hpp"
#include "costor_info.hpp"
#include "relative_residuals.hpp"
#include "types.hpp"
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <cmath>
#include "ceres/ceres.h"
#include "ceres/rotation.h"
#include <Eigen/Dense>

// 三角化函数声明
Eigen::Vector3d triangulateOnePoint(const Eigen::Matrix3d &R, const Eigen::Vector3d &t,
                                    const Eigen::Vector3d &point1, const Eigen::Vector3d &point2);
Eigen::Vector3d triangulate2(const Eigen::Matrix3d &R, const Eigen::Vector3d &t,
                             const Eigen::Vector3d &point1, const Eigen::Vector3d &point2);

namespace PoSDK
{
    using types::CayleyToRotation;
    using types::RotationToCayley;

    namespace
    {
        // 简化的Ceres代价函数
        struct TwoViewCeresCostFunction
        {
            TwoViewCeresCostFunction(const Vector3d &point1, const Vector3d &point2,
                                     const std::string &residual_type)
                : point1_(point1), point2_(point2), residual_type_(residual_type)
            {
            }

            template <typename T>
            bool operator()(const T *const pose_params, T *residuals) const
            {
                // 从球坐标系参数恢复位姿
                T theta = pose_params[0]; // 天顶角
                T phi = pose_params[1];   // 方位角

                // 球坐标系转换为笛卡尔坐标系
                T sin_theta = ceres::sin(theta);
                T cos_theta = ceres::cos(theta);
                T sin_phi = ceres::sin(phi);
                T cos_phi = ceres::cos(phi);

                Vector3<T> translation(sin_theta * cos_phi, sin_theta * sin_phi, cos_theta);
                Vector3<T> cayley(pose_params[2], pose_params[3], pose_params[4]);

                // Cayley转旋转矩阵（与types.hpp中的实现保持一致）
                Matrix3<T> rotation = CayleyToRotation(cayley);

                // 计算残差（不应用权重，让Ceres自动处理）
                Vector3<T> X1 = point1_.template cast<T>();
                Vector3<T> X2 = point2_.template cast<T>();

                return ComputeResidual(X1, X2, rotation, translation, residuals);
            }

        private:
            template <typename T>
            bool ComputeResidual(const Vector3<T> &X1, const Vector3<T> &X2,
                                 const Matrix3<T> &R, const Vector3<T> &t,
                                 T *residuals) const
            {
                // 使用残差信息管理器获取残差维度
                const CostorInfoManager &manager = CostorInfoManager::GetInstance();

                if (manager.Is6DResidual(residual_type_))
                {
                    return ComputePPOOpenGVResidual(X1, X2, R, t, residuals);
                }
                else if (manager.Is3DResidual(residual_type_))
                {
                    return Compute3DResidual(X1, X2, R, t, residuals);
                }
                else
                {
                    return ComputeScalarResidual(X1, X2, R, t, residuals);
                }
            }

            template <typename T>
            bool ComputePPOOpenGVResidual(const Vector3<T> &X1, const Vector3<T> &X2,
                                          const Matrix3<T> &R, const Vector3<T> &t,
                                          T *residuals) const
            {
                // PPO OpenGV残差实现（6维残差）- 与旧版本完全一致，但使用球坐标系输入

                // 1. 计算 R * X2
                T RX2[3] = {R(0, 0) * X2(0) + R(0, 1) * X2(1) + R(0, 2) * X2(2),
                            R(1, 0) * X2(0) + R(1, 1) * X2(1) + R(1, 2) * X2(2),
                            R(2, 0) * X2(0) + R(2, 1) * X2(1) + R(2, 2) * X2(2)};

                // 2. 计算所需的叉积
                // tmp2 = RX2.cross(X1)
                T tmp2[3] = {RX2[1] * X1(2) - RX2[2] * X1(1),
                             RX2[2] * X1(0) - RX2[0] * X1(2),
                             RX2[0] * X1(1) - RX2[1] * X1(0)};

                // tmp3 = X1.cross(t)
                T tmp3[3] = {X1(1) * t(2) - X1(2) * t(1),
                             X1(2) * t(0) - X1(0) * t(2),
                             X1(0) * t(1) - X1(1) * t(0)};

                // tmp4 = RX2.cross(t)
                T tmp4[3] = {RX2[1] * t(2) - RX2[2] * t(1),
                             RX2[2] * t(0) - RX2[0] * t(2),
                             RX2[0] * t(1) - RX2[1] * t(0)};

                // 3. 计算范数
                T tmp2_norm = ceres::sqrt(tmp2[0] * tmp2[0] + tmp2[1] * tmp2[1] + tmp2[2] * tmp2[2]);
                T tmp3_norm = ceres::sqrt(tmp3[0] * tmp3[0] + tmp3[1] * tmp3[1] + tmp3[2] * tmp3[2]);
                T tmp4_norm = ceres::sqrt(tmp4[0] * tmp4[0] + tmp4[1] * tmp4[1] + tmp4[2] * tmp4[2]);

                // 4. 避免除零错误
                const T epsilon = T(1e-12);
                if (tmp2_norm < epsilon || tmp3_norm < epsilon || tmp4_norm < epsilon)
                {
                    for (int i = 0; i < 6; ++i)
                    {
                        residuals[i] = T(1e3); // 移除权重应用
                    }
                    return true;
                }

                // 5. 计算重投影坐标
                // reproj_coord1 = tmp2_norm * t + tmp3_norm * RX2
                T reproj_coord1[3] = {tmp2_norm * t(0) + tmp3_norm * RX2[0],
                                      tmp2_norm * t(1) + tmp3_norm * RX2[1],
                                      tmp2_norm * t(2) + tmp3_norm * RX2[2]};

                // reproj_coord2 = tmp4_norm * X1 - tmp2_norm * t
                T reproj_coord2[3] = {tmp4_norm * X1(0) - tmp2_norm * t(0),
                                      tmp4_norm * X1(1) - tmp2_norm * t(1),
                                      tmp4_norm * X1(2) - tmp2_norm * t(2)};

                // 6. 归一化重投影坐标
                T reproj1_norm = ceres::sqrt(reproj_coord1[0] * reproj_coord1[0] +
                                             reproj_coord1[1] * reproj_coord1[1] +
                                             reproj_coord1[2] * reproj_coord1[2]);
                T reproj2_norm = ceres::sqrt(reproj_coord2[0] * reproj_coord2[0] +
                                             reproj_coord2[1] * reproj_coord2[1] +
                                             reproj_coord2[2] * reproj_coord2[2]);

                // 避免除零
                if (reproj1_norm < epsilon || reproj2_norm < epsilon)
                {
                    for (int i = 0; i < 6; ++i)
                    {
                        residuals[i] = T(1e3); // 移除权重应用
                    }
                    return true;
                }

                reproj_coord1[0] /= reproj1_norm;
                reproj_coord1[1] /= reproj1_norm;
                reproj_coord1[2] /= reproj1_norm;

                reproj_coord2[0] /= reproj2_norm;
                reproj_coord2[1] /= reproj2_norm;
                reproj_coord2[2] /= reproj2_norm;

                // 7. 计算6D残差向量 [diff1; diff2]
                T diff1[3] = {reproj_coord1[0] - X1(0),
                              reproj_coord1[1] - X1(1),
                              reproj_coord1[2] - X1(2)};
                T diff2[3] = {reproj_coord2[0] - RX2[0],
                              reproj_coord2[1] - RX2[1],
                              reproj_coord2[2] - RX2[2]};

                // 8. 存储6D残差向量（不应用权重，让Ceres自动处理）
                residuals[0] = diff1[0];
                residuals[1] = diff1[1];
                residuals[2] = diff1[2];
                residuals[3] = diff2[0];
                residuals[4] = diff2[1];
                residuals[5] = diff2[2];

                return true;
            }

            template <typename T>
            bool Compute3DResidual(const Vector3<T> &X1, const Vector3<T> &X2,
                                   const Matrix3<T> &R, const Vector3<T> &t,
                                   T *residuals) const
            {
                Vector3<T> diff;

                if (residual_type_ == "ppo_3dvec")
                {
                    // PPO 3D向量残差：reproj_coord/reproj_coord.norm() - X1
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;
                    diff = reproj_coord / reproj_coord.norm() - X1;
                }
                else if (residual_type_ == "ligt_3dvec")
                {
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp1 = X1.cross(rotated_X2);
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    T tmp4 = tmp2.norm() * tmp2.norm();

                    diff = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
                    diff = diff / tmp3.squaredNorm() * tmp2.norm();
                }
                else if (residual_type_ == "lirt_3dvec")
                {
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp = X1.cross(rotated_X2);

                    Matrix3<T> tmp1 = X1 * tmp.transpose() * CrossMatrix(rotated_X2);
                    Matrix3<T> tmp2 = rotated_X2 * tmp.transpose() * CrossMatrix(X1);
                    Matrix3<T> tmp3 = tmp.squaredNorm() * Matrix3<T>::Identity();
                    diff = (tmp1 - tmp2 + tmp3) * t;
                }
                else if (residual_type_ == "ppog_3dvec")
                {
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;
                    diff = reproj_coord - X1 * (t.cross(rotated_X2)).norm();
                }
                else
                {
                    // 警告：没有指定的3D残差类型(蓝色)
                    std::cout << "\033[34mWarning: No specified 3D residual type '" << residual_type_ << "', use ppo_3dvec instead\033[0m" << std::endl;
                    // 默认回退到PPO_3dvec
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;
                    diff = reproj_coord / reproj_coord.norm() - X1;
                }

                // 存储3D残差向量（不应用权重，让Ceres自动处理）
                residuals[0] = diff[0];
                residuals[1] = diff[1];
                residuals[2] = diff[2];

                return true;
            }

            template <typename T>
            bool ComputeScalarResidual(const Vector3<T> &X1, const Vector3<T> &X2,
                                       const Matrix3<T> &R, const Vector3<T> &t,
                                       T *residuals) const
            {
                T residual_value = T(0.0);

                if (residual_type_ == "ppo")
                {
                    // PPO标量残差：取reproj_coord/reproj_coord.norm() - X1的norm
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;
                    Vector3<T> diff = reproj_coord / reproj_coord.norm() - X1;
                    residual_value = diff.norm();
                }
                else if (residual_type_ == "ppo_angle")
                {
                    // PPO角度残差：1 - cos(angle) = 1 - dot(reproj_normalized, X1_normalized)
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;

                    // 归一化重投影坐标和真实观测
                    Vector3<T> reproj_normalized = reproj_coord.normalized();
                    Vector3<T> X1_normalized = X1.normalized();

                    // 计算角度误差：1 - cos(angle) = 1 - dot(v1, v2)
                    T dot_product = reproj_normalized.dot(X1_normalized);

                    // 确保点积在有效范围内（数值稳定性）
                    if (dot_product > T(1.0))
                        dot_product = T(1.0);
                    if (dot_product < T(-1.0))
                        dot_product = T(-1.0);

                    residual_value = T(1.0) - dot_product;
                }
                else if (residual_type_ == "ppo_angle_rad")
                {
                    // PPO角度残差：直接使用弧度值
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;

                    // 归一化重投影坐标和真实观测
                    Vector3<T> reproj_normalized = reproj_coord.normalized();
                    Vector3<T> X1_normalized = X1.normalized();

                    // 计算角度误差：直接使用弧度值
                    T dot_product = reproj_normalized.dot(X1_normalized);

                    // 确保点积在有效范围内（数值稳定性）
                    if (dot_product > T(1.0))
                        dot_product = T(1.0);
                    if (dot_product < T(-1.0))
                        dot_product = T(-1.0);

                    residual_value = ceres::acos(dot_product);
                }
                else if (residual_type_ == "ppo_angle_sin")
                {
                    // PPO角度残差：使用正弦值 sin(angle) = ||cross(v1, v2)||
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;

                    // 归一化重投影坐标和真实观测
                    Vector3<T> reproj_normalized = reproj_coord.normalized();
                    Vector3<T> X1_normalized = X1.normalized();

                    // 计算角度误差：使用正弦值 sin(angle) = ||cross(v1, v2)||
                    Vector3<T> cross_product = reproj_normalized.cross(X1_normalized);
                    residual_value = cross_product.norm();
                }
                else if (residual_type_ == "ppo_angle_2sin")
                {
                    // PPO角度残差：使用2sin(angle/2) = ||v1 - v2||（当v1、v2为单位向量时）
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;

                    // 归一化重投影坐标和真实观测
                    Vector3<T> reproj_normalized = reproj_coord.normalized();
                    Vector3<T> X1_normalized = X1.normalized();

                    // 计算角度误差：使用2sin(angle/2) = ||v1 - v2||（当v1、v2为单位向量时）
                    Vector3<T> diff = reproj_normalized - X1_normalized;
                    residual_value = diff.norm();
                }
                else if (residual_type_ == "ppo_angle_sqrt")
                {
                    // PPO角度残差：使用sqrt(1 - cos(angle))，更鲁棒
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;

                    // 归一化重投影坐标和真实观测
                    Vector3<T> reproj_normalized = reproj_coord.normalized();
                    Vector3<T> X1_normalized = X1.normalized();

                    // 计算角度误差：sqrt(1 - cos(angle))
                    // 好处：对小角度更敏感，对大角度更鲁棒
                    T dot_product = reproj_normalized.dot(X1_normalized);

                    // 确保点积在有效范围内（数值稳定性）
                    if (dot_product > T(1.0))
                        dot_product = T(1.0);
                    if (dot_product < T(-1.0))
                        dot_product = T(-1.0);

                    T cos_error = T(1.0) - dot_product;
                    // 确保cos_error非负后开平方根
                    if (cos_error < T(0.0))
                        cos_error = T(0.0);
                    residual_value = ceres::sqrt(cos_error);
                }
                else if (residual_type_ == "sampson")
                {
                    Matrix3<T> E = CrossMatrix(t) * R;
                    Vector3<T> x1_norm = X1 / X1(2);
                    Vector3<T> x2_norm = X2 / X2(2);

                    Vector2<T> ex2 = (E.template block<2, 3>(0, 0) * x2_norm);
                    Vector2<T> etx1 = (E.transpose().template block<2, 3>(0, 0) * x1_norm);

                    T numerator = (x1_norm.transpose() * E * x2_norm);
                    T denominator = ex2.squaredNorm() + etx1.squaredNorm();

                    residual_value = numerator * numerator / denominator;
                }
                else if (residual_type_ == "coplanar")
                {
                    Matrix3<T> E = CrossMatrix(t) * R;
                    T dot_product = X1.transpose() * E * X2;
                    residual_value = ceres::abs(dot_product);
                }
                else if (residual_type_ == "kneip")
                {
                    Vector3<T> tmp = X1.cross(R * X2);
                    T cross_norm = tmp.norm();
                    if (cross_norm > T(1e-12))
                    {
                        T dot_product = tmp.dot(t);
                        residual_value = ceres::abs(dot_product) / cross_norm;
                    }
                    else
                    {
                        residual_value = T(0.0);
                    }
                }
                else if (residual_type_ == "ligt")
                {
                    // LiGT标量残差：取LiGT 3D向量的norm
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp1 = X1.cross(rotated_X2);
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    T tmp4 = tmp2.norm() * tmp2.norm();

                    Vector3<T> diff = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
                    diff = diff / tmp3.squaredNorm() * tmp2.norm();
                    residual_value = diff.norm();
                }
                else if (residual_type_ == "lirt")
                {
                    // LiRT标量残差：取LiRT 3D向量的norm
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp = X1.cross(rotated_X2);

                    Matrix3<T> tmp1 = X1 * tmp.transpose() * CrossMatrix(rotated_X2);
                    Matrix3<T> tmp2 = rotated_X2 * tmp.transpose() * CrossMatrix(X1);
                    Matrix3<T> tmp3 = tmp.squaredNorm() * Matrix3<T>::Identity();
                    Vector3<T> diff = (tmp1 - tmp2 + tmp3) * t;
                    residual_value = diff.norm();
                }
                else if (residual_type_ == "ppog")
                {
                    // PPOG标量残差：取PPOG 3D向量的norm
                    Vector3<T> rotated_X2 = R * X2;
                    Vector3<T> tmp2 = rotated_X2.cross(X1);
                    Vector3<T> tmp3 = X1.cross(t);
                    Vector3<T> reproj_coord = tmp2.norm() * t + tmp3.norm() * rotated_X2;
                    Vector3<T> diff = reproj_coord - X1 * (t.cross(rotated_X2)).norm();
                    residual_value = diff.norm();
                }
                else
                {
                    // 警告：没有指定的残差类型(蓝色)
                    std::cout << "\033[34mWarning: No specified residual type, use coplanar instead\033[0m" << std::endl;
                    // 默认回退到coplanar
                    Matrix3<T> E = CrossMatrix(t) * R;
                    T dot_product = X1.transpose() * E * X2;
                    residual_value = ceres::abs(dot_product);
                }

                // 存储标量残差（不应用权重，让Ceres自动处理）
                residuals[0] = residual_value;
                return true;
            }

            template <typename T>
            Matrix3<T> CrossMatrix(const Vector3<T> &v) const
            {
                Matrix3<T> result;
                result << T(0), -v[2], v[1],
                    v[2], T(0), -v[0],
                    -v[1], v[0], T(0);
                return result;
            }

            template <typename T>
            Matrix3<T> CayleyToRotation(const Vector3<T> &cayley) const
            {
                // 与types.hpp中的实现完全一致
                T c1 = cayley[0], c2 = cayley[1], c3 = cayley[2];
                T scale = T(1.0) + c1 * c1 + c2 * c2 + c3 * c3;

                Matrix3<T> R;
                R(0, 0) = T(1.0) + c1 * c1 - c2 * c2 - c3 * c3;
                R(0, 1) = T(2.0) * (c1 * c2 - c3);
                R(0, 2) = T(2.0) * (c1 * c3 + c2);
                R(1, 0) = T(2.0) * (c1 * c2 + c3);
                R(1, 1) = T(1.0) - c1 * c1 + c2 * c2 - c3 * c3;
                R(1, 2) = T(2.0) * (c2 * c3 - c1);
                R(2, 0) = T(2.0) * (c1 * c3 - c2);
                R(2, 1) = T(2.0) * (c2 * c3 + c1);
                R(2, 2) = T(1.0) - c1 * c1 - c2 * c2 + c3 * c3;

                // 数值稳定性检查
                if (scale < T(1e-12))
                {
                    // 退化情况：返回单位矩阵
                    return Matrix3<T>::Identity();
                }

                // 与types.hpp保持一致：先计算后除法
                R = R / scale;
                return R;
            }

            Vector3d point1_;
            Vector3d point2_;
            std::string residual_type_;
        };

        // Bundle Adjustment Cost Function (pose + 3D point optimization)
        struct BundleAdjustmentCostFunction
        {
            BundleAdjustmentCostFunction(const Vector3d &bearing1, const Vector3d &bearing2)
                : bearing1_(bearing1), bearing2_(bearing2)
            {
            }

            template <typename T>
            bool operator()(const T *const pose_params, const T *const point3d, T *residuals) const
            {
                // 从球坐标系参数恢复位姿
                T theta = pose_params[0]; // 天顶角
                T phi = pose_params[1];   // 方位角

                // 球坐标系转换为笛卡尔坐标系
                T sin_theta = ceres::sin(theta);
                T cos_theta = ceres::cos(theta);
                T sin_phi = ceres::sin(phi);
                T cos_phi = ceres::cos(phi);

                Vector3<T> translation(sin_theta * cos_phi, sin_theta * sin_phi, cos_theta);
                Vector3<T> cayley(pose_params[2], pose_params[3], pose_params[4]);

                // Cayley转旋转矩阵
                Matrix3<T> rotation = CayleyToRotation(cayley);

                // 按照residual_BA的逻辑：
                // reproj_v1 = points3D (已经是相机1坐标系中的点)
                // reproj_v2 = R.transpose() * (points3D - t)
                Vector3<T> reproj_v1(point3d[0], point3d[1], point3d[2]);

                // 变换到相机2：R.transpose() * (point3d - t)
                Vector3<T> p_minus_t(point3d[0] - translation(0), point3d[1] - translation(1), point3d[2] - translation(2));
                Vector3<T> reproj_v2 = rotation.transpose() * p_minus_t;

                // 归一化：reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1(2, i)
                const T epsilon = T(1e-12);
                if (ceres::abs(reproj_v1(2)) < epsilon || ceres::abs(reproj_v2(2)) < epsilon)
                {
                    // 设置大残差并返回
                    for (int i = 0; i < 6; ++i)
                    {
                        residuals[i] = T(1e6);
                    }
                    return true;
                }

                reproj_v1(0) /= reproj_v1(2);
                reproj_v1(1) /= reproj_v1(2);
                reproj_v1(2) = T(1.0);

                reproj_v2(0) /= reproj_v2(2);
                reproj_v2(1) /= reproj_v2(2);
                reproj_v2(2) = T(1.0);

                // 归一化原始bearing vectors：v1.col(i) / v1(2, i)
                Vector3<T> v1(T(bearing1_(0)) / T(bearing1_(2)),
                              T(bearing1_(1)) / T(bearing1_(2)),
                              T(1.0));

                Vector3<T> v2(T(bearing2_(0)) / T(bearing2_(2)),
                              T(bearing2_(1)) / T(bearing2_(2)),
                              T(1.0));

                // 计算6D残差向量：[v1.col(i) / v1(2, i) - reproj_v1.col(i); v2.col(i) / v2(2, i) - reproj_v2.col(i)]
                Vector3<T> diff1 = v1 - reproj_v1;
                Vector3<T> diff2 = v2 - reproj_v2;

                // 存储6D残差向量：[diff1; diff2]
                residuals[0] = diff1(0);
                residuals[1] = diff1(1);
                residuals[2] = diff1(2);
                residuals[3] = diff2(0);
                residuals[4] = diff2(1);
                residuals[5] = diff2(2);

                return true;
            }

        private:
            template <typename T>
            Matrix3<T> CayleyToRotation(const Vector3<T> &cayley) const
            {
                // 与types.hpp中的实现完全一致
                T c1 = cayley[0], c2 = cayley[1], c3 = cayley[2];
                T scale = T(1.0) + c1 * c1 + c2 * c2 + c3 * c3;

                Matrix3<T> R;
                R(0, 0) = T(1.0) + c1 * c1 - c2 * c2 - c3 * c3;
                R(0, 1) = T(2.0) * (c1 * c2 - c3);
                R(0, 2) = T(2.0) * (c1 * c3 + c2);
                R(1, 0) = T(2.0) * (c1 * c2 + c3);
                R(1, 1) = T(1.0) - c1 * c1 + c2 * c2 - c3 * c3;
                R(1, 2) = T(2.0) * (c2 * c3 - c1);
                R(2, 0) = T(2.0) * (c1 * c3 - c2);
                R(2, 1) = T(2.0) * (c2 * c3 + c1);
                R(2, 2) = T(1.0) - c1 * c1 - c2 * c2 + c3 * c3;

                // 数值稳定性检查
                if (scale < T(1e-12))
                {
                    // 退化情况：返回单位矩阵
                    return Matrix3<T>::Identity();
                }

                // 与types.hpp保持一致：先计算后除法
                R = R / scale;
                return R;
            }

            Vector3d bearing1_, bearing2_;
        };

        // OpenGV Bundle Adjustment残差函数（1维标量残差）
        struct OpenGVBundleCostFunction
        {
            OpenGVBundleCostFunction(const Vector3d &bearing1, const Vector3d &bearing2)
                : bearing1_(bearing1), bearing2_(bearing2)
            {
            }

            template <typename T>
            bool operator()(const T *const pose_params, const T *const point3d, T *residuals) const
            {
                // 从球坐标系参数恢复位姿
                T theta = pose_params[0]; // 天顶角
                T phi = pose_params[1];   // 方位角

                // 球坐标系转换为笛卡尔坐标系
                T sin_theta = ceres::sin(theta);
                T cos_theta = ceres::cos(theta);
                T sin_phi = ceres::sin(phi);
                T cos_phi = ceres::cos(phi);

                Vector3<T> translation(sin_theta * cos_phi, sin_theta * sin_phi, cos_theta);
                Vector3<T> cayley(pose_params[2], pose_params[3], pose_params[4]);

                // Cayley转旋转矩阵
                Matrix3<T> rotation = CayleyToRotation(cayley);

                // 按照residual_opengv的逻辑：计算重投影的方向向量
                Vector3<T> reproj_v1(point3d[0], point3d[1], point3d[2]);

                // 变换到相机2：R.transpose() * (point3d - t)
                Vector3<T> p_minus_t(point3d[0] - translation(0), point3d[1] - translation(1), point3d[2] - translation(2));
                Vector3<T> reproj_v2 = rotation.transpose() * p_minus_t;

                // 归一化为方向向量
                T norm1 = reproj_v1.norm();
                T norm2 = reproj_v2.norm();

                const T epsilon = T(1e-12);
                if (norm1 < epsilon || norm2 < epsilon)
                {
                    // 设置大残差并返回
                    residuals[0] = T(1e6);
                    return true;
                }

                reproj_v1 /= norm1;
                reproj_v2 /= norm2;

                // 计算标量残差：(1 - dot1) + (1 - dot2)，与residual_opengv一致
                T dot1 = reproj_v1.dot(bearing1_.template cast<T>());
                T dot2 = reproj_v2.dot(bearing2_.template cast<T>());

                // 确保点积在有效范围内
                if (dot1 > T(1.0))
                    dot1 = T(1.0);
                if (dot1 < T(-1.0))
                    dot1 = T(-1.0);
                if (dot2 > T(1.0))
                    dot2 = T(1.0);
                if (dot2 < T(-1.0))
                    dot2 = T(-1.0);

                residuals[0] = (T(1.0) - dot1) + (T(1.0) - dot2);

                return true;
            }

        private:
            template <typename T>
            Matrix3<T> CayleyToRotation(const Vector3<T> &cayley) const
            {
                // 与types.hpp中的实现完全一致
                T c1 = cayley[0], c2 = cayley[1], c3 = cayley[2];
                T scale = T(1.0) + c1 * c1 + c2 * c2 + c3 * c3;

                Matrix3<T> R;
                R(0, 0) = T(1.0) + c1 * c1 - c2 * c2 - c3 * c3;
                R(0, 1) = T(2.0) * (c1 * c2 - c3);
                R(0, 2) = T(2.0) * (c1 * c3 + c2);
                R(1, 0) = T(2.0) * (c1 * c2 + c3);
                R(1, 1) = T(1.0) - c1 * c1 + c2 * c2 - c3 * c3;
                R(1, 2) = T(2.0) * (c2 * c3 - c1);
                R(2, 0) = T(2.0) * (c1 * c3 - c2);
                R(2, 1) = T(2.0) * (c2 * c3 + c1);
                R(2, 2) = T(1.0) - c1 * c1 - c2 * c2 + c3 * c3;

                // 数值稳定性检查
                if (scale < T(1e-12))
                {
                    // 退化情况：返回单位矩阵
                    return Matrix3<T>::Identity();
                }

                // 与types.hpp保持一致：先计算后除法
                R = R / scale;
                return R;
            }

            Vector3d bearing1_, bearing2_;
        };
    } // anonymous namespace

    bool CeresOptimizer::Optimize(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type)
    {
        if (log_level_ >= 1) // PO_LOG_NORMAL
        {
            std::cout << "\n[TwoViewOptimizer] Starting Ceres optimization..." << std::endl;
            std::cout << "Residual type: " << residual_type << ", Loss type: " << loss_type << std::endl;
        }

        RelativePose optimized_pose;
        bool success = false;

        // 检查是否为BA或OpenGV残差（需要pose+3D点联合优化）
        bool use_bundle_adjustment = (residual_type == "ba" || residual_type == "opengv");

        if (use_bundle_adjustment)
        {
            if (log_level_ >= 1)
            {
                std::cout << "Using Bundle Adjustment mode (pose + 3D points optimization)" << std::endl;
            }
            success = OptimizePoseWith3DPoints(points1, points2, pose, weights, residual_type, loss_type, optimized_pose);
        }
        else
        {
            if (log_level_ >= 1)
            {
                std::cout << "Using Pose-only optimization mode" << std::endl;
            }
            success = OptimizePoseOnly(points1, points2, pose, weights, residual_type, loss_type, optimized_pose);
        }

        if (success)
        {
            pose = optimized_pose;
        }

        return success;
    }

    bool CeresOptimizer::OptimizePoseOnly(
        const BearingVectors &points1,
        const BearingVectors &points2,
        const RelativePose &initial_pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type,
        RelativePose &optimized_pose) const
    {
        if (points1.cols() != points2.cols())
        {
            if (log_level_ >= 1)
            {
                std::cerr << "点数不匹配: " << points1.cols() << " vs " << points2.cols() << std::endl;
            }
            return false;
        }

        size_t num_points = points1.cols();
        if (num_points < 6)
        {
            if (log_level_ >= 1)
            {
                std::cerr << "点数不足，需要至少6个点进行优化" << std::endl;
            }
            return false;
        }

        // 初始化参数（球坐标系）
        double pose_params[5];
        Vector3d translation = initial_pose.tij.normalized();
        Vector3d cayley = RotationToCayley(initial_pose.Rij);

        // 转换为球坐标系
        double theta = std::acos(std::clamp(translation.z(), -1.0, 1.0)); // 天顶角
        double phi = std::atan2(translation.y(), translation.x());        // 方位角

        pose_params[0] = theta; // θ
        pose_params[1] = phi;   // φ
        pose_params[2] = cayley.x();
        pose_params[3] = cayley.y();
        pose_params[4] = cayley.z();

        if (log_level_ >= 1)
        {
            std::cout << "初始参数检查:" << std::endl;
            std::cout << "  θ = " << theta << " (" << (theta * 180.0 / M_PI) << "°)" << std::endl;
            std::cout << "  φ = " << phi << " (" << (phi * 180.0 / M_PI) << "°)" << std::endl;
            std::cout << "  cayley = [" << cayley.x() << ", " << cayley.y() << ", " << cayley.z() << "]" << std::endl;
            std::cout << "  初始旋转矩阵行列式: " << initial_pose.Rij.determinant() << std::endl;
        }

        // 计算初始cost
        double initial_cost = 0.0;
        RelativePose initial_pose_normalized = initial_pose;
        initial_pose_normalized.tij.normalize();

        const CostorInfoManager &manager = CostorInfoManager::GetInstance();
        VectorXd initial_residuals = manager.ComputeResidual(residual_type, points1, points2, initial_pose_normalized, weights);
        initial_cost = initial_residuals.squaredNorm();

        // 设置Ceres问题
        ceres::Problem problem;

        // 计算损失函数阈值
        double huber_threshold = GetHuberThreshold(residual_type);
        double cauchy_threshold = GetCauchyThreshold(residual_type);

        if (log_level_ >= 1)
        {
            std::cout << "损失函数阈值: (显式设置) huber=" << std::scientific << std::setprecision(6) << huber_threshold << ", cauchy=" << cauchy_threshold << std::endl;
        }

        // 为每个点添加残差块
        for (size_t i = 0; i < num_points; ++i)
        {
            double weight = (weights != nullptr) ? (*weights)(i) : 1.0;
            ceres::CostFunction *cost_function = nullptr;

            if (manager.Is6DResidual(residual_type))
            {
                cost_function = new ceres::AutoDiffCostFunction<TwoViewCeresCostFunction, 6, 5>(
                    new TwoViewCeresCostFunction(points1.col(i), points2.col(i), residual_type));
            }
            else if (manager.Is3DResidual(residual_type))
            {
                cost_function = new ceres::AutoDiffCostFunction<TwoViewCeresCostFunction, 3, 5>(
                    new TwoViewCeresCostFunction(points1.col(i), points2.col(i), residual_type));
            }
            else
            {
                cost_function = new ceres::AutoDiffCostFunction<TwoViewCeresCostFunction, 1, 5>(
                    new TwoViewCeresCostFunction(points1.col(i), points2.col(i), residual_type));
            }

            // 添加损失函数
            ceres::LossFunction *loss_function = nullptr;
            if (boost::iequals(loss_type, "huber"))
            {
                loss_function = new ceres::HuberLoss(huber_threshold);
            }
            else if (boost::iequals(loss_type, "cauchy"))
            {
                loss_function = new ceres::CauchyLoss(cauchy_threshold);
            }

            // 如果有权重，通过ScaledLoss包装损失函数来应用权重
            if (weight != 1.0 && loss_function != nullptr)
            {
                loss_function = new ceres::ScaledLoss(loss_function, weight * weight, ceres::TAKE_OWNERSHIP);
            }
            else if (weight != 1.0)
            {
                // 没有损失函数但有权重，使用ScaledLoss with nullptr
                loss_function = new ceres::ScaledLoss(nullptr, weight * weight, ceres::DO_NOT_TAKE_OWNERSHIP);
            }

            problem.AddResidualBlock(cost_function, loss_function, pose_params);
        }

        // 设置球坐标系参数的边界约束（放宽约束避免奇异）
        const double theta_epsilon = 0.01;    // 约0.57度，更宽松的边界
        const double phi_range = M_PI - 0.01; // 稍微缩小phi范围避免边界问题

        problem.SetParameterLowerBound(pose_params, 0, theta_epsilon);        // θ (天顶角) 下界
        problem.SetParameterUpperBound(pose_params, 0, M_PI - theta_epsilon); // θ (天顶角) 上界
        problem.SetParameterLowerBound(pose_params, 1, -phi_range);           // φ (方位角) 下界
        problem.SetParameterUpperBound(pose_params, 1, phi_range);            // φ (方位角) 上界

        if (log_level_ >= 1)
        {
            std::cout << "设置球坐标系参数边界约束: θ∈[" << theta_epsilon << ", " << (M_PI - theta_epsilon) << "], φ∈[" << -phi_range << ", " << phi_range << "]" << std::endl;
        }

        // 配置求解器选项
        ceres::Solver::Options options;
        options.linear_solver_type = ceres::DENSE_QR;
        options.minimizer_progress_to_stdout = iter_echo_on_; // 使用iter_echo_on_控制迭代显示
        options.max_num_iterations = max_iterations_;
        options.function_tolerance = convergence_threshold_;
        options.parameter_tolerance = convergence_threshold_;

        // 求解
        ceres::Solver::Summary summary;
        ceres::Solve(options, &problem, &summary);

        // 输出结果
        if (log_level_ >= 1)
        {
            std::cout << "Ceres优化结果: " << summary.BriefReport() << std::endl;
            std::cout << "原始残差平方和 (初始): " << std::scientific << std::setprecision(6) << initial_cost << std::endl;
            std::cout << "Ceres内部cost (初始): " << std::scientific << std::setprecision(6) << summary.initial_cost << " [含损失函数+0.5因子]" << std::endl;
            std::cout << "Ceres内部cost (最终): " << std::scientific << std::setprecision(6) << summary.final_cost << " [含损失函数+0.5因子]" << std::endl;
            std::cout << "迭代次数: " << summary.num_successful_steps << std::endl;
        }

        // 提取优化结果（球坐标系转换）
        double final_theta = pose_params[0]; // 天顶角
        double final_phi = pose_params[1];   // 方位角

        // 检查优化后参数的合理性
        if (log_level_ >= 1)
        {
            std::cout << "优化后参数检查:" << std::endl;
            std::cout << "  θ = " << final_theta << " (" << (final_theta * 180.0 / M_PI) << "°)" << std::endl;
            std::cout << "  φ = " << final_phi << " (" << (final_phi * 180.0 / M_PI) << "°)" << std::endl;
            std::cout << "  cayley = [" << pose_params[2] << ", " << pose_params[3] << ", " << pose_params[4] << "]" << std::endl;
        }

        // 球坐标系转换为笛卡尔坐标系
        double sin_final_theta = std::sin(final_theta);
        double cos_final_theta = std::cos(final_theta);
        double sin_final_phi = std::sin(final_phi);
        double cos_final_phi = std::cos(final_phi);

        optimized_pose.tij = Vector3d(sin_final_theta * cos_final_phi, sin_final_theta * sin_final_phi, cos_final_theta);
        Vector3d cayley_optimized(pose_params[2], pose_params[3], pose_params[4]);

        // 使用types.hpp中的CayleyToRotation确保一致性
        optimized_pose.Rij = CayleyToRotation(cayley_optimized);

        // 验证旋转矩阵的正交性
        double det = optimized_pose.Rij.determinant();
        double orthogonality_error = (optimized_pose.Rij * optimized_pose.Rij.transpose() - Matrix3d::Identity()).norm();

        if (log_level_ >= 1)
        {
            std::cout << "优化后位姿验证:" << std::endl;
            std::cout << "  平移向量模长: " << optimized_pose.tij.norm() << std::endl;
            std::cout << "  旋转矩阵行列式: " << det << std::endl;
            std::cout << "  正交性误差: " << orthogonality_error << std::endl;
        }

        // 检查数值稳定性
        if (std::abs(det - 1.0) > 1e-6 || orthogonality_error > 1e-6)
        {
            std::cerr << "警告: 优化后的旋转矩阵不满足正交性条件!" << std::endl;
            std::cerr << "  行列式: " << det << " (应该接近1.0)" << std::endl;
            std::cerr << "  正交性误差: " << orthogonality_error << " (应该接近0.0)" << std::endl;

            // 尝试通过SVD修正旋转矩阵
            Eigen::JacobiSVD<Matrix3d> svd(optimized_pose.Rij, Eigen::ComputeFullU | Eigen::ComputeFullV);
            optimized_pose.Rij = svd.matrixU() * svd.matrixV().transpose();

            // 确保行列式为正（右手坐标系）
            if (optimized_pose.Rij.determinant() < 0)
            {
                Matrix3d V_corrected = svd.matrixV();
                V_corrected.col(2) *= -1;
                optimized_pose.Rij = svd.matrixU() * V_corrected.transpose();
            }

            if (log_level_ >= 1)
            {
                std::cout << "已通过SVD修正旋转矩阵" << std::endl;
                std::cout << "  修正后行列式: " << optimized_pose.Rij.determinant() << std::endl;
                std::cout << "  修正后正交性误差: " << (optimized_pose.Rij * optimized_pose.Rij.transpose() - Matrix3d::Identity()).norm() << std::endl;
            }
        }

        // 确保设置正确的视图ID
        optimized_pose.i = initial_pose.i;
        optimized_pose.j = initial_pose.j;
        optimized_pose.weight = initial_pose.weight;

        // 更智能的成功判断条件（pose-only优化）
        bool success = false;
        double cost_improvement = (summary.initial_cost - summary.final_cost) / summary.initial_cost;

        // 检查多种成功条件
        if (summary.termination_type == ceres::CONVERGENCE ||
            summary.termination_type == ceres::USER_SUCCESS)
        {
            success = true;
        }
        // 即使是NO_CONVERGENCE，如果cost有显著改善且结果合理，也认为成功
        else if (summary.termination_type == ceres::NO_CONVERGENCE)
        {
            // 检查cost改善程度和旋转矩阵质量
            double det = optimized_pose.Rij.determinant();
            Matrix3d should_be_identity = optimized_pose.Rij * optimized_pose.Rij.transpose();
            double orthogonality_error = (should_be_identity - Matrix3d::Identity()).norm();

            if (cost_improvement > 0.1 &&    // cost改善超过10%
                std::abs(det - 1.0) < 0.1 && // 行列式接近1
                orthogonality_error < 1e-3)  // 正交性误差小
            {
                success = true;
                if (log_level_ >= 1)
                {
                    std::cout << "虽然未达到严格收敛，但cost改善" << (cost_improvement * 100.0) << "%，结果质量良好，认为pose-only优化成功" << std::endl;
                }
            }
        }

        return success;
    }

    bool CeresOptimizer::OptimizePoseWith3DPoints(
        const BearingVectors &points1,
        const BearingVectors &points2,
        const RelativePose &initial_pose,
        const VectorXd *weights,
        const std::string &residual_type,
        const std::string &loss_type,
        RelativePose &optimized_pose) const
    {
        const size_t num_points = points1.cols();

        if (log_level_ >= 1)
        {
            std::cout << "Bundle adjustment optimization (pose + 3D points)" << std::endl;
            std::cout << "Number of 3D points to optimize: " << num_points << std::endl;
        }

        // 初始化5个球坐标系位姿参数：theta, phi, cayley1, cayley2, cayley3
        double pose_params[5];

        // 从平移向量计算球坐标系参数
        Vector3d t_normalized = initial_pose.tij.normalized();
        double theta = std::acos(std::clamp(t_normalized(2), -1.0, 1.0)); // 天顶角: [0, π]
        double phi = std::atan2(t_normalized(1), t_normalized(0));        // 方位角: [-π, π]

        pose_params[0] = theta;
        pose_params[1] = phi;

        Vector3d cayley = RotationToCayley(initial_pose.Rij);
        pose_params[2] = cayley(0);
        pose_params[3] = cayley(1);
        pose_params[4] = cayley(2);

        // 使用三角化获得3D点初始值
        std::vector<double *> point3d_params(num_points);
        for (size_t i = 0; i < num_points; ++i)
        {
            point3d_params[i] = new double[3];

            Vector3d triangulated_point;
            if (residual_type == "ba")
            {
                triangulated_point = triangulateOnePoint(initial_pose.Rij, initial_pose.tij, points1.col(i), points2.col(i));
            }
            else
            { // opengv
                triangulated_point = triangulate2(initial_pose.Rij, initial_pose.tij, points1.col(i), points2.col(i));
            }

            point3d_params[i][0] = triangulated_point(0);
            point3d_params[i][1] = triangulated_point(1);
            point3d_params[i][2] = triangulated_point(2);

            // 确保3D点在相机前方
            if (point3d_params[i][2] < 1e-6)
            {
                point3d_params[i][2] = 1e-6;
            }
        }

        if (log_level_ >= 1)
        {
            std::cout << "设置球坐标系参数边界约束: θ∈[0.01, " << (M_PI - 0.01) << "], φ∈[" << -M_PI << ", " << M_PI << "]" << std::endl;
            std::cout << "初始球坐标系参数: θ=" << pose_params[0] << " (" << (pose_params[0] * 180.0 / M_PI) << "°), φ=" << pose_params[1] << " (" << (pose_params[1] * 180.0 / M_PI) << "°)" << std::endl;
            std::cout << "三角化得到 " << num_points << " 个3D点作为初始值" << std::endl;
        }

        // 创建Ceres问题
        ceres::Problem problem;

        // 为每个点添加Bundle Adjustment残差块
        for (size_t i = 0; i < num_points; ++i)
        {
            double weight = (weights != nullptr) ? (*weights)(i) : 1.0;

            ceres::CostFunction *cost_function = nullptr;

            if (residual_type == "ba")
            {
                // BA残差：6维向量 [diff1; diff2]
                cost_function = new ceres::AutoDiffCostFunction<BundleAdjustmentCostFunction, 6, 5, 3>(
                    new BundleAdjustmentCostFunction(points1.col(i), points2.col(i)));
            }
            else
            { // opengv
                // OpenGV残差：1维标量 (1 - dot1) + (1 - dot2)
                cost_function = new ceres::AutoDiffCostFunction<OpenGVBundleCostFunction, 1, 5, 3>(
                    new OpenGVBundleCostFunction(points1.col(i), points2.col(i)));
            }

            // 添加损失函数
            ceres::LossFunction *loss_function = nullptr;
            double huber_threshold = GetHuberThreshold(residual_type);
            double cauchy_threshold = GetCauchyThreshold(residual_type);

            if (loss_type == "huber")
            {
                loss_function = new ceres::HuberLoss(huber_threshold);
            }
            else if (loss_type == "cauchy")
            {
                loss_function = new ceres::CauchyLoss(cauchy_threshold);
            }

            // 如果有权重，通过ScaledLoss包装损失函数来应用权重
            if (weight != 1.0 && loss_function != nullptr)
            {
                loss_function = new ceres::ScaledLoss(loss_function, weight * weight, ceres::TAKE_OWNERSHIP);
            }
            else if (weight != 1.0)
            {
                // 没有损失函数但有权重，使用ScaledLoss with nullptr
                loss_function = new ceres::ScaledLoss(nullptr, weight * weight, ceres::DO_NOT_TAKE_OWNERSHIP);
            }

            problem.AddResidualBlock(cost_function, loss_function, pose_params, point3d_params[i]);
        }

        // 设置球坐标系参数边界约束
        problem.SetParameterLowerBound(pose_params, 0, 0.01);        // θ下界
        problem.SetParameterUpperBound(pose_params, 0, M_PI - 0.01); // θ上界
        problem.SetParameterLowerBound(pose_params, 1, -M_PI);       // φ下界
        problem.SetParameterUpperBound(pose_params, 1, M_PI);        // φ上界

        // 设置3D点深度约束（避免点在相机后方）
        for (size_t i = 0; i < num_points; ++i)
        {
            problem.SetParameterLowerBound(point3d_params[i], 2, 1e-6); // z > 1e-6
        }

        // 配置求解器选项（使用Schur求解器适合Bundle Adjustment）
        ceres::Solver::Options options;
        options.linear_solver_type = ceres::SPARSE_SCHUR;     // 使用稀疏Schur分解，适合大规模BA问题
        options.minimizer_progress_to_stdout = iter_echo_on_; // 使用iter_echo_on_控制迭代显示
        options.max_num_iterations = max_iterations_;
        options.function_tolerance = convergence_threshold_;
        options.parameter_tolerance = convergence_threshold_;

        if (log_level_ >= 1)
        {
            std::cout << "配置Bundle Adjustment求解器选项: linear_solver=SPARSE_SCHUR" << std::endl;
        }

        // 计算初始残差
        double initial_cost = 0.0;
        problem.Evaluate(ceres::Problem::EvaluateOptions(), &initial_cost, nullptr, nullptr, nullptr);

        // 求解
        ceres::Solver::Summary summary;
        ceres::Solve(options, &problem, &summary);

        // 输出结果
        if (log_level_ >= 1)
        {
            std::cout << "Ceres优化结果: " << summary.BriefReport() << std::endl;
            std::cout << "原始残差平方和 (初始): " << std::scientific << std::setprecision(6) << initial_cost << std::endl;
            std::cout << "Ceres内部cost (初始): " << std::scientific << std::setprecision(6) << summary.initial_cost << " [含损失函数+0.5因子]" << std::endl;
            std::cout << "Ceres内部cost (最终): " << std::scientific << std::setprecision(6) << summary.final_cost << " [含损失函数+0.5因子]" << std::endl;
            std::cout << "迭代次数: " << summary.num_successful_steps << std::endl;
        }

        // 从球坐标系参数恢复位姿
        double final_theta = pose_params[0];
        double final_phi = pose_params[1];

        // 球坐标系转换为笛卡尔坐标系
        double sin_theta = std::sin(final_theta);
        double cos_theta = std::cos(final_theta);
        double sin_phi = std::sin(final_phi);
        double cos_phi = std::cos(final_phi);

        optimized_pose.tij = Vector3d(sin_theta * cos_phi, sin_theta * sin_phi, cos_theta);
        optimized_pose.Rij = CayleyToRotation(Vector3d(pose_params[2], pose_params[3], pose_params[4]));

        // 设置视图ID
        optimized_pose.i = initial_pose.i;
        optimized_pose.j = initial_pose.j;

        // 检查旋转矩阵正交性
        double det = optimized_pose.Rij.determinant();
        Matrix3d should_be_identity = optimized_pose.Rij * optimized_pose.Rij.transpose();
        double orthogonality_error = (should_be_identity - Matrix3d::Identity()).norm();

        if (log_level_ >= 1)
        {
            std::cout << "最终球坐标系参数: θ=" << final_theta << " (" << (final_theta * 180.0 / M_PI) << "°), φ=" << final_phi << " (" << (final_phi * 180.0 / M_PI) << "°)" << std::endl;
            std::cout << "旋转矩阵行列式: " << det << " (应接近1)" << std::endl;
            std::cout << "旋转矩阵正交性误差: " << orthogonality_error << " (应接近0)" << std::endl;
        }

        // 如果旋转矩阵不正交，使用SVD修正
        if (orthogonality_error > 1e-6)
        {
            Eigen::JacobiSVD<Matrix3d> svd(optimized_pose.Rij, Eigen::ComputeFullU | Eigen::ComputeFullV);
            Matrix3d U = svd.matrixU();
            Matrix3d V = svd.matrixV();
            optimized_pose.Rij = U * V.transpose();

            // 确保行列式为正
            if (optimized_pose.Rij.determinant() < 0)
            {
                U.col(2) = -U.col(2);
                optimized_pose.Rij = U * V.transpose();
            }

            if (log_level_ >= 1)
            {
                std::cout << "已使用SVD修正非正交旋转矩阵" << std::endl;
            }
        }

        // 清理内存
        for (size_t i = 0; i < num_points; ++i)
        {
            delete[] point3d_params[i];
        }

        // 更智能的成功判断条件
        bool success = false;
        double cost_improvement = (summary.initial_cost - summary.final_cost) / summary.initial_cost;

        // 检查多种成功条件
        if (summary.termination_type == ceres::CONVERGENCE ||
            summary.termination_type == ceres::USER_SUCCESS)
        {
            success = true;
        }
        // 即使是NO_CONVERGENCE，如果cost有显著改善且结果合理，也认为成功
        else if (summary.termination_type == ceres::NO_CONVERGENCE)
        {
            // 检查cost改善程度和旋转矩阵质量
            double det = optimized_pose.Rij.determinant();
            Matrix3d should_be_identity = optimized_pose.Rij * optimized_pose.Rij.transpose();
            double orthogonality_error = (should_be_identity - Matrix3d::Identity()).norm();

            if (cost_improvement > 0.1 &&    // cost改善超过10%
                std::abs(det - 1.0) < 0.1 && // 行列式接近1
                orthogonality_error < 1e-3)  // 正交性误差小
            {
                success = true;
                if (log_level_ >= 1)
                {
                    std::cout << "虽然未达到严格收敛，但cost改善" << (cost_improvement * 100.0) << "%，结果质量良好，认为优化成功" << std::endl;
                }
            }
        }

        if (log_level_ >= 1)
        {
            std::cout << "Bundle Adjustment优化 " << (success ? "成功" : "失败") << std::endl;
            std::cout << "Cost改善: " << (cost_improvement * 100.0) << "%" << std::endl;
        }

        return success;
    }

    double CeresOptimizer::GetHuberThreshold(const std::string &residual_type) const
    {
        if (huber_threshold_explicit_ >= 0)
        {
            return huber_threshold_explicit_;
        }

        const CostorInfoManager &manager = CostorInfoManager::GetInstance();
        ErrorFormType error_form = manager.GetErrorFormType(residual_type);

        if (error_form == ErrorFormType::REPROJ_ANGLE)
        {
            return ComputeHuberThresholdFromAngle(residual_type, reproj_theta_threshold_deg_);
        }
        else if (error_form == ErrorFormType::REPROJ_PIXEL)
        {
            return reproj_theta_threshold_deg_;
        }
        else
        {
            return 1e-3;
        }
    }

    double CeresOptimizer::GetCauchyThreshold(const std::string &residual_type) const
    {
        if (cauchy_threshold_explicit_ >= 0)
        {
            return cauchy_threshold_explicit_;
        }

        const CostorInfoManager &manager = CostorInfoManager::GetInstance();
        ErrorFormType error_form = manager.GetErrorFormType(residual_type);

        if (error_form == ErrorFormType::REPROJ_ANGLE)
        {
            return ComputeCauchyThresholdFromAngle(residual_type, reproj_theta_threshold_deg_);
        }
        else if (error_form == ErrorFormType::REPROJ_PIXEL)
        {
            return reproj_theta_threshold_deg_;
        }
        else
        {
            return 1e-3;
        }
    }

    double CeresOptimizer::ComputeHuberThresholdFromAngle(const std::string &residual_type, double angle_deg) const
    {
        double angle_rad = angle_deg * M_PI / 180.0;

        if (boost::iequals(residual_type, "ppo_opengv"))
        {
            return 2.0 * (1.0 - std::cos(angle_rad));
        }
        else if (boost::iequals(residual_type, "ppo_angle"))
        {
            return 1.0 - std::cos(angle_rad);
        }
        else if (boost::iequals(residual_type, "ppo_angle_rad"))
        {
            return angle_rad;
        }
        else if (boost::iequals(residual_type, "ppo_angle_sin"))
        {
            return std::sin(angle_rad);
        }
        else if (boost::iequals(residual_type, "ppo_angle_2sin"))
        {
            return 2.0 * std::sin(angle_rad / 2.0);
        }
        else if (boost::iequals(residual_type, "ppo_angle_sqrt"))
        {
            return std::sqrt(1.0 - std::cos(angle_rad));
        }
        else
        {
            return angle_rad;
        }
    }

    double CeresOptimizer::ComputeCauchyThresholdFromAngle(const std::string &residual_type, double angle_deg) const
    {
        double angle_rad = angle_deg * M_PI / 180.0;

        if (boost::iequals(residual_type, "ppo_opengv"))
        {
            return 2.0 * (1.0 - std::cos(angle_rad));
        }
        else if (boost::iequals(residual_type, "ppo_angle"))
        {
            return 1.0 - std::cos(angle_rad);
        }
        else if (boost::iequals(residual_type, "ppo_angle_rad"))
        {
            return angle_rad;
        }
        else if (boost::iequals(residual_type, "ppo_angle_sin"))
        {
            return std::sin(angle_rad);
        }
        else if (boost::iequals(residual_type, "ppo_angle_2sin"))
        {
            return 2.0 * std::sin(angle_rad / 2.0);
        }
        else if (boost::iequals(residual_type, "ppo_angle_sqrt"))
        {
            return std::sqrt(1.0 - std::cos(angle_rad));
        }
        else
        {
            return angle_rad;
        }
    }

} // namespace PoSDK