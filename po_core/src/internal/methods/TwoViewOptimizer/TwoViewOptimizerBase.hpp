/**
 * @file TwoViewOptimizerBase.hpp
 * @brief 双视图位姿优化器基类
 * @copyright Copyright (c) 2024 Qi Cai
 */

#ifndef PO_CORE_TWO_VIEW_OPTIMIZER_BASE_HPP
#define PO_CORE_TWO_VIEW_OPTIMIZER_BASE_HPP

#include "relative_pose.hpp"
#include <Eigen/Dense>
#include <string>

namespace PoSDK
{
    using BearingVectors = Eigen::Matrix<double, 3, Eigen::Dynamic>;
    using VectorXd = Eigen::VectorXd;

    /**
     * @brief 双视图位姿优化器基类
     */
    class TwoViewOptimizerBase
    {
    public:
        TwoViewOptimizerBase() = default;
        virtual ~TwoViewOptimizerBase() = default;

        /**
         * @brief 执行位姿优化
         * @param points1 第一个视图的观测向量
         * @param points2 第二个视图的观测向量
         * @param pose 输入初始位姿，输出优化后位姿
         * @param weights 权重向量（可为nullptr）
         * @param residual_type 残差函数类型
         * @param loss_type 损失函数类型
         * @return 是否优化成功
         */
        virtual bool Optimize(
            const BearingVectors &points1,
            const BearingVectors &points2,
            RelativePose &pose,
            const VectorXd *weights = nullptr,
            const std::string &residual_type = "ppo_opengv",
            const std::string &loss_type = "huber") = 0;

        /**
         * @brief 设置最大迭代次数
         * @param max_iterations 最大迭代次数
         */
        virtual void SetMaxIterations(int max_iterations) { max_iterations_ = max_iterations; }

        /**
         * @brief 设置收敛阈值
         * @param threshold 收敛阈值
         */
        virtual void SetConvergenceThreshold(double threshold) { convergence_threshold_ = threshold; }

        /**
         * @brief 设置重投影角度阈值
         * @param threshold_deg 重投影角度阈值（度）
         */
        virtual void SetReprojThetaThreshold(double threshold_deg) { reproj_theta_threshold_deg_ = threshold_deg; }

        /**
         * @brief 设置日志级别
         * @param log_level 日志级别
         */
        virtual void SetLogLevel(int log_level) { log_level_ = log_level; }

        /**
         * @brief 设置显式Huber损失阈值
         * @param threshold Huber损失阈值（-1表示使用自动计算）
         */
        virtual void SetHuberThresholdExplicit(double threshold) { huber_threshold_explicit_ = threshold; }

        /**
         * @brief 设置显式Cauchy损失阈值
         * @param threshold Cauchy损失阈值（-1表示使用自动计算）
         */
        virtual void SetCauchyThresholdExplicit(double threshold) { cauchy_threshold_explicit_ = threshold; }

        /**
         * @brief 设置是否显示迭代信息
         * @param enable 是否启用迭代信息显示
         */
        virtual void SetIterInfo(bool enable) { iter_echo_on_ = enable; }

    protected:
        int max_iterations_{50};                  ///< 最大迭代次数
        double convergence_threshold_{1e-8};      ///< 收敛阈值
        double reproj_theta_threshold_deg_{0.57}; ///< 重投影角度阈值（度）
        double huber_threshold_explicit_{-1.0};   ///< 显式Huber损失阈值（-1表示使用自动计算）
        double cauchy_threshold_explicit_{-1.0};  ///< 显式Cauchy损失阈值（-1表示使用自动计算）
        int log_level_{0};                        ///< 日志级别，与PoSDK日志系统一致
        bool iter_echo_on_{false};                ///< 是否显示迭代信息（独立于log_level_）
    };

} // namespace PoSDK

#endif // PO_CORE_TWO_VIEW_OPTIMIZER_BASE_HPP