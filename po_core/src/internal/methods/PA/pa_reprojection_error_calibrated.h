// Ceres Solver - A fast non-linear least squares minimizer
// Copyright 2015 Google Inc. All rights reserved.
// http://ceres-solver.org/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
// * Redistributions of source code must retain the above copyright notice,
//   this list of conditions and the following disclaimer.
// * Redistributions in binary form must reproduce the above copyright notice,
//   this list of conditions and the following disclaimer in the documentation
//   and/or other materials provided with the distribution.
// * Neither the name of Google Inc. nor the names of its contributors may be
//   used to endorse or promote products derived from this software without
//   specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
// ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
// LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
// CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
// SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
// INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
// CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
// ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
// POSSIBILITY OF SUCH DAMAGE.
//
// Author: <EMAIL> (Sameer Agarwal)
//
// Templated struct implementing the camera model and residual
// computation for bundle adjustment used by Noah Snavely's Bundler
// SfM system. This is also the camera model/residual for the bundle
// adjustment problems in the BAL dataset. It is templated so that we
// can use Ceres's automatic differentiation to compute analytic
// jacobians.
//
// For details see: http://phototour.cs.washington.edu/bundler/
// and http://grail.cs.washington.edu/projects/bal/

#ifndef CERES_EXAMPLES_SNAVELY_REPROJECTION_ERROR_H_
#define CERES_EXAMPLES_SNAVELY_REPROJECTION_ERROR_H_

#include "ceres/rotation.h"
#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/Sparse>
namespace ceres
{
    namespace examples
    {

        template <typename T>
        inline void AngleAxisRotatePoint_double(const T angle_axis[3], const double pt[3], T result[3])
        {
            const T theta2 = DotProduct(angle_axis, angle_axis);
            if (theta2 > T(std::numeric_limits<double>::epsilon()))
            {
                // Away from zero, use the rodriguez formula
                //
                //   result = pt costheta +
                //            (w x pt) * sintheta +
                //            w (w . pt) (1 - costheta)
                //
                // We want to be careful to only evaluate the square root if the
                // norm of the angle_axis vector is greater than zero. Otherwise
                // we get a division by zero.
                //
                const T theta = sqrt(theta2);
                const T costheta = cos(theta);
                const T sintheta = sin(theta);
                const T theta_inverse = T(1.0) / theta;

                const T w[3] = {angle_axis[0] * theta_inverse,
                                angle_axis[1] * theta_inverse,
                                angle_axis[2] * theta_inverse};

                // Explicitly inlined evaluation of the cross product for
                // performance reasons.
                const T w_cross_pt[3] = {w[1] * pt[2] - w[2] * pt[1],
                                         w[2] * pt[0] - w[0] * pt[2],
                                         w[0] * pt[1] - w[1] * pt[0]};
                const T tmp =
                    (w[0] * pt[0] + w[1] * pt[1] + w[2] * pt[2]) * (T(1.0) - costheta);

                result[0] = pt[0] * costheta + w_cross_pt[0] * sintheta + w[0] * tmp;
                result[1] = pt[1] * costheta + w_cross_pt[1] * sintheta + w[1] * tmp;
                result[2] = pt[2] * costheta + w_cross_pt[2] * sintheta + w[2] * tmp;
            }
            else
            {
                // Near zero, the first order Taylor approximation of the rotation
                // matrix R corresponding to a vector w and angle w is
                //
                //   R = I + hat(w) * sin(theta)
                //
                // But sintheta ~ theta and theta * w = angle_axis, which gives us
                //
                //  R = I + hat(w)
                //
                // and actually performing multiplication with the point pt, gives us
                // R * pt = pt + w x pt.
                //
                // Switching to the Taylor expansion near zero provides meaningful
                // derivatives when evaluated using Jets.
                //
                // Explicitly inlined evaluation of the cross product for
                // performance reasons.
                const T w_cross_pt[3] = {angle_axis[1] * pt[2] - angle_axis[2] * pt[1],
                                         angle_axis[2] * pt[0] - angle_axis[0] * pt[2],
                                         angle_axis[0] * pt[1] - angle_axis[1] * pt[0]};

                result[0] = w_cross_pt[0] + pt[0];
                result[1] = w_cross_pt[1] + pt[1];
                result[2] = w_cross_pt[2] + pt[2];
            }
        }
        // xy = x cross y;
        template <typename T>
        inline void CrossProduct_double(const T x[3], const double y[3], T x_cross_y[3])
        {
            x_cross_y[0] = x[1] * y[2] - x[2] * y[1];
            x_cross_y[1] = x[2] * y[0] - x[0] * y[2];
            x_cross_y[2] = x[0] * y[1] - x[1] * y[0];
        }

        // Templated pinhole camera model for used with Ceres.  The camera is
        // parameterized using 9 parameters: 3 for rotation, 3 for translation, 1 for
        // focal length and 2 for radial distortion. The principal point is not modeled
        // (i.e. it is assumed be located at the image center).
        struct PAReprojectionError_calib
        {
            PAReprojectionError_calib(double observed_x, double observed_y,
                                      double *intrinsic_params,
                                      int left_base_view,
                                      int right_base_view,
                                      double *left_base_obs,
                                      double *right_base_obs,
                                      bool is_right_current_views_equal,
                                      int point_id)
                : observed_x(observed_x), observed_y(observed_y),
                  intrinsic_params(intrinsic_params),
                  left_base_view(left_base_view),
                  right_base_view(right_base_view),
                  left_base_obs(left_base_obs),
                  right_base_obs(right_base_obs),
                  is_right_current_views_equal(is_right_current_views_equal),
                  point_id(point_id)
            {
            }

            template <typename T>
            //    bool operator()(const T*  left_base_camera,
            //                    const T*  right_base_camera,
            //                    const T*  current_camera,
            //                    T* residuals) const {
            //        // load rotation
            //        Eigen::Matrix3d R_l;
            //        double rot[3];
            //        rot[0] = *(double*)(left_base_camera+0);
            //        rot[1] = *(double*)(left_base_camera+1);
            //        rot[2] = *(double*)(left_base_camera+2);

            //        AngleAxisToRotationMatrix(rot,R_l.data());

            //        Eigen::Matrix3d R_r;
            //        rot[0] = *(double*)(right_base_camera+0);
            //        rot[1] = *(double*)(right_base_camera+1);
            //        rot[2] = *(double*)(right_base_camera+2);
            //        AngleAxisToRotationMatrix(rot,R_r.data());

            //        Eigen::Matrix3d R_c;
            //        if (is_right_current_views_equal){
            //            // if equal, then use right_base_camera
            //            R_c = R_r;
            //        }
            //        else{
            //            // if no equal, then use current_camera
            //            rot[0] = *(double*)(current_camera+0);
            //            rot[1] = *(double*)(current_camera+1);
            //            rot[2] = *(double*)(current_camera+2);

            //            AngleAxisToRotationMatrix(rot,R_c.data());
            //        }

            //        // load translation
            //        Eigen::Vector3d t_l;
            //        t_l(0) = *((double*)(left_base_camera+3));
            //        t_l(1) = *((double*)(left_base_camera+4));
            //        t_l(2) = *((double*)(left_base_camera+5));

            //        Eigen::Vector3d t_r;
            //        t_r(0) = *((double*)(right_base_camera+3));
            //        t_r(1) = *((double*)(right_base_camera+4));
            //        t_r(2) = *((double*)(right_base_camera+5));

            //        Eigen::Vector3d t_c;

            //        if (is_right_current_views_equal){
            //            // if equal, then use right_base_camera
            //            t_c = t_r;
            //        }
            //        else{
            //            // if no equal, then use current_camera
            //            t_c(0) = *((double*)(current_camera+3));
            //            t_c(1) = *((double*)(current_camera+4));
            //            t_c(2) = *((double*)(current_camera+5));
            //        }

            //        // load observation
            //        Eigen::Vector3d x_l;
            //        x_l(0) = -left_base_obs[0];
            //        x_l(1) = -left_base_obs[1];
            //        x_l(2) = 1;

            //        Eigen::Vector3d x_r;
            //        x_r(0) = -right_base_obs[0];
            //        x_r(1) = -right_base_obs[1];
            //        x_r(2) = 1;

            //        Eigen::Vector3d x_c;
            //        x_c(0) = -observed_x;
            //        x_c(1) = -observed_y;
            //        x_c(2) = 1;

            //        Eigen::Vector3d t_lr  = t_r-R_r*(R_l.transpose()*t_l);
            //        Eigen::Vector3d t_lc  = t_c-R_c*(R_l.transpose()*t_l);

            //        Eigen::Vector3d Rl_xl = R_l.transpose()*x_l;

            //        double s1  = (t_lr.cross(x_r)).norm();
            //        Eigen::Vector3d Rx  = R_r*Rl_xl;
            //        Eigen::Vector3d xRx = x_r.cross(Rx);
            //        double s2  = xRx.norm();

            //        Eigen::Vector3d Rc_Rl_xl =R_c*Rl_xl;
            //        Eigen::Vector3d p = s1 * Rc_Rl_xl + s2 * t_lc;

            //        T xp = T(-p.data()[0]);
            //        T yp = T(-p.data()[1]);

            //        xp/=p[2];
            //        yp/=p[2];
            //        // Apply second and fourth order radial distortion.

            //        double l1 = intrinsic_params[1];
            //        double l2 = intrinsic_params[2];
            //        //    const T& l1 = camera[7];
            //        //    const T& l2 = camera[8];
            //        const T r2 = xp*xp + yp*yp;
            //        const T distortion = 1.0 + r2  * (l1 + l2  * r2);

            //        // Compute final projected point position.
            //        //    const T& focal = camera[6];
            //        double focal = intrinsic_params[0];
            //        const T predicted_x = focal * distortion * xp;
            //        const T predicted_y = focal * distortion * yp;

            //        // The error is the difference between the predicted and observed position.
            //        residuals[0] = predicted_x - observed_x;
            //        residuals[1] = predicted_y - observed_y;

            //        return true;
            //    }
            bool operator()(const T *left_base_camera,
                            const T *right_base_camera,
                            const T *current_camera,
                            T *residuals) const
            {
                const T *t_l = left_base_camera + 3;
                const T *t_r = right_base_camera + 3;

                if (is_right_current_views_equal)
                {
                    // if equal, then use right_base_camera
                }
                else
                {
                    // if no equal, then use current_camera
                }

                if (is_right_current_views_equal)
                {
                    // if equal, then use right_base_camera
                }
                else
                {
                    // if no equal, then use current_camera
                }

                // load observation
                Eigen::Vector3d x_l;
                x_l(0) = -left_base_obs[0];
                x_l(1) = -left_base_obs[1];
                x_l(2) = 1;

                Eigen::Vector3d x_r;
                x_r(0) = -right_base_obs[0];
                x_r(1) = -right_base_obs[1];
                x_r(2) = 1;

                Eigen::Vector3d x_c;
                x_c(0) = -observed_x;
                x_c(1) = -observed_y;
                x_c(2) = 1;

                // R_l.transpose()
                T left_base_camera_T[3];
                left_base_camera_T[0] = -left_base_camera[0];
                left_base_camera_T[1] = -left_base_camera[1];
                left_base_camera_T[2] = -left_base_camera[2];

                // Rl_tl = R_l.transpose() * t_l
                T Rl_tl[3];
                AngleAxisRotatePoint(left_base_camera_T, t_l, Rl_tl);

                // t_lr = t_r - R_r * ( R_l.transpose() * t_l );
                T t_lr[3];
                AngleAxisRotatePoint(right_base_camera, Rl_tl, t_lr);
                t_lr[0] = t_r[0] - t_lr[0];
                t_lr[1] = t_r[1] - t_lr[1];
                t_lr[2] = t_r[2] - t_lr[2];

                // t_lc  = t_c - R_c * ( R_l.transpose() * t_l );
                T t_lc[3];
                if (is_right_current_views_equal)
                {
                    t_lc[0] = t_lr[0];
                    t_lc[1] = t_lr[1];
                    t_lc[2] = t_lr[2];
                }
                else
                {
                    AngleAxisRotatePoint(current_camera, Rl_tl, t_lc);
                    const T *t_c = current_camera + 3;
                    t_lc[0] = t_c[0] - t_lc[0];
                    t_lc[1] = t_c[1] - t_lc[1];
                    t_lc[2] = t_c[2] - t_lc[2];
                }

                // Rl_xl = R_l.transpose()*x_l;
                T Rl_xl[3];
                AngleAxisRotatePoint_double(left_base_camera_T, x_l.data(), Rl_xl);

                // tlr_cross_xr = t_lr.cross(x_r)
                T tlr_cross_xr[3];
                CrossProduct_double(t_lr, x_r.data(), tlr_cross_xr);

                // s1  = tlr_cross_xr.norm();
                T s1 = DotProduct(tlr_cross_xr, tlr_cross_xr);
                s1 = sqrt(s1);

                // Rlr_xl = R_r*Rl_xl;
                T Rlr_xl[3];
                AngleAxisRotatePoint(right_base_camera, Rl_xl, Rlr_xl);

                // R_xl_cross_xr = Rlr_xl.cross(x_r);
                T R_xl_cross_xr[3];
                CrossProduct_double(Rlr_xl, x_r.data(), R_xl_cross_xr);

                T s2 = DotProduct(R_xl_cross_xr, R_xl_cross_xr);
                s2 = sqrt(s2);

                // Rcl_xl =R_c*Rl_xl;
                T Rcl_xl[3];
                if (is_right_current_views_equal)
                {
                    AngleAxisRotatePoint(right_base_camera, Rl_xl, Rcl_xl);
                }
                else
                {
                    AngleAxisRotatePoint(current_camera, Rl_xl, Rcl_xl);
                }

                T p[3];
                //        double threshold=1e-6;
                //        if (s1<s2 && s1<threshold){
                //            p[0] =  Rcl_xl[0] + t_lc[0];
                //            p[1] =  Rcl_xl[1] + t_lc[1];
                //            p[2] =  Rcl_xl[2] + t_lc[2];
                //        }
                //        else{
                //            p[0] = (s1+threshold)/(s2+threshold) * Rcl_xl[0] + t_lc[0];
                //            p[1] = (s1+threshold)/(s2+threshold) * Rcl_xl[1] + t_lc[1];
                //            p[2] = (s1+threshold)/(s2+threshold) * Rcl_xl[2] + t_lc[2];
                //        }
                p[0] = s1 * Rcl_xl[0] + s2 * t_lc[0];
                p[1] = s1 * Rcl_xl[1] + s2 * t_lc[1];
                p[2] = s1 * Rcl_xl[2] + s2 * t_lc[2];
                const T xp = -p[0] / p[2];
                const T yp = -p[1] / p[2];
                // Apply second and fourth order radial distortion.

                double l1 = intrinsic_params[1];
                double l2 = intrinsic_params[2];
                //    const T& l1 = camera[7];
                //    const T& l2 = camera[8];
                const T r2 = xp * xp + yp * yp;
                const T distortion = 1.0 + r2 * (l1 + l2 * r2);

                // Compute final projected point position.
                //    const T& focal = camera[6];
                // double focal = intrinsic_params[0];
                const double focal = 1.0;
                const T predicted_x = focal * distortion * xp;
                const T predicted_y = focal * distortion * yp;

                // The error is the difference between the predicted and observed position.
                residuals[0] = predicted_x - observed_x;
                residuals[1] = predicted_y - observed_y;

                return true;
            }
            // Factory to hide the construction of the CostFunction object from
            // the client code.
            static ceres::CostFunction *Create(double observed_x,
                                               double observed_y,
                                               double *intrinsic_params,
                                               int left_base_view,
                                               int right_base_view,
                                               double *left_base_obs,
                                               double *right_base_obs,
                                               bool is_right_current_views_equal,
                                               int point_id)
            {
                // left_base_view:6
                // right_base_view:6
                // current_view:6
                return (new ceres::AutoDiffCostFunction<PAReprojectionError_calib, 2, 6, 6, 6>(
                    new PAReprojectionError_calib(observed_x, observed_y,
                                                  intrinsic_params,
                                                  left_base_view,
                                                  right_base_view,
                                                  left_base_obs,
                                                  right_base_obs,
                                                  is_right_current_views_equal,
                                                  point_id)));
            }

            double observed_x;
            double observed_y;
            double *intrinsic_params;
            int left_base_view;
            int right_base_view;

            double *left_base_obs;
            double *right_base_obs;
            bool is_right_current_views_equal;
            int point_id;
        };

    } // namespace examples
} // namespace ceres

#endif // CERES_EXAMPLES_SNAVELY_REPROJECTION_ERROR_H_
