/**
 * @file method_pa.cpp
 * @brief 实现基于Ceres的位姿优化方法
 */

#include "method_PA.hpp"
#include "PA/pa_reprojection_error_calibrated.h"
#include <filesystem>
#include "factory.hpp"
#include <cstddef>

namespace PoSDK
{
    DataPtr MethodPA::Run()
    {
        try
        {
            DisplayConfigInfo();

            // 获取输入数据
            auto tracks_ptr = Interface::GetDataPtr<Tracks>(required_package_["data_tracks"]);
            auto camera_models_ptr = Interface::GetDataPtr<CameraModels>(required_package_["data_camera_models"]);
            auto poses_ptr = Interface::GetDataPtr<GlobalPoses>(required_package_["data_global_poses"]);

            if (!tracks_ptr || !camera_models_ptr || !poses_ptr)
            {
                std::cerr << "[MethodPA] Missing required input data" << std::endl;
                return nullptr;
            }

            // 检查轨迹坐标是否已经归一化，如果没有，则使用相机模型进行归一化
            if (!tracks_ptr->IsNormalized())
            {
                std::cout << "[MethodPA] 轨迹坐标未归一化，使用相机模型进行归一化处理..." << std::endl;
                if (!tracks_ptr->NormalizeTracks(*camera_models_ptr))
                {
                    std::cerr << "[MethodPA] 警告：轨迹归一化失败，可能影响BA优化精度" << std::endl;
                }
            }

            num_cameras_ = poses_ptr->Size();

            // 创建Ceres问题
            ::ceres::Problem problem;

            // 构建优化问题
            BuildProblem(*tracks_ptr, *camera_models_ptr, *poses_ptr, problem);

            // 配置求解器
            ::ceres::Solver::Options options;
            SetSolverOptions(options);

            // 求解
            ::ceres::Solver::Summary summary;
            ::ceres::Solve(options, &problem, &summary); // 崩溃，显示检测到invalid value

            // 输出求解结果
            if (GetOptionAsIndexT("log_level", 1) > 0)
            {
                std::cout << summary.FullReport() << "\n";
            }

            // 从bal_problem中提取优化后的位姿并更新原始数据
            const double *cameras = parameters_;
            // poses_ptr->rotations.clear(); // 不直接清空，而是逐个更新
            // poses_ptr->translations.clear();

            for (Size i = 0; i < num_cameras_; ++i)
            {
                const double *camera = cameras + i * 6; // Ceres优化后的相机参数
                ViewId original_view_id = i;            // 假设parameters_中的顺序与原始ID一致，并且是连续的

                // 在PA中，我们通常优化所有参与的视图
                // 如果EstInfo是准确的，可以基于est_info.est2origin_id来获取original_view_id
                if (i < poses_ptr->est_info.est2origin_id.size())
                {
                    original_view_id = poses_ptr->est_info.est2origin_id[i];
                }
                else
                {
                    // 如果est_info.est2origin_id不够大，可能意味着并非所有相机都参与了PA优化
                    // 或者est_info的构建逻辑需要适配PA的场景
                    // 这里暂时假设PA优化了所有传入的相机，并且顺序与初始传入时一致
                    // 如果PA只优化部分相机，这里的逻辑需要调整
                    if (poses_ptr->est_info.origin2est_id.size() > original_view_id &&
                        poses_ptr->est_info.origin2est_id[original_view_id] == EstInfo::kInvalidViewId)
                    {
                        // 如果该原始ID在est_info中标记为无效，则跳过更新，因为它可能未参与优化
                        continue;
                    }
                }

                // 提取旋转和平移
                Matrix3d R_optimized;
                Vector3d t_optimized(camera[3], camera[4], camera[5]);
                ::ceres::AngleAxisToRotationMatrix(camera, R_optimized.data());

                // 更新原始位姿数据
                // 需要确保original_view_id是有效的，并且在poses_ptr的范围内
                if (original_view_id < poses_ptr->rotations.size() && original_view_id < poses_ptr->translations.size())
                {
                    poses_ptr->rotations[original_view_id] = R_optimized;
                    poses_ptr->translations[original_view_id] = t_optimized;
                    poses_ptr->est_info.SetViewState(original_view_id, EstInfo::ViewState::OPTIMIZED);
                }
                else
                {
                    std::cerr << "[MethodPA] 警告: original_view_id " << original_view_id
                              << " 超出范围，无法更新位姿。poses_ptr大小: "
                              << poses_ptr->rotations.size() << std::endl;
                }
            }

            // 清理旧的旋转和平移动态数组，如果它们是通过push_back填充的
            // 但由于上面是直接索引更新，如果poses_ptr一开始就通过resize设定了大小则无需此步
            // 如果初始为空然后push_back，则需要先clear再push_back优化后的，或如下面的resize调整
            // 考虑到est_info的存在，这里的处理需要非常小心，确保与est_info中的视图数量和ID映射一致
            // 如果PA优化后视图数量可能改变（例如剔除），则需要更复杂的处理
            // 目前假设PA不改变视图数量，仅优化传入的视图

            // 如果PA优化的是所有传入的相机，并且poses_ptr一开始就是匹配的大小：
            // 不需要额外操作，因为上面已经通过索引更新了

            // 如果PA可能只优化了est_info中标记为ESTIMATED的视图，并且希望输出只包含这些优化后的视图：
            // （这种逻辑更复杂，需要根据EstInfo重建rotations和translations，并更新EstInfo）
            // 当前的实现是直接修改传入的poses_ptr，所以返回它即可

            return required_package_["data_global_poses"];
        }
        catch (const std::exception &e)
        {
            std::cerr << "[MethodPA] Error: " << e.what() << std::endl;
            return nullptr;
        }
        catch (...)
        {
            std::cerr << "[MethodPA] Unknown error occurred" << std::endl;
            return nullptr;
        }
    }

    void MethodPA::SetSolverOptions(::ceres::Solver::Options &options)
    {
        // 从配置文件读取求解器选项
        options.linear_solver_type = ::ceres::SPARSE_SCHUR;
        options.minimizer_progress_to_stdout = GetOptionAsIndexT("verbose", 1) > 0;
        options.max_num_iterations = GetOptionAsIndexT("max_num_iterations", 100);
        options.function_tolerance = GetOptionAsFloat("function_tolerance", 1e-6);
        options.gradient_tolerance = GetOptionAsFloat("gradient_tolerance", 1e-10);
        options.parameter_tolerance = GetOptionAsFloat("parameter_tolerance", 1e-8);
        options.num_threads = GetOptionAsIndexT("num_threads", 8);
    }

    bool MethodPA::SetBaseView(const TrackInfo &track,
                               const GlobalPoses &initial_poses,
                               ViewId &left_view_id,
                               ViewId &right_view_id,
                               Vector2d &left_obs,
                               Vector2d &right_obs)
    {
        double max_s = 0.0;
        unsigned int max_id_l = 0;
        unsigned int max_id_r = 0;

        const size_t track_length = track.track.size();

        // 双重循环遍历所有观测对
        for (size_t j = 0; j < track_length - 1; ++j)
        {
            if (!track.track[j].is_used)
                continue;

            const ViewId &left_view = track.track[j].view_id;
            // 构建齐次坐标
            Vector3d x_l;
            x_l << track.track[j].coord[0], track.track[j].coord[1], 1.0;

            // 计算 R_l^T * x_l
            const Vector3d Rl_xl = initial_poses.rotations[left_view].transpose() * x_l;

            for (size_t k = j + 1; k < track_length; ++k)
            {
                if (!track.track[k].is_used)
                    continue;

                const ViewId &right_view = track.track[k].view_id;
                const Matrix3d &R_r = initial_poses.rotations[right_view];

                // 构建右视图的齐次坐标
                Vector3d x_r;
                x_r << track.track[k].coord[0], track.track[k].coord[1], 1.0;

                // 计算 R_r * R_l^T * x_l
                Vector3d Rx = R_r * Rl_xl;

                // 计算叉乘的范数
                Vector3d xRx = x_r.cross(Rx);
                double s = xRx.norm();

                // 更新最大值
                if (s > max_s)
                {
                    max_s = s;
                    // 如果right_view是0，则交换左右视图
                    if (right_view == 0)
                    {
                        max_id_l = k;
                        max_id_r = j;
                    }
                    else
                    {
                        max_id_l = j;
                        max_id_r = k;
                    }
                }
            }
        }

        // 设置输出结果
        if (max_s > 0)
        {
            left_view_id = track.track[max_id_l].view_id;
            right_view_id = track.track[max_id_r].view_id;
            left_obs << track.track[max_id_l].coord[0], track.track[max_id_l].coord[1];
            right_obs << track.track[max_id_r].coord[0], track.track[max_id_r].coord[1];
            return true;
        }

        // 如果没有找到合适的基准视图对，设置为无效值
        left_view_id = -1;
        right_view_id = -1;
        left_obs.setZero();
        right_obs.setZero();
        return false;
    }

    void MethodPA::BuildProblem(const Tracks &tracks,
                                const CameraModels &camera_models,
                                const GlobalPoses &initial_poses,
                                ::ceres::Problem &problem)
    {
        // 获取相机模型
        const CameraModel *camera_model = GetCameraModel(camera_models, 0);
        if (!camera_model)
        {
            throw std::runtime_error("[MethodPA] Failed to get camera model");
        }

        // 设置相机内参
        intrinsic_params_ = new double[3];
        intrinsic_params_[0] = camera_model->intrinsics.fx;

        // 检查畸变类型并获取径向畸变参数
        if (camera_model->intrinsics.distortion_type == types::DistortionType::NO_DISTORTION)
        {
            // 无畸变，设置为0
            intrinsic_params_[1] = 0.0; // k1
            intrinsic_params_[2] = 0.0; // k2
        }
        else if (camera_model->intrinsics.distortion_type == types::DistortionType::RADIAL_K1 &&
                 camera_model->intrinsics.radial_distortion.size() >= 1)
        {
            // 一阶径向畸变
            intrinsic_params_[1] = camera_model->intrinsics.radial_distortion[0]; // k1
            intrinsic_params_[2] = 0.0;                                           // k2
        }
        else if ((camera_model->intrinsics.distortion_type == types::DistortionType::RADIAL_K3 ||
                  camera_model->intrinsics.distortion_type == types::DistortionType::BROWN_CONRADY) &&
                 camera_model->intrinsics.radial_distortion.size() >= 2)
        {
            // 三阶径向畸变或Brown-Conrady畸变，取前两个参数
            intrinsic_params_[1] = camera_model->intrinsics.radial_distortion[0]; // k1
            intrinsic_params_[2] = camera_model->intrinsics.radial_distortion[1]; // k2
        }
        else
        {
            // 默认设置为0
            std::cerr << "[MethodPA] 警告: 未知畸变类型或参数不足，使用无畸变模型" << std::endl;
            intrinsic_params_[1] = 0.0; // k1
            intrinsic_params_[2] = 0.0; // k2
        }

        std::cout << "[MethodPA] 使用相机内参: fx=" << intrinsic_params_[0]
                  << ", k1=" << intrinsic_params_[1]
                  << ", k2=" << intrinsic_params_[2] << std::endl;

        // 设置初始位姿
        const Size num_cameras = initial_poses.Size();
        parameters_ = new double[num_cameras * 6];
        buffer_camera_ = new double[6];

        for (Size i = 0; i < num_cameras; ++i)
        {
            const Matrix3d &R = initial_poses.rotations[i];
            const Vector3d &t = initial_poses.translations[i];

            double *camera = parameters_ + i * 6;

            // 转换旋转矩阵为角轴表示
            ::ceres::RotationMatrixToAngleAxis(R.data(), camera);

            // 设置平移
            camera[3] = t[0];
            camera[4] = t[1];
            camera[5] = t[2];
        }

        // 为每个track确定左右基准视图
        left_base_views_.clear();
        right_base_views_.clear();
        left_base_obs_.clear();
        right_base_obs_.clear();

        size_t valid_tracks_count = 0;
        size_t invalid_tracks_count = 0;

        for (const auto &track : tracks)
        {
            if (!track.is_used)
            {
                invalid_tracks_count++;
                continue;
            }

            valid_tracks_count++;
            ViewId left_view_id, right_view_id;
            Vector2d left_obs, right_obs;

            SetBaseView(track, initial_poses, left_view_id, right_view_id, left_obs, right_obs);

            // 存储基准视图信息
            left_base_views_.push_back(left_view_id);
            right_base_views_.push_back(right_view_id);

            // 创建并存储基准观测
            double *left_obs_ptr = new double[2]{-left_obs[0], -left_obs[1]};
            double *right_obs_ptr = new double[2]{-right_obs[0], -right_obs[1]};
            left_base_obs_.push_back(left_obs_ptr);
            right_base_obs_.push_back(right_obs_ptr);
        }

        std::cout << "[MethodPA] 轨迹统计: 有效=" << valid_tracks_count
                  << ", 无效=" << invalid_tracks_count
                  << ", 总计=" << tracks.size() << std::endl;
        std::cout << "[MethodPA] 基准视图数组大小: " << left_base_views_.size() << std::endl;

        // 打印left_base_views_和right_base_views_
        // std::cout << "[left_base_views_, right_base_views_]: ";
        // for (Size i = 0; i < left_base_views_.size(); ++i)
        // {
        //     std::cout << "[" << left_base_views_[i] << ", " << right_base_views_[i] << "] ";
        // }
        // std::cout << std::endl;

        // 添加残差块
        ::ceres::LossFunction *loss_function = nullptr;
        std::string loss_type = GetOptionAsString("loss_function", "huber");

        if (loss_type == "huber")
        {
            double huber_param = GetOptionAsFloat("huber_parameter", 1.0);
            loss_function = new ::ceres::HuberLoss(huber_param);
            std::cout << "[MethodPA] 使用Huber损失函数，参数: " << huber_param << std::endl;
        }
        else if (loss_type == "cauchy")
        {
            double cauchy_param = GetOptionAsFloat("cauchy_parameter", 1.0);
            loss_function = new ::ceres::CauchyLoss(cauchy_param);
            std::cout << "[MethodPA] 使用Cauchy损失函数，参数: " << cauchy_param << std::endl;
        }
        else if (loss_type == "tukey")
        {
            double tukey_param = GetOptionAsFloat("tukey_parameter", 1.0);
            loss_function = new ::ceres::TukeyLoss(tukey_param);
            std::cout << "[MethodPA] 使用Tukey损失函数，参数: " << tukey_param << std::endl;
        }
        else if (loss_type == "softlone")
        {
            double softlone_param = GetOptionAsFloat("softlone_parameter", 1.0);
            loss_function = new ::ceres::SoftLOneLoss(softlone_param);
            std::cout << "[MethodPA] 使用SoftLOne损失函数，参数: " << softlone_param << std::endl;
        }
        else if (loss_type == "tolerant")
        {
            double tolerant_a = GetOptionAsFloat("tolerant_a", 1.0);
            double tolerant_b = GetOptionAsFloat("tolerant_b", 1.0);
            loss_function = new ::ceres::TolerantLoss(tolerant_a, tolerant_b);
            std::cout << "[MethodPA] 使用Tolerant损失函数，参数a: " << tolerant_a << ", b: " << tolerant_b << std::endl;
        }
        else if (loss_type == "none" || loss_type == "l2")
        {
            loss_function = nullptr; // 使用标准L2损失
            std::cout << "[MethodPA] 使用标准L2损失函数（无鲁棒性）" << std::endl;
        }
        else
        {
            std::cerr << "[MethodPA] 警告: 未知的损失函数类型 '" << loss_type
                      << "'，回退到Huber损失函数" << std::endl;
            loss_function = new ::ceres::HuberLoss(GetOptionAsFloat("huber_parameter", 1.0));
        }

        // 遍历所有tracks添加观测约束
        size_t valid_track_index = 0;     // 添加有效轨迹计数器
        size_t total_residual_blocks = 0; // 添加残差块计数器
        for (const auto &track_info : tracks)
        {
            if (!track_info.is_used)
                continue;

            // 检查 valid_track_index 是否在有效范围内
            if (valid_track_index >= left_base_views_.size() || valid_track_index >= right_base_views_.size())
            {
                std::cerr << "[MethodPA] 错误: valid_track_index " << valid_track_index
                          << " 超出基准视图数组范围 " << left_base_views_.size() << std::endl;
                break;
            }

            const int &left_base_view = left_base_views_[valid_track_index];
            const int &right_base_view = right_base_views_[valid_track_index];

            if (left_base_view < 0)
            {
                valid_track_index++; // 即使跳过，也要增加计数器
                continue;            // 跳过无效的基准视图
            }

            // 获取左右基准观测
            double *left_base_obs = left_base_obs_[valid_track_index];
            double *right_base_obs = right_base_obs_[valid_track_index];

            size_t track_residual_blocks = 0; // 当前轨迹的残差块数量
            for (const auto &obs : track_info.track)
            {
                if (!obs.is_used)
                    continue;

                const int camera_id = obs.view_id;

                // 检查当前视图
                double *current_camera = nullptr;
                bool is_right_current_views_equal = false;

                if (camera_id == left_base_view)
                {
                    continue; // 跳过左基准视图
                }
                else if (camera_id == right_base_view)
                {
                    is_right_current_views_equal = true;
                    current_camera = buffer_camera_; // 使用常量缓冲相机
                }
                else
                {
                    is_right_current_views_equal = false;
                    current_camera = parameters_ + camera_id * 6;
                }

                // 检查相机ID是否有效
                if (camera_id >= num_cameras)
                {
                    std::cerr << "[MethodPA] 警告: camera_id " << camera_id
                              << " 超出范围 " << num_cameras << std::endl;
                    continue;
                }

                ::ceres::CostFunction *cost_function =
                    ::ceres::examples::PAReprojectionError_calib::Create(
                        -obs.coord[0], -obs.coord[1],
                        intrinsic_params_,
                        left_base_view,
                        right_base_view,
                        left_base_obs,
                        right_base_obs,
                        is_right_current_views_equal,
                        static_cast<int>(valid_track_index)); // 使用轨迹索引而不是点ID

                // 获取左右基准相机参数
                double *left_base_camera = parameters_ + left_base_view * 6;
                double *right_base_camera = parameters_ + right_base_view * 6;

                problem.AddResidualBlock(cost_function,
                                         loss_function,
                                         left_base_camera,
                                         right_base_camera,
                                         current_camera);

                track_residual_blocks++;
                total_residual_blocks++;
            }

            valid_track_index++; // 处理完一个有效轨迹后增加计数器
        }

        std::cout << "[MethodPA] 添加了 " << total_residual_blocks << " 个残差块" << std::endl;

        // 添加并固定第一个相机参数块
        // parameters_指向所有相机参数的起始位置,每个相机有6个参数(3个旋转+3个平移)
        // 第一个相机作为参考系,需要固定其参数
        problem.AddParameterBlock(parameters_, 6);
        problem.SetParameterBlockConstant(parameters_);

        // 添加并固定缓冲相机参数块
        // buffer_camera_用于存储右基准视图相机参数的临时缓冲区
        // 当当前视图与右基准视图相同时使用该缓冲区,避免重复计算
        problem.AddParameterBlock(buffer_camera_, 6);
        problem.SetParameterBlockConstant(buffer_camera_);
    }

} // namespace PoSDK
