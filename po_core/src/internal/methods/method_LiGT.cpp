// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include <filesystem>

#include <cstdlib>
#include <memory>
#include <string>

#include "method_LiGT.hpp"
#include "factory/factory.hpp"
#include "LiGT/LiGT.hpp"
#include "po_core/file_io.hpp"

using namespace PoSDK;
using namespace PoSDK::types;

DataPtr MethodLiGT::Run()
{
    /// step.1 catch interface of input data
    TracksPtr tracks_ptr = GetDataPtr<Tracks>(required_package_["data_tracks"]);

    /// step.2 I/O data Ptr
    GlobalPosesPtr global_poses_ptr = GetDataPtr<GlobalPoses>(required_package_["data_global_poses"]);
    CameraModelsPtr camera_models_ptr = GetDataPtr<CameraModels>(required_package_["data_camera_models"]);

    /// step.3 检查轨迹坐标是否已经归一化，如果没有，则使用相机模型进行归一化
    if (!tracks_ptr->IsNormalized() && camera_models_ptr != nullptr)
    {
        std::cout << "轨迹坐标未归一化，使用相机模型进行归一化处理..." << std::endl;

        const CameraModels &camera_models = *camera_models_ptr;

        // 遍历每个Track
        for (auto &track_info : *tracks_ptr)
        {
            if (!track_info.is_used)
                continue;

            // 遍历Track中的每个观测
            for (auto &obs : track_info.track)
            {
                if (!obs.is_used)
                    continue;

                // 获取对应视图的相机模型
                const CameraModel *camera_model = GetCameraModel(camera_models, obs.view_id);

                // 将像素坐标转换为归一化坐标
                Vector2d normalized_coord = camera_model->PixelToNormalized(obs.coord);
                obs.coord = normalized_coord;
            }
        }

        // 标记轨迹已归一化
        tracks_ptr->SetNormalized(true);
        std::cout << "坐标归一化完成" << std::endl;
    }
    else if (!tracks_ptr->IsNormalized() && camera_models_ptr == nullptr)
    {
        std::cerr << "错误：轨迹坐标未归一化，但未提供相机模型，可能影响LiGT算法精度" << std::endl;
    }

    GlobalRotations &rotations = global_poses_ptr->rotations;
    GlobalTranslations &translations = global_poses_ptr->translations;

    global_poses_ptr->BuildEstInfoFromTracks(tracks_ptr);
    EstInfo &est_info = global_poses_ptr->GetEstInfo();

    const std::string &bal_file_path = GetOptionAsString("bal_file_path", "");

    LiGT_algorithm(*tracks_ptr,
                   est_info,
                   rotations,
                   translations);

    if (!bal_file_path.empty())
    {
        file::WriteBalFile(bal_file_path,
                           *tracks_ptr,
                           rotations,
                           translations);
    }

    // --- 设置LiGT输出的位姿格式 ---
    global_poses_ptr->SetPoseFormat(PoseFormat::RwTc);
    // --- 设置结束 ---

    return required_package_["data_global_poses"];
}
