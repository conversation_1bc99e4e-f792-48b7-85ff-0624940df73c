/**
 * @file analytical_reconstruction.hpp
 * @brief 解析式三角化重建方法
 * @details 基于全局位姿进行快速三角化重建3D点云
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _POMVG_ANanalytical_reconstruction_HPP_
#define _POMVG_ANanalytical_reconstruction_HPP_

#include "interfaces_preset_profiler.hpp"
#include "types.hpp"
#include "data/data_points_3d.hpp"
#include <memory>
#include <vector>
#include <string>
#include <fstream>
#include <set>

namespace PoSDK
{
    namespace methods
    {
        using namespace PoSDK::types;
        using namespace PoSDK::Interface;

        /**
         * @brief 解析式三角化重建方法
         * @details 基于已知的全局旋转和平移进行快速三角化重建3D点云
         */
        class AnalyticalReconstruction : public MethodPresetProfiler
        {
        public:
            /**
             * @brief 重建模式枚举
             */
            enum class ReconstructionMode
            {
                NORMAL,               ///< 普通重建模式
                REMOVE_OUTLIERS,      ///< 移除异常值模式
                ROBUST_RECONSTRUCTION ///< 鲁棒重建模式
            };

        public:
            AnalyticalReconstruction()
            {
                // 定义输入数据类型，参考method_LiGT
                required_package_.insert({"data_tracks", nullptr});
                required_package_.insert({"data_global_poses", nullptr});
                required_package_.insert({"data_camera_models", nullptr});
            }

            virtual ~AnalyticalReconstruction() = default;

            // MethodPreset interface
            const std::string &GetType() const override
            {
                static const std::string type = "analytical_reconstruction";
                return type;
            }

            DataPtr Run() override;

        protected:
            /**
             * @brief 解析式三角化重建单个3D点
             * @param tracks 跟踪点数据
             * @param global_rotations 全局旋转
             * @param global_translations 全局平移
             * @param world_point 输出的世界坐标点
             * @param track_id 跟踪点ID
             * @return 是否重建成功
             */
            bool ReconstructSinglePoint(
                const Tracks &tracks,
                const GlobalRotations &global_rotations,
                const GlobalTranslations &global_translations,
                Point3d &world_point,
                const IndexT track_id) const;

            /**
             * @brief 鲁棒解析式三角化重建单个3D点
             * @param tracks 跟踪点数据
             * @param global_rotations 全局旋转
             * @param global_translations 全局平移
             * @param world_point_info 输出的世界坐标点信息
             * @param track_id 跟踪点ID
             * @return 是否重建成功
             */
            bool ReconstructSinglePointRobust(
                const Tracks &tracks,
                const GlobalRotations &global_rotations,
                const GlobalTranslations &global_translations,
                WorldPointInfo &world_point_info,
                const IndexT track_id) const;

            /**
             * @brief 检查指定跟踪点是否可重建
             * @param tracks 轨迹数据
             * @param global_rotations 全局旋转矩阵
             * @param track_id 跟踪点ID
             * @return 是否可重建
             */
            bool CheckPointReconstructable(
                const Tracks &tracks,
                const GlobalRotations &global_rotations,
                const IndexT track_id) const;

            /**
             * @brief 移除无法重建的异常值跟踪点
             * @param tracks 轨迹数据
             * @param global_rotations 全局旋转矩阵
             * @param outlier_track_ids 输出异常值跟踪点ID列表
             * @return 是否成功移除异常值
             */
            bool RemoveOutliers(
                const Tracks &tracks,
                const GlobalRotations &global_rotations,
                std::vector<IndexT> &outlier_track_ids) const;

            /**
             * @brief 加载配置参数
             */
            void LoadParameters();

        private:
            // 配置参数
            double m3_ratio_;                        ///< 三角化阈值
            IndexT min_num_observations_per_point_;  ///< 最小观测数量
            ReconstructionMode reconstruction_mode_; ///< 重建模式
            std::string tracks_file_;                ///< 跟踪点文件路径
            std::string global_rotation_file_;       ///< 全局旋转文件路径
            std::string global_translation_file_;    ///< 全局平移文件路径
            std::string points_output_file_;         ///< 输出点云文件路径
            std::string outliers_output_file_;       ///< 异常值输出文件路径

            // 内部数据
            IndexT num_views_;        ///< 视图数量
            IndexT num_points_;       ///< 点数量
            IndexT num_observations_; ///< 观测数量
        };

        using AnalyticalReconstructionPtr = std::shared_ptr<AnalyticalReconstruction>;

    } // namespace methods
} // namespace PoSDK

#endif // _POMVG_ANanalytical_reconstruction_HPP_
