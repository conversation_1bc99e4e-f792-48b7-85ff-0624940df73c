/**
 * @file opengv_simulator.cpp
 * @brief OpenGV双视图仿真器实现
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#include "opengv_simulator.hpp"
#include "data/data_camera_models.hpp"
#include "data/data_features.hpp"
#include "data/data_matches.hpp"
#include "../fileIO/file_io.hpp"
#include <iostream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <filesystem>
#include <iomanip>
#include <opengv/relative_pose/CentralRelativeAdapter.hpp>
#include <opengv/relative_pose/methods.hpp>
#include "OpenGV/random_generators.hpp"
#include "OpenGV/experiment_helpers.hpp"

// ========================================================================
// 内部实现的OpenGV辅助函数，避免链接整个OpenGV库
// 注意：Cayley参数化函数现在统一使用types.hpp中的实现
// ========================================================================

namespace PoSDK
{

    OpenGVSimulator::OpenGVSimulator()
    {
        // 1. 加载通用配置
        InitializeDefaultConfigPath();

        // 2. 初始化真值位姿
        ground_truth_pose_.i = 0;
        ground_truth_pose_.j = 1;
        ground_truth_pose_.weight = 1.0;
    }

    DataPtr OpenGVSimulator::Run()
    {
        // 从配置中获取参数
        log_level_ = GetOptionAsIndexT("log_level", 1);

        const double max_parallax = GetOptionAsDouble("max_parallax", 1.0);
        const double max_rotation = GetOptionAsDouble("max_rotation", 0.5);
        const size_t num_points = GetOptionAsIndexT("num_points", 100);
        const double noise_level = GetOptionAsDouble("noise_level", 0.0);
        const double outlier_fraction = GetOptionAsDouble("outlier_fraction", 0.0);
        const double focal_length = GetOptionAsDouble("focal_length", 1000.0);
        const double image_width = GetOptionAsDouble("image_width", 640.0);
        const double image_height = GetOptionAsDouble("image_height", 480.0);
        const int random_seed = GetOptionAsIndexT("random_seed", 42);

        DisplayConfigInfo();

        // 初始化随机数生成器
        std::mt19937 rng(random_seed);

        // 生成双视图场景数据
        opengv::bearingVectors_t bearingVectors1, bearingVectors2;
        opengv::rotation_t gt_R;
        opengv::translation_t gt_t;

        GenerateTwoViewScene(max_parallax, max_rotation, num_points,
                             noise_level, outlier_fraction, rng,
                             bearingVectors1, bearingVectors2, gt_R, gt_t);

        // 保存真值位姿
        ground_truth_pose_.Rij = gt_R;
        ground_truth_pose_.tij = gt_t;

        // 计算内点索引（无外点时为所有点）
        inlier_indices_.clear();
        size_t num_inliers = static_cast<size_t>(num_points * (1.0 - outlier_fraction));
        for (size_t i = 0; i < num_inliers; ++i)
        {
            inlier_indices_.insert(i);
        }

        // 创建相机模型数据（缓存起来供多个方法使用）
        camera_model_data_ = CreateCameraModelData(focal_length, image_width, image_height);

        // 创建主数据包，包含所有方法的数据接口
        auto main_package = std::make_shared<DataPackage>();

        // 为不同方法添加对应的数据接口 - 使用新的字典式赋值方式
        (*main_package)["method_LiRP"] = CreateLiRPData(bearingVectors1, bearingVectors2);
        (*main_package)["method_povgSixPoint"] = CreateLiRPData(bearingVectors1, bearingVectors2); // 复用LiRP数据格式
        (*main_package)["opengv_model_estimator"] = CreateOpenGVModelEstimatorData(bearingVectors1, bearingVectors2, camera_model_data_);
        (*main_package)["opencv_two_view_estimator"] = CreateOpenCVTwoViewEstimatorData(bearingVectors1, bearingVectors2, camera_model_data_);
        (*main_package)["two_view_estimator"] = CreateTwoViewEstimatorData(bearingVectors1, bearingVectors2, camera_model_data_);

        // 添加通用数据（所有方法都可能需要）- 使用新的字典式赋值方式
        (*main_package)["data_camera_models"] = camera_model_data_;
        (*main_package)["ground_truth"] = CreateGroundTruthData(gt_R, gt_t);

        PO_LOG(PO_LOG_NORMAL) << "OpenGVSimulator Finished " << std::endl;

        return main_package;
    }

    void OpenGVSimulator::GenerateTwoViewScene(
        double max_parallax,
        double max_rotation,
        size_t num_points,
        double noise_level,
        double outlier_fraction,
        std::mt19937 &rng,
        opengv::bearingVectors_t &bearingVectors1,
        opengv::bearingVectors_t &bearingVectors2,
        opengv::rotation_t &gt_R,
        opengv::translation_t &gt_t)
    {
        // 初始化随机种子（使用experiment_helpers中的函数）
        opengv::initializeRandomSeed();

        // 生成相对位姿
        opengv::translation_t position1 = Eigen::Vector3d::Zero();
        opengv::rotation_t rotation1 = Eigen::Matrix3d::Identity();
        opengv::translation_t position2 = opengv::generateRandomTranslation(max_parallax);
        opengv::rotation_t rotation2 = opengv::generateRandomRotation(max_rotation);

        // 提取真值相对位姿
        opengv::extractRelativePose(position1, position2, rotation1, rotation2,
                                    gt_t, gt_R, true); // normalize translation

        // 使用OpenGV的辅助函数生成中心相机系统
        opengv::translations_t camOffsets;
        opengv::rotations_t camRotations;
        opengv::generateCentralCameraSystem(camOffsets, camRotations);

        // 生成2D-2D对应点
        std::vector<int> camCorrespondences1, camCorrespondences2;
        Eigen::MatrixXd gt_points(3, num_points);

        // 使用OpenGV的函数生成对应点
        opengv::generateRandom2D2DCorrespondences(
            position1, rotation1,
            position2, rotation2,
            camOffsets, camRotations,
            num_points,
            noise_level,
            outlier_fraction,
            bearingVectors1, bearingVectors2,
            camCorrespondences1, camCorrespondences2,
            gt_points);

        if (log_level_ > 1)
        {
            PO_LOG(PO_LOG_VERBOSE) << "生成的真值相对位姿：" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "旋转矩阵 R:\n"
                                   << gt_R << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "平移向量 t: " << gt_t.transpose() << std::endl;
            opengv::printExperimentCharacteristics(position2, rotation2, noise_level, outlier_fraction);
            opengv::printEssentialMatrix(gt_t, gt_R);
        }
    }

    DataPtr OpenGVSimulator::CreateLiRPData(
        const opengv::bearingVectors_t &bearingVectors1,
        const opengv::bearingVectors_t &bearingVectors2)
    {
        // 转换为BearingPairs格式
        BearingPairs bearing_pairs;
        ConvertToBearingPairs(bearingVectors1, bearingVectors2, bearing_pairs);

        // 创建DataSample
        return std::make_shared<DataSample<BearingPairs>>(bearing_pairs);
    }

    DataPtr OpenGVSimulator::CreateOpenGVModelEstimatorData(
        const opengv::bearingVectors_t &bearingVectors1,
        const opengv::bearingVectors_t &bearingVectors2,
        DataPtr camera_data)
    {
        auto package = std::make_shared<DataPackage>();

        // 使用新的字典式赋值方式添加数据
        (*package)["data_camera_models"] = camera_data;
        (*package)["data_features"] = CreateFeaturesData(bearingVectors1, bearingVectors2, camera_data);
        (*package)["data_sample"] = CreateSampleIdMatches(bearingVectors1.size());

        return package;
    }

    DataPtr OpenGVSimulator::CreateOpenCVTwoViewEstimatorData(
        const opengv::bearingVectors_t &bearingVectors1,
        const opengv::bearingVectors_t &bearingVectors2,
        DataPtr camera_data)
    {
        auto package = std::make_shared<DataPackage>();

        // 使用新的字典式赋值方式添加数据
        (*package)["data_features"] = CreateFeaturesData(bearingVectors1, bearingVectors2, camera_data);
        (*package)["data_matches"] = CreateDataMatches(bearingVectors1.size()); // 使用DataMatches格式
        (*package)["data_camera_models"] = camera_data;                         // 某些OpenCV方法可能需要

        return package;
    }

    DataPtr OpenGVSimulator::CreateTwoViewEstimatorData(
        const opengv::bearingVectors_t &bearingVectors1,
        const opengv::bearingVectors_t &bearingVectors2,
        DataPtr camera_data)
    {
        auto package = std::make_shared<DataPackage>();

        // 使用新的字典式赋值方式添加所有可能需要的数据
        (*package)["data_camera_models"] = camera_data;
        (*package)["data_features"] = CreateFeaturesData(bearingVectors1, bearingVectors2, camera_data);
        (*package)["data_matches"] = CreateDataMatches(bearingVectors1.size()); // 使用DataMatches格式

        // 添加视图对信息
        std::vector<ViewPair> view_pairs;
        view_pairs.push_back(ViewPair(0, 1));
        auto view_pairs_data = std::make_shared<DataMap<std::vector<ViewPair>>>(view_pairs);
        (*package)["data_view_pairs"] = view_pairs_data;

        return package;
    }

    DataPtr OpenGVSimulator::CreateCameraModelData(
        double focal_length,
        double image_width,
        double image_height)
    {
        auto camera_models_data = std::make_shared<DataCameraModels>();
        auto camera_models_ptr = GetDataPtr<CameraModels>(camera_models_data);

        // 创建相机内参
        CameraIntrinsics intrinsics;
        intrinsics.fx = focal_length;
        intrinsics.fy = focal_length;
        intrinsics.cx = image_width / 2.0;
        intrinsics.cy = image_height / 2.0;
        intrinsics.width = image_width;
        intrinsics.height = image_height;
        intrinsics.model_type = CameraModelType::PINHOLE;
        intrinsics.distortion_type = DistortionType::NO_DISTORTION;

        // 创建相机模型
        CameraModel camera_model;
        camera_model.intrinsics = intrinsics;
        camera_model.camera_make = "PoSDK";
        camera_model.camera_model = "OpenGVSimulator";
        camera_model.serial_number = "SIM001";

        // 添加到相机模型列表
        camera_models_ptr->clear();
        camera_models_ptr->push_back(camera_model);

        return camera_models_data;
    }

    DataPtr OpenGVSimulator::CreateFeaturesData(
        const opengv::bearingVectors_t &bearingVectors1,
        const opengv::bearingVectors_t &bearingVectors2,
        DataPtr camera_data)
    {
        auto features_data = std::make_shared<DataFeatures>();
        auto features_info_ptr = GetDataPtr<FeaturesInfo>(features_data);

        // 获取相机模型用于投影
        auto camera_models_ptr = GetDataPtr<CameraModels>(camera_data);
        if (!camera_models_ptr || camera_models_ptr->empty())
        {
            PO_LOG_ERR << "无法获取相机模型数据" << std::endl;
            return features_data;
        }

        const auto &camera_model = camera_models_ptr->front();

        // 初始化两个视图的特征信息
        features_info_ptr->resize(2);
        (*features_info_ptr)[0].image_path = "view_0";
        (*features_info_ptr)[1].image_path = "view_1";

        // 转换bearing vectors为图像坐标
        for (size_t i = 0; i < bearingVectors1.size(); ++i)
        {
            // 投影第一个视图的特征点
            Eigen::Vector2d img_point1 = ProjectToImagePlane(bearingVectors1[i], camera_model);
            Feature feat1;
            feat1.x() = img_point1.x();
            feat1.y() = img_point1.y();
            (*features_info_ptr)[0].features.push_back(FeaturePoint(feat1));

            // 投影第二个视图的特征点
            Eigen::Vector2d img_point2 = ProjectToImagePlane(bearingVectors2[i], camera_model);
            Feature feat2;
            feat2.x() = img_point2.x();
            feat2.y() = img_point2.y();
            (*features_info_ptr)[1].features.push_back(FeaturePoint(feat2));
        }

        return features_data;
    }

    DataPtr OpenGVSimulator::CreateSampleIdMatches(size_t num_matches)
    {
        // 创建DataSample<IdMatches>格式，用于opengv_model_estimator等方法
        IdMatches matches;
        matches.reserve(num_matches);

        // 创建简单的1对1匹配
        for (size_t i = 0; i < num_matches; ++i)
        {
            IdMatch match;
            match.i = i;             // 第一个视图的特征点索引
            match.j = i;             // 第二个视图的特征点索引
            match.is_inlier = false; // 初始化为false，由算法设置
            matches.push_back(match);
        }

        return std::make_shared<DataSample<IdMatches>>(matches);
    }

    DataPtr OpenGVSimulator::CreateDataMatches(size_t num_matches)
    {
        // 创建DataMatches对象，包含Matches类型的数据
        auto data_matches = std::make_shared<DataMatches>();
        auto matches_ptr = GetDataPtr<Matches>(data_matches);

        IdMatches id_matches;
        id_matches.reserve(num_matches);

        // 创建简单的1对1匹配
        for (size_t i = 0; i < num_matches; ++i)
        {
            IdMatch match;
            match.i = i;             // 第一个视图的特征点索引
            match.j = i;             // 第二个视图的特征点索引
            match.is_inlier = false; // 初始化为false，由算法设置
            id_matches.push_back(match);
        }

        // 添加视图对(0,1)的匹配
        ViewPair view_pair(0, 1);
        (*matches_ptr)[view_pair] = id_matches;

        return data_matches;
    }

    DataPtr OpenGVSimulator::CreateGroundTruthData(
        const opengv::rotation_t &gt_R,
        const opengv::translation_t &gt_t)
    {
        RelativePose gt_pose;
        gt_pose.i = 0;
        gt_pose.j = 1;
        gt_pose.Rij = gt_R;
        gt_pose.tij = gt_t;
        gt_pose.weight = 1.0;

        return std::make_shared<DataMap<RelativePose>>(gt_pose);
    }

    void OpenGVSimulator::ConvertToBearingPairs(
        const opengv::bearingVectors_t &bearingVectors1,
        const opengv::bearingVectors_t &bearingVectors2,
        BearingPairs &bearing_pairs)
    {
        bearing_pairs.clear();
        bearing_pairs.reserve(bearingVectors1.size());

        for (size_t i = 0; i < bearingVectors1.size(); ++i)
        {
            Eigen::Matrix<double, 6, 1> match_pair;
            match_pair.head<3>() = bearingVectors1[i];
            match_pair.tail<3>() = bearingVectors2[i];
            bearing_pairs.push_back(match_pair);
        }
    }

    Eigen::Vector2d OpenGVSimulator::ProjectToImagePlane(
        const opengv::bearingVector_t &bearing_vector,
        const CameraModel &camera_model)
    {
        // 获取相机内参
        const auto &intrinsics = camera_model.intrinsics;

        // 归一化坐标（齐次坐标转换为2D）
        Eigen::Vector2d norm_point(bearing_vector.x() / bearing_vector.z(),
                                   bearing_vector.y() / bearing_vector.z());

        // 投影到图像平面
        Eigen::Vector2d img_point;
        img_point.x() = intrinsics.fx * norm_point.x() + intrinsics.cx;
        img_point.y() = intrinsics.fy * norm_point.y() + intrinsics.cy;

        return img_point;
    }

} // namespace PoSDK