/**
 * @file method_global_outlier_removal.cpp
 * @brief 全局特征观测异常处理框架实现
 *
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#include "methods/method_global_outlier_removal.hpp"
#include "LiGT/LiGT.hpp"
#include <algorithm>
#include <numeric>
#include <cmath>
#include <iostream>
#include <fstream>
#include <iomanip>
#include <unordered_set>
#include <vector>
#include <limits>
#include <boost/algorithm/string.hpp> // 用于不区分大小写的字符串比较

// 确保M_PI在所有平台上都可用
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace PoSDK
{

    DataPtr MethodGlobalOutlierRemoval::Run()
    {
        try
        {
            if (log_level_ >= PO_LOG_NORMAL)
            {
                PO_LOG(PO_LOG_NORMAL) << "Run called. Displaying config if log_level is appropriate." << std::endl;
                DisplayConfigInfo();
            }

            // 1. 获取输入数据
            auto tracks_ptr = GetDataPtr<Tracks>(required_package_["data_tracks"]);
            auto global_poses_ptr = GetDataPtr<GlobalPoses>(required_package_["data_global_poses"]);
            if (!tracks_ptr || !global_poses_ptr)
            {
                std::cerr << "错误：无法获取轨迹数据或全局位姿数据" << std::endl;
                return nullptr;
            }

            // 2. 获取原始轨迹和位姿
            Tracks &original_tracks = *tracks_ptr;
            GlobalPoses &original_poses = *global_poses_ptr;

            // 3. 计算残差
            std::vector<ResidualInfo> residual_info_list; // 使用新的结构体
            bool residual_computed = false;
            const std::string residual_method = GetOptionAsString("residual_method", "LiGT");
            if (boost::iequals(residual_method, "LiGT"))
            {
                residual_computed = ComputeLiGTResiduals(original_tracks, original_poses, residual_info_list);
            }
            else if (boost::iequals(residual_method, "MedLiGT"))
            { // Add MedLiGT case
                residual_computed = ComputeMedLiGTResiduals(original_tracks, original_poses, residual_info_list);
            }
            else
            {
                std::cerr << "不支持的残差计算方法: " << residual_method << std::endl;
                return nullptr;
            }
            if (!residual_computed || residual_info_list.empty())
            {
                std::cerr << "残差计算失败" << std::endl;
                return nullptr;
            }

            // 4. 检测异常值
            std::vector<IndexT> outlier_obs_ids; // 存储异常观测的 obs_id
            const std::string detection_method = GetOptionAsString("detection_method", "sigma");
            if (boost::iequals(detection_method, "sigma"))
            {
                const double threshold_multiplier = std::stod(GetOptionAsString("threshold_multiplier", "3.0"));
                DetectOutliersBySigmaRule(residual_info_list, threshold_multiplier, outlier_obs_ids);
            }
            else if (boost::iequals(detection_method, "mad"))
            {
                const double threshold_multiplier = std::stod(GetOptionAsString("threshold_multiplier", "3.0"));
                DetectOutliersByMADRule(residual_info_list, threshold_multiplier, outlier_obs_ids);
            }
            else if (boost::iequals(detection_method, "chi2"))
            {                                                                                                          // 新增 chi2 方法处理
                const double standard_chi2_quantile = std::stod(GetOptionAsString("standard_chi2_quantile", "5.991")); // 默认 p=0.95, df=2
                DetectOutliersByChiSquaredRule(residual_info_list, standard_chi2_quantile, outlier_obs_ids);
            }
            else if (boost::iequals(detection_method, "threshold"))
            {
                DetectOutliersByDirectThreshold(residual_info_list, outlier_obs_ids);
            }
            else
            {
                std::cerr << "不支持的异常值检测方法: " << detection_method << std::endl;
                return nullptr;
            }

            // 5. 保存残差和异常标记 (现在使用 obs_id)
            const std::string residuals_output_path = GetOptionAsString("output_residuals_path", "");
            if (!residuals_output_path.empty())
            {
                std::ofstream residuals_file(residuals_output_path);
                if (residuals_file.is_open())
                {
                    residuals_file << "ObsID,Residual,IsOutlierDetected\n"; // 更新表头
                    std::unordered_set<IndexT> outlier_set(outlier_obs_ids.begin(), outlier_obs_ids.end());
                    for (const auto &info : residual_info_list)
                    {
                        int is_outlier = outlier_set.count(info.obs_id) ? 1 : 0;
                        residuals_file << info.obs_id << "," // 输出 obs_id
                                       << std::fixed << std::setprecision(12) << info.residual_value << ","
                                       << is_outlier << "\n";
                    }
                    residuals_file.close();
                    std::cout << "残差数据已保存到: " << residuals_output_path << std::endl;
                }
                else
                {
                    std::cerr << "警告：无法打开文件以写入残差数据: " << residuals_output_path << std::endl;
                }
            }

            // 6. 处理异常值
            const bool remove_outliers = GetOptionAsBool("remove_outliers", false);
            auto cleaned_tracks_data_ptr = std::make_shared<DataTracks>();
            Tracks &cleaned_tracks = *static_cast<Tracks *>(cleaned_tracks_data_ptr->GetData());
            ProcessOutliers(original_tracks, outlier_obs_ids, cleaned_tracks, remove_outliers); // 传入 outlier_obs_ids

            // 7. 打印统计信息
            const int log_level = std::stoi(GetOptionAsString("log_level", "1"));
            if (log_level > 0)
            {
                std::cout << "全局异常值移除统计信息:" << std::endl;
                std::cout << "  - 总计算残差数: " << residual_info_list.size() << std::endl; // 修改统计标签
                std::cout << "  - 检测到的异常值数: " << outlier_obs_ids.size() << std::endl;
                std::cout << "  - 异常值比例: "
                          << (residual_info_list.empty() ? 0.0 : static_cast<double>(outlier_obs_ids.size()) / residual_info_list.size() * 100.0) << "%" << std::endl;
            }

            return cleaned_tracks_data_ptr;
        }
        catch (const std::exception &e)
        {
            std::cerr << "MethodGlobalOutlierRemoval::Run() 执行失败: " << e.what() << std::endl;
            return nullptr;
        }
    }

    bool MethodGlobalOutlierRemoval::ComputeLiGTResiduals(
        const Tracks &tracks,
        const GlobalPoses &global_poses,
        std::vector<ResidualInfo> &residual_info_list)
    { // 修改返回值类型

        try
        {
            // 清空旧数据并预估大小
            residual_info_list.clear();
            size_t total_valid_obs_approx = 0;
            for (const auto &track_info : tracks)
            {
                if (track_info.is_used)
                    total_valid_obs_approx += track_info.GetValidObservationCount();
            }
            residual_info_list.reserve(total_valid_obs_approx);

            // 1. 获取EstInfo, 全局旋转和全局平移
            const EstInfo &est_info = global_poses.GetEstInfo();
            const GlobalRotations &global_rotations = global_poses.rotations;
            const GlobalTranslations &global_translations = global_poses.translations; // 获取平移数据

            const size_t num_views = est_info.GetNumEstimatedViews();
            if (num_views < 2)
            {
                std::cerr << "视图数量不足，无法计算残差" << std::endl;
                return false;
            }

            // 检查输入位姿数量是否足够
            if (global_rotations.size() != est_info.GetNumEstimatedViews())
            {
                std::cerr << "错误：输入的全局旋转矩阵数量与EstInfo不匹配" << std::endl;
                std::cerr << "global_rotations.size(): " << global_rotations.size()
                          << " est_info.GetNumEstimatedViews(): " << est_info.GetNumEstimatedViews() << std::endl;
                return false;
            }

            double max_residual_sq_norm = 0.0; // 用于后续归一化（可选）
            unsigned int max_id_l = 0;
            unsigned int max_id_r = 0;
            double max_s = 0;
            Vector3d tmp_residual_l, tmp_residual_r, tmp_residual_c, tmp_residual;

            for (size_t track_id = 0; track_id < tracks.size(); ++track_id)
            {
                const auto &track_info = tracks[track_id];
                if (!track_info.is_used)
                    continue;

                const Track &track = track_info.track;
                const size_t track_length = track.size();
                if (track_length < 2)
                    continue; // 至少需要两个观测

                // 为当前轨迹寻找最佳的基准视图对
                max_s = 0;
                max_id_l = 0;
                max_id_r = 0;

                // 调用LiGT库的build_base_views函数
                build_base_views(track, global_rotations, max_s, max_id_l, max_id_r);

                // 如果找不到有效的基准视图对，跳过当前轨迹
                if (max_s <= 1e-10)
                    continue;

                // 确保基准视图是有效的
                if (!track[max_id_l].is_used || !track[max_id_r].is_used)
                {
                    continue;
                }

                // 获取基准视图信息 (使用原始ID)
                const ViewId lbase_view = track[max_id_l].view_id;
                const ViewId rbase_view = track[max_id_r].view_id;
                const Matrix3d &R_l_base = global_poses.GetRotation(lbase_view);
                const Matrix3d &R_r_base = global_poses.GetRotation(rbase_view);
                const Vector3d &t_l_base = global_poses.GetTranslation(lbase_view); // 获取基准平移
                const Vector3d &t_r_base = global_poses.GetTranslation(rbase_view); // 获取基准平移
                const Vector3d &x_l = track[max_id_l].GetHomoCoord();
                const Vector3d &x_r = track[max_id_r].GetHomoCoord();

                // 计算相对旋转
                Matrix3d R_lr = R_r_base * R_l_base.transpose();

                // 计算中间变量
                Matrix3d cross_xr;
                cross_xr << 0, -x_r(2), x_r(1),
                    x_r(2), 0, -x_r(0),
                    -x_r(1), x_r(0), 0;

                Vector3d R_lr_x_l = R_lr * x_l;
                Vector3d m3_lr = cross_xr * R_lr_x_l;

                // 遍历轨迹中的每个有效观测，计算残差
                for (size_t j = 0; j < track_length; ++j)
                {
                    const auto &obs = track[j]; // 获取当前观测
                    if (!obs.is_used || obs.view_id == lbase_view)
                        continue;

                    const ViewId current_view = obs.view_id;
                    const Matrix3d &R_c = global_poses.GetRotation(current_view);
                    const Vector3d &t_c = global_poses.GetTranslation(current_view); // 获取当前视图平移
                    const Matrix3d R_lc = R_c * R_l_base.transpose();                // 注意这里R_lc是小写
                    const Vector3d &x_c = obs.GetHomoCoord();

                    // 计算残差（重投影误差的近似）
                    Vector3d Rlc_xl = R_lc * x_l;
                    Vector3d m3_lc = x_c.cross(Rlc_xl);

                    // 创建叉乘矩阵
                    Matrix3d cross_xc;
                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    Vector3d R_lc_x_l = R_lc * x_l;
                    Vector3d xc_R_lc_x_l = cross_xc * R_lc_x_l;

                    // 计算残差项的矩阵部分
                    RowVector3d tmp_A = m3_lr.transpose() * cross_xr;
                    tmp_A = -tmp_A;
                    Matrix3d tmp_F_rbase = xc_R_lc_x_l * tmp_A;
                    Matrix3d tmp_F_cur = cross_xc * max_s * max_s;
                    Matrix3d tmp_F_lbase = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                    // 计算残差向量 (根据参考代码)
                    tmp_residual_l.noalias() = tmp_F_lbase * t_l_base;
                    tmp_residual_r.noalias() = tmp_F_rbase * t_r_base;
                    tmp_residual_c.noalias() = tmp_F_cur * t_c;
                    tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                    // 计算残差值（标量，使用平方范数）
                    double residual_sq_norm = tmp_residual.squaredNorm();

                    // 添加残差信息到列表
                    residual_info_list.push_back({obs.obs_id, residual_sq_norm}); // 存储 obs_id 和残差值
                }
            }

            return !residual_info_list.empty();
        }
        catch (const std::exception &e)
        {
            std::cerr << "计算LiGT残差失败: " << e.what() << std::endl;
            return false;
        }
    }

    void MethodGlobalOutlierRemoval::DetectOutliersBySigmaRule(
        const std::vector<ResidualInfo> &residual_info_list, // 修改输入
        double sigma_multiplier,
        std::vector<IndexT> &outlier_obs_ids)
    { // 修改输出

        if (residual_info_list.empty())
            return;

        // 提取残差值用于计算
        std::vector<double> residuals;
        residuals.reserve(residual_info_list.size());
        for (const auto &info : residual_info_list)
        {
            residuals.push_back(info.residual_value);
        }

        // 计算均值
        double sum = std::accumulate(residuals.begin(), residuals.end(), 0.0);
        double mean = sum / residuals.size();

        // 计算方差
        double sq_sum = 0.0;
        for (const double &r : residuals)
        {
            sq_sum += (r - mean) * (r - mean);
        }
        double variance = residuals.size() > 1 ? sq_sum / (residuals.size() - 1) : 0.0; // 使用 n-1 作为分母（无偏估计）

        // 计算标准差，并确保其不小于最低阈值
        double std_dev = std::sqrt(variance);
        const double min_std_dev = 1e-12;
        std_dev = std::max(std_dev, min_std_dev);

        // 设置阈值
        double threshold = std_dev * sigma_multiplier;

        // 检测异常值，记录 obs_id
        outlier_obs_ids.clear();
        for (const auto &info : residual_info_list)
        {
            if (std::abs(info.residual_value - mean) > threshold)
            {
                outlier_obs_ids.push_back(info.obs_id); // 记录 obs_id
            }
        }
    }

    void MethodGlobalOutlierRemoval::DetectOutliersByMADRule(
        const std::vector<ResidualInfo> &residual_info_list, // 修改输入
        double mad_multiplier,
        std::vector<IndexT> &outlier_obs_ids)
    { // 修改输出

        if (residual_info_list.empty())
            return;

        // 对于残差分析，理论上median应该接近0，所以直接使用MAD=median|residual_i|
        // 参考：https://blog.csdn.net/qq_19446965/article/details/112419085
        std::vector<double> abs_residuals;
        abs_residuals.reserve(residual_info_list.size());
        for (const auto &info : residual_info_list)
        {
            abs_residuals.push_back(std::abs(info.residual_value));
        }

        // 计算绝对残差的中位数作为MAD
        std::sort(abs_residuals.begin(), abs_residuals.end());
        double mad;
        size_t n = abs_residuals.size();
        if (n % 2 == 0)
        {
            mad = (abs_residuals[n / 2 - 1] + abs_residuals[n / 2]) / 2.0;
        }
        else
        {
            mad = abs_residuals[n / 2];
        }

        // 考虑到正态分布，MAD需要乘以常数1.4826
        // 并且确保归一化后的MAD不小于最低阈值
        double normalized_mad = 1.4826 * mad;
        const double min_normalized_mad = 1e-12;
        normalized_mad = std::max(normalized_mad, min_normalized_mad);

        // 设置阈值
        double threshold = normalized_mad * mad_multiplier;

        // 检测异常值，记录 obs_id
        outlier_obs_ids.clear();
        for (const auto &info : residual_info_list)
        {
            // 对于残差，我们直接与阈值比较（因为残差都是非负的）
            if (info.residual_value > threshold)
            {
                outlier_obs_ids.push_back(info.obs_id); // 记录 obs_id
            }
        }
    }

    // Helper function to calculate median of a mutable vector
    double CalculateMedian(std::vector<double> &data)
    { // Takes mutable vector
        if (data.empty())
        {
            return 0.0;
        }
        size_t n = data.size();
        size_t mid = n / 2;
        // Use nth_element for efficiency, partially sorts the vector
        std::nth_element(data.begin(), data.begin() + mid, data.end());
        double median = data[mid];
        if (n % 2 == 0)
        {
            // Need the element before the middle as well for even size
            std::nth_element(data.begin(), data.begin() + mid - 1, data.end());
            median = (median + data[mid - 1]) / 2.0;
        }
        return median;
    }

    void MethodGlobalOutlierRemoval::DetectOutliersByChiSquaredRule(
        const std::vector<ResidualInfo> &residual_info_list,
        double standard_chi2_quantile, // 2自由度卡方分布的分位数 (例如, p=0.95时约为5.991)
        std::vector<IndexT> &outlier_obs_ids)
    {

        if (residual_info_list.empty())
        {
            std::cerr << "警告：残差列表为空，无法执行卡方检测。" << std::endl;
            return;
        }
        if (standard_chi2_quantile <= 0)
        {
            std::cerr << "警告：标准卡方分位数阈值无效 (" << standard_chi2_quantile << ")。" << std::endl;
            return;
        }

        // 提取残差平方范数
        std::vector<double> residual_sq_norms;
        residual_sq_norms.reserve(residual_info_list.size());
        for (const auto &info : residual_info_list)
        {
            if (info.residual_value >= 0)
            {
                residual_sq_norms.push_back(info.residual_value);
            }
            else
            {
                std::cerr << "警告：跳过负的残差平方范数 " << info.residual_value << " for ObsID " << info.obs_id << std::endl;
            }
        }

        if (residual_sq_norms.empty())
        {
            std::cerr << "警告：没有有效的残差用于卡方检测。" << std::endl;
            outlier_obs_ids.clear();
            return;
        }

        // 对于2自由度的卡方分布，理论上残差平方范数应该符合该分布
        // 但实际数据可能需要标准化，使用MAD进行鲁棒的尺度估计
        std::vector<double> abs_residual_sq_norms;
        abs_residual_sq_norms.reserve(residual_sq_norms.size());
        for (double res_sq : residual_sq_norms)
        {
            abs_residual_sq_norms.push_back(std::abs(res_sq));
        }

        // 计算残差平方范数的MAD作为尺度估计
        std::sort(abs_residual_sq_norms.begin(), abs_residual_sq_norms.end());
        double mad_residual_sq;
        size_t n = abs_residual_sq_norms.size();
        if (n % 2 == 0)
        {
            mad_residual_sq = (abs_residual_sq_norms[n / 2 - 1] + abs_residual_sq_norms[n / 2]) / 2.0;
        }
        else
        {
            mad_residual_sq = abs_residual_sq_norms[n / 2];
        }

        // 对于2自由度卡方分布，方差为2*σ^2，如果我们假设σ^2=1（标准化），那么方差为2
        // 使用MAD估计尺度，然后计算自适应阈值
        const double min_mad_scale = 1e-6;
        double robust_scale = std::max(mad_residual_sq, min_mad_scale);

        // 对于2自由度卡方分布，MAD约为0.954（当σ=1时）
        // 我们需要将MAD转换为标准卡方分布的尺度
        double normalized_scale = robust_scale / 0.954; // 近似转换

        // 计算自适应阈值
        double adaptive_threshold = normalized_scale * standard_chi2_quantile;

        // 检测异常值
        outlier_obs_ids.clear();
        outlier_obs_ids.reserve(residual_info_list.size() / 10);

        for (const auto &info : residual_info_list)
        {
            if (info.residual_value > adaptive_threshold)
            {
                outlier_obs_ids.push_back(info.obs_id);
            }
        }

        // 输出统计信息
        if (!outlier_obs_ids.empty())
        {
            std::cout << "使用2自由度卡方检测 (MAD尺度估计=" << robust_scale
                      << ", 标准化尺度=" << normalized_scale
                      << ", 自适应阈值=" << adaptive_threshold
                      << ", 基于标准分位数=" << standard_chi2_quantile
                      << ") 检测到 " << outlier_obs_ids.size() << " 个异常点。" << std::endl;
        }
        else
        {
            std::cout << "使用2自由度卡方检测未检测到异常点 (自适应阈值=" << adaptive_threshold << ")" << std::endl;
        }
    }

    void MethodGlobalOutlierRemoval::ProcessOutliers(
        const Tracks &tracks,
        const std::vector<IndexT> &outlier_obs_ids, // 修改输入
        Tracks &cleaned_tracks,
        bool remove_outliers)
    {

        cleaned_tracks = tracks;
        if (outlier_obs_ids.empty())
            return;

        // 使用集合进行快速查找
        std::unordered_set<IndexT> outlier_set(outlier_obs_ids.begin(), outlier_obs_ids.end());
        size_t marked_outliers = 0;

        for (auto &track_info : cleaned_tracks)
        { // 直接迭代修改 cleaned_tracks
            if (!track_info.is_used)
                continue;

            bool track_modified = false;
            for (size_t obs_idx = 0; obs_idx < track_info.track.size(); ++obs_idx)
            {                                          // 使用索引访问
                auto &obs = track_info.track[obs_idx]; // 获取可修改的引用
                if (!obs.is_used)
                    continue;

                if (outlier_set.count(obs.obs_id))
                {                        // 直接检查 obs_id
                    obs.is_used = false; // 标记为未使用
                    marked_outliers++;
                    track_modified = true;
                }
            }

            // 检查轨迹是否还有至少两个有效观测
            if (track_modified && track_info.GetValidObservationCount() < 2)
            {
                track_info.SetUsed(false);
            }
        }

        // 输出处理结果
        const int log_level = std::stoi(GetOptionAsString("log_level", "1"));
        if (log_level > 1)
        {
            std::cout << "  - 成功标记异常值数量: " << marked_outliers << std::endl;
            std::cout << "  - 有效轨迹数量: " << std::count_if(cleaned_tracks.begin(), cleaned_tracks.end(), [](const TrackInfo &t)
                                                               { return t.is_used; })
                      << std::endl;
        }
    }

    // Implementation for MedLiGT residual calculation
    bool MethodGlobalOutlierRemoval::ComputeMedLiGTResiduals(
        Tracks &tracks, // 改为可修改的引用，以便直接更新tracks状态
        const GlobalPoses &global_poses,
        std::vector<ResidualInfo> &residual_info_list)
    {

        try
        {
            residual_info_list.clear();

            const EstInfo &est_info = global_poses.GetEstInfo();
            if (est_info.GetNumEstimatedViews() < 2)
            {
                std::cerr << "视图数量不足，无法计算MedLiGT残差" << std::endl;
                return false;
            }

            // 检查是否需要详细的debug统计（只有在最详细日志级别时才计算）
            const bool enable_detailed_debug = (log_level_ >= PO_LOG_VERBOSE);

#ifdef _DEBUG
            // Debug模式下的统计数据 - 只有在详细日志级别时才收集
            std::vector<double> all_individual_residuals;
            std::vector<double> all_median_residuals_per_baseline_pair;
            std::vector<double> all_final_residuals_per_obs;
            size_t total_baseline_pairs = 0;
            size_t total_valid_tracks = 0;
            size_t debug_output_count = 0;      // 限制debug输出数量
            const size_t max_debug_outputs = 2; // 只输出前2个观测的详细信息
            size_t invalid_obs_count = 0;       // 统计无效观测数量

            if (enable_detailed_debug)
            {
                // 只有在详细级别时才预分配内存进行统计收集
                all_median_residuals_per_baseline_pair.reserve(1000);
                all_final_residuals_per_obs.reserve(500);
            }
#endif

            for (auto &track_info : tracks) // 改为可修改的引用
            {
                if (!track_info.is_used || track_info.GetValidObservationCount() < 2)
                {
                    continue; // 跳过无效或观测数不足的轨迹
                }

                Track &track = track_info.track; // 改为可修改的引用
                const size_t track_length = track.size();

#ifdef _DEBUG
                if (enable_detailed_debug)
                {
                    total_valid_tracks++;
                    if (total_valid_tracks <= 2)
                    { // 只显示前2个轨迹的信息
                        std::cout << "Debug: 处理轨迹 " << total_valid_tracks << ", 有效观测数: " << track_info.GetValidObservationCount() << std::endl;
                    }
                }
#endif

                // 对于每个观测k作为left_base
                for (size_t k = 0; k < track_length; ++k)
                {
                    auto &left_obs = track[k]; // 改为可修改的引用
                    if (!left_obs.is_used)
                        continue;

                    std::vector<double> right_obs_residuals; // 存储所有p产生的MedResidualLiGT结果

                    // 对于每个观测p作为right_base
                    for (size_t p = 0; p < track_length; ++p)
                    {
                        if (k == p || !track[p].is_used)
                            continue;

                        // 计算MedResidualLiGT(k, p, global_poses, track_info)
                        double med_residual = ComputeMedResidualLiGT(k, p, track, global_poses);

                        if (med_residual >= 0) // 有效残差
                        {
                            right_obs_residuals.push_back(med_residual);
#ifdef _DEBUG
                            if (enable_detailed_debug)
                            {
                                total_baseline_pairs++;
                                all_median_residuals_per_baseline_pair.push_back(med_residual);
                            }
#endif
                        }
                    }

                    // 从right_obs_residuals中取median作为观测k的最终残差
                    if (!right_obs_residuals.empty())
                    {
                        std::vector<double> residuals_copy = right_obs_residuals; // 复制用于median计算
                        double final_residual = CalculateMedian(residuals_copy);
                        residual_info_list.push_back({left_obs.obs_id, final_residual});

#ifdef _DEBUG
                        if (enable_detailed_debug)
                        {
                            all_final_residuals_per_obs.push_back(final_residual);
                            if (debug_output_count < max_debug_outputs)
                            {
                                std::cout << "Debug: ObsID " << left_obs.obs_id << " 最终残差: " << std::fixed << std::setprecision(12) << final_residual
                                          << " (来自 " << right_obs_residuals.size() << " 个baseline pairs)" << std::endl;
                                debug_output_count++;
                            }
                        }
#endif
                    }
                    else
                    {
                        // 无效残差：直接将观测标记为不可用
                        left_obs.is_used = false;
#ifdef _DEBUG
                        if (enable_detailed_debug)
                        {
                            invalid_obs_count++;
                            if (debug_output_count < max_debug_outputs)
                            {
                                std::cout << "Debug: ObsID " << left_obs.obs_id << " 标记为无效（无法计算有效残差）" << std::endl;
                            }
                        }
#endif
                    }
                }

                // 检查轨迹是否还有足够的有效观测
                if (track_info.GetValidObservationCount() < 2)
                {
                    track_info.SetUsed(false);
#ifdef _DEBUG
                    if (enable_detailed_debug && total_valid_tracks <= 2)
                    {
                        std::cout << "Debug: 轨迹 " << total_valid_tracks << " 因观测数不足被标记为无效" << std::endl;
                    }
#endif
                }
            }

#ifdef _DEBUG
            // 只有在详细日志级别时才输出debug统计数据到CSV文件
            if (enable_detailed_debug)
            {
                std::cout << "Debug: 总共标记了 " << invalid_obs_count << " 个无效观测" << std::endl;
                OutputDebugStatistics(all_individual_residuals, all_median_residuals_per_baseline_pair,
                                      all_final_residuals_per_obs, total_baseline_pairs, total_valid_tracks);
            }
            else if (log_level_ >= PO_LOG_NORMAL)
            {
                // 在普通日志级别时只输出基本统计信息
                std::cout << "MedLiGT残差计算完成，共处理 " << residual_info_list.size() << " 个观测残差" << std::endl;
            }
#endif

            return !residual_info_list.empty();
        }
        catch (const std::exception &e)
        {
            std::cerr << "计算MedLiGT残差失败: " << e.what() << std::endl;
            return false;
        }
    }

    // 新增私有辅助函数：计算单个baseline pair的MedLiGT残差
    double MethodGlobalOutlierRemoval::ComputeMedResidualLiGT(
        size_t left_idx,
        size_t right_idx,
        const Track &track,
        const GlobalPoses &global_poses)
    {
        try
        {
            if (left_idx >= track.size() || right_idx >= track.size() || left_idx == right_idx)
            {
                return -1.0; // 返回无效值
            }

            const auto &left_obs = track[left_idx];
            const auto &right_obs = track[right_idx];

            if (!left_obs.is_used || !right_obs.is_used)
            {
                return -1.0; // 返回无效值
            }

            // 获取baseline视图信息
            const ViewId lbase_view = left_obs.view_id;
            const ViewId rbase_view = right_obs.view_id;
            const Vector3d &x_l = left_obs.GetHomoCoord();
            const Vector3d &x_r = right_obs.GetHomoCoord();
            const Matrix3d &R_l_base = global_poses.GetRotation(lbase_view);
            const Matrix3d &R_r_base = global_poses.GetRotation(rbase_view);
            const Vector3d &t_l_base = global_poses.GetTranslation(lbase_view);
            const Vector3d &t_r_base = global_poses.GetTranslation(rbase_view);

            // 计算baseline的几何强度
            Matrix3d R_lr = R_r_base * R_l_base.transpose();
            Vector3d theta_lr = x_r.cross(R_lr * x_l);
            double base_s = theta_lr.norm();

            if (base_s <= 1e-10) // baseline几何强度太弱
            {
                return -1.0; // 返回无效值
            }

            // 预计算baseline相关的矩阵
            Matrix3d cross_xr;
            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

            Vector3d R_lr_x_l = R_lr * x_l;
            Vector3d m3_lr = cross_xr * R_lr_x_l;
            RowVector3d tmp_A = -m3_lr.transpose() * cross_xr;

            // 遍历所有其他观测作为current，计算残差
            std::vector<double> current_obs_residuals;
            current_obs_residuals.reserve(track.size());

            for (size_t current_idx = 0; current_idx < track.size(); ++current_idx)
            {
                if (current_idx == left_idx || !track[current_idx].is_used)
                    continue; // 跳过left_base和无效观测

                const auto &current_obs = track[current_idx];
                const ViewId current_view = current_obs.view_id;
                const Matrix3d &R_c = global_poses.GetRotation(current_view);
                const Vector3d &t_c = global_poses.GetTranslation(current_view);
                const Vector3d &x_c = current_obs.GetHomoCoord();

                // 计算相对旋转
                Matrix3d R_lc = R_c * R_l_base.transpose();
                Vector3d R_lc_x_l = R_lc * x_l;

                // 计算叉乘矩阵
                Matrix3d cross_xc;
                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                Vector3d xc_R_lc_x_l = cross_xc * R_lc_x_l;

                // 计算残差项的F矩阵
                Matrix3d tmp_F_rbase = xc_R_lc_x_l * tmp_A;
                Matrix3d tmp_F_cur = cross_xc * (base_s * base_s);
                Matrix3d tmp_F_lbase = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                // 计算残差向量
                Vector3d tmp_residual_l = tmp_F_lbase * t_l_base;
                Vector3d tmp_residual_r = tmp_F_rbase * t_r_base;
                Vector3d tmp_residual_c = tmp_F_cur * t_c;
                Vector3d tmp_residual = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                // 根据angle_error_mode选项计算残差值
                double residual_value;
                bool use_angle_error = GetOptionAsBool("angle_error_mode", false);

                if (use_angle_error)
                {
                    // 角度误差模式：计算两个方向向量之间的角度误差
                    // 计算重建的3D点方向向量 y
                    Vector3d y = R_lc_x_l * tmp_A * t_r_base +
                                 (base_s * base_s) * t_c -
                                 (R_lc_x_l * tmp_A * R_lr + base_s * base_s * R_lc) * t_l_base;

                    // 计算角度误差
                    // tmp_residual = cross(x_c, y)，所以：
                    double cross_norm = tmp_residual.norm();
                    double x_c_norm = x_c.norm();
                    double y_norm = y.norm();

                    // 避免除零
                    if (y_norm < 1e-10)
                    {
                        continue; // 跳过无效的重建
                    }

                    // 计算sin(angle) = ||cross(x_c, y)|| / (||x_c|| * ||y||)
                    double sin_angle = cross_norm / (x_c_norm * y_norm);

                    // 限制sin_angle在[-1, 1]范围内（数值稳定性）
                    sin_angle = std::min(1.0, std::max(-1.0, sin_angle));

                    // 角度误差（弧度，不使用平方）
                    residual_value = std::asin(sin_angle);
                }
                else
                {
                    // 代数误差模式：使用原来的平方范数计算方式
                    residual_value = tmp_residual.squaredNorm();
                }

                current_obs_residuals.push_back(residual_value);
            }

            // 计算并返回median残差
            if (!current_obs_residuals.empty())
            {
                return CalculateMedian(current_obs_residuals);
            }
            else
            {
                return -1.0; // 返回无效值
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "计算MedResidualLiGT失败: " << e.what() << std::endl;
            return -1.0; // 返回无效值
        }
    }

#ifdef _DEBUG
    // Debug模式下输出统计数据到CSV文件
    void MethodGlobalOutlierRemoval::OutputDebugStatistics(
        const std::vector<double> &all_individual_residuals,
        const std::vector<double> &all_median_residuals_per_baseline_pair,
        const std::vector<double> &all_final_residuals_per_obs,
        size_t total_baseline_pairs,
        size_t total_valid_tracks)
    {
        try
        {
            // 输出baseline pairs的median残差统计
            if (!all_median_residuals_per_baseline_pair.empty())
            {
                std::ofstream baseline_stats_file("debug_baseline_residuals.csv");
                if (baseline_stats_file.is_open())
                {
                    baseline_stats_file << "BaselinePairIndex,MedianResidual\n";
                    for (size_t i = 0; i < all_median_residuals_per_baseline_pair.size(); ++i)
                    {
                        baseline_stats_file << i << "," << std::fixed << std::setprecision(12)
                                            << all_median_residuals_per_baseline_pair[i] << "\n";
                    }
                    baseline_stats_file.close();
                    std::cout << "Debug: Baseline pairs残差数据已保存到 debug_baseline_residuals.csv" << std::endl;
                }
            }

            // 输出最终观测残差统计
            if (!all_final_residuals_per_obs.empty())
            {
                std::ofstream final_stats_file("debug_final_residuals.csv");
                if (final_stats_file.is_open())
                {
                    final_stats_file << "ObservationIndex,FinalResidual\n";
                    for (size_t i = 0; i < all_final_residuals_per_obs.size(); ++i)
                    {
                        final_stats_file << i << "," << std::fixed << std::setprecision(12)
                                         << all_final_residuals_per_obs[i] << "\n";
                    }
                    final_stats_file.close();
                    std::cout << "Debug: 最终观测残差数据已保存到 debug_final_residuals.csv" << std::endl;
                }
            }

            // 计算并输出统计信息
            std::ofstream summary_file("debug_medligt_summary.csv");
            if (summary_file.is_open())
            {
                summary_file << "统计项,数值\n";
                summary_file << "总有效轨迹数," << total_valid_tracks << "\n";
                summary_file << "总baseline pairs数," << total_baseline_pairs << "\n";
                summary_file << "最终观测残差数," << all_final_residuals_per_obs.size() << "\n";

                if (!all_final_residuals_per_obs.empty())
                {
                    // 计算最终残差的统计信息
                    std::vector<double> final_residuals_copy = all_final_residuals_per_obs;
                    double mean = std::accumulate(final_residuals_copy.begin(), final_residuals_copy.end(), 0.0) / final_residuals_copy.size();

                    // 计算方差
                    double variance = 0.0;
                    for (const double &val : final_residuals_copy)
                    {
                        variance += (val - mean) * (val - mean);
                    }
                    variance /= (final_residuals_copy.size() - 1);
                    double std_dev = std::sqrt(variance);

                    // 计算median
                    double median = CalculateMedian(final_residuals_copy);

                    // 计算最小值和最大值
                    auto minmax = std::minmax_element(all_final_residuals_per_obs.begin(), all_final_residuals_per_obs.end());

                    summary_file << "最终残差均值," << std::fixed << std::setprecision(12) << mean << "\n";
                    summary_file << "最终残差标准差," << std::fixed << std::setprecision(12) << std_dev << "\n";
                    summary_file << "最终残差中位数," << std::fixed << std::setprecision(12) << median << "\n";
                    summary_file << "最终残差最小值," << std::fixed << std::setprecision(12) << *minmax.first << "\n";
                    summary_file << "最终残差最大值," << std::fixed << std::setprecision(12) << *minmax.second << "\n";
                }

                summary_file.close();
                std::cout << "Debug: MedLiGT统计摘要已保存到 debug_medligt_summary.csv" << std::endl;
            }
        }
        catch (const std::exception &e)
        {
            std::cerr << "输出Debug统计数据失败: " << e.what() << std::endl;
        }
    }
#endif

    void MethodGlobalOutlierRemoval::DetectOutliersByDirectThreshold(
        const std::vector<ResidualInfo> &residual_info_list,
        std::vector<IndexT> &outlier_obs_ids)
    {
        if (residual_info_list.empty())
        {
            outlier_obs_ids.clear();
            return;
        }

        // 获取当前配置
        const bool use_angle_error = GetOptionAsBool("angle_error_mode", false);
        const std::string residual_method = GetOptionAsString("residual_method", "LiGT");

        double threshold;
        std::string threshold_description;

        if (use_angle_error)
        {
            // 角度误差模式：使用度数阈值，需要转换为弧度
            double threshold_degrees = std::stod(GetOptionAsString("direct_threshold_angle_error", "5.0"));
            threshold = threshold_degrees * M_PI / 180.0; // 转换为弧度
            threshold_description = std::to_string(threshold_degrees) + "度 (" + std::to_string(threshold) + "弧度)";
        }
        else
        {
            // 代数误差模式：根据残差计算方法选择阈值
            if (boost::iequals(residual_method, "LiGT"))
            {
                threshold = std::stod(GetOptionAsString("direct_threshold_ligt_algebraic", "1e-4"));
                threshold_description = std::to_string(threshold) + " (LiGT代数误差)";
            }
            else if (boost::iequals(residual_method, "MedLiGT"))
            {
                threshold = std::stod(GetOptionAsString("direct_threshold_medligt_algebraic", "1e-5"));
                threshold_description = std::to_string(threshold) + " (MedLiGT代数误差)";
            }
            else
            {
                // 默认使用LiGT阈值
                threshold = std::stod(GetOptionAsString("direct_threshold_ligt_algebraic", "1e-4"));
                threshold_description = std::to_string(threshold) + " (默认代数误差)";
                std::cerr << "警告：未知的残差计算方法 '" << residual_method << "'，使用默认LiGT阈值" << std::endl;
            }
        }

        // 检测异常值
        outlier_obs_ids.clear();
        outlier_obs_ids.reserve(residual_info_list.size() / 10); // 预估10%的异常率

        for (const auto &info : residual_info_list)
        {
            if (info.residual_value > threshold)
            {
                outlier_obs_ids.push_back(info.obs_id);
            }
        }

        // 输出统计信息
        const int log_level = std::stoi(GetOptionAsString("log_level", "1"));
        if (log_level > 0)
        {
            std::cout << "使用直接阈值检测 (阈值=" << threshold_description
                      << ") 检测到 " << outlier_obs_ids.size() << " 个异常点" << std::endl;

            if (log_level > 1 && !residual_info_list.empty())
            {
                // 计算残差统计信息
                std::vector<double> residuals;
                residuals.reserve(residual_info_list.size());
                for (const auto &info : residual_info_list)
                {
                    residuals.push_back(info.residual_value);
                }

                auto minmax = std::minmax_element(residuals.begin(), residuals.end());
                double mean = std::accumulate(residuals.begin(), residuals.end(), 0.0) / residuals.size();

                std::cout << "  - 残差范围: [" << *minmax.first << ", " << *minmax.second << "]" << std::endl;
                std::cout << "  - 残差均值: " << mean << std::endl;
                std::cout << "  - 异常点比例: " << (static_cast<double>(outlier_obs_ids.size()) / residual_info_list.size() * 100.0) << "%" << std::endl;
            }
        }
    }

} // namespace PoSDK
