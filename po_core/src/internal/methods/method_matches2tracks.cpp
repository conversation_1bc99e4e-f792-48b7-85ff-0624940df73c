/**
 * @file method_matches2tracks.cpp
 * @brief 实现从特征匹配构建特征轨迹的方法
 * @details 使用并查集(Union-Find)算法高效构建特征轨迹
 */

#include "method_matches2tracks.hpp"
#include "union_find.hpp"
#include <unordered_map>
#include "factory/factory.hpp"
#include <set>
#include <filesystem>

namespace PoSDK
{

    DataPtr MethodMatches2Tracks::Run()
    {
        try
        {
            if (log_level_ >= PO_LOG_NORMAL)
                DisplayConfigInfo();

            // 获取输入数据
            auto matches_ptr = Interface::GetDataPtr<Matches>(required_package_["data_matches"]);
            auto features_ptr = Interface::GetDataPtr<FeaturesInfo>(required_package_["data_features"]);

            if (!matches_ptr || !features_ptr)
            {
                PO_LOG_ERR << "Invalid input data for track building" << std::endl;
                return nullptr;
            }

            // 创建tracks数据
            auto tracks_data = FactoryData::Create("data_tracks");
            if (!tracks_data)
            {
                PO_LOG_ERR << "Failed to create tracks data" << std::endl;
                return nullptr;
            }

            auto tracks_ptr = Interface::GetDataPtr<Tracks>(tracks_data);
            if (!tracks_ptr)
            {
                PO_LOG_ERR << "Failed to get tracks pointer" << std::endl;
                return nullptr;
            }

            // 构建tracks
            BuildTracksFromMatches(*matches_ptr, *features_ptr, *tracks_ptr);

            return tracks_data;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Error in MethodMatches2Tracks::Run: " << e.what() << std::endl;
            return nullptr;
        }
        catch (...)
        {
            PO_LOG_ERR << "Unknown error in MethodMatches2Tracks::Run" << std::endl;
            return nullptr;
        }
    }

    void MethodMatches2Tracks::BuildTracksFromMatches(
        const Matches &matches,
        const FeaturesInfo &features,
        Tracks &tracks)
    {
        // ==================== 增强的输入数据验证 ====================

        // 1. 基本数据有效性检查
        if (matches.empty())
        {
            PO_LOG(PO_LOG_NORMAL) << "[MethodMatches2Tracks] Warning: Empty matches input, creating empty tracks" << std::endl;
            tracks.clear();
            return;
        }

        if (features.empty())
        {
            PO_LOG_ERR << "[MethodMatches2Tracks] Error: Empty features input" << std::endl;
            tracks.clear();
            return;
        }

        // 2. 数据一致性验证：检查matches中的view_id是否与features索引一致
        std::set<ViewId> referenced_views;
        size_t total_matches = 0;
        size_t inlier_matches = 0;

        for (const auto &[view_pair, id_matches] : matches)
        {
            referenced_views.insert(view_pair.first);
            referenced_views.insert(view_pair.second);
            total_matches += id_matches.size();

            for (const auto &match : id_matches)
            {
                if (match.is_inlier)
                    inlier_matches++;
            }
        }

        // 3. 验证view_id范围
        ViewId max_view_id = *std::max_element(referenced_views.begin(), referenced_views.end());
        if (max_view_id >= features.size())
        {
            PO_LOG_ERR << "[MethodMatches2Tracks] Critical Error: View ID " << max_view_id
                       << " exceeds features size " << features.size()
                       << ". Data inconsistency detected!" << std::endl;
            tracks.clear();
            return;
        }

        // 4. 检查inlier比例，如果太低发出警告
        double inlier_ratio = total_matches > 0 ? (double)inlier_matches / total_matches : 0.0;
        if (inlier_ratio < 0.3) // 少于10%的inlier
        {
            PO_LOG(PO_LOG_NORMAL) << "[MethodMatches2Tracks] Warning: Low inlier ratio "
                                  << std::fixed << std::setprecision(2) << (inlier_ratio * 100)
                                  << "% (" << inlier_matches << "/" << total_matches
                                  << "). Possible dual-view estimation issues." << std::endl;
        }

        // 5. 预先验证特征索引范围
        std::map<ViewId, PtsId> max_feature_ids;
        for (const auto &[view_pair, id_matches] : matches)
        {
            for (const auto &match : id_matches)
            {
                if (!match.is_inlier)
                    continue;

                max_feature_ids[view_pair.first] = std::max(max_feature_ids[view_pair.first], match.i);
                max_feature_ids[view_pair.second] = std::max(max_feature_ids[view_pair.second], match.j);
            }
        }

        // 验证特征索引是否超出范围
        for (const auto &[view_id, max_pts_id] : max_feature_ids)
        {
            if (view_id >= features.size())
            {
                PO_LOG_ERR << "[MethodMatches2Tracks] Error: View ID " << view_id
                           << " not found in features" << std::endl;
                tracks.clear();
                return;
            }

            if (max_pts_id >= features[view_id].features.size())
            {
                PO_LOG_ERR << "[MethodMatches2Tracks] Error: Feature ID " << max_pts_id
                           << " exceeds feature count " << features[view_id].features.size()
                           << " in view " << view_id << std::endl;
                tracks.clear();
                return;
            }
        }

        PO_LOG(PO_LOG_NORMAL) << "[MethodMatches2Tracks] Input validation passed:" << std::endl;
        PO_LOG(PO_LOG_NORMAL) << "  - Views: " << referenced_views.size()
                              << ", Matches: " << total_matches
                              << ", Inliers: " << inlier_matches
                              << " (" << std::fixed << std::setprecision(1) << (inlier_ratio * 100) << "%)" << std::endl;

        // ==================== 原有的轨迹构建逻辑 ====================

        using NodeType = std::pair<ViewId, PtsId>;
        std::map<NodeType, Size> node_to_index;
        Size node_count = 0;

        // 1. 只为匹配点建立索引（保持原有效率）
        for (const auto &[view_pair, id_matches] : matches)
        {
            for (const auto &match : id_matches)
            {
                if (!match.is_inlier)
                    continue;

                NodeType node_i(view_pair.first, match.i);
                NodeType node_j(view_pair.second, match.j);

                if (node_to_index.find(node_i) == node_to_index.end())
                {
                    node_to_index[node_i] = node_count++;
                }
                if (node_to_index.find(node_j) == node_to_index.end())
                {
                    node_to_index[node_j] = node_count++;
                }
            }
        }

        // 检查是否有有效的inlier匹配
        if (node_to_index.empty())
        {
            PO_LOG(PO_LOG_NORMAL) << "[MethodMatches2Tracks] Warning: No valid inlier matches found, creating empty tracks" << std::endl;
            tracks.clear();
            return;
        }

        UnionFind uf_tree(node_count);

        // 2. 合并匹配点
        for (const auto &[view_pair, id_matches] : matches)
        {
            for (const auto &match : id_matches)
            {
                if (!match.is_inlier)
                    continue;
                NodeType node_i(view_pair.first, match.i);
                NodeType node_j(view_pair.second, match.j);

                // 额外的安全检查
                auto it_i = node_to_index.find(node_i);
                auto it_j = node_to_index.find(node_j);
                if (it_i != node_to_index.end() && it_j != node_to_index.end())
                {
                    uf_tree.Union(it_i->second, it_j->second);
                }
            }
        }

        // 3. 构建轨迹（使用root_id作为统一的track_id）
        std::map<Size, TrackInfo> track_builder;
        size_t skipped_nodes = 0;
        IndexT global_obs_id = 0; // 全局唯一的obs_id计数器

        for (const auto &[node, index] : node_to_index)
        {
            Size root_id = uf_tree.Find(index);

            // 增强的边界检查和错误处理
            try
            {
                if (node.first >= features.size())
                {
                    PO_LOG_ERR << "[MethodMatches2Tracks] Invalid view_id: " << node.first
                               << " (max: " << features.size() - 1 << ")" << std::endl;
                    skipped_nodes++;
                    continue;
                }

                const auto &view_features = features[node.first];
                if (node.second >= view_features.features.size())
                {
                    PO_LOG_ERR << "[MethodMatches2Tracks] Invalid feature_id: " << node.second
                               << " (max: " << view_features.features.size() - 1 << ")"
                               << " in view " << node.first << std::endl;
                    skipped_nodes++;
                    continue;
                }

                const auto &feature = view_features.features[node.second];

                // 验证特征坐标有效性
                if (!std::isfinite(feature.coord.x()) || !std::isfinite(feature.coord.y()))
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[MethodMatches2Tracks] Warning: Invalid feature coordinates ("
                                           << feature.coord.x() << ", " << feature.coord.y()
                                           << ") in view " << node.first << ", feature " << node.second << std::endl;
                    skipped_nodes++;
                    continue;
                }

                ObsInfo obs;
                obs.view_id = node.first;
                obs.pts_id = root_id;         // 使用root_id作为统一的pts_id
                obs.obs_id = global_obs_id++; // 设置全局唯一的obs_id
                obs.coord = Vector2d(feature.coord.x(), feature.coord.y());
                obs.is_used = feature.is_used;

                track_builder[root_id].track.push_back(obs);
                track_builder[root_id].is_used = true;
            }
            catch (const std::exception &e)
            {
                PO_LOG_ERR << "[MethodMatches2Tracks] Exception processing node ("
                           << node.first << ", " << node.second << "): " << e.what() << std::endl;
                skipped_nodes++;
                continue;
            }
        }

        // 报告跳过的节点数量
        if (skipped_nodes > 0)
        {
            PO_LOG(PO_LOG_NORMAL) << "[MethodMatches2Tracks] Warning: Skipped " << skipped_nodes
                                  << " invalid nodes out of " << node_to_index.size() << std::endl;
        }

        // 4. 过滤和重新分配连续的track_id
        const Size min_track_length = GetOptionAsIndexT("min_track_length", 2);
        const Size max_track_length = GetOptionAsIndexT("max_track_length", 100);

        tracks.clear();
        tracks.reserve(track_builder.size());

        Size new_track_id = 0;
        size_t filtered_tracks = 0;

        for (auto &[root_id, track_info] : track_builder)
        {
            // 检查轨迹长度是否在有效范围内
            if (track_info.track.size() >= min_track_length && track_info.track.size() <= max_track_length)
            {
                // 验证轨迹内部一致性
                bool track_valid = true;
                std::set<ViewId> track_views;

                for (const auto &obs : track_info.track)
                {
                    // 检查是否有重复的view_id
                    if (track_views.find(obs.view_id) != track_views.end())
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "[MethodMatches2Tracks] Warning: Duplicate view_id "
                                               << obs.view_id << " in track " << root_id << std::endl;
                        track_valid = false;
                        break;
                    }
                    track_views.insert(obs.view_id);
                }

                if (track_valid)
                {
                    // 更新所有观测点的pts_id为新的连续id
                    for (auto &obs : track_info.track)
                    {
                        obs.pts_id = new_track_id;
                    }
                    tracks.push_back(std::move(track_info));
                    new_track_id++;
                }
                else
                {
                    filtered_tracks++;
                }
            }
            else
            {
                filtered_tracks++;
            }
        }

        // 检查最终结果的有效性
        if (tracks.empty())
        {
            PO_LOG(PO_LOG_NORMAL) << "[MethodMatches2Tracks] Warning: No valid tracks generated. "
                                  << "Original track candidates: " << track_builder.size()
                                  << ", Filtered: " << filtered_tracks << std::endl;
        }
        else
        {
            // 统计信息
            size_t total_observations = 0;
            size_t min_obs = SIZE_MAX, max_obs = 0;
            for (const auto &track : tracks)
            {
                size_t obs_count = track.track.size();
                total_observations += obs_count;
                min_obs = std::min(min_obs, obs_count);
                max_obs = std::max(max_obs, obs_count);
            }

            double avg_obs = (double)total_observations / tracks.size();

            PO_LOG(PO_LOG_NORMAL) << "[MethodMatches2Tracks] Track statistics:" << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "  - Valid tracks: " << tracks.size()
                                  << " (filtered: " << filtered_tracks << ")" << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "  - Total observations: " << total_observations
                                  << " with unique obs_ids: 0-" << (global_obs_id - 1) << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "  - Observations per track: min=" << min_obs
                                  << ", max=" << max_obs
                                  << ", avg=" << std::fixed << std::setprecision(1) << avg_obs << std::endl;
        }

        // 6. 导出轨迹数据（如果配置为导出）
        bool is_export_tracks = GetOptionAsBool("export_tracks", false);
        if (method_options_["export_tracks"] == "ON")
        {
            std::string export_path = method_options_["export_tracks_path"];
            if (!export_path.empty())
            {
                std::filesystem::path tracks_path(export_path);
                std::filesystem::create_directories(tracks_path);

                // 创建轨迹数据容器
                auto tracks_data = FactoryData::Create("data_tracks");
                if (tracks_data)
                {
                    auto tracks_ptr = Interface::GetDataPtr<Tracks>(tracks_data);
                    if (tracks_ptr)
                    {
                        *tracks_ptr = tracks; // 复制轨迹数据

                        // 导出轨迹数据
                        if (tracks_data->Save(export_path, "tracks_all", ".pb"))
                        {
                            PO_LOG(PO_LOG_NONE) << "[MethodMatches2Tracks] Tracks exported to: "
                                                << export_path << std::endl;
                        }
                        else
                        {
                            PO_LOG_ERR << "[MethodMatches2Tracks] Failed to export tracks to: "
                                       << export_path << std::endl;
                        }
                    }
                }
            }
        }
    }

} // namespace PoSDK
