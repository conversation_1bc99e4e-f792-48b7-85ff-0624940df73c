/**
 * @file opengv_simulator.hpp
 * @brief OpenGV双视图仿真器，生成适用于多种双视图估计方法的数据包
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef POMVG_OPENGV_SIMULATOR_HPP_
#define POMVG_OPENGV_SIMULATOR_HPP_

#include "interfaces_preset_profiler.hpp"
#include "interfaces_robust_estimator.hpp"
#include "data/data_camera_models.hpp"
#include "data/data_features.hpp"
#include "data/data_matches.hpp"
#include "data/data_relative_poses.hpp"
#include "factory/factory.hpp"
#include "types.hpp"
#include <Eigen/Dense>
#include <random>
#include <opengv/types.hpp>
#include <opengv/relative_pose/CentralRelativeAdapter.hpp>

namespace PoSDK
{
    using namespace Interface;
    using namespace types;
    using namespace Eigen;

    /**
     * @brief 用于生成双视图估计数据的OpenGV仿真器
     *
     * 该仿真器生成一个包含多种数据接口的DataPackage，
     * 支持不同的双视图估计方法通过method.GetType()获取对应的数据。
     *
     * 支持的方法类型：
     * - method_LiRP: 提供BearingPairs数据
     * - method_povgSixPoint: 提供BearingPairs数据（复用LiRP数据格式）
     * - opengv_model_estimator: 提供相机模型、特征点、匹配数据
     * - opencv_two_view_estimator: 提供特征点和匹配数据
     * - two_view_estimator: 提供通用双视图数据
     */
    class OpenGVSimulator : public MethodPresetProfiler
    {
    public:
        OpenGVSimulator();
        ~OpenGVSimulator() override = default;

        const std::string &GetType() const override
        {
            static const std::string type = "opengv_simulator";
            return type;
        }

        DataPtr Run() override;

        /**
         * @brief 获取生成的真值相对位姿
         * @return 真值相对位姿
         */
        const RelativePose &GetGroundTruthPose() const { return ground_truth_pose_; }

        /**
         * @brief 获取内点索引（无外点情况下的所有点）
         * @return 内点索引集合
         */
        const std::set<size_t> &GetInlierIndices() const { return inlier_indices_; }

    private:
        /**
         * @brief 生成双视图场景数据
         * @param config 场景配置参数
         * @param rng 随机数生成器
         * @param bearingVectors1 输出：第一个视图的归一化方向向量
         * @param bearingVectors2 输出：第二个视图的归一化方向向量
         * @param gt_R 输出：真值旋转矩阵
         * @param gt_t 输出：真值平移向量
         */
        void GenerateTwoViewScene(
            double max_parallax,
            double max_rotation,
            size_t num_points,
            double noise_level,
            double outlier_fraction,
            std::mt19937 &rng,
            opengv::bearingVectors_t &bearingVectors1,
            opengv::bearingVectors_t &bearingVectors2,
            opengv::rotation_t &gt_R,
            opengv::translation_t &gt_t);

        /**
         * @brief 创建适用于method_LiRP的BearingPairs数据
         * @param bearingVectors1 第一个视图的方向向量
         * @param bearingVectors2 第二个视图的方向向量
         * @return BearingPairs数据的DataPtr
         */
        DataPtr CreateLiRPData(
            const opengv::bearingVectors_t &bearingVectors1,
            const opengv::bearingVectors_t &bearingVectors2);

        /**
         * @brief 创建适用于opengv_model_estimator的数据包
         * @param bearingVectors1 第一个视图的方向向量
         * @param bearingVectors2 第二个视图的方向向量
         * @param camera_data 相机模型数据
         * @return 包含相机、特征、匹配数据的DataPackage
         */
        DataPtr CreateOpenGVModelEstimatorData(
            const opengv::bearingVectors_t &bearingVectors1,
            const opengv::bearingVectors_t &bearingVectors2,
            DataPtr camera_data);

        /**
         * @brief 创建适用于opencv_two_view_estimator的数据包
         * @param bearingVectors1 第一个视图的方向向量
         * @param bearingVectors2 第二个视图的方向向量
         * @param camera_data 相机模型数据
         * @return 包含特征和匹配数据的DataPackage
         */
        DataPtr CreateOpenCVTwoViewEstimatorData(
            const opengv::bearingVectors_t &bearingVectors1,
            const opengv::bearingVectors_t &bearingVectors2,
            DataPtr camera_data);

        /**
         * @brief 创建适用于two_view_estimator的数据包
         * @param bearingVectors1 第一个视图的方向向量
         * @param bearingVectors2 第二个视图的方向向量
         * @param camera_data 相机模型数据
         * @return 通用双视图数据包
         */
        DataPtr CreateTwoViewEstimatorData(
            const opengv::bearingVectors_t &bearingVectors1,
            const opengv::bearingVectors_t &bearingVectors2,
            DataPtr camera_data);

        /**
         * @brief 创建相机模型数据
         * @param focal_length 焦距
         * @param image_width 图像宽度
         * @param image_height 图像高度
         * @return 相机模型数据的DataPtr
         */
        DataPtr CreateCameraModelData(
            double focal_length,
            double image_width,
            double image_height);

        /**
         * @brief 创建特征点数据
         * @param bearingVectors1 第一个视图的方向向量
         * @param bearingVectors2 第二个视图的方向向量
         * @param camera_data 相机模型数据
         * @return 特征点数据的DataPtr
         */
        DataPtr CreateFeaturesData(
            const opengv::bearingVectors_t &bearingVectors1,
            const opengv::bearingVectors_t &bearingVectors2,
            DataPtr camera_data);

        /**
         * @brief 创建匹配数据（DataSample<IdMatches>格式，用于opengv_model_estimator等）
         * @param num_matches 匹配数量
         * @return 匹配数据的DataPtr
         */
        DataPtr CreateSampleIdMatches(size_t num_matches);

        /**
         * @brief 创建DataMatches格式的匹配数据（用于two_view_estimator等）
         * @param num_matches 匹配数量
         * @return DataMatches类型的匹配数据的DataPtr
         */
        DataPtr CreateDataMatches(size_t num_matches);

        /**
         * @brief 创建真值相对位姿数据
         * @param gt_R 真值旋转矩阵
         * @param gt_t 真值平移向量
         * @return 真值相对位姿数据的DataPtr
         */
        DataPtr CreateGroundTruthData(
            const opengv::rotation_t &gt_R,
            const opengv::translation_t &gt_t);

        /**
         * @brief 将归一化方向向量转换为BearingPairs格式
         * @param bearingVectors1 第一个视图的方向向量
         * @param bearingVectors2 第二个视图的方向向量
         * @param bearing_pairs 输出：BearingPairs数据
         */
        void ConvertToBearingPairs(
            const opengv::bearingVectors_t &bearingVectors1,
            const opengv::bearingVectors_t &bearingVectors2,
            BearingPairs &bearing_pairs);

        /**
         * @brief 将归一化方向向量投影到图像平面
         * @param bearing_vector 归一化方向向量
         * @param camera_model 相机模型
         * @return 图像坐标
         */
        Eigen::Vector2d ProjectToImagePlane(
            const opengv::bearingVector_t &bearing_vector,
            const CameraModel &camera_model);

        // 成员变量
        RelativePose ground_truth_pose_;  ///< 真值相对位姿
        std::set<size_t> inlier_indices_; ///< 内点索引
        DataPtr camera_model_data_;       ///< 相机模型数据缓存
    };

} // namespace PoSDK

#endif // POMVG_OPENGV_SIMULATOR_HPP_