/**
 * @file method_matches2tracks.hpp
 * @brief 从特征匹配构建特征轨迹的方法
 * @details 将特征点匹配信息转换为特征点轨迹，需要特征和匹配数据
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _METHOD_MATCHES2TRACKS_
#define _METHOD_MATCHES2TRACKS_

#include "interfaces_preset_profiler.hpp"
#include "types.hpp"

namespace PoSDK
{
    using namespace Interface;
    class MethodMatches2Tracks : public Interface::MethodPresetProfiler
    {
    public:
        MethodMatches2Tracks()
        {
            // 设置需要的输入数据包
            required_package_["data_features"] = nullptr;
            required_package_["data_matches"] = nullptr;

            // 先加载通用配置
            InitializeDefaultConfigPath(); // 这会加载 [method_matches2tracks] 部分
        }
        virtual ~MethodMatches2Tracks() = default;
        const std::string &GetType() const override
        {
            static const std::string type = "method_matches2tracks";
            return type;
        }

        DataPtr Run() override;

    private:
        void BuildTracksFromMatches(
            const Matches &matches,
            const FeaturesInfo &features,
            Tracks &tracks);

        // 添加轨迹有效性检查函数
        bool IsValidTrack(const TrackInfo &track_info) const;
    };

} // namespace PoSDK

#endif // _METHOD_MATCHES2TRACKS_
