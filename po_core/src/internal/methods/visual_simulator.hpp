/**
 * @file visual_simulator.hpp
 * @brief 可视化模拟器用于生成合成数据集
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef POMVG_VISUAL_SIMULATOR_HPP_
#define POMVG_VISUAL_SIMULATOR_HPP_

#include "interfaces_preset_profiler.hpp"
#include "data/data_tracks.hpp"
#include "data/data_camera_models.hpp"
#include "data/data_global_poses.hpp"
#include "data/data_points_3d.hpp"
#include "factory/factory.hpp"
#include "types.hpp"
#include <Eigen/Dense>
#include <random>

namespace PoSDK
{
    using namespace Interface;
    using namespace types;
    using namespace Eigen;

    /**
     * @brief 用于生成合成视觉数据的模拟器
     */
    class VisualSimulator : public MethodPresetProfiler
    {
    public:
        VisualSimulator();
        ~VisualSimulator() override = default;

        const std::string &GetType() const override
        {
            static const std::string type = "visual_simulator";
            return type;
        }

        DataPtr Run() override;

        /**
         * @brief 保存真值数据到simu_folder_/GroundTruth目录
         * @return 是否保存成功
         */
        bool SaveGroundTruth();

        /**
         * @brief 从simu_folder_/GroundTruth目录加载真值数据
         * @return 是否加载成功
         */
        bool LoadGroundTruth();

        /**
         * @brief 获取全局位姿真值数据
         * @return 全局位姿数据的共享指针
         */
        std::shared_ptr<DataGlobalPoses> GetGlobalPoses() const
        {
            return global_poses_ptr_;
        }

        /**
         * @brief 获取相机模型数据
         * @return 相机模型数据的共享指针
         */
        std::shared_ptr<DataCameraModels> GetCameraModels() const
        {
            return camera_model_ptr_;
        }

        /**
         * @brief 获取真值3D点数据
         * @return 真值3D点数据的共享指针
         */
        std::shared_ptr<DataPoints3D> GetGroundTruthPoints() const
        {
            return gt_points_ptr_;
        }

        /**
         * @brief 按照PoSDK格式导出数据为meshlab工程格式
         * @param export_path 导出路径
         * @return 是否导出成功
         */
        bool ExportToMeshlabPoSDK(const std::string &export_path);

    private:
        /**
         * @brief 生成结构化的相机位姿和3D点
         * @param num_views 相机视图数量
         * @param num_points 3D点数量
         * @param structure_type 结构类型，默认为close_loop_circle
         * @param noise_level 噪声水平
         * @param rng 随机数生成器
         * @param Rs 输出的旋转矩阵
         * @param ts 输出的平移向量
         * @param X 输出的3D点
         * @return 是否成功生成结构
         */
        bool GenerateStructure(
            int num_views,
            int num_points,
            const std::string &structure_type,
            double noise_level,
            std::mt19937 &rng,
            GlobalRotations &Rs,
            GlobalTranslations &ts,
            Points3d &X);

        /**
         * @brief 以圆环方式生成结构
         * @param num_views 相机视图数量
         * @param num_points 3D点数量
         * @param num_circles 环形数量
         * @param dt_setting 位移量
         * @param PRO_ratio 纯旋转异常点比例
         * @param rng 随机数生成器
         * @param Rs 输出的旋转矩阵
         * @param ts 输出的平移向量
         * @param X 输出的3D点
         */
        void CloseLoopCircle(
            int num_views,
            int num_points,
            int num_circles,
            double dt_setting,
            double PRO_ratio,
            std::mt19937 &rng,
            GlobalRotations &Rs,
            GlobalTranslations &ts,
            Points3d &X);

        /**
         * @brief 生成特征点
         * @param Ks 相机内参矩阵列表
         * @param Rs 相机旋转矩阵列表
         * @param ts 相机平移向量列表
         * @param X 3D点(3 x N)
         * @param noise_level 像素噪声水平
         * @param use_gaussian_noise 是否使用高斯噪声
         * @param max_points_per_view 每个视图最大点数
         * @param closest_points 选择最近点的数量
         * @param tracks 输出的跟踪数据
         */
        void GenerateFeatures(
            const KMats &Ks,
            const GlobalRotations &Rs,
            const GlobalTranslations &ts,
            const Points3d &X,
            double noise_level,
            bool use_gaussian_noise,
            int max_points_per_view,
            int closest_points,
            Tracks &tracks);

        /**
         * @brief 向轨迹中添加一定比例的观测点异常值
         * @param tracks 输入输出的轨迹数据
         * @param obs_outlier_ratio 异常点比例，范围[0,1]
         * @param rng 随机数生成器
         * @return 实际添加的异常点的obs_id集合
         */
        std::set<IndexT> AddOutliersToTracks(Tracks &tracks, double obs_outlier_ratio, std::mt19937 &rng);

        // 检查点是否在图像边界内
        bool IsWithinImageBounds(const Vector2d &point, double width, double height) const;

        // 生成方向性噪声
        Vector2d GenerateDirectionalNoise(double magnitude);

        // 生成高斯噪声
        Vector2d GenerateGaussianNoise(double sigma);

        // 创建随机相机位姿
        void GenerateRandomCameras(
            int num_views,
            GlobalRotations &Rs,
            GlobalTranslations &ts,
            std::mt19937 &rng);

        // 创建相机内参
        void GenerateCameraIntrinsics(
            int num_views,
            double focal_length,
            double width,
            double height,
            KMats &Ks);

        // 新增：更新观测ID的辅助函数
        void UpdateObsIds(Tracks &tracks);

        /**
         * @brief 生成模拟图像文件
         * @param image_path 图像文件路径
         * @return 是否生成成功
         */
        bool GenerateSimulatorImage(const std::string &image_path);

        /**
         * @brief 显示仿真摘要统计信息
         * @param num_views 视图数量
         * @param num_points 3D点数量
         * @param structure_type 结构类型
         * @param noise_level 噪声水平
         * @param tracks 生成的轨迹数据
         */
        void DisplaySimulationSummary(int num_views, int num_points,
                                      const std::string &structure_type,
                                      double noise_level,
                                      const Tracks &tracks);

        // 仿真数据存储目录
        std::string simu_folder_;

        // 保存真值数据
        std::shared_ptr<DataGlobalPoses> global_poses_ptr_;
        std::shared_ptr<DataCameraModels> camera_model_ptr_;
        std::shared_ptr<DataPoints3D> gt_points_ptr_;
    };

} // namespace PoSDK

#endif // POMVG_VISUAL_SIMULATOR_HPP_
