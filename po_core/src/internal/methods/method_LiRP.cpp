/**
 * @file method_LiRP.cpp
 * @brief LiRP相对位姿估计器实现
 * @copyright Copyright (c) 2024 Qi <PERSON>ai
 */

#include "method_LiRP.hpp"

#include <boost/algorithm/string.hpp> // 添加 Boost 字符串算法库
#include <chrono>                     // 时间统计
#include <iomanip>                    // 格式化输出
#include "Spectra/SymEigsShiftSolver.h"
#include "Spectra/MatOp/SparseSymShiftSolve.h"
#include "relative_residuals.hpp"
#include "relative_pose.hpp"
#include "compute_coefficients.hpp"

namespace PoSDK
{
    using namespace Spectra;

    // -----------------------------------------------------------------------------
    // 辅助函数
    // -----------------------------------------------------------------------------

    // 辅助函数：计算克罗内克积
    // 优化版本的克罗内克积计算（用于row vector × row vector）
    MatrixXd kroneckerProduct(const MatrixXd &A, const MatrixXd &B)
    {
        MatrixXd result(A.rows() * B.rows(), A.cols() * B.cols());
        for (int i = 0; i < A.rows(); ++i)
        {
            for (int j = 0; j < A.cols(); ++j)
            {
                result.block(i * B.rows(), j * B.cols(), B.rows(), B.cols()) = A(i, j) * B;
            }
        }
        return result;
    }

    // 内联快速克罗内克积计算（专用于3x1向量的转置）
    inline RowVectorXd fastKroneckerProduct(const Vector3d &a, const Vector3d &b, double weight = 1.0)
    {
        RowVectorXd result(9);
        const double a0w = a(0) * weight;
        const double a1w = a(1) * weight;
        const double a2w = a(2) * weight;

        result(0) = a0w * b(0);
        result(1) = a0w * b(1);
        result(2) = a0w * b(2);
        result(3) = a1w * b(0);
        result(4) = a1w * b(1);
        result(5) = a1w * b(2);
        result(6) = a2w * b(0);
        result(7) = a2w * b(1);
        result(8) = a2w * b(2);

        return result;
    }

    // -----------------------------------------------------------------------------
    // 主函数
    // -----------------------------------------------------------------------------

    MethodLiRP::MethodLiRP()
    {
        // 设置需要的输入数据包
        required_package_["data_sample"] = nullptr; // DataSample<BearingPairs>

        // 初始化默认配置路径
        InitializeDefaultConfigPath();

        // 预分配固定大小的矩阵缓冲区
        ATA_buffer_.setZero();
        eigenvectors_buffer_.setZero();
        eigenvalues_buffer_.setZero();
        null_space_buffer_.setZero();
        C1_buffer_.setZero();
        C2_buffer_.setZero();
        C3_buffer_.setZero();
        M_buffer_.setZero();
        Evec_buffer_.setZero();
        coeff_mat_buffer_.setZero();
        coeff_D_mat_buffer_.setZero();

        // 初始化日志目录 - 将在MethodPreset中根据log_level_决定是否真的创建
        // InitializeLogDir(); // 如果需要，可以在基类或此处无条件调用，或基于log_level_
    }

    bool MethodLiRP::ConvertBearingPairsToBearingVectors(
        BearingVectors &points1,
        BearingVectors &points2) const
    {

        // 获取输入数据
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (!sample_ptr)
        {
            PO_LOG_ERR << "Invalid data sample" << std::endl;
            return false;
        }

        if (sample_ptr->empty())
        {
            PO_LOG_ERR << "Empty bearing pairs" << std::endl;
            return false;
        }

        const size_t num_points = sample_ptr->size();

        // 调整输出向量的大小
        points1.resize(3, num_points); // 修正为正确的维度 (3 x N)
        points2.resize(3, num_points);

        // 使用DataSample的迭代器特性遍历数据
        size_t i = 0;
        for (const auto &bearing_pair : *sample_ptr)
        {
            points1.col(i) = bearing_pair.head<3>(); // 使用col()访问列
            points2.col(i) = bearing_pair.tail<3>();
            ++i;
        }

        return true;
    }

    DataPtr MethodLiRP::Run()
    {

        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "Run called. Displaying config if log_level is appropriate." << std::endl;
            DisplayConfigInfo();
        }

        // 1. 更新DataSample状态 - 对于非鲁棒LiRP，所有观测都是内点
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (sample_ptr && !sample_ptr->empty())
        {
            // 创建内点索引向量（所有点都是内点）
            auto inliers_ptr = std::make_shared<std::vector<size_t>>();
            inliers_ptr->reserve(sample_ptr->size());
            for (size_t i = 0; i < sample_ptr->size(); ++i)
            {
                inliers_ptr->push_back(i);
            }

            // 设置到DataSample中
            sample_ptr->SetBestInliers(inliers_ptr);

            PO_LOG(PO_LOG_VERBOSE) << "Updated DataSample with " << inliers_ptr->size()
                                   << " inliers (all points for non-robust LiRP)" << std::endl;
        }

        // 2. 从method_options_获取视图对信息
        ViewPair view_pair(
            GetOptionAsIndexT("view_i", 0), // 源视图ID
            GetOptionAsIndexT("view_j", 1)  // 目标视图ID
        );

        // 3. 转换bearing pairs到bearing vectors
        BearingVectors points1, points2;
        if (!ConvertBearingPairsToBearingVectors(points1, points2))
        {
            PO_LOG_ERR << "Failed to convert bearing pairs" << std::endl;
            return nullptr;
        }

        // 4. 检查是否有权重信息
        VectorXd *weights_ptr = nullptr;
        VectorXd weights;

        // 检查先验信息中是否包含权重
        if (!prior_info_.empty() && prior_info_.find("weights") != prior_info_.end())
        {
            auto weights_data = std::dynamic_pointer_cast<DataMap<VectorXd>>(prior_info_["weights"]);
            if (weights_data && weights_data->GetMapPtr())
            {
                // 获取权重向量的指针
                weights = *(weights_data->GetMapPtr());
                weights_ptr = &weights;

                PO_LOG(PO_LOG_VERBOSE) << "Using weights from prior_info_" << std::endl;
            }
        }

        // 5. 估计相对位姿
        Matrix3d R;
        Vector3d t;

        // 记录核心计算开始时间
        auto core_start_time = std::chrono::high_resolution_clock::now();

        if (!SolveLiRPMainOptimized(points1, points2, R, t, weights_ptr))
        {
            PO_LOG_ERR << "Failed to estimate relative pose" << std::endl;
            return nullptr;
        }

        // 记录核心计算结束时间
        auto core_end_time = std::chrono::high_resolution_clock::now();
        double core_time_ms = std::chrono::duration<double, std::milli>(core_end_time - core_start_time).count();

        // 设置核心计算时间
        SetCoreTime(core_time_ms);

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "LiRP core computation time: " << core_time_ms << " ms" << std::endl;
        }

        // 6. 创建并返回结果
        RelativePose relative_pose(
            view_pair.first,  // 源视图ID
            view_pair.second, // 目标视图ID
            R,                // 相对旋转
            t,                // 相对平移
            1.0f              // 默认权重
        );

        return std::make_shared<DataMap<RelativePose>>(relative_pose);
    }

    bool MethodLiRP::SolveLiRPMain(
        const BearingVectors &points1,
        const BearingVectors &points2,
        Matrix3d &R,
        Vector3d &t,
        const VectorXd *weights)
    {

        // 1. 检查输入数据
        const int num_matches = points1.cols();
        if (num_matches < kMinNumPoints)
        {
            PO_LOG_ERR << "Need at least " << kMinNumPoints << " point pairs, got " << num_matches << std::endl;
            return false;
        }

        // 2. 构建克罗内克积矩阵A
        Matrix<double, Dynamic, 9> A(num_matches, 9);

        // 使用优化版本的快速克罗内克积构建矩阵A
        for (int i = 0; i < num_matches; ++i)
        {
            double w = (weights != nullptr) ? (*weights)(i) : 1.0;
            // 使用内联快速克罗内克积计算，避免矩阵创建开销
            A.row(i) = fastKroneckerProduct(points2.col(i), points1.col(i), w);
        }

        // 3. 构建ATA矩阵
        Matrix<double, 9, 9> ATA = A.transpose() * A;

        // 4. 构建稀疏矩阵用于特征值分解
        SparseMatrix<double> sparseATA = ATA.sparseView();

        // 5. 构造矩阵操作对象和特征值求解器
        SparseSymShiftSolve<double, Eigen::Upper> op(sparseATA);
        SymEigsShiftSolver<double, LARGEST_MAGN,
                           SparseSymShiftSolve<double, Eigen::Upper>>
            eigs(&op, 3, 8, -1e-10); // 求解3个特征值，使用8个Arnoldi向量

        // 6. 初始化求解器并计算
        eigs.init();
        eigs.compute();

        // 7. 获取特征值和特征向量
        VectorXd evalues = eigs.eigenvalues();
        MatrixXd V = eigs.eigenvectors();

        // 8. 构建eigen_sols并求解本质矩阵
        EigenSols eigen_sols = V.rightCols(3).transpose(); // 取最小的3个特征值对应的特征向量
        Matrix<double, 9, 10> coeff_mat;

        compute_Bcoefficients(eigen_sols, coeff_mat);

        // 9. 求解本质矩阵
        MatrixXd Evec_real;
        if (!Solve(eigen_sols, coeff_mat, Evec_real))
        {
            PO_LOG_ERR << "Failed to solve 6-point problem" << std::endl;
            return false;
        }

        // 10. 构建最终解向量
        MatrixXd Evec = MatrixXd(Evec_real.rows(), Evec_real.cols() + 3);
        Evec << Evec_real, V.rightCols(3);

        // 11. 处理结果并获取最优R,t

        // 获取计算模式选项
        bool compute_mode = GetOptionAsBool("compute_mode", true);
        bool use_opt_mode = GetOptionAsBool("use_opt_mode", false);
        bool use_median_cost = GetOptionAsBool("use_median_cost", true);

        // 获取RT_Check函数类型选择
        const std::string rt_check_type_str = GetOptionAsString("rt_check_type", "RT_CHECK2");
        RTCheckType rt_check_type = StringToRTCheckType(rt_check_type_str);

        // 根据method_options_中的identify_mode选择不同的残差计算方法
        const std::string identify_mode = GetOptionAsString("identify_mode", "PPO");

        Eval_Residual_func residual_func;
        if (boost::iequals(identify_mode, "opengv_c"))
        { // 使用 boost::iequals
            residual_func = &residual_opengv;
        }
        else if (boost::iequals(identify_mode, "PPObvcInvd"))
        { // 使用 boost::iequals
            residual_func = &residual_PPO_bvc_invd;
        }
        else if (boost::iequals(identify_mode, "PPO"))
        { // 使用 boost::iequals
            residual_func = &residual_PPO;
        }
        else if (boost::iequals(identify_mode, "PPOG"))
        { // 使用 boost::iequals
            residual_func = &residual_PPOG;
        }
        else if (boost::iequals(identify_mode, "sampson"))
        { // 使用 boost::iequals
            residual_func = &residual_sampson;
        }
        else if (boost::iequals(identify_mode, "LiGT"))
        { // 使用 boost::iequals
            residual_func = &residual_LiGT;
        }
        else if (boost::iequals(identify_mode, "LiRT"))
        { // 使用 boost::iequals
            residual_func = &residual_LiRT;
        }
        else
        {
            // 默认使用PPO方法
            residual_func = &residual_PPO;
        }

        // 根据use_opt_mode选择使用哪个版本的ProcessEvec
        Matrix<double, 3, 4> best_solution;
        if (use_opt_mode)
        {
            best_solution = process_Evec_opt(Evec, points1, points2, A, weights,
                                             residual_func, compute_mode, use_median_cost, rt_check_type);
            PO_LOG(PO_LOG_VERBOSE) << "Using optimized ProcessEvec with RT_Check type: " << RTCheckTypeToString(rt_check_type);
        }
        else
        {
            best_solution = process_Evec(Evec, points1, points2, A, weights,
                                         residual_func, compute_mode, use_median_cost, rt_check_type);
            PO_LOG(PO_LOG_VERBOSE) << "Using standard ProcessEvec with RT_Check type: " << RTCheckTypeToString(rt_check_type);
        }

        // 提取最优旋转矩阵和平移向量
        R = best_solution.block<3, 3>(0, 0);
        t = best_solution.col(3);

        // Debug输出
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "Final rotation matrix:\n"
                                  << R << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Final translation vector:\n"
                                  << t << std::endl;
        }

        // 验证结果
        if (!CheckRotation(R) || !CheckTranslation(t))
        {
            PO_LOG_ERR << "Invalid rotation or translation" << std::endl;
            return false;
        }

        return true;
    }

    bool MethodLiRP::CheckRotation(const Matrix3d &R) const
    {
        const double det = R.determinant();
        return std::abs(det - 1.0) < 1e-6;
    }

    bool MethodLiRP::CheckTranslation(const Vector3d &t) const
    {
        return t.norm() > 1e-6;
    }
    bool MethodLiRP::Solve(const EigenSols &eigen_sols,
                           const Matrix<double, 9, 10> &B,
                           MatrixXd &Evec_real,
                           bool flags_keep_real)
    {
        Matrix<double, 4, 6, ColMajor> M;
        M = B.leftCols(4).colPivHouseholderQr().solve(B.rightCols(B.cols() - 4));

        // solve x-basis polynomial sols
        Matrix<double, 6, 6, ColMajor> C1;

        C1.topRows<3>() = -M.topRows<3>();
        C1.bottomRows<3>() << 1, 0, 0, 0, 0, 0,
            0, 1, 0, 0, 0, 0,
            0, 0, 0, 1, 0, 0;

        EigenSolver<Matrix<double, 6, 6, ColMajor>> es1(C1);
        Matrix<double, 6, 6> V1 = es1.pseudoEigenvectors();

        Matrix<double, 2, 6> SOLS1 = V1.block(3, 0, 2, V1.cols()).array() / V1.row(5).replicate(2, 1).array();
        Matrix<double, 9, 6> Evec1 = eigen_sols.transpose() * (MatrixXd(SOLS1.rows() + 1, SOLS1.cols()) << SOLS1, MatrixXd::Ones(1, SOLS1.cols())).finished();

        // solve y-basis polynomial sols
        Matrix<double, 6, 6, ColMajor> C2;

        C2.topRows<3>() = -M.middleRows<3>(1);
        C2.bottomRows<3>() << 0, 1, 0, 0, 0, 0,
            0, 0, 1, 0, 0, 0,
            0, 0, 0, 0, 1, 0;

        EigenSolver<Matrix<double, 6, 6, ColMajor>> es2(C2);
        Matrix<double, 6, 6> V2 = es2.pseudoEigenvectors();

        Matrix<double, 2, 6> SOLS2 = V2.block(3, 0, 2, V2.cols()).array() / V2.row(5).replicate(2, 1).array();
        Matrix<double, 9, 6> Evec2 = eigen_sols.transpose() * (MatrixXd(SOLS2.rows() + 1, SOLS2.cols()) << SOLS2, MatrixXd::Ones(1, SOLS2.cols())).finished();

        // solve c=0 and b=1 with Det(E)=0 to solve a
        Matrix<double, 1, 4> coeff_D_mat;
        compute_Dcoefficients(eigen_sols, coeff_D_mat);

        Matrix<double, 3, 3, ColMajor> C3;

        C3.topRows<1>() = -coeff_D_mat.rightCols(3) / coeff_D_mat(0);
        C3.bottomRows<2>() << 1, 0, 0,
            0, 1, 0;

        EigenSolver<Matrix<double, 3, 3, ColMajor>> es3(C3);

        Matrix3d V3 = es3.pseudoEigenvectors();
        Vector3d a_sols;
        a_sols << V3(1, 0) / V3(2, 0), V3(1, 1) / V3(2, 1), V3(1, 2) / V3(2, 2);

        Matrix3d SOLS3;
        SOLS3 << a_sols, Vector3d::Ones(3), Vector3d::Zero(3);

        Matrix<double, 9, 3> Evec3 = eigen_sols.transpose() * SOLS3;

        //     ------------------ module of d_min ---------------------------------
        //     for testing the singularity problem of 5pts/LiRP/npt
        //     --------------------------------------------------------------------
        //    Vector3d minDists;
        //    minDists(0) = minEigenvalueDistance(C1);
        //    minDists(1) = minEigenvalueDistance(C2);
        //    minDists(2) = minEigenvalueDistance(C3);
        //    std::cout << "Minimum eigenvalue distances: " << minDists.transpose() << std::endl;
        //    std::cout << "Minimum eigenvalue distance: " << minDists.maxCoeff() << std::endl;

        //    // Specify the file path
        //    std::string file_path = "/home/<USER>/min_eigen_dist.txt";

        //    // Create an ofstream object for writing to file
        //    std::ofstream out_file(file_path);

        //    // Check if the file is open (i.e., successfully created)
        //    if (out_file.is_open()) {
        //        // Write the minimum distance to the file
        //        out_file << minDists.maxCoeff() << std::endl;
        //        // Close the file
        //        out_file.close();
        //        std::cout << "Minimum eigenvalue distance was written to " << file_path << std::endl;
        //    } else {
        //        std::cerr << "Unable to open file at " << file_path << std::endl;
        //    }
        //    -----------------------------------------------------------------------

        // Solutions
        Matrix<double, 9, 15> Evec;
        Evec << Evec1, Evec2, Evec3;

        for (int i = 0; i < Evec.cols(); ++i)
        {
            Evec.col(i).normalize();
        }

        if (flags_keep_real)
        {
            Evec_real = Evec.real();
        }
        else
        {
            vector<int> I;
            for (size_t i = 0; i < Evec.cols(); ++i)
            {
                if ((Evec.col(i).unaryExpr([](const std::complex<double> &value)
                                           { return value.imag(); })
                         .array() == 0)
                        .all())
                {
                    I.push_back(i);
                }
            }

            Evec_real.setZero(Evec.rows(), I.size());

            for (size_t i = 0; i < I.size(); ++i)
            {
                Evec_real.col(i) = Evec.col(I[i]);
            }
        }
        return true;
    }

    // -----------------------------------------------------------------------------
    // 优化版本的核心算法实现
    // -----------------------------------------------------------------------------

    bool MethodLiRP::SolveLiRPMainOptimized(
        const BearingVectors &points1,
        const BearingVectors &points2,
        Matrix3d &R,
        Vector3d &t,
        const VectorXd *weights) const
    {
        // 定义时间统计变量
        std::chrono::high_resolution_clock::time_point step_start_time, step_end_time;
        double input_check_time = 0.0;
        double ata_computation_time = 0.0;
        double eigen_solve_time = 0.0;
        double b_coeff_time = 0.0;
        double essential_solve_time = 0.0;
        double solution_build_time = 0.0;
        double final_process_time = 0.0;

        // 总体开始时间
        auto total_start_time = std::chrono::high_resolution_clock::now();

        // 1. 检查输入数据
        step_start_time = std::chrono::high_resolution_clock::now();
        const int num_matches = points1.cols();
        if (num_matches < kMinNumPoints)
        {
            PO_LOG_ERR << "Need at least " << kMinNumPoints << " point pairs, got " << num_matches << std::endl;
            return false;
        }
        step_end_time = std::chrono::high_resolution_clock::now();
        input_check_time = std::chrono::duration<double, std::milli>(step_end_time - step_start_time).count();

        // 2. 直接计算ATA矩阵，避免存储A矩阵
        step_start_time = std::chrono::high_resolution_clock::now();
        ComputeATADirectly(points1, points2, weights, ATA_buffer_);
        step_end_time = std::chrono::high_resolution_clock::now();
        ata_computation_time = std::chrono::duration<double, std::milli>(step_end_time - step_start_time).count();

        // 3. 使用优化的特征值求解
        step_start_time = std::chrono::high_resolution_clock::now();
        if (!SolveEigenOptimized(ATA_buffer_, null_space_buffer_))
        {
            PO_LOG_ERR << "Failed to compute eigenvalues" << std::endl;
            return false;
        }
        step_end_time = std::chrono::high_resolution_clock::now();
        eigen_solve_time = std::chrono::duration<double, std::milli>(step_end_time - step_start_time).count();

        // 4. 构建eigen_sols并求解本质矩阵
        step_start_time = std::chrono::high_resolution_clock::now();
        EigenSols eigen_sols = null_space_buffer_.transpose(); // 取最小的3个特征值对应的特征向量

        compute_Bcoefficients(eigen_sols, coeff_mat_buffer_);
        step_end_time = std::chrono::high_resolution_clock::now();
        b_coeff_time = std::chrono::duration<double, std::milli>(step_end_time - step_start_time).count();

        // 5. 求解本质矩阵
        step_start_time = std::chrono::high_resolution_clock::now();
        MatrixXd Evec_real;
        if (!SolveOptimized(eigen_sols, coeff_mat_buffer_, Evec_real))
        {
            PO_LOG_ERR << "Failed to solve 6-point problem" << std::endl;
            return false;
        }
        step_end_time = std::chrono::high_resolution_clock::now();
        essential_solve_time = std::chrono::duration<double, std::milli>(step_end_time - step_start_time).count();

        // 6. 构建最终解向量
        step_start_time = std::chrono::high_resolution_clock::now();
        MatrixXd Evec = MatrixXd(Evec_real.rows(), Evec_real.cols() + 3);
        Evec << Evec_real, null_space_buffer_;

        // 7. 处理结果并获取最优R,t
        const std::string identify_mode = GetOptionAsString("identify_mode", "PPO");
        bool compute_mode = GetOptionAsBool("compute_mode", true);
        bool use_opt_mode = GetOptionAsBool("use_opt_mode", false);
        bool use_median_cost = GetOptionAsBool("use_median_cost", true);

        // 获取RT_Check函数类型选择
        const std::string rt_check_type_str = GetOptionAsString("rt_check_type", "RT_CHECK2");
        RTCheckType rt_check_type = StringToRTCheckType(rt_check_type_str);

        Eval_Residual_func residual_func;
        if (boost::iequals(identify_mode, "opengv_c"))
        {
            residual_func = &residual_opengv;
        }
        else if (boost::iequals(identify_mode, "PPObvcInvd"))
        {
            residual_func = &residual_PPO_bvc_invd;
        }
        else if (boost::iequals(identify_mode, "PPO"))
        {
            residual_func = &residual_PPO;
        }
        else if (boost::iequals(identify_mode, "PPOG"))
        {
            residual_func = &residual_PPOG;
        }
        else if (boost::iequals(identify_mode, "sampson"))
        {
            residual_func = &residual_sampson;
        }
        else if (boost::iequals(identify_mode, "LiGT"))
        {
            residual_func = &residual_LiGT;
        }
        else if (boost::iequals(identify_mode, "LiRT"))
        {
            residual_func = &residual_LiRT;
        }
        else
        {
            residual_func = &residual_PPO;
        }

        // 为了保持与原版本的兼容性，这里构造一个临时的A矩阵
        // 在实际应用中，可以进一步优化这个过程
        Matrix<double, Dynamic, 9> A_temp(num_matches, 9);
        for (int i = 0; i < num_matches; ++i)
        {
            double w = (weights != nullptr) ? (*weights)(i) : 1.0;
            A_temp.row(i) = fastKroneckerProduct(points2.col(i), points1.col(i), w);
        }
        step_end_time = std::chrono::high_resolution_clock::now();
        solution_build_time = std::chrono::duration<double, std::milli>(step_end_time - step_start_time).count();

        // 7. 最终处理步骤
        step_start_time = std::chrono::high_resolution_clock::now();
        Matrix<double, 3, 4> best_solution;
        if (use_opt_mode)
        {
            best_solution = process_Evec_opt(Evec, points1, points2, A_temp, weights,
                                             residual_func, compute_mode, use_median_cost, rt_check_type);
        }
        else
        {
            best_solution = process_Evec(Evec, points1, points2, A_temp, weights,
                                         residual_func, compute_mode, use_median_cost, rt_check_type);
        }

        // 提取最优旋转矩阵和平移向量
        R = best_solution.block<3, 3>(0, 0);
        t = best_solution.col(3);

        // 验证结果
        if (!CheckRotation(R) || !CheckTranslation(t))
        {
            PO_LOG_ERR << "Invalid rotation or translation" << std::endl;
            return false;
        }
        step_end_time = std::chrono::high_resolution_clock::now();
        final_process_time = std::chrono::duration<double, std::milli>(step_end_time - step_start_time).count();

        // 计算总时间
        auto total_end_time = std::chrono::high_resolution_clock::now();
        double total_time = std::chrono::duration<double, std::milli>(total_end_time - total_start_time).count();

        // 统一显示所有时间统计信息
        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "\n=== SolveLiRPMainOptimized 详细时间统计 ===" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "1. 输入数据检查:           " << std::fixed << std::setprecision(3) << input_check_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "2. ATA矩阵直接计算:        " << std::fixed << std::setprecision(3) << ata_computation_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "3. 特征值分解求解:         " << std::fixed << std::setprecision(3) << eigen_solve_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "4. B系数矩阵计算:          " << std::fixed << std::setprecision(3) << b_coeff_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "5. 本质矩阵求解:           " << std::fixed << std::setprecision(3) << essential_solve_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "6. 解向量构建:             " << std::fixed << std::setprecision(3) << solution_build_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "7. 最终处理(RT提取):       " << std::fixed << std::setprecision(3) << final_process_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "----------------------------------------" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "总计算时间:                " << std::fixed << std::setprecision(3) << total_time << " ms" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "数据点数量:                " << num_matches << " 对" << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "使用权重:                  " << (weights ? "是" : "否") << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "残差函数类型:              " << identify_mode << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "RT_Check类型:              " << rt_check_type_str << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "使用优化模式:              " << (use_opt_mode ? "是" : "否") << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "===========================================" << std::endl;
        }
        else if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "SolveLiRPMainOptimized: 总时间 " << std::fixed << std::setprecision(3) << total_time
                                  << " ms (" << num_matches << " 点对)" << std::endl;
        }

        return true;
    }

    void MethodLiRP::ComputeATADirectly(
        const BearingVectors &points1,
        const BearingVectors &points2,
        const VectorXd *weights,
        Matrix<double, 9, 9> &ATA) const
    {
        // 直接累加计算ATA = A^T * A，避免存储整个A矩阵
        ATA.setZero();

        const int num_matches = points1.cols();
        for (int i = 0; i < num_matches; ++i)
        {
            const double w = (weights != nullptr) ? (*weights)(i) : 1.0;

            // 计算当前行的克罗内克积
            const Vector3d &p1 = points1.col(i);
            const Vector3d &p2 = points2.col(i);

            // 直接计算外积并累加到ATA中
            const double w_sq = w * w;
            for (int j = 0; j < 3; ++j)
            {
                for (int k = 0; k < 3; ++k)
                {
                    const double p2j_p1k = p2(j) * p1(k);
                    const int base_idx = j * 3 + k;

                    for (int l = 0; l < 3; ++l)
                    {
                        for (int m = 0; m < 3; ++m)
                        {
                            const double p2l_p1m = p2(l) * p1(m);
                            const int other_idx = l * 3 + m;

                            ATA(base_idx, other_idx) += w_sq * p2j_p1k * p2l_p1m;
                        }
                    }
                }
            }
        }
    }

    bool MethodLiRP::SolveEigenOptimized(
        const Matrix<double, 9, 9> &ATA,
        Matrix<double, 9, 3> &null_space) const
    {
        // 使用稠密矩阵特征值求解器，比稀疏求解器更适合9x9矩阵
        SelfAdjointEigenSolver<Matrix<double, 9, 9>> solver(ATA);

        if (solver.info() != Success)
        {
            return false;
        }

        // 获取最小的3个特征值对应的特征向量
        null_space = solver.eigenvectors().leftCols(3);

        return true;
    }

    bool MethodLiRP::SolveOptimized(
        const EigenSols &eigen_sols,
        const Matrix<double, 9, 10> &B,
        MatrixXd &Evec_real,
        bool flags_keep_real) const
    {
        // 使用预分配的缓冲区
        M_buffer_ = B.leftCols(4).colPivHouseholderQr().solve(B.rightCols(B.cols() - 4));

        // solve x-basis polynomial sols
        C1_buffer_.topRows<3>() = -M_buffer_.topRows<3>();
        C1_buffer_.bottomRows<3>() << 1, 0, 0, 0, 0, 0,
            0, 1, 0, 0, 0, 0,
            0, 0, 0, 1, 0, 0;

        EigenSolver<Matrix<double, 6, 6, ColMajor>> es1(C1_buffer_);
        Matrix<double, 6, 6> V1 = es1.pseudoEigenvectors();

        Matrix<double, 2, 6> SOLS1 = V1.block(3, 0, 2, V1.cols()).array() / V1.row(5).replicate(2, 1).array();
        Matrix<double, 9, 6> Evec1 = eigen_sols.transpose() * (MatrixXd(SOLS1.rows() + 1, SOLS1.cols()) << SOLS1, MatrixXd::Ones(1, SOLS1.cols())).finished();

        // solve y-basis polynomial sols
        C2_buffer_.topRows<3>() = -M_buffer_.middleRows<3>(1);
        C2_buffer_.bottomRows<3>() << 0, 1, 0, 0, 0, 0,
            0, 0, 1, 0, 0, 0,
            0, 0, 0, 0, 1, 0;

        EigenSolver<Matrix<double, 6, 6, ColMajor>> es2(C2_buffer_);
        Matrix<double, 6, 6> V2 = es2.pseudoEigenvectors();

        Matrix<double, 2, 6> SOLS2 = V2.block(3, 0, 2, V2.cols()).array() / V2.row(5).replicate(2, 1).array();
        Matrix<double, 9, 6> Evec2 = eigen_sols.transpose() * (MatrixXd(SOLS2.rows() + 1, SOLS2.cols()) << SOLS2, MatrixXd::Ones(1, SOLS2.cols())).finished();

        // solve c=0 and b=1 with Det(E)=0 to solve a
        compute_Dcoefficients(eigen_sols, coeff_D_mat_buffer_);

        C3_buffer_.topRows<1>() = -coeff_D_mat_buffer_.rightCols(3) / coeff_D_mat_buffer_(0);
        C3_buffer_.bottomRows<2>() << 1, 0, 0,
            0, 1, 0;

        EigenSolver<Matrix<double, 3, 3, ColMajor>> es3(C3_buffer_);

        Matrix3d V3 = es3.pseudoEigenvectors();
        Vector3d a_sols;
        a_sols << V3(1, 0) / V3(2, 0), V3(1, 1) / V3(2, 1), V3(1, 2) / V3(2, 2);

        Matrix3d SOLS3;
        SOLS3 << a_sols, Vector3d::Ones(3), Vector3d::Zero(3);

        Matrix<double, 9, 3> Evec3 = eigen_sols.transpose() * SOLS3;

        // 使用预分配的缓冲区
        Evec_buffer_ << Evec1, Evec2, Evec3;

        for (int i = 0; i < Evec_buffer_.cols(); ++i)
        {
            Evec_buffer_.col(i).normalize();
        }

        if (flags_keep_real)
        {
            Evec_real = Evec_buffer_.real();
        }
        else
        {
            std::vector<int> I;
            for (int i = 0; i < Evec_buffer_.cols(); ++i)
            {
                if ((Evec_buffer_.col(i).unaryExpr([](const std::complex<double> &value)
                                                   { return value.imag(); })
                         .array() == 0)
                        .all())
                {
                    I.push_back(i);
                }
            }

            Evec_real.setZero(Evec_buffer_.rows(), I.size());

            for (size_t i = 0; i < I.size(); ++i)
            {
                Evec_real.col(i) = Evec_buffer_.col(I[i]);
            }
        }
        return true;
    }

} // namespace PoSDK
