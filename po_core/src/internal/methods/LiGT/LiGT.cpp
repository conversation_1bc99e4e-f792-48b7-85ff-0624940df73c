// #define EIGEN_USE_BLAS
// #define EIGEN_USE_LAPACKE
// #define ARMA_USE_BLAS
// #define ARMA_USE_NEWARP
// #define ARMA_USE_LAPACK
// #define EIGEN_USE_MKL_ALL
#define EIGEN_NO_DEBUG

#include <math.h>
#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include <type_traits>
#include <limits>
#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>
#include <math.h>
#include <unordered_map>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <chrono>
#include <set>
#include <Eigen/Core>

#include "Spectra/SymEigsShiftSolver.h"
// <Spectra/MatOp/DenseSymShiftSolve.h> is implicitly included
#include "Spectra/MatOp/SparseSymShiftSolve.h"

#include "LiGT.hpp"
#define IsNonZero(d) (fabs(d) > 1e-15)
#define T_size 3 * (est_view_size)
#define LIGT_MAX(a, b) (a) < (b) ? (b) : (a)

//------------------- namespace ----------------------
using namespace std;
using namespace chrono;
using namespace Eigen;
using namespace Spectra;

//------------------- global/static vars --------------------

double time_use = 0;

//------------------- function declaration ------------------

void SetupOrientationFromPAResults(
    const std::string &filename,
    GlobalRotations &global_R)
{
    auto start_time = steady_clock::now();
    std::cout << "##start load global R from PA results file<<<" << endl;
    fstream cq_in;
    cq_in.open(filename, std::ios::in);
    if (!cq_in.is_open())
    {
        cout << "file cannot open" << endl;
        return;
    }
    cout << "file name:" << filename << endl;
    int cq_i = 0;
    Size num_view = 0;
    cq_in >> num_view;
    global_R.resize(num_view);
    Vector3d tmp_trans;
    for (int cq_i = 0; cq_i < num_view; cq_i++)
    {
        Eigen::Matrix3d tmp_R;
        cq_in >> tmp_R(0, 0) >> tmp_R(1, 0) >> tmp_R(2, 0);
        cq_in >> tmp_R(0, 1) >> tmp_R(1, 1) >> tmp_R(2, 1);
        cq_in >> tmp_R(0, 2) >> tmp_R(1, 2) >> tmp_R(2, 2);
        cq_in >> tmp_trans(0) >> tmp_trans(1) >> tmp_trans(2);

        // global_R.emplace_back(tmp_R);
        global_R[cq_i] = (tmp_R);
    }
    cq_in.close();
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time for loading global rotation: "
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void build_base_views(const Track &ptr_track,
                      const GlobalRotations &global_R,
                      double &max_s,
                      unsigned int &max_id_l,
                      unsigned int &max_id_r)
{
    max_s = 0;
    max_id_l = 0;
    max_id_r = 0;
    Vector3d Rx;
    Vector3d xRx;
    const unsigned int &track_length = ptr_track.size();
    for (unsigned int j = 0; j < track_length - 1; ++j)
    {
        const ViewId &left_view = ptr_track[j].view_id;

        // 检查观测点是否有效
        if (!ptr_track[j].is_used)
            continue;

        const Vector3d &x_l = ptr_track[j].GetHomoCoord();
        Vector3d Rl_xl = global_R[left_view].transpose() * x_l;
        for (unsigned int k = j + 1; k < track_length; ++k)
        {

            // 检查观测点是否有效
            if (!ptr_track[k].is_used)
                continue;

            const ViewId &right_view = ptr_track[k].view_id;
            const Matrix3d &R_r = global_R[right_view];
            Rx = R_r * Rl_xl;
            xRx = ptr_track[k].GetHomoCoord().cross(Rx);
            double s = xRx.norm();
            if (s > max_s)
            {
                max_s = s;
                max_id_l = j;
                max_id_r = k;
            }
        }
    }
}

inline void build_base_views_dist(const Track &ptr_track,
                                  const GlobalRotations &global_R,
                                  const GlobalTranslations &global_t,
                                  double &min_dist,
                                  unsigned int &id_l,
                                  unsigned int &id_r)
{
    unsigned int j, k;
    Vector3d Rx;
    Vector3d xRx;
    const unsigned int &track_length = ptr_track.size();
    double lambda, s, predict_s, dist_s;
    Vector3d predict_x_r;
    Vector3d t_lr;
    min_dist = 1e10;
    for (j = 0; j < track_length; ++j)
    {
        const ViewId &left_view = ptr_track[j].view_id;
        const Vector3d &x_l = ptr_track[j].GetHomoCoord();
        const Matrix3d &R_l = global_R[left_view];
        const Vector3d Rl_xl = R_l.transpose() * x_l;
        for (k = 0; k < track_length; ++k)
        {
            if (j == k)
                continue;
            const ViewId &right_view = ptr_track[k].view_id;
            const Matrix3d &R_r = global_R[right_view];
            Rx = R_r * Rl_xl;
            xRx = ptr_track[k].GetHomoCoord().cross(Rx);
            s = xRx.norm();

            // t_lr = t_r - R_r * ( R_l.transpose() * t_l );
            const Vector3d &t_l = global_t[left_view];
            const Vector3d &t_r = global_t[right_view];

            t_lr = t_r - R_r * (R_l.transpose() * t_l);

            // calculate s by reprojection coordinates
            lambda = ptr_track[k].GetHomoCoord().cross(t_lr).norm();
            predict_x_r = lambda * Rx + s * t_lr;
            predict_x_r = predict_x_r / predict_x_r(2);

            //            cout<<"predict_x_r: "<<predict_x_r.transpose()
            //               <<", x_r"<<ptr_track[k].GetHomoCoord().transpose()<<endl;
            predict_s = 1 / predict_x_r.cross(Rx).norm();

            dist_s = abs(predict_s - 1.0 / s);
            if (dist_s < min_dist)
            {
                min_dist = s;
                if (right_view == 0)
                {
                    id_l = k;
                    id_r = j;
                }
                else
                {
                    id_l = j;
                    id_r = k;
                }
            }
        }
    }
}

inline double build_base_views_residual(const Track &ptr_track,
                                        const GlobalRotations &global_R,
                                        const GlobalTranslations &global_t,
                                        double &max_s,
                                        unsigned int &id_l,
                                        unsigned int &id_r)
{
    unsigned int j, k;
    Vector3d Rx;
    Vector3d xRx;
    const unsigned int &track_length = ptr_track.size();
    double lambda, s, predict_s, dist_s;
    Vector3d predict_x;
    Vector3d t_lr, t_lc;

    double max_dist = 0;
    double min_dist = 1e10;
    max_s = 0;
    ViewId right_base_view, right_base_view_id;
    for (j = 0; j < track_length; ++j)
    {
        const ViewId &left_view = ptr_track[j].view_id;
        const Vector3d &x_l = ptr_track[j].GetHomoCoord();
        const Matrix3d &R_l = global_R[left_view];
        const Vector3d &t_l = global_t[left_view];

        const Vector3d Rl_xl = R_l.transpose() * x_l;

        double tmp_max_s = 0;
        for (k = 0; k < track_length; ++k)
        {
            if (j == k)
                continue;
            const ViewId &right_view = ptr_track[k].view_id;
            const Matrix3d &R_r = global_R[right_view];
            Rx = R_r * Rl_xl;
            xRx = ptr_track[k].GetHomoCoord().cross(Rx);
            s = xRx.norm();

            if (s > tmp_max_s)
            {
                tmp_max_s = s;
                right_base_view_id = k;
                right_base_view = right_view;
            }
        }

        const Vector3d &x_r = ptr_track[right_base_view_id].GetHomoCoord();

        const Matrix3d &R_r = global_R[right_base_view];
        const Vector3d &t_r = global_t[right_base_view];
        t_lr = t_r - R_r * (R_l.transpose() * t_l);
        lambda = t_lr.cross(x_r).norm();

        for (k = 0; k < track_length; ++k)
        {
            if (j == k)
                continue;
            const ViewId &current_view = ptr_track[k].view_id;
            const Matrix3d &R_c = global_R[current_view];
            Rx = R_c * Rl_xl;

            // t_lr = t_r - R_r * ( R_l.transpose() * t_l );
            const Vector3d &t_c = global_t[current_view];
            t_lc = t_c - R_c * (R_l.transpose() * t_l);

            // calculate s by reprojection coordinates
            predict_x = lambda * Rx + tmp_max_s * t_lc;
            predict_x = predict_x / predict_x(2);

            //            cout<<"predict_x: "<<predict_x.transpose()
            //               <<", x_c"<<ptr_track[k].GetHomoCoord().transpose()<<endl;

            dist_s = (predict_x - ptr_track[k].GetHomoCoord()).norm();
            if (dist_s < min_dist)
            //            if(dist_s > max_dist)
            {
                //                max_dist=dist_s;
                min_dist = dist_s;
                max_s = tmp_max_s;
                id_l = j;
                id_r = right_base_view_id;
            }
        }
    }
    //    return max_dist;
    return min_dist;
}

void ConstructF(Tracks &tracks,
                GlobalRotations &global_R,
                EstInfo &est_info,
                double *&ftf,
                RowVectorXd &H)
{
    auto start_time = steady_clock::now();
    Size est_view_size = est_info.GetNumEstimatedViews();
    Size est_t_length = 3 * est_view_size - 3;

    H.setZero(est_t_length);
    ftf = new double[est_t_length * est_t_length]();

    vector<ViewId> origin2est_id = est_info.origin2est_id;

    Matrix3d cross_xr, cross_xc;

    double s;
    Matrix3d R_lr, R_lc;
    Vector3d Rlc_xl;
    Vector3d m3_lr, m3_lc;
    RowVector3d tmp_A;
    Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;
    Matrix3d FtF_ll, FtF_rr, FtF_cc;
    Matrix3d FtF_coview;
    unsigned int track_len;
    unsigned int i = 0;
    unsigned int j = 0;
    unsigned int k = 0;
    PtsId pts_len = tracks.size();
    Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
    RowVector3d tmp_A_R_lr;
    int id_r, id_l, id_c;

    unsigned int used_pts = 0;

    unsigned int max_id_l = 0;
    unsigned int max_id_r = 0;
    double max_s = 0;

    Vector3d Rx;
    Vector3d xRx;

    for (i = 0; i < pts_len; ++i)
    {
        if (!tracks[i].is_used)
            continue;
        used_pts++;
        Track &ptr_track = tracks[i].track;
        const unsigned int &track_length = ptr_track.size();
        max_id_l = 0;
        max_id_r = 0;
        max_s = 0;
        if (track_length < 2)
        {
            continue;
        }

        build_base_views(ptr_track, global_R, max_s, max_id_l, max_id_r);

        // 确保有有效的基准视图
        if (max_s <= 1e-10)
        {
            continue; // 如果没有找到有效的基准视图对，跳过此轨迹
        }

        // 确保基准视图是有效的
        if (!ptr_track[max_id_l].is_used || !ptr_track[max_id_r].is_used)
        {
            continue;
        }

        ViewId &left_base = max_id_l;
        ViewId &right_base = max_id_r;

        ViewId &lbase_view = ptr_track[left_base].view_id;
        ViewId &rbase_view = ptr_track[right_base].view_id;

        ViewId &new_l_view = origin2est_id[lbase_view];
        ViewId &new_r_view = origin2est_id[rbase_view];

        double &base_s = max_s;
        Matrix3d &R_l_base = global_R[lbase_view];
        Matrix3d &R_r_base = global_R[rbase_view];
        R_lr = R_r_base * R_l_base.transpose();
        const Vector3d &x_l = ptr_track[left_base].GetHomoCoord();
        const Vector3d &x_r = ptr_track[right_base].GetHomoCoord();
        cross_xr << 0, -x_r(2), x_r(1),
            x_r(2), 0, -x_r(0),
            -x_r(1), x_r(0), 0;
        R_lr_x_l = R_lr * x_l;
        m3_lr = cross_xr * R_lr_x_l;

        tmp_A = m3_lr.transpose() * cross_xr;
        tmp_A = -tmp_A;
        tmp_A_R_lr = -tmp_A * R_lr;
        id_r = 3 * new_r_view - 3;
        id_l = 3 * new_l_view - 3;

        if (id_l >= 0)
        {
            H.middleCols(id_l, 3) += tmp_A_R_lr;
        }
        else
        {
            // int aaa=0;
        }
        if (id_r >= 0)
        {
            H.middleCols(id_r, 3) += tmp_A;
        }
        else
        {
            // int aaa=0;
        }

        track_len = ptr_track.size();
        for (j = 0; j < track_len; ++j)
        {
            // 检查观测点是否有效
            if (!ptr_track[j].is_used)
                continue;

            ViewId &current_view = ptr_track[j].view_id;
            ViewId &new_c_view = origin2est_id[current_view];
            if (current_view == lbase_view)
                continue;
            Matrix3d &R_c = global_R[current_view];
            R_lc = R_c * R_l_base.transpose();
            const Vector3d &x_c = ptr_track[j].GetHomoCoord();
            Rlc_xl = R_lc * x_l;
            m3_lc = x_c.cross(Rlc_xl);
            s = m3_lc.norm();
            cross_xc << 0, -x_c(2), x_c(1),
                x_c(2), 0, -x_c(0),
                -x_c(1), x_c(0), 0;
            R_lc_x_l = R_lc * x_l;
            xc_R_lc_x_l = cross_xc * R_lc_x_l;

            tmp_F_rbase = xc_R_lc_x_l * tmp_A;

            tmp_F_cur = cross_xc * base_s * base_s;
            tmp_F_lbase = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);
            FtF_ll = tmp_F_lbase.transpose() * tmp_F_lbase;
            FtF_rr = tmp_F_rbase.transpose() * tmp_F_rbase;
            FtF_cc = tmp_F_cur.transpose() * tmp_F_cur;

            id_c = 3 * new_c_view - 3;

            if (id_l >= 0)
            {
                assignment_cq(ftf, id_l, id_l, FtF_ll);
            }

            if (id_r >= 0)
            {
                assignment_cq(ftf, id_r, id_r, FtF_rr);
            }

            if (id_c >= 0)
            {
                assignment_cq(ftf, id_c, id_c, FtF_cc);
            }

            if (id_l >= 0 && id_r >= 0)
            {
                if (new_l_view < new_r_view)
                {
                    FtF_coview = tmp_F_lbase.transpose() * tmp_F_rbase;
                    assignment_cq(ftf, id_l, id_r, FtF_coview);
                }
                else
                {
                    FtF_coview = tmp_F_rbase.transpose() * tmp_F_lbase;
                    assignment_cq(ftf, id_r, id_l, FtF_coview);
                }
            }

            if (id_l >= 0 && id_c >= 0)
            {
                if (new_l_view < new_c_view)
                {
                    FtF_coview = tmp_F_lbase.transpose() * tmp_F_cur;
                    assignment_cq(ftf, id_l, id_c, FtF_coview);
                }
                else
                {
                    FtF_coview = tmp_F_cur.transpose() * tmp_F_lbase;
                    assignment_cq(ftf, id_c, id_l, FtF_coview);
                }
            }

            if (id_r >= 0 && id_c >= 0)
            {
                if (new_r_view < new_c_view)
                {
                    FtF_coview = tmp_F_rbase.transpose() * tmp_F_cur;
                    assignment_cq(ftf, id_r, id_c, FtF_coview);
                }
                else if (new_r_view == new_c_view)
                {
                    FtF_coview = tmp_F_cur.transpose() * tmp_F_rbase;
                    assignment_cq(ftf, id_c, id_r, 2 * FtF_coview);
                }
                else
                {
                    FtF_coview = tmp_F_cur.transpose() * tmp_F_rbase;
                    assignment_cq(ftf, id_c, id_r, FtF_coview);
                }
            }
        }
    }

    //    for(int j=0;j<est_view_size-1;j++){
    //        for(int i=j+1;i<est_view_size;i++){
    //            ftf[(3*i+0)*est_t_length+3*j]+=ftf[(3*j+0)*est_t_length+3*i+0];
    //            ftf[(3*i+1)*est_t_length+3*j]+=ftf[(3*j+0)*est_t_length+3*i+1];
    //            ftf[(3*i+2)*est_t_length+3*j]+=ftf[(3*j+0)*est_t_length+3*i+2];
    //            ftf[(3*i+0)*est_t_length+3*j+1]+=ftf[(3*j+1)*est_t_length+3*i+0];
    //            ftf[(3*i+1)*est_t_length+3*j+1]+=ftf[(3*j+1)*est_t_length+3*i+1];
    //            ftf[(3*i+2)*est_t_length+3*j+1]+=ftf[(3*j+1)*est_t_length+3*i+2];
    //            ftf[(3*i+0)*est_t_length+3*j+2]+=ftf[(3*j+2)*est_t_length+3*i+0];
    //            ftf[(3*i+1)*est_t_length+3*j+2]+=ftf[(3*j+2)*est_t_length+3*i+1];
    //            ftf[(3*i+2)*est_t_length+3*j+2]+=ftf[(3*j+2)*est_t_length+3*i+2];
    //        }
    //    }

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "时间统计 - 构建L矩阵: "
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "秒" << endl;
}

void Solution_Est2Origin(
    EstInfo &est_info,
    VectorXd &solution,
    GlobalTranslations &translation)
{
    auto start_time = steady_clock::now();

    Size num_view = est_info.GetNumEstimatedViews();
    const auto &est2origin = est_info.origin2est_id;

    // est part
    translation.resize(num_view);
    for (int i = 0; i < num_view; i++)
    {
        translation[est2origin[i]] = solution.middleRows<3>(3 * i);
    }
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time for recovering solution: "
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void Obtain_Solution_Eigen(Eigen::SparseMatrix<double> &est_FtF, RowVectorXd &H, VectorXd &t_solution)
{
    auto start_time = steady_clock::now();
    SparseSymShiftSolve<double> op(est_FtF);
    // Construct eigen solver object with shift 0
    // This will find eigenvalues that are closest to 0
    SymEigsShiftSolver<double, LARGEST_MAGN,
                       SparseSymShiftSolve<double>>
        eigs(&op, 1, 8, -1e-8);

    eigs.init();
    eigs.compute();

    Eigen::VectorXd evalues = eigs.eigenvalues();
    cout << "set val precision = 6" << endl;
    cout << "eigenvalue:" << std::fixed << std::setprecision(6) << evalues << endl;

    unsigned int remain_size = est_FtF.cols();

    t_solution.setZero(remain_size + 3);
    t_solution.bottomRows(remain_size) = eigs.eigenvectors(1);

    double ratio = H * t_solution.bottomRows(remain_size);

    if (ratio < 0)
    {
        t_solution = -t_solution;
    }

    auto end_time = steady_clock::now();
    double dr_ms = std::chrono::duration<double>(end_time - start_time).count();
    cout << "time for Eigen (sparse)Decomposition: "
         << dr_ms
         << "s" << endl;
}
void ConstructF_t(Tracks &tracks,
                  GlobalRotations &global_R,
                  GlobalTranslations &global_t,
                  EstInfo &est_info,
                  double *&ftf,
                  RowVectorXd &H)
{
    auto start_time = steady_clock::now();
    Size est_view_size = est_info.GetNumEstimatedViews();
    Size est_t_length = 3 * est_view_size - 3;

    H.setZero(est_t_length);
    ftf = new double[est_t_length * est_t_length]();

    vector<ViewId> origin2est_id = est_info.origin2est_id;

    Matrix3d cross_xr, cross_xc;

    double s;
    Matrix3d R_lr, R_lc;
    Vector3d Rlc_xl;
    Vector3d m3_lr, m3_lc;
    RowVector3d tmp_A;
    Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;
    Matrix3d FtF_ll, FtF_rr, FtF_cc;
    Matrix3d FtF_coview;
    unsigned int track_len;
    unsigned int i = 0;
    unsigned int j = 0;
    unsigned int k = 0;
    PtsId pts_len = tracks.size();
    Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
    RowVector3d tmp_A_R_lr;
    int id_r, id_l, id_c;

    unsigned int used_pts = 0;

    unsigned int max_id_l = 0;
    unsigned int max_id_r = 0;
    double max_s = 0;

    Vector3d Rx;
    Vector3d xRx;

    double error_threshold = 0.5; // pixel
    Eigen::VectorXd reprojection_error(pts_len);
    reprojection_error.setZero();
    for (i = 0; i < pts_len; ++i)
    {
        if (!tracks[i].is_used)
            continue;
        used_pts++;
        Track &ptr_track = tracks[i].track;
        const unsigned int &track_length = ptr_track.size();
        max_id_l = 0;
        max_id_r = 0;
        max_s = 0;
        reprojection_error(i) = 2759.48 * build_base_views_residual(ptr_track, global_R, global_t, max_s, max_id_l, max_id_r);
        //        build_base_views_dist(ptr_track,global_R,global_t,max_s,max_id_l,max_id_r);

        if (reprojection_error(i) > error_threshold)
        {
            cout << "erase outlier" << endl;
            continue;
        }
        build_base_views(ptr_track, global_R, max_s, max_id_l, max_id_r);
        //       build_base_views_residual(ptr_track, global_R, global_t,max_s, max_id_l, max_id_r);
        //      cout<<"max_s: "<<max_s<<" ,max_id_l:"<<max_id_l<<" ,max_id_r:"<<max_id_r<<endl;

        ViewId &left_base = max_id_l;
        ViewId &right_base = max_id_r;

        ViewId &lbase_view = ptr_track[left_base].view_id;
        ViewId &rbase_view = ptr_track[right_base].view_id;

        ViewId &new_l_view = origin2est_id[lbase_view];
        ViewId &new_r_view = origin2est_id[rbase_view];

        double &base_s = max_s;
        Matrix3d &R_l_base = global_R[lbase_view];
        Matrix3d &R_r_base = global_R[rbase_view];
        R_lr = R_r_base * R_l_base.transpose();
        const Vector3d &x_l = ptr_track[left_base].GetHomoCoord();
        const Vector3d &x_r = ptr_track[right_base].GetHomoCoord();
        cross_xr << 0, -x_r(2), x_r(1),
            x_r(2), 0, -x_r(0),
            -x_r(1), x_r(0), 0;
        R_lr_x_l = R_lr * x_l;
        m3_lr = cross_xr * R_lr_x_l;

        tmp_A = m3_lr.transpose() * cross_xr;
        tmp_A = -tmp_A;
        tmp_A_R_lr = -tmp_A * R_lr;
        id_r = 3 * new_r_view - 3;
        id_l = 3 * new_l_view - 3;

        if (id_l >= 0)
        {
            H.middleCols(id_l, 3) += tmp_A_R_lr;
        }
        else
        {
            // int aaa=0;
        }
        if (id_r >= 0)
        {
            H.middleCols(id_r, 3) += tmp_A;
        }
        else
        {
            // int aaa=0;
        }

        track_len = ptr_track.size();
        for (j = 0; j < track_len; ++j)
        {
            ViewId &current_view = ptr_track[j].view_id;
            ViewId &new_c_view = origin2est_id[current_view];
            if (current_view == lbase_view)
                continue;
            Matrix3d &R_c = global_R[current_view];
            R_lc = R_c * R_l_base.transpose();
            const Vector3d &x_c = ptr_track[j].GetHomoCoord();
            Rlc_xl = R_lc * x_l;
            m3_lc = x_c.cross(Rlc_xl);
            s = m3_lc.norm();
            cross_xc << 0, -x_c(2), x_c(1),
                x_c(2), 0, -x_c(0),
                -x_c(1), x_c(0), 0;
            R_lc_x_l = R_lc * x_l;
            xc_R_lc_x_l = cross_xc * R_lc_x_l;

            tmp_F_rbase = xc_R_lc_x_l * tmp_A;

            tmp_F_cur = cross_xc * base_s * base_s;
            tmp_F_lbase = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);
            FtF_ll = tmp_F_lbase.transpose() * tmp_F_lbase;
            FtF_rr = tmp_F_rbase.transpose() * tmp_F_rbase;
            FtF_cc = tmp_F_cur.transpose() * tmp_F_cur;

            id_c = 3 * new_c_view - 3;

            if (id_l >= 0)
            {
                assignment_cq(ftf, id_l, id_l, FtF_ll);
            }

            if (id_r >= 0)
            {
                assignment_cq(ftf, id_r, id_r, FtF_rr);
            }

            if (id_c >= 0)
            {
                assignment_cq(ftf, id_c, id_c, FtF_cc);
            }

            if (id_l >= 0 && id_r >= 0)
            {
                if (new_l_view < new_r_view)
                {
                    FtF_coview = tmp_F_lbase.transpose() * tmp_F_rbase;
                    assignment_cq(ftf, id_l, id_r, FtF_coview);
                }
                else
                {
                    FtF_coview = tmp_F_rbase.transpose() * tmp_F_lbase;
                    assignment_cq(ftf, id_r, id_l, FtF_coview);
                }
            }

            if (id_l >= 0 && id_c >= 0)
            {
                if (new_l_view < new_c_view)
                {
                    FtF_coview = tmp_F_lbase.transpose() * tmp_F_cur;
                    assignment_cq(ftf, id_l, id_c, FtF_coview);
                }
                else
                {
                    FtF_coview = tmp_F_cur.transpose() * tmp_F_lbase;
                    assignment_cq(ftf, id_c, id_l, FtF_coview);
                }
            }

            if (id_r >= 0 && id_c >= 0)
            {
                if (new_r_view < new_c_view)
                {
                    FtF_coview = tmp_F_rbase.transpose() * tmp_F_cur;
                    assignment_cq(ftf, id_r, id_c, FtF_coview);
                }
                else if (new_r_view == new_c_view)
                {
                    FtF_coview = tmp_F_cur.transpose() * tmp_F_rbase;
                    assignment_cq(ftf, id_c, id_r, 2 * FtF_coview);
                }
                else
                {
                    FtF_coview = tmp_F_cur.transpose() * tmp_F_rbase;
                    assignment_cq(ftf, id_c, id_r, FtF_coview);
                }
            }
        }
    }

    //======================= Debug: output error to check ==========================

    fstream output("/home/<USER>/reprojection_error.txt", std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        cout << "output file cannot create, please check path" << endl;
        return;
    }
    output << reprojection_error << endl;
    output.close();

    //=============================================

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time2 for constructing L matrix: "
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void LiGT_algorithm(
    Tracks &tracks,
    EstInfo &est_info,
    GlobalRotations &global_R,
    GlobalTranslations &translation)
{
    auto start_time = steady_clock::now();
    Size num_view = est_info.GetNumEstimatedViews();

    if (est_info.GetNumEstimatedViews() > 1)
    {
        int est_t_length = 3 * num_view;
        int remain_size = est_t_length - 3;

        double *FtF;
        RowVectorXd H;
        ConstructF(tracks, global_R, est_info, FtF, H);

        //======================= transform FtF into est_FtF by fixed id ==============
        Map<MatrixXd> FtF_map(FtF, remain_size, remain_size);
        Eigen::SparseMatrix<double> sp_FtF = FtF_map.sparseView();

        VectorXd t_solution;
        cout << endl
             << "************  Eigen Time  **************" << endl;

        Obtain_Solution_Eigen(sp_FtF, H, t_solution);
        Solution_Est2Origin(est_info, t_solution, translation);
    }
    else
    {
        cout << "only one image" << endl;
        VectorXd t_solution;
        t_solution.setZero(num_view);
    }

    auto end_time = steady_clock::now();
    double dr_ms = std::chrono::duration<double>(end_time - start_time).count();
    cout << "time for LiGT algorithm: "
         << dr_ms << "s" << endl;
    cout << "*******************************************" << endl;
    time_use = dr_ms;
}
