#ifndef _LIGT_ALGORITHM_
#define _LIGT_ALGORITHM_

#define EIGEN_NO_DEBUG

#include <math.h>
#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include <type_traits>
#include <limits>
#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>
#include <math.h>
#include <unordered_map>

#include <Eigen/Core>
#include "types.hpp"
#define IsNonZero(d) (fabs(d) > 1e-15)
#define T_size 3 * (est_view_size)
// #define max(a, b) (a) < (b) ? (b): (a)
#define LIGT_MAX(a, b) (a) < (b) ? (b) : (a)

using namespace std;
using namespace Eigen;
using namespace PoSDK::types;

#define assignment_cq(ftf, id_l, id_r, source_mat)                 \
    ftf[(id_l + 0) * est_t_length + id_r] += source_mat(0, 0);     \
    ftf[(id_l + 1) * est_t_length + id_r] += source_mat(1, 0);     \
    ftf[(id_l + 2) * est_t_length + id_r] += source_mat(2, 0);     \
    ftf[(id_l + 0) * est_t_length + id_r + 1] += source_mat(0, 1); \
    ftf[(id_l + 1) * est_t_length + id_r + 1] += source_mat(1, 1); \
    ftf[(id_l + 2) * est_t_length + id_r + 1] += source_mat(2, 1); \
    ftf[(id_l + 0) * est_t_length + id_r + 2] += source_mat(0, 2); \
    ftf[(id_l + 1) * est_t_length + id_r + 2] += source_mat(1, 2); \
    ftf[(id_l + 2) * est_t_length + id_r + 2] += source_mat(2, 2);

void LiGT_algorithm(Tracks &tracks,
                    EstInfo &est_info,
                    GlobalRotations &global_R,
                    GlobalTranslations &translation);

void ConstructF(Tracks &tracks,
                GlobalRotations &global_R,
                EstInfo &est_info,
                double *&FtF,
                RowVectorXd &H);

// reprocess version

void ConstructF_t(Tracks &tracks,
                  GlobalRotations &global_R,
                  GlobalTranslations &global_t,
                  EstInfo &est_info,
                  double *&ftf,
                  RowVectorXd &H);

void Solution_Est2Origin(EstInfo &est_info,
                         VectorXd &solution,
                         GlobalTranslations &translation);

void Obtain_Solution_Eigen(Eigen::SparseMatrix<double> &est_FtF,
                           RowVectorXd &H,
                           VectorXd &t_solution);

void build_base_views(const Track &ptr_track,
                      const GlobalRotations &global_R,
                      double &max_s,
                      unsigned int &max_id_l,
                      unsigned int &max_id_r);

inline void build_base_views_dist(const Track &ptr_track,
                                  const GlobalRotations &global_R,
                                  const GlobalTranslations &global_t,
                                  double &min_dist,
                                  unsigned int &id_l,
                                  unsigned int &id_r);

inline double build_base_views_residual(const Track &ptr_track,
                                        const GlobalRotations &global_R,
                                        const GlobalTranslations &global_t,
                                        double &max_s,
                                        unsigned int &id_l,
                                        unsigned int &id_r);

#endif
