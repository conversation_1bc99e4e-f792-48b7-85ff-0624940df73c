/**
 * @file method_LiRP.cpp
 * @brief LiRP相对位姿估计器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "method_LiRPFast.hpp"

#include <algorithm>                  // 添加算法库用于 std::max
#include <boost/algorithm/string.hpp> // 添加 Boost 字符串算法库
#include <chrono>                     // 时间统计
#include <iomanip>                    // 格式化输出
#include <limits>                     // 数值限制
#include "Spectra/SymEigsShiftSolver.h"
#include "Spectra/MatOp/SparseSymShiftSolve.h"
#include "relative_residuals.hpp"
#include "relative_pose.hpp"
#include "compute_coefficients.hpp"

namespace PoSDK
{
    using namespace Spectra;

    // -----------------------------------------------------------------------------
    // 主函数
    // -----------------------------------------------------------------------------
    // 内联快速克罗内克积计算（专用于3x1向量的转置）
    inline Eigen::Matrix<double, 1, 9> fastKroneckerProduct(const Eigen::Vector3d &a, const Eigen::Vector3d &b, double weight = 1.0)
    {
        Eigen::Matrix<double, 1, 9> result;
        const double a0w = a(0) * weight;
        const double a1w = a(1) * weight;
        const double a2w = a(2) * weight;

        result(0) = a0w * b(0);
        result(1) = a0w * b(1);
        result(2) = a0w * b(2);
        result(3) = a1w * b(0);
        result(4) = a1w * b(1);
        result(5) = a1w * b(2);
        result(6) = a2w * b(0);
        result(7) = a2w * b(1);
        result(8) = a2w * b(2);

        return result;
    }

    MethodLiRPFast::MethodLiRPFast()
    {
        // 设置需要的输入数据包
        required_package_["data_sample"] = nullptr; // DataSample<BearingPairs>

        // 初始化默认配置路径
        InitializeDefaultConfigPath();

        // 预分配固定大小的矩阵缓冲区
        ATA_buffer_.setZero();

        null_space_buffer_.setZero();
        C1_buffer_.setZero();
        C2_buffer_.setZero();
        C3_buffer_.setZero();
        M_buffer_.setZero();
        Evec_buffer_.setZero();
        coeff_mat_buffer_.setZero();
        coeff_D_mat_buffer_.setZero();

        // 初始化日志目录 - 将在MethodPreset中根据log_level_决定是否真的创建
        // InitializeLogDir(); // 如果需要，可以在基类或此处无条件调用，或基于log_level_
    }

    bool MethodLiRPFast::ConvertBearingPairsToBearingVectors(
        BearingVectors &points1,
        BearingVectors &points2)
    {

        // 获取输入数据
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (!sample_ptr)
        {
            PO_LOG_ERR << "Invalid data sample" << std::endl;
            return false;
        }

        if (sample_ptr->empty())
        {
            PO_LOG_ERR << "Empty bearing pairs" << std::endl;
            return false;
        }

        const size_t num_points = sample_ptr->size();

        // 调整输出向量的大小
        points1.resize(3, num_points); // 修正为正确的维度 (3 x N)
        points2.resize(3, num_points);

        // 使用DataSample的迭代器特性遍历数据
        size_t i = 0;
        for (const auto &bearing_pair : *sample_ptr)
        {
            points1.col(i) = bearing_pair.head<3>(); // 使用col()访问列
            points2.col(i) = bearing_pair.tail<3>();
            ++i;
        }

        return true;
    }

    DataPtr MethodLiRPFast::Run()
    {
        // 0. 配置算法参数
        ConfigureAlgorithmParams();

        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "Run called. Displaying config if log_level is appropriate." << std::endl;
            DisplayConfigInfo();
        }

        // 1. 更新DataSample状态 - 对于非鲁棒LiRP，所有观测都是内点
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (sample_ptr && !sample_ptr->empty())
        {
            // 创建内点索引向量（所有点都是内点）
            auto inliers_ptr = std::make_shared<std::vector<size_t>>();
            inliers_ptr->reserve(sample_ptr->size());
            for (size_t i = 0; i < sample_ptr->size(); ++i)
            {
                inliers_ptr->push_back(i);
            }

            // 设置到DataSample中
            sample_ptr->SetBestInliers(inliers_ptr);

            PO_LOG(PO_LOG_VERBOSE) << "Updated DataSample with " << inliers_ptr->size()
                                   << " inliers (all points for non-robust LiRP)" << std::endl;
        }

        // 2. 从method_options_获取视图对信息
        ViewPair view_pair(
            GetOptionAsIndexT("view_i", 0), // 源视图ID
            GetOptionAsIndexT("view_j", 1)  // 目标视图ID
        );

        // 3. 转换bearing pairs到bearing vectors
        BearingVectors points1, points2;
        if (!ConvertBearingPairsToBearingVectors(points1, points2))
        {
            PO_LOG_ERR << "Failed to convert bearing pairs" << std::endl;
            return nullptr;
        }

        // 4. 检查是否有权重信息
        VectorXd *weights_ptr = nullptr;
        VectorXd weights;

        // 检查先验信息中是否包含权重
        if (!prior_info_.empty() && prior_info_.find("weights") != prior_info_.end())
        {
            auto weights_data = std::dynamic_pointer_cast<DataMap<VectorXd>>(prior_info_["weights"]);
            if (weights_data && weights_data->GetMapPtr())
            {
                // 获取权重向量的指针
                weights = *(weights_data->GetMapPtr());
                weights_ptr = &weights;

                PO_LOG(PO_LOG_VERBOSE) << "Using weights from prior_info_" << std::endl;
            }
        }

        // 5. 估计相对位姿
        Matrix3d R;
        Vector3d t;

        // 记录核心计算开始时间
        auto core_start_time = std::chrono::high_resolution_clock::now();

        if (!SolveLiRPMainOptimized(points1, points2, R, t, weights_ptr))
        {
            PO_LOG_ERR << "Failed to estimate relative pose" << std::endl;
            return nullptr;
        }

        // 记录核心计算结束时间
        auto core_end_time = std::chrono::high_resolution_clock::now();
        double core_time_ms = std::chrono::duration<double, std::milli>(core_end_time - core_start_time).count();

        // 设置核心计算时间
        SetCoreTime(core_time_ms);

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "LiRP core computation time: " << core_time_ms << " ms" << std::endl;
        }

        // 6. 创建并返回结果
        RelativePose relative_pose(
            view_pair.first,  // 源视图ID
            view_pair.second, // 目标视图ID
            R,                // 相对旋转
            t,                // 相对平移
            1.0f              // 默认权重
        );

        return std::make_shared<DataMap<RelativePose>>(relative_pose);
    }

    bool MethodLiRPFast::CheckRotation(const Matrix3d &R)
    {
        const double det = R.determinant();
        return std::abs(det - 1.0) < 1e-6;
    }

    bool MethodLiRPFast::CheckTranslation(const Vector3d &t)
    {
        return t.norm() > 1e-6;
    }

    // -----------------------------------------------------------------------------
    // 优化版本的核心算法实现
    // -----------------------------------------------------------------------------

    bool MethodLiRPFast::SolveLiRPMainOptimized(
        const BearingVectors &points1,
        const BearingVectors &points2,
        Matrix3d &R,
        Vector3d &t,
        const VectorXd *weights)
    {
        // 1. 检查输入数据
        const int num_matches = points1.cols();
        if (num_matches < kMinNumPoints)
        {
            PO_LOG_ERR << "Need at least " << kMinNumPoints << " point pairs, got " << num_matches << std::endl;
            return false;
        }

        // 2. 直接计算ATA矩阵，避免存储A矩阵
        ComputeATADirectly(points1, points2, weights, ATA_buffer_);

        if (!params_.use_fast_mode)
            ComputeSumA(points1, points2, weights);

        // 3. 使用优化的特征值求解
        if (!SolveEigenOptimized(ATA_buffer_, null_space_buffer_))
        {
            PO_LOG_ERR << "Failed to compute eigenvalues" << std::endl;
            return false;
        }

        // 4. 构建eigen_sols并求解本质矩阵
        EigenSols eigen_sols = null_space_buffer_.transpose(); // 取最小的3个特征值对应的特征向量
        compute_Bcoefficients(eigen_sols, coeff_mat_buffer_);

        // 5. 求解本质矩阵
        if (!SolveOptimized(eigen_sols, coeff_mat_buffer_))
        {
            PO_LOG_ERR << "Failed to solve 6-point problem" << std::endl;
            return false;
        }

        // 6. 构建最终解向量（已经直接保存在Evec_buffer_中）
        // 构建最终的Evec矩阵：Evec_buffer_ + null_space_buffer_
        Matrix<double, 9, 9> Evec;
        Evec.leftCols(6) = Evec_buffer_;
        Evec.rightCols(3) = null_space_buffer_;

        // 7. 最终处理步骤
        Matrix<double, 3, 4> best_solution;
        best_solution = process_Evec(Evec, points1, points2, weights);

        // 提取最优旋转矩阵和平移向量
        R = best_solution.block<3, 3>(0, 0);
        t = best_solution.col(3);

        // 验证结果
        if (!CheckRotation(R) || !CheckTranslation(t))
        {
            PO_LOG_ERR << "Invalid rotation or translation" << std::endl;
            return false;
        }

        // 简化日志输出
        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "SolveLiRPMainOptimized: 处理了 " << num_matches << " 对点"
                                   << ", 残差函数=" << params_.identify_mode
                                   << ", RT_Check=" << params_.rt_check_type_str
                                   << ", 优化模式=" << (params_.use_opt_mode ? "开" : "关")
                                   << ", 快速模式=" << (params_.use_fast_mode ? "开" : "关") << std::endl;
        }

        return true;
    }
    Matrix<double, 3, 4> MethodLiRPFast::process_Evec(const Matrix<double, 9, 9> &Evec,
                                                      const BearingVectors &v1,
                                                      const BearingVectors &v2,
                                                      const VectorXd *ptr_weigths)
    {
        Matrix<double, 3, 4> est_Rt = Matrix<double, 3, 4>::Zero();
        est_Rt.block<3, 3>(0, 0) = Matrix3d::Identity(); // 默认初始化

        if (params_.use_fast_mode)
        {
            compute_cost_fast(Evec, v1, v2, ptr_weigths, est_Rt);
        }
        else
        {

            double min_cost = std::numeric_limits<double>::max();

            Matrix<double, 3, 4> tmp_Rt;
            if (params_.compute_mode)
            {
                // 对齐processEvec2：前两组(6)用evec_mode=false，后两组(3)用evec_mode=true
                double cost1 = compute_cost(Evec.middleCols<6>(0), v1, v2, false, ptr_weigths, tmp_Rt);
                if (cost1 < min_cost)
                {
                    min_cost = cost1;
                    est_Rt = tmp_Rt;
                }

                double cost2 = compute_cost(Evec.middleCols<3>(6), v1, v2, true, ptr_weigths, tmp_Rt);
                if (cost2 < min_cost)
                {
                    min_cost = cost2;
                    est_Rt = tmp_Rt;
                }
            }
            else
            {
                double cost1 = compute_cost_rt(Evec.middleCols<6>(0), v1, v2, true, ptr_weigths, tmp_Rt);
                if (cost1 < min_cost)
                {
                    min_cost = cost1;
                    est_Rt = tmp_Rt;
                }

                double cost2 = compute_cost_rt(Evec.middleCols<3>(6), v1, v2, true, ptr_weigths, tmp_Rt);
                if (cost2 < min_cost)
                {
                    min_cost = cost2;
                    est_Rt = tmp_Rt;
                }
            }
        }
        return est_Rt;
    }
    // 参数配置函数
    void MethodLiRPFast::ConfigureAlgorithmParams()
    {
        // 配置识别模式和残差函数
        params_.identify_mode = GetOptionAsString("identify_mode", "PPO");

        if (boost::iequals(params_.identify_mode, "opengv_c"))
        {
            params_.residual_func = &residual_opengv;
        }
        else if (boost::iequals(params_.identify_mode, "PPObvcInvd"))
        {
            params_.residual_func = &residual_PPO_bvc_invd;
        }
        else if (boost::iequals(params_.identify_mode, "PPO"))
        {
            params_.residual_func = &residual_PPO;
        }
        else if (boost::iequals(params_.identify_mode, "PPOG"))
        {
            params_.residual_func = &residual_PPOG;
        }
        else if (boost::iequals(params_.identify_mode, "sampson"))
        {
            params_.residual_func = &residual_sampson;
        }
        else if (boost::iequals(params_.identify_mode, "LiGT"))
        {
            params_.residual_func = &residual_LiGT;
        }
        else if (boost::iequals(params_.identify_mode, "LiRT"))
        {
            params_.residual_func = &residual_LiRT;
        }
        else
        {
            params_.residual_func = &residual_PPO;
        }

        // 配置其他参数
        params_.compute_mode = GetOptionAsBool("compute_mode", true);
        params_.use_opt_mode = GetOptionAsBool("use_opt_mode", false);
        params_.use_median_cost = GetOptionAsBool("use_median_cost", true);
        params_.use_fast_mode = GetOptionAsBool("use_fast_mode", false);

        // 配置RT检查点数：最小10个点，默认50个点
        int rt_check_pts_value = GetOptionAsIndexT("rt_check_pts", 50);
        params_.rt_check_pts = std::max(10, rt_check_pts_value);

        // 配置RT_Check类型
        params_.rt_check_type_str = GetOptionAsString("rt_check_type", "RT_CHECK2");
        params_.rt_check_type = StringToRTCheckType(params_.rt_check_type_str);
    }

    void MethodLiRPFast::ComputeATADirectly(
        const BearingVectors &points1,
        const BearingVectors &points2,
        const VectorXd *weights,
        Matrix<double, 9, 9> &ATA)
    {
        // 直接累加计算ATA = A^T * A，避免存储整个A矩阵
        ATA.setZero();

        const int num_matches = points1.cols();
        for (int i = 0; i < num_matches; ++i)
        {
            const double w = (weights != nullptr) ? (*weights)(i) : 1.0;

            // 计算当前行的克罗内克积
            const Vector3d &p1 = points1.col(i);
            const Vector3d &p2 = points2.col(i);

            // 直接计算外积并累加到ATA中
            const double w_sq = w * w;
            for (int j = 0; j < 3; ++j)
            {
                for (int k = 0; k < 3; ++k)
                {
                    const double p2j_p1k = p2(j) * p1(k);
                    const int base_idx = j * 3 + k;

                    for (int l = 0; l < 3; ++l)
                    {
                        for (int m = 0; m < 3; ++m)
                        {
                            const double p2l_p1m = p2(l) * p1(m);
                            const int other_idx = l * 3 + m;

                            ATA(base_idx, other_idx) += w_sq * p2j_p1k * p2l_p1m;
                        }
                    }
                }
            }
        }
    }

    bool MethodLiRPFast::SolveEigenOptimized(
        const Matrix<double, 9, 9> &ATA,
        Matrix<double, 9, 3> &null_space)
    {
        // 使用稠密矩阵特征值求解器，比稀疏求解器更适合9x9矩阵
        SelfAdjointEigenSolver<Matrix<double, 9, 9>> solver(ATA);

        if (solver.info() != Success)
        {
            return false;
        }

        // 获取最小的3个特征值对应的特征向量
        null_space = solver.eigenvectors().leftCols(3);

        // 当详细日志级别时，分析特征值分布
        if (log_level_ >= PO_LOG_VERBOSE)
        {
            // 获取所有特征值（已按升序排列）
            VectorXd eigenvalues = solver.eigenvalues();

            if (eigenvalues.size() >= 4)
            {
                // 获取最小的4个特征值
                double smallest = eigenvalues(0); // 最小特征值
                double second = eigenvalues(1);   // 第二小特征值
                double third = eigenvalues(2);    // 第三小特征值
                double fourth = eigenvalues(3);   // 第四小特征值（在最小4个中是最大的）

                // 计算比值分析
                double ratio_fourth_to_third = (third != 0.0) ? fourth / third : std::numeric_limits<double>::infinity();
                double ratio_third_to_second = (second != 0.0) ? third / second : std::numeric_limits<double>::infinity();
                double ratio_second_to_first = (smallest != 0.0) ? second / smallest : std::numeric_limits<double>::infinity();

                PO_LOG(PO_LOG_VERBOSE) << "[EigenAnalysis] 最小4个特征值分析:" << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "  最小特征值:   " << std::scientific << std::setprecision(6) << smallest << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "  第二小特征值: " << std::scientific << std::setprecision(6) << second << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "  第三小特征值: " << std::scientific << std::setprecision(6) << third << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "  第四小特征值: " << std::scientific << std::setprecision(6) << fourth << std::endl;

                PO_LOG(PO_LOG_VERBOSE) << "[EigenAnalysis] 特征值比值分析:" << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "  λ₄/λ₃ (最大/次大): " << std::fixed << std::setprecision(3) << ratio_fourth_to_third << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "  λ₃/λ₂:             " << std::fixed << std::setprecision(3) << ratio_third_to_second << std::endl;
                PO_LOG(PO_LOG_VERBOSE) << "  λ₂/λ₁:             " << std::fixed << std::setprecision(3) << ratio_second_to_first << std::endl;

                // 评估零空间质量
                if (ratio_fourth_to_third > 100.0)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[EigenAnalysis] 零空间质量: 良好 (λ₄/λ₃ > 100)" << std::endl;
                }
                else if (ratio_fourth_to_third > 10.0)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[EigenAnalysis] 零空间质量: 中等 (10 < λ₄/λ₃ < 100)" << std::endl;
                }
                else
                {
                    PO_LOG(PO_LOG_VERBOSE) << "[EigenAnalysis] 零空间质量: 较差 (λ₄/λ₃ < 10)" << std::endl;
                }
            }
        }

        return true;
    }

    bool MethodLiRPFast::SolveOptimized(
        const EigenSols &eigen_sols,
        const Matrix<double, 9, 10> &B)
    {
        // 使用预分配的缓冲区
        M_buffer_ = B.leftCols(4).colPivHouseholderQr().solve(B.rightCols(B.cols() - 4));

        // 始终计算 x-basis polynomial sols (6解)
        C1_buffer_.topRows<3>() = -M_buffer_.topRows<3>();
        C1_buffer_.bottomRows<3>() << 1, 0, 0, 0, 0, 0,
            0, 1, 0, 0, 0, 0,
            0, 0, 0, 1, 0, 0;

        EigenSolver<Matrix<double, 6, 6, ColMajor>> es1(C1_buffer_);
        Matrix<double, 6, 6> V1 = es1.pseudoEigenvectors();

        Matrix<double, 2, 6> SOLS1 = V1.block(3, 0, 2, V1.cols()).array() / V1.row(5).replicate(2, 1).array();
        Matrix<double, 3, 6> SOLS1_extended;
        SOLS1_extended << SOLS1, Matrix<double, 1, 6>::Ones();
        Matrix<double, 9, 6> Evec1 = eigen_sols.transpose() * SOLS1_extended;

        Evec_buffer_ << Evec1.real();

        // 规范化每一列
        for (int i = 0; i < Evec_buffer_.cols(); ++i)
        {
            Evec_buffer_.col(i).normalize();
        }
        return true;
    }

} // namespace PoSDK
