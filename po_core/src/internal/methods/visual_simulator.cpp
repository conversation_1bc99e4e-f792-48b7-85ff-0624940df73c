/**
 * @file visual_simulator.cpp
 * @brief 可视化模拟器实现
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#include "visual_simulator.hpp"
#include "data/data_camera_models.hpp"
#include "../fileIO/file_io.hpp"
#include <iostream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <filesystem>
#include <iomanip>

namespace PoSDK
{

    VisualSimulator::VisualSimulator()
    {
        // 1. 加载通用配置
        InitializeDefaultConfigPath();
    }

    DataPtr VisualSimulator::Run()
    {
        // 从配置中获取日志级别
        log_level_ = GetOptionAsIndexT("log_level", 1);

        // 获取选项
        const double noise_level = GetOptionAsDouble("noise_level", 1.0);
        const int num_views = GetOptionAsIndexT("num_views", 3);
        const int num_points = GetOptionAsIndexT("num_points", 1000);
        const int max_points_per_view = GetOptionAsIndexT("max_points_per_view", 5000);
        const double image_width = GetOptionAsDouble("image_width", 1920.0);
        const double image_height = GetOptionAsDouble("image_height", 1080.0);
        const double focal_length = GetOptionAsDouble("focal_length", 1000.0);
        const bool gaussian_noise = GetOptionAsBool("gaussian_noise", false);
        const int closest_points = GetOptionAsIndexT("closest_points", 500);
        const int random_seed = GetOptionAsIndexT("random_seed", 0);
        const bool save_gt_info = GetOptionAsBool("save_gt_info", false);
        const std::string camera_make = GetOptionAsString("camera_make", "PoSDK");
        const std::string camera_model = GetOptionAsString("camera_model", "Simulator");
        const std::string structure_type = GetOptionAsString("structure_type", "close_loop_circle");
        const double PRO_ratio = GetOptionAsDouble("PRO_ratio", 0.0);
        const double obs_outlier_ratio = GetOptionAsDouble("obs_outlier_ratio", 0.0);
        simu_folder_ = GetOptionAsString("simu_folder", "simulated_data");

        PO_LOG(PO_LOG_NONE) << "开始视觉仿真，参数配置：" << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 视图数量: " << num_views << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 3D点数量: " << num_points << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 噪声水平: " << noise_level << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 图像尺寸: " << image_width << "x" << image_height << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 焦距: " << focal_length << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 结构类型: " << structure_type << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 纯旋转异常值比例(PRO): " << PRO_ratio << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 观测点异常值比例: " << obs_outlier_ratio << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 仿真目录: " << simu_folder_ << std::endl;
        PO_LOG(PO_LOG_NONE) << "  - 随机种子: " << random_seed << std::endl;

        // 初始化随机数生成器
        std::mt19937 rng(random_seed);

        // 创建相机内参
        KMats Ks;
        GenerateCameraIntrinsics(num_views, focal_length, image_width, image_height, Ks);

        // 生成相机位姿和3D点云
        GlobalRotations Rs;
        GlobalTranslations ts;
        Points3d X(3, num_points);

        // 使用结构化方法生成相机位姿和3D点
        bool structure_generated = GenerateStructure(num_views, num_points, structure_type,
                                                     noise_level, rng, Rs, ts, X);

        // 详细显示相机位姿（仅在详细日志模式下）
        PO_LOG(PO_LOG_VERBOSE) << "生成的相机位姿详情：" << std::endl;
        for (int i = 0; i < num_views; ++i)
        {
            PO_LOG(PO_LOG_VERBOSE) << "  相机 " << i << ": " << ts[i].transpose() << std::endl;
        }

        if (!structure_generated)
        {
            // 如果结构生成失败，使用随机方法
            PO_LOG_WARNING << "结构生成失败，使用随机相机位姿" << std::endl;

            // 生成随机相机位姿
            GenerateRandomCameras(num_views, Rs, ts, rng);

            // 生成随机3D点云
            std::uniform_real_distribution<double> point_dist(-10.0, 10.0);
            for (int i = 0; i < num_points; ++i)
            {
                X(0, i) = point_dist(rng);
                X(1, i) = point_dist(rng);
                X(2, i) = point_dist(rng) + 15.0; // 确保大部分点在相机前方
            }
        }

        // 生成特征点和跟踪
        Tracks tracks;
        GenerateFeatures(Ks, Rs, ts, X, noise_level, gaussian_noise,
                         max_points_per_view, closest_points, tracks);

        // --- 新增：在打包前更新观测ID ---
        UpdateObsIds(tracks);
        // --- 更新结束 ---

        // --- 新增：添加观测点异常值处理 ---
        if (obs_outlier_ratio > 0.0)
        {
            std::set<IndexT> outlier_obs_ids = AddOutliersToTracks(tracks, obs_outlier_ratio, rng);
            if (!outlier_obs_ids.empty())
            {
                PO_LOG(PO_LOG_NORMAL) << "添加了 " << outlier_obs_ids.size()
                                      << " 个观测点异常值（比例: " << std::fixed << std::setprecision(2)
                                      << (obs_outlier_ratio * 100) << "%）" << std::endl;
            }
        }
        // --- 观测点异常值处理结束 ---

        PO_LOG(PO_LOG_NORMAL) << "生成了 " << tracks.size() << " 条轨迹" << std::endl;

        // 计算并显示平均轨迹长度
        if (!tracks.empty())
        {
            double avg_length = 0.0;
            for (const auto &track : tracks)
            {
                avg_length += track.track.size();
            }
            avg_length /= tracks.size();
            PO_LOG(PO_LOG_VERBOSE) << "平均轨迹长度: " << std::fixed << std::setprecision(2) << avg_length << std::endl;
        }

        // 创建DataPackage，用于返回结果
        auto package = std::make_shared<DataPackage>();

        // 创建并添加DataTracks
        auto data_tracks = std::make_shared<DataTracks>();
        auto internal_tracks_ptr = GetDataPtr<Tracks>(data_tracks);
        if (internal_tracks_ptr)
        {
            *internal_tracks_ptr = std::move(tracks);
        }
        else
        {
            PO_LOG_ERR << "无法从DataTracks获取内部轨迹指针" << std::endl;
        }
        package->AddData(data_tracks);

        // 创建并添加DataCameraModel
        camera_model_ptr_ = std::make_shared<DataCameraModels>();

        // 配置相机模型
        CameraIntrinsics intrinsics;
        intrinsics.fx = focal_length;
        intrinsics.fy = focal_length;
        intrinsics.cx = image_width / 2.0;
        intrinsics.cy = image_height / 2.0;
        intrinsics.width = image_width;
        intrinsics.height = image_height;
        intrinsics.model_type = CameraModelType::PINHOLE;
        intrinsics.distortion_type = DistortionType::NO_DISTORTION;

        // 正确设置相机模型数据
        auto camera_models_ptr = GetDataPtr<CameraModels>(camera_model_ptr_);
        if (!camera_models_ptr)
        {
            PO_LOG_ERR << "无法从DataCameraModel获取CameraModels数据" << std::endl;
            return package;
        }

        // 创建相机模型并设置内参和基本信息
        CameraModel cam_model;
        cam_model.SetCameraIntrinsics(intrinsics);
        cam_model.SetCameraInfo(camera_make, camera_model, "SIM" + std::to_string(random_seed));

        // 添加到相机模型列表（仿真器中所有视图使用相同的相机）
        camera_models_ptr->clear();
        camera_models_ptr->push_back(cam_model);

        package->AddData(camera_model_ptr_);

        // 创建并添加DataGlobalPoses
        global_poses_ptr_ = std::make_shared<DataGlobalPoses>();
        auto *global_poses_ptr = static_cast<GlobalPoses *>(global_poses_ptr_->GetData());

        // 初始化GlobalPoses
        global_poses_ptr->Init(num_views);

        // 设置旋转矩阵和平移向量
        for (int i = 0; i < num_views; ++i)
        {
            global_poses_ptr->SetRotation(i, Rs[i]);
            global_poses_ptr->SetTranslation(i, ts[i]);
        }

        // 构建EstInfo
        global_poses_ptr->GetEstInfo().Init(num_views);
        for (int i = 0; i < num_views; ++i)
        {
            global_poses_ptr->GetEstInfo().AddEstimatedView(i);
        }

        // --- 设置初始位姿格式 ---
        global_poses_ptr->SetPoseFormat(PoseFormat::RwTw);
        // --- 设置结束 ---

        package->AddData(global_poses_ptr_);

        // 创建并添加真值3D点数据
        gt_points_ptr_ = std::dynamic_pointer_cast<DataPoints3D>(
            FactoryData::Create("data_points_3d"));

        // 获取WorldPointInfo并设置3D点数据
        auto world_point_info = static_cast<WorldPointInfo *>(gt_points_ptr_->GetData());
        world_point_info->resize(X.cols());
        for (int i = 0; i < X.cols(); ++i)
        {
            world_point_info->setPoint(i, X.col(i));
            world_point_info->setUsed(i, true); // 所有仿真生成的点都标记为使用
        }

        package->AddData("data_points_3d", gt_points_ptr_);

        // 显示仿真摘要(add parameter: disp_summary)
        bool disp_summary = GetOptionAsBool("disp_summary", false);
        if (disp_summary)
        {
            DisplaySimulationSummary(num_views, num_points, structure_type, noise_level, *internal_tracks_ptr);
        }

        // 检查是否需要导出meshlab工程
        std::string export_meshlab_path = GetOptionAsString("export_meshlab_path", "");
        if (!export_meshlab_path.empty())
        {
            bool meshlab_exported = ExportToMeshlabPoSDK(export_meshlab_path);
            if (meshlab_exported)
            {
                PO_LOG(PO_LOG_NORMAL) << "Meshlab工程导出成功" << std::endl;
            }
            else
            {
                PO_LOG_ERR << "Meshlab工程导出失败" << std::endl;
            }
        }

        // 如果有需要，保存真值
        if (save_gt_info)
        {
            bool saved = SaveGroundTruth();
            if (saved)
            {
                PO_LOG(PO_LOG_VERBOSE) << "真值数据保存成功" << std::endl;
            }
            else
            {
                PO_LOG_ERR << "真值数据保存失败" << std::endl;
            }
        }

        return package;
    }

    void VisualSimulator::DisplaySimulationSummary(int num_views, int num_points,
                                                   const std::string &structure_type,
                                                   double noise_level,
                                                   const Tracks &tracks)
    {
        PO_LOG(PO_LOG_NONE) << "=============== Visual Simulator Summary ===============" << std::endl;

        // 基本配置信息
        PO_LOG(PO_LOG_NONE) << "┌─────────────────────────────────────────┐" << std::endl;
        PO_LOG(PO_LOG_NONE) << "│                基本配置                 │" << std::endl;
        PO_LOG(PO_LOG_NONE) << "├─────────────────────────────────────────┤" << std::endl;
        PO_LOG(PO_LOG_NONE) << "│ 相机数量       : " << std::setw(6) << num_views << "           │" << std::endl;
        PO_LOG(PO_LOG_NONE) << "│ 3D点数量       : " << std::setw(6) << num_points << "           │" << std::endl;
        PO_LOG(PO_LOG_NONE) << "│ 结构类型       : " << std::setw(15) << std::left << structure_type << " │" << std::endl;
        PO_LOG(PO_LOG_NONE) << "│ 噪声水平       : " << std::setw(6) << std::fixed << std::setprecision(2) << noise_level << "           │" << std::endl;
        PO_LOG(PO_LOG_NONE) << "└─────────────────────────────────────────┘" << std::endl;

        if (!tracks.empty())
        {
            // 轨迹统计信息
            size_t total_observations = 0;
            size_t min_track_length = SIZE_MAX;
            size_t max_track_length = 0;
            std::vector<size_t> track_lengths;

            for (const auto &track : tracks)
            {
                size_t length = track.track.size();
                track_lengths.push_back(length);
                total_observations += length;
                min_track_length = std::min(min_track_length, length);
                max_track_length = std::max(max_track_length, length);
            }

            double avg_track_length = static_cast<double>(total_observations) / tracks.size();

            // 计算中位数轨迹长度
            std::sort(track_lengths.begin(), track_lengths.end());
            size_t median_track_length = track_lengths[track_lengths.size() / 2];

            // 计算每个视图的观测数量
            std::vector<size_t> observations_per_view(num_views, 0);
            for (const auto &track : tracks)
            {
                for (const auto &obs : track.track)
                {
                    if (obs.view_id >= 0 && obs.view_id < num_views)
                    {
                        observations_per_view[obs.view_id]++;
                    }
                }
            }

            size_t min_obs_per_view = *std::min_element(observations_per_view.begin(), observations_per_view.end());
            size_t max_obs_per_view = *std::max_element(observations_per_view.begin(), observations_per_view.end());
            double avg_obs_per_view = static_cast<double>(total_observations) / num_views;

            PO_LOG(PO_LOG_NONE) << "┌─────────────────────────────────────────┐" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│                轨迹统计                 │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "├─────────────────────────────────────────┤" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 轨迹总数       : " << std::setw(6) << tracks.size() << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 观测总数       : " << std::setw(6) << total_observations << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 平均轨迹长度   : " << std::setw(6) << std::fixed << std::setprecision(2) << avg_track_length << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 中位数轨迹长度 : " << std::setw(6) << median_track_length << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 最短轨迹长度   : " << std::setw(6) << min_track_length << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 最长轨迹长度   : " << std::setw(6) << max_track_length << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "└─────────────────────────────────────────┘" << std::endl;

            PO_LOG(PO_LOG_NONE) << "┌─────────────────────────────────────────┐" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│              视图观测统计               │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "├─────────────────────────────────────────┤" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 平均每视图观测 : " << std::setw(6) << std::fixed << std::setprecision(2) << avg_obs_per_view << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 最少视图观测   : " << std::setw(6) << min_obs_per_view << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "│ 最多视图观测   : " << std::setw(6) << max_obs_per_view << "           │" << std::endl;
            PO_LOG(PO_LOG_NONE) << "└─────────────────────────────────────────┘" << std::endl;

            // 详细的每个视图观测统计（仅在详细日志模式下显示）
            PO_LOG(PO_LOG_NONE) << "每个视图的观测数量详情：" << std::endl;
            for (int i = 0; i < num_views; ++i)
            {
                PO_LOG(PO_LOG_NONE) << "  视图 " << std::setw(2) << i << ": "
                                    << std::setw(4) << observations_per_view[i] << " 个观测" << std::endl;
            }
        }
        else
        {
            PO_LOG_WARNING << "未生成任何轨迹数据！" << std::endl;
        }

        PO_LOG(PO_LOG_NONE) << "============================================" << std::endl;
    }

    bool VisualSimulator::GenerateStructure(
        int num_views,
        int num_points,
        const std::string &structure_type,
        double noise_level,
        std::mt19937 &rng,
        GlobalRotations &Rs,
        GlobalTranslations &ts,
        Points3d &X)
    {
        // 检查输入参数
        if (num_views < 2 || num_points < 1)
        {
            PO_LOG_ERR << "结构生成参数无效" << std::endl;
            return false;
        }

        bool success = false;

        // 1. 尝试使用内置结构类型
        if (structure_type == "close_loop_circle")
        {
            // 获取内置结构的配置参数
            const int num_circles = GetOptionAsIndexT("num_circles", 1);
            const double dt_setting = GetOptionAsDouble("dt_setting", 5.0);
            const double PRO_ratio = GetOptionAsDouble("PRO_ratio", 0.0);

            // 使用CloseLoopCircle函数生成结构
            CloseLoopCircle(num_views, num_points, num_circles, dt_setting, PRO_ratio, rng, Rs, ts, X);

            success = true;
        }
        // 2. 尝试从工厂创建结构生成器
        else
        {
            try
            {
                // 创建方法选项
                MethodOptions options;
                options["num_views"] = std::to_string(num_views);
                options["num_points"] = std::to_string(num_points);
                options["noise_level"] = std::to_string(noise_level);

                // 从工厂创建结构生成方法
                auto method_ptr = std::dynamic_pointer_cast<MethodPreset>(
                    FactoryMethod::Create("structure_" + structure_type));

                if (method_ptr)
                {
                    // 设置方法选项
                    method_ptr->SetMethodOptions(options);

                    // 运行结构生成方法
                    auto result = method_ptr->Build();
                    if (result)
                    {
                        // 结果是直接指向DataMap<StructureInfo>的DataPtr
                        auto structure_ptr = GetDataPtr<StructureInfo>(result);
                        if (structure_ptr)
                        {
                            // 复制旋转矩阵和平移向量
                            Rs = structure_ptr->rotations;
                            ts = structure_ptr->translations;
                            X = structure_ptr->points;
                            success = true;
                        }
                        else
                        {
                            PO_LOG_ERR << "无法从结果中获取StructureInfo" << std::endl;
                        }
                    }
                    else
                    {
                        PO_LOG_ERR << "方法构建返回空结果" << std::endl;
                    }
                }
                else
                {
                    PO_LOG_ERR << "无法从工厂创建结构生成器: structure_" + structure_type << std::endl;
                }
            }
            catch (const std::exception &e)
            {
                PO_LOG_ERR << "创建结构时发生异常: " << e.what() << std::endl;
            }
        }

        return success;
    }

    void VisualSimulator::CloseLoopCircle(
        int num_views,
        int num_points,
        int num_circles,
        double dt_setting,
        double PRO_ratio,
        std::mt19937 &rng,
        GlobalRotations &Rs,
        GlobalTranslations &ts,
        Points3d &X)
    {

        // 初始化输出
        Rs.resize(num_views);
        ts.resize(num_views);
        X.resize(3, num_points);

        // 0. 检查输入参数是否有效
        if (dt_setting <= 0)
        {
            PO_LOG_WARNING << "dt_setting <= 0，使用默认相机位置值" << std::endl;

            // 使用默认值，与MATLAB代码一致
            for (int i = 0; i < num_views; ++i)
            {
                ts[i] = Vector3d(1.0, 1.0, 1.0);
            }
            double t_radius = 0.0;

            // 生成相机朝向和3D点
            // 此处继续执行后续代码...
        }
        else
        {
            // 1. 计算轨道半径: t_radius = num_views * dt_setting / (2*PI*num_circles)
            double t_radius = num_views * dt_setting / (2.0 * M_PI * num_circles);

            // 检查dt_setting是否过大导致asin函数的参数超出[-1,1]范围
            double arg = dt_setting / (2.0 * t_radius);
            if (arg >= 1.0 || arg <= -1.0)
            {
                PO_LOG_WARNING << "dt_setting对于给定的num_views和num_circles过大，asin参数 = "
                               << arg << "，使用截断值" << std::endl;
                arg = std::min(std::max(arg, -0.99), 0.99); // 限制在安全范围内
            }

            // 计算每步旋转角度
            double t_angle = 2.0 * asin(arg);

            // 2. 生成相机位置（圆形轨道）
            Vector3d origin_pos(t_radius, 0, 0); // 与MATLAB一致，第一个相机在[t_radius,0,0]
            ts[0] = origin_pos;

            // 创建旋转矩阵（绕Z轴旋转）- 确保与MATLAB中的quat2dcm结果一致
            Matrix3d t_rotation;
            t_rotation << cos(t_angle), -sin(t_angle), 0,
                sin(t_angle), cos(t_angle), 0,
                0, 0, 1;

            // 检查旋转矩阵是否有效
            if (!t_rotation.allFinite())
            {
                PO_LOG_WARNING << "生成的旋转矩阵无效，使用单位矩阵代替" << std::endl;
                t_rotation = Matrix3d::Identity();
            }

            // 沿轨道生成相机位置
            for (int i = 1; i < num_views; ++i)
            {
                ts[i] = t_rotation * ts[i - 1];

                // 检查生成的位置是否有效
                if (!ts[i].allFinite())
                {
                    PO_LOG_WARNING << "在索引 " << i << " 处生成的相机位置无效，使用回退位置" << std::endl;
                    // 回退到安全值
                    ts[i] = ts[0] + Vector3d(i, i, 0);
                }
            }

            // 添加Z轴随机扰动 (与MATLAB代码一致，范围为[-0.5, 0.5]，乘以固定值5.0)
            std::uniform_real_distribution<double> bias_dist(0.0, 1.0);
            double bias_length = GetOptionAsDouble("baseline_z_bias", 5.0); // 与MATLAB中的bias_length相同
            for (int i = 0; i < num_views; ++i)
            {
                ts[i][2] += bias_length * (0.5 - bias_dist(rng));
            }

            // 添加异常点
            if (PRO_ratio > 0)
            {
                int num_outliers = std::floor(PRO_ratio * (num_views - 1));
                if (num_outliers > 0)
                {
                    // 随机选择异常点索引（不包括第一个点）
                    std::vector<int> indices(num_views - 1);
                    std::iota(indices.begin(), indices.end(), 1);
                    std::shuffle(indices.begin(), indices.end(), rng);

                    // 处理前num_outliers个索引
                    for (int i = 0; i < num_outliers && i < indices.size(); ++i)
                    {
                        int idx = indices[i];
                        // 使异常点靠近相邻点 - 确保与MATLAB一致
                        if (idx > 0)
                        {
                            // 使异常点靠近前一个点
                            ts[idx] = 0.1 * ts[idx] + 0.9 * ts[idx - 1];
                        }
                        else if (idx < num_views - 1)
                        {
                            // 或者靠近后一个点
                            ts[idx] = 0.1 * ts[idx] + 0.9 * ts[idx + 1];
                        }
                    }
                }
            }
        }

        // 3. 生成相机朝向（旋转矩阵）
        // 设置轴向扰动与MATLAB一致，使用标准差为1e-4的正态分布
        std::normal_distribution<double> normal_dist(0.0, 1e-4);
        std::uniform_real_distribution<double> uniform_dist(0.0, 2.0 * M_PI);

        // 初始化旋转轴和角度 - 与MATLAB中的R_quat_dvec和R_quat_angle对应
        std::vector<Vector3d> R_quat_dvec(num_views, Vector3d(0, 0, 1));
        std::vector<double> R_quat_angle(num_views);

        // 设置旋转角度和添加轴向扰动
        for (int i = 0; i < num_views; ++i)
        {
            // 围绕Z轴的随机旋转，但第一个相机角度为0
            R_quat_angle[i] = (i == 0) ? 0.0 : uniform_dist(rng);

            // 添加微小扰动到旋转轴（与MATLAB中的normrnd一致）
            R_quat_dvec[i](0) += normal_dist(rng);
            R_quat_dvec[i](1) += normal_dist(rng);
            R_quat_dvec[i].normalize();
        }

        // 构建旋转矩阵 - 类似于MATLAB中的quat2dcm函数
        for (int i = 0; i < num_views; ++i)
        {
            double angle = R_quat_angle[i];
            const Vector3d &axis = R_quat_dvec[i];

            // 使用罗德里格斯公式构建旋转矩阵
            Matrix3d skew;
            skew << 0, -axis(2), axis(1),
                axis(2), 0, -axis(0),
                -axis(1), axis(0), 0;

            Rs[i] = Matrix3d::Identity() + sin(angle) * skew +
                    (1 - cos(angle)) * (skew * skew);

            // 检查生成的旋转矩阵是否有效
            if (!Rs[i].allFinite())
            {
                PO_LOG_WARNING << "在索引 " << i << " 处生成的旋转矩阵无效，使用单位矩阵代替" << std::endl;
                Rs[i] = Matrix3d::Identity();
            }
        }

        // 4. 生成3D点云（与MATLAB代码保持一致）
        // 计算点云的半径，与MATLAB中的X_radius对应
        double X_radius = ts[0].norm(); // 使用第一个相机位置的半径
        if (X_radius < 1e-5)
            X_radius = 10.0; // 防止半径太小

        // 为每个点生成随机角度
        std::vector<double> X_rod_angle(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            X_rod_angle[i] = uniform_dist(rng); // [0, 2π]
        }

        // 计算点的中心位置
        std::vector<Vector3d> X_center(num_points);
        Vector3d X_origin_pos(X_radius, 0, 0);

        for (int i = 0; i < num_points; ++i)
        {
            double angle = X_rod_angle[i];

            // 围绕Z轴计算位置
            X_center[i](0) = X_radius * cos(angle);
            X_center[i](1) = X_radius * sin(angle);
            X_center[i](2) = 0;
        }

        // 设置扰动参数（与MATLAB一致）
        double x_perturb = 50.0; // MATLAB中的x_perturb
        double y_perturb = 50.0; // MATLAB中的y_perturb
        double z_perturb = 50.0; // MATLAB中的z_perturb

        // 将点上移z_perturb距离
        for (int i = 0; i < num_points; ++i)
        {
            X_center[i](2) += z_perturb;
        }

        // 添加随机扰动到每个点
        std::uniform_real_distribution<double> perturb_dist(0.0, 1.0);
        for (int i = 0; i < num_points; ++i)
        {
            // 添加随机扰动（使用MATLAB中相同的公式，范围[0.5-rand, 0.5-rand]）
            X(0, i) = X_center[i](0) + x_perturb * 2.0 * (0.5 - perturb_dist(rng));
            X(1, i) = X_center[i](1) + y_perturb * 2.0 * (0.5 - perturb_dist(rng));
            X(2, i) = X_center[i](2) + z_perturb * 1.0 * (0.5 - perturb_dist(rng)) + z_perturb;
        }

        // 输出调试信息
        PO_LOG(PO_LOG_VERBOSE) << "CloseLoopCircle生成了 " << num_views
                               << " 个相机位置和 " << num_points << " 个3D点" << std::endl;
        PO_LOG(PO_LOG_VERBOSE) << "第一个相机位置: " << ts[0].transpose() << std::endl;
        if (num_views > 1)
        {
            PO_LOG(PO_LOG_VERBOSE) << "第二个相机位置: " << ts[1].transpose() << std::endl;
        }
    }

    bool VisualSimulator::SaveGroundTruth()
    {
        if (!global_poses_ptr_ || !camera_model_ptr_)
        {
            PO_LOG_ERR << "无法保存真值数据：数据未初始化" << std::endl;
            return false;
        }

        try
        {
            // 创建真值目录
            std::filesystem::path gt_dir = simu_folder_;
            gt_dir /= "GroundTruth";
            std::filesystem::create_directories(gt_dir);

            // 保存相机模型
            std::string camera_file = (gt_dir / "camera_model").string();
            if (!camera_model_ptr_->Save(gt_dir.string(), "camera_model"))
            {
                PO_LOG_ERR << "保存相机模型失败" << std::endl;
                return false;
            }

            // 保存全局位姿
            std::string poses_file = (gt_dir / "global_poses").string();
            if (!global_poses_ptr_->Save(gt_dir.string(), "global_poses", ".po"))
            {
                PO_LOG_ERR << "保存全局位姿失败" << std::endl;
                return false;
            }

            PO_LOG(PO_LOG_NORMAL) << "真值数据已保存到 " << gt_dir << std::endl;
            return true;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "保存真值数据时发生错误: " << e.what() << std::endl;
            return false;
        }
    }

    bool VisualSimulator::LoadGroundTruth()
    {
        try
        {
            // 检查真值目录是否存在
            std::filesystem::path gt_dir = simu_folder_;
            gt_dir /= "GroundTruth";

            if (!std::filesystem::exists(gt_dir))
            {
                PO_LOG_ERR << "真值目录不存在: " << gt_dir << std::endl;
                return false;
            }

            // 加载相机模型
            camera_model_ptr_ = std::make_shared<DataCameraModels>();
            std::string camera_file = (gt_dir / "camera_model.pb").string();
            if (!camera_model_ptr_->Load(camera_file))
            {
                PO_LOG_ERR << "从 " << camera_file << " 加载相机模型失败" << std::endl;
                return false;
            }

            // 加载全局位姿
            global_poses_ptr_ = std::make_shared<DataGlobalPoses>();
            std::string poses_file = (gt_dir / "global_poses.po").string();
            if (!global_poses_ptr_->Load(poses_file, "po"))
            {
                PO_LOG_ERR << "从 " << poses_file << " 加载全局位姿失败" << std::endl;
                return false;
            }

            PO_LOG(PO_LOG_NORMAL) << "从 " << gt_dir << " 加载真值数据成功" << std::endl;
            return true;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "加载真值数据时发生错误: " << e.what() << std::endl;
            return false;
        }
    }

    void VisualSimulator::GenerateFeatures(
        const KMats &Ks,
        const GlobalRotations &Rs,
        const GlobalTranslations &ts,
        const Points3d &X,
        double noise_level,
        bool use_gaussian_noise,
        int max_points_per_view,
        int closest_points,
        Tracks &tracks)
    {

        const int num_views = Ks.size();
        const int num_points = X.cols();

        // 初始化轨迹容器
        tracks.clear();

        // 存储每个视图的观测
        std::vector<std::vector<ObsInfo>> observations_per_view(num_views);

        // 观测ID计数器
        IndexT obs_id_counter = 0;

        // 点ID向量，用于追踪原始点索引
        std::vector<int> id_pts(num_points);
        std::iota(id_pts.begin(), id_pts.end(), 0); // 0, 1, 2, ..., num_points-1

        // 每个视图处理
        for (int view_id = 0; view_id < num_views; ++view_id)
        {
            const Matrix3d &K = Ks[view_id];
            const Matrix3d &R = Rs[view_id];
            const Vector3d &t = ts[view_id];

            // 图像尺寸
            double image_width = K(0, 2) * 2.0;
            double image_height = K(1, 2) * 2.0;

            // 存储所有投影点及相关信息
            std::vector<Vector3d> projections(num_points);
            std::vector<bool> chirality_check(num_points, false);
            std::vector<Vector2d> noise_vectors(num_points);
            std::vector<double> distances(num_points);

            // 1. 投影所有3D点并检查正向性约束
            for (int i = 0; i < num_points; ++i)
            {
                // 计算投影: x = K * R * (X - t)
                projections[i] = K * R * (X.col(i) - t);

                // 检查正向性约束 (z > 1)
                chirality_check[i] = (projections[i](2) > 1.0);

                // 计算3D点到相机的距离
                distances[i] = (X.col(i) - t).norm();

                // 生成噪声
                if (use_gaussian_noise)
                {
                    // 高斯噪声模式
                    noise_vectors[i] = GenerateGaussianNoise(noise_level);
                }
                else
                {
                    // 方向性噪声模式（使噪声向量长度等于noise_level）
                    Vector2d random_dir(
                        static_cast<double>(rand()) / RAND_MAX,
                        static_cast<double>(rand()) / RAND_MAX);
                    double norm = random_dir.norm();
                    if (norm > 1e-10)
                    {
                        random_dir /= norm;
                    }
                    noise_vectors[i] = random_dir * noise_level;
                }
            }

            // 2. 筛选满足正向性约束的点
            std::vector<int> valid_indices;
            for (int i = 0; i < num_points; ++i)
            {
                if (chirality_check[i])
                {
                    valid_indices.push_back(i);
                }
            }

            // 如果没有有效点，跳过当前视图
            if (valid_indices.empty())
            {
                continue;
            }

            // 3. 添加噪声并透视除法
            std::vector<Vector2d> image_points;
            std::vector<int> point_ids;

            for (int idx : valid_indices)
            {
                // 透视除法 (x/z, y/z)
                Vector2d coords(
                    projections[idx](0) / projections[idx](2),
                    projections[idx](1) / projections[idx](2));

                // 添加噪声
                coords += noise_vectors[idx];

                // 检查图像边界
                if (coords(0) >= 0 && coords(0) < image_width &&
                    coords(1) >= 0 && coords(1) < image_height)
                {
                    // 存储有效的图像点
                    image_points.push_back(coords);
                    point_ids.push_back(id_pts[idx]);
                }
            }

            // 如果没有在图像边界内的点，跳过此视图
            if (image_points.empty())
            {
                continue;
            }

            // 4. 如果需要，选择距离最近的点
            if (closest_points > 0 && closest_points < image_points.size())
            {
                // 构建用于排序的索引数组
                std::vector<size_t> sorted_indices(valid_indices.size());
                std::iota(sorted_indices.begin(), sorted_indices.end(), 0);

                // 按距离排序
                std::sort(sorted_indices.begin(), sorted_indices.end(),
                          [&valid_indices, &distances](size_t i1, size_t i2)
                          {
                              return distances[valid_indices[i1]] < distances[valid_indices[i2]];
                          });

                // 选择最近的点
                std::vector<Vector2d> selected_points;
                std::vector<int> selected_ids;

                for (size_t i = 0; i < closest_points && i < sorted_indices.size(); ++i)
                {
                    size_t orig_idx = sorted_indices[i];
                    if (orig_idx < image_points.size())
                    {
                        selected_points.push_back(image_points[orig_idx]);
                        selected_ids.push_back(point_ids[orig_idx]);
                    }
                }

                image_points = std::move(selected_points);
                point_ids = std::move(selected_ids);
            }

            // 5. 如果需要，限制每个视图的最大点数
            if (max_points_per_view > 0 && max_points_per_view < image_points.size())
            {
                // 随机选择点
                std::vector<size_t> indices(image_points.size());
                std::iota(indices.begin(), indices.end(), 0);
                std::shuffle(indices.begin(), indices.end(), std::mt19937(view_id));

                std::vector<Vector2d> selected_points;
                std::vector<int> selected_ids;

                for (size_t i = 0; i < max_points_per_view && i < indices.size(); ++i)
                {
                    size_t idx = indices[i];
                    selected_points.push_back(image_points[idx]);
                    selected_ids.push_back(point_ids[idx]);
                }

                image_points = std::move(selected_points);
                point_ids = std::move(selected_ids);
            }

            // 6. 为该视图创建观测
            for (size_t i = 0; i < point_ids.size(); ++i)
            {
                ObsInfo obs;
                obs.view_id = view_id;
                obs.pts_id = point_ids[i];
                obs.obs_id = obs_id_counter++;
                obs.coord = image_points[i];
                obs.is_used = true;

                observations_per_view[view_id].push_back(obs);
            }
        }

        // 7. 检查是否有空视图，如果有则移除
        std::vector<bool> valid_views(num_views, true);
        int empty_views = 0;

        for (int view_id = 0; view_id < num_views; ++view_id)
        {
            if (observations_per_view[view_id].empty())
            {
                valid_views[view_id] = false;
                empty_views++;
            }
        }

        if (empty_views > 0)
        {
            PO_LOG_WARNING << "警告: " << empty_views << " 个视图没有有效观测，将被排除" << std::endl;
        }

        // 8. 从观测创建轨迹
        std::map<int, TrackInfo> track_map;

        for (int view_id = 0; view_id < num_views; ++view_id)
        {
            if (!valid_views[view_id])
                continue;

            for (const auto &obs : observations_per_view[view_id])
            {
                int point_id = obs.pts_id;

                // 添加到轨迹
                if (track_map.find(point_id) == track_map.end())
                {
                    track_map[point_id] = TrackInfo();
                    track_map[point_id].is_used = true;
                }

                track_map[point_id].track.push_back(obs);
            }
        }

        // 9. 过滤少于2个观测的轨迹
        for (auto it = track_map.begin(); it != track_map.end();)
        {
            if (it->second.track.size() < 2)
            {
                it = track_map.erase(it);
            }
            else
            {
                ++it;
            }
        }

        // 10. 将map转换为vector
        tracks.reserve(track_map.size());
        for (const auto &[point_id, track_info] : track_map)
        {
            tracks.push_back(track_info);
        }

        // 输出统计信息
        PO_LOG(PO_LOG_NORMAL) << "生成了 " << tracks.size()
                              << " 条轨迹，观测分布在 " << (num_views - empty_views) << " 个视图中" << std::endl;
    }

    bool VisualSimulator::IsWithinImageBounds(
        const Vector2d &point,
        double image_width,
        double image_height) const
    {

        return point(0) >= 0 && point(0) < image_width &&
               point(1) >= 0 && point(1) < image_height;
    }

    Vector2d VisualSimulator::GenerateDirectionalNoise(double magnitude)
    {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_real_distribution<double> dist(0.0, 1.0);

        // 生成随机方向
        Vector2d direction(dist(gen), dist(gen));
        // 归一化
        double norm = direction.norm();
        if (norm > 1e-10)
        {
            direction /= norm;
        }
        else
        {
            direction = Vector2d(1.0, 0.0);
        }

        // 缩放到指定幅度
        return direction * magnitude;
    }

    Vector2d VisualSimulator::GenerateGaussianNoise(double sigma)
    {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::normal_distribution<double> dist(0.0, 1.0);

        return Vector2d(dist(gen) * sigma, dist(gen) * sigma);
    }

    void VisualSimulator::GenerateRandomCameras(
        int num_views,
        GlobalRotations &Rs,
        GlobalTranslations &ts,
        std::mt19937 &rng)
    {

        Rs.resize(num_views);
        ts.resize(num_views);

        // 第一个相机在原点
        Rs[0] = Matrix3d::Identity();
        ts[0] = Vector3d::Zero();

        // 生成其他相机位姿
        std::uniform_real_distribution<double> angle_dist(0, M_PI / 4); // 小角度旋转
        std::uniform_real_distribution<double> radius_dist(5.0, 10.0);
        std::uniform_real_distribution<double> circle_dist(0, 2 * M_PI);

        for (int i = 1; i < num_views; ++i)
        {
            // 随机生成旋转矩阵
            double angle_x = angle_dist(rng);
            double angle_y = angle_dist(rng);
            double angle_z = angle_dist(rng);

            Matrix3d Rx, Ry, Rz;
            Rx << 1, 0, 0,
                0, cos(angle_x), -sin(angle_x),
                0, sin(angle_x), cos(angle_x);

            Ry << cos(angle_y), 0, sin(angle_y),
                0, 1, 0,
                -sin(angle_y), 0, cos(angle_y);

            Rz << cos(angle_z), -sin(angle_z), 0,
                sin(angle_z), cos(angle_z), 0,
                0, 0, 1;

            Rs[i] = Rz * Ry * Rx;

            // 在球面上生成随机位置
            double radius = radius_dist(rng);
            double theta = circle_dist(rng);
            double phi = acos(2.0 * std::uniform_real_distribution<double>(0, 1)(rng) - 1.0);

            ts[i](0) = radius * sin(phi) * cos(theta);
            ts[i](1) = radius * sin(phi) * sin(theta);
            ts[i](2) = radius * cos(phi);
        }
    }

    void VisualSimulator::GenerateCameraIntrinsics(
        int num_views,
        double focal_length,
        double width,
        double height,
        KMats &Ks)
    {

        Ks.resize(num_views);

        for (int i = 0; i < num_views; ++i)
        {
            Ks[i] = Matrix3d::Identity();
            Ks[i](0, 0) = focal_length;
            Ks[i](1, 1) = focal_length;
            Ks[i](0, 2) = width / 2.0;
            Ks[i](1, 2) = height / 2.0;
        }
    }

    void VisualSimulator::UpdateObsIds(Tracks &tracks)
    {
        IndexT current_obs_id = 0;
        for (auto &track_info : tracks)
        {
            // 注意：这里我们直接修改 track_info.track 中的 obs_id
            // Track 类继承自 std::vector<ObsInfo>，允许直接访问和修改其元素
            for (auto &obs : track_info.track)
            {
                obs.obs_id = current_obs_id++;
            }
        }
        PO_LOG(PO_LOG_VERBOSE) << "已按顺序更新观测ID，最大ID为 "
                               << (current_obs_id > 0 ? current_obs_id - 1 : 0) << std::endl;
    }

    std::set<IndexT> VisualSimulator::AddOutliersToTracks(Tracks &tracks, double obs_outlier_ratio, std::mt19937 &rng)
    {
        std::set<IndexT> actual_outlier_ids;
        std::uniform_real_distribution<double> dist(0, 1);
        std::uniform_real_distribution<double> noise_dist(-50, 50);

        int total_obs = 0;
        int outliers_added = 0;

        for (auto &track_info : tracks)
        {
            for (auto &obs : track_info.track)
            {
                total_obs++;

                // 以obs_outlier_ratio的概率将该观测点变为异常点
                if (dist(rng) < obs_outlier_ratio)
                {
                    // 添加大量噪声，使其成为异常点
                    obs.coord.x() += noise_dist(rng);
                    obs.coord.y() += noise_dist(rng);
                    actual_outlier_ids.insert(obs.obs_id); // 记录异常点的obs_id
                    outliers_added++;
                }
            }
        }

        PO_LOG(PO_LOG_VERBOSE) << "添加了 " << outliers_added << " 个观测点异常值，异常点比例: "
                               << (total_obs > 0 ? (static_cast<double>(outliers_added) / total_obs * 100) : 0) << "%" << std::endl;

        return actual_outlier_ids;
    }

    bool VisualSimulator::GenerateSimulatorImage(const std::string &image_path)
    {
        try
        {
            // 正确获取相机内参
            auto camera_models_ptr = GetDataPtr<CameraModels>(camera_model_ptr_);
            if (!camera_models_ptr || camera_models_ptr->empty())
            {
                PO_LOG_ERR << "无法获取相机模型数据" << std::endl;
                return false;
            }

            // 使用第一个相机的内参（仿真器中所有视图使用相同的相机）
            const CameraModel *camera_model = types::GetCameraModel(*camera_models_ptr, 0);
            if (!camera_model)
            {
                PO_LOG_ERR << "无法获取相机模型" << std::endl;
                return false;
            }

            const CameraIntrinsics &camera_intrinsics = camera_model->intrinsics;
            int width = static_cast<int>(camera_intrinsics.width);
            int height = static_cast<int>(camera_intrinsics.height);

            // 生成一个简单的PPM图像文件（P3格式）
            // 这是一种简单的图像格式，大多数图像查看器都支持
            std::string ppm_path = image_path;
            size_t ext_pos = ppm_path.find_last_of('.');
            if (ext_pos != std::string::npos)
            {
                ppm_path = ppm_path.substr(0, ext_pos) + ".ppm";
            }
            else
            {
                ppm_path += ".ppm";
            }

            std::ofstream image_file(ppm_path);
            if (image_file.is_open())
            {
                // PPM文件头
                image_file << "P3\n";
                image_file << width << " " << height << "\n";
                image_file << "255\n";

                // 生成简单的渐变图像
                for (int y = 0; y < height; ++y)
                {
                    for (int x = 0; x < width; ++x)
                    {
                        // 创建从左上角到右下角的蓝色渐变
                        int r = static_cast<int>((double)x / width * 100);  // 红色分量
                        int g = static_cast<int>((double)y / height * 100); // 绿色分量
                        int b = 200;                                        // 蓝色分量固定

                        image_file << r << " " << g << " " << b << " ";
                    }
                    image_file << "\n";
                }

                image_file.close();

                // 如果原始路径是.jpg，则创建一个符号链接或复制文件
                if (ppm_path != image_path)
                {
                    // 尝试复制文件到原始路径
                    std::ifstream src(ppm_path, std::ios::binary);
                    std::ofstream dst(image_path, std::ios::binary);
                    if (src && dst)
                    {
                        dst << src.rdbuf();
                        src.close();
                        dst.close();
                    }
                }

                PO_LOG(PO_LOG_VERBOSE) << "创建模拟图像文件: " << image_path << " (尺寸: " << width << "x" << height << ")" << std::endl;
                return true;
            }
            else
            {
                PO_LOG_ERR << "无法创建模拟图像文件: " << ppm_path << std::endl;
                return false;
            }
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "生成模拟图像时发生错误: " << e.what() << std::endl;
            return false;
        }
    }

    bool VisualSimulator::ExportToMeshlabPoSDK(const std::string &export_path)
    {
        if (export_path.empty())
        {
            return true; // 空路径，不需要导出
        }

        if (!global_poses_ptr_ || !gt_points_ptr_ || !camera_model_ptr_)
        {
            PO_LOG_ERR << "无法导出meshlab工程：数据未初始化" << std::endl;
            return false;
        }

        try
        {
            PO_LOG(PO_LOG_NORMAL) << "开始导出PoSDK风格meshlab工程到: " << export_path << std::endl;

            // 直接调用file_io.hpp中的ExportToMeshLab函数（DataPtr版本）
            bool success = file::ExportToMeshLab(
                export_path,
                global_poses_ptr_, // DataPtr for GlobalPoses
                camera_model_ptr_, // DataPtr for CameraModels
                gt_points_ptr_,    // DataPtr for WorldPointInfo
                nullptr,           // 没有真实图像，使用模拟图像
                "points.ply",      // PLY文件名
                "scene_posdk.mlp"  // 工程文件名
            );

            if (success)
            {
                PO_LOG(PO_LOG_NORMAL) << "成功导出PoSDK风格meshlab工程文件到: " << export_path << std::endl;
            }
            else
            {
                PO_LOG_ERR << "导出PoSDK风格meshlab工程失败" << std::endl;
            }

            return success;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "导出PoSDK风格meshlab工程时发生错误: " << e.what() << std::endl;
            return false;
        }
    }

} // namespace PoSDK
