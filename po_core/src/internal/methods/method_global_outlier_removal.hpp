/**
 * @file method_global_outlier_removal.hpp
 * @brief 全局特征观测异常处理框架
 * @details 支持使用不同残差模型和检测算法识别和移除全局观测中的异常值
 * @note 性能说明：在DEBUG模式下，详细的统计计算（CSV输出、方差计算等）只有在
 *       log_level >= PO_LOG_VERBOSE(2) 时才执行。建议在生产环境中使用 log_level=0
 *       以获得最佳性能。
 *
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _METHOD_GLOBAL_OUTLIER_REMOVAL_HPP_
#define _METHOD_GLOBAL_OUTLIER_REMOVAL_HPP_

#include "interfaces_preset_profiler.hpp"
#include "data/data_tracks.hpp"
#include "types.hpp"
#include <vector>
#include <string>
#include <memory>

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    /**
     * @class MethodGlobalOutlierRemoval
     * @brief 全局特征观测异常处理框架
     * @details 提供多种残差计算方法（LiGT、MedLiGT）和异常值检测方法（sigma、MAD、卡方检验、直接阈值）
     * @note 主要配置选项：
     *       - residual_method: "LiGT" 或 "MedLiGT"
     *       - detection_method: "sigma", "mad", "chi2", 或 "threshold"
     *       - angle_error_mode: false（代数误差）或 true（角度误差）
     *       - direct_threshold_*: 直接阈值检测的各种阈值配置
     */
    class MethodGlobalOutlierRemoval : public Interface::MethodPresetProfiler
    {
    public:
        MethodGlobalOutlierRemoval()
        {

            // 切记：构造函数禁止初始化任何method_options_（否则可能会覆盖configs中的配置）
            // 1. 初始化配置路径
            InitializeDefaultConfigPath();

            // 2. 设置输入数据类型需求
            required_package_["data_tracks"] = nullptr;       // 需要轨迹数据
            required_package_["data_global_poses"] = nullptr; // 需要全局位姿数据
        }

        ~MethodGlobalOutlierRemoval() override = default;

        const std::string &GetType() const override
        {
            static const std::string type = "method_global_outlier_removal";
            return type;
        }

        DataPtr Run() override;

    private:
        // 定义残差信息结构体
        struct ResidualInfo
        {
            IndexT obs_id;         // 观测的唯一ID
            double residual_value; // 计算出的残差值
        };

        /**
         * @brief 使用LiGT方法计算残差
         * @param tracks 轨迹数据
         * @param global_poses 全局位姿数据
         * @param[out] residual_info_list 计算出的残差信息列表 (包含obs_id和残差值)
         * @return 是否成功计算残差
         */
        bool ComputeLiGTResiduals(
            const Tracks &tracks,
            const GlobalPoses &global_poses,
            std::vector<ResidualInfo> &residual_info_list);

        /**
         * @brief 使用sigma规则检测异常值
         * @param residual_info_list 残差信息列表
         * @param sigma_multiplier sigma乘数
         * @param[out] outlier_obs_ids 异常观测的 obs_id 列表
         */
        void DetectOutliersBySigmaRule(
            const std::vector<ResidualInfo> &residual_info_list,
            double sigma_multiplier,
            std::vector<IndexT> &outlier_obs_ids);

        /**
         * @brief 使用MAD规则检测异常值
         * @param residual_info_list 残差信息列表
         * @param mad_multiplier MAD乘数
         * @param[out] outlier_obs_ids 异常观测的 obs_id 列表
         */
        void DetectOutliersByMADRule(
            const std::vector<ResidualInfo> &residual_info_list,
            double mad_multiplier,
            std::vector<IndexT> &outlier_obs_ids);

        /**
         * @brief 使用自适应卡方分布规则检测异常值
         * @details 通过MAD鲁棒估计残差平方范数的尺度，然后动态计算阈值。残差平方范数理论上符合2自由度卡方分布
         * @param residual_info_list 残差信息列表 (包含 obs_id 和残差平方范数)
         * @param standard_chi2_quantile 2自由度卡方分布的分位数 (例如, df=2, p=0.95 时约为 5.991)
         * @param[out] outlier_obs_ids 异常观测的 obs_id 列表
         */
        void DetectOutliersByChiSquaredRule(
            const std::vector<ResidualInfo> &residual_info_list,
            double standard_chi2_quantile,
            std::vector<IndexT> &outlier_obs_ids);

        /**
         * @brief 使用直接阈值检测异常值
         * @details 根据angle_error_mode和residual_method自动选择合适的阈值
         * @param residual_info_list 残差信息列表
         * @param outlier_obs_ids 输出的异常观测ID列表
         */
        void DetectOutliersByDirectThreshold(
            const std::vector<ResidualInfo> &residual_info_list,
            std::vector<IndexT> &outlier_obs_ids);

        /**
         * @brief 使用MedLiGT方法计算残差 (类似 RobustLiGT::RemoveObsOutliers)
         * @details 对每个观测i，将其作为左基准，找到最佳右基准j，计算所有其他观测k相对于(i,j)的残差范数，取中位数作为观测i的残差。
         * @note 在DEBUG模式下，详细的统计计算只有在log_level_ >= PO_LOG_VERBOSE(2)时才执行，以避免性能损失
         * @param tracks 轨迹数据（可修改，用于标记无效观测）
         * @param global_poses 全局位姿数据
         * @param[out] residual_info_list 计算出的残差信息列表 (包含obs_id和对应的中位数残差范数)
         * @return 是否成功计算残差
         */
        bool ComputeMedLiGTResiduals(
            Tracks &tracks,
            const GlobalPoses &global_poses,
            std::vector<ResidualInfo> &residual_info_list);

        /**
         * @brief 对轨迹数据中的异常观测进行处理
         * @param tracks 输入轨迹数据
         * @param outlier_obs_ids 异常观测的 obs_id 列表
         * @param[out] cleaned_tracks 处理后的轨迹数据
         * @param remove_outliers 是否移除异常值（true）或只是标记它们（false）
         */
        void ProcessOutliers(
            const Tracks &tracks,
            const std::vector<IndexT> &outlier_obs_ids,
            Tracks &cleaned_tracks,
            bool remove_outliers);

        /**
         * @brief 计算单个baseline pair的MedLiGT残差
         * @details 给定left和right观测作为baseline，计算其他所有观测相对于该baseline的残差，返回median值
         * @param left_idx 左基准观测在轨迹中的索引
         * @param right_idx 右基准观测在轨迹中的索引
         * @param track 当前轨迹
         * @param global_poses 全局位姿数据
         * @return 该baseline pair的median残差值，如果计算失败返回-1.0
         * @note 残差计算方式由angle_error_mode选项控制：
         *       - false（默认）：使用代数误差（平方范数）
         *       - true：使用角度误差（弧度，不平方）
         */
        double ComputeMedResidualLiGT(
            size_t left_idx,
            size_t right_idx,
            const Track &track,
            const GlobalPoses &global_poses);

#ifdef _DEBUG
        /**
         * @brief Debug模式下输出统计数据到CSV文件
         * @param all_individual_residuals 所有individual残差（暂未使用）
         * @param all_median_residuals_per_baseline_pair 所有baseline pair的median残差
         * @param all_final_residuals_per_obs 所有观测的最终残差
         * @param total_baseline_pairs 总baseline pairs数量
         * @param total_valid_tracks 总有效轨迹数量
         */
        void OutputDebugStatistics(
            const std::vector<double> &all_individual_residuals,
            const std::vector<double> &all_median_residuals_per_baseline_pair,
            const std::vector<double> &all_final_residuals_per_obs,
            size_t total_baseline_pairs,
            size_t total_valid_tracks);
#endif
    };

} // namespace PoSDK

#endif // _METHOD_GLOBAL_OUTLIER_REMOVAL_HPP_