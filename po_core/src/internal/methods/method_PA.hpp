/**
 * @file method_pa.hpp
 * @brief 基于Ceres的位姿优化方法
 * @details 使用Ceres Solver实现的Bundle Adjustment位姿优化
 */

#ifndef _METHOD_PA_
#define _METHOD_PA_

#include "interfaces_preset_profiler.hpp"
#include "types.hpp"
#include <ceres/ceres.h>

namespace PoSDK
{
    using namespace Interface;

    class MethodPA : public Interface::MethodPresetProfiler
    {
    public:
        MethodPA() : num_cameras_(0)
        {
            required_package_["data_tracks"] = nullptr;
            required_package_["data_camera_models"] = nullptr;
            required_package_["data_global_poses"] = nullptr;
            // InitializeLogDir();
        }

        ~MethodPA()
        {
            // 清理内存
            if (parameters_)
                delete[] parameters_;
            if (intrinsic_params_)
                delete[] intrinsic_params_;
            if (buffer_camera_)
                delete[] buffer_camera_;
            for (auto &obs : left_base_obs_)
                delete[] obs;
            for (auto &obs : right_base_obs_)
                delete[] obs;
        }

        const std::string &GetType() const override
        {
            static const std::string type = "method_pa";
            return type;
        }

        DataPtr Run() override;

    private:
        // 配置Ceres求解器选项
        void SetSolverOptions(::ceres::Solver::Options &options);

        // 构建BA问题
        void BuildProblem(const Tracks &tracks,
                          const CameraModels &camera_models,
                          const GlobalPoses &initial_poses,
                          ::ceres::Problem &problem);

        bool SetBaseView(const TrackInfo &track,
                         const GlobalPoses &initial_poses,
                         ViewId &left_view_id,
                         ViewId &right_view_id,
                         Vector2d &left_obs,
                         Vector2d &right_obs);

        // 优化参数
        Size num_cameras_;                   // 添加相机数量成员
        double *parameters_ = nullptr;       // 相机参数：每个相机6个参数(3旋转+3平移)
        double *intrinsic_params_ = nullptr; // 相机内参：fx, k1, k2
        double *buffer_camera_ = nullptr;    // 缓冲相机参数

        // 基准视图信息
        std::vector<ViewId> left_base_views_;  // 每个track的左基准视图
        std::vector<ViewId> right_base_views_; // 每个track的右基准视图
        std::vector<double *> left_base_obs_;  // 每个track的左基准观测
        std::vector<double *> right_base_obs_; // 每个track的右基准观测
    };

} // namespace PoSDK

#endif // _METHOD_PA_
