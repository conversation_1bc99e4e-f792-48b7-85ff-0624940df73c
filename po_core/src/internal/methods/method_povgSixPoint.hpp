/**
 * @file method_povgSixPoint.hpp
 * @brief PoVG六点算法相对位姿估计器
 * @details 调用外部povg_sixpt_algorithm程序进行鲁棒相对位姿估计
 *
 * @copyright Copyright (c) 2024 Qi Cai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _METHOD_POVG_SIXPOINT_HPP_
#define _METHOD_POVG_SIXPOINT_HPP_

#include "interfaces_preset_profiler.hpp"
#include "interfaces_robust_estimator.hpp"
#include "TwoViewOptimizer/method_TwoViewOptimizer.hpp"
#include "evaluator.hpp"
#include "types.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    /**
     * @class MethodPovgSixPoint
     * @brief PoVG六点算法方法，调用外部povg_sixpt_algorithm程序
     * @details 该方法将BearingPairs数据传递给外部程序，并读取结果
     */
    class MethodPovgSixPoint : public Interface::MethodPresetProfiler
    {
    public:
        MethodPovgSixPoint();
        ~MethodPovgSixPoint() override = default;

        /**
         * @brief 执行PoVG六点算法估计
         * @return 估计结果，包含相对位姿
         */
        DataPtr Run() override;

        /**
         * @brief 获取方法类型
         * @return 方法类型字符串
         */
        const std::string &GetType() const override
        {
            static const std::string type = "method_povgSixPoint";
            return type;
        }

    private:
        /**
         * @brief 将BearingPairs数据写入文件供外部程序使用
         * @param bearing_file 输出文件路径
         * @param bearing_pairs 观测向量对数据
         * @return 是否成功写入
         */
        bool WriteBearingInfo(const std::string &bearing_file,
                              const BearingPairs &bearing_pairs);

        /**
         * @brief 从文件中读取PoVG内点索引
         * @param inliers_file 内点文件路径
         * @param inliers 输出的内点索引
         * @param total_matches 总匹配点数量，用于在文件不存在时生成全部索引
         * @return 是否成功读取
         */
        bool LoadPovgInliers(const std::string &inliers_file,
                             std::vector<size_t> &inliers,
                             size_t total_matches);

        /**
         * @brief 从文件中读取PoVG估计位姿
         * @param pose_file 位姿文件路径
         * @param relative_pose 输出的相对位姿
         * @return 是否成功读取
         */
        bool LoadPovgPose(const std::string &pose_file,
                          RelativePose &relative_pose);

        /**
         * @brief 调用外部povg_sixpt_algorithm程序
         * @param bearing_file 输入观测文件
         * @param pose_file 输出位姿文件
         * @param inliers_file 输出内点文件
         * @return 是否成功执行
         */
        bool CallPovgSixPointAlgorithm(const std::string &bearing_file,
                                       const std::string &pose_file,
                                       const std::string &inliers_file);

        /**
         * @brief 获取临时文件的路径
         * @param prefix 文件前缀
         * @param extension 文件扩展名
         * @return 临时文件路径
         */
        std::string GetTempFilePath(const std::string &prefix,
                                    const std::string &extension);

        /**
         * @brief 清理临时文件
         * @param file_paths 要清理的文件路径列表
         */
        void CleanupTempFiles(const std::vector<std::string> &file_paths);

        /**
         * @brief 获取PoVG六点算法所需的最小样本数
         * @return 最小样本数
         */
        size_t GetMinimumSamplesForPovgSixPoint() const;

        /**
         * @brief 配置GNC-IRLS估计器参数
         * @param gnc_estimator GNC-IRLS估计器引用
         */
        void ConfigureTwoViewOptimizer(MethodTwoViewOptimizer &optimizer);
    };

} // namespace PoSDK

#endif // _METHOD_POVG_SIXPOINT_HPP_
