/**
 * @file method_povgSixPoint.cpp
 * @brief PoVG六点算法相对位姿估计器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "method_povgSixPoint.hpp"
#include "method_LiRP.hpp"
#include "relative_pose.hpp"
#include <boost/algorithm/string.hpp>
#include <fstream>
#include <iomanip>
#include <cstdlib>
#include <cmath>
#include <chrono>
#include <random>
#include <sstream>

// macOS/Ubuntu兼容性处理
#if __has_include(<filesystem>)
#include <filesystem>
namespace fs = std::filesystem;
#else
#include <experimental/filesystem>
namespace fs = std::experimental::filesystem;
#endif

namespace PoSDK
{

    MethodPovgSixPoint::MethodPovgSixPoint()
    {
        // 设置需要的输入数据包
        required_package_["data_sample"] = nullptr; // DataSample<BearingPairs>

        // 初始化默认配置路径
        InitializeDefaultConfigPath();
    }

    DataPtr MethodPovgSixPoint::Run()
    {
        DisplayConfigInfo();

        // 获取输入数据
        auto sample_ptr = required_package_.at("data_sample");
        if (!sample_ptr)
        {
            PO_LOG_ERR << "Invalid data sample" << std::endl;
            return nullptr;
        }

        // 检查匹配点对数量是否满足最低要求
        auto bearing_pairs_ptr = GetDataPtr<BearingPairs>(sample_ptr);
        if (!bearing_pairs_ptr || bearing_pairs_ptr->empty())
        {
            PO_LOG_ERR << "Empty bearing pairs data" << std::endl;
            return nullptr;
        }

        size_t total_matches = bearing_pairs_ptr->size();
        size_t min_samples = GetMinimumSamplesForPovgSixPoint();

        if (total_matches < min_samples)
        {
            PO_LOG_ERR << "Insufficient matches for PoVG SixPoint algorithm: got "
                       << total_matches << ", need at least " << min_samples << std::endl;
            return nullptr;
        }

        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "PoVG SixPoint algorithm check passed: "
                                  << "Total matches: " << total_matches
                                  << ", Min required: " << min_samples << std::endl;
        }

        // 生成临时文件路径
        std::string bearing_file = GetTempFilePath("bearing_pairs", ".txt");
        std::string pose_file = GetTempFilePath("estimated_pose", ".txt");
        std::string inliers_file = GetTempFilePath("inliers", ".txt");

        std::vector<std::string> temp_files = {bearing_file, pose_file, inliers_file};

        try
        {
            // 1. 写入bearing数据到文件
            if (!WriteBearingInfo(bearing_file, *bearing_pairs_ptr))
            {
                PO_LOG_ERR << "Failed to write bearing info to file: " << bearing_file << std::endl;
                CleanupTempFiles(temp_files);
                return nullptr;
            }

            // 2. 调用外部povg_sixpt_algorithm程序
            if (!CallPovgSixPointAlgorithm(bearing_file, pose_file, inliers_file))
            {
                PO_LOG_ERR << "Failed to execute PoVG SixPoint algorithm" << std::endl;
                CleanupTempFiles(temp_files);
                return nullptr;
            }

            // 3. 读取估计的位姿
            RelativePose estimated_pose;
            if (!LoadPovgPose(pose_file, estimated_pose))
            {
                PO_LOG_ERR << "Failed to load estimated pose from file: " << pose_file << std::endl;
                CleanupTempFiles(temp_files);
                return nullptr;
            }

            // 4. 读取内点索引
            std::vector<size_t> inliers;
            if (!LoadPovgInliers(inliers_file, inliers, total_matches))
            {
                PO_LOG_ERR << "Failed to load inliers from file: " << inliers_file << std::endl;
                CleanupTempFiles(temp_files);
                return nullptr;
            }

            // 5. 更新DataSample的内点信息
            auto sample_ptr_cast = std::dynamic_pointer_cast<DataSample<BearingPairs>>(sample_ptr);
            if (sample_ptr_cast && !inliers.empty())
            {
                auto inliers_ptr = std::make_shared<std::vector<size_t>>(inliers);
                sample_ptr_cast->SetBestInliers(inliers_ptr);

                PO_LOG(PO_LOG_NORMAL) << "PoVG SixPoint found " << inliers.size()
                                      << " inliers out of " << total_matches << " matches" << std::endl;
            }

            // 6. 检查是否需要进行两阶段优化：PoVG SixPoint + TwoViewOptimizer
            bool enable_two_stage = GetOptionAsBool("enable_two_stage_refinement", false);

            DataPtr final_result = std::make_shared<DataMap<RelativePose>>(estimated_pose);

            // 评估并显示三种pose的精度
            DataPtr gt_data = GetGTData();
            bool has_gt_data = (gt_data != nullptr);

            // 存储三种结果的误差，用于最终对比
            struct PoseAccuracy
            {
                std::string method_name;
                std::string color_code;
                double rotation_error = -1.0;
                double translation_error = -1.0;
                bool is_valid = false;
            };

            PoseAccuracy povg_accuracy{"PoVG SixPoint", "\033[32m"};     // 绿色
            PoseAccuracy lirp_accuracy{"LiRP", "\033[36m"};              // 青色
            PoseAccuracy final_accuracy{"TwoViewOptimizer", "\033[35m"}; // 紫色

            if (has_gt_data)
            {
                // 评估PoVG SixPoint的初始结果
                auto initial_result_cast = std::dynamic_pointer_cast<DataMap<RelativePose>>(final_result);
                if (initial_result_cast)
                {
                    auto eval_status = initial_result_cast->EvaluateRelativePose(gt_data);

                    if (eval_status.is_successful)
                    {
                        // 获取误差值
                        auto rotation_errors = eval_status.GetResults("rotation_error");
                        auto translation_errors = eval_status.GetResults("translation_error");

                        if (!rotation_errors.empty() && !translation_errors.empty())
                        {
                            povg_accuracy.rotation_error = rotation_errors[0];
                            povg_accuracy.translation_error = translation_errors[0];
                            povg_accuracy.is_valid = true;

                            PO_LOG(PO_LOG_NONE) << "=== 位姿精度评估 ===" << std::endl;
                            PO_LOG(PO_LOG_NONE) << "1. PoVG SixPoint 初始结果:" << std::endl;
                            PO_LOG(PO_LOG_NONE) << povg_accuracy.color_code << "  旋转误差: " << std::fixed << std::setprecision(6)
                                                << povg_accuracy.rotation_error << " (度)" << std::endl;
                            PO_LOG(PO_LOG_NONE) << povg_accuracy.color_code << "  平移误差: " << std::fixed << std::setprecision(6)
                                                << povg_accuracy.translation_error << "\033[0m" << std::endl;

                            // 添加评估结果到全局评估器
                            std::string algorithm_name = GetType();
                            std::string eval_commit = "initial_estimation";

                            // 添加评估结果到全局评估器（暂时注释测试）
                            // TODO: 重新启用评估器功能
                            // std::unordered_map<std::string, std::string> notes;
                            // notes["inliers"] = std::to_string(inliers.size());
                            // notes["total_matches"] = std::to_string(total_matches);
                            // notes["inlier_ratio"] = std::to_string(static_cast<double>(inliers.size()) / total_matches);

                            // Interface::EvaluatorManager::AddEvaluationResult(
                            //     "RelativePose", algorithm_name, eval_commit,
                            //     "rotation_error", rot_err, notes);
                            // Interface::EvaluatorManager::AddEvaluationResult(
                            //     "RelativePose", algorithm_name, eval_commit,
                            //     "translation_error", trans_err, notes);
                        }
                    }
                    else
                    {
                        PO_LOG_ERR << "PoVG SixPoint 初始结果评估失败" << std::endl;
                    }
                }
            }
            else
            {
                PO_LOG(PO_LOG_VERBOSE) << "未提供真值数据，跳过PoVG SixPoint精度评估" << std::endl;
            }

            if (enable_two_stage && !inliers.empty() && inliers.size() >= 6) // TwoViewOptimizer至少需要6个点
            {
                PO_LOG(PO_LOG_NORMAL) << "启用两阶段优化：PoVG SixPoint + LiRP + TwoViewOptimizer..." << std::endl;
                PO_LOG(PO_LOG_NORMAL) << "PoVG找到 " << inliers.size()
                                      << " 个内点，开始LiRP精确初值估计..." << std::endl;

                // 创建内点子集
                auto inlier_subset = sample_ptr_cast->GetInlierSubset(inliers);

                // 第一步：使用LiRP对内点数据进行精确求解作为初值
                RelativePose lirp_pose = estimated_pose; // 默认使用PoVG结果

                // 转换为DataSample以便调用size()方法
                auto inlier_subset_cast = CastToSample<BearingPairs>(inlier_subset);
                if (inlier_subset_cast && inlier_subset_cast->size() >= 6) // LiRP至少需要6个点
                {
                    // 创建MethodLiRP实例
                    auto lirp_solver = std::make_shared<MethodLiRP>();

                    // 设置LiRP的输入数据（内点子集）
                    auto lirp_package = std::make_shared<DataPackage>();
                    (*lirp_package)["data_sample"] = inlier_subset;
                    lirp_solver->SetRequiredData(lirp_package);

                    // 设置LiRP参数
                    MethodOptions lirp_options{
                        {"view_i", GetOptionAsString("view_i", "0")},
                        {"view_j", GetOptionAsString("view_j", "1")},
                        {"identify_mode", "PPO"}, // 使用PPO残差
                        {"compute_mode", "true"},
                        {"use_opt_mode", "false"},
                        {"use_median_cost", "true"},
                        {"log_level", std::to_string(log_level_)}};
                    lirp_solver->SetMethodOptions(lirp_options);

                    // 运行LiRP求解
                    auto lirp_result = lirp_solver->Run();

                    if (lirp_result)
                    {
                        auto lirp_pose_ptr = GetDataPtr<RelativePose>(lirp_result);
                        if (lirp_pose_ptr)
                        {
                            lirp_pose = *lirp_pose_ptr;
                            PO_LOG(PO_LOG_NORMAL) << "LiRP求解成功，使用 " << inlier_subset_cast->size()
                                                  << " 个内点得到精确初值" << std::endl;

                            // 评估LiRP结果与PoVG结果的差异
                            if (has_gt_data)
                            {
                                auto lirp_result_cast = std::dynamic_pointer_cast<DataMap<RelativePose>>(lirp_result);
                                if (lirp_result_cast)
                                {
                                    auto lirp_eval_status = lirp_result_cast->EvaluateRelativePose(gt_data);
                                    if (lirp_eval_status.is_successful)
                                    {
                                        auto lirp_rot_errors = lirp_eval_status.GetResults("rotation_error");
                                        auto lirp_trans_errors = lirp_eval_status.GetResults("translation_error");

                                        if (!lirp_rot_errors.empty() && !lirp_trans_errors.empty())
                                        {
                                            lirp_accuracy.rotation_error = lirp_rot_errors[0];
                                            lirp_accuracy.translation_error = lirp_trans_errors[0];
                                            lirp_accuracy.is_valid = true;

                                            PO_LOG(PO_LOG_NONE) << "2. LiRP 精确初值结果:" << std::endl;
                                            PO_LOG(PO_LOG_NONE) << lirp_accuracy.color_code << "  旋转误差: " << std::fixed << std::setprecision(6)
                                                                << lirp_accuracy.rotation_error << " (度)" << std::endl;
                                            PO_LOG(PO_LOG_NONE) << lirp_accuracy.color_code << "  平移误差: " << std::fixed << std::setprecision(6)
                                                                << lirp_accuracy.translation_error << "\033[0m" << std::endl;
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            PO_LOG_ERR << "LiRP结果转换失败，使用PoVG SixPoint结果作为初值" << std::endl;
                        }
                    }
                    else
                    {
                        PO_LOG_ERR << "LiRP求解失败，使用PoVG SixPoint结果作为初值" << std::endl;
                    }
                }
                else
                {
                    PO_LOG_ERR << "内点数量不足进行LiRP求解，使用PoVG SixPoint结果作为初值" << std::endl;
                }

                PO_LOG(PO_LOG_NORMAL) << "开始TwoViewOptimizer精细优化..." << std::endl;

                // 第二步：创建TwoViewOptimizer
                auto optimizer = std::make_shared<MethodTwoViewOptimizer>();

                // 设置输入数据：内点子集和LiRP初始位姿估计
                auto optimizer_package = std::make_shared<DataPackage>();
                (*optimizer_package)["data_sample"] = inlier_subset;
                (*optimizer_package)["data_map"] = std::make_shared<DataMap<RelativePose>>(lirp_pose);
                optimizer->SetRequiredData(optimizer_package);

                // 配置TwoViewOptimizer参数
                ConfigureTwoViewOptimizer(*optimizer);

                // 运行TwoViewOptimizer优化
                auto refined_result = optimizer->Run();
                if (refined_result)
                {
                    PO_LOG(PO_LOG_NORMAL) << "三阶段优化完成：PoVG SixPoint -> LiRP -> TwoViewOptimizer" << std::endl;
                    final_result = refined_result; // 使用精细优化的结果

                    // TwoViewOptimizer会将所有输入点都视为内点，因此保持原始内点信息
                    PO_LOG(PO_LOG_NORMAL) << "TwoViewOptimizer优化使用了 " << inliers.size()
                                          << " 个内点进行精细优化" << std::endl;

                    // 评估精细优化后的结果
                    if (has_gt_data)
                    {
                        auto refined_result_cast = std::dynamic_pointer_cast<DataMap<RelativePose>>(refined_result);
                        if (refined_result_cast)
                        {
                            auto eval_status = refined_result_cast->EvaluateRelativePose(gt_data);

                            if (eval_status.is_successful)
                            {
                                // 获取误差值
                                auto rotation_errors = eval_status.GetResults("rotation_error");
                                auto translation_errors = eval_status.GetResults("translation_error");

                                if (!rotation_errors.empty() && !translation_errors.empty())
                                {
                                    final_accuracy.rotation_error = rotation_errors[0];
                                    final_accuracy.translation_error = translation_errors[0];
                                    final_accuracy.is_valid = true;

                                    PO_LOG(PO_LOG_NONE) << "3. TwoViewOptimizer 最终优化结果:" << std::endl;
                                    PO_LOG(PO_LOG_NONE) << final_accuracy.color_code << "  旋转误差: " << std::fixed << std::setprecision(6)
                                                        << final_accuracy.rotation_error << " (度)" << std::endl;
                                    PO_LOG(PO_LOG_NONE) << final_accuracy.color_code << "  平移误差: " << std::fixed << std::setprecision(6)
                                                        << final_accuracy.translation_error << "\033[0m" << std::endl;

                                    // 添加评估结果到全局评估器
                                    std::string algorithm_name = GetType() + "_refined";
                                    std::string eval_commit = "refined_estimation";

                                    // 添加评估结果到全局评估器（暂时注释测试）
                                    // TODO: 重新启用评估器功能
                                    // std::unordered_map<std::string, std::string> notes;
                                    // notes["inliers"] = std::to_string(inliers.size());
                                    // notes["total_matches"] = std::to_string(total_matches);
                                    // notes["inlier_ratio"] = std::to_string(static_cast<double>(inliers.size()) / total_matches);
                                    // notes["optimizer"] = "TwoViewOptimizer";
                                    // notes["residual_type"] = GetOptionAsString("residual_type", "ppo_opengv");

                                    // Interface::EvaluatorManager::AddEvaluationResult(
                                    //     "RelativePose", algorithm_name, eval_commit,
                                    //     "rotation_error", rot_err, notes);
                                    // Interface::EvaluatorManager::AddEvaluationResult(
                                    //     "RelativePose", algorithm_name, eval_commit,
                                    //     "translation_error", trans_err, notes);
                                }
                            }
                            else
                            {
                                PO_LOG_ERR << "TwoViewOptimizer 精细优化结果评估失败" << std::endl;
                            }
                        }
                    }
                }
                else
                {
                    PO_LOG_ERR << "TwoViewOptimizer精细优化失败，使用LiRP结果" << std::endl;
                    // 如果TwoViewOptimizer失败，使用LiRP结果
                    final_result = std::make_shared<DataMap<RelativePose>>(lirp_pose);
                }
            }
            else if (enable_two_stage && inliers.size() < 6)
            {
                PO_LOG_ERR << "PoVG内点数量不足(" << inliers.size()
                           << ")，跳过TwoViewOptimizer精细优化" << std::endl;
            }

            // 显示三种方法的精度对比总结
            if (has_gt_data && (povg_accuracy.is_valid || lirp_accuracy.is_valid || final_accuracy.is_valid))
            {
                PO_LOG(PO_LOG_NONE) << "\n=== 三种方法精度对比总结 ===" << std::endl;

                // 显示旋转误差对比
                PO_LOG(PO_LOG_NONE) << "旋转误差 (度):" << std::endl;
                if (povg_accuracy.is_valid)
                    PO_LOG(PO_LOG_NONE) << "  " << povg_accuracy.color_code << povg_accuracy.method_name << ": "
                                        << std::fixed << std::setprecision(6) << povg_accuracy.rotation_error << "\033[0m" << std::endl;
                if (lirp_accuracy.is_valid)
                    PO_LOG(PO_LOG_NONE) << "  " << lirp_accuracy.color_code << lirp_accuracy.method_name << ": "
                                        << std::fixed << std::setprecision(6) << lirp_accuracy.rotation_error << "\033[0m" << std::endl;
                if (final_accuracy.is_valid)
                    PO_LOG(PO_LOG_NONE) << "  " << final_accuracy.color_code << final_accuracy.method_name << ": "
                                        << std::fixed << std::setprecision(6) << final_accuracy.rotation_error << "\033[0m" << std::endl;

                // 显示平移误差对比
                PO_LOG(PO_LOG_NONE) << "平移误差:" << std::endl;
                if (povg_accuracy.is_valid)
                    PO_LOG(PO_LOG_NONE) << "  " << povg_accuracy.color_code << povg_accuracy.method_name << ": "
                                        << std::fixed << std::setprecision(6) << povg_accuracy.translation_error << "\033[0m" << std::endl;
                if (lirp_accuracy.is_valid)
                    PO_LOG(PO_LOG_NONE) << "  " << lirp_accuracy.color_code << lirp_accuracy.method_name << ": "
                                        << std::fixed << std::setprecision(6) << lirp_accuracy.translation_error << "\033[0m" << std::endl;
                if (final_accuracy.is_valid)
                    PO_LOG(PO_LOG_NONE) << "  " << final_accuracy.color_code << final_accuracy.method_name << ": "
                                        << std::fixed << std::setprecision(6) << final_accuracy.translation_error << "\033[0m" << std::endl;

                // 计算并显示改进幅度
                if (povg_accuracy.is_valid && final_accuracy.is_valid)
                {
                    double rot_improvement = ((povg_accuracy.rotation_error - final_accuracy.rotation_error) / povg_accuracy.rotation_error) * 100.0;
                    double trans_improvement = ((povg_accuracy.translation_error - final_accuracy.translation_error) / povg_accuracy.translation_error) * 100.0;

                    PO_LOG(PO_LOG_NONE) << "\n性能改进 (相对于PoVG SixPoint):" << std::endl;
                    PO_LOG(PO_LOG_NONE) << "  旋转误差改进: " << std::fixed << std::setprecision(2) << rot_improvement << "%" << std::endl;
                    PO_LOG(PO_LOG_NONE) << "  平移误差改进: " << std::fixed << std::setprecision(2) << trans_improvement << "%" << std::endl;
                }

                PO_LOG(PO_LOG_NONE) << "=========================" << std::endl;
            }

            // 清理临时文件
            CleanupTempFiles(temp_files);

            // 显示最终结果信息
            auto final_pose = GetDataPtr<RelativePose>(final_result);
            if (final_pose)
            {
                PO_LOG(PO_LOG_NONE) << "PoVG SixPoint estimation completed successfully" << std::endl;
                if (log_level_ >= PO_LOG_NORMAL)
                {
                    PO_LOG(PO_LOG_NORMAL) << "Final Rotation matrix: \n"
                                          << final_pose->Rij << std::endl;
                    PO_LOG(PO_LOG_NORMAL) << "Final Translation vector: \n"
                                          << final_pose->tij << std::endl;
                }
            }

            return final_result;
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Exception in PoVG SixPoint processing: " << e.what() << std::endl;
            CleanupTempFiles(temp_files);
            return nullptr;
        }
    }

    bool MethodPovgSixPoint::WriteBearingInfo(const std::string &bearing_file,
                                              const BearingPairs &bearing_pairs)
    {
        std::ofstream file(bearing_file);
        if (!file.is_open())
        {
            PO_LOG_ERR << "Cannot create bearing file: " << bearing_file << std::endl;
            return false;
        }

        // 写入匹配点数量
        file << bearing_pairs.size() << std::endl;

        // 写入每个匹配点对的观测向量 (6个值：v1.x v1.y v1.z v2.x v2.y v2.z)
        file << std::setprecision(16);
        for (const auto &bearing_pair : bearing_pairs)
        {
            // bearing_pair是6x1向量，前3个是第一个观测向量，后3个是第二个观测向量
            file << bearing_pair(0) << " " << bearing_pair(1) << " " << bearing_pair(2) << " "
                 << bearing_pair(3) << " " << bearing_pair(4) << " " << bearing_pair(5) << std::endl;
        }

        file.close();

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "Written " << bearing_pairs.size()
                                   << " bearing pairs to file: " << bearing_file << std::endl;
        }

        return true;
    }

    bool MethodPovgSixPoint::LoadPovgInliers(const std::string &inliers_file,
                                             std::vector<size_t> &inliers,
                                             size_t total_matches)
    {
        inliers.clear();

        // 检查inliers文件是否存在
        if (!fs::exists(inliers_file))
        {
            // 如果inliers文件不存在，将所有匹配点对都认为是inliers
            PO_LOG(PO_LOG_NORMAL) << "Inliers file not found: " << inliers_file
                                  << ", treating all " << total_matches << " matches as inliers" << std::endl;

            for (size_t i = 0; i < total_matches; ++i)
            {
                inliers.push_back(i);
            }

            return true;
        }

        std::ifstream file(inliers_file);
        if (!file.is_open())
        {
            PO_LOG_ERR << "Cannot open inliers file: " << inliers_file << std::endl;
            return false;
        }

        int inlier_idx;
        while (file >> inlier_idx)
        {
            if (inlier_idx >= 0)
            {
                inliers.push_back(static_cast<size_t>(inlier_idx));
            }
        }

        file.close();

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "Loaded " << inliers.size()
                                   << " inliers from file: " << inliers_file << std::endl;
        }

        return !inliers.empty();
    }

    bool MethodPovgSixPoint::LoadPovgPose(const std::string &pose_file,
                                          RelativePose &relative_pose)
    {
        std::ifstream file(pose_file);
        if (!file.is_open())
        {
            PO_LOG_ERR << "Cannot open pose file: " << pose_file << std::endl;
            return false;
        }

        // 读取旋转矩阵 (注意：povg格式是转置存储的)
        Matrix3d rotation_matrix;
        for (int i = 0; i < 3; ++i)
        {
            for (int j = 0; j < 3; ++j)
            {
                file >> rotation_matrix(j, i); // 注意这里使用了转置读取
            }
        }

        // 读取平移向量
        Vector3d translation_vector;
        for (int i = 0; i < 3; ++i)
        {
            file >> translation_vector(i);
        }

        file.close();

        // 设置相对位姿
        relative_pose.Rij = rotation_matrix;
        relative_pose.tij = translation_vector;
        relative_pose.i = GetOptionAsIndexT("view_i", 0);
        relative_pose.j = GetOptionAsIndexT("view_j", 1);
        relative_pose.weight = 1.0f;

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "Loaded pose from file: " << pose_file << std::endl;
        }

        return true;
    }

    bool MethodPovgSixPoint::CallPovgSixPointAlgorithm(const std::string &bearing_file,
                                                       const std::string &pose_file,
                                                       const std::string &inliers_file)
    {
        // 获取配置参数
        std::string executable_path = GetOptionAsString("povg_bin_path", "");
        std::string solve_mode = GetOptionAsString("solve_mode", "gncransac_6pts_LiGT");

        if (executable_path.empty())
        {
            PO_LOG_ERR << "PoVG executable path not specified. Please set 'povg_bin_path' in configuration." << std::endl;
            return false;
        }

        // 检查可执行文件是否存在
        if (!fs::exists(executable_path))
        {
            PO_LOG_ERR << "PoVG executable not found at: " << executable_path << std::endl;
            return false;
        }

        // 生成时间文件路径
        std::string time_file = GetTempFilePath("time_output", ".csv");

        // 构建命令行参数（基于sixpt_algorithm.cpp的DEFINE参数）
        std::ostringstream command;
        command << executable_path
                << " --solve_mode=" << solve_mode
                << " --bearing_vector_file=" << bearing_file
                << " --output_file=" << pose_file
                << " --inliers_file=" << inliers_file
                << " --time_file=" << time_file;

        // 添加eigen_sols_file参数（即使不使用也需要提供以避免加载错误）
        std::string eigen_sols_file = GetTempFilePath("eigen_sols", ".txt");

        // 创建一个空的eigen sols文件避免加载错误
        std::ofstream eigen_file(eigen_sols_file);
        if (eigen_file.is_open())
        {
            // 写入一个最小的有效格式（3x9矩阵的头部）
            eigen_file << "3 9" << std::endl;
            for (int i = 0; i < 3; ++i)
            {
                for (int j = 0; j < 9; ++j)
                {
                    eigen_file << "0.0 ";
                }
                eigen_file << std::endl;
            }
            eigen_file.close();
        }

        command << " --eigenvec_sols_file=" << eigen_sols_file;

        // 添加其他配置参数
        double inlier_threshold = GetOptionAsFloat("inlier_threshold", 5e-3);
        IndexT sigma_mode = GetOptionAsIndexT("sigma_mode", 0);
        IndexT max_gncransac_niter = GetOptionAsIndexT("max_gncransac_niter", 50);
        IndexT gnc_samplesize = GetOptionAsIndexT("gnc_samplesize", 20);
        bool computeMode = GetOptionAsBool("computeMode", true);
        bool med_or_sum = GetOptionAsBool("med_or_sum", true);
        std::string identify_mode = GetOptionAsString("identify_mode", "PPO");
        std::string gnc_residual_func = GetOptionAsString("gnc_residual_func", "residual_PPO");
        std::string gnc_dist_func = GetOptionAsString("gnc_dist_func", "residual_PPO");

        command
            // << " --inlier_threhold=" << inlier_threshold // 注意：原代码中拼写错误为threhold
            << " --sigma_mode=" << sigma_mode
            // << " --max_gncransac_niter=" << max_gncransac_niter
            // << " --gnc_samplesize=" << gnc_samplesize
            // << " --computeMode=" << (computeMode ? "true" : "false")
            // << " --med_or_sum=" << (med_or_sum ? "true" : "false")
            // << " --identify_mode=" << identify_mode
            // << " --gnc_residual_func=" << gnc_residual_func
            // << " --gnc_dist_func=" << gnc_dist_func
            ;

        std::string cmd_str = command.str();

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "Executing command: " << cmd_str << std::endl;
        }

        // 执行外部程序
        int result = std::system(cmd_str.c_str());

        // 检查输出文件是否生成（这比检查退出码更可靠）
        bool pose_exists = fs::exists(pose_file);

        if (!pose_exists)
        {
            PO_LOG_ERR << "PoVG SixPoint algorithm did not generate expected pose file" << std::endl;
            PO_LOG_ERR << "Pose file exists: " << pose_exists << std::endl;
            PO_LOG_ERR << "Exit code: " << result << std::endl;
            return false;
        }

        // 警告非零退出码，但如果文件存在就继续（程序可能因为eigen sols加载失败而返回非0，但算法仍然成功）
        if (result != 0)
        {
            PO_LOG_ERR << "PoVG SixPoint algorithm returned non-zero exit code: " << result
                       << ", but output files were generated. Continuing..." << std::endl;
        }

        // 清理eigen sols文件和时间文件
        try
        {
            if (fs::exists(eigen_sols_file))
            {
                fs::remove(eigen_sols_file);
            }
            if (fs::exists(time_file))
            {
                fs::remove(time_file);
            }
        }
        catch (const std::exception &e)
        {
            PO_LOG_ERR << "Failed to cleanup additional temp files: " << e.what() << std::endl;
        }

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "PoVG SixPoint algorithm executed successfully" << std::endl;
        }

        return true;
    }

    std::string MethodPovgSixPoint::GetTempFilePath(const std::string &prefix,
                                                    const std::string &extension)
    {
        // 获取临时目录
        std::string temp_dir = GetOptionAsString("temp_dir", "/tmp");

        // 生成唯一文件名
        auto now = std::chrono::high_resolution_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(now.time_since_epoch()).count();

        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(1000, 9999);
        int random_num = dis(gen);

        std::ostringstream filename;
        filename << temp_dir << "/" << prefix << "_" << timestamp << "_" << random_num << extension;

        return filename.str();
    }

    void MethodPovgSixPoint::CleanupTempFiles(const std::vector<std::string> &file_paths)
    {
        for (const auto &file_path : file_paths)
        {
            try
            {
                if (fs::exists(file_path))
                {
                    fs::remove(file_path);

                    if (log_level_ >= PO_LOG_VERBOSE)
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "Cleaned up temp file: " << file_path << std::endl;
                    }
                }
            }
            catch (const std::exception &e)
            {
                PO_LOG_ERR << "Failed to cleanup temp file " << file_path << ": " << e.what() << std::endl;
            }
        }
    }

    size_t MethodPovgSixPoint::GetMinimumSamplesForPovgSixPoint() const
    {
        // PoVG六点算法至少需要6个点对
        return 6;
    }

    void MethodPovgSixPoint::ConfigureTwoViewOptimizer(MethodTwoViewOptimizer &optimizer)
    {
        // 配置TwoViewOptimizer的基本参数
        MethodOptions optimizer_options{
            {"optimizer_type", GetOptionAsString("optimizer_type", "eigen_lm")},
            {"max_iterations", std::to_string(GetOptionAsIndexT("max_iterations", 50))},
            {"convergence_threshold", std::to_string(GetOptionAsFloat("convergence_threshold", 1e-8))},
            {"residual_type", GetOptionAsString("optimizer_coster", "ppo_opengv")}, // 内部仍使用residual_type
            {"loss_type", GetOptionAsString("loss_type", "huber")},
            {"huber_threshold", std::to_string(GetOptionAsFloat("huber_threshold", 0.01f))},
            {"view_i", GetOptionAsString("view_i", "0")},
            {"view_j", GetOptionAsString("view_j", "1")},
            {"log_level", std::to_string(log_level_)},
            {"enable_profiling", GetOptionAsString("enable_profiling", "false")},

            // Eigen LM参数
            {"eigen_lm_ftol", std::to_string(GetOptionAsFloat("eigen_lm_ftol", 1e-8f))},
            {"eigen_lm_xtol", std::to_string(GetOptionAsFloat("eigen_lm_xtol", 1e-8f))},
            {"eigen_lm_maxfev", std::to_string(GetOptionAsIndexT("eigen_lm_maxfev", 500))},
        };

        optimizer.SetMethodOptions(optimizer_options);

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "TwoViewOptimizer配置完成：使用 "
                                   << GetOptionAsString("optimizer_coster", "ppo_opengv")
                                   << " 残差函数和 "
                                   << GetOptionAsString("loss_type", "huber")
                                   << " 损失函数" << std::endl;
        }
    }

} // namespace PoSDK