/**
 * @file method_relative_cost.cpp
 * @brief 相对位姿残差评估器实现
 *
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#include "methods/method_relative_cost.hpp"
#include "interfaces_robust_estimator.hpp"
#include <unordered_map>
#include <iostream>
#include <functional>
#include <boost/algorithm/string.hpp>
#include "relative_residuals.hpp"

namespace PoSDK
{

    // 使用BearingPairs类型定义
    using BearingPairs = std::vector<Matrix<double, 6, 1>>;

    MethodRelativeCost::MethodRelativeCost()
    {
        // 1. 加载通用配置
        InitializeDefaultConfigPath();

        // 2. 设置输入和输出数据类型需求
        required_package_["data_sample"] = nullptr; // 需要样本数据， DataSample<BearingPairs>

        required_package_["data_map"] = nullptr; // 需要相对位姿数据
    }

    DataPtr MethodRelativeCost::Run()
    {
        try
        {
            DisplayConfigInfo();
            // 1. 获取样本数据和位姿数据
            auto sample_data = required_package_["data_sample"];
            auto pose_data = required_package_["data_map"];

            if (!sample_data || !pose_data)
            {
                std::cerr << "Missing required input data" << std::endl;
                return nullptr;
            }

            // 2. 提取相对位姿
            // 使用GetDataPtr函数简化类型转换
            auto rel_pose_ptr = GetDataPtr<RelativePose>(pose_data);
            if (!rel_pose_ptr)
            {
                std::cerr << "Failed to get RelativePose data: input data is not of RelativePose type" << std::endl;
                return nullptr;
            }
            RelativePose pose = *rel_pose_ptr;

            // 3. 提取bearing向量对
            BearingVectors points1, points2;
            if (!ExtractBearingVectors(points1, points2))
            {
                std::cerr << "Failed to extract bearing vectors" << std::endl;
                return nullptr;
            }

            // 4. 获取配置选项
            const std::string residual_type =
                GetOptionAsString("residual_type", "sampson");
            const bool use_weights =
                GetOptionAsBool("use_weights", false);

            // 5. 检查是否有权重信息
            VectorXd *weights_ptr = nullptr;
            VectorXd weights;

            // 检查先验信息中是否包含权重
            if (use_weights && !prior_info_.empty() && prior_info_.find("weights") != prior_info_.end())
            {
                auto weights_data = std::dynamic_pointer_cast<DataMap<VectorXd>>(prior_info_["weights"]);
                if (weights_data && weights_data->GetMapPtr())
                {
                    // 获取权重向量的指针
                    weights = *(weights_data->GetMapPtr());
                    weights_ptr = &weights;

                    PO_LOG(PO_LOG_VERBOSE) << "Using weights from prior_info_" << std::endl;
                }
            }

            // 6. 计算残差
            VectorXd residuals = ComputeResiduals(points1, points2, pose, weights_ptr);

            // 7. 创建并返回DataCosts对象
            auto costs = std::make_shared<DataCosts>();
            for (int i = 0; i < residuals.size(); ++i)
            {
                costs->push_back(residuals(i));
            }

            return costs;
        }
        catch (const std::exception &e)
        {
            std::cerr << "MethodRelativeCost::Run() failed: " << e.what() << std::endl;
            return nullptr;
        }
    }

    VectorXd MethodRelativeCost::ComputeResiduals(
        const BearingVectors &v1,
        const BearingVectors &v2,
        const RelativePose &pose,
        const VectorXd *weights)
    {

        // 获取残差类型
        const std::string residual_type =
            GetOptionAsString("residual_type", "sampson");

        // 使用switch-case来替代函数映射表，避免类型转换问题
        if (boost::iequals(residual_type, "coplanar"))
        {
            return residual_coplanar(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "sampson"))
        {
            return residual_sampson(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "kneip"))
        {
            return residual_Kneip(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ba"))
        {
            return residual_BA(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "opengv"))
        {
            return residual_opengv(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ppo"))
        {
            return residual_PPO(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ppo_opengv"))
        {
            return residual_ppo_opengv(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ppog"))
        {
            return residual_PPOG(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ppo_invd"))
        {
            return residual_PPO_invd(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ppo_bvc_invd"))
        {
            return residual_PPO_bvc_invd(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ppo_bva_invd"))
        {
            return residual_PPO_bva_invd(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ligt_direct"))
        {
            return residual_LiGT_direct(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ligt_d3"))
        {
            return residual_LiGT_d3(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "ligt"))
        {
            return residual_LiGT(v1, v2, pose, weights);
        }
        else if (boost::iequals(residual_type, "lirt"))
        {
            return residual_LiRT(v1, v2, pose, weights);
        }
        else
        {
            std::cerr << "Unknown residual type: " << residual_type
                      << ", falling back to sampson" << std::endl;
            return residual_sampson(v1, v2, pose, weights);
        }
    }

    bool MethodRelativeCost::ExtractBearingVectors(
        BearingVectors &points1,
        BearingVectors &points2)
    {

        try
        {
            // 获取样本数据
            auto sample_data = required_package_["data_sample"];
            if (!sample_data)
            {
                return false;
            }

            // 尝试将数据转换为BearingPairs
            auto bearing_pairs_ptr = std::dynamic_pointer_cast<DataSample<BearingPairs>>(sample_data);
            if (bearing_pairs_ptr)
            {
                // 使用STL兼容接口获取正确的样本数量（支持子集模式）
                const int num_pairs = static_cast<int>(bearing_pairs_ptr->size());
                points1.resize(3, num_pairs);
                points2.resize(3, num_pairs);

                // 使用STL兼容接口遍历样本（自动处理子集索引映射）
                for (int i = 0; i < num_pairs; ++i)
                {
                    // 使用operator[]访问，自动处理子集索引
                    const auto &bearing_pair = (*bearing_pairs_ptr)[i];

                    Vector3d p1 = bearing_pair.head<3>();
                    Vector3d p2 = bearing_pair.tail<3>();

                    // 确保向量是单位向量
                    p1.normalize();
                    p2.normalize();

                    // 将向量存储到对应列
                    points1.col(i) = p1;
                    points2.col(i) = p2;
                }

                return true;
            }

            // 如果不是BearingPairs，检查是否可以直接获取两组向量
            // 这部分取决于样本数据的实际存储方式，可能需要进一步调整

            return false;
        }
        catch (const std::exception &e)
        {
            std::cerr << "Failed to extract bearing vectors: " << e.what() << std::endl;
            return false;
        }
    }

} // namespace PoSDK
