#ifndef _POMVG_INTERFACE_PRESET_PROFILER_
#define _POMVG_INTERFACE_PRESET_PROFILER_

#include "interfaces_preset.hpp"
#include "evaluator.hpp"
#include <chrono>
#include <unordered_map>
#include <memory>
#include <string>
#include <fstream>
#include <iomanip>
#include <ctime>

// 系统相关头文件
#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#else
#include <sys/time.h>
#include <sys/resource.h>
#include <unistd.h>
#endif

namespace PoSDK
{
    namespace Interface
    {

        class MethodPresetProfiler : public MethodPreset
        {
        public:
            // 继承 MethodPreset 的纯虚函数
            virtual const std::string &GetType() const override = 0;
            virtual DataPtr Run() = 0;

            struct ProfileInfo
            {
                std::string method_type;                               // 方法类型
                std::string timestamp;                                 // 执行时间戳
                std::string profile_commit;                            // 配置说明/标签
                size_t peak_memory_usage = 0;                          // 峰值内存使用(bytes)
                size_t current_memory_usage = 0;                       // 当前内存使用(bytes)
                double total_time_ms = 0.0;                            // 总执行时间(ms)
                double compute_time_ms = 0.0;                          // 计算时间(ms)
                double data_process_time_ms = 0.0;                     // 数据处理时间(ms)
                double core_time_ms = 0.0;                             // 核心计算时间(ms)，不包括数据转换等辅助操作
                std::unordered_map<std::string, double> stage_timings; // 各阶段耗时
            };

            // 构造函数
            MethodPresetProfiler() : enable_profiling_(true), enable_evaluator_(false), algorithm_("")
            {
                // 使用默认日志目录
                std::filesystem::path default_log_dir = GetLogDir();
                if (!std::filesystem::exists(default_log_dir))
                {
                    std::filesystem::create_directories(default_log_dir);
                }

                // 初始化配置说明选项
                if (method_options_.find("ProfileCommit") == method_options_.end())
                {
                    method_options_["ProfileCommit"] = ""; // 默认为空
                }
            }

            // 析构函数
            virtual ~MethodPresetProfiler() = default;

            DataPtr Build(const DataPtr &material_ptr = nullptr) override
            {
                // 读取配置选项
                enable_profiling_ = GetOptionAsBool("enable_profiling", true);
                enable_evaluator_ = GetOptionAsBool("enable_evaluator", false);

                // 处理不同的选项组合
                if (!enable_profiling_ && !enable_evaluator_)
                {
                    // 两个都关闭，直接调用基类Build
                    return MethodPreset::Build(material_ptr);
                }

                if (!enable_profiling_ && enable_evaluator_)
                {
                    // 关闭性能分析但开启评估
                    InitializeLogDir();

                    // 初始化current_profile_以支持核心时间统计
                    current_profile_.method_type = GetType();
                    current_profile_.timestamp = GetCurrentTimestamp();
                    current_profile_.profile_commit = method_options_.count("ProfileCommit") > 0 ? method_options_.at("ProfileCommit") : "default_commit";
                    current_profile_.core_time_ms = 0.0; // 初始化核心时间

                    // 调用基类Build获取结果
                    DataPtr result = MethodPreset::Build(material_ptr);

                    // 进行评估
                    if (result)
                    {
                        bool evaluation_success = CallEvaluator(result);
                        if (!evaluation_success)
                        {
                            std::cerr << "[" << GetType() << "] Warning: Evaluation failed" << std::endl;
                        }
                    }

                    return result;
                }

                // 如果启用性能分析，则需要初始化日志目录
                InitializeLogDir();

                // 以下是原有的性能分析逻辑（可能结合评估）
                ProfileInfo profile;

                profile.method_type = GetType();
                profile.timestamp = GetCurrentTimestamp();
                profile.profile_commit = method_options_["ProfileCommit"]; // 获取配置说明

                auto start_time = std::chrono::high_resolution_clock::now();

                // 记录初始内存使用
                UpdateMemoryUsage(profile);

                try
                {
                    DataPtr result = nullptr;

                    if (enable_evaluator_)
                    {
                        // 开启评估时，需要执行完整的检查和运行流程
                        if (!CheckInput(material_ptr))
                        {
                            std::cerr << "Error in " << GetType()
                                      << ": Input check failed" << std::endl;
                            return nullptr;
                        }

                        auto check_end = std::chrono::high_resolution_clock::now();
                        profile.stage_timings["input_check"] =
                            std::chrono::duration<double, std::milli>(check_end - start_time).count();

                        // 执行实际计算
                        auto compute_start = std::chrono::high_resolution_clock::now();
                        result = Run();
                        auto compute_end = std::chrono::high_resolution_clock::now();

                        // 记录计算时间
                        profile.compute_time_ms =
                            std::chrono::duration<double, std::milli>(compute_end - compute_start).count();

                        // 进行评估
                        if (result)
                        {
                            bool evaluation_success = CallEvaluator(result);
                            if (!evaluation_success)
                            {
                                std::cerr << "[" << GetType() << "] Warning: Evaluation failed during profiling" << std::endl;
                            }
                        }
                    }
                    else
                    {
                        // 不开启评估时，执行原有的性能分析逻辑
                        if (!CheckInput(material_ptr))
                        {
                            std::cerr << "Error in " << GetType()
                                      << ": Input check failed" << std::endl;
                            return nullptr;
                        }

                        auto check_end = std::chrono::high_resolution_clock::now();
                        profile.stage_timings["input_check"] =
                            std::chrono::duration<double, std::milli>(check_end - start_time).count();

                        // 执行实际计算
                        auto compute_start = std::chrono::high_resolution_clock::now();
                        result = Run();
                        auto compute_end = std::chrono::high_resolution_clock::now();

                        // 记录计算时间
                        profile.compute_time_ms =
                            std::chrono::duration<double, std::milli>(compute_end - compute_start).count();
                    }

                    // 更新峰值内存使用
                    UpdateMemoryUsage(profile);

                    // 记录总时间
                    auto end_time = std::chrono::high_resolution_clock::now();
                    profile.total_time_ms =
                        std::chrono::duration<double, std::milli>(end_time - start_time).count();

                    // 输出性能报告
                    PrintProfileReport(profile);

                    // 导出性能数据
                    ExportToCSV(profile);

                    return result;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Error in " << GetType() << ": " << e.what() << std::endl;
                    return nullptr;
                }
            }

            // 设置日志目录
            void SetLogDir(const std::filesystem::path &dir)
            {
                log_dir_ = dir;
                // 确保目录存在
                if (!std::filesystem::exists(log_dir_))
                {
                    std::filesystem::create_directories(log_dir_);
                }
            }

            /**
             * @brief 设置评估器中使用的算法名称
             * @param algorithm_name 算法名称
             * @details 用于在评估器中区分不同的算法配置，如果不设置则默认使用GetType()的值
             */
            void SetEvaluatorAlgorithm(const std::string &algorithm_name) override
            {
                algorithm_ = algorithm_name;
                std::cout << "[" << GetType() << "] Set evaluator algorithm to: " << algorithm_name << std::endl;
            }

            /**
             * @brief 设置核心计算时间
             * @param core_time_ms 核心计算时间（毫秒）
             */
            void SetCoreTime(double core_time_ms)
            {
                current_profile_.core_time_ms = core_time_ms;
            }

            /**
             * @brief 获取核心计算时间
             * @return 核心计算时间（毫秒）
             */
            double GetCoreTime() const override
            {
                return current_profile_.core_time_ms;
            }

            // 添加初始化函数，在派生类构造完成后调用
            void InitializeLogDir()
            {
                // 现在可以安全地使用 GetType()
                if (log_dir_.empty())
                {
                    log_dir_ = GetType() + "_performance_log";
                    if (!std::filesystem::exists(log_dir_))
                    {
                        std::filesystem::create_directories(log_dir_);
                    }
                }
            }

            /**
             * @brief 调用评估器进行评估
             * @param result_data 待评估的结果数据
             * @details 该函数会拷贝输入数据，检查真值数据，并调用Evaluate函数进行评估
             *          支持单个数据对象和DataPackage两种情况的评估
             *          具体的评估逻辑和结果添加由DataIO::Evaluate函数实现
             * @return 是否成功调用评估
             */
            bool CallEvaluator(const DataPtr &result_data)
            {
                if (!result_data)
                {
                    std::cerr << "[" << GetType() << "] Error: Result data is null" << std::endl;
                    return false;
                }

                try
                {
                    // 步骤1: 拷贝数据
                    DataPtr copied_data = result_data->CopyData();
                    if (!copied_data)
                    {
                        std::cerr << "[" << GetType() << "] Error: Failed to copy result data " << result_data->GetType() << std::endl;
                        return false;
                    }

                    // 步骤2: 检查prior_info_中是否有gt_data类型的data_ptr
                    bool found_any_gt_data = false;
                    bool evaluation_success = false;

                    for (const auto &[key, gt_data_ptr] : prior_info_)
                    {
                        if (key == "gt_data" && gt_data_ptr)
                        {
                            found_any_gt_data = true;

                            // 步骤3: 处理不同的数据组合情况
                            if (gt_data_ptr->GetType() == "data_package" && copied_data->GetType() == "data_package")
                            {
                                // 情况1: 两者都是DataPackage
                                if (CallEvaluateDataPackageToDataPackage(gt_data_ptr, copied_data))
                                {
                                    evaluation_success = true;
                                }
                            }
                            else if (gt_data_ptr->GetType() == "data_package" && copied_data->GetType() != "data_package")
                            {
                                // 情况2: GT是DataPackage，Result是单个数据对象
                                if (CallEvaluateDataPackageToSingleData(gt_data_ptr, copied_data))
                                {
                                    evaluation_success = true;
                                }
                            }
                            else if (gt_data_ptr->GetType() != "data_package" && copied_data->GetType() == "data_package")
                            {
                                // 情况3: GT是单个数据对象，Result是DataPackage
                                if (CallEvaluateSingleDataToDataPackage(gt_data_ptr, copied_data))
                                {
                                    evaluation_success = true;
                                }
                            }
                            else
                            {
                                // 情况4: 两者都是单个数据对象
                                if (CallEvaluateSingleDataToSingleData(gt_data_ptr, copied_data))
                                {
                                    evaluation_success = true;
                                }
                            }
                        }
                    }

                    if (!found_any_gt_data)
                    {
                        std::cerr << "[" << GetType() << "] Warning: No GT data found for evaluation" << std::endl;
                        return false;
                    }

                    return evaluation_success;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[" << GetType() << "] Error in CallEvaluator: " << e.what() << std::endl;
                    return false;
                }
            }

        protected:
            bool enable_profiling_;         // 控制是否启用性能分析
            bool enable_evaluator_;         // 控制是否启用评估
            std::filesystem::path log_dir_; // 日志目录
            std::string algorithm_;         // 评估器中使用的算法名称，默认为GetType()的值
            ProfileInfo current_profile_;   // 当前性能分析信息

            // 获取日志目录路径
            virtual std::filesystem::path GetLogDir() const
            {
                if (!log_dir_.empty())
                {
                    return log_dir_;
                }
                // 默认使用 "performance_log" 目录
                return "performance_log";
            }

            // 获取CSV文件路径
            virtual std::filesystem::path GetCSVPath() const
            {
                return GetLogDir() / "method_performance.csv";
            }

            virtual void UpdateMemoryUsage(ProfileInfo &profile)
            {
#ifdef _WIN32
                PROCESS_MEMORY_COUNTERS pmc;
                if (GetProcessMemoryInfo(GetCurrentProcess(), &pmc, sizeof(pmc)))
                {
                    profile.current_memory_usage = pmc.WorkingSetSize;
                    profile.peak_memory_usage = std::max(profile.peak_memory_usage,
                                                         profile.current_memory_usage);
                }
#else
                // Linux系统下获取内存使用
                struct rusage usage;
                if (getrusage(RUSAGE_SELF, &usage) == 0)
                {
                    profile.current_memory_usage = usage.ru_maxrss * 1024; // 转换为bytes
                    profile.peak_memory_usage = std::max(profile.peak_memory_usage,
                                                         profile.current_memory_usage);
                }
#endif
            }

            /**
             * @brief 打印性能分析报告
             * @param profile 性能分析信息
             * @deprecated 此函数已弃用，时间统计现在统一到评估器系统中
             */
            [[deprecated("Use EvaluatorManager time statistics instead")]]
            virtual void PrintProfileReport(const ProfileInfo &profile)
            {
                std::cout << "\n=== Performance Profile for " << GetType() << " ===\n"
                          << "Timestamp: " << profile.timestamp << "\n"
                          << "Description: " << (profile.profile_commit.empty() ? "(no description)" : profile.profile_commit) << "\n"
                          << "Total Time: " << profile.total_time_ms << " ms\n"
                          << "Compute Time: " << profile.compute_time_ms << " ms\n"
                          << "Core Time: " << profile.core_time_ms << " ms\n"
                          << "Peak Memory Usage: " << (profile.peak_memory_usage / 1024.0 / 1024.0) << " MB\n"
                          << "Current Memory Usage: " << (profile.current_memory_usage / 1024.0 / 1024.0) << " MB\n"
                          << "\nStage Timings:\n";

                for (const auto &[stage, time] : profile.stage_timings)
                {
                    std::cout << "  " << stage << ": " << time << " ms\n";
                }
                std::cout << "=====================================\n"
                          << std::endl;
            }

            std::string GetCurrentTimestamp()
            {
                auto now = std::chrono::system_clock::now();
                auto now_c = std::chrono::system_clock::to_time_t(now);
                std::stringstream ss;
                ss << std::put_time(std::localtime(&now_c), "%Y%m%d_%H%M%S");
                return ss.str();
            }

            /**
             * @brief 导出性能分析数据到CSV文件
             * @param profile 性能分析信息
             * @deprecated 此函数已弃用，时间统计现在统一到评估器系统中，使用EvaluatorManager::ExportTimeStatisticsToCSV
             */
            [[deprecated("Use EvaluatorManager::ExportTimeStatisticsToCSV instead")]]
            void ExportToCSV(const ProfileInfo &profile)
            {
                std::filesystem::path csv_path = GetCSVPath();
                bool file_exists = std::filesystem::exists(csv_path);

                std::ofstream csv_file(csv_path, std::ios::app);

                // 如果是新文件，写入表头
                if (!file_exists)
                {
                    csv_file << "Timestamp,Method,Description,TotalTime(ms),ComputeTime(ms),CoreTime(ms),"
                             << "PeakMemory(MB),CurrentMemory(MB)";

                    // 添加所有可能的阶段名称
                    for (const auto &[stage, _] : profile.stage_timings)
                    {
                        csv_file << "," << stage << "(ms)";
                    }
                    csv_file << "\n";
                }

                // 写入数据行
                csv_file << profile.timestamp << ","
                         << profile.method_type << ","
                         << "\"" << profile.profile_commit << "\"" << ","
                         << std::fixed << std::setprecision(2)
                         << profile.total_time_ms << ","
                         << profile.compute_time_ms << ","
                         << profile.core_time_ms << ","
                         << (profile.peak_memory_usage / 1024.0 / 1024.0) << ","
                         << (profile.current_memory_usage / 1024.0 / 1024.0);

                // 写入各阶段耗时
                for (const auto &[stage, time] : profile.stage_timings)
                {
                    csv_file << "," << time;
                }
                csv_file << "\n";

                csv_file.close();
            }

        private:
            /**
             * @brief 调用单个数据对象到单个数据对象的评估
             */
            bool CallEvaluateSingleDataToSingleData(const DataPtr &gt_data, const DataPtr &result_data)
            {
                try
                {
                    // 调用result_data的Evaluate方法
                    EvaluatorStatus eval_status = result_data->Evaluate(gt_data);

                    if (eval_status.is_successful)
                    {
                        // 处理评估结果：将EvaluatorStatus的结果添加到全局评估器
                        return ProcessEvaluationResults(eval_status);
                    }
                    else
                    {
                        std::cerr << "[" << GetType() << "] Evaluation failed for single data to single data" << std::endl;
                        return false;
                    }
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[" << GetType() << "] Error in CallEvaluateSingleDataToSingleData: " << e.what() << std::endl;
                    return false;
                }
            }

            /**
             * @brief 调用数据包到数据包的评估
             */
            bool CallEvaluateDataPackageToDataPackage(const DataPtr &gt_data, const DataPtr &result_data)
            {
                try
                {
                    // 获取数据包
                    auto gt_package = std::dynamic_pointer_cast<DataPackage>(gt_data);
                    auto result_package = std::dynamic_pointer_cast<DataPackage>(result_data);

                    if (!gt_package || !result_package)
                    {
                        std::cerr << "[" << GetType() << "] Failed to get data packages" << std::endl;
                        return false;
                    }

                    std::vector<std::string> valid_evaluations;
                    std::vector<std::string> invalid_evaluations;
                    bool overall_success = false;

                    // 遍历结果数据包中的每个数据项，直接尝试评估
                    for (const auto &[key, result_item] : result_package->GetPackage())
                    {
                        if (!result_item)
                        {
                            invalid_evaluations.push_back(key + "(null_ptr)");
                            continue;
                        }

                        bool item_evaluated = false;

                        // 遍历GT数据包中的每个数据项，尝试评估
                        for (const auto &[gt_key, gt_item] : gt_package->GetPackage())
                        {
                            if (!gt_item)
                            {
                                continue;
                            }

                            try
                            {
                                // 直接尝试评估，不预先检查类型
                                EvaluatorStatus eval_status = result_item->Evaluate(gt_item);
                                if (eval_status.is_successful)
                                {
                                    bool success = ProcessEvaluationResults(eval_status);
                                    if (success)
                                    {
                                        overall_success = true;
                                        valid_evaluations.push_back(result_item->GetType());
                                        item_evaluated = true;
                                        break; // 找到匹配的GT数据，跳出内层循环
                                    }
                                }
                            }
                            catch (const std::exception &e)
                            {
                                // 评估失败，继续尝试下一个GT数据项
                                continue;
                            }
                        }

                        if (!item_evaluated)
                        {
                            invalid_evaluations.push_back(result_item->GetType() + "(no_matching_gt)");
                        }
                    }

                    // 输出评估结果日志
                    if (!valid_evaluations.empty())
                    {
                        std::cout << "[" << GetType() << "] 有效评估数据列表: ";
                        for (size_t i = 0; i < valid_evaluations.size(); ++i)
                        {
                            if (i > 0)
                                std::cout << " | ";
                            std::cout << valid_evaluations[i];
                        }
                        std::cout << std::endl;
                    }

                    if (!invalid_evaluations.empty())
                    {
                        std::cerr << "[" << GetType() << "] 无效评估数据列表: ";
                        for (size_t i = 0; i < invalid_evaluations.size(); ++i)
                        {
                            if (i > 0)
                                std::cerr << " | ";
                            std::cerr << invalid_evaluations[i];
                        }
                        std::cerr << std::endl;
                    }

                    return overall_success;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[" << GetType() << "] Error in CallEvaluateDataPackageToDataPackage: " << e.what() << std::endl;
                    return false;
                }
            }

            /**
             * @brief 调用数据包到单个数据对象的评估
             */
            bool CallEvaluateDataPackageToSingleData(const DataPtr &gt_data, const DataPtr &result_data)
            {
                try
                {
                    auto gt_package = std::dynamic_pointer_cast<DataPackage>(gt_data);
                    if (!gt_package)
                    {
                        std::cerr << "[" << GetType() << "] Failed to get GT data package" << std::endl;
                        return false;
                    }

                    // 遍历GT数据包中的每个数据项，直接尝试评估
                    for (const auto &[gt_key, gt_item] : gt_package->GetPackage())
                    {
                        if (!gt_item)
                        {
                            continue;
                        }

                        try
                        {
                            // 直接尝试评估，不预先检查类型
                            EvaluatorStatus eval_status = result_data->Evaluate(gt_item);
                            if (eval_status.is_successful)
                            {
                                return ProcessEvaluationResults(eval_status);
                            }
                        }
                        catch (const std::exception &e)
                        {
                            // 评估失败，继续尝试下一个GT数据项
                            continue;
                        }
                    }

                    std::cerr << "[" << GetType() << "] No matching GT data found for result type: " << result_data->GetType() << std::endl;
                    return false;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[" << GetType() << "] Error in CallEvaluateDataPackageToSingleData: " << e.what() << std::endl;
                    return false;
                }
            }

            /**
             * @brief 调用单个数据对象到数据包的评估
             */
            bool CallEvaluateSingleDataToDataPackage(const DataPtr &gt_data, const DataPtr &result_data)
            {
                try
                {
                    auto result_package = std::dynamic_pointer_cast<DataPackage>(result_data);
                    if (!result_package)
                    {
                        std::cerr << "[" << GetType() << "] Failed to get result data package" << std::endl;
                        return false;
                    }

                    std::vector<std::string> valid_evaluations;
                    std::vector<std::string> invalid_evaluations;
                    bool overall_success = false;

                    // 遍历结果数据包中的每个数据项，直接尝试评估
                    for (const auto &[key, result_item] : result_package->GetPackage())
                    {
                        if (!result_item)
                        {
                            invalid_evaluations.push_back(key + "(null_ptr)");
                            continue;
                        }

                        try
                        {
                            // 直接尝试评估，不预先检查类型
                            EvaluatorStatus eval_status = result_item->Evaluate(gt_data);
                            if (eval_status.is_successful)
                            {
                                bool success = ProcessEvaluationResults(eval_status);
                                if (success)
                                {
                                    overall_success = true;
                                    valid_evaluations.push_back(result_item->GetType());
                                }
                                else
                                {
                                    invalid_evaluations.push_back(result_item->GetType() + "(process_failed)");
                                }
                            }
                            else
                            {
                                invalid_evaluations.push_back(result_item->GetType() + "(eval_failed)");
                            }
                        }
                        catch (const std::exception &e)
                        {
                            invalid_evaluations.push_back(result_item->GetType() + "(exception)");
                        }
                    }

                    // 输出评估结果日志
                    if (!valid_evaluations.empty())
                    {
                        std::cout << "[" << GetType() << "] 有效评估数据列表: ";
                        for (size_t i = 0; i < valid_evaluations.size(); ++i)
                        {
                            if (i > 0)
                                std::cout << " | ";
                            std::cout << valid_evaluations[i];
                        }
                        std::cout << std::endl;
                    }

                    if (!invalid_evaluations.empty())
                    {
                        std::cerr << "[" << GetType() << "] 无效评估数据列表: ";
                        for (size_t i = 0; i < invalid_evaluations.size(); ++i)
                        {
                            if (i > 0)
                                std::cerr << " | ";
                            std::cerr << invalid_evaluations[i];
                        }
                        std::cerr << std::endl;
                    }

                    return overall_success;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[" << GetType() << "] Error in CallEvaluateSingleDataToDataPackage: " << e.what() << std::endl;
                    return false;
                }
            }

            /**
             * @brief 处理评估结果，将EvaluatorStatus的结果添加到全局评估器（支持新的多类型Note结构）
             */
            bool ProcessEvaluationResults(const EvaluatorStatus &eval_status)
            {
                try
                {
                    // 验证note数据的一致性
                    if (!eval_status.ValidateNoteData())
                    {
                        std::cerr << "[" << GetType() << "] Error: Note data validation failed" << std::endl;
                        return false;
                    }

                    // 从EvaluatorStatus中获取eval_type，如果为空则使用默认值
                    std::string eval_type = eval_status.eval_type;
                    if (eval_type.empty())
                    {
                        eval_type = "UnknownType"; // 默认类型，可以根据需要调整
                        std::cerr << "[" << GetType() << "] Warning: eval_type is empty, using default: " << eval_type << std::endl;
                    }

                    // 使用原有的eval_commit方式
                    std::string eval_commit = method_options_.count("ProfileCommit") > 0 ? method_options_.at("ProfileCommit") : "default_commit";

                    // 确定算法名称：如果设置了algorithm_则使用它，否则使用GetType()
                    std::string algorithm_name = algorithm_.empty() ? GetType() : algorithm_;

                    // 创建eval_status副本以添加core_time note
                    EvaluatorStatus modified_eval_status = eval_status;

                    // 为所有评估结果添加core_time note（转换为字符串）
                    std::string core_time_str = std::to_string(current_profile_.core_time_ms);
                    for (size_t i = 0; i < modified_eval_status.eval_results.size(); ++i)
                    {
                        // 确保core_time note数组有足够的元素
                        if (modified_eval_status.note_data["core_time"].size() <= i)
                        {
                            modified_eval_status.note_data["core_time"].resize(i + 1);
                        }
                        modified_eval_status.note_data["core_time"][i] = core_time_str;
                    }

                    // 使用新的批量添加方法，传递修改后的eval_status
                    bool success = EvaluatorManager::AddEvaluationResultsFromStatus(eval_type, algorithm_name, eval_commit, modified_eval_status);

                    if (success)
                    {
                        // 统计note信息
                        auto all_note_types = eval_status.GetAllNoteTypes();
                        size_t total_note_values = 0;
                        for (const auto &[note_type, note_values] : eval_status.note_data)
                        {
                            total_note_values += note_values.size();
                        }

                        PO_LOG(PO_LOG_NORMAL) << "[" << GetType() << "] Successfully added " << eval_status.eval_results.size()
                                              << " evaluation results to global evaluator (type: " << eval_type
                                              << ", algorithm: " << algorithm_name
                                              << ", commit: " << eval_commit;

                        if (!all_note_types.empty())
                        {
                            PO_LOG(PO_LOG_NORMAL) << ", with " << all_note_types.size() << " note type(s): ";
                            bool first = true;
                            for (const auto &note_type : all_note_types)
                            {
                                if (!first)
                                    PO_LOG(PO_LOG_NORMAL) << ", ";
                                PO_LOG(PO_LOG_NORMAL) << note_type;
                                first = false;
                            }
                        }
                        PO_LOG(PO_LOG_NORMAL) << ")" << std::endl;
                    }
                    else
                    {
                        std::cerr << "[" << GetType() << "] Failed to add evaluation results to global evaluator" << std::endl;
                    }

                    return success;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[" << GetType() << "] Error processing evaluation results: " << e.what() << std::endl;
                    return false;
                }
            }
        };

        using MethodPresetProfilerPtr = std::shared_ptr<MethodPresetProfiler>;

    } // namespace Interface
} // namespace PoSDK

#endif // _POMVG_INTERFACE_PRESET_PROFILER_
