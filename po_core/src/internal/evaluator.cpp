#include "evaluator.hpp"
#include "executable_path.hpp"

#include <iostream>
#include <sstream>
#include <set>
#include <map>
#include <filesystem>
#include <cstdlib>
#include <boost/regex.hpp>
#include <boost/lexical_cast.hpp>

namespace PoSDK
{
    namespace Interface
    {
        // 全局评估器实例
        static GlobalEvaluator POSDK_EVALUATOR;

        // conda环境名称静态成员变量定义
        std::string EvaluatorManager::conda_env_ = "";

        // CSV精度静态成员变量定义
        int EvaluatorManager::csv_precision_ = 6;

        GlobalEvaluator &EvaluatorManager::GetGlobalEvaluator()
        {
            return POSDK_EVALUATOR;
        }

        void EvaluatorManager::SetCondaEnv(const std::string &conda_env_name)
        {
            conda_env_ = conda_env_name;
            std::cout << "[EvaluatorManager] Set conda environment to: "
                      << (conda_env_name.empty() ? "system default" : conda_env_name) << std::endl;
        }

        std::string EvaluatorManager::GetCondaEnv()
        {
            return conda_env_;
        }

        void EvaluatorManager::ClearCondaEnv()
        {
            conda_env_.clear();
            std::cout << "[EvaluatorManager] Cleared conda environment, using system default Python" << std::endl;
        }

        void EvaluatorManager::SetCSVPrecision(int precision)
        {
            csv_precision_ = std::max(0, std::min(precision, 15)); // 限制精度范围[0, 15]
            std::cout << "[EvaluatorManager] Set CSV precision to: " << csv_precision_ << std::endl;
        }

        int EvaluatorManager::GetCSVPrecision()
        {
            return csv_precision_;
        }

        void EvaluatorManager::ResetCSVPrecision()
        {
            csv_precision_ = 6;
            std::cout << "[EvaluatorManager] Reset CSV precision to default: " << csv_precision_ << std::endl;
        }

        std::string EvaluatorManager::FormatNumber(double value)
        {
            // 计算最小阈值：10^(-precision)
            double threshold = std::pow(10.0, -csv_precision_);

            // 如果绝对值小于阈值，返回"0"
            if (std::abs(value) < threshold)
            {
                return "0";
            }

            // 否则格式化为指定精度
            std::ostringstream oss;
            oss << std::fixed << std::setprecision(csv_precision_) << value;

            // 移除尾部的0（保留至少一位小数）
            std::string result = oss.str();
            if (result.find('.') != std::string::npos)
            {
                result.erase(result.find_last_not_of('0') + 1, std::string::npos);
                if (result.back() == '.')
                {
                    result += '0';
                }
            }

            return result;
        }

        EvaluationDataPtr EvaluatorManager::GetOrCreateEvaluator(const std::string &eval_type,
                                                                 const std::string &algorithm_name,
                                                                 const std::string &metric_name)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            EvaluationKey key = std::make_tuple(eval_type, algorithm_name, metric_name);
            auto it = global_evaluator.find(key);
            if (it == global_evaluator.end())
            {
                // 创建新的评估器
                auto evaluation_data = std::make_shared<EvaluationData>();
                global_evaluator[key] = evaluation_data;
                return evaluation_data;
            }

            return it->second;
        }

        bool EvaluatorManager::ClearEvaluator(const std::string &eval_type)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            bool found_any = false;
            auto it = global_evaluator.begin();
            while (it != global_evaluator.end())
            {
                if (std::get<0>(it->first) == eval_type)
                {
                    it->second->Clear();
                    found_any = true;
                }
                ++it;
            }

            return found_any;
        }

        bool EvaluatorManager::ClearAlgorithm(const std::string &eval_type, const std::string &algorithm_name)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            bool found_any = false;
            auto it = global_evaluator.begin();
            while (it != global_evaluator.end())
            {
                const auto &[type, alg, metric] = it->first;
                if (type == eval_type && alg == algorithm_name)
                {
                    it->second->Clear();
                    found_any = true;
                }
                ++it;
            }

            return found_any;
        }

        void EvaluatorManager::ClearAllEvaluators()
        {
            auto &global_evaluator = GetGlobalEvaluator();

            for (auto &[key, evaluation_data] : global_evaluator)
            {
                if (evaluation_data)
                {
                    evaluation_data->Clear();
                }
            }
        }

        bool EvaluatorManager::AddEvaluationResult(const std::string &eval_type,
                                                   const std::string &algorithm_name,
                                                   const std::string &eval_commit,
                                                   const std::string &metric_name,
                                                   double value,
                                                   const std::string &note)
        {
            try
            {
                auto evaluation_data = GetOrCreateEvaluator(eval_type, algorithm_name, metric_name);
                evaluation_data->AddResult(eval_commit, value, note);
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error adding evaluation result: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::AddEvaluationResult(const std::string &eval_type,
                                                   const std::string &algorithm_name,
                                                   const std::string &eval_commit,
                                                   const std::string &metric_name,
                                                   double value,
                                                   const std::unordered_map<std::string, std::string> &notes)
        {
            try
            {
                auto evaluation_data = GetOrCreateEvaluator(eval_type, algorithm_name, metric_name);
                evaluation_data->AddResult(eval_commit, value, notes);
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error adding evaluation result with multi-type notes: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::AddEvaluationResultsFromStatus(const std::string &eval_type,
                                                              const std::string &algorithm_name,
                                                              const std::string &eval_commit,
                                                              const EvaluatorStatus &eval_status)
        {
            try
            {
                bool success = true;

                // 遍历所有评估结果
                for (size_t i = 0; i < eval_status.eval_results.size(); ++i)
                {
                    const auto &[metric_name, value] = eval_status.eval_results[i];

                    auto evaluation_data = GetOrCreateEvaluator(eval_type, algorithm_name, metric_name);
                    evaluation_data->AddResult(eval_commit, value, eval_status.note_data, i);
                }

                return success;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error adding evaluation results from status: " << e.what() << std::endl;
                return false;
            }
        }

        EvaluationData::Statistics EvaluatorManager::GetStatistics(const std::string &eval_type,
                                                                   const std::string &algorithm_name,
                                                                   const std::string &metric_name,
                                                                   const std::string &eval_commit)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            EvaluationKey key = std::make_tuple(eval_type, algorithm_name, metric_name);
            auto it = global_evaluator.find(key);
            if (it != global_evaluator.end() && it->second)
            {
                return it->second->GetStatistics(eval_commit);
            }

            return EvaluationData::Statistics(); // 返回空统计信息
        }

        // ParsedEvalCommit 的构造函数实现
        ParsedEvalCommit::ParsedEvalCommit(const std::string &eval_commit) : original(eval_commit), has_numeric_value(false), value(0.0)
        {
            try
            {
                // 定义多个正则表达式模式来匹配不同的EvalCommit格式

                // 模式1: 标准格式 "prefix_number" (如: points_100, noise_0.01)
                boost::regex pattern1(R"(^(.+)_([+-]?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?)$)");

                // 模式2: 多下划线格式 "prefix1_prefix2_number" (如: outlier_ratio_0.15)
                boost::regex pattern2(R"(^(.+)_([+-]?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?)$)");

                // 模式3: 含单位格式 "prefix_numberunit" (如: time_100ms, size_50kb)
                boost::regex pattern3(R"(^(.+)_([+-]?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?)[a-zA-Z]*$)");

                boost::smatch matches;

                // 尝试匹配各种模式
                if (boost::regex_match(eval_commit, matches, pattern1) ||
                    boost::regex_match(eval_commit, matches, pattern2) ||
                    boost::regex_match(eval_commit, matches, pattern3))
                {
                    prefix = matches[1].str();
                    std::string value_str = matches[2].str();

                    // 使用boost::lexical_cast进行类型转换
                    try
                    {
                        value = boost::lexical_cast<double>(value_str);
                        has_numeric_value = true;
                    }
                    catch (const boost::bad_lexical_cast &)
                    {
                        // 如果数值转换失败，回退到原始字符串作为前缀
                        prefix = eval_commit;
                        value = 0.0;
                        has_numeric_value = false;
                    }
                }
                else
                {
                    // 如果所有模式都不匹配，尝试查找任何数值
                    boost::regex number_pattern(R"(([+-]?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?))");
                    boost::sregex_iterator iter(eval_commit.begin(), eval_commit.end(), number_pattern);
                    boost::sregex_iterator end;

                    if (iter != end)
                    {
                        // 找到数值，提取第一个数值
                        auto match = *iter;
                        std::string value_str = match.str();

                        try
                        {
                            value = boost::lexical_cast<double>(value_str);
                            has_numeric_value = true;

                            // 生成前缀：移除数值部分
                            prefix = boost::regex_replace(eval_commit, number_pattern, "", boost::format_first_only);

                            // 清理前缀：移除尾部的下划线或其他分隔符
                            boost::regex cleanup_pattern(R"([_\-\s]+$)");
                            prefix = boost::regex_replace(prefix, cleanup_pattern, "");
                        }
                        catch (const boost::bad_lexical_cast &)
                        {
                            prefix = eval_commit;
                            value = 0.0;
                            has_numeric_value = false;
                        }
                    }
                    else
                    {
                        // 没有找到数值，使用原始字符串作为前缀
                        prefix = eval_commit;
                        value = 0.0;
                        has_numeric_value = false;
                    }
                }

                // 如果前缀为空，使用原始字符串
                if (prefix.empty())
                {
                    prefix = eval_commit;
                }
            }
            catch (const std::exception &e)
            {
                // 如果regex解析出现异常，回退到简单解析
                std::cerr << "[ParsedEvalCommit] Regex parsing failed for '" << eval_commit
                          << "', falling back to simple parsing: " << e.what() << std::endl;

                // 回退方案：简单的下划线分割
                size_t underscore_pos = eval_commit.find_last_of('_');
                if (underscore_pos != std::string::npos && underscore_pos < eval_commit.length() - 1)
                {
                    prefix = eval_commit.substr(0, underscore_pos);
                    std::string value_str = eval_commit.substr(underscore_pos + 1);

                    try
                    {
                        value = std::stod(value_str);
                        has_numeric_value = true;
                    }
                    catch (const std::exception &)
                    {
                        prefix = eval_commit;
                        value = 0.0;
                        has_numeric_value = false;
                    }
                }
                else
                {
                    prefix = eval_commit;
                    value = 0.0;
                    has_numeric_value = false;
                }
            }
        }

        // ParsedEvalCommit 的排序操作符实现
        bool ParsedEvalCommit::operator<(const ParsedEvalCommit &other) const
        {
            if (prefix != other.prefix)
            {
                return prefix < other.prefix;
            }

            if (has_numeric_value && other.has_numeric_value)
            {
                return value < other.value;
            }
            else if (has_numeric_value && !other.has_numeric_value)
            {
                return true; // 有数值的排在前面
            }
            else if (!has_numeric_value && other.has_numeric_value)
            {
                return false; // 有数值的排在前面
            }
            else
            {
                return original < other.original; // 都没有数值时按字符串排序
            }
        }

        bool EvaluatorManager::ExportAlgorithmComparisonToCSV(const std::string &eval_type,
                                                              const std::string &metric_name,
                                                              const std::filesystem::path &output_path,
                                                              const std::string &stat_type)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            // 收集该类型和指标下的所有算法
            std::vector<std::string> algorithms = GetAllAlgorithms(eval_type);
            if (algorithms.empty())
            {
                std::cerr << "[EvaluatorManager] No algorithms found for type: " << eval_type << std::endl;
                return false;
            }

            // 收集所有评估提交名称并解析
            std::set<std::string> all_commits_set;
            for (const auto &algorithm : algorithms)
            {
                auto commits = GetAllEvalCommits(eval_type, algorithm, metric_name);
                all_commits_set.insert(commits.begin(), commits.end());
            }

            std::vector<std::string> all_commits(all_commits_set.begin(), all_commits_set.end());
            if (all_commits.empty())
            {
                std::cerr << "[EvaluatorManager] No evaluation commits found for " << eval_type << "::" << metric_name << std::endl;
                return false;
            }

            // 智能解析所有EvalCommit，按前缀分组
            std::map<std::string, std::vector<ParsedEvalCommit>> grouped_commits;
            std::vector<ParsedEvalCommit> all_parsed_commits;

            for (const auto &commit : all_commits)
            {
                ParsedEvalCommit parsed(commit);
                all_parsed_commits.push_back(parsed);
                grouped_commits[parsed.GetDisplayName()].push_back(parsed);
            }

            // 对每个组内的提交按智能排序
            for (auto &[prefix, commits] : grouped_commits)
            {
                std::sort(commits.begin(), commits.end());
            }

            // 同时对所有解析的提交进行排序，用于生成列标题
            std::sort(all_parsed_commits.begin(), all_parsed_commits.end());

            try
            {
                // 确保输出目录存在
                std::filesystem::create_directories(output_path.parent_path());

                std::ofstream csv_file(output_path);
                if (!csv_file.is_open())
                {
                    std::cerr << "[EvaluatorManager] Cannot open CSV file: " << output_path << std::endl;
                    return false;
                }

                // 检查是否有数值型的EvalCommit，决定输出格式
                bool has_numeric_commits = std::any_of(all_parsed_commits.begin(), all_parsed_commits.end(),
                                                       [](const ParsedEvalCommit &pc)
                                                       { return pc.has_numeric_value; });

                // 自动选择最佳的表格格式
                if (has_numeric_commits)
                {
                    std::cout << "[智能解析] 检测到数值型EvalCommit，使用智能表格格式（前缀作为列名）" << std::endl;
                    GenerateSmartCSVTable(csv_file, eval_type, metric_name, stat_type, algorithms, grouped_commits, global_evaluator);
                }
                else
                {
                    std::cout << "[智能解析] 未检测到数值型EvalCommit，使用传统表格格式（EvalCommit作为列名）" << std::endl;
                    GenerateTraditionalCSVTable(csv_file, eval_type, metric_name, stat_type, algorithms, all_parsed_commits, global_evaluator);
                }

                csv_file.close();
                std::cout << "[EvaluatorManager] Exported enhanced algorithm comparison for " << eval_type << "::" << metric_name
                          << " (" << stat_type << ") to: " << output_path << std::endl;
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting algorithm comparison to CSV: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::ExportAllMetricsToCSV(const std::string &eval_type,
                                                     const std::filesystem::path &output_dir)
        {
            // 获取该类型下的所有指标
            std::set<std::string> all_metrics_set;
            auto algorithms = GetAllAlgorithms(eval_type);
            for (const auto &algorithm : algorithms)
            {
                auto metrics = GetAllMetrics(eval_type, algorithm);
                all_metrics_set.insert(metrics.begin(), metrics.end());
            }

            if (all_metrics_set.empty())
            {
                std::cout << "[EvaluatorManager] No metrics found for type: " << eval_type << std::endl;
                return true;
            }

            try
            {
                std::filesystem::create_directories(output_dir);

                bool all_success = true;
                std::vector<std::string> stat_types = {"mean", "median", "max", "min", "std_dev"};

                for (const auto &metric : all_metrics_set)
                {
                    for (const auto &stat_type : stat_types)
                    {
                        std::string filename = eval_type + "_" + metric + "_" + stat_type + "_comparison.csv";
                        std::filesystem::path file_path = output_dir / filename;

                        if (!ExportAlgorithmComparisonToCSV(eval_type, metric, file_path, stat_type))
                        {
                            all_success = false;
                        }
                    }
                }

                return all_success;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting all metrics to CSV: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::ExportTimeStatisticsToCSV(const std::string &eval_type,
                                                         const std::filesystem::path &output_path)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            // 收集该类型下的所有算法
            std::vector<std::string> algorithms = GetAllAlgorithms(eval_type);
            if (algorithms.empty())
            {
                std::cout << "[EvaluatorManager] No algorithms found for type: " << eval_type << std::endl;
                return true;
            }

            // 收集时间数据：按 (algorithm, eval_commit) 组织
            // 对于每个 (eval_type, algorithm, eval_commit) 组合，收集多次运行的平均时间
            std::map<std::string, std::map<std::string, std::vector<double>>> algorithm_time_data; // [algorithm][commit] -> [avg_times_per_run]
            std::set<std::string> all_commits_set;

            for (const auto &algorithm : algorithms)
            {
                // 收集该算法下所有metric的时间数据
                std::map<std::string, std::vector<std::vector<double>>> commit_metric_times; // [commit] -> [metrics][run_index] -> time

                for (const auto &[key, evaluation_data] : global_evaluator)
                {
                    const auto &[type, alg, metric] = key;

                    if (type != eval_type || alg != algorithm || !evaluation_data)
                    {
                        continue;
                    }

                    // 检查该 metric 中是否有 core_time note
                    auto commits = evaluation_data->GetAllEvalCommits();
                    for (const auto &commit : commits)
                    {
                        // 检查是否有数据
                        auto eval_it = evaluation_data->eval_commit_data.find(commit);
                        if (eval_it != evaluation_data->eval_commit_data.end() && !eval_it->second.empty())
                        {
                            // 获取core_time类型的note值
                            auto core_time_notes = evaluation_data->GetNotes(commit, "core_time");
                            if (!core_time_notes.empty())
                            {
                                std::vector<double> metric_times;
                                for (const auto &time_str : core_time_notes)
                                {
                                    if (!time_str.empty())
                                    {
                                        try
                                        {
                                            double time_value = std::stod(time_str);
                                            metric_times.push_back(time_value);
                                        }
                                        catch (const std::exception &e)
                                        {
                                            // 忽略无效的时间值
                                        }
                                    }
                                }
                                if (!metric_times.empty())
                                {
                                    commit_metric_times[commit].push_back(metric_times);
                                    all_commits_set.insert(commit);
                                }
                            }
                        }
                    }
                }

                // 计算每次运行的平均时间（平均所有metric的时间）
                for (const auto &[commit, metrics_times] : commit_metric_times)
                {
                    if (metrics_times.empty())
                        continue;

                    // 确定最大运行次数
                    size_t max_runs = 0;
                    for (const auto &metric_times : metrics_times)
                    {
                        max_runs = std::max(max_runs, metric_times.size());
                    }

                    // 计算每次运行的平均时间
                    std::vector<double> avg_times_per_run;
                    for (size_t run_idx = 0; run_idx < max_runs; ++run_idx)
                    {
                        double total_time = 0.0;
                        size_t valid_metrics = 0;

                        for (const auto &metric_times : metrics_times)
                        {
                            if (run_idx < metric_times.size())
                            {
                                total_time += metric_times[run_idx];
                                valid_metrics++;
                            }
                        }

                        if (valid_metrics > 0)
                        {
                            double avg_time = total_time / valid_metrics;
                            avg_times_per_run.push_back(avg_time);
                        }
                    }

                    if (!avg_times_per_run.empty())
                    {
                        algorithm_time_data[algorithm][commit] = avg_times_per_run;
                    }
                }
            }

            if (algorithm_time_data.empty())
            {
                std::cout << "[EvaluatorManager] No time statistics found for type: " << eval_type << std::endl;
                return true;
            }

            std::vector<std::string> all_commits(all_commits_set.begin(), all_commits_set.end());

            // 智能解析所有EvalCommit并排序
            std::vector<ParsedEvalCommit> all_parsed_commits;
            for (const auto &commit : all_commits)
            {
                all_parsed_commits.emplace_back(commit);
            }

            // 智能排序：按前缀分组，组内按数值排序
            std::sort(all_parsed_commits.begin(), all_parsed_commits.end());

            try
            {
                // 确保输出目录存在
                std::filesystem::create_directories(output_path.parent_path());

                std::ofstream csv_file(output_path);
                if (!csv_file.is_open())
                {
                    std::cerr << "[EvaluatorManager] Cannot open CSV file: " << output_path << std::endl;
                    return false;
                }

                std::cout << "[时间统计] 导出时间统计表格" << std::endl;
                std::cout << "[时间统计] 解析了 " << all_parsed_commits.size() << " 个EvalCommit，按智能排序" << std::endl;

                // 显示解析结果和格式判断
                std::cout << "[时间统计] 分析EvalCommit格式:" << std::endl;
                bool has_numeric = false;
                std::set<std::string> prefixes_set;

                for (const auto &parsed : all_parsed_commits)
                {
                    if (parsed.has_numeric_value)
                    {
                        has_numeric = true;
                        prefixes_set.insert(parsed.prefix);
                        std::cout << "  ✓ " << parsed.original << " -> 前缀: '" << parsed.prefix << "', 数值: "
                                  << std::fixed << std::setprecision(15) << parsed.value << std::endl;
                    }
                    else
                    {
                        std::cout << "  ✗ " << parsed.original << " -> 无数值解析" << std::endl;
                    }
                }

                std::cout << "[时间统计] 格式分析结果:" << std::endl;
                std::cout << "  - 数值型EvalCommit: " << (has_numeric ? "是" : "否") << std::endl;
                if (has_numeric)
                {
                    std::cout << "  - 检测到的前缀数量: " << prefixes_set.size() << std::endl;
                    for (const auto &prefix : prefixes_set)
                    {
                        std::cout << "    * '" << prefix << "'" << std::endl;
                    }
                    if (prefixes_set.size() == 1)
                    {
                        std::cout << "  - 前缀统一性: 是（将使用智能表格格式）" << std::endl;
                    }
                    else
                    {
                        std::cout << "  - 前缀统一性: 否（将使用传统表格格式）" << std::endl;
                    }
                }

                // 使用智能解析格式生成时间统计表格
                if (has_numeric && prefixes_set.size() == 1)
                {
                    // 智能格式：前缀作为列名，数值作为列值
                    std::string prefix = *prefixes_set.begin();
                    std::cout << "[时间统计] 使用智能表格格式，前缀: '" << prefix << "'" << std::endl;

                    csv_file << "Algorithm," << prefix << ",Mean,Median,Min,Max" << std::endl;

                    for (const auto &algorithm : algorithms)
                    {
                        if (algorithm_time_data.find(algorithm) == algorithm_time_data.end())
                        {
                            continue;
                        }

                        for (const auto &parsed : all_parsed_commits)
                        {
                            if (!parsed.has_numeric_value)
                                continue;

                            const auto &commit_data = algorithm_time_data[algorithm];
                            auto it = commit_data.find(parsed.original);

                            if (it != commit_data.end() && !it->second.empty())
                            {
                                auto sorted_values = it->second;
                                std::sort(sorted_values.begin(), sorted_values.end());

                                double mean = std::accumulate(sorted_values.begin(), sorted_values.end(), 0.0) / sorted_values.size();
                                double median = sorted_values[sorted_values.size() / 2];
                                double min_val = sorted_values.front();
                                double max_val = sorted_values.back();

                                csv_file << "\"" << algorithm << "\","
                                         << std::fixed << std::setprecision(15) << parsed.value << ","
                                         << FormatNumber(mean) << ","
                                         << FormatNumber(median) << ","
                                         << FormatNumber(min_val) << ","
                                         << FormatNumber(max_val) << std::endl;
                            }
                        }
                    }
                }
                else
                {
                    // 传统格式：EvalCommit作为列名
                    std::cout << "[时间统计] 使用传统表格格式" << std::endl;

                    csv_file << "Algorithm,EvalCommit,Mean,Median,Min,Max" << std::endl;

                    for (const auto &algorithm : algorithms)
                    {
                        if (algorithm_time_data.find(algorithm) == algorithm_time_data.end())
                        {
                            continue;
                        }

                        for (const auto &parsed : all_parsed_commits)
                        {
                            const auto &commit_data = algorithm_time_data[algorithm];
                            auto it = commit_data.find(parsed.original);

                            if (it != commit_data.end() && !it->second.empty())
                            {
                                auto sorted_values = it->second;
                                std::sort(sorted_values.begin(), sorted_values.end());

                                double mean = std::accumulate(sorted_values.begin(), sorted_values.end(), 0.0) / sorted_values.size();
                                double median = sorted_values[sorted_values.size() / 2];
                                double min_val = sorted_values.front();
                                double max_val = sorted_values.back();

                                csv_file << "\"" << algorithm << "\","
                                         << "\"" << parsed.original << "\","
                                         << FormatNumber(mean) << ","
                                         << FormatNumber(median) << ","
                                         << FormatNumber(min_val) << ","
                                         << FormatNumber(max_val) << std::endl;
                            }
                        }
                    }
                }

                csv_file.close();
                std::cout << "[EvaluatorManager] Exported time statistics for " << eval_type
                          << " to: " << output_path << std::endl;
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting time statistics to CSV: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::ExportMetricAllStatsToCSV(const std::string &eval_type,
                                                         const std::string &metric_name,
                                                         const std::filesystem::path &output_path)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            // 收集该类型和指标下的所有算法
            std::vector<std::string> algorithms = GetAllAlgorithms(eval_type);
            if (algorithms.empty())
            {
                std::cerr << "[EvaluatorManager] No algorithms found for type: " << eval_type << std::endl;
                return false;
            }

            // 收集所有评估提交名称并解析
            std::set<std::string> all_commits_set;
            for (const auto &algorithm : algorithms)
            {
                auto commits = GetAllEvalCommits(eval_type, algorithm, metric_name);
                all_commits_set.insert(commits.begin(), commits.end());
            }

            std::vector<std::string> all_commits(all_commits_set.begin(), all_commits_set.end());
            if (all_commits.empty())
            {
                std::cerr << "[EvaluatorManager] No evaluation commits found for " << eval_type << "::" << metric_name << std::endl;
                return false;
            }

            // 智能解析所有EvalCommit并排序
            std::vector<ParsedEvalCommit> all_parsed_commits;
            for (const auto &commit : all_commits)
            {
                all_parsed_commits.emplace_back(commit);
            }

            // 智能排序：按前缀分组，组内按数值排序
            std::sort(all_parsed_commits.begin(), all_parsed_commits.end());

            try
            {
                // 确保输出目录存在
                std::filesystem::create_directories(output_path.parent_path());

                std::ofstream csv_file(output_path);
                if (!csv_file.is_open())
                {
                    std::cerr << "[EvaluatorManager] Cannot open CSV file: " << output_path << std::endl;
                    return false;
                }

                std::cout << "[智能解析] 导出指标 " << metric_name << " 的所有统计类型（统一智能解析格式）" << std::endl;
                std::cout << "[智能解析] 解析了 " << all_parsed_commits.size() << " 个EvalCommit，按智能排序" << std::endl;

                // 显示解析结果和格式判断
                std::cout << "[智能解析] 分析EvalCommit格式:" << std::endl;
                bool has_numeric = false;
                std::set<std::string> prefixes_set;

                for (const auto &parsed : all_parsed_commits)
                {
                    if (parsed.has_numeric_value)
                    {
                        has_numeric = true;
                        prefixes_set.insert(parsed.prefix);
                        // 显示完整精度的解析值，不受CSV精度设置影响
                        std::cout << "  ✓ " << parsed.original << " -> 前缀: '" << parsed.prefix << "', 数值: "
                                  << std::fixed << std::setprecision(15) << parsed.value << std::endl;
                    }
                    else
                    {
                        std::cout << "  ✗ " << parsed.original << " -> 无数值解析" << std::endl;
                    }
                }

                std::cout << "[智能解析] 格式分析结果:" << std::endl;
                std::cout << "  - 数值型EvalCommit: " << (has_numeric ? "是" : "否") << std::endl;
                if (has_numeric)
                {
                    std::cout << "  - 检测到的前缀数量: " << prefixes_set.size() << std::endl;
                    for (const auto &prefix : prefixes_set)
                    {
                        std::cout << "    * '" << prefix << "'" << std::endl;
                    }
                    if (prefixes_set.size() == 1)
                    {
                        std::cout << "  - 前缀统一性: 是（将使用智能表格格式）" << std::endl;
                    }
                    else
                    {
                        std::cout << "  - 前缀统一性: 否（将使用传统表格格式）" << std::endl;
                    }
                }

                GenerateUnifiedAllStatsTable(csv_file, eval_type, metric_name, algorithms, all_parsed_commits, global_evaluator);

                csv_file.close();
                std::cout << "[EvaluatorManager] Exported unified all stats for " << eval_type << "::" << metric_name
                          << " to: " << output_path << std::endl;
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting metric all stats to CSV: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::ExportDetailedStatsToCSV(const std::string &eval_type,
                                                        const std::string &algorithm_name,
                                                        const std::filesystem::path &output_path)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            // 收集该算法下的所有指标和评估提交
            std::vector<std::string> metrics = GetAllMetrics(eval_type, algorithm_name);
            if (metrics.empty())
            {
                std::cerr << "[EvaluatorManager] No metrics found for " << eval_type << "::" << algorithm_name << std::endl;
                return false;
            }

            try
            {
                // 确保输出目录存在
                std::filesystem::create_directories(output_path.parent_path());

                std::ofstream csv_file(output_path);
                if (!csv_file.is_open())
                {
                    std::cerr << "[EvaluatorManager] Cannot open CSV file: " << output_path << std::endl;
                    return false;
                }

                // 收集所有EvalCommit并进行智能解析
                std::vector<ParsedEvalCommit> all_parsed_commits;
                std::set<std::string> all_commits_set;

                for (const auto &metric : metrics)
                {
                    EvaluationKey key = std::make_tuple(eval_type, algorithm_name, metric);
                    auto it = global_evaluator.find(key);
                    if (it != global_evaluator.end() && it->second)
                    {
                        auto commits = it->second->GetAllEvalCommits();
                        all_commits_set.insert(commits.begin(), commits.end());
                    }
                }

                // 对所有EvalCommit进行智能解析
                for (const auto &commit : all_commits_set)
                {
                    all_parsed_commits.emplace_back(commit);
                }

                // 检查是否有数值型EvalCommit
                bool has_numeric_commits = std::any_of(all_parsed_commits.begin(), all_parsed_commits.end(),
                                                       [](const ParsedEvalCommit &pc)
                                                       { return pc.has_numeric_value; });

                std::cout << "[智能解析] 详细统计表格 - 分析了 " << all_parsed_commits.size() << " 个EvalCommit" << std::endl;
                if (has_numeric_commits)
                {
                    std::cout << "[智能解析] 检测到数值型EvalCommit，将添加解析列（前缀、数值）" << std::endl;
                }
                else
                {
                    std::cout << "[智能解析] 未检测到数值型EvalCommit，使用标准格式" << std::endl;
                }

                // 写入增强的表头（包含智能解析列）
                if (has_numeric_commits)
                {
                    csv_file << "Metric,EvalCommit,EvalPrefix,EvalValue,Count,Mean,Median,Min,Max,StdDev\n";
                }
                else
                {
                    csv_file << GenerateDetailedStatsCSVHeader() << "\n";
                }

                // 写入每个指标的详细统计
                for (const auto &metric : metrics)
                {
                    EvaluationKey key = std::make_tuple(eval_type, algorithm_name, metric);
                    auto it = global_evaluator.find(key);
                    if (it != global_evaluator.end() && it->second)
                    {
                        auto commits = it->second->GetAllEvalCommits();
                        for (const auto &commit : commits)
                        {
                            auto stats = it->second->GetStatistics(commit);
                            if (has_numeric_commits)
                            {
                                // 查找对应的解析结果
                                auto parsed_it = std::find_if(all_parsed_commits.begin(), all_parsed_commits.end(),
                                                              [&commit](const ParsedEvalCommit &pc)
                                                              { return pc.original == commit; });

                                if (parsed_it != all_parsed_commits.end())
                                {
                                    csv_file << FormatDetailedStatsWithParsingToCSVRow(metric, *parsed_it, stats) << "\n";
                                }
                                else
                                {
                                    // 如果没找到解析结果，创建一个临时的
                                    ParsedEvalCommit temp_parsed(commit);
                                    csv_file << FormatDetailedStatsWithParsingToCSVRow(metric, temp_parsed, stats) << "\n";
                                }
                            }
                            else
                            {
                                csv_file << FormatDetailedStatsToCSVRow(metric, commit, stats) << "\n";
                            }
                        }
                    }
                }

                csv_file.close();
                std::cout << "[EvaluatorManager] Exported enhanced detailed stats for " << eval_type << "::" << algorithm_name
                          << " to: " << output_path << std::endl;
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting detailed stats to CSV: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::ExportAllRawValuesToCSV(const std::string &eval_type,
                                                       const std::filesystem::path &output_dir,
                                                       const std::string &note_types)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            // 收集该类型下的所有指标
            std::set<std::string> all_metrics_set;
            auto algorithms = GetAllAlgorithms(eval_type);
            for (const auto &algorithm : algorithms)
            {
                auto metrics = GetAllMetrics(eval_type, algorithm);
                all_metrics_set.insert(metrics.begin(), metrics.end());
            }

            if (all_metrics_set.empty())
            {
                std::cout << "[EvaluatorManager] No metrics found for type: " << eval_type << std::endl;
                return true;
            }

            // 解析note类型
            std::vector<std::string> requested_note_types;
            if (!note_types.empty())
            {
                std::istringstream iss(note_types);
                std::string note_type;
                while (std::getline(iss, note_type, '|'))
                {
                    if (!note_type.empty())
                    {
                        requested_note_types.push_back(note_type);
                    }
                }
            }

            try
            {
                std::filesystem::create_directories(output_dir);

                bool all_success = true;

                for (const auto &metric : all_metrics_set)
                {
                    // 生成文件名：{metric_name}_all_evaluated_values.csv
                    std::string filename = metric + "_all_evaluated_values.csv";
                    std::filesystem::path file_path = output_dir / filename;

                    std::ofstream csv_file(file_path);
                    if (!csv_file.is_open())
                    {
                        std::cerr << "[EvaluatorManager] Cannot open CSV file: " << file_path << std::endl;
                        all_success = false;
                        continue;
                    }

                    // 写入CSV表头
                    csv_file << "Algorithm,EvalCommit,Value";
                    for (const auto &note_type : requested_note_types)
                    {
                        csv_file << "," << note_type;
                    }
                    csv_file << "\n";

                    // 为该指标收集所有算法的数据
                    for (const auto &algorithm : algorithms)
                    {
                        EvaluationKey key = std::make_tuple(eval_type, algorithm, metric);
                        auto it = global_evaluator.find(key);
                        if (it != global_evaluator.end() && it->second)
                        {
                            // 获取该算法该指标下的所有评估提交
                            auto commits = it->second->GetAllEvalCommits();
                            for (const auto &commit : commits)
                            {
                                // 获取该评估提交下的所有原始值
                                auto eval_it = it->second->eval_commit_data.find(commit);

                                if (eval_it != it->second->eval_commit_data.end())
                                {
                                    const auto &values = eval_it->second;

                                    for (size_t i = 0; i < values.size(); ++i)
                                    {
                                        csv_file << "\"" << algorithm << "\","
                                                 << "\"" << commit << "\","
                                                 << FormatNumber(values[i]);

                                        // 添加请求的note类型列
                                        for (const auto &note_type : requested_note_types)
                                        {
                                            auto notes = it->second->GetNotes(commit, note_type);
                                            std::string note_value = (i < notes.size()) ? notes[i] : "";
                                            csv_file << ",\"" << note_value << "\"";
                                        }
                                        csv_file << "\n";
                                    }
                                }
                            }
                        }
                    }

                    csv_file.close();

                    std::string note_info = requested_note_types.empty() ? "" : (" with note types: " + note_types);
                    std::cout << "[EvaluatorManager] Exported raw values for " << eval_type << "::" << metric
                              << note_info << " to: " << file_path << std::endl;
                }

                return all_success;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting raw values to CSV: " << e.what() << std::endl;
                return false;
            }
        }

        std::vector<std::string> EvaluatorManager::GetAllAlgorithms(const std::string &eval_type)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            std::set<std::string> algorithms_set;
            for (const auto &[key, evaluation_data] : global_evaluator)
            {
                const auto &[type, algorithm, metric] = key;
                if (type == eval_type && evaluation_data && !evaluation_data->eval_commit_data.empty())
                {
                    algorithms_set.insert(algorithm);
                }
            }

            return std::vector<std::string>(algorithms_set.begin(), algorithms_set.end());
        }

        std::vector<std::string> EvaluatorManager::GetAllMetrics(const std::string &eval_type,
                                                                 const std::string &algorithm_name)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            std::set<std::string> metrics_set;
            for (const auto &[key, evaluation_data] : global_evaluator)
            {
                const auto &[type, algorithm, metric] = key;
                if (type == eval_type && algorithm == algorithm_name &&
                    evaluation_data && !evaluation_data->eval_commit_data.empty())
                {
                    metrics_set.insert(metric);
                }
            }

            return std::vector<std::string>(metrics_set.begin(), metrics_set.end());
        }

        std::vector<std::string> EvaluatorManager::GetAllEvalCommits(const std::string &eval_type,
                                                                     const std::string &algorithm_name,
                                                                     const std::string &metric_name)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            EvaluationKey key = std::make_tuple(eval_type, algorithm_name, metric_name);
            auto it = global_evaluator.find(key);
            if (it != global_evaluator.end() && it->second)
            {
                return it->second->GetAllEvalCommits();
            }

            return std::vector<std::string>();
        }

        void EvaluatorManager::PrintEvaluationReport(const std::string &eval_type)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            // 收集该类型下的所有数据
            std::map<std::string, std::map<std::string, EvaluationDataPtr>> type_data; // algorithm -> metric -> data

            for (const auto &[key, evaluation_data] : global_evaluator)
            {
                const auto &[type, algorithm, metric] = key;
                if (type == eval_type && evaluation_data && !evaluation_data->eval_commit_data.empty())
                {
                    type_data[algorithm][metric] = evaluation_data;
                }
            }

            if (type_data.empty())
            {
                std::cout << "[EvaluatorManager] No evaluation data for type: " << eval_type << std::endl;
                return;
            }

            std::cout << "\n=== Evaluation Report for " << eval_type << " ===\n";

            for (const auto &[algorithm, metrics_data] : type_data)
            {
                std::cout << "\nAlgorithm: " << algorithm << "\n";
                std::cout << std::string(50, '-') << "\n";

                for (const auto &[metric, evaluation_data] : metrics_data)
                {
                    std::cout << "  Metric: " << metric << "\n";

                    auto commits = evaluation_data->GetAllEvalCommits();
                    for (const auto &commit : commits)
                    {
                        auto stats = evaluation_data->GetStatistics(commit);
                        std::cout << "    " << commit << ": "
                                  << "count=" << stats.count
                                  << ", mean=" << std::fixed << std::setprecision(4) << stats.mean
                                  << ", std=" << stats.std_dev
                                  << ", [" << stats.min << ", " << stats.max << "]\n";
                    }
                    std::cout << "\n";
                }
            }

            std::cout << "==========================================\n"
                      << std::endl;
        }

        void EvaluatorManager::PrintAlgorithmComparison(const std::string &eval_type, const std::string &metric_name)
        {
            auto algorithms = GetAllAlgorithms(eval_type);
            if (algorithms.empty())
            {
                std::cout << "[EvaluatorManager] No algorithms found for " << eval_type << "::" << metric_name << std::endl;
                return;
            }

            std::cout << "\n=== Algorithm Comparison for " << eval_type << "::" << metric_name << " ===\n";

            // 收集所有评估提交
            std::set<std::string> all_commits_set;
            for (const auto &algorithm : algorithms)
            {
                auto commits = GetAllEvalCommits(eval_type, algorithm, metric_name);
                all_commits_set.insert(commits.begin(), commits.end());
            }

            std::vector<std::string> all_commits(all_commits_set.begin(), all_commits_set.end());

            // 打印表头
            std::cout << std::setw(15) << "Algorithm";
            for (const auto &commit : all_commits)
            {
                std::cout << std::setw(12) << (commit + "_mean");
            }
            std::cout << "\n"
                      << std::string(15 + 12 * all_commits.size(), '-') << "\n";

            // 打印每个算法的数据
            for (const auto &algorithm : algorithms)
            {
                std::cout << std::setw(15) << algorithm;
                for (const auto &commit : all_commits)
                {
                    auto stats = GetStatistics(eval_type, algorithm, metric_name, commit);
                    if (stats.count > 0)
                    {
                        std::cout << std::setw(12) << std::fixed << std::setprecision(4) << stats.mean;
                    }
                    else
                    {
                        std::cout << std::setw(12) << "N/A";
                    }
                }
                std::cout << "\n";
            }

            std::cout << "==========================================\n"
                      << std::endl;
        }

        void EvaluatorManager::PrintAllEvaluationReports()
        {
            auto eval_types = GetAllEvaluationTypes();

            if (eval_types.empty())
            {
                std::cout << "[EvaluatorManager] No evaluation data available" << std::endl;
                return;
            }

            for (const auto &eval_type : eval_types)
            {
                PrintEvaluationReport(eval_type);
            }
        }

        std::vector<std::string> EvaluatorManager::GetAllEvaluationTypes()
        {
            auto &global_evaluator = GetGlobalEvaluator();

            std::set<std::string> types_set;
            for (const auto &[key, evaluation_data] : global_evaluator)
            {
                const auto &[type, algorithm, metric] = key;
                if (evaluation_data && !evaluation_data->eval_commit_data.empty())
                {
                    types_set.insert(type);
                }
            }

            return std::vector<std::string>(types_set.begin(), types_set.end());
        }

        void EvaluatorManager::PrintLatestEvaluationResults(const std::string &eval_type,
                                                            const std::string &algorithm_name,
                                                            const std::string &note_types)
        {
            auto &global_evaluator = GetGlobalEvaluator();

            try
            {
                // 解析note类型
                std::vector<std::string> requested_note_types;
                if (!note_types.empty())
                {
                    std::istringstream iss(note_types);
                    std::string note_type;
                    while (std::getline(iss, note_type, '|'))
                    {
                        if (!note_type.empty())
                        {
                            requested_note_types.push_back(note_type);
                        }
                    }
                }

                // 获取该算法下的所有指标
                std::vector<std::string> metrics = GetAllMetrics(eval_type, algorithm_name);
                if (metrics.empty())
                {
                    std::cout << "[" << algorithm_name << "] No metrics found for " << eval_type << std::endl;
                    return;
                }

                // 收集最新的评估结果
                std::vector<std::string> result_parts;
                std::unordered_map<std::string, std::string> collected_notes;

                for (const auto &metric : metrics)
                {
                    EvaluationKey key = std::make_tuple(eval_type, algorithm_name, metric);
                    auto it = global_evaluator.find(key);
                    if (it != global_evaluator.end() && it->second)
                    {
                        const auto &evaluation_data = it->second;

                        // 获取所有eval_commits
                        auto commits = evaluation_data->GetAllEvalCommits();
                        if (!commits.empty())
                        {
                            // 使用最后一个eval_commit（按字符串排序）
                            std::sort(commits.begin(), commits.end());
                            const std::string &latest_commit = commits.back();

                            // 获取该commit下的最新值（向量中的最后一个值）
                            auto commit_it = evaluation_data->eval_commit_data.find(latest_commit);
                            if (commit_it != evaluation_data->eval_commit_data.end() && !commit_it->second.empty())
                            {
                                double latest_value = commit_it->second.back();

                                // 格式化数值输出
                                result_parts.push_back(metric + "=" + FormatNumber(latest_value));

                                // 收集note信息（只收集一次，使用第一个找到的metric的note）
                                if (collected_notes.empty() && !requested_note_types.empty())
                                {
                                    for (const auto &note_type : requested_note_types)
                                    {
                                        auto notes = evaluation_data->GetNotes(latest_commit, note_type);
                                        if (!notes.empty())
                                        {
                                            // 使用最后一个note值
                                            collected_notes[note_type] = notes.back();
                                        }
                                        else
                                        {
                                            collected_notes[note_type] = "N/A";
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 输出结果
                if (!result_parts.empty())
                {
                    std::cout << "\033[0;34m[" << algorithm_name << "] Latest results: ";

                    // 输出metrics
                    for (size_t i = 0; i < result_parts.size(); ++i)
                    {
                        if (i > 0)
                            std::cout << ", ";
                        std::cout << result_parts[i];
                    }

                    // 输出notes
                    for (const auto &note_type : requested_note_types)
                    {
                        auto note_it = collected_notes.find(note_type);
                        if (note_it != collected_notes.end())
                        {
                            std::cout << ", " << note_type << "=" << note_it->second;
                        }
                    }

                    std::cout << "\033[0m" << std::endl;
                }
                else
                {
                    std::cout << "[" << algorithm_name << "] No evaluation results found for " << eval_type << std::endl;
                }
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error printing latest evaluation results: " << e.what() << std::endl;
            }
        }

        std::string EvaluatorManager::GenerateAlgorithmComparisonCSVHeader(const std::vector<std::string> &eval_commits)
        {
            std::ostringstream oss;
            oss << "Algorithm";
            for (const std::string &commit : eval_commits)
            {
                oss << "," << commit;
            }
            return oss.str();
        }

        std::string EvaluatorManager::GenerateDetailedStatsCSVHeader()
        {
            return "Metric,EvalCommit,Count,Mean,Median,Min,Max,StdDev";
        }

        std::string EvaluatorManager::FormatAlgorithmComparisonToCSVRow(const std::string &algorithm_name,
                                                                        const std::vector<std::string> &eval_commits,
                                                                        const EvaluationDataPtr &evaluation_data,
                                                                        const std::string &stat_type)
        {
            std::ostringstream oss;
            oss << "\"" << algorithm_name << "\"";

            for (const std::string &commit : eval_commits)
            {
                auto stats = evaluation_data->GetStatistics(commit);
                double value = 0.0;

                if (stats.count > 0)
                {
                    if (stat_type == "mean")
                        value = stats.mean;
                    else if (stat_type == "median")
                        value = stats.median;
                    else if (stat_type == "max")
                        value = stats.max;
                    else if (stat_type == "min")
                        value = stats.min;
                    else if (stat_type == "std_dev")
                        value = stats.std_dev;
                    else if (stat_type == "count")
                        value = static_cast<double>(stats.count);
                }

                oss << "," << FormatNumber(value);
            }

            return oss.str();
        }

        std::string EvaluatorManager::FormatDetailedStatsToCSVRow(const std::string &metric_name,
                                                                  const std::string &eval_commit,
                                                                  const EvaluationData::Statistics &stats)
        {
            std::ostringstream oss;
            oss << "\"" << metric_name << "\","
                << "\"" << eval_commit << "\","
                << stats.count << ","
                << FormatNumber(stats.mean) << ","
                << FormatNumber(stats.median) << ","
                << FormatNumber(stats.min) << ","
                << FormatNumber(stats.max) << ","
                << FormatNumber(stats.std_dev);
            return oss.str();
        }

        std::string EvaluatorManager::FormatDetailedStatsWithParsingToCSVRow(const std::string &metric_name,
                                                                             const ParsedEvalCommit &parsed_commit,
                                                                             const EvaluationData::Statistics &stats)
        {
            std::ostringstream oss;
            oss << "\"" << metric_name << "\","
                << "\"" << parsed_commit.original << "\","
                << "\"" << parsed_commit.prefix << "\","
                << std::fixed << std::setprecision(15) << parsed_commit.value << "," // 使用高精度格式化threshold值
                << stats.count << ","
                << FormatNumber(stats.mean) << ","
                << FormatNumber(stats.median) << ","
                << FormatNumber(stats.min) << ","
                << FormatNumber(stats.max) << ","
                << FormatNumber(stats.std_dev);
            return oss.str();
        }

        // 生成智能表格格式的CSV（前缀作为列名）
        void EvaluatorManager::GenerateSmartCSVTable(std::ofstream &csv_file,
                                                     const std::string &eval_type,
                                                     const std::string &metric_name,
                                                     const std::string &stat_type,
                                                     const std::vector<std::string> &algorithms,
                                                     const std::map<std::string, std::vector<ParsedEvalCommit>> &grouped_commits,
                                                     GlobalEvaluator &global_evaluator)
        {
            // 收集所有唯一的前缀（作为列名）
            std::set<std::string> all_prefixes;
            for (const auto &[prefix, commits] : grouped_commits)
            {
                all_prefixes.insert(prefix);
            }

            // 显示智能解析的列名映射
            std::cout << "[智能解析] 生成的表格列名:" << std::endl;
            std::cout << "  Algorithm (算法名称)" << std::endl;
            for (const auto &prefix : all_prefixes)
            {
                // 显示这个前缀包含的原始EvalCommit
                auto prefix_it = grouped_commits.find(prefix);
                if (prefix_it != grouped_commits.end())
                {
                    std::cout << "  " << prefix << " (包含: ";
                    for (size_t i = 0; i < prefix_it->second.size(); ++i)
                    {
                        if (i > 0)
                            std::cout << ", ";
                        std::cout << prefix_it->second[i].original;
                    }
                    std::cout << ")" << std::endl;
                }
            }

            // 生成表头：Algorithm + 各个前缀列名
            csv_file << "Algorithm";
            for (const auto &prefix : all_prefixes)
            {
                csv_file << "," << prefix;
            }
            csv_file << "\n";

            // 为每个算法生成数据行
            for (const auto &algorithm : algorithms)
            {
                csv_file << "\"" << algorithm << "\"";

                for (const auto &prefix : all_prefixes)
                {
                    // 查找该算法和前缀对应的数据
                    auto prefix_it = grouped_commits.find(prefix);
                    double best_value = 0.0;
                    bool found_data = false;

                    if (prefix_it != grouped_commits.end())
                    {
                        // 在该前缀组中查找该算法的数据
                        for (const auto &parsed_commit : prefix_it->second)
                        {
                            EvaluationKey key = std::make_tuple(eval_type, algorithm, metric_name);
                            auto it = global_evaluator.find(key);
                            if (it != global_evaluator.end() && it->second)
                            {
                                auto stats = it->second->GetStatistics(parsed_commit.original);
                                if (stats.count > 0)
                                {
                                    double value = GetStatValue(stats, stat_type);
                                    if (!found_data || IsBetterValue(value, best_value, metric_name))
                                    {
                                        best_value = value;
                                        found_data = true;
                                    }
                                }
                            }
                        }
                    }

                    if (found_data)
                    {
                        csv_file << "," << FormatNumber(best_value);
                    }
                    else
                    {
                        csv_file << ",N/A";
                    }
                }
                csv_file << "\n";
            }
        }

        // 生成传统表格格式的CSV（每个EvalCommit作为一列）
        void EvaluatorManager::GenerateTraditionalCSVTable(std::ofstream &csv_file,
                                                           const std::string &eval_type,
                                                           const std::string &metric_name,
                                                           const std::string &stat_type,
                                                           const std::vector<std::string> &algorithms,
                                                           const std::vector<ParsedEvalCommit> &all_parsed_commits,
                                                           GlobalEvaluator &global_evaluator)
        {
            // 生成表头：Algorithm + 各个EvalCommit
            csv_file << "Algorithm";
            for (const auto &parsed_commit : all_parsed_commits)
            {
                csv_file << "," << parsed_commit.original;
            }
            csv_file << "\n";

            // 为每个算法生成数据行
            for (const auto &algorithm : algorithms)
            {
                csv_file << "\"" << algorithm << "\"";

                for (const auto &parsed_commit : all_parsed_commits)
                {
                    EvaluationKey key = std::make_tuple(eval_type, algorithm, metric_name);
                    auto it = global_evaluator.find(key);
                    if (it != global_evaluator.end() && it->second)
                    {
                        auto stats = it->second->GetStatistics(parsed_commit.original);
                        if (stats.count > 0)
                        {
                            double value = GetStatValue(stats, stat_type);
                            csv_file << "," << FormatNumber(value);
                        }
                        else
                        {
                            csv_file << ",N/A";
                        }
                    }
                    else
                    {
                        csv_file << ",N/A";
                    }
                }
                csv_file << "\n";
            }
        }

        // 从统计数据中获取指定类型的值
        double EvaluatorManager::GetStatValue(const EvaluationData::Statistics &stats, const std::string &stat_type)
        {
            if (stat_type == "mean")
                return stats.mean;
            else if (stat_type == "median")
                return stats.median;
            else if (stat_type == "max")
                return stats.max;
            else if (stat_type == "min")
                return stats.min;
            else if (stat_type == "std_dev")
                return stats.std_dev;
            else if (stat_type == "count")
                return static_cast<double>(stats.count);
            else
                return stats.mean; // 默认返回均值
        }

        // 判断某个值是否比另一个值更好（根据指标类型决定）
        bool EvaluatorManager::IsBetterValue(double new_value, double current_value, const std::string &metric_name)
        {
            // 对于误差类指标，越小越好
            if (metric_name.find("error") != std::string::npos ||
                metric_name.find("Error") != std::string::npos ||
                metric_name.find("loss") != std::string::npos ||
                metric_name.find("Loss") != std::string::npos)
            {
                return new_value < current_value;
            }
            // 对于准确率、精度等指标，越大越好
            else
            {
                return new_value > current_value;
            }
        }

        // 生成统一格式的所有统计类型表格（每个EvalCommit一行）
        void EvaluatorManager::GenerateUnifiedAllStatsTable(std::ofstream &csv_file,
                                                            const std::string &eval_type,
                                                            const std::string &metric_name,
                                                            const std::vector<std::string> &algorithms,
                                                            const std::vector<ParsedEvalCommit> &all_parsed_commits,
                                                            GlobalEvaluator &global_evaluator)
        {
            // 检查是否可以使用智能解析格式
            bool can_use_smart_format = false;
            std::string common_prefix;

            if (!all_parsed_commits.empty())
            {
                // 检查是否所有EvalCommit都有数值且有相同的前缀
                bool all_have_numeric = std::all_of(all_parsed_commits.begin(), all_parsed_commits.end(),
                                                    [](const ParsedEvalCommit &pc)
                                                    { return pc.has_numeric_value; });

                if (all_have_numeric)
                {
                    // 获取第一个前缀作为参考
                    common_prefix = all_parsed_commits[0].prefix;

                    // 检查是否所有EvalCommit都有相同的前缀
                    bool same_prefix = std::all_of(all_parsed_commits.begin(), all_parsed_commits.end(),
                                                   [&common_prefix](const ParsedEvalCommit &pc)
                                                   { return pc.prefix == common_prefix; });

                    can_use_smart_format = same_prefix;
                }
            }

            if (can_use_smart_format)
            {
                std::cout << "[智能解析] 检测到统一前缀的数值型EvalCommit，使用智能表格格式" << std::endl;
                std::cout << "[智能解析] 前缀: '" << common_prefix << "', 将作为列名" << std::endl;

                // 智能格式：前缀作为列名，数值作为列值
                csv_file << "Algorithm," << common_prefix << ",Mean,Median,Min,Max,StdDev\n";

                // 为每个算法生成数据行
                for (const auto &algorithm : algorithms)
                {
                    for (const auto &parsed_commit : all_parsed_commits)
                    {
                        EvaluationKey key = std::make_tuple(eval_type, algorithm, metric_name);
                        auto it = global_evaluator.find(key);
                        if (it != global_evaluator.end() && it->second)
                        {
                            auto stats = it->second->GetStatistics(parsed_commit.original);
                            if (stats.count > 0)
                            {
                                csv_file << "\"" << algorithm << "\","
                                         << std::fixed << std::setprecision(15) << parsed_commit.value << "," // 使用高精度格式化threshold值
                                         << FormatNumber(stats.mean) << ","
                                         << FormatNumber(stats.median) << ","
                                         << FormatNumber(stats.min) << ","
                                         << FormatNumber(stats.max) << ","
                                         << FormatNumber(stats.std_dev) << "\n";
                            }
                            else
                            {
                                // 输出N/A数据行
                                csv_file << "\"" << algorithm << "\","
                                         << std::fixed << std::setprecision(15) << parsed_commit.value << "," // 使用高精度格式化threshold值
                                         << "N/A,N/A,N/A,N/A,N/A\n";
                            }
                        }
                        else
                        {
                            // 算法没有该指标的数据，输出N/A数据行
                            csv_file << "\"" << algorithm << "\","
                                     << std::fixed << std::setprecision(15) << parsed_commit.value << "," // 使用高精度格式化threshold值
                                     << "N/A,N/A,N/A,N/A,N/A\n";
                        }
                    }
                }
            }
            else
            {
                std::cout << "[智能解析] 使用传统格式 - 每个EvalCommit一行，包含所有统计类型" << std::endl;

                // 传统格式：EvalCommit作为列名
                csv_file << "Algorithm,EvalCommit,Mean,Median,Min,Max,StdDev\n";

                // 为每个算法生成数据行
                for (const auto &algorithm : algorithms)
                {
                    for (const auto &parsed_commit : all_parsed_commits)
                    {
                        EvaluationKey key = std::make_tuple(eval_type, algorithm, metric_name);
                        auto it = global_evaluator.find(key);
                        if (it != global_evaluator.end() && it->second)
                        {
                            auto stats = it->second->GetStatistics(parsed_commit.original);
                            if (stats.count > 0)
                            {
                                csv_file << "\"" << algorithm << "\","
                                         << "\"" << parsed_commit.original << "\"," // 输出原始EvalCommit
                                         << FormatNumber(stats.mean) << ","
                                         << FormatNumber(stats.median) << ","
                                         << FormatNumber(stats.min) << ","
                                         << FormatNumber(stats.max) << ","
                                         << FormatNumber(stats.std_dev) << "\n";
                            }
                            else
                            {
                                // 输出N/A数据行
                                csv_file << "\"" << algorithm << "\","
                                         << "\"" << parsed_commit.original << "\"," // 输出原始EvalCommit
                                         << "N/A,N/A,N/A,N/A,N/A\n";
                            }
                        }
                        else
                        {
                            // 算法没有该指标的数据，输出N/A数据行
                            csv_file << "\"" << algorithm << "\","
                                     << "\"" << parsed_commit.original << "\"," // 输出原始EvalCommit
                                     << "N/A,N/A,N/A,N/A,N/A\n";
                        }
                    }
                }
            }
        }

        // ==================== 可视化绘图功能实现 ====================

        bool EvaluatorManager::GenerateMetricVisualization(const std::string &eval_type,
                                                           const std::string &metric_name,
                                                           const std::filesystem::path &csv_file_path,
                                                           const std::filesystem::path &output_dir,
                                                           const std::vector<std::string> &stat_types,
                                                           const std::string &style_file)
        {
            try
            {
                // 确保输出目录存在
                std::filesystem::create_directories(output_dir);

                // 使用ExecutablePath工具类获取Python脚本路径
                std::string script_path_str = ExecutablePath::GetRelativeToExecutable("../python/plot_metric_visualizer.py");
                if (script_path_str.empty())
                {
                    std::cerr << "[EvaluatorManager] Failed to resolve Python script path" << std::endl;
                    return false;
                }
                std::filesystem::path script_path(script_path_str);

                if (!std::filesystem::exists(script_path))
                {
                    std::cerr << "[EvaluatorManager] Python plot script not found: " << script_path << std::endl;
                    std::cerr << "[EvaluatorManager] Please ensure the script is installed in the python directory" << std::endl;
                    return false;
                }

                // 为每个统计类型生成图表
                bool all_success = true;
                for (const auto &stat_type : stat_types)
                {
                    std::string output_filename = eval_type + "_" + metric_name + "_" + stat_type + ".png";
                    std::filesystem::path output_path = output_dir / output_filename;

                    std::vector<std::string> args = {
                        "--stat_type", stat_type,
                        "--title", eval_type + " - " + metric_name + " (" + stat_type + ")",
                        "--ylabel", metric_name + " (" + stat_type + ")"};

                    // EvaluatorManager只生成文件，不提供交互功能
                    // 交互功能由专门的GUI工具处理
                    if (!CallPythonPlotScript(script_path, csv_file_path, output_path, args))
                    {
                        std::cerr << "[EvaluatorManager] Failed to generate plot for " << stat_type << std::endl;
                        all_success = false;
                    }
                    else
                    {
                        std::cout << "[EvaluatorManager] Generated plot: " << output_path << std::endl;
                    }

                    if (!style_file.empty())
                    {
                        args.push_back("--style_file");
                        args.push_back(style_file);
                    }
                }

                return all_success;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error generating metric visualization: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::GenerateAllMetricsVisualization(const std::string &eval_type,
                                                               const std::filesystem::path &csv_output_dir,
                                                               const std::filesystem::path &plot_output_dir,
                                                               const std::vector<std::string> &stat_types,
                                                               const std::string &style_file)
        {
            try
            {
                // 查找所有的ALL_STATS.csv文件
                std::vector<std::filesystem::path> csv_files;
                std::string pattern = eval_type + "_.*_ALL_STATS\\.csv";
                boost::regex csv_pattern(pattern);

                for (const auto &entry : std::filesystem::directory_iterator(csv_output_dir))
                {
                    if (entry.is_regular_file())
                    {
                        std::string filename = entry.path().filename().string();
                        if (boost::regex_match(filename, csv_pattern))
                        {
                            csv_files.push_back(entry.path());
                        }
                    }
                }

                if (csv_files.empty())
                {
                    std::cout << "[EvaluatorManager] No ALL_STATS CSV files found for " << eval_type << std::endl;
                    return true;
                }

                bool all_success = true;
                for (const auto &csv_file : csv_files)
                {
                    // 从文件名提取指标名称
                    std::string filename = csv_file.filename().string();
                    boost::regex metric_pattern(eval_type + "_(.*)_ALL_STATS\\.csv");
                    boost::smatch matches;

                    if (boost::regex_match(filename, matches, metric_pattern))
                    {
                        std::string metric_name = matches[1].str();

                        if (!GenerateMetricVisualization(eval_type, metric_name, csv_file, plot_output_dir, stat_types, style_file))
                        {
                            all_success = false;
                        }
                    }
                }

                return all_success;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error generating all metrics visualization: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::ExportMetricWithVisualization(const std::string &eval_type,
                                                             const std::string &metric_name,
                                                             const std::filesystem::path &output_dir,
                                                             const std::vector<std::string> &stat_types,
                                                             const std::string &style_file)
        {
            try
            {
                // 首先导出CSV
                std::string csv_filename = eval_type + "_" + metric_name + "_ALL_STATS.csv";
                std::filesystem::path csv_path = output_dir / csv_filename;

                if (!ExportMetricAllStatsToCSV(eval_type, metric_name, csv_path))
                {
                    std::cerr << "[EvaluatorManager] Failed to export CSV for " << eval_type << "::" << metric_name << std::endl;
                    return false;
                }

                // 然后生成可视化图表
                std::filesystem::path plot_dir = output_dir / "plots";
                if (!GenerateMetricVisualization(eval_type, metric_name, csv_path, plot_dir, stat_types, style_file))
                {
                    std::cerr << "[EvaluatorManager] Failed to generate visualization for " << eval_type << "::" << metric_name << std::endl;
                    return false;
                }

                std::cout << "[EvaluatorManager] Successfully exported CSV and generated plots for "
                          << eval_type << "::" << metric_name << std::endl;
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting metric with visualization: " << e.what() << std::endl;
                return false;
            }
        }

        // ==================== 时间统计可视化功能实现 ====================

        bool EvaluatorManager::GenerateTimeStatisticsVisualization(const std::string &eval_type,
                                                                   const std::filesystem::path &csv_output_dir,
                                                                   const std::filesystem::path &plot_output_dir,
                                                                   const std::vector<std::string> &stat_types)
        {
            try
            {
                // 查找时间统计CSV文件
                std::filesystem::path time_csv_path = csv_output_dir / (eval_type + "_time_statistics.csv");

                if (!std::filesystem::exists(time_csv_path))
                {
                    std::cout << "[EvaluatorManager] No time statistics CSV file found: " << time_csv_path << std::endl;
                    return true;
                }

                // 确保输出目录存在
                std::filesystem::create_directories(plot_output_dir);

                // 使用ExecutablePath工具类获取Python脚本路径
                std::string script_path_str = ExecutablePath::GetRelativeToExecutable("../python/plot_metric_visualizer.py");
                if (script_path_str.empty())
                {
                    std::cerr << "[EvaluatorManager] Failed to resolve Python script path" << std::endl;
                    return false;
                }
                std::filesystem::path script_path(script_path_str);

                if (!std::filesystem::exists(script_path))
                {
                    std::cerr << "[EvaluatorManager] Python plot script not found: " << script_path << std::endl;
                    return false;
                }

                // 为每个统计类型生成图表
                bool all_success = true;
                for (const auto &stat_type : stat_types)
                {
                    std::string output_filename = eval_type + "_time_statistics_" + stat_type + ".png";
                    std::filesystem::path output_path = plot_output_dir / output_filename;

                    std::vector<std::string> args = {
                        "--stat_type", stat_type,
                        "--title", eval_type + " - Time Statistics (" + stat_type + ")",
                        "--ylabel", "Time (ms) (" + stat_type + ")"};

                    if (!CallPythonPlotScript(script_path, time_csv_path, output_path, args))
                    {
                        std::cerr << "[EvaluatorManager] Failed to generate time statistics plot for " << stat_type << std::endl;
                        all_success = false;
                    }
                    else
                    {
                        std::cout << "[EvaluatorManager] Generated time statistics plot: " << output_path << std::endl;
                    }
                }

                return all_success;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error generating time statistics visualization: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::ExportTimeStatisticsWithVisualization(const std::string &eval_type,
                                                                     const std::filesystem::path &output_dir,
                                                                     const std::vector<std::string> &stat_types)
        {
            try
            {
                // 首先导出时间统计CSV
                std::string csv_filename = eval_type + "_time_statistics.csv";
                std::filesystem::path csv_path = output_dir / csv_filename;

                if (!ExportTimeStatisticsToCSV(eval_type, csv_path))
                {
                    std::cerr << "[EvaluatorManager] Failed to export time statistics CSV for " << eval_type << std::endl;
                    return false;
                }

                // 然后生成可视化图表
                std::filesystem::path plot_dir = output_dir / "plots";
                if (!GenerateTimeStatisticsVisualization(eval_type, output_dir, plot_dir, stat_types))
                {
                    std::cerr << "[EvaluatorManager] Failed to generate time statistics visualization for " << eval_type << std::endl;
                    return false;
                }

                std::cout << "[EvaluatorManager] Successfully exported time statistics CSV and generated plots for "
                          << eval_type << std::endl;
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error exporting time statistics with visualization: " << e.what() << std::endl;
                return false;
            }
        }

        bool EvaluatorManager::CallPythonPlotScript(const std::filesystem::path &python_script_path,
                                                    const std::filesystem::path &csv_file_path,
                                                    const std::filesystem::path &output_path,
                                                    const std::vector<std::string> &additional_args)
        {
            try
            {
                std::string python_command;

                // 检查用户是否手动指定了conda环境
                if (!conda_env_.empty())
                {
                    std::cout << "[EvaluatorManager] Using user-specified conda environment: " << conda_env_ << std::endl;

                    // 首先尝试检测conda基础路径
                    std::string conda_base_path;

                    // 方法1：从环境变量获取
                    const char *conda_prefix = std::getenv("CONDA_PREFIX");
                    if (conda_prefix)
                    {
                        std::string prefix_str(conda_prefix);
                        // 如果当前在conda环境中，获取base路径
                        size_t envs_pos = prefix_str.find("/envs/");
                        if (envs_pos != std::string::npos)
                        {
                            conda_base_path = prefix_str.substr(0, envs_pos);
                        }
                        else
                        {
                            conda_base_path = prefix_str; // 可能已经是base路径
                        }
                    }

                    // 方法2：尝试常见的conda安装路径
                    if (conda_base_path.empty())
                    {
                        std::vector<std::string> common_conda_paths = {
                            "/Users/" + std::string(std::getenv("USER") ? std::getenv("USER") : "user") + "/miniconda3",
                            "/Users/" + std::string(std::getenv("USER") ? std::getenv("USER") : "user") + "/anaconda3",
                            "/opt/miniconda3",
                            "/opt/anaconda3",
                            "/usr/local/miniconda3",
                            "/usr/local/anaconda3"};

                        for (const auto &path : common_conda_paths)
                        {
                            if (std::filesystem::exists(path + "/etc/profile.d/conda.sh"))
                            {
                                conda_base_path = path;
                                break;
                            }
                        }
                    }

                    // 如果找到了conda路径，构建完整的激活命令
                    if (!conda_base_path.empty() && std::filesystem::exists(conda_base_path + "/etc/profile.d/conda.sh"))
                    {
                        std::ostringstream conda_cmd;
                        conda_cmd << "source " << conda_base_path << "/etc/profile.d/conda.sh && conda activate " << conda_env_ << " && python";
                        python_command = conda_cmd.str();
                        std::cout << "[EvaluatorManager] Found conda at: " << conda_base_path << std::endl;
                    }
                    else
                    {
                        std::cout << "[EvaluatorManager] Warning: Could not find conda installation, falling back to configure drawer environment" << std::endl;
                        python_command = "";
                    }
                }
                else
                {
                    std::cout << "[EvaluatorManager] No conda environment specified, using local drawer environment" << std::endl;
                }

                // 如果没有设置conda环境或conda不可用，使用本地drawer环境
                if (python_command.empty())
                {
                    // 获取OUTPUT_BASE_DIR/python目录中的环境配置脚本
                    std::string drawer_python_dir;
                    const char *output_base_dir = std::getenv("OUTPUT_BASE_DIR");
                    if (output_base_dir)
                    {
                        drawer_python_dir = std::string(output_base_dir) + "/python";
                    }
                    else
                    {
                        // 备选方案：从可执行文件目录推导 (bin -> python)
                        std::filesystem::path exe_dir = ExecutablePath::GetExecutableDirectory();
                        if (!exe_dir.empty())
                        {
                            // 可执行文件在OUTPUT_BASE_DIR/bin，python脚本在OUTPUT_BASE_DIR/python
                            std::filesystem::path output_base = exe_dir.parent_path(); // 从bin目录回到OUTPUT_BASE_DIR
                            drawer_python_dir = (output_base / "python").string();
                        }
                    }

                    if (!drawer_python_dir.empty())
                    {
                        // 首先检查本地Python环境是否已经存在并可用
                        std::filesystem::path local_python = std::filesystem::path(drawer_python_dir) / "conda_env" / "bin" / "python";
                        bool env_ready = false;

                        if (std::filesystem::exists(local_python))
                        {
                            // 测试Python环境是否可用（检查关键包）
                            std::string test_cmd = local_python.string() + " -c \"import matplotlib, pandas, seaborn, numpy; print('OK')\" 2>/dev/null";
                            int test_result = std::system(test_cmd.c_str());

                            if (test_result == 0)
                            {
                                env_ready = true;
                                python_command = local_python.string();
                                std::cout << "[EvaluatorManager] Using existing local Python environment: " << python_command << std::endl;
                            }
                            else
                            {
                                std::cout << "[EvaluatorManager] Local Python environment exists but packages missing, will reconfigure" << std::endl;
                            }
                        }

                        // 如果环境不存在或不可用，才配置环境
                        if (!env_ready)
                        {
                            std::filesystem::path configure_script = std::filesystem::path(drawer_python_dir) / "configure_drawer_env.sh";

                            if (std::filesystem::exists(configure_script))
                            {
                                std::cout << "[EvaluatorManager] Configuring drawer environment using: " << configure_script << std::endl;

                                // 调用环境配置脚本（使用非交互模式）
                                std::string config_cmd = "bash \"" + configure_script.string() + "\" < /dev/null";
                                int config_result = std::system(config_cmd.c_str());

                                if (config_result == 0)
                                {
                                    std::cout << "[EvaluatorManager] Drawer environment configured successfully" << std::endl;

                                    // 使用配置好的本地Python环境
                                    if (std::filesystem::exists(local_python))
                                    {
                                        python_command = local_python.string();
                                        std::cout << "[EvaluatorManager] Using configured local Python: " << python_command << std::endl;
                                    }
                                    else
                                    {
                                        std::cerr << "[EvaluatorManager] Local Python environment not found after configuration" << std::endl;
                                        python_command = "";
                                    }
                                }
                                else
                                {
                                    std::cerr << "[EvaluatorManager] Failed to configure drawer environment" << std::endl;
                                    python_command = "";
                                }
                            }
                            else
                            {
                                std::cout << "[EvaluatorManager] Configure script not found at: " << configure_script << std::endl;
                                python_command = "";
                            }
                        }
                    }

                    // 如果本地环境配置失败，不回退到系统Python，而是报错
                    if (python_command.empty())
                    {
                        std::cerr << "[EvaluatorManager] ERROR: Python environment configuration failed!" << std::endl;
                        std::cerr << "[EvaluatorManager] Please ensure one of the following:" << std::endl;
                        std::cerr << "  1. Set a conda environment using EvaluatorManager::SetCondaEnv(\"env_name\")" << std::endl;
                        std::cerr << "  2. Ensure OUTPUT_BASE_DIR/python/configure_drawer_env.sh exists and is executable" << std::endl;
                        std::cerr << "  3. Manually configure Python environment with required packages:" << std::endl;

                        // 查找requirements.txt并显示安装命令
                        if (!drawer_python_dir.empty())
                        {
                            std::filesystem::path requirements_path = std::filesystem::path(drawer_python_dir) / "requirements.txt";
                            if (std::filesystem::exists(requirements_path))
                            {
                                std::cerr << "     pip install -r " << requirements_path << std::endl;
                            }
                            else
                            {
                                std::cerr << "     pip install PyQt5 matplotlib pandas seaborn numpy" << std::endl;
                            }
                        }
                        else
                        {
                            std::cerr << "     pip install PyQt5 matplotlib pandas seaborn numpy" << std::endl;
                        }

                        std::cerr << "[EvaluatorManager] Drawer python directory: " << (drawer_python_dir.empty() ? "not found" : drawer_python_dir) << std::endl;
                        return false;
                    }
                }

                // 构建完整的命令
                std::ostringstream cmd;
                cmd << python_command << " \"" << python_script_path.string() << "\"";
                cmd << " \"" << csv_file_path.string() << "\"";
                cmd << " \"" << output_path.string() << "\"";

                // 添加额外参数
                for (const auto &arg : additional_args)
                {
                    cmd << " \"" << arg << "\"";
                }

                std::string command = cmd.str();
                std::cout << "[EvaluatorManager] Executing: " << command << std::endl;

                // 执行Python脚本
                int result = std::system(command.c_str());

                if (result == 0)
                {
                    std::cout << "[EvaluatorManager] Python plot script executed successfully" << std::endl;
                    return true;
                }
                else
                {
                    std::cerr << "[EvaluatorManager] Python plot script failed with exit code: " << result << std::endl;

                    // 提供调试信息
                    std::cerr << "[EvaluatorManager] Debug info:" << std::endl;
                    std::cerr << "  Conda environment: " << (conda_env_.empty() ? " user not set" : conda_env_) << std::endl;
                    std::cerr << "  Python command: " << python_command << std::endl;
                    std::cerr << "  Script path: " << python_script_path << std::endl;
                    std::cerr << "  CSV file: " << csv_file_path << std::endl;
                    std::cerr << "  Output path: " << output_path << std::endl;

                    // 如果使用conda环境失败，尝试提供解决建议
                    if (!conda_env_.empty())
                    {
                        std::cerr << "[EvaluatorManager] Suggestion: Check if conda environment '" << conda_env_
                                  << "' exists and has required packages (pandas, matplotlib, seaborn)" << std::endl;
                        std::cerr << "[EvaluatorManager] You can verify with: conda activate " << conda_env_
                                  << " && python -c \"import pandas, matplotlib, seaborn; print('All packages available')\"" << std::endl;
                    }

                    return false;
                }
            }
            catch (const std::exception &e)
            {
                std::cerr << "[EvaluatorManager] Error calling Python plot script: " << e.what() << std::endl;
                return false;
            }
        }

    } // namespace Interface
} // namespace PoSDK