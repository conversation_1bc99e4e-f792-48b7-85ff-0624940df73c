/**
 * @file ransac_estimator.hpp
 * @brief RANSAC robust estimator implementation
 * @details Implements RANSAC algorithm with zero-copy design and robust error handling
 *
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef POMVG_INTERFACES_RANSAC_ESTIMATOR_HPP_
#define POMVG_INTERFACES_RANSAC_ESTIMATOR_HPP_

#include "interfaces_robust_estimator.hpp"
#include <random>
#include <algorithm>
#include <limits>
#include <iomanip>

namespace PoSDK
{
    namespace Interface
    {

        /**
         * @brief RANSAC (Random Sample Consensus) estimator implementation
         * @details Provides a generic RANSAC implementation with:
         *          - Zero-copy data handling
         *          - Early termination optimization
         *          - Thread-safe random number generation
         *          - Robust error handling
         */

        template <typename TSample>
        class RANSACEstimator : public RobustEstimator
        {
        public:
            RANSACEstimator()
            {
                // 添加调试输出
                std::cout << "Initializing RANSACEstimator..." << std::endl;

                // 加载通用配置
                InitializeDefaultConfigPath(); // 默认加载 [GetType()] 部分
            }

            virtual ~RANSACEstimator() = default;

            // 使用静态局部变量返回类型
            const std::string &GetType() const override
            {
                static const std::string type = "ransac_estimator";
                return type;
            }

            /**
             * @brief 实际的RANSAC算法实现
             * @param material_ptr 输入数据指针
             * @return 估计结果的数据指针
             * @throws std::runtime_error 如果创建模型估计器或代价评估器失败
             */
            DataPtr Run() override
            {
                try
                {
                    DisplayConfigInfo();

                    // 1. 获取配置参数
                    const size_t max_iterations = GetOptionAsIndexT("max_iterations", 1000);
                    const double confidence = GetOptionAsFloat("confidence", 0.99f);
                    const double inlier_threshold = GetOptionAsFloat("inlier_threshold", 1.0f);
                    const size_t min_sample_size = GetOptionAsIndexT("min_sample_size", 8);

                    // 2. 获取总样本数据指针： DataPtr >> DataSamplePtr<TSample>
                    DataSamplePtr<TSample> sample_data_ptr = CastToSample<TSample>(GetPopulationPtr());

                    if (!sample_data_ptr || sample_data_ptr->GetPopulationSize() < min_sample_size)
                    {
                        std::cerr << "Sample data is not valid or has insufficient samples" << std::endl;
                        return nullptr;
                    }

                    // 2.1 配置采样策略
                    ConfigureSamplingStrategy(sample_data_ptr);

                    // 3. RANSAC主循环
                    DataPtr best_model;
                    size_t best_inlier_count = 0;
                    double best_cost = 1e10;

                    size_t iteration = 0;
                    double k = 1.0; // 初始需要的迭代次数

                    while (iteration < k && iteration < max_iterations)
                    {
                        // 3.1 获取随机子样本
                        DataPtr subset = sample_data_ptr->GetRandomSubset(min_sample_size);
                        if (!subset)
                            continue;

                        // 3.2 估计模型
                        auto current_model = EstimateModel(subset);
                        if (!current_model)
                            continue;

                        // 3.3 评估模型
                        auto [inlier_count, total_cost] = EvaluateModel(current_model, sample_data_ptr);

                        // 3.4 更新最佳模型
                        if (UpdateBestModel(inlier_count, total_cost, best_inlier_count,
                                            best_cost, current_model, best_model))
                        {
                            // 更新迭代次数k
                            double w = static_cast<double>(best_inlier_count) / static_cast<double>(sample_data_ptr->GetPopulationSize());
                            double p_no_outliers = 1.0 - std::pow(w, static_cast<double>(min_sample_size));

                            p_no_outliers = std::max(std::numeric_limits<double>::epsilon(), p_no_outliers);
                            p_no_outliers = std::min(1.0 - std::numeric_limits<double>::epsilon(), p_no_outliers);
                            k = std::log(1.0 - confidence) / std::log(p_no_outliers);
                            std::cout << std::fixed << std::setprecision(10) << "[pomvg]:iterations=" << iteration
                                      << ",best_inlier_count=" << best_inlier_count
                                      << ",best_cost=" << best_cost
                                      << ",k=" << k
                                      << std::endl;
                        }

                        ++iteration;
                    }

                    // 4. 为最佳模型构建内点集
                    if (best_model)
                    {
                        BuildBestInliers(best_model, sample_data_ptr);

                        // 将内点信息设置到DataSample中
                        auto inliers_ptr = std::make_shared<std::vector<size_t>>(best_inliers_);
                        sample_data_ptr->SetBestInliers(inliers_ptr);
                    }

                    // 5. 使用所有内点重新估计最终模型
                    return RefineFinalModel(best_model, sample_data_ptr);
                }
                catch (const std::exception &e)
                {
                    std::cerr << "RANSAC estimation failed: " << e.what() << std::endl;
                    return nullptr;
                }
            }

        private:
            /**
             * @brief 配置采样策略
             * @param sample_data_ptr 样本数据指针
             */
            void ConfigureSamplingStrategy(DataSamplePtr<TSample> sample_data_ptr)
            {
                if (!sample_data_ptr)
                    return;

                // 读取采样策略配置
                std::string strategy_str = GetOptionAsString("sampling_strategy", "adaptive");

                SamplingStrategy strategy = SamplingStrategy::ADAPTIVE;
                if (strategy_str == "uniform_random")
                {
                    strategy = SamplingStrategy::UNIFORM_RANDOM;
                }
                else if (strategy_str == "spherical_uniform")
                {
                    strategy = SamplingStrategy::SPHERICAL_UNIFORM;
                }
                else if (strategy_str == "stratified")
                {
                    strategy = SamplingStrategy::STRATIFIED;
                }
                else if (strategy_str == "adaptive")
                {
                    strategy = SamplingStrategy::ADAPTIVE;
                }

                // 设置采样策略
                sample_data_ptr->SetSamplingStrategy(strategy);

                if (log_level_ >= PO_LOG_NORMAL)
                {
                    PO_LOG(PO_LOG_NORMAL) << "[RANSAC] 采样策略设置为: " << strategy_str << std::endl;
                }
            }

            /**
             * @brief 使用最小样本集估计模型
             * @param estimator 模型估计器
             * @param sample_subset 最小样本集
             * @return 估计的模型
             */
            DataPtr EstimateModel(const DataPtr &subset) const
            {
                try
                {
                    if (!model_estimator_ptr_ || !subset)
                    {
                        return nullptr;
                    }

                    // 直接调用Build传入打包好的数据
                    return model_estimator_ptr_->Build(subset);
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Model estimation failed: " << e.what() << std::endl;
                    return nullptr;
                }
            }

            /**
             * @brief 评估当前模型
             * @return pair<内点数量, 总代价>
             */
            std::pair<size_t, double> EvaluateModel(const DataPtr &model,
                                                    const DataPtr &sample_data)
            {
                try
                {
                    if (!cost_evaluator_ptr_ || !model || !sample_data)
                    {
                        return {0, std::numeric_limits<double>::max()};
                    }

                    // 使用DataPackage打包数据
                    auto package = std::make_shared<DataPackage>();
                    package->AddData(model);
                    package->AddData(sample_data);

                    // 计算代价
                    auto costs = std::dynamic_pointer_cast<DataCosts>(cost_evaluator_ptr_->Build(package));
                    if (!costs)
                    {
                        return {0, std::numeric_limits<double>::max()};
                    }

                    // 统计内点
                    const double threshold = GetOptionAsFloat("inlier_threshold", 1.0f);
                    size_t inlier_count = 0;
                    double total_cost = 0.0;

                    for (size_t i = 0; i < costs->size(); ++i)
                    {
                        if ((*costs)[i] <= threshold)
                        {
                            ++inlier_count;
                            total_cost += (*costs)[i];
                        }
                    }

                    return {inlier_count, total_cost};
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Model evaluation failed: " << e.what() << std::endl;
                    return {0, std::numeric_limits<double>::max()};
                }
            }

            /**
             * @brief 为最佳模型构建内点集
             * @param best_model 最佳模型
             * @param sample_data 样本数据
             */
            void BuildBestInliers(const DataPtr &best_model, const DataPtr &sample_data)
            {
                try
                {
                    if (!cost_evaluator_ptr_ || !best_model || !sample_data)
                    {
                        return;
                    }

                    // 使用DataPackage打包数据
                    auto package = std::make_shared<DataPackage>();
                    package->AddData(best_model->GetType(), best_model);
                    package->AddData(sample_data->GetType(), sample_data);

                    // 计算代价
                    auto costs = std::dynamic_pointer_cast<DataCosts>(cost_evaluator_ptr_->Build(package));
                    if (!costs)
                    {
                        return;
                    }

                    // 统计内点并保存索引
                    const double threshold = GetOptionAsFloat("inlier_threshold", 1.0f);
                    best_inliers_.clear();

                    for (size_t i = 0; i < costs->size(); ++i)
                    {
                        if ((*costs)[i] <= threshold)
                        {
                            best_inliers_.push_back(i);
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Building best inliers failed: " << e.what() << std::endl;
                }
            }

            /**
             * @brief 更新最佳模型
             * @return 是否更新了最佳模型
             */
            bool UpdateBestModel(size_t inlier_count, double total_cost,
                                 size_t &best_inlier_count, double &best_cost,
                                 const DataPtr &current_model, DataPtr &best_model) noexcept
            {
                if (inlier_count > best_inlier_count ||
                    (inlier_count == best_inlier_count && total_cost < best_cost))
                {
                    best_inlier_count = inlier_count;
                    best_cost = total_cost;
                    best_model = current_model;
                    return true;
                }
                return false;
            }

            /**
             * @brief 使用所有内点重新估计最终模型
             * @return 最终模型的数据指针
             */
            DataPtr RefineFinalModel(const DataPtr &best_model,
                                     const DataPtr &sample_data)
            {
                try
                {
                    if (!model_estimator_ptr_ || !best_model || !sample_data)
                    {
                        return best_model;
                    }

                    // 使用已定义的类型转换函数
                    auto typed_sample = CastToSample<TSample>(sample_data);
                    if (!typed_sample)
                    {
                        std::cerr << "Failed to cast sample data to correct type" << std::endl;
                        return best_model;
                    }

                    // 获取内点子集
                    auto inlier_subset = typed_sample->GetSubset(best_inliers_);
                    if (!inlier_subset)
                    {
                        std::cerr << "Failed to create inlier subset" << std::endl;
                        return best_model;
                    }

                    // 使用DataPackage打包数据
                    auto package = std::make_shared<DataPackage>();
                    package->AddData("data_sample", inlier_subset);

                    // 使用内点重新估计模型
                    auto refined_model = model_estimator_ptr_->Build(package);
                    return refined_model ? refined_model : best_model;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Final model refinement failed: " << e.what() << std::endl;
                    return best_model;
                }
            }
        };

    } // namespace Interface
} // namespace PoSDK

#endif // POMVG_INTERFACES_RANSAC_ESTIMATOR_HPP_
