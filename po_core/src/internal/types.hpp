/**
 * @file types.hpp
 * @brief Core type definitions for the PoSDK library
 * @details Defines basic types, data structures and enumerations used throughout PoSDK
 *
 * @copyright Copyright (c) 2021-2024 Qi <PERSON>ai
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef _POMVG_TYPES_HPP_
#define _POMVG_TYPES_HPP_

#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <Eigen/Geometry>
#include <map>
#include <memory>
#include <string>
#include <vector>
#include <iostream>
#include <unordered_map>
#include <set>
#include <cstddef>
#include <algorithm>
#include <cmath>
#include <limits>
#include <numeric>

#include <ceres/ceres.h>
#include <ceres/rotation.h>

namespace PoSDK
{
    namespace types
    {
        //------------------------------------------------------------------------------
        // Smart Pointer Types
        //------------------------------------------------------------------------------

        /// @brief Shared pointer type definitions
        template <typename T>
        using Ptr = std::shared_ptr<T>;

        //------------------------------------------------------------------------------
        // Basic Types and Eigen Types
        //------------------------------------------------------------------------------

        using IndexT = uint32_t; ///< Index type used throughout the library (最大值为4,294,967,295)
        using ViewId = uint32_t; ///< View identifier type
        using PtsId = uint32_t;  ///< Point/Track identifier type
        using Size = uint32_t;   ///< Size type

        // Eigen type aliases
        using SpMatrix = Eigen::SparseMatrix<double>; ///< Sparse matrix type
        using Matrix3d = Eigen::Matrix3d;             ///< 3x3 double matrix
        using Vector3d = Eigen::Vector3d;             ///< 3D double vector
        using Vector2f = Eigen::Vector2f;             ///< 2D float vector
        using Vector2d = Eigen::Vector2d;             ///< 2D float vector
        using VectorXd = Eigen::VectorXd;             ///< Dynamic size double vector
        using RowVectorXd = Eigen::RowVectorXd;       ///< Dynamic size row vector
        using MatrixXd = Eigen::MatrixXd;             ///< Dynamic size matrix

        using InfoMatPtr = double *; // G2O节点信息矩阵的指针类型,用于存储不同大小的信息矩阵，行优先

        //------------------------------------------------------------------------------
        // Environment Variables
        //------------------------------------------------------------------------------

        /// @brief Global environment variables manager
        class EnvVars
        {
        public:
            /// @brief Initialize environment variables with default values
            static void InitEnvVars();

            /// @brief Get/Set initial BA path
            static const std::string &GetInitBalPath() { return gpath_ini_bal; }
            static void SetInitBalPath(const std::string &path) { gpath_ini_bal = path; }

            /// @brief Get/Set optimized BA path
            static const std::string &GetOptBalPath() { return gpath_opt_bal; }
            static void SetOptBalPath(const std::string &path) { gpath_opt_bal = path; }

            // Public for backward compatibility
            static std::string gpath_ini_bal; ///< Initial BA path
            static std::string gpath_opt_bal; ///< Optimized BA path
        };

        //------------------------------------------------------------------------------
        // Method Types
        //------------------------------------------------------------------------------
        typedef std::string MethodType; // 方法类型名称，如"SIFT", "ORB"等

        typedef std::string MethodParams; // 方法参数名称，如"nfeatures", "threshold"等

        typedef std::string ParamsValue; // 参数值类型，以字符串形式保存

        typedef std::unordered_map<MethodParams, ParamsValue> MethodOptions; // 单个方法的所有参数配置

        typedef std::unordered_map<MethodType, MethodOptions> MethodsConfig; // 所有方法的配置集合

        //------------------------------------------------------------------------------
        // Image Paths
        //------------------------------------------------------------------------------

        /// @brief Type definition for storing image paths and their validity status
        using ImagePaths = std::vector<std::pair<std::string, bool>>;
        using ImagePathsPtr = std::shared_ptr<ImagePaths>;

        //------------------------------------------------------------------------------
        // Camera Intrinsic Types
        //------------------------------------------------------------------------------

        /**
         * @brief 相机畸变类型枚举
         */
        enum class DistortionType
        {
            NO_DISTORTION, ///< 无畸变
            RADIAL_K1,     ///< 一阶径向畸变
            RADIAL_K3,     ///< 三阶径向畸变
            BROWN_CONRADY  ///< Brown-Conrady畸变模型(包含切向畸变)
        };

        /**
         * @brief 相机模型类型枚举
         */
        enum class CameraModelType
        {
            PINHOLE,        ///< 针孔相机模型
            FISHEYE,        ///< 鱼眼相机模型
            SPHERICAL,      ///< 球面相机模型
            OMNIDIRECTIONAL ///< 全向相机模型
        };

        /**
         * @brief 相机内参数据结构
         */
        struct CameraIntrinsics
        {
            double fx{1.0};                                                ///< x方向焦距
            double fy{1.0};                                                ///< y方向焦距
            double cx{0.0};                                                ///< x方向主点偏移
            double cy{0.0};                                                ///< y方向主点偏移
            uint32_t width{0};                                             ///< 图像宽度
            uint32_t height{0};                                            ///< 图像高度
            CameraModelType model_type{CameraModelType::PINHOLE};          ///< 相机模型类型
            DistortionType distortion_type{DistortionType::NO_DISTORTION}; ///< 畸变类型
            std::vector<double> radial_distortion;                         ///< 径向畸变参数
            std::vector<double> tangential_distortion;                     ///< 切向畸变参数

            // 获取相机内参矩阵,使用引用避免拷贝
            void GetK(Matrix3d &K) const
            {
                K.setIdentity();
                K(0, 0) = fx;
                K(1, 1) = fy;
                K(0, 2) = cx;
                K(1, 2) = cy;
            }

            // 设置相机内参矩阵,使用常量引用避免拷贝
            void SetK(const Matrix3d &K)
            {
                fx = K(0, 0);
                fy = K(1, 1);
                cx = K(0, 2);
                cy = K(1, 2);
            }

            /**
             * @brief 设置相机内参 (方式1)
             * @param intrinsics 相机内参
             */
            void SetCameraIntrinsics(const CameraIntrinsics &intrinsics)
            {
                this->fx = intrinsics.fx;
                this->fy = intrinsics.fy;
                this->cx = intrinsics.cx;
                this->cy = intrinsics.cy;
                this->width = intrinsics.width;
                this->height = intrinsics.height;
                this->model_type = intrinsics.model_type;
                this->distortion_type = intrinsics.distortion_type;
                this->radial_distortion = intrinsics.radial_distortion;
                this->tangential_distortion = intrinsics.tangential_distortion;
            }

            /**
             * @brief 设置相机内参 (方式2)
             * @param fx 焦距
             * @param fy 焦距
             * @param cx 主点偏移
             * @param cy 主点偏移
             * @note:如果width,height没有初始化，则根据cx,cy初始化width,height
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const double cx,
                                     const double cy)
            {
                this->fx = fx;
                this->fy = fy;
                this->cx = cx;
                this->cy = cy;

                // 初始化长宽
                if (width == 0)
                {
                    this->width = cx * 2;
                }
                if (height == 0)
                {
                    this->height = cy * 2;
                }
            }

            /**
             * @brief 设置相机内参 (方式3)
             * @param fx 焦距
             * @param fy 焦距
             * @param width 图像宽度
             * @param height 图像高度
             * @note:如果cx,cy没有初始化，则根据width,height初始化cx,cy
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const uint32_t width,
                                     const uint32_t height)
            {
                this->fx = fx;
                this->fy = fy;
                this->width = width;
                this->height = height;

                // 初始化cx,cy
                if (cx == 0.0)
                {
                    this->cx = width / 2;
                }
                if (cy == 0.0)
                {
                    this->cy = height / 2;
                }
            }

            /**
             * @brief 设置相机内参 (方式4)
             * @param fx x方向焦距
             * @param fy y方向焦距
             * @param cx x方向主点偏移
             * @param cy y方向主点偏移
             * @param width 图像宽度
             * @param height 图像高度
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const double cx,
                                     const double cy,
                                     const uint32_t width,
                                     const uint32_t height)
            {
                this->fx = fx;
                this->fy = fy;
                this->cx = cx;
                this->cy = cy;
                this->width = width;
                this->height = height;
            }

            /**
             * @brief 设置相机内参 (方式5)
             * @param fx x方向焦距
             * @param fy y方向焦距
             * @param cx x方向主点偏移
             * @param cy y方向主点偏移
             * @param width 图像宽度
             * @param height 图像高度
             * @param radial_distortion 径向畸变参数
             * @param tangential_distortion 切向畸变参数(可选)
             * @param model_type 相机模型类型(默认针孔相机)
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const double cx,
                                     const double cy,
                                     const uint32_t width,
                                     const uint32_t height,
                                     const std::vector<double> &radial_distortion,
                                     const std::vector<double> &tangential_distortion = {},
                                     const CameraModelType model_type = CameraModelType::PINHOLE)
            {
                this->fx = fx;
                this->fy = fy;
                this->cx = cx;
                this->cy = cy;
                this->width = width;
                this->height = height;
                this->model_type = model_type;
                this->radial_distortion = radial_distortion;
                this->tangential_distortion = tangential_distortion;

                // 识别畸变类型
                if (radial_distortion.size() == 0)
                {
                    this->distortion_type = DistortionType::NO_DISTORTION;
                }
                else if (radial_distortion.size() == 1)
                {
                    this->distortion_type = DistortionType::RADIAL_K1;
                }
                else if (radial_distortion.size() == 3)
                {
                    this->distortion_type = DistortionType::RADIAL_K3;
                }

                if (radial_distortion.size() == 3 && tangential_distortion.size() == 2)
                {
                    this->distortion_type = DistortionType::BROWN_CONRADY;
                }
            }

            // 构造函数
            CameraIntrinsics()
            {
                InitDistortionParams();
            }

            // 初始化畸变参数
            void InitDistortionParams()
            {
                switch (distortion_type)
                {
                case DistortionType::NO_DISTORTION:
                    radial_distortion.clear();
                    tangential_distortion.clear();
                    break;
                case DistortionType::RADIAL_K1:
                    radial_distortion.resize(1, 0.0);
                    tangential_distortion.clear();
                    break;
                case DistortionType::RADIAL_K3:
                    radial_distortion.resize(3, 0.0);
                    tangential_distortion.clear();
                    break;
                case DistortionType::BROWN_CONRADY:
                    radial_distortion.resize(3, 0.0);     // k1, k2, k3
                    tangential_distortion.resize(2, 0.0); // p1, p2
                    break;
                }
            }
        };

        /**
         * @brief 相机模型完整定义
         */
        struct CameraModel
        {
            CameraIntrinsics intrinsics; ///< 相机内参
            std::string camera_make;     ///< 相机制造商
            std::string camera_model;    ///< 相机型号
            std::string serial_number;   ///< 序列号

            /**
             * @brief 将像素坐标转换为归一化坐标
             * @param pixel_coord 像素坐标
             * @return 归一化坐标
             */
            Vector2d PixelToNormalized(const Vector2d &pixel_coord) const
            {
                Vector2d normalized;
                normalized.x() = (pixel_coord.x() - intrinsics.cx) / intrinsics.fx;
                normalized.y() = (pixel_coord.y() - intrinsics.cy) / intrinsics.fy;
                return normalized;
            }

            /**
             * @brief 将归一化坐标转换为像素坐标
             * @param normalized_coord 归一化坐标
             * @return 像素坐标
             */
            Vector2d NormalizedToPixel(const Vector2d &normalized_coord) const
            {
                Vector2d pixel;
                pixel.x() = normalized_coord.x() * intrinsics.fx + intrinsics.cx;
                pixel.y() = normalized_coord.y() * intrinsics.fy + intrinsics.cy;
                return pixel;
            }

            // 设置相机内参矩阵
            void SetKMat(const Matrix3d &K)
            {
                intrinsics.SetK(K); // 移除std::move,因为SetK已经使用常量引用避免拷贝
            }

            // 获取相机内参矩阵
            void GetKMat(Matrix3d &K) const
            {
                intrinsics.GetK(K); // 使用引用参数避免拷贝
            }

            // 设置畸变类型和参数
            void SetDistortionParams(const DistortionType &distortion_type,
                                     const std::vector<double> &radial_distortion,
                                     const std::vector<double> &tangential_distortion)
            {
                intrinsics.distortion_type = distortion_type;
                intrinsics.radial_distortion = radial_distortion;
                intrinsics.tangential_distortion = tangential_distortion;
            }

            // 设置相机模型类型
            void SetModelType(const CameraModelType &model_type)
            {
                intrinsics.model_type = model_type;
            }

            // 设置相机制造商/型号/序列号
            void SetCameraInfo(const std::string &camera_make,
                               const std::string &camera_model,
                               const std::string &serial_number)
            {
                this->camera_make = camera_make;
                this->camera_model = camera_model;
                this->serial_number = serial_number;
            }

            /**
             * @brief 设置相机内参 (方式1)
             * @param intrinsics 相机内参结构体
             */
            void SetCameraIntrinsics(const CameraIntrinsics &intrinsics)
            {
                this->intrinsics.SetCameraIntrinsics(intrinsics);
            }

            /**
             * @brief 设置相机内参 (方式2)
             * @param fx 焦距
             * @param fy 焦距
             * @param cx 主点偏移
             * @param cy 主点偏移
             * @note:如果width,height没有初始化，则根据cx,cy初始化width,height
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const double cx,
                                     const double cy)
            {
                intrinsics.SetCameraIntrinsics(fx, fy, cx, cy);
            }

            /**
             * @brief 设置相机内参 (方式3)
             * @param fx x方向焦距
             * @param fy y方向焦距
             * @param width 图像宽度
             * @param height 图像高度
             * @note:如果cx,cy没有初始化，则根据width,height初始化cx,cy
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const uint32_t width,
                                     const uint32_t height)
            {
                intrinsics.SetCameraIntrinsics(fx, fy, width, height);
            }

            /**
             * @brief 设置相机内参 (方式4)
             * @param fx x方向焦距
             * @param fy y方向焦距
             * @param cx x方向主点偏移
             * @param cy y方向主点偏移
             * @param width 图像宽度
             * @param height 图像高度
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const double cx,
                                     const double cy,
                                     const uint32_t width,
                                     const uint32_t height)
            {
                intrinsics.SetCameraIntrinsics(fx, fy, cx, cy, width, height);
            }

            /**
             * @brief 设置相机内参 (方式5)
             * @param fx x方向焦距
             * @param fy y方向焦距
             * @param cx x方向主点偏移
             * @param cy y方向主点偏移
             * @param width 图像宽度
             * @param height 图像高度
             * @param radial_distortion 径向畸变参数
             * @param tangential_distortion 切向畸变参数(可选)
             * @param model_type 相机模型类型(默认针孔相机)
             */
            void SetCameraIntrinsics(const double fx,
                                     const double fy,
                                     const double cx,
                                     const double cy,
                                     const uint32_t width,
                                     const uint32_t height,
                                     const std::vector<double> &radial_distortion,
                                     const std::vector<double> &tangential_distortion = {},
                                     const CameraModelType model_type = CameraModelType::PINHOLE)
            {
                intrinsics.SetCameraIntrinsics(fx, fy, cx, cy, width, height,
                                               radial_distortion, tangential_distortion, model_type);
            }
        };

        using CameraModelPtr = std::shared_ptr<CameraModel>;

        /**
         * @brief 智能相机模型容器类
         * @details 重载operator[]以支持智能的相机模型访问：
         *          - 单相机模型时，所有视图共用同一个模型
         *          - 多相机模型时，根据view_id选择对应模型
         */
        class CameraModels : public std::vector<CameraModel>
        {
        public:
            // 继承基类的构造函数
            using std::vector<CameraModel>::vector;

            /**
             * @brief 智能索引访问（非const版本）
             * @param view_id 视图ID
             * @return 相机模型引用
             * @throws std::out_of_range 当访问无效时抛出异常
             */
            CameraModel &operator[](ViewId view_id)
            {
                if (empty())
                {
                    throw std::out_of_range("[CameraModels] Camera models is empty");
                }

                // 如果只有一个相机模型，则所有视图共用该模型
                if (size() == 1)
                {
                    return std::vector<CameraModel>::operator[](0);
                }

                // 如果有多个相机模型，则根据view_id查找对应的模型
                if (view_id >= size())
                {
                    throw std::out_of_range("[CameraModels] View ID " + std::to_string(view_id) +
                                            " exceeds camera models size " + std::to_string(size()));
                }

                return std::vector<CameraModel>::operator[](view_id);
            }

            /**
             * @brief 智能索引访问（const版本）
             * @param view_id 视图ID
             * @return 相机模型常量引用
             * @throws std::out_of_range 当访问无效时抛出异常
             */
            const CameraModel &operator[](ViewId view_id) const
            {
                if (empty())
                {
                    throw std::out_of_range("[CameraModels] Camera models is empty");
                }

                // 如果只有一个相机模型，则所有视图共用该模型
                if (size() == 1)
                {
                    return std::vector<CameraModel>::operator[](0);
                }

                // 如果有多个相机模型，则根据view_id查找对应的模型
                if (view_id >= size())
                {
                    throw std::out_of_range("[CameraModels] View ID " + std::to_string(view_id) +
                                            " exceeds camera models size " + std::to_string(size()));
                }

                return std::vector<CameraModel>::operator[](view_id);
            }

            /**
             * @brief 安全的索引访问，返回指针（非const版本）
             * @param view_id 视图ID
             * @return 相机模型指针，失败时返回nullptr
             */
            CameraModel *at_safe(ViewId view_id)
            {
                try
                {
                    return &(*this)[view_id];
                }
                catch (const std::out_of_range &)
                {
                    return nullptr;
                }
            }

            /**
             * @brief 安全的索引访问，返回指针（const版本）
             * @param view_id 视图ID
             * @return 相机模型指针，失败时返回nullptr
             */
            const CameraModel *at_safe(ViewId view_id) const
            {
                try
                {
                    return &(*this)[view_id];
                }
                catch (const std::out_of_range &)
                {
                    return nullptr;
                }
            }
        };

        using CameraModelsPtr = std::shared_ptr<CameraModels>;

        /**
         * @brief 获取指定视图的相机模型
         * @param camera_models 相机模型集合
         * @param view_id 视图ID
         * @return 相机模型指针，如果是单相机模型则返回第一个
         */
        inline CameraModel *GetCameraModel(
            CameraModels &camera_models,
            ViewId view_id)
        {

            if (camera_models.empty())
            {
                std::cerr << "[OpenGVConverter] Camera models is empty" << std::endl;
                return nullptr;
            }

            // 如果只有一个相机模型，则所有视图共用该模型
            if (camera_models.size() == 1)
            {
                return &camera_models[0];
            }

            // 如果有多个相机模型，则根据view_id查找对应的模型
            if (view_id >= camera_models.size())
            {
                std::cerr << "[OpenGVConverter] View ID " << view_id
                          << " exceeds camera models size " << camera_models.size() << std::endl;
                return nullptr;
            }

            // 在多相机模型情况下，view_id对应camera_models中的索引
            return &camera_models[view_id];
        }

        // const版本
        inline const CameraModel *GetCameraModel(
            const CameraModels &camera_models,
            ViewId view_id)
        {

            if (camera_models.empty())
            {
                std::cerr << "[OpenGVConverter] Camera models is empty" << std::endl;
                return nullptr;
            }

            // 如果只有一个相机模型，则所有视图共用该模型
            if (camera_models.size() == 1)
            {
                return &camera_models[0];
            }

            // 如果有多个相机模型，则根据view_id查找对应的模型
            if (view_id >= camera_models.size())
            {
                std::cerr << "[OpenGVConverter] View ID " << view_id
                          << " exceeds camera models size " << camera_models.size() << std::endl;
                return nullptr;
            }

            // 在多相机模型情况下，view_id对应camera_models中的索引
            return &camera_models[view_id];
        }

        /**
         * @brief 相机内参矩阵相关类型定义
         * @details 用于存储和管理相机的内部参数矩阵
         */
        using KMats = std::vector<Matrix3d, Eigen::aligned_allocator<Matrix3d>>; ///< 内参矩阵序列容器
        using KMatsPtr = std::shared_ptr<KMats>;                                 ///< 内参矩阵序列的智能指针

        //------------------------------------------------------------------------------
        // Feature and Match Types
        //------------------------------------------------------------------------------

        /// @brief 基础特征点（仅包含位置信息）
        using Feature = Vector2d; ///< Basic 2D feature point

        /// @brief 特征描述子类型定义(暂不使用)
        using Descriptor = std::vector<float>;

        /// @brief 描述子集合类型定义(暂不使用)
        using Descs = std::vector<Descriptor>;

        /// @brief 完整特征点信息
        struct FeaturePoint
        {
            EIGEN_MAKE_ALIGNED_OPERATOR_NEW // 添加 Eigen 对齐宏
                Feature coord;              ///< 特征点位置 (x,y)
            float size;                     ///< 特征点大小
            float angle;                    ///< 特征点方向
            // 描述子已移除，将单独处理
            bool is_used{true}; ///< 特征点是否被使用

            // 构造函数 - 不使用 Feature::Zero() 避免创建临时对象
            explicit FeaturePoint()
            {
                coord(0) = 0.0;
                coord(1) = 0.0;
                size = 0.0f;
                angle = 0.0f;
            }

            // 从基础特征点构造 - 避免直接赋值
            explicit FeaturePoint(const Feature &pos, float size_ = 0, float angle_ = 0)
            {
                coord(0) = pos(0);
                coord(1) = pos(1);
                size = size_;
                angle = angle_;
            }

            // 从坐标构造 - 直接设置分量
            explicit FeaturePoint(float x, float y, float size_ = 0, float angle_ = 0)
            {
                coord(0) = x;
                coord(1) = y;
                size = size_;
                angle = angle_;
            }

            // 转换为基础特征点
            operator Feature() const { return coord; }
        };

        /// @brief 单张图片的特征信息
        struct ImageFeatureInfo
        {
            EIGEN_MAKE_ALIGNED_OPERATOR_NEW                                             // 如果 ImageFeatureInfo 本身也可能在需要对齐的容器中或动态分配，可以加上
                std::string image_path;                                                 ///< 图像文件路径
            std::vector<FeaturePoint, Eigen::aligned_allocator<FeaturePoint>> features; ///< 特征点集合, 使用对齐分配器
            bool is_used{true};                                                         ///< 该图像特征是否被使用

            // 默认构造函数
            ImageFeatureInfo() = default;

            // 带参数的构造函数
            explicit ImageFeatureInfo(const std::string &path, bool used = true)
                : image_path(path), is_used(used) {}

            // 添加特征点
            void AddFeature(const FeaturePoint &feat)
            {
                features.push_back(feat);
            }

            // 获取特征点数量
            Size GetNumFeatures() const
            {
                return features.size();
            }

            // 清除未使用的特征点
            void ClearUnusedFeatures()
            {
                features.erase(
                    std::remove_if(features.begin(), features.end(),
                                   [](const FeaturePoint &feat)
                                   { return !feat.is_used; }),
                    features.end());
            }
        };

        /// @brief 所有图片的特征信息集合
        class FeaturesInfo : public std::vector<ImageFeatureInfo>
        {
        public:
            // 继承vector的构造函数
            using std::vector<ImageFeatureInfo>::vector;

            // 添加图像特征
            void AddImageFeatures(const std::string &image_path, bool is_used = true)
            {
                this->emplace_back(image_path, is_used);
            }

            // 获取有效图像数量
            Size GetNumValidImages() const
            {
                return std::count_if(this->begin(), this->end(),
                                     [](const ImageFeatureInfo &info)
                                     { return info.is_used; });
            }

            // 清除未使用的图像
            void ClearUnusedImages()
            {
                this->erase(
                    std::remove_if(this->begin(), this->end(),
                                   [](const ImageFeatureInfo &info)
                                   { return !info.is_used; }),
                    this->end());
            }

            // 清除所有未使用的特征点
            void ClearAllUnusedFeatures()
            {
                for (auto &image_info : *this)
                {
                    if (image_info.is_used)
                    {
                        image_info.ClearUnusedFeatures();
                    }
                }
            }
        };

        /// @brief 特征信息的智能指针类型
        using FeaturesInfoPtr = Ptr<FeaturesInfo>;

        /// @brief 特征点匹配
        struct IdMatch
        {
            IndexT i;              ///< Index of first feature
            IndexT j;              ///< Index of second feature
            bool is_inlier{false}; ///< RANSAC inlier flag
        };

        using IdMatches = std::vector<IdMatch>;
        using ViewPair = std::pair<IndexT, IndexT>;    ///< Pair of view indices
        using Matches = std::map<ViewPair, IdMatches>; ///< All matches between views
        using MatchesPtr = Ptr<Matches>;
        using BearingVectors = Eigen::Matrix<double, 3, Eigen::Dynamic>; // 3xN的观测向量
        using BearingPairs = std::vector<Eigen::Matrix<double, 6, 1>>;
        using BearingVector = Vector3d; ///< 单位观测向量类型

        /**
         * @brief 将像素坐标转换为单位观测向量
         * @param pixel_coord 像素坐标
         * @param camera_model 相机模型
         * @return 单位观测向量
         */
        inline BearingVector PixelToBearingVector(const Vector2d &pixel_coord, const CameraModel &camera_model)
        {
            // 转换为归一化坐标
            Vector2d normalized = camera_model.PixelToNormalized(pixel_coord);

            // 构造3D向量并归一化
            Vector3d bearing_vector(normalized.x(), normalized.y(), 1.0);
            bearing_vector.normalize();

            return bearing_vector;
        }

        /**
         * @brief 将像素坐标转换为单位观测向量（重载版本）
         * @param pixel_coord 像素坐标
         * @param fx x方向焦距
         * @param fy y方向焦距
         * @param cx x方向主点偏移
         * @param cy y方向主点偏移
         * @return 单位观测向量
         */
        inline BearingVector PixelToBearingVector(const Vector2d &pixel_coord,
                                                  double fx, double fy, double cx, double cy)
        {
            // 转换为归一化坐标
            Vector2d normalized;
            normalized.x() = (pixel_coord.x() - cx) / fx;
            normalized.y() = (pixel_coord.y() - cy) / fy;

            // 构造3D向量并归一化
            Vector3d bearing_vector(normalized.x(), normalized.y(), 1.0);
            bearing_vector.normalize();

            return bearing_vector;
        }

        //------------------------------------------------------------------------------
        // Track Types
        //------------------------------------------------------------------------------
        /// @brief 3D点观测信息
        struct ObsInfo
        {
            EIGEN_MAKE_ALIGNED_OPERATOR_NEW // 添加 Eigen 对齐宏
                Vector2d coord;             // 图像特征点的2D坐标 (移到最前以保证对齐)
            ViewId view_id;                 // 图像ID (与ImagePaths中的索引一致)
            PtsId pts_id;                   // 3D点ID
            IndexT obs_id{0};               // 观测ID
            bool is_used{true};             // 当前观测是否被使用

            // 添加显式的构造函数 (初始化列表顺序已更新)
            ObsInfo() : coord(Vector2d::Zero()), view_id(0), pts_id(0), obs_id(0), is_used(true) {}
            ObsInfo(ViewId vid, PtsId pid, const Vector2d &c) // 参数顺序保持，但初始化列表顺序更改
                : coord(c), view_id(vid), pts_id(pid), obs_id(0), is_used(true)
            {
            }

            /// @brief 获取观测坐标(如果观测不可用返回false)
            /// @param[out] coord_out 输出坐标
            /// @return 是否成功获取坐标
            bool GetCoord(Vector2d &coord_out) const
            {
                coord_out = coord;
                return true;
            }

            /// @brief 获取观测坐标（齐次坐标）
            /// @return 齐次坐标
            Vector3d GetHomoCoord() const
            {
                return Vector3d(coord.x(), coord.y(), 1.0);
            }

            /// @brief 设置观测使用状态
            /// @param used 是否使用该观测
            void SetUsed(bool used)
            {
                is_used = used;
            }
        };

        /// @brief 3D点观测信息集合
        class Track : public std::vector<ObsInfo, Eigen::aligned_allocator<ObsInfo>>
        {
        public:
            // 继承 vector 的构造函数
            using std::vector<ObsInfo, Eigen::aligned_allocator<ObsInfo>>::vector;

            /// @brief 添加观测
            /// @param view_id 视图ID
            /// @param track_id 跟踪点ID
            /// @param coord 观测坐标
            void AddObservation(ViewId view_id, PtsId track_id, const Vector2d &coord)
            {
                this->emplace_back(view_id, track_id, coord);
            }

            /// @brief 获取观测点数量
            /// @return 观测点数量
            Size GetNumObservations() const
            {
                return this->size();
            }

            /// @brief 获取指定索引的观测信息
            /// @param index 观测索引
            /// @return 观测信息的常量引用
            const ObsInfo &GetObservation(IndexT index) const
            {
                return (*this)[index];
            }

            /// @brief 获取有效观测数量
            /// @return 有效观测的数量
            Size GetValidObservationCount() const
            {
                return std::count_if(this->begin(), this->end(),
                                     [](const ObsInfo &obs)
                                     { return obs.is_used; });
            }

            /// @brief 设置指定观测的使用状态
            /// @param index 观测索引
            /// @param used 是否使用该观测
            void SetObservationUsed(IndexT index, bool used)
            {
                (*this)[index].is_used = used;
            }
        };

        /// @brief 跟踪信息，包括使用标志
        struct TrackInfo
        {
            EIGEN_MAKE_ALIGNED_OPERATOR_NEW // 添加 Eigen 对齐宏
                Track track;
            bool is_used{true};

            // 添加默认构造函数
            TrackInfo() = default;

            // 添加带参数的构造函数
            explicit TrackInfo(const Track &t, bool used = true)
                : track(t), is_used(used) {}

            // 添加显式的拷贝构造函数
            TrackInfo(const TrackInfo &) = default;
            // 添加显式的移动构造函数
            TrackInfo(TrackInfo &&) noexcept = default;
            // 添加显式的赋值运算符
            TrackInfo &operator=(const TrackInfo &) = default;
            TrackInfo &operator=(TrackInfo &&) noexcept = default;

            /// @brief 获取Track的观测数量
            /// @return 观测数量
            Size GetObservationCount() const
            {
                return track.size();
            }

            /// @brief 获取有效观测数量
            /// @return 有效观测的数量
            Size GetValidObservationCount() const
            {
                return track.GetValidObservationCount();
            }

            /// @brief 获取指定观测信息
            /// @param index 观测索引
            /// @return 观测信息的常量引用
            const ObsInfo &GetObservation(IndexT index) const
            {
                return track[index];
            }

            /// @brief 获取指定观测的坐标
            /// @param index 观测索引
            /// @param[out] coord_out 输出坐标
            /// @return 是否成功获取坐标
            bool GetObservationCoord(IndexT index, Vector2d &coord_out) const
            {
                return track[index].GetCoord(coord_out);
            }

            /// @brief 设置Track的使用状态
            /// @param used 是否使用该Track
            void SetUsed(bool used)
            {
                is_used = used;
            }

            /// @brief 设置指定观测的使用状态
            /// @param index 观测索引
            /// @param used 是否使用该观测
            void SetObservationUsed(IndexT index, bool used)
            {
                track.SetObservationUsed(index, used);
            }

            /// @brief 添加新的观测
            /// @param obs 观测信息
            void AddObservation(const ObsInfo &obs)
            {
                track.push_back(obs);
            }
        };

        class Tracks : public std::vector<TrackInfo, Eigen::aligned_allocator<TrackInfo>>
        {
        private:
            std::vector<TrackInfo, Eigen::aligned_allocator<TrackInfo>> tracks_;
            bool is_normalized_{false}; // 新增变量，表示是否已归一化

        public:
            // 迭代器类型定义
            using iterator = std::vector<TrackInfo, Eigen::aligned_allocator<TrackInfo>>::iterator;
            using const_iterator = std::vector<TrackInfo, Eigen::aligned_allocator<TrackInfo>>::const_iterator;
            using reference = TrackInfo &;
            using const_reference = const TrackInfo &;

            // 标准容器接口
            void reserve(Size n) { tracks_.reserve(n); }
            Size size() const { return tracks_.size(); }
            bool empty() const { return tracks_.empty(); }
            void clear() { tracks_.clear(); }

            // 访问元素
            reference operator[](Size index) { return tracks_[index]; }
            const_reference operator[](Size index) const { return tracks_[index]; }

            // 迭代器接口
            iterator begin() { return tracks_.begin(); }
            iterator end() { return tracks_.end(); }
            const_iterator begin() const { return tracks_.begin(); }
            const_iterator end() const { return tracks_.end(); }
            const_iterator cbegin() const { return tracks_.cbegin(); }
            const_iterator cend() const { return tracks_.cend(); }

            // 修改容器
            void push_back(const TrackInfo &track) { tracks_.push_back(track); }
            void push_back(TrackInfo &&track) { tracks_.push_back(std::move(track)); }
            template <typename... Args>
            void emplace_back(Args &&...args) { tracks_.emplace_back(std::forward<Args>(args)...); }

            // 原有的接口方法
            /// @brief 添加新的Track
            /// @param observations 观测信息集合
            void AddTrack(const std::vector<ObsInfo> &observations)
            {
                TrackInfo track_info;
                track_info.track.reserve(observations.size());
                for (const auto &obs : observations)
                {
                    track_info.track.push_back(obs);
                }
                tracks_.push_back(std::move(track_info));
            }

            /// @brief 添加观测到指定Track
            /// @param track_id 跟踪点ID
            /// @param obs 观测信息
            void AddObservation(PtsId track_id, const ObsInfo &obs)
            {
                for (auto &track : tracks_)
                {
                    if (track.track[track_id].view_id == 0)
                    {
                        track.track.push_back(obs);
                        break;
                    }
                }
            }

            /// @brief 获取Track数量
            /// @return Track数量
            Size GetTrackCount() const
            {
                return tracks_.size();
            }

            /// @brief 获取指定Track的观测数量
            /// @param track_id 跟踪点ID
            /// @return 观测数量
            Size GetObservationCount(PtsId track_id) const
            {
                return tracks_[track_id].track.size();
            }

            /// @brief 获取指定Track的指定观测
            /// @param track_id 跟踪点ID
            /// @param index 观测索引
            /// @return 观测信息的常量引用
            const ObsInfo &GetObservation(PtsId track_id, IndexT index) const
            {
                return tracks_[track_id].track[index];
            }

            /// @brief 获取有效Track数量
            /// @return 有效Track数量
            Size GetValidTrackCount() const
            {
                return std::count_if(tracks_.begin(), tracks_.end(),
                                     [](const TrackInfo &track)
                                     { return track.is_used; });
            }

            /// @brief 获取归一化状态
            /// @return 是否已归一化
            bool IsNormalized() const { return is_normalized_; }

            /// @brief 设置归一化状态
            /// @param normalized 归一化状态
            void SetNormalized(bool normalized) { is_normalized_ = normalized; }

            /**
             * @brief 使用相机模型将整个轨迹集归一化
             * @param camera_models 相机模型集合
             * @return 归一化是否成功
             */
            bool NormalizeTracks(const CameraModels &camera_models)
            {
                if (is_normalized_)
                {
                    // 已经归一化，无需操作
                    return true;
                }

                if (empty())
                {
                    std::cerr << "轨迹为空，无法归一化" << std::endl;
                    return false;
                }

                // 计数用于日志输出
                size_t total_observations = 0;
                size_t normalized_observations = 0;

                // 遍历每个Track
                for (auto &track_info : *this)
                {
                    if (!track_info.is_used)
                        continue;

                    // 遍历Track中的每个观测
                    for (auto &obs : track_info.track)
                    {
                        if (!obs.is_used)
                            continue;

                        total_observations++;

                        // 获取对应视图的相机模型
                        const CameraModel *camera_model = GetCameraModel(camera_models, obs.view_id);
                        if (!camera_model)
                        {
                            std::cerr << "警告: 视图 " << obs.view_id << " 没有对应的相机模型" << std::endl;
                            continue;
                        }

                        // 将像素坐标转换为归一化坐标
                        Vector2d normalized_coord = camera_model->PixelToNormalized(obs.coord);
                        obs.coord = normalized_coord;
                        normalized_observations++;
                    }
                }

                // 标记轨迹已归一化
                is_normalized_ = true;

                std::cout << "轨迹归一化完成: 处理了 " << normalized_observations
                          << "/" << total_observations << " 个观测点" << std::endl;
                return true;
            }
        };
        using TracksPtr = Ptr<Tracks>;

        //------------------------------------------------------------------------------
        // Rotation Types
        //------------------------------------------------------------------------------

        /**
         * @brief 相对旋转关系的表示
         * @details 存储两个相机视图间的相对旋转变换信息
         */
        struct RelativeRotation
        {
            IndexT i;     ///< 源相机视图的索引
            IndexT j;     ///< 目标相机视图的索引
            Matrix3d Rij; ///< 从视图i到视图j的相对旋转矩阵
            float weight; ///< 相对旋转的权重因子

            /**
             * @brief 构造相对旋转实例
             * @param i_ 源视图索引,默认为0
             * @param j_ 目标视图索引,默认为0
             * @param Rij_ 相对旋转矩阵,默认为单位矩阵
             * @param weight_ 权重系数,默认为1.0
             */
            RelativeRotation(
                IndexT i_ = 0,
                IndexT j_ = 0,
                const Matrix3d &Rij_ = Matrix3d::Identity(),
                float weight_ = 1.0f) : i(i_), j(j_), Rij(Rij_), weight(weight_) {}
        };

        /** @brief 相对旋转的容器类型定义 */
        using RelativeRotations = std::vector<RelativeRotation>;
        using RelativeRotationsPtr = Ptr<RelativeRotations>;

        //------------------------------------------------------------------------------
        // Relative Pose Types
        // -----------------------------------------------------------------------------
        // PoSDK中相对位姿(R|t)的表示方式:  xj = R * xi + t
        //  注意：第三方库相对位姿或不同，例如OpenGV中相对位姿的表示为 xi = R * xj + t
        //------------------------------------------------------------------------------

        /**
         * @brief 相对位姿的表示
         * @details 存储两个相机视图间的相对位姿变换信息(R,t)
         */

        struct RelativePose
        {
            EIGEN_MAKE_ALIGNED_OPERATOR_NEW

            IndexT i;     ///< 源相机视图的索引
            IndexT j;     ///< 目标相机视图的索引
            Matrix3d Rij; ///< 从视图i到视图j的相对旋转矩阵
            Vector3d tij; ///< 从视图i到视图j的相对平移向量
            float weight; ///< 位姿估计的权重因子

            /**
             * @brief 构造相对位姿实例
             * @param i_ 源视图索引,默认为0
             * @param j_ 目标视图索引,默认为0
             * @param Rij_ 相对旋转矩阵,默认为单位矩阵
             * @param tij_ 相对平移向量,默认为零向量
             * @param weight_ 权重系数,默认为1.0
             */
            RelativePose(
                IndexT i_ = 0,
                IndexT j_ = 0,
                const Matrix3d &Rij_ = Matrix3d::Identity(),
                const Vector3d &tij_ = Vector3d::Zero(),
                float weight_ = 1.0f) : i(i_), j(j_), Rij(Rij_), tij(tij_), weight(weight_) {}

            /**
             * @brief 获取本质矩阵
             * @return 返回3x3的本质矩阵
             */
            Matrix3d GetEssentialMatrix() const;

            /**
             * @brief 计算与另一个相对位姿之间的误差
             * @param other 另一个相对位姿
             * @param rotation_error 输出旋转误差（角度）
             * @param translation_error 输出平移方向误差（角度）
             * @return 如果视图ID匹配则返回true，否则返回false
             */
            bool ComputeErrors(const RelativePose &other,
                               double &rotation_error,
                               double &translation_error,
                               bool is_opengv_format = false) const;
        };

        /** @brief 相对位姿的容器类型定义 */
        class RelativePoses : public std::vector<RelativePose, Eigen::aligned_allocator<RelativePose>>
        {
        public:
            // 继承vector的构造函数
            using std::vector<RelativePose, Eigen::aligned_allocator<RelativePose>>::vector;

            /**
             * @brief 评估相对位姿精度
             * @param gt_poses 真值相对位姿
             * @param rotation_errors 输出旋转误差(角度)
             * @param translation_errors 输出平移方向误差(角度)
             * @return 返回匹配位姿对的数量
             */
            std::size_t EvaluateAgainst(
                const RelativePoses &gt_poses,
                std::vector<double> &rotation_errors,
                std::vector<double> &translation_errors) const;

            /**
             * @brief 根据ViewPair获取相对位姿
             * @param view_pair 视图对(i,j)
             * @param R 输出旋转矩阵
             * @param t 输出平移向量
             * @return 如果找到匹配的位姿则返回true，否则返回false
             * @details 如果存储的是(i,j)，直接返回R和t；如果存储的是(j,i)，返回R^T和-R^T*t
             */
            bool GetRelativePose(const ViewPair &view_pair, Matrix3d &R, Vector3d &t) const;
        };

        using RelativePosesPtr = Ptr<RelativePoses>;

        //------------------------------------------------------------------------------
        // Global Rotation Types
        //------------------------------------------------------------------------------

#ifdef USE_LIGT_VERSION
        /** @brief LiGT版本的全局旋转表示 */
        using GlobalRotationsPtr = Ptr<Matrix3d>; ///< 单个全局旋转矩阵的指针
#else
        /** @brief PoMVG版本的全局旋转表示 */
        using GlobalRotations = std::vector<Matrix3d, Eigen::aligned_allocator<Matrix3d>>; ///< 全局旋转矩阵序列
        using GlobalRotationsPtr = Ptr<GlobalRotations>;
#endif

        //------------------------------------------------------------------------------
        // Translation Types
        //------------------------------------------------------------------------------

#ifdef USE_LIGT_VERSION
        /** @brief LiGT版本的全局平移表示 */
        using TranslationsPtr = Ptr<Vector3d>; ///< 单个全局平移向量的指针
#else
        /** @brief PoMVG版本的全局平移表示 */
        using GlobalTranslations = std::vector<Vector3d, Eigen::aligned_allocator<Vector3d>>; ///< 全局平移向量序列
        using GlobalTranslationsPtr = Ptr<GlobalTranslations>;
#endif

        //------------------------------------------------------------------------------
        // Forward declarations similarity transform functions
        //------------------------------------------------------------------------------

        /// @brief Forward declarations for similarity transform functions
        template <typename PointContainer>
        bool ComputeSimilarityTransform(
            const PointContainer &src_points,
            const PointContainer &dst_points,
            PointContainer &src_points_transformed,
            double &scale,
            Matrix3d &rotation,
            Vector3d &translation);

        bool ComputeSimilarityTransform(
            const std::vector<Vector3d> &src_centers,
            const std::vector<Vector3d> &dst_centers,
            std::vector<Vector3d> &src_centers_transformed,
            double &scale,
            Matrix3d &rotation,
            Vector3d &translation);

        //------------------------------------------------------------------------------
        // Pose Format Types
        //------------------------------------------------------------------------------

        /**
         * @brief 位姿坐标系格式枚举
         * @details 定义了不同的相机位姿表示方式
         */
        enum class PoseFormat
        {
            RwTw, ///< xc = Rw * (Xw - tw)
            ///< Rw: 世界到相机的旋转, tw: 相机在世界坐标系中的位置
            RwTc, ///< xc = Rw * Xw + tc
            ///< Rw: 世界到相机的旋转, tc: 相机坐标系中的平移向量

            RcTw, ///< xc =  Rc^T * (Xw - tw)
            ///< Rc: 相机到相机的旋转, tw: 相机在世界坐标系中的位置

            RcTc ///< xc = Rc^T * Xw + tc
            ///< Rc: 相机到相机的旋转, tc: 相机坐标系中的平移向量
        };

        //------------------------------------------------------------------------------
        // EstInfo Types
        //------------------------------------------------------------------------------

        /**
         * @brief 视图估计状态信息
         * @details 记录视图的估计状态及ID映射关系
         */
        struct EstInfo
        {
            /// @brief 视图状态枚举
            enum class ViewState : uint8_t
            {
                INVALID = 0,   ///< 无效或未初始化的视图
                VALID = 1,     ///< 有效但未参与估计的视图
                ESTIMATED = 2, ///< 已完成估计的视图
                OPTIMIZED = 3, ///< 已完成优化的视图
                FILTERED = 4,  ///< 被过滤的视图（如离群点）
            };

            /// @brief 估计ID到原始ID的映射（仅包含已估计的视图）
            std::vector<ViewId> est2origin_id;

            /// @brief 原始ID到估计ID的映射（所有视图）
            std::vector<ViewId> origin2est_id;

            /// @brief 视图状态表（所有视图）
            std::vector<ViewState> view_states;

            /// @brief 无效的视图ID标记
            static constexpr ViewId kInvalidViewId = std::numeric_limits<ViewId>::max();

            /**
             * @brief 初始化EstInfo (reset all)
             * @param num_views 视图总数
             */
            void Init(std::size_t num_views);

            /**
             * @brief 添加一个已估计的视图
             * @param original_id 原始视图ID
             */
            void AddEstimatedView(ViewId original_id);

            /**
             * @brief 设置视图状态
             * @param original_id 原始视图ID
             * @param state 新状态
             */
            void SetViewState(ViewId original_id, ViewState state);

            /**
             * @brief 获取视图状态
             * @param original_id 原始视图ID
             * @return 视图状态
             */
            ViewState GetViewState(ViewId original_id) const;

            /**
             * @brief 检查视图是否已估计
             * @param original_id 原始视图ID
             * @return 是否已估计
             */
            bool IsEstimated(ViewId original_id) const;

            /**
             * @brief 获取已估计视图数量
             * @return 已估计视图数量
             */
            std::size_t GetNumEstimatedViews() const;

            /**
             * @brief 获取特定状态的所有视图ID
             * @param state 目标状态
             * @return 视图ID列表
             */
            std::vector<ViewId> GetViewsInState(ViewState state) const;

            /**
             * @brief 从Tracks中构建EstInfo
             * @param tracks_ptr 跟踪点信息
             * @param fixed_id 固定视图ID
             */
            void BuildFromTracks(const TracksPtr &tracks_ptr, const ViewId fixed_id);

            /**
             * @brief 收集有效的视图ID
             * @param tracks_ptr 跟踪点信息
             * @return 有效的视图ID集合
             */
            std::set<ViewId> CollectValidViews(const TracksPtr &tracks_ptr) const;
        };

        /**
         * @brief 全局位姿信息
         * @details 存储所有视图的全局位姿信息
         */
        class GlobalPoses
        {
        public:
            GlobalRotations rotations;       // 所有视图的旋转矩阵（使用原始ID）[Rw]
            GlobalTranslations translations; // 所有视图的平移向量（使用原始ID）[tc/tw]
            EstInfo est_info;
            PoseFormat pose_format{PoseFormat::RwTw}; // 默认为RwTw格式

        public:
            /**
             * @brief 初始化位姿数据
             * @param num_views 视图数量
             */
            void Init(std::size_t num_views);

            /**
             * @brief 获取当前位姿格式
             * @return 位姿格式枚举值
             */
            PoseFormat GetPoseFormat() const { return pose_format; }

            /**
             * @brief 设置位姿格式
             * @param format 位姿格式枚举值
             */
            void SetPoseFormat(PoseFormat format) { pose_format = format; }

            /**
             * @brief 获取视图的旋转矩阵（使用原始ID）
             * @param original_id 原始视图ID
             * @return 旋转矩阵的常量引用
             */
            const Matrix3d &GetRotation(ViewId original_id) const;

            /**
             * @brief 获取视图的平移向量（使用原始ID）
             * @param original_id 原始视图ID
             * @return 平移向量的常量引用
             */
            const Vector3d &GetTranslation(ViewId original_id) const;

            /**
             * @brief 获取视图的旋转矩阵（使用估计ID）
             * @param est_id 估计视图ID
             * @return 旋转矩阵的常量引用
             */
            const Matrix3d &GetRotationByEstId(ViewId est_id) const;

            /**
             * @brief 获取视图的平移向量（使用估计ID）
             * @param est_id 估计视图ID
             * @return 平移向量的常量引用
             */
            const Vector3d &GetTranslationByEstId(ViewId est_id) const;

            /**
             * @brief 设置视图的旋转矩阵（使用原始ID）
             * @param original_id 原始视图ID
             * @param rotation 旋转矩阵
             */
            void SetRotation(ViewId original_id,
                             const Matrix3d &rotation);

            /**
             * @brief 设置视图的平移向量（使用原始ID）
             * @param original_id 原始视图ID
             * @param translation 平移向量
             */
            void SetTranslation(ViewId original_id,
                                const Vector3d &translation);

            /**
             * @brief 设置视图的旋转矩阵（使用估计ID）
             * @param est_id 估计视图ID
             * @param rotation 旋转矩阵
             */
            void SetRotationByEstId(ViewId est_id,
                                    const Matrix3d &rotation);

            /**
             * @brief 设置视图的平移向量（使用估计ID）
             * @param est_id 估计视图ID
             * @param translation 平移向量
             */
            void SetTranslationByEstId(ViewId est_id,
                                       const Vector3d &translation);

            /**
             * @brief 获取位姿数量
             * @return 位姿数量
             */
            std::size_t Size() const;

            /**
             * @brief 获取EstInfo的引用
             * @return EstInfo的引用
             */
            EstInfo &GetEstInfo();

            /**
             * @brief 获取EstInfo的常量引用
             * @return EstInfo的常量引用
             */
            const EstInfo &GetEstInfo() const;

            /**
             * @brief 从Tracks构建EstInfo
             * @param tracks_ptr 跟踪点信息
             * @param fixed_id 固定视图ID（参考视图）
             */
            void BuildEstInfoFromTracks(const TracksPtr &tracks_ptr,
                                        const ViewId fixed_id = 0);

            /**
             * @brief 将位姿从RwTw格式转换为RwTc格式
             * @details 对应MATLAB中的转换: Rw_tw -> Rw_tc
             * @param ref_id 参考视图ID（默认为0）
             * @param fixed_id 用于尺度归一化的固定视图ID（默认为与参考视图最远的视图）
             * @return 是否转换成功
             */
            bool RwTw_to_RwTc(ViewId ref_id = 0,
                              ViewId fixed_id = std::numeric_limits<ViewId>::max());

            /**
             * @brief 将位姿从RwTc格式转换为RwTw格式
             * @details 对应MATLAB中的转换: Rw_tc -> Rw_tw
             * @param ref_id 参考视图ID（默认为0）
             * @param fixed_id 用于尺度归一化的固定视图ID（如非零则进行尺度恢复）
             * @return 是否转换成功
             */
            bool RwTc_to_RwTw(ViewId ref_id = 0,
                              ViewId fixed_id = std::numeric_limits<ViewId>::max());

            /**
             * @brief 在不同位姿格式之间进行转换
             * @param target_format 目标位姿格式
             * @param ref_id 参考视图ID（默认为0）
             * @param fixed_id 用于尺度归一化的固定视图ID（默认为与参考视图最远的视图）
             * @return 是否转换成功
             */
            bool ConvertPoseFormat(PoseFormat target_format,
                                   ViewId ref_id = 0,
                                   ViewId fixed_id = std::numeric_limits<ViewId>::max());

            /**
             * @brief 评估全局位姿精度
             * @param gt_poses 真值全局位姿
             * @param position_errors 输出位置误差
             * @param rotation_errors 输出旋转误差(角度)
             * @param compute_similarity 是否计算相似变换进行对齐
             * @return 返回是否成功评估
             */
            bool EvaluateAgainst(
                const GlobalPoses &gt_poses,
                std::vector<double> &position_errors,
                std::vector<double> &rotation_errors,
                bool compute_similarity = true) const
            {
                std::cout << "GlobalPoses::EvaluateAgainst" << std::endl;
                if (gt_poses.rotations.empty() || rotations.empty())
                {
                    return false;
                }

                // 如果格式不匹配，创建一个副本并尝试转换
                if (gt_poses.GetPoseFormat() != GetPoseFormat())
                {
                    GlobalPoses gt_poses_copy = gt_poses;
                    if (!gt_poses_copy.ConvertPoseFormat(GetPoseFormat()))
                    {
                        std::cerr << "无法将真值位姿从"
                                  << (gt_poses.GetPoseFormat() == PoseFormat::RwTw ? "RwTw" : "RwTc")
                                  << "格式转换为"
                                  << (GetPoseFormat() == PoseFormat::RwTw ? "RwTw" : "RwTc")
                                  << "格式" << std::endl;
                        return false;
                    }

                    // 使用转换后的副本进行评估
                    return EvaluateAgainst(gt_poses_copy, position_errors, rotation_errors, compute_similarity);
                }

                // 收集匹配的位姿对
                std::vector<Vector3d> gt_centers;
                std::vector<Vector3d> est_centers;
                std::vector<Matrix3d> gt_rotations;
                std::vector<Matrix3d> est_rotations;

                for (std::size_t i = 0; i < std::min(gt_poses.Size(), Size()); ++i)
                {
                    gt_centers.push_back(gt_poses.translations[i]);
                    est_centers.push_back(translations[i]);
                    gt_rotations.push_back(gt_poses.rotations[i]);
                    est_rotations.push_back(rotations[i]);
                }

                // 如果需要计算相似变换
                std::vector<Vector3d> est_centers_aligned = est_centers;
                std::vector<Matrix3d> est_rotations_aligned = est_rotations;

                if (compute_similarity)
                {
                    // 计算从估计位姿到真值位姿的相似变换 (SIM3)
                    double scale;
                    Matrix3d R_align;
                    Vector3d t_align;

                    // 先确保两者点数量相等
                    if (gt_centers.size() != est_centers.size())
                    {
                        std::cerr << "视图数量不匹配，无法计算对齐变换" << std::endl;
                        return false;
                    }

                    // 计算质心
                    Vector3d gt_centroid = Vector3d::Zero();
                    Vector3d est_centroid = Vector3d::Zero();
                    for (std::size_t i = 0; i < gt_centers.size(); ++i)
                    {
                        gt_centroid += gt_centers[i];
                        est_centroid += est_centers[i];
                    }
                    gt_centroid /= gt_centers.size();
                    est_centroid /= est_centers.size();

                    // 去中心化
                    std::vector<Vector3d> gt_centered, est_centered;
                    for (std::size_t i = 0; i < gt_centers.size(); ++i)
                    {
                        gt_centered.push_back(gt_centers[i] - gt_centroid);
                        est_centered.push_back(est_centers[i] - est_centroid);
                    }

                    // 计算尺度
                    double gt_norm = 0, est_norm = 0;
                    for (std::size_t i = 0; i < gt_centered.size(); ++i)
                    {
                        gt_norm += gt_centered[i].squaredNorm();
                        est_norm += est_centered[i].squaredNorm();
                    }
                    scale = std::sqrt(gt_norm / est_norm);

                    // 计算旋转
                    Matrix3d H = Matrix3d::Zero();
                    for (std::size_t i = 0; i < gt_centered.size(); ++i)
                    {
                        H += scale * est_centered[i] * gt_centered[i].transpose();
                    }
                    Eigen::JacobiSVD<Matrix3d> svd(H, Eigen::ComputeFullU | Eigen::ComputeFullV);
                    R_align = svd.matrixV() * svd.matrixU().transpose();

                    // 确保是正交矩阵
                    if (R_align.determinant() < 0)
                    {
                        Matrix3d V = svd.matrixV();
                        V.col(2) = -V.col(2);
                        R_align = V * svd.matrixU().transpose();
                    }

                    // 计算平移
                    t_align = gt_centroid - scale * R_align * est_centroid;

                    // 应用变换
                    for (std::size_t i = 0; i < est_centers.size(); ++i)
                    {
                        est_centers_aligned[i] = scale * R_align * est_centers[i] + t_align;
                        est_rotations_aligned[i] = est_rotations[i] * R_align.transpose(); // 修正旋转对齐方式
                    }
                }

                // 计算误差
                position_errors.clear();
                rotation_errors.clear();

                for (std::size_t i = 0; i < gt_centers.size(); ++i)
                {
                    // 位置误差
                    position_errors.push_back((gt_centers[i] - est_centers_aligned[i]).norm());

                    // 旋转误差
                    // 注：此计算方法与MATLAB中使用罗德里格斯公式 norm(rodrigues(R_gt'*R_est)) 等价
                    // 都是计算两个旋转矩阵之间的最小旋转角度
                    Matrix3d R_error = gt_rotations[i] * est_rotations_aligned[i].transpose();
                    double angle_error = std::acos(std::clamp((R_error.trace() - 1.0) / 2.0, -1.0, 1.0));
                    angle_error = angle_error * 180.0 / M_PI; // 转换为度
                    rotation_errors.push_back(angle_error);
                }

                return true;
            }

            /**
             * @brief 使用Ceres优化的相似变换评估全局位姿精度（更精确）
             * @details 通过Ceres优化找到最佳相似变换参数(S,R,t)，将估计位姿对齐到真值
             * @param gt_poses 真值全局位姿
             * @param position_errors 输出位置误差
             * @param rotation_errors 输出旋转误差(角度)
             * @return 返回是否成功评估
             */
            bool EvaluateWithSimilarityTransform(
                const GlobalPoses &gt_poses,
                std::vector<double> &position_errors,
                std::vector<double> &rotation_errors) const
            {
                if (gt_poses.rotations.empty() || rotations.empty())
                {
                    std::cerr << "EvaluateWithSimilarityTransform: 位姿为空" << std::endl;
                    return false;
                }

                // 创建位姿副本，统一转换为RwTw格式进行评估
                GlobalPoses est_poses_copy = *this;
                GlobalPoses gt_poses_copy = gt_poses;

                // 转换为RwTw格式（如果不是）
                if (est_poses_copy.GetPoseFormat() != PoseFormat::RwTw)
                {
                    if (!est_poses_copy.ConvertPoseFormat(PoseFormat::RwTw))
                    {
                        std::cerr << "无法将估计位姿转换为RwTw格式用于评估" << std::endl;
                        return false;
                    }
                }

                if (gt_poses_copy.GetPoseFormat() != PoseFormat::RwTw)
                {
                    if (!gt_poses_copy.ConvertPoseFormat(PoseFormat::RwTw))
                    {
                        std::cerr << "无法将真值位姿转换为RwTw格式用于评估" << std::endl;
                        return false;
                    }
                }

                // 收集位姿中心和旋转矩阵
                std::vector<Vector3d> gt_centers;
                std::vector<Vector3d> est_centers;
                std::vector<Matrix3d> gt_rotations;
                std::vector<Matrix3d> est_rotations;

                size_t num_poses = std::min(gt_poses_copy.Size(), est_poses_copy.Size());
                gt_centers.reserve(num_poses);
                est_centers.reserve(num_poses);
                gt_rotations.reserve(num_poses);
                est_rotations.reserve(num_poses);

                for (size_t i = 0; i < num_poses; ++i)
                {
                    gt_centers.push_back(gt_poses_copy.translations[i]);
                    est_centers.push_back(est_poses_copy.translations[i]);
                    gt_rotations.push_back(gt_poses_copy.rotations[i]);
                    est_rotations.push_back(est_poses_copy.rotations[i]);
                }

                // 计算相似变换参数
                std::vector<Vector3d> est_centers_transformed;
                double scale;
                Matrix3d R_align;
                Vector3d t_align;

                bool success = ComputeSimilarityTransform(
                    est_centers, gt_centers, est_centers_transformed,
                    scale, R_align, t_align);

                if (!success)
                {
                    std::cerr << "相似变换计算失败" << std::endl;
                    return false;
                }

                // 变换估计的旋转矩阵
                std::vector<Matrix3d> est_rotations_aligned(num_poses);
                for (size_t i = 0; i < num_poses; ++i)
                {
                    est_rotations_aligned[i] = est_rotations[i] * R_align.transpose();
                }

                // 计算位置和旋转误差
                position_errors.clear();
                rotation_errors.clear();
                position_errors.reserve(num_poses);
                rotation_errors.reserve(num_poses);

                for (size_t i = 0; i < num_poses; ++i)
                {
                    // 位置误差
                    position_errors.push_back((gt_centers[i] - est_centers_transformed[i]).norm());

                    // 旋转误差：通过旋转矩阵的差异计算角度
                    Matrix3d R_error = gt_rotations[i] * est_rotations_aligned[i].transpose();
                    double angle_error = std::acos(std::clamp((R_error.trace() - 1.0) / 2.0, -1.0, 1.0));
                    rotation_errors.push_back(angle_error * 180.0 / M_PI); // 转换为度
                }

                return true;
            }

            /**
             * @brief 仅评估全局旋转部分的精度
             * @details 不计算完整相似变换，只将第一个视图对齐到单位矩阵
             * @param gt_poses 真值全局位姿
             * @param rotation_errors 输出旋转误差(角度)
             * @return 返回是否成功评估
             */
            bool EvaluateRotations(
                const GlobalPoses &gt_poses,
                std::vector<double> &rotation_errors) const
            {
                if (gt_poses.rotations.empty() || rotations.empty())
                {
                    std::cerr << "EvaluateRotations: 位姿为空" << std::endl;
                    return false;
                }

                // 收集旋转矩阵
                std::vector<Matrix3d> gt_rotations;
                std::vector<Matrix3d> est_rotations;

                size_t num_poses = std::min(gt_poses.Size(), Size());
                gt_rotations.reserve(num_poses);
                est_rotations.reserve(num_poses);

                for (size_t i = 0; i < num_poses; ++i)
                {
                    gt_rotations.push_back(gt_poses.rotations[i]);
                    est_rotations.push_back(rotations[i]);
                }

                // 将第一个视图对齐到单位矩阵
                Matrix3d Rgt_1_inv = gt_rotations[0].transpose();
                Matrix3d Rest_1_inv = est_rotations[0].transpose();

                std::vector<Matrix3d> gt_rotations_aligned(num_poses);
                std::vector<Matrix3d> est_rotations_aligned(num_poses);

                for (size_t i = 0; i < num_poses; ++i)
                {
                    gt_rotations_aligned[i] = gt_rotations[i] * Rgt_1_inv;
                    est_rotations_aligned[i] = est_rotations[i] * Rest_1_inv;
                }

                // 计算旋转误差
                rotation_errors.clear();
                rotation_errors.reserve(num_poses);

                for (size_t i = 0; i < num_poses; ++i)
                {
                    // 旋转误差：通过旋转矩阵的差异计算角度
                    Matrix3d R_error = gt_rotations_aligned[i] * est_rotations_aligned[i].transpose();
                    double angle_error = std::acos(std::clamp((R_error.trace() - 1.0) / 2.0, -1.0, 1.0));
                    rotation_errors.push_back(angle_error * 180.0 / M_PI); // 转换为度
                }

                return true;
            }
        };

        using GlobalPosesPtr = Ptr<GlobalPoses>;
        //------------------------------------------------------------------------------
        // 3D Points Types
        //------------------------------------------------------------------------------

        /// @brief Forward declaration for 3D points matrix type
        using Points3d = Eigen::Matrix<double, 3, Eigen::Dynamic>;
        using Points3dPtr = Ptr<Points3d>;
        using Point3d = Vector3d; ///< 单个3D点类型，与Vector3d相同

        /**
         * @brief 世界坐标点信息（高性能版本）
         * @details 用于存储重建的3D点云和使用状态，支持批量计算
         */
        struct WorldPointInfo
        {
            EIGEN_MAKE_ALIGNED_OPERATOR_NEW
            Points3d world_points;      ///< 世界坐标点集合 (3xN矩阵)
            std::vector<bool> ids_used; ///< 是否被使用的标志位数组

            WorldPointInfo() = default;

            /**
             * @brief 构造函数
             * @param num_points 点的数量
             */
            explicit WorldPointInfo(size_t num_points)
                : world_points(3, num_points), ids_used(num_points, true)
            {
                world_points.setZero();
            }

            /**
             * @brief 调整大小
             * @param num_points 新的点数量
             */
            void resize(size_t num_points)
            {
                world_points.resize(3, num_points);
                ids_used.resize(num_points, true);
                world_points.setZero();
            }

            /**
             * @brief 获取点数量
             * @return 点数量
             */
            size_t size() const
            {
                return world_points.cols();
            }

            /**
             * @brief 设置点坐标
             * @param index 点索引
             * @param point 3D点坐标
             */
            void setPoint(size_t index, const Point3d &point)
            {
                if (index < size())
                {
                    world_points.col(index) = point;
                }
            }

            /**
             * @brief 获取点坐标
             * @param index 点索引
             * @return 3D点坐标
             */
            Point3d getPoint(size_t index) const
            {
                if (index < size())
                {
                    return world_points.col(index);
                }
                return Point3d::Zero();
            }

            /**
             * @brief 设置点使用状态
             * @param index 点索引
             * @param used 是否使用
             */
            void setUsed(size_t index, bool used)
            {
                if (index < ids_used.size())
                {
                    ids_used[index] = used;
                }
            }

            /**
             * @brief 获取点使用状态
             * @param index 点索引
             * @return 是否使用
             */
            bool isUsed(size_t index) const
            {
                return index < ids_used.size() ? ids_used[index] : false;
            }

            /**
             * @brief 获取有效点数量
             * @return 有效点数量
             */
            size_t getValidPointsCount() const
            {
                return std::count(ids_used.begin(), ids_used.end(), true);
            }

            /**
             * @brief 评估3D点重建精度
             * @details 通过Ceres优化找到最佳相似变换参数(S,R,t)，将估计点对齐到真值
             * @param gt_points 真值WorldPointInfo
             * @param position_errors 输出位置误差
             * @return 返回是否成功评估
             */
            bool EvaluateWith3DPoints(
                const WorldPointInfo &gt_points,
                std::vector<double> &position_errors) const
            {
                if (gt_points.size() == 0 || size() == 0)
                {
                    std::cerr << "EvaluateWith3DPoints: 点云为空" << std::endl;
                    return false;
                }

                // 收集有效的3D点为Points3d格式
                Points3d gt_points_matrix(3, 0);
                Points3d est_points_matrix(3, 0);

                size_t num_points = std::min(gt_points.size(), size());

                // 先统计有效点数量
                size_t valid_count = 0;
                for (size_t i = 0; i < num_points; ++i)
                {
                    if (gt_points.isUsed(i) && isUsed(i))
                    {
                        valid_count++;
                    }
                }

                if (valid_count < 3)
                {
                    std::cerr << "EvaluateWith3DPoints: 有效点数量少于3个，无法计算相似变换" << std::endl;
                    return false;
                }

                // 重新设置矩阵大小并填充数据
                gt_points_matrix.resize(3, valid_count);
                est_points_matrix.resize(3, valid_count);

                size_t idx = 0;
                for (size_t i = 0; i < num_points; ++i)
                {
                    if (gt_points.isUsed(i) && isUsed(i))
                    {
                        gt_points_matrix.col(idx) = gt_points.getPoint(i);
                        est_points_matrix.col(idx) = getPoint(i);
                        idx++;
                    }
                }

                // 使用模版函数计算相似变换参数
                Points3d est_points_transformed;
                double scale;
                Matrix3d R_align;
                Vector3d t_align;

                bool success = ComputeSimilarityTransform<Points3d>(
                    est_points_matrix, gt_points_matrix, est_points_transformed,
                    scale, R_align, t_align);

                if (!success)
                {
                    std::cerr << "3D点相似变换计算失败" << std::endl;
                    return false;
                }

                // 计算位置误差
                position_errors.clear();
                position_errors.reserve(valid_count);

                for (size_t i = 0; i < valid_count; ++i)
                {
                    // 位置误差
                    position_errors.push_back((gt_points_matrix.col(i) - est_points_transformed.col(i)).norm());
                }

                return true;
            }

        private:
            // 注意：ComputeSimilarityTransformFor3DPoints方法已删除，
            // 现在统一使用通用的ComputeSimilarityTransform函数
        };

        using WorldPointInfoPtr = Ptr<WorldPointInfo>;

        //------------------------------------------------------------------------------
        // StructureInfo Types
        //------------------------------------------------------------------------------

        struct StructureInfo
        {
            GlobalRotations rotations;
            GlobalTranslations translations;
            Points3d points;
        };
        using StructureInfoPtr = Ptr<StructureInfo>;

        //------------------------------------------------------------------------------
        // 全局位姿与相对位姿转换函数
        /**
         * PoSDK中相对位姿(R|t)的表示方式:  xj = R * xi + t
         * 注意：第三方库相对位姿或不同，例如OpenGV中相对位姿的表示为 xi = R * xj + t
         *
         * 对于RwTw格式 x = R(X-t)：
         * R_ij = R_j * R_i.transpose()
         * t_ij = R_j * (t_i - t_j)
         *
         * 对于RwTc格式 x = RX+t：
         * R_ij = R_j * R_i.transpose()
         * t_ij = t_j - R_j * R_i.transpose() * t_i
         */
        //------------------------------------------------------------------------------

        /**
         * @brief 从全局位姿生成相对位姿
         * @details 根据全局位姿计算两两视图间的相对位姿关系
         * @param global_poses 全局位姿
         * @param relative_poses_output 输出的相对位姿
         * @return 是否成功生成相对位姿
         */
        bool Global2RelativePoses(
            const GlobalPoses &global_poses,
            RelativePoses &relative_poses_output);

        //------------------------------------------------------------------------------
        // 基础类型转换和MethodOptions配置函数
        //------------------------------------------------------------------------------

        /**
         * 注意：以下函数已迁移到 MethodPreset 类中，它们将在未来版本中从此处移除。
         * 请改用 MethodPreset 类中的相应方法。
         */

        /*
         * @brief 安全地将字符串转换为整数
         */
        // inline IndexT String2IndexT(const std::string &value, IndexT default_value = 0)
        // {
        //     try
        //     {
        //         if (!value.empty())
        //         {
        //             return static_cast<IndexT>(std::stoul(value));
        //         }
        //     }
        //     catch (const std::exception &e)
        //     {
        //         std::cerr << "[Converter] Warning: Failed to convert '"
        //                   << value << "' to IndexT, using default value" << std::endl;
        //     }
        //     return default_value;
        // }

        /**
         * @brief 安全地将字符串转换为浮点数
         */
        // inline float String2Float(const std::string &value, float default_value = 0.0f)
        // {
        //     try
        //     {
        //         if (!value.empty())
        //         {
        //             return std::stof(value);
        //         }
        //     }
        //     catch (const std::exception &e)
        //     {
        //         std::cerr << "[Converter] Warning: Failed to convert '"
        //                   << value << "' to float, using default value" << std::endl;
        //     }
        //     return default_value;
        // }

        /**
         * @brief 将字符串转换为布尔值
         * @param str 输入字符串
         * @param default_value 默认值
         * @return 布尔值
         */
        // inline bool String2Bool(const std::string &str, bool default_value = false)
        // {
        //     // 转换为大写以进行不区分大小写的比较
        //     std::string upper_str = str;
        //     std::transform(upper_str.begin(), upper_str.end(), upper_str.begin(), ::toupper);
        //
        //     // 检查常见的 true 值
        //     if (upper_str == "TRUE" || upper_str == "ON" || upper_str == "YES" ||
        //         upper_str == "1" || upper_str == "T" || upper_str == "Y")
        //     {
        //         return true;
        //     }
        //
        //     // 检查常见的 false 值
        //     if (upper_str == "FALSE" || upper_str == "OFF" || upper_str == "NO" ||
        //         upper_str == "0" || upper_str == "F" || upper_str == "N")
        //     {
        //         return false;
        //     }
        //
        //     // 如果不是已知的值，输出警告并返回默认值
        //     std::cerr << "[Converter] Warning: Failed to convert '"
        //               << str << "' to bool, using default value" << std::endl;
        //     return default_value;
        // }
        /**
         * @brief 从方法配置中获取整数值
         *
         * @已废弃 请改用 MethodPreset::GetOptionAsIndexT()
         */
        // inline IndexT GetOptionAsIndexT(const MethodOptions &options,
        //                                 const MethodParams &key,
        //                                 IndexT default_value = 0)
        // {
        //     auto it = options.find(key);
        //     if (it != options.end())
        //     {
        //         return String2IndexT(it->second, default_value);
        //     }
        //     return default_value;
        // }

        /**
         * @brief 从方法配置中获取浮点值
         *
         * @已废弃 请改用 MethodPreset::GetOptionAsFloat()
         */
        // inline float GetOptionAsFloat(const MethodOptions &options,
        //                               const MethodParams &key,
        //                               float default_value = 0.0f)
        // {
        //     auto it = options.find(key);
        //     if (it != options.end())
        //     {
        //         return String2Float(it->second, default_value);
        //     }
        //     return default_value;
        // }

        /**
         * @brief 从方法配置中获取布尔值
         *
         * @已废弃 请改用 MethodPreset::GetOptionAsBool()
         */
        // inline bool GetOptionAsBool(const MethodOptions &options,
        //                             const MethodParams &key,
        //                             bool default_value = false)
        // {
        //     auto it = options.find(key);
        //     if (it != options.end())
        //     {
        //         return String2Bool(it->second, default_value);
        //     }
        //     return default_value;
        // }

        /**
         * @brief 从方法配置中获取字符串值
         *
         * @已废弃 请改用 MethodPreset::GetOptionAsString()
         */
        // inline std::string GetOptionAsString(const MethodOptions &options,
        //                                      const MethodParams &key,
        //                                      const std::string &default_value = "")
        // {
        //     auto it = options.find(key);
        //     if (it != options.end())
        //     {
        //         return it->second;
        //     }
        //     return default_value;
        // }

        /**
         * @brief 计算向量的中位数
         */
        template <typename T>
        inline double find_median(const std::vector<T> &a)
        {
            if (a.empty())
            {
                return 0.0;
            }

            std::vector<T> temp = a;
            std::size_t n = temp.size();

            // 如果数组大小是偶数
            if (n % 2 == 0)
            {
                std::nth_element(temp.begin(), temp.begin() + n / 2, temp.end());
                std::nth_element(temp.begin(), temp.begin() + (n - 1) / 2, temp.end());
                return (temp[(n - 1) / 2] + temp[n / 2]) / 2.0;
            }
            // 如果数组大小是奇数
            else
            {
                std::nth_element(temp.begin(), temp.begin() + n / 2, temp.end());
                return temp[n / 2];
            }
        }

        // 计算向量的中位数 (Eigen::VectorXd特化版本)
        inline double find_median(const Eigen::VectorXd &a)
        {
            std::vector<double> temp(a.data(), a.data() + a.size());
            return find_median(temp);
        }

        //------------------------------------------------------------------------------
        // 通用相似变换函数 - 位于Points3d定义之后
        //------------------------------------------------------------------------------
        // 计算两组位姿间的最优相似变换
        // **************  sRt变换方式公式说明： **************
        // 投影方程： x ~ R_src * (X_src - t_src)
        //          = R_src * s * R^T * R (X_src + t - t_src - t)
        //          = R_src * R^T * [s * R * X_src + t - (s * R * t_src + t)]
        // 变换过程：
        // R_src >> R_dst = R_src * R^T
        // t_src >> t_dst = s * R * t_src + t （ComputeSimilarityTransform输入）
        // X_src >> X_dst = s * R * X_src + t （备选输入，未使用）
        // ******************************************************
        /**
         * @brief 相似变换优化的Ceres Cost Function
         * @details 用于计算两组3D点之间最优相似变换的代价函数
         */
        struct SimilarityTransformError
        {
            SimilarityTransformError(const Vector3d &src_point, const Vector3d &dst_point)
                : src_point_(src_point), dst_point_(dst_point) {}

            template <typename T>
            bool operator()(const T *const scale,
                            const T *const rotation,
                            const T *const translation,
                            T *residuals) const
            {
                // 将源点转换为T类型
                T src_point[3];
                src_point[0] = T(src_point_(0));
                src_point[1] = T(src_point_(1));
                src_point[2] = T(src_point_(2));

                // 应用缩放
                T scaled_point[3];
                scaled_point[0] = (*scale) * src_point[0];
                scaled_point[1] = (*scale) * src_point[1];
                scaled_point[2] = (*scale) * src_point[2];

                // 应用旋转
                T rotated_point[3];
                ceres::AngleAxisRotatePoint(rotation, scaled_point, rotated_point);

                // 应用平移
                T transformed_point[3];
                transformed_point[0] = rotated_point[0] + translation[0];
                transformed_point[1] = rotated_point[1] + translation[1];
                transformed_point[2] = rotated_point[2] + translation[2];

                // 计算残差 (L2 distance)
                residuals[0] = transformed_point[0] - T(dst_point_(0));
                residuals[1] = transformed_point[1] - T(dst_point_(1));
                residuals[2] = transformed_point[2] - T(dst_point_(2));

                return true;
            }

            // 工厂函数
            static ceres::CostFunction *Create(const Vector3d &src_point, const Vector3d &dst_point)
            {
                return (new ceres::AutoDiffCostFunction<SimilarityTransformError, 3, 1, 3, 3>(
                    new SimilarityTransformError(src_point, dst_point)));
            }

        private:
            // 源点和目标点
            const Vector3d src_point_;
            const Vector3d dst_point_;
        };

        // 计算两组位姿间的最优相似变换
        // **************  sRt变换方式公式说明： **************
        // 投影方程： x ~ R_src * (X_src - t_src)
        //          = R_src * s * R^T * R (X_src + t - t_src - t)
        //          = R_src * R^T * [s * R * X_src + t - (s * R * t_src + t)]
        // 变换过程：
        // R_src >> R_dst = R_src * R^T
        // t_src >> t_dst = s * R * t_src + t （ComputeSimilarityTransform输入）
        // X_src >> X_dst = s * R * X_src + t （备选输入，未使用）
        // ******************************************************

        /**
         * @brief 模版函数：计算两组3D点间的最优相似变换（使用Ceres优化）
         * @tparam PointContainer 点容器类型，支持std::vector<Vector3d>或Points3d
         * @param[in] src_points 源点集合
         * @param[in] dst_points 目标点集合
         * @param[out] src_points_transformed 变换后的源点集合
         * @param[out] scale 输出尺度因子
         * @param[out] rotation 输出旋转矩阵
         * @param[out] translation 输出平移向量
         * @return 是否成功计算相似变换
         */
        template <typename PointContainer>
        inline bool ComputeSimilarityTransform(
            const PointContainer &src_points,
            const PointContainer &dst_points,
            PointContainer &src_points_transformed,
            double &scale,
            Matrix3d &rotation,
            Vector3d &translation)
        {
            // 提取点数据到统一的vector<Vector3d>格式
            std::vector<Vector3d> src_centers, dst_centers;

            if constexpr (std::is_same_v<PointContainer, std::vector<Vector3d>>)
            {
                src_centers = src_points;
                dst_centers = dst_points;
            }
            else if constexpr (std::is_same_v<PointContainer, Points3d>)
            {
                // Points3d是Eigen::Matrix<double, 3, Dynamic>类型
                src_centers.reserve(src_points.cols());
                dst_centers.reserve(dst_points.cols());

                for (int i = 0; i < src_points.cols(); ++i)
                {
                    src_centers.push_back(src_points.col(i));
                }
                for (int i = 0; i < dst_points.cols(); ++i)
                {
                    dst_centers.push_back(dst_points.col(i));
                }
            }
            else
            {
                static_assert(std::is_same_v<PointContainer, std::vector<Vector3d>> ||
                                  std::is_same_v<PointContainer, Points3d>,
                              "PointContainer must be std::vector<Vector3d> or Points3d");
                return false;
            }

            if (src_centers.size() != dst_centers.size() || src_centers.size() < 3)
            {
                std::cerr << "ComputeSimilarityTransform: 源点和目标点数量不匹配或少于3个点" << std::endl;
                return false;
            }

            // 1. 使用Eigen::umeyama计算初始相似变换
            Eigen::Matrix<double, 3, Eigen::Dynamic> src_mat(3, src_centers.size());
            Eigen::Matrix<double, 3, Eigen::Dynamic> dst_mat(3, dst_centers.size());

            for (size_t i = 0; i < src_centers.size(); ++i)
            {
                src_mat.col(i) = src_centers[i];
                dst_mat.col(i) = dst_centers[i];
            }

            // 使用umeyama算法计算变换
            Eigen::Matrix4d transform = Eigen::umeyama(src_mat, dst_mat, true);

            // 提取变换参数
            rotation = transform.topLeftCorner<3, 3>();
            scale = std::pow(rotation.determinant(), 1.0 / 3.0);
            rotation /= scale;
            translation = transform.topRightCorner<3, 1>();

            // 检查退化情况
            if (scale < std::numeric_limits<double>::epsilon())
            {
                std::cerr << "ComputeSimilarityTransform: 变换尺度接近零，可能是退化配置" << std::endl;
                return false;
            }

            // 2. 使用Ceres优化相似变换参数
            double angle_axis[3];
            ceres::RotationMatrixToAngleAxis(rotation.data(), angle_axis);

            // 设置优化变量
            double scale_opt = scale;
            double translation_opt[3] = {translation(0), translation(1), translation(2)};

            // 构建Ceres问题
            ceres::Problem problem;
            for (size_t i = 0; i < src_centers.size(); ++i)
            {
                ceres::CostFunction *cost_function =
                    SimilarityTransformError::Create(src_centers[i], dst_centers[i]);
                problem.AddResidualBlock(cost_function,
                                         nullptr, // 不使用稳健核函数
                                         &scale_opt,
                                         angle_axis,
                                         translation_opt);
            }

            // 确保尺度为正
            problem.SetParameterLowerBound(&scale_opt, 0, 1e-10);

            // 设置Ceres求解器选项
            ceres::Solver::Options options;
            options.linear_solver_type = ceres::DENSE_QR;
            options.minimizer_progress_to_stdout = false;
            options.max_num_iterations = 100;
            options.function_tolerance = 1e-8;

            // 求解优化问题
            ceres::Solver::Summary summary;
            ceres::Solve(options, &problem, &summary);

            // 更新优化后的参数
            scale = scale_opt;
            ceres::AngleAxisToRotationMatrix(angle_axis, rotation.data());
            translation = Vector3d(translation_opt[0], translation_opt[1], translation_opt[2]);

            // 变换源点并更新输出容器
            if constexpr (std::is_same_v<PointContainer, std::vector<Vector3d>>)
            {
                src_points_transformed.resize(src_centers.size());
                for (size_t i = 0; i < src_centers.size(); ++i)
                {
                    src_points_transformed[i] = scale * rotation * src_centers[i] + translation;
                }
            }
            else if constexpr (std::is_same_v<PointContainer, Points3d>)
            {
                src_points_transformed.resize(3, src_centers.size());
                for (size_t i = 0; i < src_centers.size(); ++i)
                {
                    src_points_transformed.col(i) = scale * rotation * src_centers[i] + translation;
                }
            }

            return true;
        }

        /**
         * @brief 计算两组位姿间的最优相似变换（为保持向后兼容性）
         * @param[in] src_centers 源位置向量
         * @param[in] dst_centers 目标位置向量
         * @param[out] src_centers_transformed 变换后的源位置向量
         * @param[out] scale 输出尺度因子
         * @param[out] rotation 输出旋转矩阵
         * @param[out] translation 输出平移向量
         * @return 是否成功计算相似变换
         */
        inline bool ComputeSimilarityTransform(
            const std::vector<Vector3d> &src_centers,
            const std::vector<Vector3d> &dst_centers,
            std::vector<Vector3d> &src_centers_transformed,
            double &scale,
            Matrix3d &rotation,
            Vector3d &translation)
        {
            return ComputeSimilarityTransform<std::vector<Vector3d>>(
                src_centers, dst_centers, src_centers_transformed,
                scale, rotation, translation);
        }

        //------------------------------------------------------------------------------
        // Mathematics Utilities
        //------------------------------------------------------------------------------

        /**
         * @brief Cayley参数化类型定义
         */
        using CayleyParams = Vector3d;      ///< Cayley参数（3维向量）
        using Quaternion = Eigen::Vector4d; ///< 四元数（4维向量）

        /**
         * @brief 将Cayley参数转换为旋转矩阵
         * @param cayley Cayley参数（3维向量）
         * @return 对应的旋转矩阵
         */
        inline Matrix3d CayleyToRotation(const CayleyParams &cayley)
        {
            Matrix3d R;
            double scale = 1.0 + cayley[0] * cayley[0] + cayley[1] * cayley[1] + cayley[2] * cayley[2];

            R(0, 0) = 1.0 + cayley[0] * cayley[0] - cayley[1] * cayley[1] - cayley[2] * cayley[2];
            R(0, 1) = 2.0 * (cayley[0] * cayley[1] - cayley[2]);
            R(0, 2) = 2.0 * (cayley[0] * cayley[2] + cayley[1]);
            R(1, 0) = 2.0 * (cayley[0] * cayley[1] + cayley[2]);
            R(1, 1) = 1.0 - cayley[0] * cayley[0] + cayley[1] * cayley[1] - cayley[2] * cayley[2];
            R(1, 2) = 2.0 * (cayley[1] * cayley[2] - cayley[0]);
            R(2, 0) = 2.0 * (cayley[0] * cayley[2] - cayley[1]);
            R(2, 1) = 2.0 * (cayley[1] * cayley[2] + cayley[0]);
            R(2, 2) = 1.0 - cayley[0] * cayley[0] - cayley[1] * cayley[1] + cayley[2] * cayley[2];

            R = R / scale;
            return R;
        }

        /**
         * @brief 将旋转矩阵转换为Cayley参数
         * @param R 旋转矩阵
         * @return 对应的Cayley参数
         */
        inline CayleyParams RotationToCayley(const Matrix3d &R)
        {
            Matrix3d C1 = R - Matrix3d::Identity();
            Matrix3d C2 = R + Matrix3d::Identity();
            Matrix3d C = C1 * C2.inverse();

            CayleyParams cayley;
            cayley[0] = -C(1, 2);
            cayley[1] = C(0, 2);
            cayley[2] = -C(0, 1);

            return cayley;
        }

        /**
         * @brief 将相对位姿转换为参数向量（6自由度：3旋转+3平移）
         * @param pose 相对位姿
         * @return 6维参数向量（前3维为Cayley参数，后3维为平移）
         */
        inline Eigen::Matrix<double, 6, 1> RelativePoseToParams(const RelativePose &pose)
        {
            Eigen::Matrix<double, 6, 1> params;
            CayleyParams cayley = RotationToCayley(pose.Rij);
            params.head<3>() = cayley;
            params.tail<3>() = pose.tij;
            return params;
        }

        /**
         * @brief 将参数向量转换为相对位姿
         * @param params 6维参数向量（前3维为Cayley参数，后3维为平移）
         * @return 相对位姿
         */
        inline RelativePose ParamsToRelativePose(const Eigen::Matrix<double, 6, 1> &params)
        {
            RelativePose pose;
            pose.Rij = CayleyToRotation(params.head<3>());
            pose.tij = params.tail<3>();
            return pose;
        }

    } // namespace types

} // namespace PoSDK

#endif
