#ifndef _INTERFACE_
#define _INTERFACE_

#include <memory>
#include <vector>
#include <iostream>
#include <cstdarg>
#include <string>
#include <filesystem>
#include <unordered_map>
#include <set>
namespace PoSDK
{
    namespace Interface
    {

// ==================== Log Macro section ======================
// Copyright: author Qi <PERSON>
// Readme:
// ===============================================================
// 日志级别定义
#define PO_LOG_NONE 0    // 输出简单日志
#define PO_LOG_NORMAL 1  // 输出普通日志
#define PO_LOG_VERBOSE 2 // 输出详细日志

        // 辅助函数，提取文件名
        inline std::string FileNameOnly(const char *path)
        {
            return std::filesystem::path(path).filename().string();
        }

        // 日志宏定义 - 在DEBUG模式下支持所有级别，否则只支持简单日志

#ifdef _DEBUG
#define PO_LOG(level)        \
    if (log_level_ >= level) \
    std::cout << "[PoSDK | " << GetType() << "] >>> " << ((level > 0) ? (PoSDK::Interface::FileNameOnly(__FILE__) + " line" + std::to_string(__LINE__) + ": ") : "")
#else
#define PO_LOG(level)        \
    if (log_level_ >= level) \
    std::cout << "[PoSDK | " << GetType() << "] >>> "
#endif

// 错误日志宏定义（红色）
#ifdef _DEBUG
#define PO_LOG_ERR \
    std::cerr << "\033[31m[PoSDK | " << GetType() << "] ERROR >>> " << PoSDK::Interface::FileNameOnly(__FILE__) << " line" << std::to_string(__LINE__) << ": \033[0m"
#else
#define PO_LOG_ERR \
    std::cerr << "\033[31m[PoSDK | " << GetType() << "] ERROR >>> \033[0m"
#endif

// 警告日志宏定义 (蓝色)
#ifdef _DEBUG
#define PO_LOG_WARNING \
    std::cout << "\033[34m[PoSDK | " << GetType() << "] WARNING >>> " << PoSDK::Interface::FileNameOnly(__FILE__) << " line" << std::to_string(__LINE__) << ": \033[0m"
#else
#define PO_LOG_WARNING \
    std::cout << "\033[34m[PoSDK | " << GetType() << "] WARNING >>> \033[0m"
#endif

        // 通用的void指针类型定义
        typedef std::shared_ptr<void> VoidPtr;

// 用于回调函数的宏定义
#define FUNC_INTERFACE(X, args...) \
    if (func == #X)                \
    {                              \
        X(args);                   \
    }

// 接口类定义宏
#define INTERFACE class

// 参数类型宏 - 简化参数获取
#define REF(type) (*va_arg(argp, type *))
#define VAL(type) va_arg(argp, type)
#define STR() std::string(va_arg(argp, const char *))
// #define STR() va_arg(argp, const std::string&)

// 基础回调函数宏定义
#define FUNC_INTERFACE_BEGIN                 \
    virtual void Call(std::string func, ...) \
    {                                        \
        va_list argp;                        \
        va_start(argp, func);                \
        const std::string &func_name = func;

#define CALL(func, ...)     \
    if (func_name == #func) \
    {                       \
        func(__VA_ARGS__);  \
        return;             \
    }

#define FUNC_INTERFACE_END \
    va_end(argp);          \
    }

        // 前向声明
        class DataIO;
        typedef std::shared_ptr<DataIO> DataPtr;

        /**
         * @brief 评估状态结构体 - 增强版本，支持多类型Note
         *
         * 用于存储评估结果的状态信息
         */
        struct EvaluatorStatus
        {
            bool is_successful = false;                               // 评估是否成功
            std::string eval_type;                                    // 评估数据类型（如"RelativePoses"）
            std::vector<std::pair<std::string, double>> eval_results; // 评估结果: <变量名, 评估值>

            // 多类型Note数据：每个note类型对应一个字符串向量，索引与eval_results对应
            std::unordered_map<std::string, std::vector<std::string>> note_data;

            EvaluatorStatus() = default;
            EvaluatorStatus(bool success) : is_successful(success) {}
            EvaluatorStatus(const std::string &type) : eval_type(type) {}
            EvaluatorStatus(bool success, const std::string &type) : is_successful(success), eval_type(type) {}

            // 添加评估结果的便捷方法
            void AddResult(const std::string &var_name, double value)
            {
                eval_results.emplace_back(var_name, value);
                // note_data不需要特殊处理，按需添加即可
            }

            // 添加评估结果并同时添加单个note类型
            void AddResult(const std::string &var_name, double value,
                           const std::string &note_type, const std::string &note_value)
            {
                eval_results.emplace_back(var_name, value);
                note_data[note_type].emplace_back(note_value);
            }

            // 添加评估结果并同时添加单个note类型（使用初始化列表语法）
            void AddResult(const std::string &var_name, double value,
                           const std::initializer_list<std::string> &note_pair)
            {
                eval_results.emplace_back(var_name, value);
                if (note_pair.size() == 2)
                {
                    auto it = note_pair.begin();
                    std::string note_type = *it;
                    ++it;
                    std::string note_value = *it;
                    note_data[note_type].emplace_back(note_value);
                }
                else
                {
                    std::cerr << "[EvaluatorStatus] Warning: Invalid note pair size, expected 2 elements" << std::endl;
                }
            }

            // 添加评估结果并指定多类型note（兼容旧版本）
            void AddResult(const std::string &var_name, double value,
                           const std::unordered_map<std::string, std::string> &notes)
            {
                eval_results.emplace_back(var_name, value);
                for (const auto &[note_type, note_value] : notes)
                {
                    note_data[note_type].emplace_back(note_value);
                }
            }

            // 设置评估类型的便捷方法
            void SetEvalType(const std::string &type)
            {
                eval_type = type;
            }

            // 为指定结果索引提交特定类型的note
            void SubmitNoteMsg(size_t result_index, const std::string &note_type, const std::string &note_value)
            {
                if (result_index < eval_results.size())
                {
                    // 确保note_data[note_type]有足够的元素
                    if (note_data[note_type].size() <= result_index)
                    {
                        note_data[note_type].resize(result_index + 1);
                    }
                    note_data[note_type][result_index] = note_value;
                }
                else
                {
                    std::cerr << "[EvaluatorStatus] Warning: result_index " << result_index
                              << " out of range, current eval_results size: " << eval_results.size() << std::endl;
                }
            }

            // 为最新添加的结果提交note（便捷方法）
            void SubmitNoteMsg(const std::string &note_type, const std::string &note_value)
            {
                if (!eval_results.empty())
                {
                    note_data[note_type].emplace_back(note_value);
                }
                else
                {
                    std::cerr << "[EvaluatorStatus] Warning: No results available to attach note" << std::endl;
                }
            }

            // 为最新添加的结果提交默认note（兼容旧版本）
            void SubmitNoteMsg(const std::string &note)
            {
                SubmitNoteMsg("default", note);
            }

            // 获取所有note类型
            std::vector<std::string> GetAllNoteTypes() const
            {
                std::vector<std::string> note_types;
                for (const auto &[note_type, note_values] : note_data)
                {
                    if (!note_values.empty())
                    {
                        note_types.push_back(note_type);
                    }
                }
                return note_types;
            }

            // 获取指定变量名的所有评估结果
            std::vector<double> GetResults(const std::string &var_name) const
            {
                std::vector<double> results;
                for (const auto &[name, value] : eval_results)
                {
                    if (name == var_name)
                    {
                        results.push_back(value);
                    }
                }
                return results;
            }

            // 获取指定变量名的第一个评估结果（便捷方法）
            double GetFirstResult(const std::string &var_name, double default_value = 0.0) const
            {
                for (const auto &[name, value] : eval_results)
                {
                    if (name == var_name)
                    {
                        return value;
                    }
                }
                return default_value;
            }

            // 检查是否包含指定变量名的结果
            bool HasResult(const std::string &var_name) const
            {
                for (const auto &[name, value] : eval_results)
                {
                    if (name == var_name)
                    {
                        return true;
                    }
                }
                return false;
            }

            // 验证note数据的一致性
            bool ValidateNoteData() const
            {
                if (eval_results.empty())
                {
                    return true; // 没有评估结果时，note数据无关紧要
                }

                size_t eval_results_size = eval_results.size();

                // 验证每个note类型的向量长度
                for (const auto &[note_type, note_values] : note_data)
                {
                    if (!note_values.empty())
                    {
                        // note向量的长度要么是1（所有结果共享），要么等于eval_results的长度（一对一对应）
                        if (note_values.size() != 1 && note_values.size() != eval_results_size)
                        {
                            std::cerr << "[EvaluatorStatus] Error: note type '" << note_type
                                      << "' has " << note_values.size() << " values, but should be either 1 or "
                                      << eval_results_size << std::endl;
                            return false;
                        }
                    }
                }

                return true;
            }

            // 清空评估结果
            void Clear()
            {
                is_successful = false;
                eval_type.clear();
                eval_results.clear();
                note_data.clear();
            }
        };

        /**
         * @brief 数据输入输出基类
         *
         * 提供数据访问、存储和加载的基本接口
         */
        INTERFACE DataIO
        {
        public:
            // 必须重写的函数
            /**
             * @brief 获取数据指针
             * @return void* 指向具体数据的指针
             */
            virtual void *GetData() = 0;

            /**
             * @brief 获取插件类型名称
             * @return const std::string& 插件类型名称
             */
            virtual const std::string &GetType() const = 0;

            // 构造和析构函数
            DataIO() = default;
            virtual ~DataIO()
            {
#ifdef DEBUG_MEMORY
                std::cout << "[DataIO] Destroying at " << this << std::endl;
#endif
            }

            // 可选重写的函数
            /**
             * @brief 保存数据到文件
             * @param folder 存储目录路径
             * @param filename 文件名（不含扩展名）
             * @param extension 文件扩展名（包含点号，如 ".pb"）
             * @return bool 是否保存成功
             */
            virtual bool Save(const std::string &folder = "",
                              const std::string &filename = "",
                              const std::string &extension = ".pb") { return false; }

            /**
             * @brief 从文件加载数据
             * @param filepath 完整的文件路径（包含文件名和扩展名）
             * @param file_type 文件类型（如 "pb"）
             * @return bool 是否加载成功
             */
            virtual bool Load(const std::string &filepath = "",
                              const std::string &file_type = "pb") { return false; }

            /**
             * @brief 回调函数接口
             * @param func 函数名
             */
            virtual void Call(std::string func, ...) {}

            // /**
            //  * @brief 可视化接口
            //  * @param data_ptr 数据指针
            //  * @return bool 是否可视化成功
            //  */
            // virtual bool Visualize(DataPtr& data_ptr) { return false; }

            /**
             * @brief 深拷贝数据
             * @return 深拷贝后的数据指针
             */
            virtual DataPtr CopyData() const
            {
                std::cout << "\033[34m[" << GetType() << "] CopyData not implemented\033[0m" << std::endl;
                return nullptr;
            }

            /**
             * @brief 评估数据准确性
             * @param gt_data 真值数据指针
             * @return 评估状态，包含评估结果
             * @details 派生类需要实现此函数来提供具体的评估逻辑
             *          返回的EvaluatorStatus包含评估结果，由调用方负责将结果添加到全局评估器
             */
            virtual EvaluatorStatus Evaluate(DataPtr gt_data)
            {
                EvaluatorStatus eval_status;
                eval_status.is_successful = false;
                if (!eval_status.is_successful)
                {
                    // 蓝色警告
                    std::cout << "\033[34m[" << GetType() << "] Evaluate not implemented\033[0m" << std::endl;
                }
                return eval_status;
            }
        };

        /**
         * @brief 方法基类
         *
         * 提供算法方法的基本接口
         */
        INTERFACE Method
        {
        public:
            /**
             * @brief 构建/执行方法
             * @param material_ptr 输入数据指针
             * @return DataPtr 输出数据指针
             */
            virtual DataPtr Build(const DataPtr &material_ptr = nullptr) = 0;

            /**
             * @brief 获取插件类型名称
             * @return const std::string& 插件类型名称
             */
            virtual const std::string &GetType() const = 0;

            /**
             * @brief 获取方法输入数据类型
             * @return const std::vector<std::string>& 输入数据类型列表
             */
            virtual const std::vector<std::string> &GetInputTypes() const = 0;

            /**
             * @brief 获取核心计算时间（毫秒）
             * @return 核心计算时间，默认返回0.0
             * @details 派生类应当重写此函数以返回实际的核心计算时间，
             *          核心计算时间指算法的主要计算过程，不包括数据转换等辅助操作
             */
            virtual double GetCoreTime() const { return 0.0; }

            virtual ~Method() = default;
        };

        typedef std::shared_ptr<Method> MethodPtr;

        /**
         * @brief 行为基类
         *
         * 继承自Method类,提供更多行为相关的接口
         */
        INTERFACE Behavior : virtual public Method
        {
        public:
            virtual DataPtr Build(const DataPtr &material_ptr = nullptr) = 0;
            virtual const std::string &GetType() const = 0;

            virtual ~Behavior() = default;

            /**
             * @brief 获取材料类型
             * @return const std::string& 材料类型名称
             */
            virtual const std::string &GetMaterialType() const
            {
                static const std::string empty;
                return empty;
            }

            /**
             * @brief 获取产品类型
             * @return const std::string& 产品类型名称
             */
            virtual const std::string &GetProductType() const
            {
                static const std::string empty;
                return empty;
            }

            /**
             * @brief 从配置文件设置选项
             * @param path 配置文件路径
             * @param file_type 文件类型
             */
            virtual void SetOptionsFromConfigFile(const std::string &path,
                                                  const std::string &file_type) {}
        };

        typedef std::shared_ptr<Behavior> BehaviorPtr;

        /*
                // Original GetDataPtr - to be replaced by the enhanced version in interfaces_preset.hpp
                template<typename T>
                std::shared_ptr<T> GetDataPtr(const Interface::DataPtr& data_ptr) {
                    try {
                        if (!data_ptr) {
                            return nullptr;
                        }

                        // 获取原始数据指针
                        void* raw_ptr = data_ptr->GetData();
                        if (!raw_ptr) {
                            std::cerr << "[Interface] Data pointer is null" << std::endl;
                            return nullptr;
                        }

                        // 尝试进行类型转换
                        T* typed_ptr = dynamic_cast<T*>(static_cast<T*>(raw_ptr));
                        if (!typed_ptr) {
                            std::cerr << "[Interface] Failed to cast data pointer to target type" << std::endl;
                            return nullptr;
                        }

                        // 返回共享指针,使用空析构函数避免重复释放
                        return std::shared_ptr<T>(typed_ptr, [](T*){});

                    } catch (const std::bad_cast& e) {
                        std::cerr << "[Interface] Type cast error: " << e.what() << std::endl;
                        return nullptr;
                    } catch (const std::exception& e) {
                        std::cerr << "[Interface] Error: " << e.what() << std::endl;
                        return nullptr;
                    } catch (...) {
                        std::cerr << "[Interface] Unknown error occurred" << std::endl;
                        return nullptr;
                    }
                }
                */

    } // namespace Interface
} // namespace PoSDK
#endif // _INTERFACE_
