#ifndef _DATA_MATCHES_
#define _DATA_MATCHES_

#include "interfaces_preset.hpp"
#include "types.hpp"
#include <proto/matches.pb.h>
#include <filesystem>
#include <iostream>
#include <sstream>

namespace PoSDK
{

    using namespace Interface;
    using namespace types;

    class DataMatches : public PbDataIO
    {
    public:
        DataMatches()
        {
            // 设置默认存储路径
            auto exe_path = std::filesystem::current_path();
            auto tmp_store_folder = exe_path / "storage" / "matches";
            std::filesystem::create_directories(tmp_store_folder);
            SetStorageFolder(tmp_store_folder);
            // std::cout << "[DataMatches] Default matches storage folder: " << tmp_store_folder << std::endl;
        }

        virtual ~DataMatches() = default;

        const std::string &GetType() const override
        {
            static const std::string &type = "data_matches";
            return type;
        }

        virtual void *GetData() override
        {
            return static_cast<void *>(&matches_);
        }

        // 实现 Call 接口
        FUNC_INTERFACE_BEGIN
        CALL(PrintMatchesInfo)
        FUNC_INTERFACE_END

        // 重写 CopyData 方法
        DataPtr CopyData() const override
        {
            std::cout << "[DataMatches] Creating deep copy of matches data" << std::endl;

            auto copied_data = std::make_shared<DataMatches>();

            // 深拷贝所有匹配数据
            for (const auto &[view_pair, id_matches] : matches_)
            {
                IdMatches copied_matches;
                copied_matches.reserve(id_matches.size());

                for (const auto &match : id_matches)
                {
                    IdMatch copied_match;
                    copied_match.i = match.i;
                    copied_match.j = match.j;
                    copied_match.is_inlier = match.is_inlier;
                    copied_matches.push_back(copied_match);
                }

                copied_data->matches_[view_pair] = std::move(copied_matches);
            }

            // 复制存储路径设置
            copied_data->SetStorageFolder(GetStorageFolder());

            std::cout << "[DataMatches] Deep copy completed with "
                      << copied_data->matches_.size() << " view pairs" << std::endl;

            return std::static_pointer_cast<DataIO>(copied_data);
        }

        // 添加访问器方法
        const Matches &GetMatches() const { return matches_; }
        Matches &GetMatches() { return matches_; }

    protected:
        // 实现protobuf序列化接口
        std::unique_ptr<google::protobuf::Message> CreateProtoMessage() const override
        {
            std::cout << "[DataMatches] Creating proto message" << std::endl;
            return std::make_unique<pomvg::proto::MatchesData>();
        }

        std::unique_ptr<google::protobuf::Message> ToProto() const override
        {
            auto proto_msg = std::make_unique<pomvg::proto::MatchesData>();

            std::cout << "[DataMatches] Converting matches to proto format" << std::endl;
            std::cout << "[DataMatches] Number of view pairs: " << matches_.size() << std::endl;

            // 转换匹配数据
            for (const auto &[view_pair, id_matches] : matches_)
            {
                auto *proto_view_matches = proto_msg->add_view_matches();

                // 设置视图对索引
                PROTO_SET_BASIC(proto_view_matches, view_i, view_pair.first);
                PROTO_SET_BASIC(proto_view_matches, view_j, view_pair.second);

                std::cout << "[DataMatches] Processing view pair ("
                          << view_pair.first << "," << view_pair.second
                          << ") with " << id_matches.size() << " matches" << std::endl;

                // 添加匹配对
                for (const auto &match : id_matches)
                {
                    auto *proto_match = proto_view_matches->add_matches();
                    PROTO_SET_BASIC(proto_match, i, match.i);
                    PROTO_SET_BASIC(proto_match, j, match.j);
                    PROTO_SET_BASIC(proto_match, is_inlier, match.is_inlier);
                }
            }

            std::cout << "[DataMatches] Proto conversion completed" << std::endl;
            return proto_msg;
        }

        bool FromProto(const google::protobuf::Message &message) override
        {
            const auto &proto_msg = dynamic_cast<const pomvg::proto::MatchesData &>(message);

            std::cout << "[DataMatches] Converting from proto format" << std::endl;
            std::cout << "[DataMatches] Number of view matches in proto: "
                      << proto_msg.view_matches_size() << std::endl;

            // 清除现有数据
            matches_.clear();

            // 转换匹配数据
            for (const auto &proto_view_matches : proto_msg.view_matches())
            {
                ViewPair view_pair(proto_view_matches.view_i(), proto_view_matches.view_j());
                IdMatches &id_matches = matches_[view_pair];

                std::cout << "[DataMatches] Loading view pair ("
                          << view_pair.first << "," << view_pair.second
                          << ") with " << proto_view_matches.matches_size()
                          << " matches" << std::endl;

                for (const auto &proto_match : proto_view_matches.matches())
                {
                    IdMatch match;
                    PROTO_GET_BASIC(proto_match, i, match.i);
                    PROTO_GET_BASIC(proto_match, j, match.j);
                    PROTO_GET_BASIC(proto_match, is_inlier, match.is_inlier);
                    id_matches.push_back(match);
                }
            }

            std::cout << "[DataMatches] Proto loading completed with "
                      << matches_.size() << " view pairs" << std::endl;
            return true;
        }

    private:
        // 将 PrintMatchesInfo 改为私有方法
        void PrintMatchesInfo()
        {
            std::cout << "\n=== Matches Information ===" << std::endl;
            std::cout << "Total view pairs: " << matches_.size() << std::endl;

            for (const auto &[view_pair, id_matches] : matches_)
            {
                std::cout << "View pair (" << view_pair.first << ","
                          << view_pair.second << "):" << std::endl;
                std::cout << "  Number of matches: " << id_matches.size() << std::endl;

                size_t inlier_count = 0;
                for (const auto &match : id_matches)
                {
                    if (match.is_inlier)
                        inlier_count++;
                }
                std::cout << "  Inlier matches: " << inlier_count << std::endl;
            }
            std::cout << "========================\n"
                      << std::endl;
        }

        Matches matches_;
    };

} // namespace PoSDK

#endif
