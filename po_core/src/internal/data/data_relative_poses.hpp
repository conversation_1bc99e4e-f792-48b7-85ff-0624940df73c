#ifndef _DATA_RELATIVE_POSES_
#define _DATA_RELATIVE_POSES_

#include <iostream>
#include <fstream>
#include <numeric>
#include <algorithm>
#include <iomanip>
#include <limits>
#include "interfaces_preset.hpp"
#include "types.hpp"
#include "../fileIO/file_io.hpp"
#include "proto/relative_poses.pb.h"
#include "evaluator.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    class DataRelativePoses : public PbDataIO
    {
    public:
        const std::string &GetType() const override
        {
            static const std::string &type = "data_relative_poses";
            return type;
        }

        EvaluatorStatus Evaluate(DataPtr gt_data) override
        {
            EvaluatorStatus eval_status;
            eval_status.is_successful = false;
            eval_status.SetEvalType("RelativePoses");

            try
            {
                // 检查真值数据是否有效
                if (!gt_data)
                {
                    std::cerr << "[" << GetType() << "] GT data is null" << std::endl;
                    return eval_status;
                }

                // 获取真值相对位姿数据
                auto gt_relative_poses_ptr = GetDataPtr<RelativePoses>(gt_data);
                if (!gt_relative_poses_ptr)
                {
                    std::cerr << "[" << GetType() << "] Failed to get GT relative poses data" << std::endl;
                    return eval_status;
                }

                const RelativePoses &gt_relative_poses = *gt_relative_poses_ptr;

                // 检查数据是否为空
                if (relative_poses_.empty())
                {
                    std::cerr << "[" << GetType() << "] Estimated relative poses data is empty" << std::endl;
                    return eval_status;
                }

                if (gt_relative_poses.empty())
                {
                    std::cerr << "[" << GetType() << "] GT relative poses data is empty" << std::endl;
                    return eval_status;
                }

                // 执行评估：使用RelativePoses的EvaluateAgainst函数
                std::vector<double> rotation_errors, translation_errors;
                size_t matched_pairs = relative_poses_.EvaluateAgainst(
                    gt_relative_poses, rotation_errors, translation_errors);

                if (matched_pairs == 0)
                {
                    std::cerr << "[" << GetType() << "] No matching pose pairs found for evaluation" << std::endl;
                    return eval_status;
                }

                // 将所有误差全部添加到EvaluatorStatus中，使用规范的多类型Note接口
                if (!rotation_errors.empty() && !translation_errors.empty())
                {
                    for (size_t i = 0; i < rotation_errors.size(); i++)
                    {
                        std::string view_pairs_msg = "(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")";

                        // 使用规范接口添加旋转误差和多类型note
                        eval_status.AddResult("rotation_error_deg", rotation_errors[i], {"view_pairs", view_pairs_msg});

                        // 使用规范接口添加平移误差和多类型note
                        eval_status.AddResult("translation_error_deg", translation_errors[i], {"view_pairs", view_pairs_msg});
                    }
                }

                eval_status.is_successful = true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[" << GetType() << "] Error during evaluation: " << e.what() << std::endl;
                eval_status.is_successful = false;
            }

            return eval_status;
        }

        DataRelativePoses()
        {
            // 设置默认存储路径
            auto exe_path = std::filesystem::current_path();
            auto tmp_store_folder = exe_path / "storage" / "poses";
            std::filesystem::create_directories(tmp_store_folder);
            SetStorageFolder(tmp_store_folder);
            // std::cout << "[DataRelativePoses] Default poses storage folder: " << tmp_store_folder << std::endl;
        }

        ~DataRelativePoses()
        {
            relative_poses_.clear();
        }

        virtual bool Save(const std::string &path = "",
                          const std::string &filename = "",
                          const std::string &extension = ".g2o") override
        {
            // 构建完整的文件路径
            std::filesystem::path file_path;
            if (path.empty())
            {
                file_path = GetStorageFolder();
            }
            else
            {
                file_path = path;
            }

            if (!filename.empty())
            {
                file_path /= filename;
            }
            else
            {
                file_path /= "relative_poses";
            }

            file_path += extension;

            // 使用g2o_io保存数据
            bool success = file::SaveToG2O(file_path.string(), relative_poses_);

            if (success)
            {
                std::cout << "[DataRelativePoses] Successfully saved to: "
                          << file_path << std::endl;
            }
            else
            {
                std::cerr << "[DataRelativePoses] Failed to save to: "
                          << file_path << std::endl;
            }

            return success;
        }

        virtual bool Load(const std::string &path,
                          const std::string &file_type = "g2o") override
        {
            if (file_type != "g2o")
            {
                std::cerr << "[DataRelativePoses] Unsupported file type: "
                          << file_type << std::endl;
                return false;
            }

            // 检查文件是否存在
            if (!std::filesystem::exists(path))
            {
                std::cerr << "[DataRelativePoses] File not found: "
                          << path << std::endl;
                return false;
            }

            // 使用g2o_io加载数据
            bool success = file::LoadFromG2O(path, relative_poses_);

            if (success)
            {
                std::cout << "[DataRelativePoses] Successfully loaded from: "
                          << path << std::endl;
                std::cout << "Loaded " << relative_poses_.size()
                          << " relative poses" << std::endl;
            }
            else
            {
                std::cerr << "[DataRelativePoses] Failed to load from: "
                          << path << std::endl;
            }

            return success;
        }

        virtual void *GetData() override
        {
            return static_cast<void *>(&relative_poses_);
        }

        /// @brief 创建数据的深拷贝
        /// @return 拷贝后的DataPtr
        DataPtr CopyData() const override
        {
            // 创建一个新的DataRelativePoses实例
            auto cloned_data = std::make_shared<DataRelativePoses>();

            // 获取新实例内部RelativePoses的指针
            auto cloned_poses_ptr = static_cast<RelativePoses *>(cloned_data->GetData());
            if (!cloned_poses_ptr)
            {
                std::cerr << "[DataRelativePoses::CopyData] Error: Failed to get internal RelativePoses pointer for clone." << std::endl;
                return nullptr;
            }

            // 执行RelativePoses的深拷贝（依赖RelativePoses的默认拷贝构造/赋值）
            *cloned_poses_ptr = relative_poses_;

            return cloned_data;
        }

        // 实现 Call 接口
        FUNC_INTERFACE_BEGIN
        CALL(PrintPosesInfo)
        FUNC_INTERFACE_END

        // 实现protobuf序列化接口
        std::unique_ptr<google::protobuf::Message> CreateProtoMessage() const override
        {
            std::cout << "[DataRelativePoses] Creating proto message" << std::endl;
            return std::make_unique<pomvg::proto::RelativePoses>();
        }

        std::unique_ptr<google::protobuf::Message> ToProto() const override
        {
            auto proto_msg = std::make_unique<pomvg::proto::RelativePoses>();

            std::cout << "[DataRelativePoses] Converting poses to proto format" << std::endl;
            std::cout << "[DataRelativePoses] Number of poses: " << relative_poses_.size() << std::endl;

            // 转换所有相对位姿
            for (const auto &pose : relative_poses_)
            {
                auto *proto_pose = proto_msg->add_poses();

                // 设置视图索引
                PROTO_SET_BASIC(proto_pose, i, pose.i);
                PROTO_SET_BASIC(proto_pose, j, pose.j);

                // 设置旋转矩阵(按列存储)
                PROTO_SET_MATRIX3D(proto_pose, rij, pose.Rij);

                // 设置平移向量
                PROTO_SET_BASIC(proto_pose, tij_x, pose.tij.x());
                PROTO_SET_BASIC(proto_pose, tij_y, pose.tij.y());
                PROTO_SET_BASIC(proto_pose, tij_z, pose.tij.z());

                // 设置权重
                PROTO_SET_BASIC(proto_pose, weight, pose.weight);
            }

            std::cout << "[DataRelativePoses] Proto conversion completed" << std::endl;
            return proto_msg;
        }

        bool FromProto(const google::protobuf::Message &message) override
        {
            const auto &proto_msg = dynamic_cast<const pomvg::proto::RelativePoses &>(message);

            std::cout << "[DataRelativePoses] Converting from proto format" << std::endl;
            std::cout << "[DataRelativePoses] Number of poses in proto: "
                      << proto_msg.poses_size() << std::endl;

            // 清除现有数据
            relative_poses_.clear();

            // 转换所有相对位姿
            for (const auto &proto_pose : proto_msg.poses())
            {
                RelativePose pose;

                // 获取视图索引
                PROTO_GET_BASIC(proto_pose, i, pose.i);
                PROTO_GET_BASIC(proto_pose, j, pose.j);

                // 获取旋转矩阵
                PROTO_GET_MATRIX3D(proto_pose, rij, pose.Rij);

                // 获取平移向量
                pose.tij = Vector3d(
                    proto_pose.tij_x(),
                    proto_pose.tij_y(),
                    proto_pose.tij_z());

                // 获取权重
                PROTO_GET_BASIC(proto_pose, weight, pose.weight);

                relative_poses_.push_back(std::move(pose));
            }

            std::cout << "[DataRelativePoses] Proto loading completed with "
                      << relative_poses_.size() << " poses" << std::endl;
            return true;
        }

    private:
        // 打印相对位姿信息
        void PrintPosesInfo()
        {
            std::cout << "\n=== Relative Poses Information ===" << std::endl;
            std::cout << "Total view pairs: " << relative_poses_.size() << std::endl;
        }

        RelativePoses relative_poses_;
    };

} // namespace PoSDK

#endif // _DATA_RELATIVE_POSES_
