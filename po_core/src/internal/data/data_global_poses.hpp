#ifndef _DATA_GLOBAL_POSES_
#define _DATA_GLOBAL_POSES_

#include <iostream>
#include "interfaces_preset.hpp"
#include "types.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    /**
     * @brief 全局位姿数据IO类
     * @details 负责全局位姿数据的序列化和反序列化
     */
    class DataGlobalPoses : public DataIO
    {
    public:
        /// @brief 获取数据类型
        /// @return 数据类型字符串
        const std::string &GetType() const override
        {
            static const std::string &type = "data_global_poses";
            return type;
        }

        /// @brief 默认构造函数
        DataGlobalPoses() = default;

        /// @brief 加载数据
        /// @param path 文件路径
        /// @param file_type 文件类型
        /// @return 是否加载成功
        virtual bool Load(const std::string &path,
                          const std::string &file_type = "pb") override;

        /// @brief 获取数据指针
        /// @return 数据指针
        virtual void *GetData() override
        {
            return static_cast<void *>(&global_poses_);
        }

        /**
         * @brief 从文件加载位姿数据
         * @param path 文件路径
         * @return 是否加载成功
         */
        bool LoadPoseFromFile(const std::string &path);

        /**
         * @brief 保存位姿数据到文件
         * @param path 文件路径
         * @return 是否保存成功
         */
        bool SavePoseToFile(const std::string &path) const;

        /// @brief 保存数据
        /// @param path 保存路径
        /// @param filename 文件名
        /// @param extension 文件扩展名
        /// @return 是否保存成功
        virtual bool Save(const std::string &path = "",
                          const std::string &filename = "",
                          const std::string &extension = ".pb") override;

        /// @brief 创建数据的深拷贝
        /// @return 拷贝后的DataPtr
        DataPtr CopyData() const override;

    private:
        GlobalPoses global_poses_; ///< 全局位姿数据
    };

} // namespace PoSDK
#endif // _DATA_GLOBAL_POSES_
