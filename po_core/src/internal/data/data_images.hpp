#ifndef _DATA_IMAGES_
#define _DATA_IMAGES_

#include "interfaces.hpp"
#include "types.hpp"
#include <cctype> // 用于tolower
#include <string>

namespace PoSDK
{
    using namespace Interface;
    using namespace types;
    class DataImages : public DataIO
    {
    public:
        const std::string &GetType() const override
        {
            static const std::string &type = "data_images";
            return type;
        }

        DataImages()
        {
            // 其他初始化操作...
        }

        virtual void *GetData() override
        {
            return static_cast<void *>(&image_paths_);
        }

        virtual bool Save(const std::string &path = "",
                          const std::string &filename = "",
                          const std::string &extension = ".pb") override;
        virtual bool Load(const std::string &path,
                          const std::string &file_type = "pb") override;

        void AddImagesFromFolder(const std::string &folder_path);

        // 基类中的默认实现
        FUNC_INTERFACE_BEGIN
        CALL(AddImagesFromFolder, STR())
        FUNC_INTERFACE_END



    private:
        ImagePaths image_paths_;
    };

}
#endif
