// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "data_features.hpp"
#include <fstream>
#include <iostream>

namespace PoSDK
{
    // 可以在这里添加额外的实现，如果需要的话
    // 目前所有功能都在头文件中实现了

} // namespace PoSDK