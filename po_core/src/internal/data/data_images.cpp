// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "data_images.hpp"
#include <filesystem> // C++17
#include <iostream>
#include <fstream> // 用于文件读取

#include <algorithm>     // 用于std::transform
#include <unordered_set> // 用于supported_extensions集合

namespace fs = std::filesystem;

namespace PoSDK
{

    bool DataImages::Save(const std::string &path,
                          const std::string &filename,
                          const std::string &extension)
    {
        return true;
    }

    bool DataImages::Load(const std::string &path,
                          const std::string &file_type)
    {
        // 检查path是否是目录
        // debug
        std::cout << "[DataImages] Load: " << path << " " << file_type << std::endl;
        std::cout << "[DataImages] is_directory: " << std::filesystem::is_directory(path) << std::endl;
        if (std::filesystem::is_directory(path) || file_type == "folder")
        {
            // 如果是目录，直接调用AddImagesFromFolder函数
            AddImagesFromFolder(path);
            return true;
        }
        else
        {
            // 异常检测（std::cerr）
            std::cerr << "[DataImages] Error: Invalid file type: " << file_type << std::endl;
            return false;
        }

        return true;
    }
    void DataImages::AddImagesFromFolder(const std::string &folder_path)
    {
        // 支持的图像文件扩展名（不区分大小写）
        const std::unordered_set<std::string> supported_extensions = {
            ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".webp"};

        for (const auto &entry : fs::directory_iterator(folder_path))
        {
            if (entry.is_regular_file())
            {
                // 获取文件扩展名并转换为小写
                std::string extension = entry.path().extension().string();
                std::transform(extension.begin(), extension.end(),
                               extension.begin(), ::tolower);

                // 检查是否为支持的图像文件类型
                if (supported_extensions.find(extension) != supported_extensions.end())
                {
                    try
                    {
                        // 检查文件是否可读
                        std::ifstream file(entry.path(), std::ios::binary);
                        if (file.good())
                        {
                            // 检查文件头部魔数（Magic Numbers）
                            unsigned char header[8] = {0};
                            file.read(reinterpret_cast<char *>(header), sizeof(header));

                            bool is_valid_image = false;

                            // JPEG: FF D8
                            if (header[0] == 0xFF && header[1] == 0xD8)
                            {
                                is_valid_image = true;
                            }
                            // PNG: 89 50 4E 47 0D 0A 1A 0A
                            else if (header[0] == 0x89 && header[1] == 0x50 &&
                                     header[2] == 0x4E && header[3] == 0x47)
                            {
                                is_valid_image = true;
                            }
                            // BMP: 42 4D
                            else if (header[0] == 0x42 && header[1] == 0x4D)
                            {
                                is_valid_image = true;
                            }
                            // TIFF: 49 49 2A 00 或 4D 4D 00 2A
                            else if ((header[0] == 0x49 && header[1] == 0x49 &&
                                      header[2] == 0x2A && header[3] == 0x00) ||
                                     (header[0] == 0x4D && header[1] == 0x4D &&
                                      header[2] == 0x00 && header[3] == 0x2A))
                            {
                                is_valid_image = true;
                            }

                            if (is_valid_image)
                            {
                                image_paths_.emplace_back(entry.path().string(), true);
                            }
                        }
                    }
                    catch (const std::exception &e)
                    {
                        std::cerr << "[DataImages] Failed to check file: "
                                  << entry.path().string() << " - "
                                  << e.what() << std::endl;
                    }
                }
            }
        }

        // 如果没有找到有效的图像文件，输出警告
        if (image_paths_.empty())
        {
            std::cerr << "[DataImages] Warning: No valid image files found in "
                      << folder_path << std::endl;
        }
    }

} // namespace PoSDK
