// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#pragma once

#include "types.hpp"

#include <proto/features.pb.h>
#include <filesystem>
#include <interfaces_preset.hpp>

namespace PoSDK
{
    using namespace types;
    using namespace Interface;

    class DataFeatures : public PbDataIO
    {
    public:
        DataFeatures()
        {
            // 获取程序运行目录
            auto exe_path = std::filesystem::current_path();
            // 创建storage目录
            auto tmp_store_folder = exe_path / "storage" / "features";
            std::filesystem::create_directories(tmp_store_folder);
            SetStorageFolder(tmp_store_folder);
            // std::cout << "[DataFeatures] Default features storage folder: " << tmp_store_folder << std::endl;
        }

        virtual ~DataFeatures() = default;

        virtual void *GetData() override
        {
            return static_cast<void *>(&features_);
        }

        // 实现纯虚函数
        const std::string &GetType() const override
        {
            static const std::string type = "data_features";
            return type;
        }

        /**
         * @brief 复制数据
         * @return 复制的数据指针
         */
        DataPtr CopyData() const override
        {
            // 创建一个新的DataFeatures对象
            auto cloned_data = std::make_shared<DataFeatures>();

            // 获取内部FeaturesInfo对象的指针
            auto cloned_features_ptr = static_cast<FeaturesInfo *>(cloned_data->GetData());
            if (!cloned_features_ptr)
            {
                std::cerr << "[DataFeatures::CopyData] Error: Failed to get internal features pointer for clone." << std::endl;
                return nullptr;
            }

            // 执行FeaturesInfo对象的深拷贝
            *cloned_features_ptr = features_;

            // 复制存储目录（如果需要）
            cloned_data->SetStorageFolder(GetStorageFolder());

            return cloned_data;
        }

    protected:
        // 实现必要的protobuf接口
        std::unique_ptr<google::protobuf::Message> CreateProtoMessage() const override
        {
            return std::make_unique<proto::FeaturesData>();
        }

        std::unique_ptr<google::protobuf::Message> ToProto() const override
        {
            auto proto_msg = std::make_unique<proto::FeaturesData>();

            // 转换所有图像特征
            for (const auto &image_features : features_)
            {
                auto *proto_image = proto_msg->add_image_features();
                proto_image->set_image_path(image_features.image_path);
                proto_image->set_is_used(image_features.is_used);

                // 转换该图像的所有特征点
                for (const auto &feature : image_features.features)
                {
                    auto *proto_feature = proto_image->add_features();
                    PROTO_SET_VECTOR2D(proto_feature, coord, feature.coord);
                    PROTO_SET_BASIC(proto_feature, size, feature.size);
                    PROTO_SET_BASIC(proto_feature, angle, feature.angle);
                    PROTO_SET_BASIC(proto_feature, is_used, feature.is_used);
                }
            }

            return proto_msg;
        }

        bool FromProto(const google::protobuf::Message &message) override
        {
            const auto &proto_msg = static_cast<const proto::FeaturesData &>(message);

            features_.clear();
            features_.resize(proto_msg.image_features_size());

            for (int i = 0; i < proto_msg.image_features_size(); ++i)
            {
                const auto &proto_image = proto_msg.image_features(i);
                auto &image_features = features_[i];

                image_features.image_path = proto_image.image_path();
                image_features.is_used = proto_image.is_used();

                for (const auto &proto_feature : proto_image.features())
                {
                    FeaturePoint feature;
                    PROTO_GET_VECTOR2D(proto_feature, coord, feature.coord);
                    PROTO_GET_BASIC(proto_feature, size, feature.size);
                    PROTO_GET_BASIC(proto_feature, angle, feature.angle);
                    PROTO_GET_BASIC(proto_feature, is_used, feature.is_used);

                    // 添加特征点到图像特征中
                    image_features.features.push_back(feature);
                }
            }
            return true;
        }

    public:
        FeaturesInfo features_;
    };

} // namespace PoSDK
