// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "data_global_rotations.hpp"
#include "file_io.hpp"
#include <chrono>
#include <fstream>

using namespace std;
using namespace chrono;

namespace PoSDK
{

    //-------------- class member function -------------------
    bool DataGlobalRotations::Save(const std::string &path,
                                   const std::string &filename,
                                   const std::string &extension)
    {
        return true;
    }

    bool DataGlobalRotations::Load(const std::string &path,
                                   const std::string &file_type)
    {
        std::cout << "<DataGlobalRotations::Load> msg: load data to path ="
                  << path << std::endl;
        file::SetupOrientation(path.c_str(), global_rotations_);
        return true;
    };

}
