#ifndef _DATA_GLOBAL_TRANSLATIONS_
#define _DATA_GLOBAL_TRANSLATIONS_

#include <iostream>
#include "interfaces.hpp"
#include "types.hpp"
namespace PoSDK
{
    using namespace Interface;
    using namespace types;
    class DataGlobalTranslations : public DataIO
    {
    public:
        const std::string &GetType() const override
        {
            static const std::string &type = "data_global_translations";
            return type;
        }

        DataGlobalTranslations()
        {
            // 其他初始化操作...
        }

        virtual bool Save(const std::string &path = "",
                          const std::string &filename = "",
                          const std::string &extension = ".pb") override;
        virtual bool Load(const std::string &path,
                          const std::string &file_type = "pb") override;
        virtual void *GetData() override
        {
            return static_cast<void *>(&global_translations_);
        }



    private:
        GlobalTranslations global_translations_;
    };

}
#endif
