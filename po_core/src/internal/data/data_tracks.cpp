// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "data_tracks.hpp"
#include <chrono>
#include <fstream>
#include <set>
#include <filesystem> // 添加filesystem支持
using namespace std;
using namespace chrono;

namespace PoSDK
{
    //-------------- internal function -------------------

    // bool LoadTracks(
    //         const std::string& filename,
    //         Tracks& tracks,
    //         Size* num_obs_ptr = nullptr,
    //         Size* num_pts_ptr = nullptr,
    //         Size* num_views_ptr = nullptr){
    //     auto start_time=steady_clock::now();

    //     //step.1 read tracks file

    //     fstream tracks_file(filename,ios::in);
    //     if (!tracks_file.is_open())
    //     {
    //         std::cout<<"tracks file cannot load"<<std::endl;
    //         return false;
    //     }

    //     Size num_view, num_pts, num_obs;
    //     tracks_file>>num_view>>num_pts>>num_obs;
    //     if ( num_obs_ptr){
    //         *num_obs_ptr = num_obs;
    //     }

    //     if ( num_pts_ptr){
    //         *num_pts_ptr = num_pts;
    //     }

    //     if ( num_views_ptr){
    //         *num_views_ptr = num_view;
    //     }

    //     int record_pts_id=-1;
    //     TrackInfo track_info;
    //     ObsInfo tmp_obs;

    //     tracks.reserve(num_pts);
    //     for (int i=0;i<num_obs;i++){
    //         tracks_file>>tmp_obs.view_id>>tmp_obs.pts_id>>tmp_obs.coord(0)>>tmp_obs.coord(1);
    //         tmp_obs.coord(0)=-tmp_obs.coord(0);
    //         tmp_obs.coord(1)=-tmp_obs.coord(1);
    //         tmp_obs.coord(2)=1;

    //         if( tmp_obs.pts_id != record_pts_id)
    //         {
    //             if(i>0)
    //             {
    //                 if (track_info.track.size()<3){
    //                     track_info.is_used=0;//only need use observation of tracking length>2
    //                 }
    //                 else{
    //                     track_info.is_used=1;
    //                 }
    //                 tracks.emplace_back(track_info);

    //             }
    //             track_info.track.clear();
    //         }
    //         track_info.track.emplace_back(tmp_obs);
    //         record_pts_id=tmp_obs.pts_id;
    //     }
    //     tracks_file.close();
    //     tracks.emplace_back(track_info);

    //     //    sort(tracks.begin(),
    //     //         tracks.end(),compare_track_info);

    //     //    simplier_tracks(tracks);

    //     auto end_time=steady_clock::now();
    //     auto duration    = duration_cast<microseconds>(end_time - start_time);
    //     cout <<  "time for loading tracks file: "
    //           << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //           << "s" << endl;
    //     return true;
    // }

    // bool SaveTracks(const std::string& filename,
    //                 const Tracks& tracks){
    //     auto start_time=steady_clock::now();

    //     // start saving tracks
    //     fstream tracks_file(filename,ios::out|ios::trunc);
    //     if (!tracks_file.is_open())
    //     {
    //         std::cout<<"tracks file cannot create"<<std::endl;
    //         return false;
    //     }

    //     // calculate number of obs
    //     Size num_obs = 0;
    //     for ( auto& track_info : tracks){
    //         num_obs += track_info.track.size();
    //     }

    //     // calculate number of pts
    //     Size num_pts = 0;
    //     num_pts = tracks.size();

    //     // calculate number of views
    //     Size num_views = 0;
    //     std::set<ViewId> views_id;
    //     for ( auto& track_info : tracks){
    //         for ( auto& track_data : track_info.track){
    //             views_id.insert(track_data.view_id);
    //         }
    //     }
    //     num_views = views_id.size();

    //     // write tracks
    //     tracks_file<< num_views << " "
    //                << num_pts << " "
    //                << num_obs << std::endl;

    //     for ( auto& track_info : tracks){
    //         for ( auto& track_data : track_info.track){
    //             tracks_file<< track_data.view_id << " ";
    //             tracks_file<< track_data.pts_id << " ";
    //             tracks_file<< -track_data.coord(0) << " ";
    //             tracks_file<< -track_data.coord(1) << " ";
    //             tracks_file<< std::endl;
    //         }
    //     }
    //     tracks_file.close();

    //     auto end_time=steady_clock::now();
    //     auto duration    = duration_cast<microseconds>(end_time - start_time);
    //     cout <<  "time for saving tracks file: "
    //           << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //           << "s" << endl;

    // }

    //-------------- class member function ----------------------
    // bool DataTracks::Save(const std::string& path,
    //                      const std::string& filename,
    //                      const std::string& extension) {
    //     return true;
    // }

    DataPtr DataTracks::CopyData() const
    {
        // 创建一个新的DataTracks对象
        auto cloned_data = std::make_shared<DataTracks>();

        // 获取内部Tracks对象的指针
        auto cloned_tracks_ptr = static_cast<Tracks *>(cloned_data->GetData());
        if (!cloned_tracks_ptr)
        {
            std::cerr << "[DataTracks::CopyData] Error: Failed to get internal tracks pointer for clone." << std::endl;
            return nullptr;
        }

        // 执行Tracks对象的深拷贝（依赖于Tracks的拷贝构造函数或赋值运算符）
        *cloned_tracks_ptr = tracks_;

        // 复制归一化状态
        cloned_tracks_ptr->SetNormalized(tracks_.IsNormalized());

        // 复制存储目录（如果需要）
        cloned_data->SetStorageFolder(GetStorageFolder());

        return cloned_data;
    }

    bool DataTracks::Load(const std::string &path,
                          const std::string &file_type)
    {
        std::cout << "<DataTracks::Load> msg: load data to path = "
                  << path << std::endl;

        if (file_type == "pb")
        {
            return PbDataIO::Load(path, file_type);
        }
        else if (file_type == "tracks")
        { // normalized tracks file - 使用file_io中的LoadTracks函数
            auto start_time = std::chrono::steady_clock::now();

            // 直接调用file_io中的LoadTracks函数
            if (!PoSDK::file::LoadTracks(path, tracks_))
            {
                std::cerr << "[DataTracks::Load] Failed to load tracks file using file_io::LoadTracks" << std::endl;
                return false;
            }

            // 输出加载时间
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
            std::cout << "[DataTracks::Load] Loading completed in "
                      << double(duration.count()) * std::chrono::microseconds::period::num / std::chrono::microseconds::period::den
                      << "s using file_io::LoadTracks" << std::endl;

            return true;
        }
        else if (file_type == "icoords")
        { // 2D coords file
            auto start_time = std::chrono::steady_clock::now();

            // 打开tracks文件
            std::fstream tracks_file(path, std::ios::in);
            if (!tracks_file.is_open())
            {
                std::cerr << "icoords file cannot load" << std::endl;
                return false;
            }

            // 读取文件头信息
            Size num_view, num_pts, num_obs;
            tracks_file >> num_view >> num_pts >> num_obs;

            // 清除现有数据并预分配空间
            tracks_.clear();
            tracks_.reserve(num_pts);
            tracks_.SetNormalized(false); // 设置初始归一化状态

            // 读取tracks数据
            int record_pts_id = -1;
            TrackInfo track_info;
            ObsInfo tmp_obs;
            IndexT obs_id_counter = 0; // 用于生成唯一的obs_id

            for (Size i = 0; i < num_obs; i++)
            {
                // 读取观测数据
                tracks_file >> tmp_obs.view_id >> tmp_obs.pts_id >> tmp_obs.coord[0] >> tmp_obs.coord[1];

                // 坐标转换（注意：现在是2D坐标）
                tmp_obs.coord[0] = -tmp_obs.coord[0];
                tmp_obs.coord[1] = -tmp_obs.coord[1];

                // 设置观测ID
                tmp_obs.obs_id = obs_id_counter++;

                // 处理新的track
                if (tmp_obs.pts_id != record_pts_id)
                {
                    if (i > 0)
                    {
                        // 使用TrackInfo的接口方法设置状态
                        if (track_info.GetObservationCount() < 2)
                        {
                            track_info.SetUsed(false);
                            for (Size j = 0; j < track_info.GetObservationCount(); ++j)
                            {
                                track_info.SetObservationUsed(j, false);
                            }
                        }
                        else
                        {
                            track_info.SetUsed(true);
                        }
                        tracks_.emplace_back(track_info);
                    }
                    track_info = TrackInfo(); // 创建新的track_info
                }

                // 使用TrackInfo的接口方法添加观测
                track_info.AddObservation(tmp_obs);
                record_pts_id = tmp_obs.pts_id;
            }

            // 添加最后一个track
            tracks_file.close();
            if (track_info.GetObservationCount() < 2)
            {
                track_info.SetUsed(false);
                for (Size j = 0; j < track_info.GetObservationCount(); ++j)
                {
                    track_info.SetObservationUsed(j, false);
                }
            }
            else
            {
                track_info.SetUsed(true);
            }
            tracks_.emplace_back(track_info);

            tracks_.SetNormalized(false);

            // 输出加载时间
            auto end_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
            std::cout << "time for loading icoords file: "
                      << double(duration.count()) * std::chrono::microseconds::period::num / std::chrono::microseconds::period::den
                      << "s" << std::endl;

            return true;
        }

        return false;
    }

    // 重写Save方法以支持tracks格式
    bool DataTracks::Save(const std::string &folder,
                          const std::string &filename,
                          const std::string &extension)
    {
        // 如果扩展名是 .tracks，使用file_io中的SaveTracks函数
        if (extension == ".tracks" || extension == "tracks")
        {
            std::cout << "<DataTracks::Save> msg: saving tracks to tracks format" << std::endl;

            // 构建完整的文件路径
            std::filesystem::path file_path;

            // 处理文件夹路径
            if (folder.empty())
            {
                if (GetStorageFolder().empty())
                {
                    std::cerr << "[DataTracks::Save] No storage directory specified" << std::endl;
                    return false;
                }
                file_path = GetStorageFolder();
            }
            else
            {
                file_path = folder;
            }

            // 处理文件名
            if (filename.empty())
            {
                file_path /= (GetType() + "_default.tracks");
            }
            else
            {
                // 移除可能存在的扩展名
                std::string base_name = filename;
                size_t ext_pos = base_name.find_last_of('.');
                if (ext_pos != std::string::npos)
                {
                    base_name = base_name.substr(0, ext_pos);
                }
                file_path /= (base_name + ".tracks");
            }

            // 创建目录
            auto directory = file_path.parent_path();
            if (!directory.empty() && !std::filesystem::exists(directory))
            {
                std::cout << "[DataTracks::Save] Creating directory: " << directory << std::endl;
                if (!std::filesystem::create_directories(directory))
                {
                    std::cerr << "[DataTracks::Save] Failed to create directory: " << directory << std::endl;
                    return false;
                }
            }

            // 调用file_io中的SaveTracks函数
            if (!PoSDK::file::SaveTracks(file_path.string(), tracks_))
            {
                std::cerr << "[DataTracks::Save] Failed to save tracks file using file_io::SaveTracks" << std::endl;
                return false;
            }

            std::cout << "[DataTracks::Save] Successfully saved tracks to: " << file_path << std::endl;
            return true;
        }
        else
        {
            // 对于其他格式，调用基类的Save方法（protobuf格式）
            return PbDataIO::Save(folder, filename, extension);
        }
    }

}
