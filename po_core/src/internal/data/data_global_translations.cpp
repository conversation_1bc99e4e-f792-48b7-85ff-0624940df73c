// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "data_global_translations.hpp"

namespace PoSDK
{

    bool DataGlobalTranslations::Save(const std::string &path,
                                      const std::string &filename,
                                      const std::string &extension)
    {
        return true;
    }

    bool DataGlobalTranslations::Load(const std::string &path,
                                      const std::string &file_type)
    {
        std::cout << "<DataGlobalTranslations::Load> msg: load data to path ="
                  << path << std::endl;
        return true;
    };

}
