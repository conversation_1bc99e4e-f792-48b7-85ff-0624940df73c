#ifndef _DATA_GLOBAL_ROTATIONS_
#define _DATA_GLOBAL_ROTATIONS_

#include <iostream>
#include "interfaces.hpp"
#include "types.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    class DataGlobalRotations : public DataIO
    {
    public:
        const std::string &GetType() const override
        {
            static const std::string &type = "data_global_rotations";
            return type;
        }

        DataGlobalRotations()
        {
        }

        virtual bool Save(const std::string &path = "",
                          const std::string &filename = "",
                          const std::string &extension = ".pb") override;
        virtual bool Load(const std::string &path,
                          const std::string &file_type = "pb") override;
        virtual void *GetData() override
        {
            return static_cast<void *>(&global_rotations_);
        }

    private:
        GlobalRotations global_rotations_;
    };

}
#endif
