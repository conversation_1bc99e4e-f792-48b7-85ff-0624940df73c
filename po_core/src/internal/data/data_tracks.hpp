#ifndef _DATA_TRACKS_
#define _DATA_TRACKS_

#include "interfaces_preset.hpp"
#include "types.hpp"
#include <proto/tracks.pb.h>
#include <iostream>

// 添加file_io.hpp以便使用LoadTracks和SaveTracks函数
#include "../../fileIO/file_io.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    class DataTracks : public PbDataIO
    {
    public:
        DataTracks()
        {
            // 设置默认存储路径
            auto exe_path = std::filesystem::current_path();
            auto tmp_store_folder = exe_path / "storage" / "tracks";
            std::filesystem::create_directories(tmp_store_folder);
            SetStorageFolder(tmp_store_folder);
            // std::cout << "[DataTracks] Default tracks storage folder: " << tmp_store_folder << std::endl;
        }

        virtual ~DataTracks() = default;

        const std::string &GetType() const override
        {
            static const std::string &type = "data_tracks";
            return type;
        }

        virtual void *GetData() override
        {
            return static_cast<void *>(&tracks_);
        }

        // 基类中的默认实现
        // FUNC_INTERFACE_BEGIN
        //     // CALL(Normalized)
        // FUNC_INTERFACE_END

        // 其他公共方法
        // void Normalized(){
        //     std::cout<<"call Normalized success!!!"<<std::endl;

        //     if(!kmats_ptr_ || is_normalized) return;

        //     std::cout<<"kmats_ptr_ = "<<kmats_ptr_<<", size = "<<kmats_ptr_->size()<<std::endl;
        //     if(kmats_ptr_->size()){
        //         std::cout<<"kmat: "<<std::endl<<(*kmats_ptr_)[0]<<std::endl;
        //         for ( auto& track_info : tracks_) {
        //             Track& track = track_info.track;

        //             for ( auto& obs : track){
        //                 Eigen::Vector3d& coord = obs.coord;
        //                 if (kmats_ptr_->size() == 1){
        //                     coord = (*kmats_ptr_)[0]*coord;
        //                 }
        //                 else{
        //                     const ViewId& view_id = obs.view_id;
        //                     coord = (*kmats_ptr_)[view_id]*coord;
        //                 }
        //             }
        //         }
        //         is_normalized = true;
        //     }
        // };

        // 实现深拷贝方法
        DataPtr CopyData() const override;

        bool Load(const std::string &path,
                  const std::string &file_type = "pb") override;

        // 重写Save方法以支持tracks格式
        bool Save(const std::string &folder = "",
                  const std::string &filename = "",
                  const std::string &extension = ".pb") override;

    protected:
        // 实现protobuf序列化接口
        std::unique_ptr<google::protobuf::Message> CreateProtoMessage() const override
        {
            std::cout << "[DataTracks] Creating proto message" << std::endl;
            return std::make_unique<pomvg::proto::TracksData>();
        }

        std::unique_ptr<google::protobuf::Message> ToProto() const override
        {
            auto proto_msg = std::make_unique<pomvg::proto::TracksData>();

            std::cout << "[DataTracks] Converting tracks to proto format" << std::endl;
            std::cout << "[DataTracks] Number of tracks: " << tracks_.size() << std::endl;

            // 设置归一化状态
            PROTO_SET_BASIC(proto_msg, is_normalized, tracks_.IsNormalized());

            // 转换所有tracks
            for (const auto &track_info : tracks_)
            {
                auto *proto_track = proto_msg->add_tracks();
                PROTO_SET_BASIC(proto_track, is_used, track_info.is_used);

                // 转换该track的所有观测点
                for (const auto &obs : track_info.track)
                {
                    auto *proto_obs = proto_track->add_observations();
                    PROTO_SET_BASIC(proto_obs, view_id, obs.view_id);
                    PROTO_SET_BASIC(proto_obs, pts_id, obs.pts_id);
                    PROTO_SET_BASIC(proto_obs, obs_id, obs.obs_id);
                    PROTO_SET_VECTOR2D(proto_obs, coord, obs.coord);
                    PROTO_SET_BASIC(proto_obs, is_used, obs.is_used);
                }
            }

            std::cout << "[DataTracks] Proto conversion completed" << std::endl;
            return proto_msg;
        }

        bool FromProto(const google::protobuf::Message &message) override
        {
            const auto &proto_msg = dynamic_cast<const pomvg::proto::TracksData &>(message);

            std::cout << "[DataTracks] Converting from proto format" << std::endl;
            std::cout << "[DataTracks] Number of tracks in proto: "
                      << proto_msg.tracks_size() << std::endl;

            // 清除现有数据
            tracks_.clear();

            // 获取归一化状态
            bool is_normalized;
            PROTO_GET_BASIC(proto_msg, is_normalized, is_normalized);
            tracks_.SetNormalized(is_normalized);

            // 转换所有tracks
            for (const auto &proto_track : proto_msg.tracks())
            {
                TrackInfo track_info;
                PROTO_GET_BASIC(proto_track, is_used, track_info.is_used);

                // 转换观测点
                for (const auto &proto_obs : proto_track.observations())
                {
                    types::ObsInfo obs;
                    PROTO_GET_BASIC(proto_obs, view_id, obs.view_id);
                    PROTO_GET_BASIC(proto_obs, pts_id, obs.pts_id);
                    PROTO_GET_BASIC(proto_obs, obs_id, obs.obs_id);
                    PROTO_GET_VECTOR2D(proto_obs, coord, obs.coord);
                    PROTO_GET_BASIC(proto_obs, is_used, obs.is_used);
                    track_info.track.push_back(obs);
                }

                tracks_.push_back(std::move(track_info));
            }

            std::cout << "[DataTracks] Proto loading completed with "
                      << tracks_.size() << " tracks" << std::endl;
            return true;
        }

    private:
        Tracks tracks_;
    };

}
#endif
