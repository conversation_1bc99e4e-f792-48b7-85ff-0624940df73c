# EvaluatorStatus SubmitNoteMsg 功能使用说明

## 概述

`EvaluatorStatus` 现在支持通过 `SubmitNoteMsg` 函数在 `Evaluate` 函数中添加备注信息，提供额外的说明信息而不影响原有的 EvalCommit 机制。

## 设计理念

备注信息作为独立的额外说明，不会覆盖或影响原有的 `ProfileCommit` 配置，保持了外部控制的灵活性。

## 使用方式

### 方式1：统一的备注信息
当所有评估结果使用同一个备注信息时：

```cpp
EvaluatorStatus eval_status;
eval_status.SetEvalType("RelativePoses");

// 添加评估结果
eval_status.AddResult("rotation_error_deg", 1.5);
eval_status.AddResult("translation_error", 0.03);

// 为所有结果设置统一的备注信息
eval_status.SubmitNoteMsg("baseline_experiment_dataset_A");

eval_status.is_successful = true;
```

### 方式2：每个结果独立的备注信息
当每个评估结果需要不同的备注信息时：

```cpp
EvaluatorStatus eval_status;
eval_status.SetEvalType("RelativePoses");

// 添加评估结果
eval_status.AddResult("rotation_error_deg", 1.5);
eval_status.AddResult("translation_error", 0.03);

// 为每个结果设置独立的备注信息
eval_status.SubmitNoteMsg("rotation_measurement_case1");
eval_status.SubmitNoteMsg("translation_measurement_case1");

eval_status.is_successful = true;
```

## 错误处理

系统会自动检查 `note_msg` 的数量是否合理：

- ✅ `note_msg.size() == 1`：所有结果使用这一个备注
- ✅ `note_msg.size() == eval_results.size()`：每个结果对应一个备注
- ❌ 其他情况：系统会发出警告并使用空备注

## 完整示例

```cpp
class DataRelativePoses : public PbDataIO
{
public:
    EvaluatorStatus Evaluate(DataPtr gt_data) override
    {
        EvaluatorStatus eval_status;
        eval_status.SetEvalType("RelativePoses");
        
        // ... 执行评估计算 ...
        
        // 添加评估结果
        for (size_t i = 0; i < rotation_errors.size(); ++i)
        {
            eval_status.AddResult("rotation_error_deg", rotation_errors[i]);
            eval_status.AddResult("translation_error", translation_errors[i]);
            
            // 为每对视图添加备注信息
            std::string note = "view_pair(" + std::to_string(relative_poses_[i].i) + 
                              "," + std::to_string(relative_poses_[i].j) + ")";
            eval_status.SubmitNoteMsg(note);
            eval_status.SubmitNoteMsg(note);
        }
        
        // 或者方式1：统一备注 (用于整体实验说明)
        // eval_status.SubmitNoteMsg("baseline_experiment_with_synthetic_data");
        
        eval_status.is_successful = true;
        return eval_status;
    }
};
```

## 输出结果

在导出的CSV文件中，备注信息会出现在最后一列：

```csv
Algorithm,EvalCommit,Value,Note
method_LiRP,default_commit,1.234,"view_pair(0,1)"
method_LiRP,default_commit,0.056,"view_pair(0,1)"
method_LiRP,default_commit,2.345,"view_pair(1,2)"
method_LiRP,default_commit,0.078,"view_pair(1,2)"
```

## 注意事项

1. 备注信息不会影响原有的 `ProfileCommit` 配置，保持外部控制的灵活性
2. 备注信息作为额外的说明列出现在CSV导出中，便于分析和追溯
3. 建议使用有意义的备注信息，如视图对标识、实验条件等
4. 系统会自动检查并警告不合理的备注信息配置 