// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "data_global_poses.hpp"
#include "file_io.hpp"
#include <chrono>
#include <fstream>

using namespace std;
using namespace chrono;

namespace PoSDK
{

    //-------------- internal function -------------------

    bool DataGlobalPoses::SavePoseToFile(const std::string &path) const
    {
        std::ofstream output(path);
        if (!output.is_open())
        {
            std::cout << "Error: Cannot create output file: " << path << std::endl;
            return false;
        }

        const size_t num_poses = global_poses_.Size();
        output << num_poses << std::endl;

        for (size_t i = 0; i < num_poses; ++i)
        {
            const Matrix3d &R = global_poses_.rotations[i];
            const Vector3d &t = global_poses_.translations[i];

            // 写入旋转矩阵（按列优先顺序）
            for (int col = 0; col < 3; ++col)
            {
                for (int row = 0; row < 3; ++row)
                {
                    output << std::setprecision(16) << R(row, col) << " ";
                }
            }

            // 写入平移向量
            output << std::setprecision(16)
                   << t[0] << " " << t[1] << " " << t[2] << std::endl;
        }

        output.close();
        return true;
    }

    bool DataGlobalPoses::LoadPoseFromFile(const std::string &path)
    {
        std::ifstream input(path);
        if (!input.is_open())
        {
            std::cout << "Error: Cannot open pose file: " << path << std::endl;
            return false;
        }

        // 读取位姿数量
        size_t num_poses;
        input >> num_poses;

        // 调整容器大小
        global_poses_.rotations.resize(num_poses);
        global_poses_.translations.resize(num_poses);

        // 读取每个位姿
        for (size_t i = 0; i < num_poses; ++i)
        {
            Matrix3d &R = global_poses_.rotations[i];
            Vector3d &t = global_poses_.translations[i];

            // 读取旋转矩阵（按列优先顺序）
            for (int col = 0; col < 3; ++col)
            {
                for (int row = 0; row < 3; ++row)
                {
                    input >> R(row, col);
                }
            }

            // 读取平移向量
            input >> t[0] >> t[1] >> t[2];

            if (input.fail())
            {
                std::cout << "Error: Failed to read pose data at position " << i << std::endl;
                return false;
            }
        }

        input.close();
        return true;
    }

    bool DataGlobalPoses::Load(const std::string &path,
                               const std::string &file_type)
    {
        std::cout << "<DataGlobalPoses::Load> msg: load data from path = "
                  << path << std::endl;

        if (file_type == "pb")
        {
            std::cout << "Error: Unsupported file type: " << file_type << std::endl;
            return false;
        }
        else if (file_type == "rot")
        {
            return file::SetupOrientation(path.c_str(), global_poses_.rotations);
        }
        else if (file_type == "po")
        {
            return LoadPoseFromFile(path);
        }
        else if (file_type == "g2o")
        {
            return file::LoadFromG2O(path, global_poses_.rotations, global_poses_.translations);
        }

        std::cout << "Error: Unsupported file type: " << file_type << std::endl;
        return false;
    }

    bool DataGlobalPoses::Save(const std::string &path,
                               const std::string &filename,
                               const std::string &extension)
    {
        std::cout << "<DataGlobalPoses::Save> msg: save data to path = "
                  << path << ", filename = " << filename << std::endl;

        if (extension == ".pb")
        {
            // return PbDataIO::Save(path, filename, extension);
            std::cout << "Error: Unsupported file type: " << extension << std::endl;
            return false;
        }
        else if (extension == ".po")
        { // pose
            std::string full_path = path;
            if (!path.empty() && path.back() != '/')
            {
                full_path += '/';
            }
            full_path += filename;
            if (!filename.empty() && filename.find('.') == std::string::npos)
            {
                full_path += extension;
            }
            return SavePoseToFile(full_path);
        }
        else if (extension == ".g2o")
        { // 添加g2o格式支持
            std::string full_path = path;
            if (!path.empty() && path.back() != '/')
            {
                full_path += '/';
            }
            full_path += filename;
            if (!filename.empty() && filename.find('.') == std::string::npos)
            {
                full_path += extension;
            }
            return file::SaveToG2O(full_path, global_poses_);
        }

        std::cout << "Error: Unsupported file extension: " << extension << std::endl;
        return false;
    }

    //-------------- class member function ----------------------

    DataPtr DataGlobalPoses::CopyData() const
    {
        // 创建一个新的DataGlobalPoses实例
        auto cloned_data = std::make_shared<DataGlobalPoses>();

        // 获取新实例内部GlobalPoses的指针
        auto cloned_poses_ptr = static_cast<GlobalPoses *>(cloned_data->GetData());
        if (!cloned_poses_ptr)
        {
            std::cerr << "[DataGlobalPoses::CopyData] Error: Failed to get internal GlobalPoses pointer for clone." << std::endl;
            return nullptr;
        }

        // 执行GlobalPoses的深拷贝（依赖GlobalPoses的默认拷贝构造/赋值）
        *cloned_poses_ptr = global_poses_;

        return cloned_data;
    }

}
