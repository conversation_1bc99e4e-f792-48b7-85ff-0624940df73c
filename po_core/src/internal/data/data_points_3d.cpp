// This file is part of PoSDK, an Pose-only Multiple View Geometry C++ library.

// Copyright (c) 2021 Qi Cai.

// This Source Code Form is subject to the terms of the Mozilla Public
// License, v. 2.0. If a copy of the MPL was not distributed with this
// file, You can obtain one at http://mozilla.org/MPL/2.0/.

#include "data_points_3d.hpp"
#include <fstream>
#include <iomanip>
#include "../fileIO/file_io.hpp"

using namespace std;

namespace PoSDK
{

    //-------------- internal function -------------------

    bool DataPoints3D::SavePointsToFile(const std::string &path) const
    {
        std::ofstream output(path);
        if (!output.is_open())
        {
            std::cout << "Error: Cannot create output file: " << path << std::endl;
            return false;
        }

        if (world_point_info_.size() == 0)
        {
            std::cout << "Warning: No 3D points to save" << std::endl;
            return true;
        }

        const size_t num_points = world_point_info_.size();

        // 写入点数量
        output << num_points << std::endl;

        // 写入每个点的坐标和使用状态
        for (size_t i = 0; i < num_points; ++i)
        {
            Point3d point = world_point_info_.getPoint(i);
            bool is_used = world_point_info_.isUsed(i);

            output << std::setprecision(16)
                   << point(0) << " "
                   << point(1) << " "
                   << point(2) << " "
                   << (is_used ? 1 : 0) << std::endl;
        }

        output.close();
        return true;
    }

    bool DataPoints3D::LoadPointsFromFile(const std::string &path)
    {
        std::ifstream input(path);
        if (!input.is_open())
        {
            std::cout << "Error: Cannot open input file: " << path << std::endl;
            return false;
        }

        size_t num_points;
        if (!(input >> num_points))
        {
            std::cout << "Error: Cannot read number of points from file: " << path << std::endl;
            return false;
        }

        // 重置WorldPointInfo
        world_point_info_ = WorldPointInfo(num_points);

        // 读取每个点的坐标和使用状态
        for (size_t i = 0; i < num_points; ++i)
        {
            double x, y, z;
            int used_flag;

            if (!(input >> x >> y >> z >> used_flag))
            {
                std::cout << "Error: Cannot read point " << i << " from file: " << path << std::endl;
                return false;
            }

            Point3d point(x, y, z);
            world_point_info_.setPoint(i, point);
            world_point_info_.setUsed(i, used_flag != 0);
        }

        input.close();
        return true;
    }

    //-------------- public API -------------------

    bool DataPoints3D::Load(const std::string &filepath,
                            const std::string &file_type)
    {
        if (file_type == "txt")
        {
            return LoadPointsFromFile(filepath);
        }
        else if (file_type == "pb")
        {
            std::cout << "Warning: Protobuf format is not yet supported for DataPoints3D. "
                      << "Using text format as fallback." << std::endl;
            return LoadPointsFromFile(filepath);
        }
        else if (file_type == "ply")
        {
            // 使用通用的PLY加载函数
            Points3d points3d;
            if (!file::LoadPlyFile(filepath, points3d))
            {
                return false;
            }

            // 将Points3d转换为WorldPointInfo
            world_point_info_.resize(points3d.cols());
            for (int i = 0; i < points3d.cols(); ++i)
            {
                world_point_info_.setPoint(i, points3d.col(i));
                world_point_info_.setUsed(i, true); // 默认所有点都可用
            }

            // 尝试加载对应的.ids文件
            LoadIdsFile(filepath);

            return true;
        }

        std::cout << "Error: Unsupported file type: " << file_type << std::endl;
        return false;
    }

    bool DataPoints3D::Save(const std::string &folder,
                            const std::string &filename,
                            const std::string &extension)
    {
        // 构建完整的文件路径
        std::string full_path;
        if (!folder.empty())
        {
            full_path = folder + "/";
        }

        if (!filename.empty())
        {
            full_path += filename;
        }
        else
        {
            full_path += "points_3d"; // 默认文件名
        }

        full_path += extension;

        if (extension == ".txt")
        {
            return SavePointsToFile(full_path);
        }
        else if (extension == ".pb")
        {
            std::cout << "Warning: Protobuf format is not yet supported for DataPoints3D. "
                      << "Using text format as fallback." << std::endl;
            return SavePointsToFile(full_path);
        }
        else if (extension == ".ply")
        {
            // 将WorldPointInfo转换为Points3d
            Points3d points3d = world_point_info_.world_points;

            // 使用通用的PLY保存函数
            if (!file::WritePlyFile(full_path, points3d))
            {
                return false;
            }

            // 保存对应的.ids文件
            return SaveIdsFile(full_path);
        }

        std::cout << "Error: Unsupported file extension: " << extension << std::endl;
        return false;
    }

    //-------------- class member function ----------------------

    DataPtr DataPoints3D::CopyData() const
    {
        // 创建一个新的DataPoints3D实例
        auto cloned_data = std::make_shared<DataPoints3D>();

        // 获取新实例内部WorldPointInfo的指针
        auto cloned_world_point_info_ptr = static_cast<WorldPointInfo *>(cloned_data->GetData());
        if (!cloned_world_point_info_ptr)
        {
            std::cerr << "[DataPoints3D::CopyData] Error: Failed to get internal WorldPointInfo pointer for clone." << std::endl;
            return nullptr;
        }

        // 执行WorldPointInfo的深拷贝
        *cloned_world_point_info_ptr = world_point_info_;

        return cloned_data;
    }

    //-------------- IDS file operations -------------------

    bool DataPoints3D::SaveIdsFile(const std::string &ply_path) const
    {
        // 从PLY文件路径生成.ids文件路径
        std::string ids_path = ply_path;
        size_t dot_pos = ids_path.find_last_of('.');
        if (dot_pos != std::string::npos)
        {
            ids_path = ids_path.substr(0, dot_pos) + ".ids";
        }
        else
        {
            ids_path += ".ids";
        }

        std::ofstream ids_file(ids_path);
        if (!ids_file.is_open())
        {
            std::cout << "Warning: Cannot create .ids file: " << ids_path << std::endl;
            return false; // 不是致命错误，PLY文件已经保存
        }

        // 写入使用状态数据
        ids_file << world_point_info_.size() << std::endl;
        for (size_t i = 0; i < world_point_info_.size(); ++i)
        {
            ids_file << (world_point_info_.isUsed(i) ? 1 : 0) << std::endl;
        }

        ids_file.close();
        std::cout << "IDS file saved: " << ids_path << std::endl;
        return true;
    }

    bool DataPoints3D::LoadIdsFile(const std::string &ply_path)
    {
        // 从PLY文件路径生成.ids文件路径
        std::string ids_path = ply_path;
        size_t dot_pos = ids_path.find_last_of('.');
        if (dot_pos != std::string::npos)
        {
            ids_path = ids_path.substr(0, dot_pos) + ".ids";
        }
        else
        {
            ids_path += ".ids";
        }

        std::ifstream ids_file(ids_path);
        if (!ids_file.is_open())
        {
            std::cout << "Info: .ids file not found: " << ids_path
                      << " (using default: all points enabled)" << std::endl;
            return true; // 不是错误，使用默认值
        }

        size_t num_points;
        if (!(ids_file >> num_points))
        {
            std::cout << "Warning: Cannot read point count from .ids file: " << ids_path << std::endl;
            ids_file.close();
            return false;
        }

        // 检查点数量是否匹配
        if (num_points != world_point_info_.size())
        {
            std::cout << "Warning: Point count mismatch between PLY ("
                      << world_point_info_.size() << ") and IDS ("
                      << num_points << ") files" << std::endl;
            ids_file.close();
            return false;
        }

        // 读取使用状态
        for (size_t i = 0; i < num_points; ++i)
        {
            int used_flag;
            if (!(ids_file >> used_flag))
            {
                std::cout << "Warning: Cannot read usage flag for point " << i
                          << " from .ids file" << std::endl;
                ids_file.close();
                return false;
            }
            world_point_info_.setUsed(i, used_flag != 0);
        }

        ids_file.close();
        std::cout << "IDS file loaded: " << ids_path << std::endl;
        return true;
    }

}