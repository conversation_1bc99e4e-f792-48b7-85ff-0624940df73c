#ifndef _DATA_RELATIVE_ROTATIONS_
#define _DATA_RELATIVE_ROTATIONS_

#include <iostream>
#include "interfaces.hpp"
#include "types.hpp"
namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    class DataRelativeRotations : public DataIO
    {
    public:
        const std::string &GetType() const override
        {
            static const std::string &type = "data_relative_rotations";
            return type;
        }

        DataRelativeRotations()
        {
            // 其他初始化操作...
        }

        ~DataRelativeRotations()
        {
            relative_rotations_.clear();
        }

        virtual bool Save(const std::string &path = "",
                          const std::string &filename = "",
                          const std::string &extension = ".pb") override;
        virtual bool Load(const std::string &path,
                          const std::string &file_type = "pb") override;

        virtual void *GetData() override
        {
            return static_cast<void *>(&relative_rotations_);
        }

    private:
        RelativeRotations relative_rotations_;
    };

}
#endif
