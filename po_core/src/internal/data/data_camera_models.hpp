#ifndef _DATA_CAMERA_MODEL_HPP_
#define _DATA_CAMERA_MODEL_HPP_

#include "interfaces_preset.hpp"
#include "types.hpp"
#include <proto/camera_model.pb.h>
#include <filesystem>
#include <iostream>

namespace PoSDK
{

    using namespace Interface;
    using namespace types;

    class DataCameraModels : public PbDataIO
    {
    public:
        DataCameraModels()
        {
            // 设置默认存储路径
            auto exe_path = std::filesystem::current_path();
            auto tmp_store_folder = exe_path / "storage" / "camera_models";
            std::filesystem::create_directories(tmp_store_folder);
            SetStorageFolder(tmp_store_folder);
            // std::cout << "[DataCameraModels] Default camera models storage folder: "
            //           << tmp_store_folder << std::endl;
        }

        virtual ~DataCameraModels() = default;

        const std::string &GetType() const override
        {
            static const std::string type = "data_camera_models";
            return type;
        }

        virtual void *GetData() override
        {
            return static_cast<void *>(&camera_models_);
        }

    public:
        // 基类中的默认实现
        FUNC_INTERFACE_BEGIN
        CALL(SetCameraInfo, STR(), STR(), STR())
        FUNC_INTERFACE_END

        // 设置相机信息
        bool SetCameraInfo(const std::string &make,
                           const std::string &model,
                           const std::string &serial)
        {
            try
            {
                for (auto &camera : camera_models_)
                {
                    camera.camera_make = make;
                    camera.camera_model = model;
                    camera.serial_number = serial;
                }
                std::cout << "[DataCameraModels] Set camera info - Make: " << make
                          << ", Model: " << model << ", Serial: " << serial << std::endl;
                return true;
            }
            catch (const std::exception &e)
            {
                std::cerr << "[DataCameraModels] Error setting camera info: "
                          << e.what() << std::endl;
                return false;
            }
        }

        // 设置相机内参
        void SetSingleCameraIntrinsics(const CameraIntrinsics &intrinsics)
        {

            if (camera_models_.empty())
            {
                // 如果相机模型列表为空，添加一个新的相机模型
                camera_models_.resize(1);
            }
            else if (camera_models_.size() > 1)
            {
                // 如果有多个相机模型，清空并重置为单个模型
                camera_models_.clear();
                camera_models_.resize(1);
            }

            camera_models_.front().intrinsics.SetCameraIntrinsics(intrinsics);
            is_single_camera_ = true;
            std::cout << "[DataCameraModels] Set single camera intrinsics" << std::endl;
        }

    protected:
        std::unique_ptr<google::protobuf::Message> CreateProtoMessage() const override
        {
            return std::make_unique<pomvg::proto::CameraModelsData>();
        }

        std::unique_ptr<google::protobuf::Message> ToProto() const override
        {
            auto proto_msg = std::make_unique<pomvg::proto::CameraModelsData>();

            std::cout << "[DataCameraModels] Converting camera models to proto format" << std::endl;
            std::cout << "[DataCameraModels] Number of Intrinsics: " << camera_models_.size() << std::endl;

            for (const auto &camera : camera_models_)
            {
                auto *proto_camera = proto_msg->add_camera_models();

                // 基本参数
                PROTO_SET_BASIC(proto_camera, fx, camera.intrinsics.fx);
                PROTO_SET_BASIC(proto_camera, fy, camera.intrinsics.fy);
                PROTO_SET_BASIC(proto_camera, cx, camera.intrinsics.cx);
                PROTO_SET_BASIC(proto_camera, cy, camera.intrinsics.cy);
                PROTO_SET_BASIC(proto_camera, width, camera.intrinsics.width);
                PROTO_SET_BASIC(proto_camera, height, camera.intrinsics.height);

                // 相机类型
                PROTO_SET_ENUM(proto_camera, model_type, camera.intrinsics.model_type);
                PROTO_SET_ENUM(proto_camera, distortion_type, camera.intrinsics.distortion_type);

                // 畸变参数
                PROTO_SET_ARRAY(proto_camera, radial_distortion, camera.intrinsics.radial_distortion);
                PROTO_SET_ARRAY(proto_camera, tangential_distortion, camera.intrinsics.tangential_distortion);

                // 相机信息
                PROTO_SET_BASIC(proto_camera, camera_make, camera.camera_make);
                PROTO_SET_BASIC(proto_camera, camera_model, camera.camera_model);
                PROTO_SET_BASIC(proto_camera, serial_number, camera.serial_number);
            }

            return proto_msg;
        }

        bool FromProto(const google::protobuf::Message &message) override
        {
            const auto &proto_msg = dynamic_cast<const pomvg::proto::CameraModelsData &>(message);

            std::cout << "[DataCameraModels] Converting from proto format" << std::endl;
            std::cout << "[DataCameraModels] Number of Intrinsics in proto: "
                      << proto_msg.camera_models_size() << std::endl;

            camera_models_.clear();
            camera_models_.reserve(proto_msg.camera_models_size());

            for (const auto &proto_camera : proto_msg.camera_models())
            {
                CameraModel camera;

                // 基本参数
                PROTO_GET_BASIC(proto_camera, fx, camera.intrinsics.fx);
                PROTO_GET_BASIC(proto_camera, fy, camera.intrinsics.fy);
                PROTO_GET_BASIC(proto_camera, cx, camera.intrinsics.cx);
                PROTO_GET_BASIC(proto_camera, cy, camera.intrinsics.cy);
                PROTO_GET_BASIC(proto_camera, width, camera.intrinsics.width);
                PROTO_GET_BASIC(proto_camera, height, camera.intrinsics.height);

                // 相机类型
                PROTO_GET_ENUM(proto_camera, model_type, camera.intrinsics.model_type);
                PROTO_GET_ENUM(proto_camera, distortion_type, camera.intrinsics.distortion_type);

                // 畸变参数
                PROTO_GET_ARRAY(proto_camera, radial_distortion, camera.intrinsics.radial_distortion);
                PROTO_GET_ARRAY(proto_camera, tangential_distortion, camera.intrinsics.tangential_distortion);

                // 相机信息
                PROTO_GET_BASIC(proto_camera, camera_make, camera.camera_make);
                PROTO_GET_BASIC(proto_camera, camera_model, camera.camera_model);
                PROTO_GET_BASIC(proto_camera, serial_number, camera.serial_number);

                camera_models_.push_back(std::move(camera));
            }

            return true;
        }

    private:
        CameraModels camera_models_;
        bool is_single_camera_ = true;
    };

} // namespace PoSDK

#endif // _DATA_CAMERA_MODEL_HPP_
