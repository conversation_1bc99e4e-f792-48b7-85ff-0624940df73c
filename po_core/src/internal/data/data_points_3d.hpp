#ifndef _DATA_POINTS_3D_
#define _DATA_POINTS_3D_

#include <iostream>
#include "interfaces_preset.hpp"
#include "types.hpp"

namespace PoSDK
{
    using namespace Interface;
    using namespace types;

    /**
     * @brief 3D点云数据IO类
     * @details 负责WorldPointInfo数据的序列化和反序列化
     *
     * 支持的文件格式：
     * - .txt: 文本格式，包含点坐标和使用状态
     * - .ply: 标准PLY点云格式，配合.ids文件存储使用状态
     * - .pb: Protobuf格式 (计划支持，目前会回退到txt格式)
     *
     * PLY格式特点：
     * - 仅存储3D点坐标，使用状态存储在同名.ids文件中
     * - 读取时如果找不到.ids文件，默认所有点都可用
     * - 写入时自动生成对应的.ids文件
     */
    class DataPoints3D : public DataIO
    {
    public:
        /// @brief 获取数据类型
        /// @return 数据类型字符串
        const std::string &GetType() const override
        {
            static const std::string &type = "data_points_3d";
            return type;
        }

        /// @brief 默认构造函数
        DataPoints3D() = default;

        /// @brief 加载数据
        /// @param filepath 完整的文件路径
        /// @param file_type 文件类型：
        ///   - "txt": 文本格式 (完全支持)
        ///   - "ply": PLY点云格式 (完全支持，自动处理.ids文件)
        ///   - "pb": Protobuf格式 (暂不支持，会回退到txt格式并显示警告)
        /// @return 是否加载成功
        virtual bool Load(const std::string &filepath = "",
                          const std::string &file_type = "pb") override;

        /// @brief 保存数据
        /// @param folder 存储目录路径
        /// @param filename 文件名（不含扩展名）
        /// @param extension 文件扩展名：
        ///   - ".txt": 文本格式 (完全支持)
        ///   - ".ply": PLY点云格式 (完全支持，自动生成.ids文件)
        ///   - ".pb": Protobuf格式 (暂不支持，会回退到txt格式并显示警告)
        /// @return 是否保存成功
        virtual bool Save(const std::string &folder = "",
                          const std::string &filename = "",
                          const std::string &extension = ".pb") override;

        /// @brief 获取数据指针
        /// @return WorldPointInfo数据的指针
        virtual void *GetData() override
        {
            return static_cast<void *>(&world_point_info_);
        }

        /// @brief 创建数据的深拷贝
        /// @return 拷贝后的DataPtr
        DataPtr CopyData() const override;

    private:
        WorldPointInfo world_point_info_; ///< 存储WorldPointInfo对象

        /// @brief 保存点到文件
        /// @param path 文件路径
        /// @return 是否保存成功
        bool SavePointsToFile(const std::string &path) const;

        /// @brief 从文件加载点
        /// @param path 文件路径
        /// @return 是否加载成功
        bool LoadPointsFromFile(const std::string &path);

        /// @brief 保存使用状态到.ids文件
        /// @param ply_path PLY文件路径，会自动生成对应的.ids文件
        /// @return 是否保存成功
        bool SaveIdsFile(const std::string &ply_path) const;

        /// @brief 从.ids文件加载使用状态
        /// @param ply_path PLY文件路径，会自动查找对应的.ids文件
        /// @return 是否加载成功
        bool LoadIdsFile(const std::string &ply_path);
    };

} // namespace PoSDK

#endif