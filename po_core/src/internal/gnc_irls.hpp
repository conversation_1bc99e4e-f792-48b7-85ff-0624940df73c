/**
 * @file gnc_irls.hpp
 * @brief GNC-IRLS (Graduated Non-Convexity with Iteratively Reweighted Least Squares) robust estimator
 * @details Implements GNC-IRLS algorithm for robust model estimation with zero-copy design
 *
 * @copyright Copyright (c) 2024 PoSDK Project
 * Licensed under the Mozilla Public License Version 2.0
 */

#ifndef POMVG_INTERFACES_GNC_IRLS_ESTIMATOR_HPP_
#define POMVG_INTERFACES_GNC_IRLS_ESTIMATOR_HPP_

#include "interfaces_robust_estimator.hpp"
#include <Eigen/Dense>
#include <random>
#include <algorithm>
#include <limits>
#include <iomanip> // 添加用于格式化输出

namespace PoSDK
{
    namespace Interface
    {

        /**
         * @brief GNC-IRLS (Graduated Non-Convexity with Iteratively Reweighted Least Squares) estimator implementation
         * @details Provides a generic GNC-IRLS implementation with:
         *          - Zero-copy data handling
         *          - Graduated Non-Convexity weight update strategy
         *          - Iteratively Reweighted Least Squares optimization
         *          - Adaptive noise parameter estimation
         */
        template <typename TSample>
        class GNCIRLSEstimator : public RobustEstimator
        {
        public:
            GNCIRLSEstimator()
            {
                // 添加调试输出
#ifdef _DEBUG
                std::cout << "Initializing GNCIRLSEstimator..." << std::endl;
#endif

                // 加载通用配置
                InitializeDefaultConfigPath(); // 默认加载 [GetType()] 部分
            }

            virtual ~GNCIRLSEstimator() = default;

            // 使用静态局部变量返回类型
            const std::string &GetType() const override
            {
                static const std::string type = "gnc_irls_estimator";
                return type;
            }

            /**
             * @brief GNC-IRLS算法实现
             * @return 估计结果的数据指针
             */
            DataPtr Run() override
            {
                try
                {

                    DisplayConfigInfo();

                    // 1. 获取配置参数
                    const size_t max_iterations = GetOptionAsIndexT("max_iterations", 20);
                    const double noise_scale = GetOptionAsFloat("noise_scale", 3.0f);
                    const double convergence_threshold = GetOptionAsFloat("convergence_threshold", 1e-10f);
                    const double gamma = GetOptionAsFloat("gamma", 1.4f);
                    const bool use_majorization = GetOptionAsBool("use_majorization", true);
                    const bool use_superlinear = GetOptionAsBool("use_superlinear", true);
                    const int sigma_mode = GetOptionAsIndexT("sigma_mode", 0);
                    const double inlier_threshold = GetOptionAsDouble("inlier_threshold", -1);
                    const bool track_best_model = GetOptionAsBool("track_best_model", false);
                    const bool echo_on_costdiff = GetOptionAsBool("echo_on_costdiff", false);

                    // 2. 获取总样本数据指针
                    DataSamplePtr<TSample> sample_data_ptr = CastToSample<TSample>(GetPopulationPtr());

                    if (!sample_data_ptr)
                    {
                        std::cerr << "Sample data is not valid" << std::endl;
                        return nullptr;
                    }

                    const size_t num_samples = sample_data_ptr->size();
                    const size_t min_samples_required = 6; // GNC-IRLS至少需要6个样本

                    if (num_samples < min_samples_required)
                    {
                        std::cerr << "Insufficient samples for GNC-IRLS (need at least "
                                  << min_samples_required << ")" << std::endl;
                        return nullptr;
                    }

                    // 3. 初始化权重和相关变量
                    weights_ = Eigen::VectorXd::Ones(num_samples);
                    double pre_residuals = std::numeric_limits<double>::infinity();
                    double mu; // 在循环内首次迭代时初始化
                    DataPtr current_model = nullptr;

                    // 跟踪最佳模型相关变量
                    double best_score = -1e10;
                    DataPtr best_model = nullptr;
                    std::vector<size_t> best_inliers;
                    std::vector<size_t> tmp_inlier_ids;

                    // 4. GNC-IRLS主循环
                    for (size_t iter = 1; iter <= max_iterations; ++iter)
                    {
                        // 4.1 构建对角权重矩阵
                        Eigen::MatrixXd P = weights_.asDiagonal();

                        // 4.2 检查有效权重数量
                        size_t valid_weights_count = (weights_.array() > 0.1).count();
                        if (valid_weights_count < min_samples_required)
                        {
                            if (iter == 1)
                            {
                                // 首次迭代就没有足够的有效权重，可能存在问题
                                std::cerr << "GNC-IRLS: Insufficient valid samples at initialization" << std::endl;
                                return nullptr;
                            }
                            break; // 中断迭代，返回当前模型
                        }

                        // 4.3 估计模型
                        current_model = EstimateModel(sample_data_ptr);

                        if (!current_model)
                        {
                            if (iter == 1)
                            {
                                // 首次迭代无法估计模型
                                std::cerr << "GNC-IRLS: Failed to estimate initial model" << std::endl;
                                return nullptr;
                            }
                            break; // 无法估计模型，但已有之前的模型，中断迭代
                        }

                        // 4.4 评估模型并计算残差
                        auto [residuals, total_cost] = EvaluateModel(current_model, sample_data_ptr);
                        if (residuals.size() == 0)
                        {
                            // 无法评估模型
                            if (iter == 1)
                            {
                                return nullptr; // 首次迭代失败
                            }
                            break; // 使用上一次迭代的模型
                        }

                        // 4.5 更新缩放参数default_sigma
                        double default_sigma;
                        switch (sigma_mode)
                        {
                        case -1:
                            default_sigma = inlier_threshold;
                            break;
                        case 0:
                            default_sigma = ScaleParameter(residuals, static_cast<double>(iter) / max_iterations);
                            break;
                        case 1:
                            default_sigma = FastScaleParameter(residuals, static_cast<double>(iter) / max_iterations);
                            break;
                        case 2:
                            default_sigma = ScaleParameter(residuals);
                            break;
                        default:
                            default_sigma = ScaleParameter(residuals, static_cast<double>(iter) / max_iterations);
                        }

                        // 4.6 根据gnc_inlier_threshold计算最终sigma
                        double sigma;
                        if (sigma_mode != 0)
                        {
                            sigma = (default_sigma + inlier_threshold) / 2.0;
                        }

                        // 计算各种sigma统计值
                        gnc_max_residual_ = residuals.array().abs().maxCoeff();
                        gnc_mean_sigma_ = residuals.array().abs().mean();
                        gnc_med_sigma_ = ScaleParameter(residuals);

                        // 4.7 计算绝对残差和平方残差
                        Eigen::VectorXd abs_residuals = residuals.cwiseAbs();

                        Eigen::VectorXd squared_residual = abs_residuals.cwiseProduct(abs_residuals);

                        // 计算最大残差
                        double max_residual = squared_residual.maxCoeff();

                        // 4.8 计算噪声边界
                        double noise_bound = noise_scale * sigma;
                        double noise_bound_squared = noise_bound * noise_bound;

                        // 4.9 更新mu参数
                        if (iter == 1)
                        {
                            mu = std::max(1.0 / (5.0 * max_residual / noise_bound_squared - 1.0), 1e-4);
                        }

                        // 4.10 更新权重
                        if (use_majorization)
                        {
                            weights_ = M_gncWeightsUpdate(mu, squared_residual, noise_bound_squared);
                        }
                        else
                        {
                            weights_ = gncWeightsUpdate(mu, squared_residual, noise_bound_squared);
                        }

                        // 输出调试信息
                        if (log_level_ >= PO_LOG_NORMAL)
                        {
                            // 计算平均权重
                            double mean_weight = weights_.mean();
                            // 计算平均残差
                            double mean_residual = residuals.array().abs().mean();
                            // 计算中位残差（使用ScaleParameter方法的原理，中位数是sigma*0.6745）
                            double med_residual = find_median(residuals);

                            PO_LOG(PO_LOG_NONE) << "iter=" << iter
                                                << ", mean_weight=" << std::fixed << std::setprecision(6) << mean_weight
                                                << ", mean_residual=" << std::fixed << std::setprecision(6) << mean_residual
                                                << ", med_residual=" << std::fixed << std::setprecision(7) << med_residual
                                                << std::endl;

                            PO_LOG(PO_LOG_NONE) << "sigma=" << sigma << ", gnc_med_sigma_=" << gnc_med_sigma_ << ", gnc_mean_sigma_=" << gnc_mean_sigma_ << ", gnc_max_residual_=" << gnc_max_residual_ << std::endl;
                        }

                        // 计算当前迭代的indicator评分
                        // tmp_inlier_ids = weights > 0.5
                        tmp_inlier_ids.clear();
                        for (size_t i = 0; i < weights_.size(); ++i)
                        {
                            if (weights_(i) > 0.5)
                            {
                                tmp_inlier_ids.push_back(i);
                            }
                        }

                        // inlier_ratio = sum(tmp_inlier_ids) / length(tmp_inlier_ids)
                        double inlier_ratio = static_cast<double>(tmp_inlier_ids.size()) / static_cast<double>(weights_.size());

                        // indicator = 2 * min(inlier_ratio, 0.5) / max(sigma, 5*1e-4)
                        double indicator = 2.0 * std::min(inlier_ratio, 0.5) / std::max(gnc_med_sigma_, inlier_threshold);

                        // 输出indicator调试信息
                        if (log_level_ >= PO_LOG_NORMAL)
                        {
                            PO_LOG(PO_LOG_NONE) << "inlier_nums=" << std::fixed << std::setprecision(4) << tmp_inlier_ids.size()
                                                << ", sigma=" << std::fixed << std::setprecision(6) << sigma
                                                << std::endl;
                        }

                        // 跟踪最佳模型（如果启用跟踪功能）
                        if (track_best_model && indicator >= best_score)
                        {
                            best_score = indicator;
                            // 深拷贝当前模型
                            best_model = CloneModel(current_model);
                            best_inliers = tmp_inlier_ids;
                        }

                        // 4.11 计算迭代代价并检查收敛
                        double cost = weights_.dot(squared_residual);
                        double cost_diff = std::abs(cost - pre_residuals);

                        // 输出代价差异调试信息（如果启用）
                        if (echo_on_costdiff)
                        {
                            PO_LOG(PO_LOG_NONE) << "iter=" << iter
                                                << ", indicator=" << std::fixed << std::setprecision(2) << indicator
                                                << ", indicator_threshold=" << std::fixed << std::setprecision(2) << 1 / (2 * inlier_threshold)
                                                << ", cost=" << std::fixed << std::setprecision(8) << cost
                                                << ", cost_diff=" << std::fixed << std::setprecision(10) << cost_diff
                                                << ", threshold=" << std::fixed << std::setprecision(10) << convergence_threshold
                                                << std::endl;
                        }

                        // 检查收敛条件
                        if (cost_diff <= convergence_threshold)
                        {
                            break; // 收敛，停止迭代
                        }

                        if (indicator >= 1 / (1 * inlier_threshold))
                        {
                            PO_LOG(PO_LOG_NONE) << "GNC-IRLS收敛: indicator=" << std::fixed << std::setprecision(2) << indicator << " >= " << 1 / (1 * inlier_threshold) << std::endl;
                            break; // 收敛，停止迭代
                        }

                        // if (gnc_med_sigma_ <= gnc_mean_sigma_)
                        // {
                        //     break; // 收敛，停止迭代
                        // }

                        // 4.12 更新mu参数
                        if (use_superlinear)
                        {
                            if (mu < 1)
                            {
                                mu = std::min(std::sqrt(mu) * gamma, 1e16);
                            }
                            else
                            {
                                mu = std::min(mu * gamma, 1e16);
                            }
                        }
                        else
                        {
                            mu = std::min(mu * gamma, 1e16);
                        }

                        pre_residuals = cost;
                        iterations_performed_ = iter;
                    }

                    // 5. 根据track_best_model选项决定返回最优模型还是最后模型
                    DataPtr final_model;
                    if (track_best_model && best_model != nullptr)
                    {
                        final_model = best_model; // 返回之前保存的最佳模型
                    }
                    else
                    {
                        final_model = current_model; // 返回最后迭代的模型
                    }

                    // 6. 计算最终内点索引并设置到DataSample中
                    std::vector<size_t> final_inliers;
                    if (track_best_model && !best_inliers.empty())
                    {
                        // 如果启用跟踪且有最佳内点，使用之前保存的best_inliers
                        final_inliers = best_inliers;
                    }
                    else
                    {
                        // 否则根据最终模型重新计算内点
                        final_inliers = tmp_inlier_ids;
                    }

                    // 7. 检查最终内点数量
                    if (final_inliers.size() < min_samples_required)
                    {
                        final_inliers.clear();
                        return nullptr;
                    }

                    // 计算内点比例和GNC指示器
                    final_inlier_ratio_ = static_cast<double>(final_inliers.size()) / num_samples;
                    gnc_indicator_ = 2 * std::min(0.5, final_inlier_ratio_) /
                                     std::max(gnc_med_sigma_, inlier_threshold);

                    // 使用GNC指示器判断是否成功（如果启用质量验证）
                    const bool enable_quality_validation = GetOptionAsBool("enable_quality_validation", true);
                    if (enable_quality_validation)
                    {
                        double tmp_threshold = 1 / (1 * inlier_threshold);
                        // 输出最终调试信息
                        PO_LOG(PO_LOG_NONE) << "GNC-IRLS完成: 内点比例=" << std::fixed << std::setprecision(2)
                                            << (final_inlier_ratio_ * 100) << "%, "
                                            << "GNC指示器=" << std::fixed << std::setprecision(4) << gnc_indicator_
                                            << ", 阈值=" << std::fixed << std::setprecision(4) << tmp_threshold
                                            << std::endl;
                        if (gnc_indicator_ < tmp_threshold)
                        {
                            PO_LOG(PO_LOG_NORMAL) << "GNC-IRLS质量验证失败: GNC指示器=" << std::fixed << std::setprecision(4) << gnc_indicator_
                                                  << " < 阈值=" << std::fixed << std::setprecision(4) << tmp_threshold << std::endl;
                            return nullptr;
                        }
                    }

                    // 8. 将内点信息设置到DataSample中
                    auto inliers_ptr = std::make_shared<std::vector<size_t>>(final_inliers);
                    sample_data_ptr->SetBestInliers(inliers_ptr);

                    return final_model;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "GNC-IRLS estimation failed: " << e.what() << std::endl;
                    return nullptr;
                }
            }

        private:
            /**
             * @brief 使用当前权重估计模型
             * @param sample_data 样本数据
             * @return 估计的模型
             */
            DataPtr EstimateModel(const DataPtr &sample_data)
            {
                try
                {
                    if (!model_estimator_ptr_ || !sample_data)
                    {
                        return nullptr;
                    }

                    // 创建权重数据并直接使用SetPriorInfo设置权重
                    auto weights_data = CreateWeightsData(weights_);

                    // 使用SetPriorInfo的增强版，传递类型名称作为键
                    model_estimator_ptr_->SetPriorInfo(weights_data, "weights");

                    // 调用Build估计模型，只传递样本数据
                    return model_estimator_ptr_->Build(sample_data);
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Model estimation failed: " << e.what() << std::endl;
                    return nullptr;
                }
            }

            /**
             * @brief 创建权重数据对象（使用DataMap）
             * @param weights 权重向量
             * @return 包含权重的DataPtr
             */
            DataPtr CreateWeightsData(const Eigen::VectorXd &weights)
            {
                // 直接使用权重向量创建DataMap对象，调用DataMap(const TMap& map)构造函数
                return std::make_shared<DataMap<Eigen::VectorXd>>(weights);
            }

            /**
             * @brief 克隆模型对象（深拷贝）
             * @param model 需要克隆的模型
             * @return 克隆后的模型（新实例）
             */
            DataPtr CloneModel(const DataPtr &model)
            {
                if (!model)
                    return nullptr;

                // 判断是否是DataIO类型模型
                auto dataio_model = std::dynamic_pointer_cast<DataIO>(model);
                if (!dataio_model)
                    return nullptr;

                try
                {
                    // 获取模型的类型名，用于决定如何克隆
                    const std::string &type_name = dataio_model->GetType();

                    // 对于DataMap类型，尝试调用Clone方法进行深拷贝
                    if (type_name == "data_map")
                    {
                        try
                        {
                            // 直接使用原始model调用Clone
                            // 注意：这取决于模型的实际类型是否实现了Clone方法
                            // 在C++中，这可能依赖于RTTI(Run-Time Type Information)
                            return model->CopyData();
                        }
                        catch (const std::exception &e)
                        {
                            std::cerr << "Clone attempt failed: " << e.what() << std::endl;
                            // 失败时返回原模型
                            return model;
                        }
                    }

                    // 对于其他未知类型，暂时返回原始模型指针
                    std::cerr << "[GNCIRLSEstimator] Warning: No specific clone implementation for type '"
                              << type_name << "', returning original model." << std::endl;
                    return model;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[GNCIRLSEstimator] Clone failed: " << e.what() << std::endl;
                    return model; // 失败时返回原模型
                }
            }

            /**
             * @brief 评估当前模型并计算残差
             * @param model 当前模型
             * @param sample_data 样本数据
             * @return std::pair<残差向量, 总代价>
             */
            std::pair<Eigen::VectorXd, double> EvaluateModel(const DataPtr &model,
                                                             const DataPtr &sample_data)
            {
                try
                {
                    if (!cost_evaluator_ptr_ || !model || !sample_data)
                    {
                        return {Eigen::VectorXd(), std::numeric_limits<double>::max()};
                    }

                    // 使用DataPackage打包数据（注意，不传递weights以匹配GNC_IRLS中的逻辑）
                    auto package = std::make_shared<DataPackage>();
                    package->AddData(model);
                    package->AddData(sample_data);

                    // 计算代价 (理论上use_weights必须为false，否则要抛出异常)
                    const auto &cost_evaluator_options = cost_evaluator_ptr_->GetMethodOptions();
                    auto use_weights_it = cost_evaluator_options.find("use_weights");
                    if (use_weights_it != cost_evaluator_options.end() && use_weights_it->second == "true")
                    {
                        throw std::runtime_error("GNC-IRLS: use_weights must be false, please check the configuration of the gnc_irls's cost_evaluator");
                    }
                    auto costs = std::dynamic_pointer_cast<DataCosts>(cost_evaluator_ptr_->Build(package));
                    if (!costs)
                    {
                        return {Eigen::VectorXd(), std::numeric_limits<double>::max()};
                    }

                    // 转换为Eigen向量并计算总代价
                    const size_t n = costs->size();
                    Eigen::VectorXd residuals(n);
                    double total_cost = 0.0;

                    for (size_t i = 0; i < n; ++i)
                    {
                        residuals(i) = (*costs)[i];
                        total_cost += residuals(i);
                    }

                    return {residuals, total_cost};
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Model evaluation failed: " << e.what() << std::endl;
                    return {Eigen::VectorXd(), std::numeric_limits<double>::max()};
                }
            }

            /**
             * @brief 基于残差分布计算sigma（与GNCInfo::ScaleParameter一致）
             * @param residuals 残差向量
             * @param iter_ratio 当前迭代比例
             * @return 计算得到的sigma值
             */
            double ScaleParameter(const Eigen::VectorXd &residuals, double iter_ratio)
            {
                const int min_needed_obs = 12;
                if (iter_ratio > 0.5)
                {
                    iter_ratio = 0.5;
                }

                int num_obs = residuals.size();
                int used_num_obs = static_cast<int>(floor(iter_ratio * num_obs));
                if (used_num_obs < min_needed_obs)
                {
                    used_num_obs = min_needed_obs;
                }

                // Extract the smallest 'used_num_obs' residuals
                std::vector<double> sorted_residuals(residuals.data(), residuals.data() + residuals.size());
                std::sort(sorted_residuals.begin(), sorted_residuals.end());
                sorted_residuals.resize(used_num_obs);

                double median;
                if (used_num_obs % 2 == 0)
                {
                    median = (sorted_residuals[used_num_obs / 2 - 1] + sorted_residuals[used_num_obs / 2]) / 2.0;
                }
                else
                {
                    median = sorted_residuals[used_num_obs / 2];
                }
                double sigma = 1.4826 * median;
                return sigma;
            }

            /**
             * @brief 计算简单版本的sigma（与GNCInfo::ScaleParameter(residuals)一致）
             * @param residuals 残差向量
             * @return 计算得到的sigma值
             */
            double ScaleParameter(const Eigen::VectorXd &residuals)
            {
                int used_num_obs = residuals.size();

                // Extract the smallest 'used_num_obs' residuals
                std::vector<double> sorted_residuals(residuals.data(), residuals.data() + residuals.size());
                std::sort(sorted_residuals.begin(), sorted_residuals.end());
                sorted_residuals.resize(used_num_obs);

                double median;
                if (used_num_obs % 2 == 0)
                {
                    median = (sorted_residuals[used_num_obs / 2 - 1] + sorted_residuals[used_num_obs / 2]) / 2.0;
                }
                else
                {
                    median = sorted_residuals[used_num_obs / 2];
                }
                double sigma = 1.4826 * median;
                //    double sigma = residuals.mean();
                return sigma;
            }

            /**
             * @brief 快速计算sigma的方法（与GNCInfo::FastScaleParameter一致）
             * @param residuals 残差向量
             * @param iter_ratio 当前迭代比例
             * @return 计算得到的sigma值
             */
            double FastScaleParameter(const Eigen::VectorXd &residuals, double iter_ratio)
            {
                const int min_needed_obs = 12;
                if (iter_ratio > 0.5)
                {
                    iter_ratio = 0.5;
                }
                int num_obs = residuals.size();
                int used_num_obs = static_cast<int>(floor(iter_ratio * num_obs));
                if (used_num_obs < min_needed_obs)
                {
                    used_num_obs = min_needed_obs;
                }

                // Use nth_element to get the smallest 'used_num_obs' residuals
                std::vector<double> sorted_residuals(residuals.data(), residuals.data() + residuals.size());
                std::nth_element(sorted_residuals.begin(), sorted_residuals.begin() + used_num_obs, sorted_residuals.end());

                // Now, sort only the smallest 'used_num_obs' residuals
                std::sort(sorted_residuals.begin(), sorted_residuals.begin() + used_num_obs);

                // Compute the median of the smallest residuals
                double median;
                if (used_num_obs % 2 == 0)
                {
                    median = (sorted_residuals[used_num_obs / 2 - 1] + sorted_residuals[used_num_obs / 2]) / 2.0;
                }
                else
                {
                    median = sorted_residuals[used_num_obs / 2];
                }
                double sigma = 1.4826 * median;
                return sigma;
            }

            /**
             * @brief 更新权重（使用majorize优化）
             * @param weights 权重向量
             * @param mu GNC参数
             * @param residuals 平方残差
             * @param barc2 噪声边界的平方
             * @return 更新后的权重
             */
            Eigen::VectorXd M_gncWeightsUpdate(double mu, const Eigen::VectorXd &residuals, double barc2)
            {
                double ub = pow(mu + 1, 2) / pow(mu, 2) * barc2;
                double lb = barc2;
                Eigen::VectorXd weights = Eigen::VectorXd::Zero(residuals.size());
                for (int k = 0; k < residuals.size(); ++k)
                {
                    if (residuals(k) - ub >= 0)
                    {
                        weights(k) = 0;
                    }
                    else if (residuals(k) - lb <= 0)
                    {
                        weights(k) = 1;
                    }
                    else
                    {
                        weights(k) = sqrt(barc2 / residuals(k)) * (mu + 1) - mu;
                    }
                }
                return weights;
            }

            /**
             * @brief 更新权重（标准版本）
             * @param weights 权重向量
             * @param mu GNC参数
             * @param residuals 平方残差
             * @param barc2 噪声边界的平方
             * @return 更新后的权重
             */
            Eigen::VectorXd gncWeightsUpdate(double mu, const Eigen::VectorXd &residuals, double barc2)
            {
                Eigen::VectorXd weights = Eigen::VectorXd::Zero(residuals.size());
                double ub = (mu + 1) / mu * barc2;
                double lb = mu / (mu + 1) * barc2;

                for (int k = 0; k < residuals.size(); ++k)
                {
                    if (residuals(k) - ub >= 0)
                    {
                        weights(k) = 0;
                    }
                    else if (residuals(k) - lb <= 0)
                    {
                        weights(k) = 1;
                    }
                    else
                    {
                        weights(k) = sqrt(barc2 * mu * (mu + 1) / residuals(k)) - mu;
                    }
                }
                return weights;
            }

            // 存储当前权重和状态
            Eigen::VectorXd weights_;
            bool is_reweighted_ = true;

            // 存储性能和状态统计信息
            size_t iterations_performed_ = 0;
            double final_inlier_ratio_ = 0.0;
            double gnc_indicator_ = 0.0;
            double gnc_max_residual_ = 0.0;
            double gnc_mean_sigma_ = 0.0;
            double gnc_med_sigma_ = 0.0;
        };

    } // namespace Interface
} // namespace PoSDK

#endif // POMVG_INTERFACES_GNC_IRLS_ESTIMATOR_HPP_
