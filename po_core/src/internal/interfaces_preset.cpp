#include "interfaces_preset.hpp"
#include "factory/factory.hpp"
#include "executable_path.hpp"
#include <boost/algorithm/string.hpp> // 用于不区分大小写的字符串比较
#include <vector>
#include <sstream>
#include <algorithm>
#include <cctype>

namespace PoSDK
{
    namespace Interface
    {

        namespace
        {
            // Helper function to evaluate simple string expressions like "5*1e-3"
            template <typename T>
            T EvaluateStringExpression(const std::string &value)
            {
                std::string s = value;
                s.erase(s.begin(), std::find_if(s.begin(), s.end(), [](unsigned char ch)
                                                { return !std::isspace(ch); }));
                s.erase(std::find_if(s.rbegin(), s.rend(), [](unsigned char ch)
                                     { return !std::isspace(ch); })
                            .base(),
                        s.end());

                if (s.find('*') != std::string::npos)
                {
                    std::replace(s.begin(), s.end(), '*', ' ');
                    std::stringstream ss(s);
                    T result;
                    if (!(ss >> result))
                    {
                        throw std::invalid_argument("Invalid number format in expression: " + value);
                    }
                    T term;
                    while (ss >> term)
                    {
                        result *= term;
                    }

                    std::string remaining;
                    if (ss >> remaining)
                    {
                        throw std::invalid_argument("Invalid characters at the end of expression: " + value);
                    }
                    return result;
                }

                if constexpr (std::is_same_v<T, double>)
                {
                    return std::stod(value);
                }
                else
                {
                    return std::stof(value);
                }
            }
        } // namespace

        //=========================== BehaviorPrest Section ==================================
        // # Copyright (c) 2021 PO tools author: Qi Cai.
        //====================================================================================
        void BehaviorPreset::SetOptionsFromConfigFile(const std::string &path,
                                                      const std::string &file_type)
        {
            if (file_type == "ini")
            {
                config_tools_.Init(path);
            }

            std::cout << "<BehaviorPreset::SetOptionsFromConfigFile> msg: build method_optios_ from "
                      << path << std::endl;
            // the interface SetOptionsFromConfigFile need to be empty
        }

        DataPtr BehaviorPreset::Build(const DataPtr &material_ptr)
        {
            std::cout << "<BehaviorPreset::Build> msg: start building!" << std::endl;
            std::cout << "--<BehaviorPreset::Build> msg: construct method factory" << std::endl;

            // step.1 call Method factory
            FactoryMethod factory;
            std::cout << "PoSDK Test: factory plugin size = " << FactoryMethod::map_plugin_.size() << std::endl;
            std::cout << "<BehaviorPreset::Build> msg: build series of method according to sequential_methods_" << std::endl;

            // step.2 create a DataPackage to record and pack already exist data
            DataPtr data_package_ptr = std::make_shared<DataPackage>(material_ptr);

            // step.3 start running methods in order
            for (unsigned int i = 0; i < sequential_methods_.size(); ++i)
            {
                const MethodType &used_method_type = sequential_methods_[i];
                std::cout << "================= " << used_method_type << " ====================" << std::endl;
                Interface::MethodPtr method_ptr = FactoryMethod::Create(used_method_type);
                std::cout << "method_ptr.get=" << method_ptr.get() << std::endl;
                MethodPresetPtr methodpreset_ptr = std::dynamic_pointer_cast<MethodPreset>(method_ptr);

                // step.3.1 configure method options
                MethodsConfig::iterator iterator = config_tools_.configs_.find(used_method_type);
                if (iterator != config_tools_.configs_.end())
                {
                    methodpreset_ptr->SetMethodOptions(iterator->second);
                }

                // step.3.2 build product data by method
                Interface::DataPtr tmp_data_ptr = method_ptr->Build(data_package_ptr);

                // step.3.3 check output pointer
                if (tmp_data_ptr)
                {
                    std::dynamic_pointer_cast<DataPackage>(data_package_ptr)->AddData(tmp_data_ptr);
                }
            }

            const Package &package = std::dynamic_pointer_cast<DataPackage>(data_package_ptr)->GetPackage();
            const auto &iter_product = package.find(GetProductType());

            Interface::DataPtr product_ptr = nullptr;
            if (iter_product != package.end())
                product_ptr = iter_product->second;
            else
                std::cout << "<BehaviorPrest::Build> msg: product_type = "
                          << GetProductType() << std::endl;
            if (product_ptr)
                std::cout << "================= " << "behavior successfully finished!" << "================= " << std::endl;
            else
                std::cout << "================= " << "behavior unsuccessfully finished!" << "================= " << std::endl;

            return product_ptr;
        }

//=========================== MethodPrest Section ==================================
// # Copyright (c) 2021 PO tools author: Qi Cai.
//==================================================================================
#define CHECK_PACKAGE(data_ptr)                                                                                               \
    if (data_ptr->GetType() == "data_package")                                                                                \
    {                                                                                                                         \
        const DataPackagePtr &package_ptr = std::dynamic_pointer_cast<DataPackage>(data_ptr);                                 \
        const Package &package_info_ptr = *(Package *)package_ptr->GetData();                                                 \
        for (auto &iter : required_package_)                                                                                  \
        {                                                                                                                     \
            const std::string &required_type = iter.first;                                                                    \
            const auto &iter_find = package_info_ptr.find(required_type);                                                     \
            if (iter_find == package_info_ptr.end())                                                                          \
            {                                                                                                                 \
                std::cout << "##warning " << data_ptr->GetType() << " vs " << required_type << std::endl;                     \
                std::cerr << "##warning <CheckDataTypes> msg: cannot find required input data in data package!" << std::endl; \
                return false;                                                                                                 \
            }                                                                                                                 \
            else                                                                                                              \
            {                                                                                                                 \
                iter.second = iter_find->second;                                                                              \
            }                                                                                                                 \
        }                                                                                                                     \
        return true;                                                                                                          \
    }

        inline bool MethodPreset::CheckPackage(const DataPtr &data_ptr)
        {
            // 检查是否需要输入数据
            if (required_package_.empty())
            {
                std::cout << "\033[33m警告：该方法不需要任何输入数据\033[0m" << std::endl;
                return true;
            }

            // 检查输入是否为数据包
            if (data_ptr->GetType() == "data_package")
            {
                const DataPackagePtr &package_ptr = std::dynamic_pointer_cast<DataPackage>(data_ptr);
                const Package &package_info_ptr = *(Package *)package_ptr->GetData();

                // 遍历required_package_，检查数据包中是否包含所需数据
                for (auto &iter : required_package_)
                {
                    const std::string &required_type = iter.first;
                    const auto &iter_find = package_info_ptr.find(required_type);

                    if (iter_find == package_info_ptr.end())
                    {
                        // 数据包中缺少所需数据，发出带颜色的警告
                        std::cout << "\033[33m警告：未在数据包中找到 " << required_type
                                  << "，将使用默认值或忽略此输入\033[0m" << std::endl;
                        continue;
                    }
                    else
                    {
                        iter.second = iter_find->second;
                    }
                }
                return true;
            }
            else
            {
                // 输入不是数据包，而是单个数据对象
                // 尝试将其匹配到required_package_中的某个键
                bool found_match = false;
                std::string data_type = data_ptr->GetType();

                for (auto &iter : required_package_)
                {
                    const std::string &required_type = iter.first;

                    if (required_type == data_type)
                    {
                        // 找到匹配的键，将数据赋值给它
                        iter.second = data_ptr;
                        found_match = true;

                        // 如果方法需要多个输入，发出警告
                        if (required_package_.size() > 1)
                        {
                            std::cout << "\033[33m警告：该方法需要完整的数据包(data_package)，"
                                      << "但只提供了部分数据：" << data_type
                                      << "。其他必需数据将使用默认值或被忽略\033[0m" << std::endl;
                        }
                        break;
                    }
                }

                if (!found_match)
                {
                    // 输入的数据类型与所需的任何类型都不匹配
                    std::cerr << "\033[31m错误：输入数据类型 " << data_type
                              << " 与方法所需的任何数据类型都不匹配\033[0m" << std::endl;
                    return false;
                }

                return true;
            }
        }

        void MethodPreset::SetCheckStatus(const bool &status)
        {
            has_checked_input = status;
        }

        void MethodPreset::SetMethodOptions(const MethodOptions &options)
        {
            // step.1: 创建临时配置存储
            MethodOptions configfile_options;

            // 保存当前的method_options_
            MethodOptions current_options = GetMethodOptions();

            // 清空当前的method_options_，准备加载基础配置
            method_options_.clear();

            // 只有在config_path_非空且文件存在时才加载配置
            if (!config_path_.empty() && std::filesystem::exists(config_path_))
            {
                LoadMethodOptions(config_path_);
                configfile_options = GetMethodOptions();

                // 遍历提供的options，预加载可能的子配置
                for (const auto &[param, value] : options)
                {
                    if (!value.empty() && value.find('/') == std::string::npos)
                    { // 不像是文件路径
                        LoadMethodOptions(config_path_, value);
                        // 将加载的子配置合并到configfile_options（低优先级）
                        for (const auto &[key, val] : GetMethodOptions())
                        {
                            if (configfile_options.find(key) == configfile_options.end())
                            {
                                configfile_options[key] = val;
                            }
                        }
                    }
                }
            }
            else
            {
                // 如果没有配置文件，直接使用空的configfile_options
                std::cout << "No configuration file specified or file not found. Using default options." << std::endl;
            }

            // step.2: 按优先级合并配置
            MethodOptions merged_options;

            // 1. 最低优先级：configfile_options
            merged_options = configfile_options;

            // 2. 中等优先级：之前保存的method_options_
            for (const auto &[key, value] : current_options)
            {
                merged_options[key] = value;
            }

            // 3. 最高优先级：提供的options - 支持"section_name|key"格式
            for (const auto &[key, value] : options)
            {
                // 检查是否使用"section_name|key"格式
                size_t pipe_pos = key.find('|');
                if (pipe_pos != std::string::npos)
                {
                    // 解析"section_name|key"格式
                    std::string section_name = key.substr(0, pipe_pos);
                    std::string param_key = key.substr(pipe_pos + 1);

                    if (section_name == GetType())
                    {
                        // 如果section_name匹配当前方法类型，设置到主方法配置
                        merged_options[param_key] = value;
                    }
                    else
                    {
                        // 如果section_name是其他方法类型，设置到specific_methods_config_
                        specific_methods_config_[section_name][param_key] = value;
                        // 注意：不再存储到merged_options，避免污染主方法配置
                    }
                }
                else
                {
                    // 普通格式，直接合并
                    merged_options[key] = value;
                }
            }

            // step.3: 更新当前的method_options_
            method_options_ = merged_options;
        }

        void MethodPreset::SetMethodOption(const MethodParams &option_type,
                                           const ParamsValue &content)
        {
            method_options_[option_type] = content;
        }

        bool MethodPreset::CheckInput(const DataPtr &data_ptr)
        {
            // 如果传入的data_ptr不为空，优先处理传入的数据
            if (data_ptr)
            {
                // 新的数据传入，清除之前的检查状态
                has_checked_input = false;

                // 处理新传入的数据
                CheckDataTypes(data_ptr);
                CheckPath();
            }
            // 如果之前已检查过，直接返回true
            else if (has_checked_input)
            {
                return true;
            }

            // 如果有提供数据或已经检查过，标记为已检查并返回true
            has_checked_input = true;

            // 输出缺失数据的警告信息（不再视为错误）
            for (const auto &iter : required_package_)
            {
                if (iter.second == nullptr)
                {
                    std::cout << "##warning## 未提供数据: " << iter.first
                              << "，将使用默认值或忽略此输入" << std::endl;
                }
            }

            return true;
        }

        void MethodPreset::CheckDataTypes(const DataPtr &data_ptr)
        {
            // std::cout<<"    >> checking input data type for method..."<<std::endl;
            switch (required_package_.size())
            {
            case 0:
                std::cout << "<" << GetType() << "> 信息：此方法没有定义需要的数据类型" << std::endl;
                break;
            case 1:
                if (!CheckPackage(data_ptr))
                {
                    const std::string &required_type = required_package_.begin()->first;
                    if (data_ptr->GetType() == required_type)
                    {
                        required_package_[required_type] = data_ptr;
                    }
                    else
                    {
                        // 不再显示警告，因为现在允许数据类型不匹配
                        std::cout << "信息：输入数据类型 " << data_ptr->GetType()
                                  << " 与预期类型 " << required_type << " 不匹配，将使用默认值或忽略" << std::endl;
                    }
                }
                break;
            default:
                CheckPackage(data_ptr);
                break;
            }
        }

        void MethodPreset::CheckPath()
        {
            for (auto &iter : required_package_)
            {
                // check if there still exist nullptr data_ptr in required_package_
                if (iter.second == nullptr)
                {
                    //  try to fill nullptr dataptr from the user supported path in configure file
                    const auto &iter_option = method_options_.find(iter.first);
                    if (iter_option != method_options_.end())
                    {
                        // create Data
                        iter.second = FactoryData::Create(iter.first);
                        iter.second->Load(iter_option->second);
                    }
                    else
                    {
                        // 更新为与其他函数一致的消息风格
                        std::cout << "信息：未能从配置文件路径找到数据类型 "
                                  << iter.first << "，将使用默认值或忽略" << std::endl;
                        continue;
                    }
                }
            }
        }

        DataPtr MethodPreset::Build(const DataPtr &material_ptr)
        {
            // 从配置中获取日志级别设置
            log_level_ = GetOptionAsIndexT("log_level", PO_LOG_NORMAL);

            if (CheckInput(material_ptr))
            {
                return Run();
            }
            else
            {
                std::cerr << "##error## <MethodPreset> msg: cannot find inputs, please check! " << std::endl;
                return nullptr;
            }
        }

        // 添加类型转换函数的实现
        IndexT MethodPreset::String2IndexT(const std::string &value, IndexT default_value) const
        {
            try
            {
                if (!value.empty())
                {
                    return static_cast<IndexT>(std::stoul(value));
                }
            }
            catch (const std::exception &e)
            {
                std::cerr << "[" << GetType() << "] Warning: Failed to convert '"
                          << value << "' to IndexT, using default value" << std::endl;
            }
            return default_value;
        }

        float MethodPreset::String2Float(const std::string &value, float default_value) const
        {
            try
            {
                if (!value.empty())
                {
                    return EvaluateStringExpression<float>(value);
                }
            }
            catch (const std::exception &e)
            {
                std::cerr << "[" << GetType() << "] Warning: Failed to convert '"
                          << value << "' to float, using default value" << std::endl;
            }
            return default_value;
        }

        double MethodPreset::String2Double(const std::string &value, double default_value) const
        {
            try
            {
                if (!value.empty())
                {
                    return EvaluateStringExpression<double>(value);
                }
            }
            catch (const std::exception &e)
            {
                std::cerr << "[" << GetType() << "] Warning: Failed to convert '"
                          << value << "' to double, using default value" << std::endl;
            }
            return default_value;
        }

        bool MethodPreset::String2Bool(const std::string &str, bool default_value) const
        {
            // 使用 boost::iequals 进行不区分大小写的比较

            // 检查常见的 true 值
            if (boost::iequals(str, "true") || boost::iequals(str, "on") || boost::iequals(str, "yes") ||
                boost::iequals(str, "1") || boost::iequals(str, "t") || boost::iequals(str, "y"))
            {
                return true;
            }

            // 检查常见的 false 值
            if (boost::iequals(str, "false") || boost::iequals(str, "off") || boost::iequals(str, "no") ||
                boost::iequals(str, "0") || boost::iequals(str, "f") || boost::iequals(str, "n"))
            {
                return false;
            }

            // 如果不是已知的值，输出警告并返回默认值
            std::cerr << "[" << GetType() << "] Warning: Failed to convert '"
                      << str << "' to bool, using default value" << std::endl;
            return default_value;
        }

        // 添加配置选项获取函数的实现
        IndexT MethodPreset::GetOptionAsIndexT(const MethodParams &key, IndexT default_value) const
        {
            auto it = method_options_.find(key);
            if (it != method_options_.end())
            {
                return String2IndexT(it->second, default_value);
            }
            return default_value;
        }

        float MethodPreset::GetOptionAsFloat(const MethodParams &key, float default_value) const
        {
            auto it = method_options_.find(key);
            if (it != method_options_.end())
            {
                return String2Float(it->second, default_value);
            }
            return default_value;
        }

        double MethodPreset::GetOptionAsDouble(const MethodParams &key, double default_value) const
        {
            auto it = method_options_.find(key);
            if (it != method_options_.end())
            {
                return String2Double(it->second, default_value);
            }
            return default_value;
        }

        bool MethodPreset::GetOptionAsBool(const MethodParams &key, bool default_value) const
        {
            auto it = method_options_.find(key);
            if (it != method_options_.end())
            {
                return String2Bool(it->second, default_value);
            }
            return default_value;
        }

        std::string MethodPreset::GetOptionAsString(const MethodParams &key, const std::string &default_value) const
        {
            auto it = method_options_.find(key);
            if (it != method_options_.end())
            {
                return it->second;
            }
            return default_value;
        }

        void MethodPreset::PassingMethodOptions(MethodPreset &target_method)
        {
            const std::string &target_method_type = target_method.GetType();

            // 创建合并后的配置选项
            MethodOptions merged_options;

            // 1. 最低优先级：从ini配置文件中加载特定方法配置
            if (!config_path_.empty() && std::filesystem::exists(config_path_))
            {
                // 临时保存当前的specific_methods_config_
                auto current_specific_config = specific_methods_config_;

                // 从配置文件加载特定方法的配置
                LoadMethodOptions(config_path_, target_method_type);

                // 检查是否成功从配置文件加载了特定方法的配置
                auto loaded_it = specific_methods_config_.find(target_method_type);
                if (loaded_it != specific_methods_config_.end())
                {
                    // 将从配置文件加载的配置作为基础配置（最低优先级）
                    merged_options = loaded_it->second;
                }

                // 恢复原来的specific_methods_config_
                specific_methods_config_ = current_specific_config;
            }

            // 2. 中等优先级：本身已存在的specific_methods_config_覆盖ini文件中的配置
            auto cached_it = specific_methods_config_.find(target_method_type);
            if (cached_it != specific_methods_config_.end())
            {
                // 用本身的specific_methods_config_覆盖从配置文件加载的配置
                for (const auto &[key, value] : cached_it->second)
                {
                    merged_options[key] = value;
                }
            }

            // 3. 最后传递合并后的配置参数
            if (!merged_options.empty())
            {
                target_method.SetMethodOptions(merged_options);
            }
            else
            {
                // 如果都没有找到配置，输出警告
                if (log_level_ >= PO_LOG_NORMAL)
                {
                    PO_LOG_WARNING << "[" << GetType() << "] 未找到 " << target_method_type
                                   << " 的配置参数，目标方法将使用默认配置" << std::endl;
                }
            }
        }

        void MethodPreset::PassingMethodOptions(MethodPresetPtr target_method_ptr)
        {
            if (target_method_ptr)
            {
                PassingMethodOptions(*target_method_ptr);
            }
            else
            {
                PO_LOG_ERR << "[" << GetType() << "] PassingMethodOptions: 目标方法指针为空" << std::endl;
            }
        }

        const MethodOptions &MethodPreset::GetSpecificMethodConfig(const std::string &method_type) const
        {
            static const MethodOptions empty_options;

            auto it = specific_methods_config_.find(method_type);
            if (it != specific_methods_config_.end())
            {
                return it->second;
            }
            return empty_options;
        }

        std::string MethodPreset::GetOptionAsPath(const MethodParams &key, const std::string &root_dir, const std::string &default_value) const
        {
            auto it = method_options_.find(key);
            std::string raw_path = (it != method_options_.end()) ? it->second : default_value;

            if (raw_path.empty())
            {
                return raw_path;
            }

            // 处理占位符替换
            std::string result_path = raw_path;

            // 首先处理通用的{key_name}占位符引用（排除特定占位符{root_dir}和{exe_dir}）
            size_t pos = 0;
            while ((pos = result_path.find('{', pos)) != std::string::npos)
            {
                size_t end_pos = result_path.find('}', pos);
                if (end_pos == std::string::npos)
                {
                    // 没有找到配对的}，跳过这个{
                    pos++;
                    continue;
                }

                std::string placeholder_content = result_path.substr(pos + 1, end_pos - pos - 1);

                // 跳过特定占位符，这些稍后单独处理
                if (placeholder_content == "root_dir" || placeholder_content == "exe_dir")
                {
                    pos = end_pos + 1;
                    continue;
                }

                // 查找对应的method_options_值
                auto ref_it = method_options_.find(placeholder_content);
                std::string replacement_value;

                if (ref_it != method_options_.end())
                {
                    replacement_value = ref_it->second;
                }
                else
                {
                    // 找不到对应的键值，输出警告并使用空字符串
                    PO_LOG_WARNING << "路径 '" << raw_path << "' 中的占位符 {"
                                   << placeholder_content << "} 未在method_options_中找到对应值，将替换为空字符串" << std::endl;
                    replacement_value = "";
                }

                // 替换占位符
                std::string full_placeholder = "{" + placeholder_content + "}";
                result_path.replace(pos, full_placeholder.length(), replacement_value);

                // 更新位置，继续查找下一个占位符
                pos += replacement_value.length();
            }

            // 替换 {root_dir}
            size_t root_pos = result_path.find("{root_dir}");
            if (root_pos != std::string::npos) // 如果root_dir不为空，则使用传入的root_dir
            {
                std::string actual_root_dir;

                // 优先级1: 如果传入的root_dir非空，使用传入的值
                if (!root_dir.empty())
                {
                    actual_root_dir = root_dir;
                }
                // 优先级2: 如果传入的root_dir为空，从method_options_中获取
                else
                {
                    auto root_it = method_options_.find("root_dir");
                    if (root_it != method_options_.end())
                    {
                        actual_root_dir = root_it->second;
                    }
                }

                // 如果最终actual_root_dir仍为空，输出警告
                if (actual_root_dir.empty())
                {
                    PO_LOG_WARNING << "路径 '" << raw_path << "' 使用了{root_dir}占位符，但未提供root_dir参数且配置中无root_dir设置" << std::endl;
                    return raw_path;
                }

                result_path.replace(root_pos, std::string("{root_dir}").length(), actual_root_dir);
            }

            // 替换 {exe_dir}
            size_t exe_pos = result_path.find("{exe_dir}");
            if (exe_pos != std::string::npos)
            {
                // 使用ExecutablePath工具类获取可执行文件目录路径
                std::string exe_dir = ExecutablePath::GetExecutableDirectory();
                if (exe_dir.empty())
                {
                    std::cerr << "[" << GetType() << "] Warning: Failed to get executable directory" << std::endl;
                    return raw_path;
                }

                result_path.replace(exe_pos, std::string("{exe_dir}").length(), exe_dir);
            }

            // 如果没有占位符，直接返回原路径（认为是绝对路径）

            // 规范化路径
            try
            {
                std::filesystem::path normalized = std::filesystem::path(result_path).lexically_normal();
                return normalized.string();
            }
            catch (const std::exception &e)
            {
                std::cerr << "[" << GetType() << "] Warning: Failed to normalize path '" << result_path << "': " << e.what() << std::endl;
                return result_path;
            }
        }

    }
}
