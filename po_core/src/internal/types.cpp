#include <algorithm>
#include <string>
#include <iostream>
#include "types.hpp"
#include <ceres/ceres.h>
#include <ceres/rotation.h>
#include <algorithm> // Required for std::max, std::clamp
#include <cmath>     // Required for std::abs, std::acos, M_PI

namespace PoSDK
{
    namespace types
    {

        using std::string;

        // class definition section
        std::string EnvVars::gpath_ini_bal;
        std::string EnvVars::gpath_opt_bal;

        void EnvVars::InitEnvVars()
        {
            gpath_ini_bal = "ini_bal.txt";
            gpath_opt_bal = "opt_bal.txt";
        }

        // 删除ObsInfo方法实现 - 已在头文件内联

        // 删除Track方法实现 - 已在头文件内联

        // 删除TrackInfo方法实现 - 已在头文件内联

        // -----------------------------------------------------------------------------
        // RelativePose methods
        // -----------------------------------------------------------------------------
        Matrix3d RelativePose::GetEssentialMatrix() const
        {
            // E = [t]_x * R
            Matrix3d t_cross;
            t_cross << 0, -tij(2), tij(1),
                tij(2), 0, -tij(0),
                -tij(1), tij(0), 0;
            return t_cross * Rij;
        }

        // 计算相对位姿的误差
        bool RelativePose::ComputeErrors(const RelativePose &other,
                                         double &rotation_error,
                                         double &translation_error,
                                         bool is_opengv_format) const
        {
            // 确保视图ID匹配
            if (i != other.i || j != other.j)
            {
                return false;
            }

            // 计算旋转误差（以角度表示）
            // 使用更高精度的数值计算方法
            Matrix3d R_error = Rij * other.Rij.transpose();

            // 提升数值精度：使用更稳定的角度计算方法
            // 方法1：通过轴角表示计算旋转角度（更稳定）
            Eigen::AngleAxisd angle_axis(R_error);
            rotation_error = std::abs(angle_axis.angle()) * 180.0 / M_PI;

            // 备选方法：如果需要更高精度，可以使用以下方法
            // double trace_val = R_error.trace();
            // double cos_theta = std::max(-1.0, std::min(1.0, (trace_val - 1.0) / 2.0));
            // rotation_error = std::acos(cos_theta) * 180.0 / M_PI;

            // 计算平移方向误差（提升精度）
            Vector3d this_t_normalized = tij.normalized();
            Vector3d other_t_normalized = other.tij.normalized();

            // 使用更稳定的角度计算方法
            double dot_product = this_t_normalized.dot(other_t_normalized);

            // 提升数值精度：处理浮点数精度问题
            dot_product = std::max(-1.0, std::min(1.0, dot_product));

            // 使用更高精度的角度计算
            if (std::abs(dot_product - 1.0) < 1e-15)
            {
                // 当两个向量几乎相同时，直接设为0度
                translation_error = 0.0;
            }
            else if (std::abs(dot_product + 1.0) < 1e-15)
            {
                // 当两个向量相反时，设为180度
                translation_error = 180.0;
            }
            else
            {
                translation_error = std::acos(dot_product) * 180.0 / M_PI;
            }

            return true;
        }

        // -----------------------------------------------------------------------------
        // RelativePoses methods
        // -----------------------------------------------------------------------------
        std::size_t RelativePoses::EvaluateAgainst(
            const RelativePoses &gt_poses,
            std::vector<double> &rotation_errors,
            std::vector<double> &translation_errors) const
        {
            if (gt_poses.empty() || this->empty())
            {
                return 0;
            }

            // 注意：相对位姿没有格式差异问题，所以这里不需要像GlobalPoses那样进行格式转换

            // 创建视图对映射，用于快速查找
            std::map<std::pair<IndexT, IndexT>, std::size_t> gt_pose_map;
            for (std::size_t i = 0; i < gt_poses.size(); ++i)
            {
                const auto &pose = gt_poses[i];
                gt_pose_map[{pose.i, pose.j}] = i;
            }

            // 收集匹配的位姿对
            rotation_errors.clear();
            translation_errors.clear();

            for (const auto &est_pose : *this)
            {
                auto key = std::make_pair(est_pose.i, est_pose.j);
                if (gt_pose_map.find(key) != gt_pose_map.end())
                {
                    const auto &gt_pose = gt_poses[gt_pose_map[key]];
                    double rot_error, trans_error;
                    if (est_pose.ComputeErrors(gt_pose, rot_error, trans_error))
                    {
                        rotation_errors.push_back(rot_error);
                        translation_errors.push_back(trans_error);
                    }
                }
            }

            return rotation_errors.size();
        }

        bool RelativePoses::GetRelativePose(const ViewPair &view_pair, Matrix3d &R, Vector3d &t) const
        {
            IndexT i = view_pair.first;
            IndexT j = view_pair.second;

            // 遍历所有相对位姿，查找匹配的视图对
            for (const auto &pose : *this)
            {
                if (pose.i == i && pose.j == j)
                {
                    // 直接匹配 (i,j)，返回原始R和t
                    R = pose.Rij;
                    t = pose.tij;
                    return true;
                }
                else if (pose.i == j && pose.j == i)
                {
                    // 反向匹配 (j,i)，返回逆变换
                    // 如果存储的是 xj = R * xi + t，那么逆变换是 xi = R^T * xj - R^T * t
                    R = pose.Rij.transpose();
                    t = -pose.Rij.transpose() * pose.tij;
                    return true;
                }
            }

            // 没有找到匹配的位姿
            return false;
        }

        // -----------------------------------------------------------------------------
        // 全局位姿与相对位姿转换函数
        // -----------------------------------------------------------------------------
        bool Global2RelativePoses(
            const GlobalPoses &global_poses,
            RelativePoses &relative_poses_output)
        {
            relative_poses_output.clear();
            const std::size_t num_views = global_poses.Size();
            if (num_views < 2)
                return false;

            // 确保全局位姿格式为RwTw
            if (global_poses.GetPoseFormat() != PoseFormat::RwTw)
            {
                std::cerr << "Global2RelativePoses expects RwTw global pose format." << std::endl;
                return false;
            }

            // 对所有视图对生成相对位姿
            for (std::size_t i = 0; i < num_views; ++i)
            {
                for (std::size_t j = i + 1; j < num_views; ++j)
                {
                    const Matrix3d &R_i = global_poses.rotations[i];
                    const Vector3d &t_i = global_poses.translations[i];

                    const Matrix3d &R_j = global_poses.rotations[j];
                    const Vector3d &t_j = global_poses.translations[j];

                    RelativePose rel_pose;
                    rel_pose.i = static_cast<IndexT>(i);
                    rel_pose.j = static_cast<IndexT>(j);

                    //------------------------------------------------------------------------------
                    // 全局位姿与相对位姿转换公式
                    /**
                     * PoSDK中相对位姿(R|t)的表示方式:  xj = R * xi + t
                     * 注意：第三方库相对位姿或不同，例如OpenGV中相对位姿的表示为 xi = R * xj + t
                     *
                     * 对于RwTw格式 x = R(X-t)：
                     * R_ij = R_j * R_i.transpose()
                     * t_ij = R_j * (t_i - t_j)
                     *
                     * 对于RwTc格式 x = RX+t：
                     * R_ij = R_j * R_i.transpose()
                     * t_ij = t_j - R_j * R_i.transpose() * t_i
                     */
                    //------------------------------------------------------------------------------

                    rel_pose.Rij = R_j * R_i.transpose();
                    rel_pose.tij = R_j * (t_i - t_j);
                    rel_pose.weight = 1.0f; // 默认权重

                    relative_poses_output.push_back(rel_pose);
                }
            }

            return !relative_poses_output.empty();
        }

        // -----------------------------------------------------------------------------
        // EstInfo methods
        // -----------------------------------------------------------------------------

        void EstInfo::Init(std::size_t num_views)
        {
            origin2est_id.resize(num_views, kInvalidViewId);
            view_states.resize(num_views, ViewState::VALID);
            est2origin_id.clear();
        }

        void EstInfo::AddEstimatedView(ViewId original_id)
        {
            ViewId est_id = est2origin_id.size();
            est2origin_id.push_back(original_id);
            origin2est_id[original_id] = est_id;
            view_states[original_id] = ViewState::ESTIMATED;
        }

        void EstInfo::SetViewState(ViewId original_id, ViewState state)
        {
            view_states[original_id] = state;
            if (state != ViewState::ESTIMATED)
            {
                origin2est_id[original_id] = kInvalidViewId;
            }
        }

        EstInfo::ViewState EstInfo::GetViewState(ViewId original_id) const
        {
            return view_states[original_id];
        }

        bool EstInfo::IsEstimated(ViewId original_id) const
        {
            return view_states[original_id] == ViewState::ESTIMATED;
        }

        std::size_t EstInfo::GetNumEstimatedViews() const
        {
            return est2origin_id.size();
        }

        std::vector<ViewId> EstInfo::GetViewsInState(ViewState state) const
        {
            std::vector<ViewId> result;
            for (ViewId id = 0; id < view_states.size(); ++id)
            {
                if (view_states[id] == state)
                {
                    result.push_back(id);
                }
            }
            return result;
        }

        void EstInfo::BuildFromTracks(const TracksPtr &tracks_ptr, const ViewId fixed_id)
        {
            // 收集所有视图ID
            std::set<ViewId> valid_views = CollectValidViews(tracks_ptr);

            // 初始化EstInfo
            std::size_t num_views = valid_views.size();
            Init(num_views);

            // 首先处理fixed_id（如果存在）
            if (valid_views.count(fixed_id) > 0)
            {
                AddEstimatedView(fixed_id);
                valid_views.erase(fixed_id);
            }

            // 处理其他视图
            for (const ViewId &view_id : valid_views)
            {
                AddEstimatedView(view_id);
            }
        }

        std::set<ViewId> EstInfo::CollectValidViews(const TracksPtr &tracks_ptr) const
        {
            // 找到最大的视图ID，用于确定向量大小
            ViewId max_view_id = 0;
            for (const auto &track_info : *tracks_ptr)
            {
                if (!track_info.is_used)
                    continue;

                for (const auto &obs : track_info.track)
                {
                    if (obs.is_used)
                    {
                        max_view_id = std::max(max_view_id, obs.view_id);
                    }
                }
            }

            // 使用vector统计观测数量（连续内存，更缓存友好）
            std::vector<std::size_t> view_obs_count(max_view_id + 1, 0);

            // 统计每个视图的有效观测数量
            for (const auto &track_info : *tracks_ptr)
            {
                if (!track_info.is_used)
                    continue;

                for (const auto &obs : track_info.track)
                {
                    if (obs.is_used)
                    {
                        view_obs_count[obs.view_id]++;
                    }
                }
            }

            // 收集有效视图
            std::set<ViewId> valid_views;
            for (ViewId view_id = 0; view_id <= max_view_id; ++view_id)
            {
                if (view_obs_count[view_id] >= 2)
                { // 至少需要2个有效观测
                    valid_views.insert(view_id);
                }
            }

            return valid_views;
        }

        // -----------------------------------------------------------------------------
        // GlobalPoses methods
        // -----------------------------------------------------------------------------
        void GlobalPoses::Init(std::size_t num_views)
        {
            rotations.resize(num_views, Matrix3d::Identity());
            translations.resize(num_views, Vector3d::Zero());
            est_info.Init(num_views);
        }

        const Matrix3d &GlobalPoses::GetRotation(ViewId original_id) const
        {
            return rotations[original_id];
        }

        const Vector3d &GlobalPoses::GetTranslation(ViewId original_id) const
        {
            return translations[original_id];
        }

        const Matrix3d &GlobalPoses::GetRotationByEstId(ViewId est_id) const
        {
            ViewId original_id = est_info.est2origin_id[est_id];
            return rotations[original_id];
        }

        const Vector3d &GlobalPoses::GetTranslationByEstId(ViewId est_id) const
        {
            ViewId original_id = est_info.est2origin_id[est_id];
            return translations[original_id];
        }

        void GlobalPoses::SetRotation(ViewId original_id,
                                      const Matrix3d &rotation)
        {
            rotations[original_id] = rotation;
        }

        void GlobalPoses::SetTranslation(ViewId original_id,
                                         const Vector3d &translation)
        {
            translations[original_id] = translation;
        }

        void GlobalPoses::SetRotationByEstId(ViewId est_id,
                                             const Matrix3d &rotation)
        {
            ViewId original_id = est_info.est2origin_id[est_id];
            rotations[original_id] = rotation;
        }

        void GlobalPoses::SetTranslationByEstId(ViewId est_id,
                                                const Vector3d &translation)
        {
            ViewId original_id = est_info.est2origin_id[est_id];
            translations[original_id] = translation;
        }

        std::size_t GlobalPoses::Size() const
        {
            return rotations.size();
        }

        EstInfo &GlobalPoses::GetEstInfo()
        {
            return est_info;
        }

        const EstInfo &GlobalPoses::GetEstInfo() const
        {
            return est_info;
        }

        void GlobalPoses::BuildEstInfoFromTracks(const TracksPtr &tracks_ptr,
                                                 const ViewId fixed_id)
        {
            // 使用EstInfo的BuildFromTracks方法构建映射关系
            est_info.BuildFromTracks(tracks_ptr, fixed_id);

            // 确保位姿数组大小足够
            ViewId max_view_id = 0; // 改为ViewId类型
            for (const auto &track_info : *tracks_ptr)
            {
                if (!track_info.is_used)
                    continue;

                for (const auto &obs : track_info.track)
                {
                    if (obs.is_used)
                    {
                        max_view_id = std::max(max_view_id, obs.view_id);
                    }
                }
            }

            // 初始化或调整位姿数组大小
            if (rotations.size() <= static_cast<std::size_t>(max_view_id))
            {
                rotations.resize(static_cast<std::size_t>(max_view_id) + 1, Matrix3d::Identity());
                translations.resize(static_cast<std::size_t>(max_view_id) + 1, Vector3d::Zero());
            }
        }

        /**
         * @brief 将位姿从RwTw格式转换为RwTc格式
         */
        bool GlobalPoses::RwTw_to_RwTc(ViewId ref_id,
                                       ViewId fixed_id)
        {
            // 检查poses是否有足够的数据
            if (rotations.empty() || translations.empty())
            {
                std::cerr << "Empty poses for format conversion" << std::endl;
                return false;
            }

            // 验证ref_id有效
            if (ref_id >= Size())
            {
                std::cerr << "Invalid reference ID for pose format conversion" << std::endl;
                return false;
            }

            const std::size_t num_poses = Size();
            GlobalTranslations new_translations(num_poses);

            // 转换所有位姿的平移部分，旋转保持不变
            for (std::size_t i = 0; i < num_poses; ++i)
            {
                // tc(:,i) = -R(:,:,i) * t(:,i);
                new_translations[i] = -rotations[i] * translations[i];
            }

            // 更新平移向量，旋转保持不变
            translations = std::move(new_translations);
            SetPoseFormat(PoseFormat::RwTc); // 标记格式已更改

            return true;
        }

        /**
         * @brief 将位姿从RwTc格式转换为RwTw格式
         */
        bool GlobalPoses::RwTc_to_RwTw(ViewId ref_id,
                                       ViewId fixed_id)
        {
            // 检查poses是否有足够的数据
            if (rotations.empty() || translations.empty())
            {
                std::cerr << "Empty poses for format conversion" << std::endl;
                return false;
            }

            // 验证ref_id有效
            if (ref_id >= Size())
            {
                std::cerr << "Invalid reference ID for pose format conversion" << std::endl;
                return false;
            }

            const Matrix3d &R0 = rotations[ref_id];
            const Vector3d &t0 = translations[ref_id];
            const std::size_t num_poses = Size();

            // 转换所有位姿
            GlobalRotations new_rotations(num_poses);
            GlobalTranslations new_translations(num_poses);

            for (std::size_t i = 0; i < num_poses; ++i)
            {
                const Matrix3d &Ri = rotations[i];
                const Vector3d &ti = translations[i];

                // RwTc -> RwTw: R保持不变，t变为 -R^T * t
                new_rotations[i] = Ri;
                new_translations[i] = -Ri.transpose() * ti;
            }

            // 更新位姿
            rotations = std::move(new_rotations);
            translations = std::move(new_translations);
            SetPoseFormat(PoseFormat::RwTw);

            return true;
        }

        /**
         * @brief 在不同位姿格式之间进行转换
         */
        bool GlobalPoses::ConvertPoseFormat(PoseFormat target_format,
                                            ViewId ref_id,
                                            ViewId fixed_id)
        {
            // 如果当前格式已经是目标格式，无需转换
            if (GetPoseFormat() == target_format)
            {
                return true;
            }

            // 根据当前格式和目标格式选择转换方法
            if (GetPoseFormat() == PoseFormat::RwTw && target_format == PoseFormat::RwTc)
            {
                return RwTw_to_RwTc(ref_id, fixed_id);
            }
            else if (GetPoseFormat() == PoseFormat::RwTc && target_format == PoseFormat::RwTw)
            {
                return RwTc_to_RwTw(ref_id, fixed_id);
            }

            // 不支持的转换
            std::cerr << "Unsupported pose format conversion" << std::endl;
            return false;
        }

        // // =============================================================================
        // // ComputeSimilarityTransform methods
        // // =============================================================================

        // // -----------------------------------------------------------------------------
        // // 相似变换优化的Ceres Cost Function
        // // -----------------------------------------------------------------------------
        // struct SimilarityTransformError
        // {
        //     SimilarityTransformError(const Vector3d &src_point, const Vector3d &dst_point)
        //         : src_point_(src_point), dst_point_(dst_point) {}

        //     template <typename T>
        //     bool operator()(const T *const scale,
        //                     const T *const rotation,
        //                     const T *const translation,
        //                     T *residuals) const
        //     {
        //         // 将源点转换为T类型
        //         T src_point[3];
        //         src_point[0] = T(src_point_(0));
        //         src_point[1] = T(src_point_(1));
        //         src_point[2] = T(src_point_(2));

        //         // 应用缩放
        //         T scaled_point[3];
        //         scaled_point[0] = (*scale) * src_point[0];
        //         scaled_point[1] = (*scale) * src_point[1];
        //         scaled_point[2] = (*scale) * src_point[2];

        //         // 应用旋转
        //         T rotated_point[3];
        //         ceres::AngleAxisRotatePoint(rotation, scaled_point, rotated_point);

        //         // 应用平移
        //         T transformed_point[3];
        //         transformed_point[0] = rotated_point[0] + translation[0];
        //         transformed_point[1] = rotated_point[1] + translation[1];
        //         transformed_point[2] = rotated_point[2] + translation[2];

        //         // 计算残差 (L2 distance)
        //         residuals[0] = transformed_point[0] - T(dst_point_(0));
        //         residuals[1] = transformed_point[1] - T(dst_point_(1));
        //         residuals[2] = transformed_point[2] - T(dst_point_(2));

        //         return true;
        //     }

        //     // 工厂函数
        //     static ceres::CostFunction *Create(const Vector3d &src_point, const Vector3d &dst_point)
        //     {
        //         return (new ceres::AutoDiffCostFunction<SimilarityTransformError, 3, 1, 3, 3>(
        //             new SimilarityTransformError(src_point, dst_point)));
        //     }

        // private:
        //     // 源点和目标点
        //     const Vector3d src_point_;
        //     const Vector3d dst_point_;
        // };

        // // 计算两组位姿间的最优相似变换
        // // **************  sRt变换方式公式说明： **************
        // // 投影方程： x ~ R_src * (X_src - t_src)
        // //          = R_src * s * R^T * R (X_src + t - t_src - t)
        // //          = R_src * R^T * [s * R * X_src + t - (s * R * t_src + t)]
        // // 变换过程：
        // // R_src >> R_dst = R_src * R^T
        // // t_src >> t_dst = s * R * t_src + t （ComputeSimilarityTransform输入）
        // // X_src >> X_dst = s * R * X_src + t （备选输入，未使用）
        // // ******************************************************

        // /**
        //  * @brief 计算两组位姿间的最优相似变换
        //  * @param[in] src_centers 源位置向量
        //  * @param[in] dst_centers 目标位置向量
        //  * @param[out] src_centers_transformed 变换后的源位置向量
        //  * @param[out] scale 输出尺度因子
        //  * @param[out] rotation 输出旋转矩阵
        //  * @param[out] translation 输出平移向量
        //  * @return 是否成功计算相似变换
        //  */
        // bool GlobalPoses::ComputeSimilarityTransform(
        //     const std::vector<Vector3d> &src_centers,
        //     const std::vector<Vector3d> &dst_centers,
        //     std::vector<Vector3d> &src_centers_transformed,
        //     double &scale,
        //     Matrix3d &rotation,
        //     Vector3d &translation)
        // {
        //     if (src_centers.size() != dst_centers.size() || src_centers.size() < 3)
        //     {
        //         std::cerr << "ComputeSimilarityTransform: 源点和目标点数量不匹配或少于3个点" << std::endl;
        //         return false;
        //     }

        //     // 1. 使用Eigen::umeyama计算初始相似变换
        //     // 将点集转换为矩阵形式
        //     Eigen::Matrix<double, 3, Eigen::Dynamic> src_mat(3, src_centers.size());
        //     Eigen::Matrix<double, 3, Eigen::Dynamic> dst_mat(3, dst_centers.size());

        //     for (size_t i = 0; i < src_centers.size(); ++i)
        //     {
        //         src_mat.col(i) = src_centers[i];
        //         dst_mat.col(i) = dst_centers[i];
        //     }

        //     // 使用umeyama算法计算变换
        //     Eigen::Matrix4d transform = Eigen::umeyama(src_mat, dst_mat, true);

        //     // 提取变换参数
        //     rotation = transform.topLeftCorner<3, 3>();
        //     scale = std::pow(rotation.determinant(), 1.0 / 3.0);
        //     rotation /= scale;
        //     translation = transform.topRightCorner<3, 1>();

        //     // 检查退化情况
        //     if (scale < std::numeric_limits<double>::epsilon())
        //     {
        //         std::cerr << "ComputeSimilarityTransform: 变换尺度接近零，可能是退化配置" << std::endl;
        //         return false;
        //     }

        //     // 2. 使用Ceres优化相似变换参数
        //     // 将旋转矩阵转换为角轴表示
        //     double angle_axis[3];
        //     ceres::RotationMatrixToAngleAxis(rotation.data(), angle_axis);

        //     // 设置优化变量
        //     double scale_opt = scale;
        //     double translation_opt[3] = {translation(0), translation(1), translation(2)};

        //     // 构建Ceres问题
        //     ceres::Problem problem;
        //     for (size_t i = 0; i < src_centers.size(); ++i)
        //     {
        //         ceres::CostFunction *cost_function =
        //             SimilarityTransformError::Create(src_centers[i], dst_centers[i]);
        //         problem.AddResidualBlock(cost_function,
        //                                  nullptr, // 不使用稳健核函数
        //                                  &scale_opt,
        //                                  angle_axis,
        //                                  translation_opt);
        //     }

        //     // 确保尺度为正
        //     problem.SetParameterLowerBound(&scale_opt, 0, 1e-10);

        //     // 设置Ceres求解器选项
        //     ceres::Solver::Options options;
        //     options.linear_solver_type = ceres::DENSE_QR;
        //     options.minimizer_progress_to_stdout = false;
        //     options.max_num_iterations = 100;
        //     options.function_tolerance = 1e-8;

        //     // 求解优化问题
        //     ceres::Solver::Summary summary;
        //     ceres::Solve(options, &problem, &summary);

        //     // 更新优化后的参数
        //     scale = scale_opt;
        //     ceres::AngleAxisToRotationMatrix(angle_axis, rotation.data());
        //     translation = Vector3d(translation_opt[0], translation_opt[1], translation_opt[2]);

        //     // 变换源点
        //     src_centers_transformed.resize(src_centers.size());
        //     for (size_t i = 0; i < src_centers.size(); ++i)
        //     {
        //         src_centers_transformed[i] = scale * rotation * src_centers[i] + translation;
        //     }

        //     return true;
        // }

        // /**
        //  * @brief 使用Ceres优化的相似变换评估全局位姿精度（更精确）
        //  * @details 通过Ceres优化找到最佳相似变换参数(S,R,t)，将估计位姿对齐到真值
        //  * @param gt_poses 真值全局位姿
        //  * @param position_errors 输出位置误差
        //  * @param rotation_errors 输出旋转误差(角度)
        //  * @return 返回是否成功评估
        //  */
        // bool GlobalPoses::EvaluateWithSimilarityTransform(
        //     const GlobalPoses &gt_poses,
        //     std::vector<double> &position_errors,
        //     std::vector<double> &rotation_errors) const
        // {
        //     if (gt_poses.rotations.empty() || rotations.empty())
        //     {
        //         std::cerr << "EvaluateWithSimilarityTransform: 位姿为空" << std::endl;
        //         return false;
        //     }

        //     // 如果格式不匹配，创建一个副本并尝试转换
        //     if (gt_poses.GetPoseFormat() != GetPoseFormat())
        //     {
        //         GlobalPoses gt_poses_copy = gt_poses;
        //         if (!gt_poses_copy.ConvertPoseFormat(GetPoseFormat()))
        //         {
        //             std::cerr << "无法将真值位姿从"
        //                       << (gt_poses.GetPoseFormat() == PoseFormat::RwTw ? "RwTw" : "RwTc")
        //                       << "格式转换为"
        //                       << (GetPoseFormat() == PoseFormat::RwTw ? "RwTw" : "RwTc")
        //                       << "格式" << std::endl;
        //             return false;
        //         }

        //         // 使用转换后的副本进行评估
        //         return EvaluateWithSimilarityTransform(gt_poses_copy, position_errors, rotation_errors);
        //     }

        //     // 收集位姿中心和旋转矩阵
        //     std::vector<Vector3d> gt_centers;
        //     std::vector<Vector3d> est_centers;
        //     std::vector<Matrix3d> gt_rotations;
        //     std::vector<Matrix3d> est_rotations;

        //     size_t num_poses = std::min(gt_poses.Size(), Size());
        //     gt_centers.reserve(num_poses);
        //     est_centers.reserve(num_poses);
        //     gt_rotations.reserve(num_poses);
        //     est_rotations.reserve(num_poses);

        //     for (size_t i = 0; i < num_poses; ++i)
        //     {
        //         gt_centers.push_back(gt_poses.translations[i]);
        //         est_centers.push_back(translations[i]);
        //         gt_rotations.push_back(gt_poses.rotations[i]);
        //         est_rotations.push_back(rotations[i]);
        //     }

        //     // 计算相似变换参数
        //     std::vector<Vector3d> est_centers_transformed;
        //     double scale;
        //     Matrix3d R_align;
        //     Vector3d t_align;

        //     bool success = ComputeSimilarityTransform(
        //         est_centers, gt_centers, est_centers_transformed,
        //         scale, R_align, t_align);

        //     if (!success)
        //     {
        //         std::cerr << "相似变换计算失败" << std::endl;
        //         return false;
        //     }

        //     // 变换估计的旋转矩阵
        //     std::vector<Matrix3d> est_rotations_aligned(num_poses);
        //     for (size_t i = 0; i < num_poses; ++i)
        //     {
        //         est_rotations_aligned[i] = est_rotations[i] * R_align.transpose();
        //     }

        //     // 计算位置和旋转误差
        //     position_errors.clear();
        //     rotation_errors.clear();
        //     position_errors.reserve(num_poses);
        //     rotation_errors.reserve(num_poses);

        //     for (size_t i = 0; i < num_poses; ++i)
        //     {
        //         // 位置误差
        //         position_errors.push_back((gt_centers[i] - est_centers_transformed[i]).norm());

        //         // 旋转误差：通过旋转矩阵的差异计算角度
        //         Matrix3d R_error = gt_rotations[i] * est_rotations_aligned[i].transpose();
        //         double angle_error = std::acos(std::clamp((R_error.trace() - 1.0) / 2.0, -1.0, 1.0));
        //         rotation_errors.push_back(angle_error * 180.0 / M_PI); // 转换为度
        //     }

        //     return true;
        // }

    }

}
