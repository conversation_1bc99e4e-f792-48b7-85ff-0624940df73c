#ifndef _POMVG_PLUGIN_
#define _POMVG_PLUGIN_
#include <memory>
#include <vector>
#include <unordered_map>
#include <iostream>
#include <string>
#include <mutex>

#include "interfaces.hpp"
#include "inifile.hpp"

#if defined(_WIN32) || defined(_WIN64)
#include <windows.h>
#define DLL_LOAD_LIB(X, Y) LoadLibrary(X)
#define DLL_GET_FCN(X, Y) GetProcAddress((HMODULE)X, Y)
#else
#include <dlfcn.h> // 在所有非Windows平台上都包含dlfcn.h
#define DLL_LOAD_LIB(X, Y) dlopen(X, Y)
#define DLL_GET_FCN(X, Y) dlsym(X, Y)
#endif

namespace PoSDK
{
    namespace Plugin
    {

        using namespace Interface;
        // ======== Macro section of plugin ==========================
        // Copyright: author Qi Cai
        // Readme:
        // =================================================================

#define PluginType std::string
#define PluginLibPath std::string
#define PluginRegex std::string

        // ================ common types section ======================
        // Copyright: author Qi Cai
        // Readme:
        // ===============================================================

        // ================ common function section ======================
        // Copyright: author Qi Cai
        // Readme:
        // ===============================================================

        // 插件类型到插件路径的映射
        typedef std::unordered_map<PluginType, PluginLibPath> MapPlugin;

        // type-tranform function claim section
        void PluginManager(const PluginLibPath &path,
                           const std::string &regex,
                           MapPlugin &map_plugin);

        bool GetPlugin(const MapPlugin &map_plugin,
                       const std::string &type,
                       VoidPtr &void_ptr);

        // rename for dynamic library calling
        typedef const std::string &(*PluginGetTypeFcn)();

        typedef std::shared_ptr<void> (*PluginRegistration)();

        class PluginHandleManager
        {
        private:
            struct HandleInfo
            {
                void *handle;
                int ref_count;
                HandleInfo(void *h) : handle(h), ref_count(1) {}
            };

            std::mutex mutex_;                                    // 添加互斥锁成员
            std::unordered_map<std::string, HandleInfo> handles_; // 路径到句柄的映射

            PluginHandleManager() = default; // 私有构造函数

        public:
            static PluginHandleManager &Instance()
            {
                static PluginHandleManager instance;
                return instance;
            }

            void *AddHandle(const std::string &path)
            {
                std::lock_guard<std::mutex> lock(mutex_);
                auto it = handles_.find(path);
                if (it != handles_.end())
                {
                    it->second.ref_count++;
                    return it->second.handle;
                }

                void *handle = DLL_LOAD_LIB(path.c_str(), RTLD_LAZY | RTLD_NODELETE);
                if (handle)
                {
                    handles_.emplace(path, HandleInfo(handle));
                }
                return handle;
            }

            void RemoveHandle(const std::string &path)
            {
                std::lock_guard<std::mutex> lock(mutex_);
                auto it = handles_.find(path);
                if (it != handles_.end())
                {
                    if (--it->second.ref_count == 0)
                    {
#if defined(_WIN32) || defined(_WIN64)
                        FreeLibrary((HMODULE)it->second.handle);
#else
                        dlclose(it->second.handle);
#endif
                        handles_.erase(it);
                    }
                }
            }

            ~PluginHandleManager()
            {
                std::lock_guard<std::mutex> lock(mutex_);
                for (auto &pair : handles_)
                {
#if defined(_WIN32) || defined(_WIN64)
                    FreeLibrary((HMODULE)pair.second.handle);
#else
                    dlclose(pair.second.handle);
#endif
                }
                handles_.clear();
            }
        };

    }
}
#endif
