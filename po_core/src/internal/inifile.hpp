#ifndef POMVG_CONFIGURATION_TOOLS_HPP
#define POMVG_CONFIGURATION_TOOLS_HPP

#include <string>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <unordered_map>
#include <iostream>
#include <fstream>
#include <sstream>
#include <boost/property_tree/ptree.hpp>
#include <boost/property_tree/ini_parser.hpp>
#include <filesystem>
#include "types.hpp"

namespace PoSDK
{

    using namespace types;

    class ConfigurationTools
    {
    public:
        // 兼容原有构造函数
        ConfigurationTools() = default;
        explicit ConfigurationTools(const std::string &filename)
        {
            Init(filename);
        }
        ~ConfigurationTools() = default;

        // 兼容原有初始化接口
        int Init()
        {
            if (configs_filename_.empty())
            {
                return -1;
            }
            return LoadFile();
        }

        int Init(const std::string &filename)
        {
            configs_filename_ = filename;
            return LoadFile();
        }

        // 兼容原有读取接口
        int ReadItem(const std::string &strSection, const std::string &strKey,
                     const std::string &strDefault, std::string &strValue)
        {
            std::shared_lock<std::shared_mutex> lock(mutex_);
            try
            {
                if (!configs_.count(strSection))
                {
                    strValue = strDefault;
                    return -1;
                }

                const auto &sectionMap = configs_[strSection];
                if (sectionMap.count(strKey))
                {
                    strValue = sectionMap.at(strKey);
                    return 0;
                }

                strValue = strDefault;
                return -1;
            }
            catch (const std::exception &e)
            {
                std::cerr << "Error reading value: " << e.what() << std::endl;
                strValue = strDefault;
                return -1;
            }
        }

        // 兼容原有写入接口
        int WriteItem(const std::string &section, const std::string &key,
                      const std::string &value)
        {
            std::unique_lock<std::shared_mutex> lock(mutex_);
            try
            {
                configs_[section][key] = value;
                return WriteFile();
            }
            catch (const std::exception &e)
            {
                std::cerr << "Error writing value: " << e.what() << std::endl;
                return -1;
            }
        }

        // 兼容原有Dump接口
        int Dump()
        {
            std::shared_lock<std::shared_mutex> lock(mutex_);
            for (const auto &[section, options] : configs_)
            {
                std::cout << "[" << section << "]" << std::endl;
                for (const auto &[key, value] : options)
                {
                    std::cout << std::setw(25) << std::left << key
                              << " = " << value << std::endl;
                }
                std::cout << std::endl;
            }
            return 0;
        }

        // 公开configs_以兼容现有代码
        MethodsConfig configs_;
        std::string configs_filename_;

    private:
        /**
         * @brief 预处理ini文件内容，移除注释行和处理行内注释
         * @param filename 文件路径
         * @return 处理后的内容字符串
         */
        std::string PreprocessIniContent(const std::string &filename)
        {
            std::ifstream file(filename);
            if (!file.is_open())
            {
                throw std::runtime_error("Cannot open file: " + filename);
            }

            std::ostringstream processed_content;
            std::string line;

            while (std::getline(file, line))
            {
                // 移除行首和行尾的空白字符
                std::string trimmed_line = TrimString(line);

                // 检查是否为注释行（以#或;开头）
                if (trimmed_line.empty() ||
                    trimmed_line[0] == '#' ||
                    trimmed_line[0] == ';')
                {
                    // 跳过注释行和空行
                    continue;
                }

                // 处理行内注释（移除#或;及其后面的内容）
                std::string cleaned_line = RemoveInlineComments(trimmed_line);

                // 再次检查处理后的行是否为空
                if (!TrimString(cleaned_line).empty())
                {
                    processed_content << cleaned_line << std::endl;
                }
            }

            return processed_content.str();
        }

        /**
         * @brief 移除字符串首尾空白字符
         * @param str 输入字符串
         * @return 去除空白字符后的字符串
         */
        std::string TrimString(const std::string &str)
        {
            if (str.empty())
                return str;

            size_t start = 0;
            size_t end = str.length() - 1;

            // 找到第一个非空白字符
            while (start <= end && std::isspace(str[start]))
            {
                start++;
            }

            // 找到最后一个非空白字符
            while (end >= start && std::isspace(str[end]))
            {
                end--;
            }

            if (start > end)
                return "";

            return str.substr(start, end - start + 1);
        }

        /**
         * @brief 移除行内注释
         * @param line 输入行
         * @return 移除注释后的行
         */
        std::string RemoveInlineComments(const std::string &line)
        {
            // 寻找注释开始位置（#或;）
            size_t comment_pos_hash = line.find('#');
            size_t comment_pos_semicolon = line.find(';');

            // 找到最早出现的注释符号
            size_t comment_pos = std::string::npos;
            if (comment_pos_hash != std::string::npos && comment_pos_semicolon != std::string::npos)
            {
                comment_pos = std::min(comment_pos_hash, comment_pos_semicolon);
            }
            else if (comment_pos_hash != std::string::npos)
            {
                comment_pos = comment_pos_hash;
            }
            else if (comment_pos_semicolon != std::string::npos)
            {
                comment_pos = comment_pos_semicolon;
            }

            if (comment_pos != std::string::npos)
            {
                // 移除注释部分并去除尾部空白
                std::string result = line.substr(0, comment_pos);
                return TrimString(result);
            }

            return line;
        }

        int LoadFile()
        {
            std::unique_lock<std::shared_mutex> lock(mutex_);
            try
            {
                // 预处理文件内容，移除注释
                std::string processed_content = PreprocessIniContent(configs_filename_);

                // 将处理后的内容写入临时字符串流
                std::istringstream content_stream(processed_content);

                boost::property_tree::ptree pt;
                boost::property_tree::ini_parser::read_ini(content_stream, pt);

                configs_.clear();
                // 处理每个section
                for (const auto &section : pt)
                {
                    MethodOptions options;
                    // 处理section中的每个key-value对
                    for (const auto &item : section.second)
                    {
                        std::string value = item.second.get_value<std::string>();
                        // 额外的安全检查：再次移除可能的注释
                        value = RemoveInlineComments(value);
                        value = TrimString(value);
                        options[item.first] = value;
                    }
                    configs_[section.first] = options;
                }
                return 0;
            }
            catch (const std::exception &e)
            {
                std::cerr << "Error loading file: " << e.what() << std::endl;
                return -1;
            }
        }

        int WriteFile()
        {
            try
            {
                boost::property_tree::ptree pt;
                // 将configs_转换为ptree
                for (const auto &[section, options] : configs_)
                {
                    for (const auto &[key, value] : options)
                    {
                        pt.put(section + "." + key, value);
                    }
                }
                // 写入文件
                boost::property_tree::write_ini(configs_filename_, pt);
                return 0;
            }
            catch (const std::exception &e)
            {
                std::cerr << "Error writing file: " << e.what() << std::endl;
                return -1;
            }
        }

        mutable std::shared_mutex mutex_;
        boost::property_tree::ptree pt_;
    };

} // namespace PoSDK

#endif // POMVG_CONFIGURATION_TOOLS_HPP
