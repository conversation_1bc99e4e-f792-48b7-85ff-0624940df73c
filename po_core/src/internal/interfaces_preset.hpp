#ifndef _POMVG_INTERFACE_PRESET_
#define _POMVG_INTERFACE_PRESET_
#include <memory>
#include <vector>
#include <unordered_map>
#include <iostream>
#include <iomanip> // 添加iomanip头文件以支持std::setw
#include <sstream> // 添加sstream头文件以支持std::istringstream
#include <filesystem>
#include <cstddef>     // 为size_t提供定义
#include <string>      // 为std::string操作提供支持
#include <numeric>     // 为std::accumulate提供支持
#include <typeinfo>    // 为typeid提供支持
#include <type_traits> // 为std::is_same_v提供支持

// 平台特定头文件
#ifdef _WIN32
#include <windows.h>
#elif defined(__APPLE__) // macOS specific
#include <mach-o/dyld.h>
#include <limits.h> // For PATH_MAX
#else               // Linux and other POSIX
#include <unistd.h> // 为POSIX函数如readlink提供支持
#include <limits.h> // 为PATH_MAX提供定义
#endif

#include "interfaces.hpp"
#include "inifile.hpp"

#include <google/protobuf/message.h>
#include <functional>

#include "pb_dataio.hpp"

namespace PoSDK
{
    namespace Interface
    {
        // 前向声明
        template <typename T>
        std::shared_ptr<T> GetDataPtr(
            const Interface::DataPtr &data_ptr_or_package_ptr,
            const std::string &data_name_in_package = "");

        /**
         * @file data_map.hpp
         * @brief 通用Map数据类型的DataIO派生类模板
         * @details 用于管理各种Map类型数据结构的数据容器
         *
         * @copyright Copyright (c) 2024 Qi Cai
         * Licensed under the Mozilla Public License Version 2.0
         */
        template <typename TMap>
        class DataMap : public Interface::DataIO
        {
            static_assert(std::is_default_constructible<TMap>::value,
                          "TMap must be default constructible");

        public:
            using MapType = TMap;

            DataMap() : data_map_ptr_(std::make_shared<TMap>()) {}

            explicit DataMap(const TMap &map)
                : data_map_ptr_(std::make_shared<TMap>(map)) {}

            explicit DataMap(TMap &&map)
                : data_map_ptr_(std::make_shared<TMap>(std::move(map))) {}

            const std::string &GetType() const override
            {
                static const std::string type_name = "data_map";
                return type_name;
            }

            void *GetData() override
            {
                return static_cast<void *>(data_map_ptr_.get());
            }

            const std::shared_ptr<TMap> &GetMapPtr() const
            {
                return data_map_ptr_;
            }

            /**
             * @brief 创建当前对象的深拷贝
             * @return 返回拷贝后的新对象指针
             * @note 拷贝的深度取决于TMap类型的拷贝语义。如果TMap类型实现了深拷贝，则此函数也会进行深拷贝；否则只进行浅拷贝。
             */
            virtual DataPtr CopyData() const override
            {
                // 如果数据指针无效，返回nullptr
                if (!data_map_ptr_)
                {
                    return nullptr;
                }

                try
                {
                    // 创建一个新的DataMap对象
                    auto cloned = std::make_shared<DataMap<TMap>>();

                    // 复制内部数据（取决于TMap的拷贝构造函数）
                    cloned->data_map_ptr_ = std::make_shared<TMap>(*data_map_ptr_);

                    return cloned;
                }
                catch (const std::exception &e)
                {
                    // 捕获并记录拷贝过程中的异常
                    std::cerr << "克隆DataMap失败: " << e.what() << std::endl;
                    return nullptr;
                }
            }

            /**
             * @brief 评估函数 - 根据TMap类型进行特化处理
             * @param gt_data 真值数据
             * @return 评估状态和结果
             * @note 目前只特化了RelativePose类型，其他类型需要用户重载此函数
             */
            virtual EvaluatorStatus Evaluate(DataPtr gt_data) override
            {
                EvaluatorStatus eval_status;
                eval_status.is_successful = false;

                try
                {
                    if (!gt_data || !data_map_ptr_)
                    {
                        std::cerr << "[DataMap] Invalid data for evaluation" << std::endl;
                        return eval_status;
                    }

                    // 使用 if constexpr 进行类型特化处理
                    if constexpr (std::is_same_v<TMap, RelativePose>)
                    {
                        // RelativePose 特化处理
                        eval_status.SetEvalType("RelativePoses");
                        return EvaluateRelativePose(gt_data);
                    }
                    else
                    {
                        // 其他类型：提示用户重载 Evaluate 函数
                        std::cerr << "[DataMap] Evaluate not implemented for type: "
                                  << typeid(TMap).name() << std::endl;
                        std::cerr << "[DataMap] Please override 'EvaluatorStatus Evaluate(DataPtr gt_data)' "
                                  << "in your DataMap<" << typeid(TMap).name() << "> derived class." << std::endl;
                        eval_status.is_successful = false;
                    }
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[DataMap] Error during evaluation: " << e.what() << std::endl;
                    eval_status.is_successful = false;
                }

                return eval_status;
            }

            ~DataMap() override {}

        protected:
            std::shared_ptr<TMap> data_map_ptr_;

        public:
            /**
             * @brief RelativePose类型的评估实现
             */
            EvaluatorStatus EvaluateRelativePose(DataPtr gt_data)
            {
                EvaluatorStatus eval_status;
                eval_status.is_successful = false;
                eval_status.SetEvalType("RelativePose");

                try
                {
                    // 获取估计的RelativePose
                    const auto &estimated_pose = *data_map_ptr_;

                    // 处理真值数据
                    const auto &gt_pose = *GetDataPtr<RelativePose>(gt_data);

                    // 计算误差
                    double rotation_error, translation_error;
                    bool success = estimated_pose.ComputeErrors(gt_pose, rotation_error, translation_error);

                    if (success)
                    {
                        eval_status.AddResult("rotation_error", rotation_error);
                        eval_status.AddResult("translation_error", translation_error);
                        eval_status.is_successful = true;
                    }
                    else
                    {
                        std::cerr << "[DataMap<RelativePose>] ComputeErrors failed" << std::endl;
                    }
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[DataMap<RelativePose>] Error: " << e.what() << std::endl;
                }

                return eval_status;
            }
        };
        template <typename TMap>
        using DataMapPtr = std::shared_ptr<DataMap<TMap>>;

        // 辅助函数:从DataPtr转换为具体的Map类型指针
        // template<typename TMap>
        // std::shared_ptr<TMap> GetMapDataPtr(const Interface::DataPtr& data_ptr) {
        //     if (!data_ptr) return nullptr;

        //     auto map_data = std::dynamic_pointer_cast<DataMap<TMap>>(data_ptr);
        //     if (!map_data) return nullptr;

        //     return map_data->GetMapPtr();
        // }

        //=========================== DataPackage ==================================
        // # Copyright (c) 2021 PO tools author: Qi Cai.
        //  to pack the multiple `DataPtr` intputs for `Method`:
        //==========================================================================
        using DataType = std::string;
        using Package = std::unordered_map<DataType, DataPtr>;

#define DATA_PACKAGE_PTR(X) \
    ([&]() -> std::shared_ptr<DataPackage> { \
        auto ptr = std::dynamic_pointer_cast<DataPackage>(X); \
        if (ptr && ptr->GetType() != "data_package") { \
            throw std::runtime_error("Invalid DataPackage type: " + ptr->GetType()); \
        } \
        return ptr; })()

#define DATA_PTR(X) std::dynamic_pointer_cast<DataIO>(X)

        class DataPackage : public DataIO
        {
        public:
            const std::string &GetType() const override
            {
                static const std::string &type = "data_package";
                return type;
            }

            ~DataPackage() { package_info_.clear(); }

            virtual void *GetData() override
            {
                return static_cast<void *>(&package_info_);
            }

            virtual void *SetData(void *package_info)
            {
                package_info_ = *(Package *)package_info;
                return nullptr;
            }

            /**
             * @brief 创建DataPackage的深拷贝
             * @return 返回拷贝后的新DataPackage指针
             * @details 遍历包中的所有DataPtr，调用它们的CopyData函数进行深拷贝
             *          对于没有实现CopyData或拷贝失败的DataPtr，会给出警告但继续处理其他数据
             */
            virtual DataPtr CopyData() const override
            {
                try
                {
                    // 创建新的DataPackage
                    auto copied_package = std::make_shared<DataPackage>();

                    std::vector<std::string> successful_copies;
                    std::vector<std::string> failed_copies;

                    // 遍历包中的所有数据项
                    for (const auto &[key, data_ptr] : package_info_)
                    {
                        if (!data_ptr)
                        {
                            failed_copies.push_back(key + "(null_ptr)");
                            continue;
                        }

                        try
                        {
                            // 尝试拷贝数据项
                            DataPtr copied_data = data_ptr->CopyData();
                            if (copied_data)
                            {
                                // 拷贝成功，添加到新包中
                                copied_package->AddData(key, copied_data);
                                successful_copies.push_back(key + "(" + data_ptr->GetType() + ")");
                            }
                            else
                            {
                                // 拷贝返回nullptr，使用原始指针（浅拷贝）
                                copied_package->AddData(key, data_ptr);
                                failed_copies.push_back(key + "(" + data_ptr->GetType() + ",shallow_copy)");
                            }
                        }
                        catch (const std::exception &e)
                        {
                            // 拷贝过程中出现异常，使用原始指针（浅拷贝）
                            copied_package->AddData(key, data_ptr);
                            failed_copies.push_back(key + "(" + data_ptr->GetType() + ",exception:" + e.what() + ")");
                        }
                    }

                    // 输出拷贝结果日志
                    if (!successful_copies.empty())
                    {
                        std::cout << "[DataPackage] 成功深拷贝数据项: ";
                        for (size_t i = 0; i < successful_copies.size(); ++i)
                        {
                            if (i > 0)
                                std::cout << " | ";
                            std::cout << successful_copies[i];
                        }
                        std::cout << std::endl;
                    }

                    if (!failed_copies.empty())
                    {
                        std::cerr << "[DataPackage] 警告 - 以下数据项使用浅拷贝: ";
                        for (size_t i = 0; i < failed_copies.size(); ++i)
                        {
                            if (i > 0)
                                std::cerr << " | ";
                            std::cerr << failed_copies[i];
                        }
                        std::cerr << std::endl;
                    }

                    return copied_package;
                }
                catch (const std::exception &e)
                {
                    std::cerr << "[DataPackage] 拷贝DataPackage时发生错误: " << e.what() << std::endl;
                    return nullptr;
                }
            }

        public:
            DataPackage() {}

            explicit DataPackage(const DataPtr &data_ptr)
            {
                AddData(data_ptr);
            }

            DataPackage(const Package &package) { package_info_ = package; }

            ////    DataPackage& operator=(DataIO& data){
            ////        return dynamic_cast<DataPackage&>(data);;
            ////    }

            //    std::shared_ptr<DataPackage> operator=(DataPtr& data_ptr){
            //        return std::dynamic_pointer_cast<DataPackage>(data_ptr);;
            //    }

            //    std::shared_ptr<DataPackage>& operator&(){
            //        std::shared_ptr<DataPackage> data_package_ptr(this);
            //        return data_package_ptr;
            //    }

            inline void AddData(const DataPtr &data_ptr)
            {
                package_info_.insert({data_ptr->GetType(), data_ptr});
            }

            inline void AddData(const Package &package)
            {
                package_info_.insert(package.begin(), package.end());
            }

            // 添加数据 (别名)
            inline void AddData(const DataType &alias_type, const DataPtr &data_ptr)
            {
                package_info_.insert({alias_type, data_ptr});
            }

            const Package &GetPackage() const { return package_info_; }
            //    inline void DeleteData(){};

            // 根据数据类型获取对应的DataPtr
            DataPtr GetData(const DataType &type) const
            {
                auto it = package_info_.find(type);
                if (it != package_info_.end())
                {
                    return it->second;
                }
                return nullptr;
            }

            // 重载 operator[] 以支持字典式访问
            // 用法: auto data_ptr = data_package["data1"];
            DataPtr operator[](const DataType &type) const
            {
                return GetData(type);
            }

            // 重载 operator[] 以支持字典式赋值
            // 用法: data_package["data1"] = data_ptr;
            DataPtr &operator[](const DataType &type)
            {
                return package_info_[type];
            }

        private:
            Package package_info_;
        };

        typedef std::shared_ptr<DataPackage> DataPackagePtr;

        //=========================== MethodPreset ==================================
        // # Copyright (c) 2021 PoSDK/PoCore author: Qi Cai.
        //
        //==========================================================================
        class MethodPreset;
        typedef std::shared_ptr<MethodPreset> MethodPresetPtr;

        class MethodPreset : virtual public Method
        {
        public:
            // ---- User Overwrite Functions ----

            // Run function is the main function to implement your algorithm in PoSDK.
            virtual DataPtr Run() = 0;
            const std::string &GetType() const override
            {
                static const std::string type = "method_preset";
                return type;
            }

            // ----------------------------------
            // 设置数据类型
            bool SetRequiredData(const DataPtr &data_ptr)
            {
                if (!data_ptr)
                {
                    std::cerr << "Invalid data pointer" << std::endl;
                    return false;
                }

                // 检查是否为数据包类型
                if (data_ptr->GetType() == "data_package")
                {
                    auto package_ptr = std::dynamic_pointer_cast<DataPackage>(data_ptr);
                    if (package_ptr)
                    {
                        // 遍历required_package_，从数据包中找到对应类型的数据
                        for (auto &iter : required_package_)
                        {
                            const std::string &required_type = iter.first;
                            auto data = package_ptr->GetData(required_type);
                            if (data)
                            {
                                iter.second = data;
                            }
                        }
                    }
                    return true;
                }

                // 如果不是数据包，按原有方式处理
                required_package_[data_ptr->GetType()] = data_ptr;
                return true;
            }

            // 从配置文件加载方法选项
            void LoadMethodOptions(const std::string &config_file, const std::string &specific_method = "")
            {
                ConfigurationTools config;
                config.Init(config_file);

                auto method_type = GetType();

                // 使用 std::function 来定义递归 lambda
                std::function<void(const std::string &, ConfigurationTools &)> LoadSection =
                    [&](const std::string &section_name, ConfigurationTools &cfg)
                {
                    for (const auto &section : cfg.configs_)
                    {
                        if (section.first == section_name)
                        {
                            // 检查是否有继承声明
                            auto inherit_it = section.second.find("@inherit");
                            if (inherit_it != section.second.end())
                            {
                                // 加载继承的配置文件
                                std::string inherit_path = inherit_it->second;
                                ConfigurationTools inherit_config;
                                if (inherit_config.Init(inherit_path))
                                {
                                    // 递归加载继承的配置
                                    LoadSection(section_name, inherit_config);
                                }
                            }

                            for (const auto &[key, value] : section.second)
                            {
                                if (key != "@inherit")
                                { // 跳过继承声明
                                    if (!specific_method.empty() && section_name != method_type)
                                    {
                                        // 特定方法配置：直接存储到specific_methods_config_，不污染method_options_
                                        specific_methods_config_[section_name][key] = value;
                                    }
                                    else
                                    {
                                        // 通用配置：存储到method_options_
                                        method_options_[key] = value;
                                    }
                                }
                            }
                            break;
                        }
                    }
                };

                // 如果指定了具体方法，加载对应的特定配置, 否则加载通用配置
                if (!specific_method.empty())
                {
                    // 加载特定方法的配置
                    LoadSection(specific_method, config);
                }
                else
                {
                    // 加载通用配置
                    LoadSection(method_type, config);
                }

                // 读取日志级别配置
                if (method_options_.find("log_level") != method_options_.end())
                {
                    log_level_ = std::stoi(method_options_["log_level"]);
                }

// Debug模式下可以设置更详细的日志
#ifdef _DEBUG
                if (log_level_ < PO_LOG_NORMAL)
                {
                    log_level_ = PO_LOG_NORMAL;
                }
#endif
            }

            /**
             * @brief 重置方法选项到初始的INI文件读取状态
             * @param specific_method 可选的特定方法名称，如果为空则使用当前方法类型
             * @details 清空当前所有方法选项，然后重新从配置文件加载初始配置。
             *          这对于测试环境中避免参数干扰非常有用。
             */
            void ResetOptionsFromIniFile(const std::string &specific_method = "")
            {
                // 1. 清空当前所有方法选项
                method_options_.clear();

                // 2. 重置日志级别为默认值
                log_level_ = PO_LOG_NORMAL;

                // 3. 如果有配置文件路径，重新加载配置
                if (!config_path_.empty() && std::filesystem::exists(config_path_))
                {
                    LoadMethodOptions(config_path_, specific_method);
                }
                else
                {
                    // 4. 如果没有配置文件路径，尝试重新初始化默认配置路径
                    InitializeDefaultConfigPath(specific_method);
                }

                // 5. 输出重置信息（仅在详细日志级别时）
                if (log_level_ >= PO_LOG_VERBOSE)
                {
                    std::cout << "[" << GetType() << "] 方法选项已重置到INI文件初始状态" << std::endl;
                    if (!specific_method.empty())
                    {
                        std::cout << "  - 使用特定方法配置: " << specific_method << std::endl;
                    }
                    std::cout << "  - 配置文件路径: " << config_path_ << std::endl;
                    std::cout << "  - 当前选项数量: " << method_options_.size() << std::endl;
                }
            }

            // 保存方法选项到配置文件
            void SaveMethodOptions(const std::string &config_file)
            {
                ConfigurationTools config;

                // 如果文件存在，先加载现有配置
                if (std::filesystem::exists(config_file))
                {
                    config.Init(config_file);
                }

                // 更新该方法类型的配置
                auto method_type = GetType();
                for (const auto &option : method_options_)
                {
                    config.WriteItem(method_type, option.first, option.second);
                }
            }

            // 获取方法输入数据类型
            const std::vector<std::string> &GetInputTypes() const override
            {
                static std::vector<std::string> input_types;
                input_types.clear();
                for (const auto &item : required_package_)
                {
                    input_types.push_back(item.first);
                }
                return input_types;
            }

            // 获取方法选项
            const MethodOptions &GetMethodOptions() const
            {
                return method_options_;
            }

            // 从方法配置中获取整数值
            IndexT GetOptionAsIndexT(const MethodParams &key, IndexT default_value = 0) const;

            // 从方法配置中获取浮点值
            float GetOptionAsFloat(const MethodParams &key, float default_value = 0.0f) const;

            // 从方法配置中获取双精度值
            double GetOptionAsDouble(const MethodParams &key, double default_value = 0.0) const;

            // 从方法配置中获取布尔值
            bool GetOptionAsBool(const MethodParams &key, bool default_value = false) const;

            // 从方法配置中获取字符串值
            std::string GetOptionAsString(const MethodParams &key, const std::string &default_value = "") const;

            // 从方法配置中获取路径值，支持{exe_dir}、{root_dir}和通用{key_name}占位符
            // 支持的占位符：
            // - {root_dir}: 根目录路径，优先使用参数root_dir，否则从method_options_中获取
            // - {exe_dir}: 可执行文件所在目录
            // - {key_name}: 引用method_options_中其他键值（注意：被引用的键值需要先存在）
            // root_dir: 根目录路径，用于{root_dir}占位符替换，如果为空则从method_options_中获取
            // default_value: 默认值，当配置项不存在时使用
            std::string GetOptionAsPath(const MethodParams &key, const std::string &root_dir = "", const std::string &default_value = "") const;

            /**
             * @brief 传递方法选项给其他方法对象
             * @param target_method 目标方法对象的引用
             * @details 自动从配置文件中读取[target_method.GetType()]部分的配置，
             *          并通过SetMethodOptions传递给目标方法对象
             */
            void PassingMethodOptions(MethodPreset &target_method);

            /**
             * @brief 传递方法选项给其他方法对象（智能指针版本）
             * @param target_method_ptr 目标方法对象的智能指针
             */
            void PassingMethodOptions(MethodPresetPtr target_method_ptr);

            /**
             * @brief 获取特定方法的配置参数
             * @param method_type 方法类型名称
             * @return 对应的方法配置选项，如果不存在则返回空的MethodOptions
             */
            const MethodOptions &GetSpecificMethodConfig(const std::string &method_type) const;

        public:
            // 析构函数
            virtual ~MethodPreset() = default;

            // preset Build function
            void SetMethodOptions(const MethodOptions &options);
            void SetMethodOption(const MethodParams &option_type, const ParamsValue &content);

            DataPtr Build(const DataPtr &material_ptr = nullptr);
            bool CheckInput(const DataPtr &data_ptr);
            inline bool CheckPackage(const DataPtr &data_ptr);
            void CheckDataTypes(const DataPtr &data_ptr);
            void CheckPath();
            void SetCheckStatus(const bool &status);

            // 设置先验信息 【看用户个人需求，来决定是否重写】
            void SetPriorInfo(const DataPtr &data_ptr, const std::string &type = "")
            {
                // 获取先验信息
                if (data_ptr == nullptr)
                {
                    ResetPriorInfo();
                    return;
                }

                // 两种模式:
                // 1. 如果type为空，则假设data_ptr是DataPackagePtr，将其内容合并到prior_info_
                // 2. 如果type不为空，则假设data_ptr是任意DataPtr，以type为键将其加入prior_info_
                if (type.empty())
                {
                    // 模式1: data_ptr应该是DataPackage
                    DataPackagePtr data_package_ptr = DATA_PACKAGE_PTR(data_ptr);
                    if (data_package_ptr)
                    {
                        prior_info_ = data_package_ptr->GetPackage();
                    }
                    else
                    {
                        std::cerr << "[" << GetType() << "] " << "Invalid PriorInfo type: " << data_ptr->GetType()
                                  << ", expected DataPackage when type is empty" << std::endl;
                    }
                }
                else
                {
                    // 模式2: data_ptr是任意DataPtr，直接添加到prior_info_
                    prior_info_[type] = data_ptr;
                }
            }

            // 重置先验信息
            void ResetPriorInfo()
            {
                prior_info_.clear();
            }

            /**
             * @brief 设置真值数据用于评估
             * @param gt_data 真值数据指针
             * @details 内部调用SetPriorInfo将真值数据存储为"gt_data"类型
             */
            void SetGTData(DataPtr &gt_data)
            {
                if (gt_data)
                {
                    SetPriorInfo(gt_data, "gt_data");
                }
                else
                {
                    std::cerr << "[" << GetType() << "] Error: GT data is null" << std::endl;
                }
            }

            /**
             * @brief 获取真值数据用于评估
             * @return 真值数据指针，如果不存在则返回nullptr
             * @details 内部通过prior_info_获取存储为"gt_data"类型的数据
             */
            DataPtr GetGTData() const
            {
                auto it = prior_info_.find("gt_data");
                if (it != prior_info_.end())
                {
                    return it->second;
                }
                else
                {
                    return nullptr;
                }
            }

            /**
             * @brief 设置评估器中使用的算法名称
             * @param algorithm_name 算法名称
             * @details 用于在评估器中区分不同的算法配置，如果不设置则默认使用GetType()的值
             */
            virtual void SetEvaluatorAlgorithm(const std::string &algorithm_name)
            {
                // 这个函数在基类中提供接口，具体实现在MethodPresetProfiler中
                // 基类中不做任何操作，避免对不使用评估器的方法产生影响
                PO_LOG_ERR << "SetEvaluatorAlgorithm is not implemented in MethodPreset" << std::endl;
            }

        protected:
            Package prior_info_;
            MethodOptions method_options_;
            MethodsConfig specific_methods_config_; // 存储特定方法的配置参数
            bool has_checked_input = false;         // 是否已经检查过输入
            int log_level_ = PO_LOG_NORMAL;         // 默认日志级别为普通

            // 显示配置信息的辅助函数
            void DisplayConfigInfo(const std::string &config_file = "", const std::string &method_type = "")
            {
                if (log_level_ < PO_LOG_VERBOSE)
                {
                    return;
                }

                const std::string &actual_config_file = config_file.empty() ? config_path_ : config_file;
                const std::string &actual_method_type = method_type.empty() ? GetType() : method_type;

                // 根据双列显示的实际需要计算合适的宽度
                const size_t KEY_WIDTH = 25;                             // 键名宽度
                const size_t VALUE_WIDTH = 20;                           // 值宽度
                const size_t COLUMN_WIDTH = KEY_WIDTH + 3 + VALUE_WIDTH; // key + " = " + value
                const size_t NUM_COLUMNS = 2;
                const size_t TOTAL_CONTENT_WIDTH = COLUMN_WIDTH * NUM_COLUMNS; // 双列内容总宽度
                const size_t TOTAL_WIDTH = TOTAL_CONTENT_WIDTH + 4;            // 总宽度（内容+边框）
                const size_t CONTENT_WIDTH = TOTAL_CONTENT_WIDTH;              // 内容宽度

                // 创建边框字符串
                std::string horizontal_line;
                for (size_t i = 0; i < TOTAL_WIDTH - 2; ++i)
                {
                    horizontal_line += "═";
                }

                // 创建底部边框字符串（用于所有底部边框）
                std::string bottom_dash_line;
                for (size_t i = 0; i < TOTAL_WIDTH - 2; ++i)
                {
                    bottom_dash_line += "─";
                }

                // 顶部边框
                std::cout << "\n╔" << horizontal_line << "╗\n";

                // 标题行
                std::string title = "配置信息显示 - " + actual_method_type;
                size_t title_padding = (CONTENT_WIDTH - title.length()) / 2;
                std::cout << "║" << std::string(title_padding, ' ') << title
                          << std::string(CONTENT_WIDTH - title_padding - title.length(), ' ') << "║\n";

                // 分隔线
                std::cout << "╠" << horizontal_line << "╣\n";

                // 配置文件路径行
                std::string config_line = "配置文件: " + actual_config_file;
                if (config_line.length() > CONTENT_WIDTH)
                {
                    config_line = config_line.substr(0, CONTENT_WIDTH - 3) + "...";
                }
                std::cout << "║ " << std::setw(CONTENT_WIDTH) << std::left << config_line << " ║\n";

                // 底部边框
                std::cout << "╚" << horizontal_line << "╝\n";

                // 1. ProfileCommit 栏
                auto commit_it = method_options_.find("ProfileCommit");
                if (commit_it != method_options_.end() && !commit_it->second.empty())
                {
                    // 创建ProfileCommit边框
                    std::string profile_dash_line;
                    for (size_t i = 0; i < TOTAL_WIDTH - 18; ++i)
                    {
                        profile_dash_line += "─";
                    }
                    std::cout << "\n┌─ ProfileCommit " << profile_dash_line << "┐\n";
                    // 处理多行描述
                    std::string description = commit_it->second;
                    std::istringstream iss(description);
                    std::string line;
                    while (std::getline(iss, line))
                    {
                        if (line.length() > CONTENT_WIDTH)
                        {
                            line = line.substr(0, CONTENT_WIDTH - 3) + "...";
                        }
                        std::cout << "│ " << std::setw(CONTENT_WIDTH) << std::left << line << " │\n";
                    }
                    std::cout << "└" << bottom_dash_line << "┘\n";
                }

                // 2. 通用配置栏
                std::string general_title = "通用配置 (" + actual_method_type + ")";
                size_t general_title_len = general_title.length() + 4; // "┌─ " + title + " "
                size_t general_dash_count = (general_title_len < TOTAL_WIDTH - 1) ? (TOTAL_WIDTH - 1 - general_title_len) : 0;
                // 创建通用配置标题的边框
                std::string general_dash_line;
                for (size_t i = 0; i < general_dash_count; ++i)
                {
                    general_dash_line += "─";
                }
                std::cout << "\n┌─ " << general_title << " " << general_dash_line << "┐\n";

                // 先显示日志级别
                std::string log_level_desc = (log_level_ == PO_LOG_NONE ? "基本" : (log_level_ == PO_LOG_NORMAL ? "普通" : "详细"));
                std::string log_level_line = "log_level = " + std::to_string(log_level_) + " (" + log_level_desc + ")";
                std::cout << "│ " << std::setw(CONTENT_WIDTH) << std::left << log_level_line << " │\n";

                // 收集通用配置选项（跳过已显示的和特定方法相关的）
                std::vector<std::pair<std::string, std::string>> general_options;
                for (const auto &[key, value] : method_options_)
                {
                    if (key != "ProfileCommit" && key != "log_level" && key.find('|') == std::string::npos)
                    {
                        general_options.push_back({key, value});
                    }
                }

                // 显示通用配置选项（双列格式）
                const size_t LIMIT_MULTI_LINE = 3; // 最大换行数

                // 预处理选项，处理长值的换行
                std::vector<std::vector<std::string>> processed_options;
                for (const auto &[key, value] : general_options)
                {
                    std::vector<std::string> lines;
                    if (value.length() <= VALUE_WIDTH)
                    {
                        // 短值，直接显示
                        std::stringstream ss;
                        ss << std::setw(KEY_WIDTH) << std::left << key << " = " << std::setw(VALUE_WIDTH) << std::left << value;
                        lines.push_back(ss.str());
                    }
                    else
                    {
                        // 长值，需要处理换行
                        std::string remaining = value;
                        size_t line_count = 0;

                        while (!remaining.empty() && line_count < LIMIT_MULTI_LINE)
                        {
                            if (line_count == 0)
                            {
                                // 第一行：显示键名
                                std::string first_part = remaining.substr(0, VALUE_WIDTH);
                                std::stringstream ss;
                                ss << std::setw(KEY_WIDTH) << std::left << key << " = " << std::setw(VALUE_WIDTH) << std::left << first_part;
                                lines.push_back(ss.str());
                                remaining = remaining.substr(std::min(VALUE_WIDTH, remaining.length()));
                            }
                            else
                            {
                                // 后续行：键名位置留空，等号对齐
                                std::string continuation = remaining.substr(0, VALUE_WIDTH);
                                std::stringstream ss;
                                ss << std::string(KEY_WIDTH, ' ') << "   " << std::setw(VALUE_WIDTH) << std::left << continuation;
                                lines.push_back(ss.str());
                                remaining = remaining.substr(std::min(VALUE_WIDTH, remaining.length()));
                            }
                            line_count++;
                        }

                        // 如果还有剩余内容，最后一行添加省略号
                        if (!remaining.empty())
                        {
                            if (lines.size() > 0)
                            {
                                std::string &last_line = lines.back();
                                // 移除最后一行的空白，添加省略号
                                size_t last_non_space = last_line.find_last_not_of(' ');
                                if (last_non_space != std::string::npos && last_non_space + 4 < last_line.length())
                                {
                                    last_line = last_line.substr(0, last_non_space + 1) + "...";
                                    last_line.resize(KEY_WIDTH + 3 + VALUE_WIDTH, ' '); // 确保宽度一致
                                }
                            }
                        }
                    }
                    processed_options.push_back(lines);
                }

                // 计算所需的显示行数
                size_t max_lines_in_options = 0;
                for (const auto &option_lines : processed_options)
                {
                    max_lines_in_options = std::max(max_lines_in_options, option_lines.size());
                }

                size_t total_display_lines = 0;
                for (size_t opt_idx = 0; opt_idx < processed_options.size(); opt_idx += NUM_COLUMNS)
                {
                    // 找出这一组中最大的行数
                    size_t max_lines_in_group = 0;
                    for (size_t col = 0; col < NUM_COLUMNS && opt_idx + col < processed_options.size(); ++col)
                    {
                        max_lines_in_group = std::max(max_lines_in_group, processed_options[opt_idx + col].size());
                    }
                    total_display_lines += max_lines_in_group;
                }

                // 按组显示选项
                for (size_t opt_idx = 0; opt_idx < processed_options.size(); opt_idx += NUM_COLUMNS)
                {
                    // 找出这一组中最大的行数
                    size_t max_lines_in_group = 0;
                    for (size_t col = 0; col < NUM_COLUMNS && opt_idx + col < processed_options.size(); ++col)
                    {
                        max_lines_in_group = std::max(max_lines_in_group, processed_options[opt_idx + col].size());
                    }

                    // 显示这一组的所有行
                    for (size_t line_idx = 0; line_idx < max_lines_in_group; ++line_idx)
                    {
                        std::string line_content;
                        for (size_t col = 0; col < NUM_COLUMNS; ++col)
                        {
                            size_t option_index = opt_idx + col;
                            if (option_index < processed_options.size())
                            {
                                const auto &option_lines = processed_options[option_index];
                                if (line_idx < option_lines.size())
                                {
                                    if (col == 0)
                                    {
                                        // 第一列：直接添加内容，然后填充到固定宽度
                                        line_content += option_lines[line_idx];
                                        if (option_lines[line_idx].length() < COLUMN_WIDTH)
                                        {
                                            line_content += std::string(COLUMN_WIDTH - option_lines[line_idx].length(), ' ');
                                        }
                                    }
                                    else
                                    {
                                        // 第二列：直接添加内容，不需要额外填充
                                        line_content += option_lines[line_idx];
                                    }
                                }
                                else
                                {
                                    if (col == 0)
                                    {
                                        line_content += std::string(COLUMN_WIDTH, ' '); // 填充空白
                                    }
                                    // 第二列为空时不添加任何内容
                                }
                            }
                            else
                            {
                                if (col == 0)
                                {
                                    line_content += std::string(COLUMN_WIDTH, ' '); // 填充空白
                                }
                                // 第二列为空时不添加任何内容
                            }
                        }

                        // 确保行内容精确填充到边框宽度
                        if (line_content.length() < CONTENT_WIDTH)
                        {
                            line_content += std::string(CONTENT_WIDTH - line_content.length(), ' ');
                        }
                        else if (line_content.length() > CONTENT_WIDTH)
                        {
                            line_content = line_content.substr(0, CONTENT_WIDTH);
                        }

                        std::cout << "│ " << line_content << " │\n";
                    }
                }

                if (general_options.empty())
                {
                    std::cout << "│ " << std::setw(CONTENT_WIDTH) << std::left << "(无通用配置参数)" << " │\n";
                }
                std::cout << "└" << bottom_dash_line << "┘\n";

                // 3. 特定方法配置栏
                if (!specific_methods_config_.empty())
                {
                    for (const auto &[method_name, method_config] : specific_methods_config_)
                    {
                        std::string specific_title = "特定方法配置 (" + method_name + ")";
                        size_t specific_title_len = specific_title.length() + 4; // "┌─ " + title + " "
                        size_t specific_dash_count = (specific_title_len < TOTAL_WIDTH - 1) ? (TOTAL_WIDTH - 1 - specific_title_len) : 0;
                        // 创建特定方法配置标题的边框
                        std::string specific_dash_line;
                        for (size_t i = 0; i < specific_dash_count; ++i)
                        {
                            specific_dash_line += "─";
                        }
                        std::cout << "\n┌─ " << specific_title << " " << specific_dash_line << "┐\n";

                        if (method_config.empty())
                        {
                            std::cout << "│ " << std::setw(CONTENT_WIDTH) << std::left << "(无特定配置参数)" << " │\n";
                        }
                        else
                        {
                            // 将特定方法配置转换为向量以便双列显示
                            std::vector<std::pair<std::string, std::string>> specific_options(
                                method_config.begin(), method_config.end());

                            // 预处理特定方法选项，处理长值的换行
                            std::vector<std::vector<std::string>> processed_specific_options;
                            for (const auto &[key, value] : specific_options)
                            {
                                std::vector<std::string> lines;
                                if (value.length() <= VALUE_WIDTH)
                                {
                                    // 短值，直接显示
                                    std::stringstream ss;
                                    ss << std::setw(KEY_WIDTH) << std::left << key << " = " << std::setw(VALUE_WIDTH) << std::left << value;
                                    lines.push_back(ss.str());
                                }
                                else
                                {
                                    // 长值，需要处理换行
                                    std::string remaining = value;
                                    size_t line_count = 0;

                                    while (!remaining.empty() && line_count < LIMIT_MULTI_LINE)
                                    {
                                        if (line_count == 0)
                                        {
                                            // 第一行：显示键名
                                            std::string first_part = remaining.substr(0, VALUE_WIDTH);
                                            std::stringstream ss;
                                            ss << std::setw(KEY_WIDTH) << std::left << key << " = " << std::setw(VALUE_WIDTH) << std::left << first_part;
                                            lines.push_back(ss.str());
                                            remaining = remaining.substr(std::min(VALUE_WIDTH, remaining.length()));
                                        }
                                        else
                                        {
                                            // 后续行：键名位置留空，等号对齐
                                            std::string continuation = remaining.substr(0, VALUE_WIDTH);
                                            std::stringstream ss;
                                            ss << std::string(KEY_WIDTH, ' ') << "   " << std::setw(VALUE_WIDTH) << std::left << continuation;
                                            lines.push_back(ss.str());
                                            remaining = remaining.substr(std::min(VALUE_WIDTH, remaining.length()));
                                        }
                                        line_count++;
                                    }

                                    // 如果还有剩余内容，最后一行添加省略号
                                    if (!remaining.empty())
                                    {
                                        if (lines.size() > 0)
                                        {
                                            std::string &last_line = lines.back();
                                            // 移除最后一行的空白，添加省略号
                                            size_t last_non_space = last_line.find_last_not_of(' ');
                                            if (last_non_space != std::string::npos && last_non_space + 4 < last_line.length())
                                            {
                                                last_line = last_line.substr(0, last_non_space + 1) + "...";
                                                last_line.resize(KEY_WIDTH + 3 + VALUE_WIDTH, ' '); // 确保宽度一致
                                            }
                                        }
                                    }
                                }
                                processed_specific_options.push_back(lines);
                            }

                            // 按组显示特定方法选项
                            for (size_t opt_idx = 0; opt_idx < processed_specific_options.size(); opt_idx += NUM_COLUMNS)
                            {
                                // 找出这一组中最大的行数
                                size_t max_lines_in_group = 0;
                                for (size_t col = 0; col < NUM_COLUMNS && opt_idx + col < processed_specific_options.size(); ++col)
                                {
                                    max_lines_in_group = std::max(max_lines_in_group, processed_specific_options[opt_idx + col].size());
                                }

                                // 显示这一组的所有行
                                for (size_t line_idx = 0; line_idx < max_lines_in_group; ++line_idx)
                                {
                                    std::string line_content;
                                    for (size_t col = 0; col < NUM_COLUMNS; ++col)
                                    {
                                        size_t option_index = opt_idx + col;
                                        if (option_index < processed_specific_options.size())
                                        {
                                            const auto &option_lines = processed_specific_options[option_index];
                                            if (line_idx < option_lines.size())
                                            {
                                                if (col == 0)
                                                {
                                                    // 第一列：直接添加内容，然后填充到固定宽度
                                                    line_content += option_lines[line_idx];
                                                    if (option_lines[line_idx].length() < COLUMN_WIDTH)
                                                    {
                                                        line_content += std::string(COLUMN_WIDTH - option_lines[line_idx].length(), ' ');
                                                    }
                                                }
                                                else
                                                {
                                                    // 第二列：直接添加内容，不需要额外填充
                                                    line_content += option_lines[line_idx];
                                                }
                                            }
                                            else
                                            {
                                                if (col == 0)
                                                {
                                                    line_content += std::string(COLUMN_WIDTH, ' '); // 填充空白
                                                }
                                                // 第二列为空时不添加任何内容
                                            }
                                        }
                                        else
                                        {
                                            if (col == 0)
                                            {
                                                line_content += std::string(COLUMN_WIDTH, ' '); // 填充空白
                                            }
                                            // 第二列为空时不添加任何内容
                                        }
                                    }

                                    // 确保行内容精确填充到边框宽度
                                    if (line_content.length() < CONTENT_WIDTH)
                                    {
                                        line_content += std::string(CONTENT_WIDTH - line_content.length(), ' ');
                                    }
                                    else if (line_content.length() > CONTENT_WIDTH)
                                    {
                                        line_content = line_content.substr(0, CONTENT_WIDTH);
                                    }

                                    std::cout << "│ " << line_content << " │\n";
                                }
                            }
                        }
                        std::cout << "└" << bottom_dash_line << "┘\n";
                    }
                }
                else
                {
                    std::string no_specific_title = "特定方法配置";
                    size_t no_specific_title_len = no_specific_title.length() + 4; // "┌─ " + title + " "
                    size_t no_specific_dash_count = (no_specific_title_len < TOTAL_WIDTH - 1) ? (TOTAL_WIDTH - 1 - no_specific_title_len) : 0;
                    // 创建"无特定方法配置"标题的边框
                    std::string no_specific_dash_line;
                    for (size_t i = 0; i < no_specific_dash_count; ++i)
                    {
                        no_specific_dash_line += "─";
                    }
                    std::cout << "\n┌─ " << no_specific_title << " " << no_specific_dash_line << "┐\n";
                    std::cout << "│ " << std::setw(CONTENT_WIDTH) << std::left << "(无特定方法配置)" << " │\n";
                    std::cout << "└" << bottom_dash_line << "┘\n";
                }

                std::cout << "\n"
                          << std::endl;
            }

            // 配置文件路径
            std::string config_path_;

            // 辅助函数：初始化配置文件路径并加载配置
            void InitializeDefaultConfigPath(const std::string &specific_method = "")
            {
                std::vector<std::string> config_paths;
                bool config_loaded = false;

                // 1. 首先检查可执行程序相对路径下的配置文件（最高优先级）
                std::filesystem::path exe_path;
#ifdef _WIN32
                char path_buffer[MAX_PATH] = {0}; // Renamed to avoid conflict with 'path' variable if any
                GetModuleFileNameA(NULL, path_buffer, MAX_PATH);
                exe_path = std::filesystem::path(path_buffer);
#elif defined(__APPLE__) // macOS specific
                char path_buffer[PATH_MAX];
                uint32_t size = sizeof(path_buffer);
                if (_NSGetExecutablePath(path_buffer, &size) == 0)
                {
                    exe_path = std::filesystem::path(path_buffer);
                }
                else
                {
                    // Buffer too small, though PATH_MAX should be large enough for most cases.
                    // Handle error or allocate larger buffer if necessary.
                    PO_LOG_WARNING << "_NSGetExecutablePath buffer too small or failed." << std::endl;
                }
#else                    // Linux and other POSIX
                char path_buffer[PATH_MAX];
                ssize_t count = readlink("/proc/self/exe", path_buffer, PATH_MAX);
                if (count != -1)
                {
                    path_buffer[count] = '\0';
                    exe_path = std::filesystem::path(path_buffer);
                }
#endif

                // 获取可执行文件所在目录的父目录
                auto exe_parent_dir = exe_path.parent_path().parent_path();
                std::string local_config = (exe_parent_dir / "configs" / "methods" / (GetType() + ".ini")).string();
                PO_LOG(PO_LOG_VERBOSE) << "local_config: " << local_config << std::endl;
                if (std::filesystem::exists(local_config))
                {
                    config_paths.push_back(local_config);
                    config_path_ = local_config; // 保存最高优先级的路径
                    config_loaded = true;
                }

// 2. 检查构建目录配置（次优先级）
#ifdef POMVG_BUILD_CONFIG_DIR
                std::string build_config = std::string(POMVG_BUILD_CONFIG_DIR) +
                                           "/" + GetType() + ".ini";
                if (std::filesystem::exists(build_config))
                {
                    config_paths.push_back(build_config);
                    if (!config_loaded)
                    {
                        config_path_ = build_config;
                    }
                    config_loaded = true;
                }
#endif

// 3. 检查安装目录配置（最低优先级）
#ifdef POMVG_INSTALL_CONFIG_DIR
                std::string install_config = std::string(POMVG_INSTALL_CONFIG_DIR) +
                                             "/" + GetType() + ".ini";
                if (std::filesystem::exists(install_config))
                {
                    config_paths.push_back(install_config);
                    if (!config_loaded)
                    {
                        config_path_ = install_config;
                    }
                    config_loaded = true;
                }
#endif

                // 4. 按优先级顺序加载配置
                for (auto it = config_paths.rbegin(); it != config_paths.rend(); ++it)
                {
                    LoadMethodOptions(*it, specific_method);
                }

                // 5. 输出配置加载信息
                if (config_loaded)
                {
                    PO_LOG(PO_LOG_VERBOSE) << "Loaded configuration files in order (low to high priority):" << std::endl;
                    for (const auto &path : config_paths)
                    {
                        PO_LOG(PO_LOG_VERBOSE) << "  - " << path << std::endl;
                    }
                }
                else
                {
                    if (GetType() != "method_preset")
                    {
                        PO_LOG_WARNING << "No configuration file found for " << GetType() << std::endl;
                        PO_LOG(PO_LOG_VERBOSE) << "Searched locations:" << std::endl;
                        PO_LOG(PO_LOG_VERBOSE) << "  - " << local_config << std::endl;
#ifdef POMVG_BUILD_CONFIG_DIR
                        PO_LOG(PO_LOG_VERBOSE) << "  - " << POMVG_BUILD_CONFIG_DIR << "/" << GetType() << ".ini" << std::endl;
#endif
#ifdef POMVG_INSTALL_CONFIG_DIR
                        PO_LOG(PO_LOG_VERBOSE) << "  - " << POMVG_INSTALL_CONFIG_DIR << "/" << GetType() << ".ini" << std::endl;
#endif
                    }
                }
            }

            // 辅助函数：用于安全地将字符串转换为各种类型
        private:
            // 安全地将字符串转换为整数
            IndexT String2IndexT(const std::string &value, IndexT default_value = 0) const;

            // 安全地将字符串转换为浮点数
            float String2Float(const std::string &value, float default_value = 0.0f) const;

            // 安全地将字符串转换为双精度数
            double String2Double(const std::string &value, double default_value = 0.0f) const;

            // 将字符串转换为布尔值
            bool String2Bool(const std::string &str, bool default_value = false) const;

        public:
            // 设置配置文件路径
            void SetConfigPath(const std::string &config_path, const std::string &specific_method = "")
            {
                config_path_ = config_path;
                if (std::filesystem::exists(config_path_))
                {
                    LoadMethodOptions(config_path_, specific_method);
                }
            }

            // 构造函数中自动检查默认配置路径
            MethodPreset()
            {
                // InitializeDefaultConfigPath();
            }

            Package required_package_;
            // Add these accessor methods
            const Package &GetRequiredPackage() const { return required_package_; }
            Package &GetRequiredPackage() { return required_package_; }
            void SetRequiredPackage(const Package &package) { required_package_ = package; }
        };

        //=========================== Enhanced GetDataPtr ================================
        // Moved from interfaces.hpp and enhanced to handle DataPackage intelligently.
        // T is the actual data structure type, e.g., GlobalPoses, Tracks, etc.
        // This function should be placed after DataPackage is fully defined.
        // 增强版本：添加类型安全检查，确保请求的类型T与DataIO实际存储的类型匹配
        //================================================================================
        template <typename T>
        std::shared_ptr<T> GetDataPtr(
            const Interface::DataPtr &data_ptr_or_package_ptr,
            const std::string &data_name_in_package // Optional: name of data if first arg is a package
        )
        {
            if (!data_ptr_or_package_ptr)
            {
                std::cerr << "[Interface::GetDataPtr] Error: Input DataPtr (data_ptr_or_package_ptr) is null." << std::endl;
                return nullptr;
            }

            Interface::DataPtr actual_data_item_ptr; // This will point to the DataIO object holding T

            if (data_ptr_or_package_ptr->GetType() == "data_package")
            {
                if (data_name_in_package.empty())
                {
                    std::cerr << "[Interface::GetDataPtr] Error: Input is a DataPackage, but no 'data_name_in_package' was provided to extract specific data." << std::endl;
                    return nullptr;
                }

                std::shared_ptr<DataPackage> package_ptr = std::dynamic_pointer_cast<DataPackage>(data_ptr_or_package_ptr);
                if (!package_ptr)
                {
                    std::cerr << "[Interface::GetDataPtr] Error: Failed to cast DataPtr to DataPackagePtr even though GetType() is 'data_package'." << std::endl;
                    return nullptr;
                }
                actual_data_item_ptr = package_ptr->GetData(data_name_in_package);
                if (!actual_data_item_ptr)
                {
                    std::cerr << "[Interface::GetDataPtr] Error: Data item '" << data_name_in_package << "' not found in DataPackage." << std::endl;
                    return nullptr;
                }
            }
            else
            {
                if (!data_name_in_package.empty())
                {
                    // It's a warning, not an error, to provide a name for a non-package. The name is just ignored.
                    std::cout << "[Interface::GetDataPtr] Warning: 'data_name_in_package' (" << data_name_in_package
                              << ") was provided, but input DataPtr is not a DataPackage (actual type: "
                              << data_ptr_or_package_ptr->GetType() << "). The name will be ignored." << std::endl;
                }
                actual_data_item_ptr = data_ptr_or_package_ptr;
            }

            if (!actual_data_item_ptr)
            {
                // This case should ideally be caught by specific checks above if data was from a package
                std::cerr << "[Interface::GetDataPtr] Error: actual_data_item_ptr is null before attempting to retrieve internal data." << std::endl;
                return nullptr;
            }

            void *raw_internal_data_ptr = actual_data_item_ptr->GetData();
            if (!raw_internal_data_ptr)
            {
                std::cerr << "[Interface::GetDataPtr] Error: actual_data_item_ptr->GetData() returned null for data item of type '"
                          << actual_data_item_ptr->GetType() << "'." << std::endl;
                return nullptr;
            }

            // Assuming DataIO derived classes correctly return T* from GetData() when T is the internal structure type.
            T *typed_internal_data_ptr = static_cast<T *>(raw_internal_data_ptr);

            // Use the aliasing constructor of std::shared_ptr.
            // This creates a shared_ptr to 'typed_internal_data_ptr' but shares ownership with 'actual_data_item_ptr'.
            // This ensures that the underlying DataIO object (and thus its internal data) remains alive
            // as long as the returned shared_ptr<T> or any copy of actual_data_item_ptr exists.
            return std::shared_ptr<T>(actual_data_item_ptr, typed_internal_data_ptr);
        }

        //=========================== BehaviorPreset ===============================
        // # Copyright (c) 2021 PO tools author: Qi Cai.
        //
        //==========================================================================

        INTERFACE BehaviorPreset : virtual public Behavior
        {
        public:
            // ---- User Overwrite Functions ----
            //
            virtual const std::string &GetType() const override = 0;
            virtual const std::string &GetProductType() const override = 0;
            virtual DataPtr Run() = 0;

            // ----------------------------------

        public:
            virtual ~BehaviorPreset() override = default;
            void SetOptionsFromConfigFile(const std::string &path,
                                          const std::string &file_type);
            DataPtr Build(const DataPtr &material_ptr = nullptr) override;

        protected:
            // control the method modules used in behavior
            std::vector<MethodType> sequential_methods_;

            // 支持并行，如果是同级别method，则并行运行，不同级别采用序列运行（后续版本）
            // std::map<std::string, MethodType> parallel_methods_;

            // control the options used in method
            ConfigurationTools config_tools_;
        };

        typedef std::shared_ptr<BehaviorPreset> BehaviorPresetPtr;
    }
}
#endif
