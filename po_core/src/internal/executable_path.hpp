#pragma once

#include <string>
#include <filesystem>

namespace PoSDK
{

    /**
     * @brief 跨平台可执行文件路径工具类
     * @details 提供获取当前可执行文件路径的跨平台实现
     */
    class ExecutablePath
    {
    public:
        /**
         * @brief 获取当前可执行文件的完整路径
         * @return 可执行文件的完整路径，失败时返回空字符串
         */
        static std::string GetExecutableFilePath();

        /**
         * @brief 获取当前可执行文件所在的目录路径
         * @return 可执行文件所在目录的路径，失败时返回空字符串
         */
        static std::string GetExecutableDirectory();

        /**
         * @brief 获取相对于可执行文件目录的路径
         * @param relative_path 相对路径（例如 "../python/script.py"）
         * @return 解析后的绝对路径，失败时返回空字符串
         */
        static std::string GetRelativeToExecutable(const std::string &relative_path);

        /**
         * @brief 检查相对于可执行文件的路径是否存在
         * @param relative_path 相对路径
         * @return 如果路径存在返回true，否则返回false
         */
        static bool ExistsRelativeToExecutable(const std::string &relative_path);

    private:
        /**
         * @brief 内部实现：获取可执行文件路径的平台特定代码
         * @return 可执行文件路径，失败时返回空路径
         */
        static std::filesystem::path GetExecutablePathImpl();
    };

} // namespace PoSDK