#ifndef _POMVG_PLUGIN_REGISTER_
#define _POMVG_PLUGIN_REGISTER_
#include <memory>
#include <vector>
#include <unordered_map>
#include <iostream>
#include <string>

#include "inifile.hpp"
#include <po_core/interfaces.hpp>

#if defined(_WIN32) || defined(_WIN64)
#include <windows.h>
#define DLL_LOAD_LIB(X, Y) LoadLibrary(X)
#define DLL_GET_FCN(X, Y) GetProcAddress((HMODULE)X, Y)
#else
#ifdef __linux
#include <dlfcn.h>
#define DLL_LOAD_LIB(X, Y) dlopen(X, Y)
#define DLL_GET_FCN(X, Y) dlsym(X, Y)
#endif
#endif

namespace PoSDK
{
    namespace Plugin
    {

        using namespace Interface;

        typedef const std::string &(*PluginGetTypeFcn)();

#ifndef REGISTRATION_PLUGIN
#define REGISTRATION_PLUGIN(X, PLUGIN_NAME)                  \
    namespace                                                \
    {                                                        \
        static const std::string &kPluginType = PLUGIN_NAME; \
    }                                                        \
    extern "C" std::shared_ptr<void> PluginRegistration()    \
    {                                                        \
        auto instance = std::make_shared<X>();               \
        return instance;                                     \
    }                                                        \
    extern "C" const std::string &PluginGetType()            \
    {                                                        \
        return kPluginType;                                  \
    }
#endif

    }
}
#endif
