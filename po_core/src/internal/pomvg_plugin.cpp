#include "pomvg_plugin.hpp"
#include <filesystem>
#include <regex>
#include <iostream>
#include <mutex>
#include <set>

namespace PoSDK
{
    namespace Plugin
    {

#ifdef DEBUG_MEMORY
#define LOG_PLUGIN_MEMORY(msg, ...) \
    std::cout << "[PluginMemory] " << msg << std::endl
#else
#define LOG_PLUGIN_MEMORY(msg, ...)
#endif

        //=========================== FindPluginPath Fcn ==================================
        // # Copyright (c) 2021 PO tools author: Qi Cai.
        // function version 0.1.1: only detect plugin in first subdirectory folder
        //====================================================================================
        // 查找插件路径
        void FindPluginPath(const std::string &folder,
                            const std::string &regex,
                            std::vector<std::string> &plugins_path)
        {
            std::regex pattern(regex);
            std::vector<std::string> dirs;

            // step.1 build all including directories (only 1-order)
            for (const auto &entry : std::filesystem::directory_iterator(folder))
            {
                if (entry.is_directory())
                {
                    dirs.push_back(entry.path().string());
                }
            }
            dirs.push_back(folder); // add root folder

            // step.2 find plugin libraries in all directories
            for (const auto &dir : dirs)
            {
                for (const auto &entry : std::filesystem::directory_iterator(dir))
                {
                    if (entry.is_regular_file() && std::regex_match(entry.path().filename().string(), pattern))
                    {
                        plugins_path.push_back(entry.path().string());
                    }
                }
            }
        }

        //=========================== PluginManager Section ==================================
        // # Copyright (c) 2021 PO tools author: Qi Cai.
        //====================================================================================

        void PluginManager(const PluginLibPath &path,
                           const std::string &regex,
                           MapPlugin &map_plugin)
        {
            std::vector<std::string> path_libs;

            std::cout << "<PluginManager> plugin msg: detect plugins" << std::endl;
            std::cout << "<PluginManager> plugin msg: regex format = " << regex << std::endl;
            std::cout << "<PluginManager> plugin msg: plugin_path = " << path << std::endl;
            FindPluginPath(path, regex, path_libs);

            if (path_libs.empty())
            {
                std::cout << "##warning <PluginManager> msg: no plugins found!" << std::endl;
                return;
            }

            for (const auto &lib_path : path_libs)
            {
                void *plugin_handle = PluginHandleManager::Instance().AddHandle(lib_path);
                if (!plugin_handle)
                {
                    std::cerr << "##warning <PluginManager> msg: cannot open library! path = "
                              << lib_path << std::endl;
                    continue;
                }

                PluginGetTypeFcn get_type_fcn = (PluginGetTypeFcn)DLL_GET_FCN(plugin_handle, "PluginGetType");
                if (!get_type_fcn)
                {
                    std::cerr << "##warning <PluginManager> msg: can't find PluginGetType function"
                              << std::endl;
                    continue;
                }

                // 获取插件类型并转换为小写
                std::string plugin_type = get_type_fcn();
                std::transform(plugin_type.begin(), plugin_type.end(),
                               plugin_type.begin(), ::tolower);

                std::cout << "<PluginManager> msg: registered plugin_type = " << plugin_type << std::endl;
                map_plugin[plugin_type] = lib_path;
            }
            std::cout << "<PluginManager> msg: plugin size = " << map_plugin.size() << std::endl;
        }

        bool GetPlugin(const MapPlugin &map_plugin,
                       const std::string &type,
                       VoidPtr &void_ptr)
        {
            auto iter_plugin = map_plugin.find(type);
            if (iter_plugin == map_plugin.end())
            {
                std::cerr << "##warning <GetPlugin> msg: could not find type in plugins: "
                          << type << std::endl;
                return false;
            }

            void *plugin_handle = PluginHandleManager::Instance().AddHandle(iter_plugin->second);
            if (!plugin_handle)
            {
                std::cerr << "##warning <GetPlugin> msg: cannot open library! path = "
                          << iter_plugin->second << std::endl;
                return false;
            }

            auto register_fcn = (PluginRegistration)DLL_GET_FCN(plugin_handle, "PluginRegistration");
            if (!register_fcn)
            {
                std::cerr << "##warning <GetPlugin> msg: can't find PluginRegistration function"
                          << std::endl;
                return false;
            }

            void_ptr = register_fcn();
            if (!void_ptr)
            {
                std::cerr << "##warning <GetPlugin> msg: plugin registration failed" << std::endl;
                return false;
            }

            LOG_PLUGIN_MEMORY("Created object at " << void_ptr.get());
            return true;
        }

    }
}
