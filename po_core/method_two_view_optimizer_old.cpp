/**
 * @file method_TwoViewOptimizer.cpp
 * @brief 双视图位姿优化器实现
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include "method_TwoViewOptimizer.hpp"
#include "TwoViewOptimizer/TwoViewOptimizerBase.hpp"
#include "TwoViewOptimizer/EigenLMOptimizer.hpp"
#include "TwoViewOptimizer/CeresOptimizer.hpp"
#include <boost/algorithm/string.hpp>
#include "relative_pose.hpp"
#include "interfaces_robust_estimator.hpp"
#include <iostream>

namespace PoSDK
{

    MethodTwoViewOptimizer::MethodTwoViewOptimizer()
    {
        // 设置需要的输入数据包
        required_package_["data_sample"] = nullptr; // DataSample<BearingPairs>
        required_package_["data_map"] = nullptr;    // RelativePose初始估计

        // 初始化默认配置路径
        InitializeDefaultConfigPath();
    }

    DataPtr MethodTwoViewOptimizer::Run()
    {
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "TwoViewOptimizer started" << std::endl;
            DisplayConfigInfo();
        }

        // 1. 验证输入数据
        if (!ValidateInputData())
        {
            PO_LOG_ERR << "Input data validation failed" << std::endl;
            return nullptr;
        }

        // 2. 从data_map获取初始位姿估计
        auto pose_data = required_package_["data_map"];
        auto initial_pose_ptr = GetDataPtr<RelativePose>(pose_data);
        if (!initial_pose_ptr)
        {
            PO_LOG_ERR << "Failed to get initial RelativePose data" << std::endl;
            return nullptr;
        }
        RelativePose optimized_pose = *initial_pose_ptr;

        // 3. 转换bearing pairs到bearing vectors
        BearingVectors points1, points2;
        if (!ConvertBearingPairsToBearingVectors(points1, points2))
        {
            PO_LOG_ERR << "Failed to convert bearing pairs" << std::endl;
            return nullptr;
        }

        // 4. 检查是否有权重信息
        VectorXd *weights_ptr = nullptr;
        VectorXd weights;
        if (!prior_info_.empty() && prior_info_.find("weights") != prior_info_.end())
        {
            auto weights_data = std::dynamic_pointer_cast<DataMap<VectorXd>>(prior_info_["weights"]);
            if (weights_data && weights_data->GetMapPtr())
            {
                weights = *(weights_data->GetMapPtr());
                weights_ptr = &weights;
                PO_LOG(PO_LOG_VERBOSE) << "Using weights from prior_info_" << std::endl;
            }
        }

        // 5. 执行位姿优化
        if (!OptimizeRelativePose(points1, points2, optimized_pose, weights_ptr))
        {
            PO_LOG_ERR << "Failed to optimize relative pose" << std::endl;
            return nullptr;
        }

        // 6. 更新DataSample状态（优化后所有点都被认为是内点）
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (sample_ptr && !sample_ptr->empty())
        {
            auto inliers_ptr = std::make_shared<std::vector<size_t>>();
            inliers_ptr->reserve(sample_ptr->size());
            for (size_t i = 0; i < sample_ptr->size(); ++i)
            {
                inliers_ptr->push_back(i);
            }
            sample_ptr->SetBestInliers(inliers_ptr);
        }

        // 7. 返回优化后的位姿
        if (log_level_ >= PO_LOG_NORMAL)
        {
            PO_LOG(PO_LOG_NORMAL) << "TwoViewOptimizer completed successfully" << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Optimized rotation matrix:\n"
                                  << optimized_pose.Rij << std::endl;
            PO_LOG(PO_LOG_NORMAL) << "Optimized translation vector:\n"
                                  << optimized_pose.tij << std::endl;
        }

        return std::make_shared<DataMap<RelativePose>>(optimized_pose);
    }

    bool MethodTwoViewOptimizer::ValidateInputData()
    {
        auto sample_data = required_package_["data_sample"];
        auto pose_data = required_package_["data_map"];

        if (!sample_data || !pose_data)
        {
            PO_LOG_ERR << "Missing required input data" << std::endl;
            return false;
        }

        auto bearing_pairs_ptr = GetDataPtr<BearingPairs>(sample_data);
        if (!bearing_pairs_ptr || bearing_pairs_ptr->empty())
        {
            PO_LOG_ERR << "Empty bearing pairs data" << std::endl;
            return false;
        }

        if (bearing_pairs_ptr->size() < kMinNumPoints)
        {
            PO_LOG_ERR << "Insufficient points for optimization: got "
                       << bearing_pairs_ptr->size() << ", need at least " << kMinNumPoints << std::endl;
            return false;
        }

        return true;
    }

    bool MethodTwoViewOptimizer::ConvertBearingPairsToBearingVectors(
        BearingVectors &points1,
        BearingVectors &points2) const
    {
        auto sample_ptr = CastToSample<BearingPairs>(required_package_.at("data_sample"));
        if (!sample_ptr || sample_ptr->empty())
        {
            return false;
        }

        const size_t num_points = sample_ptr->size();
        points1.resize(3, num_points);
        points2.resize(3, num_points);

        size_t i = 0;
        for (const auto &bearing_pair : *sample_ptr)
        {
            points1.col(i) = bearing_pair.template head<3>();
            points2.col(i) = bearing_pair.template tail<3>();
            ++i;
        }

        return true;
    }

    bool MethodTwoViewOptimizer::OptimizeRelativePose(
        const BearingVectors &points1,
        const BearingVectors &points2,
        RelativePose &pose,
        const VectorXd *weights)
    {
        std::string optimizer_type = GetOptionAsString("optimizer_type", "eigen_lm");
        auto optimizer = CreateOptimizer(optimizer_type);

        if (!optimizer)
        {
            PO_LOG_ERR << "Failed to create optimizer: " << optimizer_type << std::endl;
            return false;
        }

        // 设置优化器参数
        optimizer->SetMaxIterations(GetOptionAsIndexT("max_iterations", 50));
        optimizer->SetConvergenceThreshold(GetOptionAsFloat("convergence_threshold", 1e-8));
        optimizer->SetVerbose(log_level_ >= PO_LOG_VERBOSE);

        std::string residual_type = GetResidualType();
        std::string loss_type = GetLossType();

        if (log_level_ >= PO_LOG_VERBOSE)
        {
            PO_LOG(PO_LOG_VERBOSE) << "Using optimizer: " << optimizer_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using residual: " << residual_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using loss function: " << loss_type << std::endl;
            PO_LOG(PO_LOG_VERBOSE) << "Using 5-parameter optimization (3 Cayley + 2 translation with ||t||=1 constraint)" << std::endl;
        }

        return optimizer->Optimize(points1, points2, pose, weights, residual_type, loss_type);
    }

    std::string MethodTwoViewOptimizer::GetResidualType() const
    {
        return GetOptionAsString("residual_type", "ppo_opengv");
    }

    std::string MethodTwoViewOptimizer::GetLossType() const
    {
        return GetOptionAsString("loss_type", "huber");
    }

    std::unique_ptr<TwoViewOptimizerBase> MethodTwoViewOptimizer::CreateOptimizer(const std::string &optimizer_type)
    {
        if (boost::iequals(optimizer_type, "eigen_lm"))
        {
            return std::make_unique<EigenLMOptimizer>();
        }
        else if (boost::iequals(optimizer_type, "gauss_newton"))
        {
            return std::make_unique<GaussNewtonOptimizer>();
        }
        else if (boost::iequals(optimizer_type, "dog_leg"))
        {
            return std::make_unique<DogLegOptimizer>();
        }
        else if (boost::iequals(optimizer_type, "ceres"))
        {
            return std::make_unique<CeresOptimizer>();
        }
        else
        {
            PO_LOG_ERR << "Unknown optimizer type: " << optimizer_type << std::endl;
            return nullptr;
        }
    }

} // namespace PoSDK
