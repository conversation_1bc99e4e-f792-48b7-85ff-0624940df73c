# 改进的多类型Note系统使用指南

## 概述

基于用户反馈，我们将多类型Note系统的数据结构从 `std::vector<std::unordered_map<std::string, std::string>>` 改进为 `std::unordered_map<std::string, std::vector<std::string>>`，使其更加直观和易用。

## 新的数据结构

### EvaluatorStatus 结构变化

**原结构：**
```cpp
std::vector<std::unordered_map<std::string, std::string>> note_data;
```

**新结构：**
```cpp
std::unordered_map<std::string, std::vector<std::string>> note_data;
```

### 优势

1. **更直观**：每个note类型对应一个字符串向量
2. **更便利**：可以直接使用 `emplace_back()` 批量添加
3. **更高效**：避免了嵌套结构的复杂操作
4. **更易维护**：数据组织更清晰

## 新的API使用方式

### 1. 基本添加方式

```cpp
EvaluatorStatus eval_status;

// 方式1：先添加结果，再添加note
eval_status.AddResult("rotation_error_deg", 1.23);
eval_status.SubmitNoteMsg("view_pairs", "(0,1)");

// 方式2：同时添加结果和单个note
eval_status.AddResult("rotation_error_deg", 1.23, "view_pairs", "(0,1)");

// 方式2b：使用初始化列表语法（更简洁）
eval_status.AddResult("rotation_error_deg", 1.23, {"view_pairs", "(0,1)"});

// 方式3：同时添加结果和多个note（兼容旧版本）
std::unordered_map<std::string, std::string> notes = {
    {"view_pairs", "(0,1)"},
    {"view_i", "0"},
    {"view_j", "1"}
};
eval_status.AddResult("rotation_error_deg", 1.23, notes);
```

### 2. 批量操作（推荐方式）

```cpp
EvaluatorStatus eval_status;

// 预先批量填充note数据
for (size_t i = 0; i < data_size; i++) {
    eval_status.note_data["view_pairs"].emplace_back("(" + std::to_string(i) + "," + std::to_string(j) + ")");
    eval_status.note_data["view_i"].emplace_back(std::to_string(i));
    eval_status.note_data["view_j"].emplace_back(std::to_string(j));
}

// 批量添加评估结果
for (size_t i = 0; i < data_size; i++) {
    eval_status.AddResult("rotation_error_deg", rotation_errors[i]);
    eval_status.AddResult("translation_error_deg", translation_errors[i]);
}
```

### 3. DataRelativePoses 示例

**更新前的代码：**
```cpp
for (size_t i = 0; i < rotation_errors.size(); i++) {
    std::unordered_map<std::string, std::string> note_data;
    note_data["view_pairs"] = "(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")";
    note_data["view_i"] = std::to_string(relative_poses_[i].i);
    note_data["view_j"] = std::to_string(relative_poses_[i].j);

    eval_status.AddResult("rotation_error_deg", rotation_errors[i], note_data);
    eval_status.AddResult("translation_error_deg", translation_errors[i], note_data);
}
```

**更新后的代码：**
```cpp
// 预先填充note数据（批量操作）
for (size_t i = 0; i < rotation_errors.size(); i++) {
    eval_status.note_data["view_pairs"].emplace_back("(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")");
    eval_status.note_data["view_i"].emplace_back(std::to_string(relative_poses_[i].i));
    eval_status.note_data["view_j"].emplace_back(std::to_string(relative_poses_[i].j));
}

// 添加评估结果（无需在循环中处理note）
for (size_t i = 0; i < rotation_errors.size(); i++) {
    eval_status.AddResult("rotation_error_deg", rotation_errors[i]);
    eval_status.AddResult("translation_error_deg", translation_errors[i]);
}
```

## 数据验证规则

新的验证规则更加简单：

```cpp
bool ValidateNoteData() const {
    size_t eval_results_size = eval_results.size();
    
    for (const auto &[note_type, note_values] : note_data) {
        if (!note_values.empty()) {
            // note向量的长度要么是1（所有结果共享），要么等于eval_results的长度（一对一对应）
            if (note_values.size() != 1 && note_values.size() != eval_results_size) {
                // 验证失败
                return false;
            }
        }
    }
    return true;
}
```

## CSV导出兼容性

新的数据结构完全兼容现有的CSV导出功能：

```cpp
// 导出时仍然支持选择特定的note类型
EvaluatorManager::ExportAllRawValuesToCSV("RelativePoses", output_dir, "view_pairs|view_i|view_j");
```

CSV输出格式保持不变：
```
Algorithm,EvalCommit,Value,view_pairs,view_i,view_j
method_A,default,1.23,"(0,1)","0","1"
method_A,default,2.45,"(0,2)","0","2"
```

## 性能改进

1. **减少内存分配**：避免了嵌套unordered_map的重复创建
2. **提高访问效率**：直接通过note类型访问对应向量
3. **简化代码逻辑**：减少了循环中的复杂操作

## 迁移指南

### 现有代码迁移

1. **识别循环中的note创建模式**
2. **将note创建提取到单独的循环中**
3. **使用直接的emplace_back操作**
4. **简化AddResult调用**

### 示例迁移

**迁移前：**
```cpp
for (size_t i = 0; i < data.size(); i++) {
    std::unordered_map<std::string, std::string> notes;
    notes["type1"] = value1[i];
    notes["type2"] = value2[i];
    eval_status.AddResult("metric", data[i], notes);
}
```

**迁移后：**
```cpp
// 批量填充note数据
for (size_t i = 0; i < data.size(); i++) {
    eval_status.note_data["type1"].emplace_back(value1[i]);
    eval_status.note_data["type2"].emplace_back(value2[i]);
}

// 批量添加结果
for (size_t i = 0; i < data.size(); i++) {
    eval_status.AddResult("metric", data[i]);
}
```

## 总结

新的多类型Note系统设计更加直观和高效：

1. **数据结构更清晰**：`unordered_map<string, vector<string>>`
2. **API更简洁**：支持批量操作和直接访问
3. **性能更优**：减少了不必要的内存分配和复杂操作
4. **兼容性保持**：CSV导出和验证功能完全兼容
5. **易于维护**：代码逻辑更清晰，错误更少 