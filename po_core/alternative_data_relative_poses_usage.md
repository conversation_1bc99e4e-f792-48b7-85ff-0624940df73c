# DataRelativePoses 多种接口使用示例

## 当前实现（使用标准接口）

```cpp
// 将所有误差全部添加到EvaluatorStatus中，使用规范的多类型Note接口
if (!rotation_errors.empty() && !translation_errors.empty())
{
    for (size_t i = 0; i < rotation_errors.size(); i++)
    {
        std::string view_pairs_msg = "(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")";
        std::string view_i_msg = std::to_string(relative_poses_[i].i);
        std::string view_j_msg = std::to_string(relative_poses_[i].j);

        // 使用规范接口添加旋转误差和多类型note
        std::unordered_map<std::string, std::string> notes = {
            {"view_pairs", view_pairs_msg},
            {"view_i", view_i_msg},
            {"view_j", view_j_msg}
        };
        eval_status.AddResult("rotation_error_deg", rotation_errors[i], notes);

        // 使用规范接口添加平移误差和多类型note
        eval_status.AddResult("translation_error_deg", translation_errors[i], notes);
    }
}
```

## 备选实现1：使用单个note接口

```cpp
// 使用单个note类型的简洁接口
if (!rotation_errors.empty() && !translation_errors.empty())
{
    for (size_t i = 0; i < rotation_errors.size(); i++)
    {
        std::string view_pairs_msg = "(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")";

        // 使用规范接口添加旋转误差和note
        eval_status.AddResult("rotation_error_deg", rotation_errors[i], {"view_pairs", view_pairs_msg});
        
        // 使用规范接口添加平移误差和note
        eval_status.AddResult("translation_error_deg", translation_errors[i], {"view_pairs", view_pairs_msg});
    }
}
```

## 备选实现2：混合使用多种接口

```cpp
// 混合使用不同的添加方式
if (!rotation_errors.empty() && !translation_errors.empty())
{
    for (size_t i = 0; i < rotation_errors.size(); i++)
    {
        std::string view_pairs_msg = "(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")";
        std::string view_i_msg = std::to_string(relative_poses_[i].i);
        std::string view_j_msg = std::to_string(relative_poses_[i].j);

        // 添加旋转误差，使用主要的view_pairs note
        eval_status.AddResult("rotation_error_deg", rotation_errors[i], {"view_pairs", view_pairs_msg});
        
        // 为刚添加的结果补充额外的note信息
        eval_status.SubmitNoteMsg("view_i", view_i_msg);
        eval_status.SubmitNoteMsg("view_j", view_j_msg);

        // 添加平移误差，使用完整的多类型note
        std::unordered_map<std::string, std::string> notes = {
            {"view_pairs", view_pairs_msg},
            {"view_i", view_i_msg},
            {"view_j", view_j_msg}
        };
        eval_status.AddResult("translation_error_deg", translation_errors[i], notes);
    }
}
```

## 备选实现3：批量操作（高性能版本）

```cpp
// 高性能批量操作（如果note数据重复率高）
if (!rotation_errors.empty() && !translation_errors.empty())
{
    // 方式3a：预填充note数据，然后批量添加结果
    for (size_t i = 0; i < rotation_errors.size(); i++)
    {
        eval_status.note_data["view_pairs"].emplace_back("(" + std::to_string(relative_poses_[i].i) + "," + std::to_string(relative_poses_[i].j) + ")");
        eval_status.note_data["view_i"].emplace_back(std::to_string(relative_poses_[i].i));
        eval_status.note_data["view_j"].emplace_back(std::to_string(relative_poses_[i].j));
    }

    // 批量添加评估结果
    for (size_t i = 0; i < rotation_errors.size(); i++)
    {
        eval_status.AddResult("rotation_error_deg", rotation_errors[i]);
        eval_status.AddResult("translation_error_deg", translation_errors[i]);
    }
}
```

## 接口对比分析

### 1. 标准多类型note接口

**优势：**
- 完整的note信息
- 类型安全
- 易于理解

**劣势：**
- 代码较长
- 需要构建临时map对象

### 2. 初始化列表接口

**优势：**
- 语法简洁
- 适合单一主要note类型
- 减少临时对象创建

**劣势：**
- 只能添加一个note类型
- 需要额外调用来添加其他note

### 3. 批量操作接口

**优势：**
- 高性能（减少函数调用）
- 适合大量数据
- 灵活的数据组织

**劣势：**
- 直接操作内部数据结构
- 需要手动维护数据一致性
- 不够"规范"

## 推荐使用方式

根据不同场景推荐使用不同的接口：

1. **简单场景（单一note类型）**：使用初始化列表接口
2. **复杂场景（多种note类型）**：使用标准多类型note接口
3. **高性能场景（大量数据）**：谨慎使用批量操作接口
4. **混合场景**：结合使用AddResult和SubmitNoteMsg 