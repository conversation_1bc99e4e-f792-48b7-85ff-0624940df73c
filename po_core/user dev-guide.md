# PoSDK 用户开发指南

## 1. 系统简介

PoSDK（原 PoMVG）是一个用于多视图几何处理和位姿估计的 C++ 库系统。该系统采用模块化的插件架构设计，允许用户通过开发自定义插件来扩展系统功能，而无需修改核心库代码。

### 1.1 系统架构

PoSDK 系统主要包含以下组件：

- **核心库 (`po_core_lib`)**: 提供基础数据结构、算法接口和插件框架
- **插件系统**: 允许用户二次开发、动态加载自定义算法和功能扩展

## 2. 环境准备

### 2.1 系统要求

- **操作系统**: Linux （后续支持macOS 或 Windows）
- **编译器**: 支持 C++17 标准的编译器 (GCC 7+, Clang 5+, MSVC 2019+)
- **构建工具**: CMake 3.15+

### 2.2 依赖项

- **必需依赖**:
  - Eigen3 (3.3+)
  - Boost (1.65+), 主要使用 filesystem 和 system 组件
  - Protobuf (3.6+)
  - GTest (1.10+)

- **可选依赖**（当前PoSDK必须，后续版本会考虑识别并变更为可选依赖）:
  - OpenCV (4.0+): 用于图像处理相关功能
  - OpenGV: 用于几何视觉计算

## 3. 使用预编译的 po_core_lib

PoSDK 设计为允许用户直接使用预编译的 po_core_lib 库，而无需了解其内部实现细节。

### 3.1 准备 po_core_lib

1. 将提供的预编译库和头文件放置在一个目录中，例如：
```
po_core_lib/
├── bin/              # 可执行文件目录
├── configs/          # 配置文件目录
│   └── methods/      # 方法配置文件
├── include/          # 头文件目录
│   ├── po_core/      # 核心库头文件
│   └── proto/        # Protobuf 生成的头文件
└── lib/              # 库文件目录
    └── cmake/        # CMake 配置文件
```

### 3.2 在 CMake 项目中集成 po_core_lib

在您的 `CMakeLists.txt` 中，按照如下方式集成 po_core_lib：

```cmake
option(USE_INTEGRATED_PO_CORE "Use integrated po_core instead of installed version" ON)
# 设置 po_core_lib 路径
set(po_core_folder "/path/to/po_core_lib" CACHE PATH "Path to po_core_lib directory")

# 链接到您的目标
target_link_libraries(your_target PRIVATE PoMVG::po_core)
```


## 4. 插件注册宏

所有插件都需要使用 `REGISTRATION_PLUGIN` 宏在对应的 `.cpp` 源文件末尾进行注册。

**`REGISTRATION_PLUGIN` 参数说明：**

- **第一个参数**: 插件的 **类名** (例如 `MyDataPlugin`, `MyMethod`)
- **第二个参数**: 插件的 **类型字符串** (例如 `"my_data_plugin"`, `"my_method"`)，这个字符串必须与类中 `GetType()` 方法返回的字符串完全一致。

!!! note 注意事项
    - `REGISTRATION_PLUGIN` **必须** 放置在 **源文件 (.cpp)** 中，**不能** 放置在头文件 (.hpp) 中，否则会导致链接错误。
    - 确保注册的类型字符串在整个系统中是唯一的。

**示例:**
```cpp
// 在 my_method.cpp 文件末尾
#include "pomvg_plugin_register.hpp" // 确保包含此头文件

// ... (MyMethod 类的实现) ...

REGISTRATION_PLUGIN(MyMethod, "my_method")
```

---

## 基础开发

本部分介绍 PoSDK 插件开发的基础知识，包括三种主要插件类型（Data, Method, Behavior）的核心接口、简单派生、配置和示例。

### 4. 数据插件 (Data Plugin)

数据插件负责数据的封装、加载、保存和访问。

#### 4.1 接口

##### 必须重载接口

- **`virtual const std::string& GetType() const override;`**
  - 返回插件的唯一类型字符串标识。**必须** 使用 `static const std::string` 实现。
  - **示例**: `"data_images"`, `"data_tracks"`
- **`virtual void* GetData() override;`**
  - 返回指向插件内部核心数据存储的 `void*` 指针。用户在使用 `GetDataPtr<T>` 时进行类型智能转换。

##### 可选重载接口

- **读写接口**：
```cpp
virtual bool Save(const std::string& folder = "", 
                const std::string& filename = "", 
                const std::string& extension = ".pb") override;
virtual bool Load(const std::string& filepath = "", 
                const std::string& file_type = "pb") override;
```
用户可以重载Save/Load方法，实现将数据保存到文件的逻辑。
  - **回调函数接口**：实现自定义的回调函数接口，用于外部调用插件的特定功能。
```cpp
virtual void Call(std::string func, ...);
```
  使用 `FUNC_INTERFACE_BEGIN`, `CALL`, `FUNC_INTERFACE_END` 宏来定义接口。例如：
```cpp
FUNC_INTERFACE_BEGIN(MyDataPlugin)
 // 对应函数原型 void SetData(std::vector<double>& data, int id, const std::string& name)
CALL(void, SetData, REF(std::vector<double>), VAL(int), STR())
FUNC_INTERFACE_END()
```
在 interfaces.hpp 中提供了参数类型宏 - 简化参数获取
```cpp
#define REF(type) (*va_arg(argp, type*)) // 获取指针/引用类型参数
#define VAL(type) va_arg(argp, type) // 获取基本类型参数
#define STR() std::string(va_arg(argp, const char*)) // 获取字符串类型参数
```

- **`virtual DataPtr CopyData() const override;`**
  - 实现数据的深拷贝逻辑，输出的DataPtr与原本DataPtr指向数据内容一样，地址无关。

#### 4.2 可选派生方式

- **虚基类**：**`Interface::DataIO`**
  - 提供最基本的数据插件接口。
- **序列化类**：**`Interface::PbDataIO`**
  - 提供基于 Protobuf 的序列化和反序列化支持。
  - 派生类需要实现 `CreateProtoMessage`, `ToProto`, `FromProto`。
  - 提供了 `PROTO_SET_*` 和 `PROTO_GET_*` 系列宏简化序列化代码。
  - 自动处理文件保存 (`Save`) 和加载 (`Load`)。
- **映射类**：`DataMap<T>`
  - 提供数据映射功能的接口，用户可以作为临时的DataPtr数据类型使用，不用每次都要写单独的数据插件。
  - 提供 `GetDataPtr<T>` 方法获取映射的值。


#### 4.3 示例

```cpp
// my_data_plugin.hpp
#include <po_core.hpp>
#include <vector>

namespace MyPlugin {
using namespace PoMVG;
using namespace Interface;

struct CustomData {
    int id;
    std::vector<double> values;
};

class MyDataPlugin : public DataIO {
private:
    CustomData data_;
public:
    const std::string& GetType() const override {
        static const std::string type = "my_custom_data";
        return type;
    }
    void* GetData() override {
        return static_cast<void*>(&data_);
    }
    // 可选：实现 Save/Load/Call/CopyData
};
} // namespace MyPlugin

// my_data_plugin.cpp
#include "my_data_plugin.hpp"
#include "pomvg_plugin_register.hpp"

REGISTRATION_PLUGIN(MyPlugin::MyDataPlugin, "my_custom_data")
```

#### 4.4 常用数据类型

核心库提供了多种预定义的数据类型，可以直接在插件中使用或作为参考（详见 `po_core/types.hpp`）：

-   **基础类型**:
    -   `IndexT`: 索引类型 (`uint32_t`)
    -   `ViewId`: 视图 ID (`uint32_t`)
    -   `PtsId`: 点/轨迹 ID (`uint32_t`)
    -   `Size`: 尺寸类型 (`uint32_t`)
-   **Eigen 类型**:
    -   `Vector2d`, `Vector3d`, `VectorXd`: 列向量
    -   `Matrix3d`, `MatrixXd`: 矩阵
    -   `SpMatrix`: 稀疏矩阵
-   **特征与匹配**:
    -   `Feature`: 基础 2D 特征点 (`Vector2d`)
    -   `FeaturePoint`: 包含坐标、大小、角度、描述子的完整特征点。
    -   `ImageFeatureInfo`: 单张图像的特征信息 (路径, `std::vector<FeaturePoint>`, 使用标记)。
    -   `FeaturesInfo`: 所有图像特征的集合 (`std::vector<ImageFeatureInfo>`)。
    -   `IdMatch`: 单个特征匹配 (索引 `i`, 索引 `j`, 内点标记)。
    -   `IdMatches`: `std::vector<IdMatch>`。
    -   `ViewPair`: 视图对 (`std::pair<IndexT, IndexT>`)。
    -   `Matches`: 所有视图对之间的匹配 (`std::map<ViewPair, IdMatches>`)。
-   **轨迹与观测**:
    -   `ObsInfo`: 单个观测信息 (视图ID, 点ID, 观测ID, 2D坐标, 使用标记)。
    -   `Track`: 单条轨迹，`std::vector<ObsInfo>`。
    -   `TrackInfo`: 包含轨迹 (`Track`) 和使用标记。
    -   `Tracks`: 所有轨迹的集合 (`std::vector<TrackInfo>`), 包含归一化状态。
-   **位姿**:
    -   `RelativeRotation`: 相对旋转 (视图 `i`, `j`, 旋转矩阵 `Rij`, 权重)。
    -   `RelativeRotations`: `std::vector<RelativeRotation>`。
    -   `RelativePose`: 相对位姿 (视图 `i`, `j`, 旋转 `Rij`, 平移 `tij`, 权重)。
    -   `RelativePoses`: `std::vector<RelativePose>`。
    -   `GlobalRotations`: 全局旋转矩阵 (`std::vector<Matrix3d>`)。
    -   `GlobalTranslations`: 全局平移向量 (`std::vector<Vector3d>`)。
    -   `GlobalPoses`: 全局位姿 (包含 `rotations`, `translations`, `est_info`, `pose_format`)。
-   **相机模型**:
    -   `CameraIntrinsics`: 相机内参 (fx, fy, cx, cy, 畸变参数等)。
    -   `CameraModel`: 完整相机模型 (内参, 制造商, 型号等)。
    -   `CameraModels`: `std::vector<CameraModel>`。
-   **其他**:
    -   `ImagePaths`: 图像路径集合 (`std::vector<std::pair<std::string, bool>>`)
    -   `BearingVectors`: 归一化观测向量 (`Eigen::Matrix<double,3,Eigen::Dynamic>`)
    -   `BearingPairs`: 匹配的归一化观测向量对 (`std::vector<Eigen::Matrix<double,6,1>>`)

#### 4.5 数据配置

数据插件通常不使用独立的 `.ini` 配置文件进行参数设置。其配置和数据填充主要通过成员变量和以下方式进行：

1.  **`Load()` 方法**: 对于支持从文件加载的数据插件（如 `DataTracks`, `DataGlobalPoses`, `PbDataIO` 派生类），可以通过调用 `Load(filepath, file_type)` 来加载指定路径和类型的数据。
    ```cpp
    auto tracks_data = FactoryData::Create("data_tracks");
    tracks_data->Load("/path/to/your/tracks.tracks", "tracks");
    ```

2.  **`Call()` 回调接口**: 插件可以定义自己的成员函数，并通过 `Call()` 接口暴露出来，允许外部代码传递参数或触发特定操作。
    ```cpp
    // 假设 MyDataPlugin 有一个 SetValues(const std::vector<double>&) 方法
    auto my_data = FactoryData::Create("my_custom_data");
    std::vector<double> values = {1.0, 2.0, 3.0};
    // 注意：传递引用参数需要取地址
    my_data->Call("SetValues", &values);
    ```
    这需要插件内部使用 `FUNC_INTERFACE_BEGIN`, `CALL`, `FUNC_INTERFACE_END` 宏定义好对应的接口。

3.  **`SetStorageFolder()`** (仅限 `PbDataIO` 派生类):
    -   对于继承自 `PbDataIO` 的插件（支持 Protobuf 序列化），可以使用 `SetStorageFolder(path)` 方法设置默认的文件保存和加载目录。
    -   当调用 `Save()` 或 `Load()` 时不指定完整路径，插件会使用这个默认目录。
    ```cpp
    auto serializable_data = FactoryData::Create("my_serializable_data");
    auto pbdata_io = std::dynamic_pointer_cast<PbDataIO>(serializable_data);
    if (pbdata_io) {
        pbdata_io->SetStorageFolder("/path/to/default/storage"); // 设置默认存储路径
        // 如果设置了默认存储路径，则Save可以省略路径，会保存到默认目录
        serializable_data->Save("", "my_data_file", ".pb"); 
    }
    ```

4.  **直接成员函数调用**: 用户可以在创建实例后直接调用其公有成员函数来获取核心数据来进行算法设计。
    ```cpp
    auto camera_data = FactoryData::Create("data_camera_model");
    auto camera_models = GetDataPtr<CameraModels>(camera_data);
    if (camera_models) {
        CameraModel cam; 
        cam.SetCameraIntrinsics(/*...*/);
        camera_models->push_back(cam);
    }
    ```


---

### 5. 方法插件 (Method Plugin)

方法插件用于实现具体的算法逻辑和数据处理流程。

#### 5.1 接口

##### 必须重载接口

- **方法名称**：必须使用 `static const std::string` 实现
```cpp
virtual const std::string& GetType() const override;
// 返回插件的唯一类型字符串标识。
// 示例: `"method_sift"`, `"method_matches2tracks"`
```
- **算法主入口**:Build和Run函数二选一，推荐Run函数
```cpp
virtual DataPtr Build(const DataPtr& material_ptr = nullptr) override;
// 用于实现核心算法：
// （1）material_ptr 是可选的输入数据。如果方法需要多个输入，将它们打包在 `DataPackage` 中传入。
// （2）返回处理结果 (`DataPtr`)，如果处理失败或无结果，返回 `nullptr`。
```

```cpp
virtual DataPtr Run() override;
// 用于实现核心算法：基类的`Build` 方法会负责调用 `Run` 并处理输入/输出检查和性能分析等。
// （1）用户需要在构造函数中设置输入数据类型：required_package_["data_type"] = nullptr;
// （2）之后可以在Run函数中使用GetDataPtr<T>(required_package_["data_type"])来获取输入数据。
```

##### 可选重载接口 (针对 `MethodPreset` 及其派生类)

- **设置算法的先验信息：**
```cpp
virtual void SetPriorInfo(const DataPtr& data_ptr, const std::string& type = "");
// （1）`MethodPreset` 设置先验信息，用于需要额外指导信息（如初始位姿、权重）的算法。
// （2）`data_ptr` 是先验信息数据指针，`type` 是先验信息类型字符串。

virtual void ResetPriorInfo();
// 重置先验信息（清空所有先验信息）
```

#### 5.2 可选派生方式

- **`Interface::Method`** (基类)
  - 提供最基本的方法插件接口。需要自己处理输入检查、配置加载等。
- **`Interface::MethodPreset`** (派生自 `Method`)
  - 提供了预设的功能框架，简化开发：
    - **自动输入检查**: 基于 `required_package_` 成员变量检查输入数据类型。
    - **配置管理**: 支持通过 INI 文件配置 `method_options_`。
    - **先验信息**: 支持通过 `SetPriorInfo` 传递额外信息。
    - **核心逻辑分离**: 将核心算法实现在 `Run()` 中，`Build()` 负责流程控制。
- **`Interface::MethodPresetProfiler`** (派生自 `MethodPreset`)
  - 在 `MethodPreset` 基础上增加了**性能分析**功能。
  - 自动记录执行时间、内存使用，并可导出 CSV 报告。
  - 可以通过 `enable_profiling` 选项控制是否启用分析。
- **`Interface::RobustEstimator<TSample>`** (派生自 `MethodPresetProfiler`)
  - 模板类 (`RobustEstimator<TSample>`)，提供了鲁棒估计的框架 (如 RANSAC, GNC-IRLS)。
  - 需要配合模型估计器 (`model_estimator_ptr_`) 和代价评估器 (`cost_evaluator_ptr_`) 使用。
  - 输入数据**要求**是 `DataSample<TSample>` 类型。

#### 5.3 示例

```cpp
// my_method.hpp
#include <po_core.hpp>

namespace MyPlugin {
using namespace PoMVG;
using namespace Interface;
using namespace types; // 引入常用类型

class MyMethod : public MethodPresetProfiler { // 继承Profiler以获得性能分析
public:
    MyMethod();
    const std::string& GetType() const override;
    DataPtr Run() override; // 实现核心逻辑

    // 可选: 如果继承MethodPreset，可以重写GetInputTypes
    // const std::vector<std::string>& GetInputTypes() const override;
};
} // namespace MyPlugin

// my_method.cpp
#include "my_method.hpp"
#include "pomvg_plugin_register.hpp"

namespace MyPlugin {

MyMethod::MyMethod() {
    // 定义需要的输入数据类型
    required_package_["data_tracks"] = nullptr;
    required_package_["data_global_poses"] = nullptr;

    // 初始化默认选项 (会被配置文件覆盖)
    method_options_["threshold"] = "0.5";
    method_options_["max_iterations"] = "100";

    // 加载默认配置文件 (如果存在)
    InitializeDefaultConfigPath();
    // 初始化日志目录
    InitializeLogDir();
}

const std::string& MyMethod::GetType() const {
    static const std::string type = "my_method";
    return type;
}

DataPtr MyMethod::Run() {
    // 1. 获取输入数据 (MethodPreset已处理检查)
    auto tracks = GetDataPtr<Tracks>(required_package_["data_tracks"]);
    auto poses = GetDataPtr<GlobalPoses>(required_package_["data_global_poses"]);

    // 检查数据有效性
    if (!tracks || !poses) {
        std::cerr << "[" << GetType() << "] Error: Missing required input data." << std::endl;
        return nullptr;
    }

    // 2. 获取配置参数
    float threshold = GetOptionAsFloat(method_options_, "threshold", 0.5f);
    int max_iter = GetOptionAsIndexT(method_options_, "max_iterations", 100);

    std::cout << "[" << GetType() << "] Running with threshold=" << threshold
              << ", max_iterations=" << max_iter << std::endl;

    // 3. 实现核心算法逻辑...
    // ... process tracks and poses ...

    // 4. 创建并返回结果 (例如，返回处理后的位姿)
    // 注意: 如果不希望修改输入数据，可以先深拷贝DataPtr
    // auto result_poses_data = poses->CopyData(); // 假设DataGlobalPoses实现了CopyData
    // auto result_poses = GetDataPtr<GlobalPoses>(result_poses_data);
    // ... 修改 result_poses ...
    // return result_poses_data;

    // 或者创建新的数据对象返回
    auto result_data = std::make_shared<DataMap<std::string>>("Processing finished");
    return result_data;
}

} // namespace MyPlugin

// 注册插件
REGISTRATION_PLUGIN(MyPlugin::MyMethod, "my_method")
```

#### 5.4 常用数据类型

方法插件通常处理核心数据类型，如 `Tracks`, `Matches`, `GlobalPoses`, `RelativePoses`, `CameraModels` 等。输入和输出类型取决于具体算法，具体见 `types.hpp` 和后续章节。

#### 5.5 方法参数配置

- **设置默认值**: 
  (1) 在插件类的构造函数中，通过 `method_options_["param_name"] = "default_value";` 设置默认参数
  (2) 建议利用获取配置参数的函数来设置默认值，见 `types.hpp` 和后续章节。
- **配置文件 (.ini)**:
  - 在 `configs/methods/` 目录下创建与 `GetType()` 返回值同名的 `.ini` 文件 (例如 `my_method.ini`)。
  - 文件格式为标准的 INI 格式，包含一个与方法类型同名的节 `[my_method]`。
  - 在节下面定义 `key=value` 对。
  - `MethodPreset` 会自动加载此文件，配置文件中的值会覆盖构造函数中的默认值。
- **运行时设置**:
  - 可以通过 `SetMethodOptions(const MethodOptions& options)` 批量设置选项。
  - 可以通过 `SetMethodOption(const MethodParams& key, const ParamsValue& value)` 单独设置选项。
  - 运行时设置的优先级最高，会覆盖配置文件和默认值。
- **获取配置值**:
  - 在 `Run()` 或其他成员函数中，使用 `GetOptionAsString`, `GetOptionAsIndexT`, `GetOptionAsFloat`, `GetOptionAsBool` 等辅助函数安全地获取配置值，见 `types.hpp` 和后续章节。
- **参数设置的优先级**：运行时设置 > 配置文件 > 默认值

---

### 6. 行为插件 (Behavior Plugin)

行为插件是 `Method` 的一种特殊形式，通常用于封装一系列方法调用，形成一个特定的功能模块或工作流。

#### 6.1 接口

行为插件除了实现 `Method` 的**必须接口** (`GetType`, `Build`) 外，通常还会重载以下**可选接口**：

- **`virtual const std::string& GetMaterialType() const override;`**
  - 返回该行为期望的主要输入数据类型。
- **`virtual const std::string& GetProductType() const override;`**
  - 返回该行为最终生成的产品数据类型。
- **`virtual void SetOptionsFromConfigFile(const std::string& path, const std::string& file_type) override;`**
  - 如果需要，可以自定义当前行为所有方法配置的加载逻辑。

#### 6.2 可选派生方式

- **`Interface::Behavior`** (派生自 `Method`)
  - 提供行为插件的基础接口。
- **`Interface::BehaviorPreset`** (派生自 `Behavior`)
  - (似乎在当前代码中未完全实现或使用，但概念上存在) 可以提供类似 `MethodPreset` 的预设功能，如自动加载行为步骤、管理子方法配置等。

#### 6.3 示例

```cpp
// my_behavior.hpp
#include <po_core.hpp>

namespace MyPlugin {
using namespace PoMVG;
using namespace Interface;

class MyBehavior : public BehaviorPreset { // 继承 BehaviorPreset

public:
    MyBehavior();
    const std::string& GetType() const override;
    // Run() 通常在 BehaviorPreset 中不需要重载，Build 会处理流程
    // DataPtr Run() override; 
    const std::string& GetMaterialType() const override;
    const std::string& GetProductType() const override; 
};
} // namespace MyPlugin


// my_behavior.cpp
#include "my_behavior.hpp"
#include "pomvg_plugin_register.hpp"

namespace MyPlugin {

MyBehavior::MyBehavior() {
    // 定义行为包含的串行方法步骤
    sequential_methods_ = {
        "method_feature_extraction", 
        "method_matching", 
        "method_track_building"
    };
    

}

const std::string& MyBehavior::GetType() const {
    static const std::string type = "my_behavior";
    return type;
}

const std::string& MyBehavior::GetMaterialType() const {
    static const std::string type = "data_images"; // 假设输入是图像
    return type;
}

const std::string& MyBehavior::GetProductType() const {
    static const std::string type = "data_tracks"; // 假设输出是轨迹
    return type;
}

// Build 方法由 BehaviorPreset 基类提供，它会按 sequential_methods_ 顺序执行方法
// DataPtr MyBehavior::Build(const DataPtr& material_ptr) { ... }

} // namespace MyPlugin

REGISTRATION_PLUGIN(MyPlugin::MyBehavior, "my_behavior")
```

#### 6.4 常用数据类型

行为插件的输入 (`MaterialType`) 和输出 (`ProductType`) 取决于其封装的工作流程。

#### 6.5 行为参数配置

- **设置**：行为插件本身可以通过 `SetOptionsFromConfigFile` 加载配置（例如定义方法调用顺序）。
- **管理**：行为插件内部调用的每个方法，其配置通常由行为插件的 `method_options_`来管理和传递。
- **优先级**：行为插件的配置 > 方法插件的配置
---

## 深入开发

本部分将深入探讨 PoSDK 插件开发的高级主题。

### 7. 详细的插件派生选择

#### 7.1 数据插件 (Data Plugin)

-   **`Interface::DataIO`**:
    -   **用途**: 实现最基础的数据容器，或当数据不需要序列化/反序列化时使用。
    -   **优点**: 简单直接。
    -   **缺点**: 需要手动实现 `Save`/`Load`。
-   **`Interface::PbDataIO`**:
    -   **用途**: 需要将数据通过 Protobuf 进行序列化/反序列化（保存到文件或网络传输）的场景。
    -   **优点**: 自动处理文件 I/O；提供宏简化 Protobuf 字段映射。
    -   **缺点**: 引入 Protobuf 依赖；需要定义 `.proto` 文件并配置 CMake 生成代码。
    -   **关键实现**: 用户需要重载 `CreateProtoMessage`, `ToProto`, `FromProto`。推荐使用如下辅助宏（详情见 `interfaces_preset.hpp` 中）来简化实现：
    ```cpp
        -   `PROTO_SET_BASIC` / `PROTO_GET_BASIC`: 基础类型 (int, float, bool, string)。
        -   `PROTO_SET_VECTOR2F/D` / `PROTO_GET_VECTOR2F/D`: Eigen::Vector2f/d。
        -   `PROTO_SET_VECTOR3D` / `PROTO_GET_VECTOR3D`: Eigen::Vector3d。
        -   `PROTO_SET_MATRIX3D` / `PROTO_GET_MATRIX3D`: Eigen::Matrix3d。
        -   `PROTO_SET_ARRAY` / `PROTO_GET_ARRAY`: `std::vector<基础类型>`。
        -   `PROTO_SET_ENUM` / `PROTO_GET_ENUM`: 枚举类型。
    ```

-   **`Interface::DataMap<T>`**:
    -   **模板参数 `T`**: 任意可默认构造的类型。
    -   **用途**: 快速包装 **单个** 数据对象（如 `RelativePose`, `GlobalPoses`, `std::vector`, `std::map`, 自定义结构体等）为 `DataPtr`，方便在方法间传递或作为方法返回值，避免为简单数据结构创建完整的数据插件。
    -   **优点**: 方便将现有数据集成到 PoSDK 框架，不用专门声明和编译新的 Data 数据类。
    -   **缺点**: 本身不直接支持序列化 (`PbDataIO`)，如果需要持久化，需要包含它的插件（如 `DataPackage`）或外部逻辑来处理。
    -   **访问**: 使用 `GetDataPtr<T>(data_map_ptr)` 获取内部数据的指针。
    -   **示例**: 返回一个 `RelativePose` 对象
        ```cpp
        // In Method's Run() function
        RelativePose estimated_pose = CalculatePose(); // 假设得到一个 RelativePose 对象
        return std::make_shared<DataMap<RelativePose>>(estimated_pose);
        ```

-   **`Interface::DataSample<T>`**:
    -   **模板参数 `T`**: **必须** 是 `std::vector<ValueType>` 类型，`ValueType` 是样本元素的类型 (如 `BearingPair`, `IdMatch`)。
    -   **用途**: 专用于鲁棒估计器 (如 RANSAC, GNC-IRLS) 的样本数据管理。支持零拷贝（共享内存）的随机子集和索引子集生成。
    -   **优点**: 高效的子集采样，避免大数据拷贝；提供 STL 兼容接口 (`begin`, `end`, `operator[]`, `size` 等)，方便访问样本数据（包括子集）。
    -   **缺点**: 主要设计用于鲁棒估计流程，通用性不如 `DataMap` 或标准 `DataIO`。
    -   **关键接口**:详细请见 `interfaces_robust_estimator.hpp` 中 `DataSample` 的定义
    ```cpp
        GetPopulationPtr(): 获取总体样本数据指针
        GetPopulationSize(): 获取总体样本数据大小
        GetRandomSubset(size): 获取随机子集
        GetSubset(indices): 获取指定索引子集
        GetInlierSubset(indices): 获取内点子集
        ...
    ```
-   **`Interface::DataCosts`**: (后续可能考虑弃用，因为用`DataMap<double>`可以替代)
    -   **用途**: 存储由代价评估器 (`MethodRelativeCost` 等) 计算出的残差或代价值列表 (`std::vector<double>`)。
    -   **优点**: 标准化的代价存储容器；提供 `std::vector` 接口，方便访问和处理。
    -   **使用场景**: 通常作为代价评估器 (`CostEvaluator`) 的**输出**，用于 RANSAC 等鲁棒估计器的内点判断。
    -   **接口**: 提供了 `push_back`, `operator[]`, `size`, `empty`, `begin`, `end` 等 `std::vector<double>` 兼容接口。

-   **`Interface::DataPackage`**: 
    -   **用途**: 将多个不同的 `DataPtr` 对象打包成一个单一的 `DataPtr`，主要用于向 `Method::Build()` 或 `Behavior::Build()` 传递多个输入数据。（后续会配合开发并行和异构机制的method基类来支持发送任务驱动的消息响应包 /拆分子包并行加速算法运行的功能）
    -   **优点**: 简化了需要多输入的 `Method` 或 `Behavior` 的接口，只需传递一个 `DataPackagePtr`。
    -   **缺点**: 本身不提供序列化；需要通过类型字符串（键）来存取内部数据。
    -   **关键接口** (详见 `interfaces_preset.hpp`):
        -   **`AddData(const DataPtr& data_ptr)`**: 添加数据，使用数据的 `GetType()` 作为键。
        -   **`AddData(const Package& package)`**: 合并另一个 `DataPackage`。
        -  **`AddData(const DataType& alias_type, const DataPtr& data_ptr)`**: 使用自定义别名作为键添加数据。
        -   **`GetData(const DataType& type)`**: 根据类型字符串（键）获取内部的 `DataPtr`。
        -   **`GetPackage()`**: 获取当前Package的常量引用。
    -   **示例**: 
        ```cpp
        auto data_package = std::make_shared<DataPackage>();
        data_package->AddData(tracks_data); // 使用 "data_tracks" 作为键
        data_package->AddData("initial_poses", global_pose_data); // 使用别名 "initial_poses"
        
        // 在方法内部获取数据
        auto tracks = GetDataPtr<Tracks>(data_package->GetData("data_tracks"));
        auto poses = GetDataPtr<GlobalPoses>(data_package->GetData("initial_poses"));
        ```

#### 7.2 方法插件 (Method Plugin)

- **`Interface::Method`**:
  - **用途**: 实现独立的、不需要复杂配置或输入检查的简单算法。
  - **优点**: 最大灵活性，无额外框架约束。
  - **缺点**: 需要手动处理所有输入/输出、配置加载和错误检查。
- **`Interface::MethodPreset`**:
  - **用途**: **推荐**用于大多数方法插件开发。实现具有标准化输入/输出、配置管理和可选先验信息的算法。
  - **优点**: 简化开发流程，提供结构化框架；自动 INI 配置加载；自动输入数据检查；支持先验信息传递。
  - **缺点**: 引入少量框架约定（如 `Run()` vs `Build()`, `required_package_`）。
- **`Interface::MethodPresetProfiler`**:
  - **用途**: 需要对方法性能（执行时间、内存占用）进行分析和记录的场景。
  - **优点**: 自动性能数据采集和 CSV 导出；跨平台内存监控。
  - **缺点**: 增加少量性能开销（可通过 `enable_profiling` 配置禁用）。
- **`Interface::RobustEstimator<TSample>`**:
  - **用途**: 实现基于 RANSAC 或 GNC-IRLS 等鲁棒估计算法。
  - **优点**: 提供标准化的鲁棒估计流程；将模型估计和代价评估解耦。
  - **缺点**: 仅适用于特定类型的鲁棒估计问题；需要配合 `DataSample<TSample>` 输入。

#### 7.3 行为插件 (Behavior Plugin)

- **`Interface::Behavior`**:
  - **用途**: 封装一系列方法调用，形成一个完整的功能流或特定应用场景。
  - **优点**: 模块化组织复杂流程；定义清晰的输入（`MaterialType`）和输出（`ProductType`）。
  - **缺点**: 需要手动管理内部方法的创建、配置和数据传递。
- **`Interface::BehaviorPreset`**: (当前仅支持序列化方法调用)
  - **用途**: 可以提供更高级的行为管理，如基于配置自动编排方法、统一处理子方法配置等。

### 8. 数据映射与异常处理

#### 8.1 数据访问与类型安全 (`GetDataPtr`)

- **核心**: 使用 `Interface::GetDataPtr<T>(data_ptr)` 模板函数进行安全的数据类型转换。
- **优点**: 避免直接使用 `static_cast` 带来的类型风险；统一数据访问接口。
- **示例**:
  ```cpp
  // 在方法插件的 Run() 中
  auto tracks_data = required_package_["data_tracks"];
  auto tracks = Interface::GetDataPtr<Tracks>(tracks_data);
  if (!tracks) {//也可以不写，因为GetDataPtr会自动检查
      std::cerr << "错误：输入的 data_tracks 类型不正确或数据为空！" << std::endl;
      return nullptr;
  }
  // 现在可以安全使用 tracks 指针
  for (const auto& track_info : *tracks) {
      // ...
  }
  ```

#### 8.2 输入数据提供方式 (`MethodPreset` 派生类)

对于继承自 `MethodPreset` 或其派生类（如 `MethodPresetProfiler`, `RobustEstimator`）的方法插件，有以下几种方式可以提供所需的输入数据：

1.  **通过 `Build` 方法传入**: 
    -   在调用 `method_ptr->Build(data_ptr)` 时，可以将输入数据 `data_ptr` 直接传入。
    -   **前提**: 必须在插件的构造函数中，通过 `required_package_["data_type"] = nullptr;` 预先声明该方法所需的输入数据类型 (`data_type`)。
    -   如果需要传入多个不同类型的数据，应将它们打包在一个 `DataPackage` 中，然后将 `DataPackage` 指针传给 `Build`。
    ```cpp
    // 插件构造函数中声明
    required_package_["data_tracks"] = nullptr;
    required_package_["data_camera_model"] = nullptr;
    
    // 调用时
    auto data_package = std::make_shared<DataPackage>();
    data_package->AddData(tracks_data_ptr);
    data_package->AddData(camera_model_data_ptr);
    auto result = method_ptr->Build(data_package);
    ```

2.  **通过 `SetRequiredData` 方法设置**: 
    -   在调用 `Build` 之前，可以单独调用 `method_ptr->SetRequiredData(data_ptr)` 来设置输入数据。
    -   **前提**: 同样需要在插件的构造函数中声明所需的数据类型。
    -   如果需要设置多个数据，需要多次调用 `SetRequiredData`。
    ```cpp
    // 插件构造函数中声明
    required_package_["data_matches"] = nullptr;
    
    // 调用时
    method_ptr->SetRequiredData(matches_data_ptr);
    auto result = method_ptr->Build(); // Build 时可以不传参数
    ```

3.  **通过配置文件指定路径加载**: 
    -   可以在方法的 `.ini` 配置文件中，使用与 `required_package_` 中声明的数据类型 **同名的键** 来指定一个文件路径。
    -   `MethodPreset` 的 `Build` 方法在检查输入时，如果发现某个 `required_package_` 中的 `DataPtr` 仍为 `nullptr`，它会尝试在 `method_options_` 中查找同名键，并调用对应 `DataIO` 子类的 `Load()` 方法从该路径加载数据。
    -   **注意**: 这要求对应的数据插件正确实现了 `Load()` 方法。
    ```ini
    # 在 my_method.ini 中
    [my_method]
    data_tracks = /path/to/my_tracks.pb 
    ...
    ```
    ```cpp
    // 插件构造函数中声明
    required_package_["data_tracks"] = nullptr;
    
    // 调用时 (假设配置文件已加载)
    auto result = method_ptr->Build(); // 会自动尝试加载配置文件中指定的 data_tracks
    ```

4.  **通过 `SetPriorInfo` 方法传入 (用于先验信息)**:
    -   **目的**: 向方法插件传递额外的"指导"信息，这些信息不是核心输入数据，但可能影响算法行为（例如，RANSAC 的初始模型、优化的初始位姿估计、带权重的样本等）。
    -   **函数签名**: `void SetPriorInfo(const DataPtr& data_ptr, const std::string& type = "");`
        -   `data_ptr`: 包含先验信息的 `DataPtr`。
        -   `type` (可选): 一个字符串标识符，用于区分不同类型的先验信息。如果留空，则行为会有所不同（见下文）。
    -   **两种模式**:
        1.  **`type` 为空**: 此时 `data_ptr` **必须** 是一个 `DataPackagePtr`，支持一次性设置多种先验信息的情况。
        2.  **`type` 不为空**: 此时 `data_ptr` 可以是**任意** `DataPtr`。`SetPriorInfo` 会将这个 `data_ptr` 以 `type` 字符串为键，**添加或更新** 到插件内部的 `prior_info_` 成员中。这适用于设置单个或特定类型的先验信息。
    -   **使用**: 在调用 `Build()` 之前调用此方法设置先验信息。
    -   **插件内部访问**: 在插件的 `Run()` 方法或其他成员函数中，通过访问受保护的 `prior_info_` 成员变量（`std::unordered_map<std::string, DataPtr>`）来获取设置的先验数据，并使用 `GetDataPtr<T>()` 进行类型转换。
    ```cpp
    // 示例：设置初始位姿作为先验信息
    auto initial_pose_data = std::make_shared<DataMap<RelativePose>>(initial_pose);
    method_ptr->SetPriorInfo(initial_pose_data, "initial_guess");
    
    // 示例：一次性设置多个先验信息
    auto prior_package = std::make_shared<DataPackage>();
    prior_package->AddData("weights", weights_data);
    prior_package->AddData("mask", mask_data);
    method_ptr->SetPriorInfo(prior_package); // type 为空
    
    auto result = method_ptr->Build(main_input_data);
    ```
    -   **重置**: 调用 `ResetPriorInfo()` 可以清空所有已设置的先验信息。


#### 8.3 异常处理

- **策略**: PoSDK 核心库倾向于使用返回值 (`nullptr` 或 `false`) 表示错误 + cerr显示错误信息，而不是抛出异常 `throw()` 导致程序终止，以避免跨插件边界的异常处理复杂性。
- **后续**：考虑给Method制定log和error等级与id，方便用户调试
- **插件开发建议**:
  - 在插件内部可以使用 C++ 标准异常处理 (`try-catch`) 来捕获和处理内部错误。
  - 在覆盖的接口方法（如 `Run`, `Build`, `Save`, `Load`）中，应捕获所有内部异常，并转换为 `nullptr` 或 `false` 返回值。
  - 使用 `std::cerr` 输出详细的错误信息，帮助用户定位问题。
  - 检查从工厂函数 (`FactoryData::Create`, `FactoryMethod::Create`) 或 `GetDataPtr` 返回的指针是否为 `nullptr`。

### 9. 性能统计与分析 (`MethodPresetProfiler`)

- **基类**: 继承 `Interface::MethodPresetProfiler` 而不是 `MethodPreset`。
- **启用/禁用**:
  - 默认启用。
  - 可以通过配置文件中的 `enable_profiling=false` 选项禁用。
- **自动记录**: `Build` 方法会自动记录：
  - **总执行时间 (`total_time_ms`)**: 从 `Build` 开始到结束。
  - **计算时间 (`compute_time_ms`)**: `Run()` 方法的执行时间。
  - **内存使用**:
    - `current_memory_usage`: `Build` 结束时的内存占用。
    - `peak_memory_usage`: `Build` 过程中的峰值内存占用 (在 Linux 上通过 `getrusage`, Windows 上通过 `GetProcessMemoryInfo`)。
  - **输入检查时间 (`input_check`)**: `CheckInput` 的执行时间。
- **自定义阶段计时**: 在 `Run()` 方法内部，可以使用 `std::chrono` 手动记录特定代码段的耗时，并添加到 `ProfileInfo` 的 `stage_timings` 中。
  
  (注意: `MethodPresetProfiler` 的 `Build` 内部创建 `ProfileInfo`，目前 `Run` 无法直接访问它。如果需要在 `Run` 中添加自定义计时，需要修改框架或采用外部计时机制。)
- **报告输出**:
  - **控制台打印**: `Build` 结束后会自动调用 `PrintProfileReport` 打印摘要信息。
  - **CSV 文件**:
    - `Build` 结束后会自动调用 `ExportToCSV` 将性能数据追加到 CSV 文件。
    - **默认路径**: `performance_log/method_performance.csv`。
    - **自定义路径**:
      - 在构造函数中调用 `InitializeLogDir()` 可设置方法特定的日志目录 (如 `"method_name_performance_log"` )。
      - 可以重载 `GetLogDir()` 和 `GetCSVPath()` 来自定义目录和文件名。
    - CSV 文件包含时间戳、方法名、配置描述、时间和内存指标等。

### 10. 序列化存储与读取 (`PbDataIO`)

- **目的**: 提供一种标准化的方式来保存和加载插件数据，利用 Protobuf 实现跨平台和版本兼容。
- **基类**: 继承 `Interface::PbDataIO`。
- **核心虚函数**:
  - **`virtual std::unique_ptr<google::protobuf::Message> CreateProtoMessage() const = 0;`**: 创建一个空的 Protobuf 消息对象，用于反序列化。
  - **`virtual std::unique_ptr<google::protobuf::Message> ToProto() const = 0;`**: 将插件内部数据转换为 Protobuf 消息对象并返回。
  - **`virtual bool FromProto(const google::protobuf::Message& message) = 0;`**: 从 Protobuf 消息对象中解析数据并填充到插件内部。
- **自动文件 I/O**: `PbDataIO` 的 `Save` 和 `Load` 方法会自动处理文件读写和 Protobuf 的序列化/反序列化流程。开发者只需实现上述三个核心函数。
- **辅助宏**:
  - **`PROTO_SET_BASIC(proto_msg, field, value)`**: 设置基础类型字段 (int, float, bool, string等)。
  - **`PROTO_GET_BASIC(proto_msg, field, value)`**: 获取基础类型字段。
  - **`PROTO_SET_VECTOR2F/D(proto_msg, field, vec)`**: 设置 Eigen Vector2f/d。
  - **`PROTO_GET_VECTOR2F/D(proto_msg, field, vec)`**: 获取 Eigen Vector2f/d。
  - **`PROTO_SET_VECTOR3D(proto_msg, field, vec)`**: 设置 Eigen Vector3d。
  - **`PROTO_GET_VECTOR3D(proto_msg, field, vec)`**: 获取 Eigen Vector3d。
  - **`PROTO_SET_MATRIX3D(proto_msg, field, mat)`**: 设置 Eigen Matrix3d (按列优先)。
  - **`PROTO_GET_MATRIX3D(proto_msg, field, mat)`**: 获取 Eigen Matrix3d。
  - **`PROTO_SET_ARRAY(proto_msg, field, array)`**: 设置 `std::vector<基础类型>`。
  - **`PROTO_GET_ARRAY(proto_msg, field, array)`**: 获取 `std::vector<基础类型>`。
  - **`PROTO_SET_ENUM(proto_msg, field, value)`**: 设置枚举类型字段。
  - **`PROTO_GET_ENUM(proto_msg, field, value)`**: 获取枚举类型字段。
- **使用流程**:
  1. 定义数据的 `.proto` 文件。
  2. 在 CMake 中配置 Protobuf 代码生成。
  3. 插件类继承 `PbDataIO`。
  4. 实现 `GetType`, `GetData`。
  5. 实现 `CreateProtoMessage`, `ToProto`, `FromProto`，使用辅助宏进行字段映射。
  6. (可选) 实现 `CopyData`。
  7. 在 `.cpp` 文件中使用 `REGISTRATION_PLUGIN` 注册。
- **存储路径**:
  - `SetStorageFolder(path)`: 设置默认保存/加载目录。
  - `Save(folder, filename, ext)`:
    - 如果 `folder` 为空，使用 `storage_dir_`。
    - 如果 `filename` 为空，使用 `GetType() + "_default"`。
    - 如果 `extension` 为空，使用 `.pb`。
  - `Load(filepath, file_type)`:
    - 如果 `filepath` 为空，使用 `storage_dir_ / (GetType() + "_default.pb")`。
    - 如果 `filepath` 没有扩展名，默认添加 `.pb`。

### 11. 方法配置文件 (INI)

- **格式**: 标准 INI 文件格式。
  ```ini
  [section_name] ; 对应方法或行为的 GetType() 返回值
  key1 = value1
  key2 = value2 # 支持注释
  # 空行和注释行会被忽略
  @inherit = /path/to/base_config.ini ; 可选：继承指令
  ```
- **加载机制 (`MethodPreset::InitializeDefaultConfigPath`, `LoadMethodOptions`)**:
  1. **优先级**: 运行时设置 > 代码中 `method_options_` > 当前目录 `configs/methods/` > 构建目录 `configs/methods/` > 安装目录 `configs/methods/`。
  2. **自动加载**: `MethodPreset` 构造函数会尝试按上述优先级加载与 `GetType()` 同名的 `.ini` 文件。
  3. **继承**: 支持 `@inherit` 指令加载基础配置，当前配置会覆盖继承的配置。
  4. **特定配置加载**: `LoadMethodOptions` 可以传入第二个参数，加载 INI 文件中特定的节作为配置。
- **读取配置 (`GetOption*` in `types.hpp`)**:
  - 在插件代码中，使用 `method_options_` 成员变量访问配置。
  - 使用 `GetOptionAsString`, `GetOptionAsIndexT`, `GetOptionAsFloat`, `GetOptionAsBool` 辅助函数安全地读取配置值，并提供默认值。
- **写入配置 (`ConfigurationTools`)**:
  - 虽然 `MethodPreset` 主要用于读取，但 `ConfigurationTools` 类 (`inifile.hpp`) 提供了写入 INI 文件的功能 (`WriteItem`, `WriteFile`)。可以用于保存用户修改后的配置。

### 12. 鲁棒估计器框架

PoSDK 提供了一个鲁棒估计器的框架，目前支持 RANSAC 和 GNC-IRLS。

#### 12.1 `DataSample<TSample>` 数据容器

- **目的**: 管理用于鲁棒估计的样本数据，特别是支持高效的子集采样而无需复制原始数据（零拷贝）。
- **模板参数 `TSample`**: 必须是 `std::vector<ValueType>` 类型，其中 `ValueType` 是样本中单个元素的类型（例如，`BearingPair`）。
- **核心特性**:
  - **零拷贝子集**: `GetRandomSubset(size)` 和 `GetSubset(indices)` 返回一个新的 `DataSample` 对象，该对象共享原始数据 (`data_map_ptr_`)，但通过 `subset_indices_` 存储子集的索引。
  - **STL 兼容接口**: 提供 `begin()`, `end()`, `operator[]`, `size()`, `empty()` 等接口，使得可以像使用 `std::vector` 一样访问样本数据（无论是完整样本还是子集）。迭代器和 `operator[]` 会自动处理索引映射。
- **使用**:
  - 通常作为 `RobustEstimator` 的输入数据类型。
  - `RobustEstimator` 内部会使用 `GetRandomSubset` 来获取随机样本进行模型估计。
  - `GetInlierSubset` 用于获取内点子集以进行最终模型优化。
- **示例**:
  ```cpp
  // 假设有原始数据 std::vector<MySampleType> original_data;
  auto data_sample = std::make_shared<DataSample<std::vector<MySampleType>>>(original_data);

  // 获取随机子集
  auto random_subset = data_sample->GetRandomSubset(10);
  std::cout << "Random subset size: " << random_subset->size() << std::endl;
  if (!random_subset->empty()) {
      // 访问子集元素
      MySampleType first_element = (*random_subset)[0];
      // 迭代子集
      for (const auto& element : *random_subset) {
          // ... process element ...
      }
  }
  ```

#### 12.2 `RobustEstimator<TSample>` 基类

- **模板参数 `TSample`**: 定义了估计器处理的样本数据类型（通常是 `BearingPairs` 或类似结构）。
- **核心组件**:
  - **模型估计器 (`model_estimator_ptr_`)**: 继承自 `MethodPreset`，负责从一个最小样本集估计模型参数（例如，LiRP 方法估计相对位姿）。其类型通过 `model_estimator_type` 配置选项指定。
  - **代价评估器 (`cost_evaluator_ptr_`)**: 继承自 `MethodPreset`，负责计算每个样本点相对于给定模型的残差（代价）。其类型通过 `cost_evaluator_type` 配置选项指定。
- **工作流程**:
  1. 通过 `FactoryMethod::Create` 创建 `RobustEstimator` 实例 (如 `RANSACEstimator` 或 `GNCIRLSEstimator`)。
  2. 设置 `model_estimator_type` 和 `cost_evaluator_type` 选项。
  3. (可选) 通过 `SetModelEstimatorOptions` 和 `SetCostEvaluatorOptions` 为内部的模型估计器和代价评估器设置特定参数。
  4. 通过 `SetRequiredData` 设置包含 `DataSample<TSample>` 的输入数据。
  5. 调用 `Build()` 启动鲁棒估计流程。
- **派生类**:
  - `RANSACEstimator<TSample>`: 实现标准的 RANSAC 算法。
  - `GNCIRLSEstimator<TSample>`: 实现 GNC-IRLS 算法，通常更鲁棒但计算量更大。
- **配置**: 通过 `.ini` 文件或 `SetMethodOptions` 配置鲁棒估计器的参数（如迭代次数、置信度、内点阈值）以及内部模型估计器和代价评估器的类型。

#### 12.3 使用示例 (`test_ransac_lirp.cpp` 简化版)

```cpp
// 1. 创建 RANSAC 估计器实例
auto ransac_estimator = std::dynamic_pointer_cast<RobustEstimator>(
    FactoryMethod::Create("ransac_estimator") // 假设 TSample 是 BearingPairs
);
ASSERT_TRUE(ransac_estimator != nullptr);

// 2. 设置 RANSAC 参数
MethodOptions ransac_options {
    {"model_estimator_type", "method_LiRP"},         // 使用 LiRP 估计模型
    {"cost_evaluator_type", "method_relative_cost"}, // 使用相对位姿代价评估
    {"max_iterations", "1000"},
    {"confidence", "0.99"},
    {"inlier_threshold", "1e-4"}, // RANSAC 内点阈值
    {"min_sample_size", "8"}      // LiRP 需要 8 对点
};
ransac_estimator->SetMethodOptions(ransac_options);

// 3. (可选) 设置 LiRP 和 Cost Evaluator 的特定参数
MethodOptions lirp_options { {"identify_mode", "PPO"} /* ... 其他 LiRP 选项 ... */ };
ransac_estimator->SetModelEstimatorOptions(lirp_options);

MethodOptions cost_options { {"residual_type", "sampson"} /* ... 其他 Cost 选项 ... */ };
ransac_estimator->SetCostEvaluatorOptions(cost_options);

// 4. 准备输入数据 (DataSample<BearingPairs>)
BearingPairs bearing_pairs_data = GenerateBearingPairs(); // 假设有函数生成数据
auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs_data);

// 5. 设置输入数据
DataPackage input_package;
input_package.AddData("data_sample", sample_data); // RobustEstimator 需要 data_sample
// 如果 model_estimator 或 cost_evaluator 需要其他数据 (如相机模型)，也需添加到包中
// input_package.AddData("data_camera_model", camera_model_data);
ransac_estimator->SetRequiredData(input_package); // 或者直接传入 Build

// 6. 执行 RANSAC
auto result = ransac_estimator->Build();

// 7. 处理结果 (通常是 DataMap<RelativePose>)
if (result) {
    auto pose = GetDataPtr<RelativePose>(result);
    // ... 使用估计的位姿 ...
}
```

---

## 附录


## A. 核心数据类型详解





### 基础类型

#### IndexT
- **定义**: `using IndexT = uint32_t;`
- **含义**: 索引类型，用于各种索引标识
- **取值范围**: 0 ~ 4,294,967,295
- **使用场景**: 作为集合索引、ID标识等

#### ViewId
- **定义**: `using ViewId = uint32_t;`
- **含义**: 视图ID，用于标识相机视图
- **使用场景**: 在跟踪、位姿数据中引用特定视图

#### PtsId
- **定义**: `using PtsId = uint32_t;`
- **含义**: 点/轨迹ID，用于标识3D点或特征轨迹
- **使用场景**: 在轨迹数据中引用特定3D点或特征


### 特征与匹配

#### Feature
- **定义**: `using Feature = Vector2d;`
- **含义**: 基础2D特征点，即图像中的点坐标(x, y)
- **用途**: 表示图像中的特征点位置

#### FeaturePoint
- **含义**: 完整特征点信息
- **用途**: 存储特征点的位置、大小、方向和描述子
- **成员变量**:
  - `coord`: 特征点坐标(x, y)
  - `size`: 特征点大小
  - `angle`: 特征点方向角度
  - `descriptor`: 特征描述子向量
  - `is_used`: 是否使用该特征点的标志
- **主要方法**:
  - `FeaturePoint()`: 默认构造函数
  - `FeaturePoint(const Feature& pos, float size_=0, float angle_=0)`: 从基础特征点构造
  - `FeaturePoint(float x, float y, float size_=0, float angle_=0)`: 从坐标构造

#### ImageFeatureInfo
- **含义**: 单张图像的特征信息
- **用途**: 存储一张图像中的所有特征点
- **成员变量**:
  - `image_path`: 图像文件路径
  - `features`: 特征点集合(`std::vector<FeaturePoint>`)
  - `is_used`: 该图像特征是否被使用
- **主要方法**:
  - `ImageFeatureInfo()`: 默认构造函数
  - `ImageFeatureInfo(const std::string& path, bool used=true)`: 带参数构造函数
  - `AddFeature(const FeaturePoint& feat)`: 添加特征点
  - `GetNumFeatures()`: 获取特征点数量
  - `ClearUnusedFeatures()`: 清除未使用的特征点

#### FeaturesInfo
- **含义**: 所有图像的特征信息集合
- **用途**: 管理多张图像的特征点信息
- **类型**: `std::vector<ImageFeatureInfo>`的派生类
- **主要方法**:
  - `AddImageFeatures(const std::string& image_path, bool is_used=true)`: 添加图像特征
  - `GetNumValidImages()`: 获取有效图像数量
  - `ClearUnusedImages()`: 清除未使用的图像
  - `ClearAllUnusedFeatures()`: 清除所有未使用的特征点

#### IdMatch
- **含义**: 特征点匹配
- **用途**: 表示两个特征点之间的匹配关系
- **成员变量**:
  - `i`: 第一个特征的索引
  - `j`: 第二个特征的索引
  - `is_inlier`: RANSAC内点标志，默认为true

#### Matches
- **定义**: `using Matches = std::map<ViewPair, IdMatches>;`
- **含义**: 所有视图对之间的匹配
- **用途**: 存储不同视图对之间的特征匹配信息
- **结构**: 键为视图对(ViewPair)，值为对应的匹配集合(IdMatches)

###  轨迹与观测

#### ObsInfo
- **含义**: 3D点的单个观测信息
- **用途**: 记录3D点在某一视图中的观测
- **成员变量**:
  - `view_id`: 视图ID
  - `pts_id`: 3D点ID
  - `obs_id`: 观测ID，默认为0
  - `coord`: 图像特征点的2D坐标
  - `is_used`: 当前观测是否被使用，默认为true
- **主要方法**:
  - `ObsInfo()`: 默认构造函数
  - `ObsInfo(ViewId vid, PtsId pid, const Vector2d& c)`: 带参数构造函数
  - `GetCoord(Vector2d& coord_out)`: 获取观测坐标
  - `GetHomoCoord()`: 获取齐次坐标
  - `SetUsed(bool used)`: 设置观测使用状态

#### Track
- **含义**: 3D点的观测信息集合
- **用途**: 存储一个3D点在多个视图中的观测
- **类型**: `std::vector<ObsInfo>`的派生类
- **主要方法**:
  - `AddObservation(ViewId view_id, PtsId track_id, const Vector2d& coord)`: 添加观测
  - `GetNumObservations()`: 获取观测点数量
  - `GetObservation(IndexT index)`: 获取指定索引的观测信息
  - `GetValidObservationCount()`: 获取有效观测数量
  - `SetObservationUsed(IndexT index, bool used)`: 设置指定观测的使用状态

#### TrackInfo
- **含义**: 跟踪信息，包括使用标志
- **用途**: 封装Track并添加使用状态
- **成员变量**:
  - `track`: 轨迹数据
  - `is_used`: 该轨迹是否被使用，默认为true
- **主要方法**:
  - `TrackInfo()`: 默认构造函数
  - `TrackInfo(const Track& t, bool used=true)`: 带参数构造函数
  - `GetObservationCount()`: 获取观测数量
  - `GetValidObservationCount()`: 获取有效观测数量
  - `GetObservation(IndexT index)`: 获取指定观测信息
  - `GetObservationCoord(IndexT index, Vector2d& coord_out)`: 获取指定观测坐标
  - `SetUsed(bool used)`: 设置轨迹使用状态
  - `SetObservationUsed(IndexT index, bool used)`: 设置指定观测使用状态
  - `AddObservation(const ObsInfo& obs)`: 添加新观测

#### Tracks
- **含义**: 所有轨迹的集合
- **用途**: 管理多个轨迹数据，支持轨迹的查询和操作
- **成员变量**:
  - `tracks_`: 轨迹信息向量
  - `is_normalized_`: 是否已归一化标志
- **主要方法**:
  - 支持标准容器接口: `size()`, `empty()`, `clear()`, `begin()`, `end()`, `[]`等
  - `AddTrack(const std::vector<ObsInfo>& observations)`: 添加新轨迹
  - `AddObservation(PtsId track_id, const ObsInfo& obs)`: 向指定轨迹添加观测
  - `GetTrackCount()`: 获取轨迹数量
  - `GetObservationCount(PtsId track_id)`: 获取指定轨迹的观测数量
  - `GetObservation(PtsId track_id, IndexT index)`: 获取指定轨迹的指定观测
  - `GetValidTrackCount()`: 获取有效轨迹数量
  - `IsNormalized()`: 获取归一化状态
  - `SetNormalized(bool normalized)`: 设置归一化状态

###  位姿

#### RelativeRotation
- **含义**: 相对旋转关系
- **用途**: 存储两个相机视图间的相对旋转变换
- **成员变量**:
  - `i`: 源相机视图索引
  - `j`: 目标相机视图索引
  - `Rij`: 从视图i到视图j的相对旋转矩阵
  - `weight`: 相对旋转的权重因子，默认为1.0
- **构造函数**:
  - `RelativeRotation(IndexT i_=0, IndexT j_=0, const Matrix3d& Rij_=Matrix3d::Identity(), float weight_=1.0f)`

#### RelativePose
- **含义**: 相对位姿表示
- **用途**: 存储两个相机视图间的相对位姿变换
- **成员变量**:
  - `i`: 源相机视图索引
  - `j`: 目标相机视图索引
  - `Rij`: 从视图i到视图j的相对旋转矩阵
  - `tij`: 从视图i到视图j的相对平移向量
  - `weight`: 位姿估计的权重因子，默认为1.0
- **主要方法**:
  - `RelativePose(IndexT i_=0, IndexT j_=0, const Matrix3d& Rij_=Matrix3d::Identity(), const Vector3d& tij_=Vector3d::Zero(), float weight_=1.0f)`: 构造函数
  - `GetEssentialMatrix()`: 获取本质矩阵，返回3×3矩阵

#### GlobalPoses
- **含义**: 全局位姿信息
- **用途**: 存储所有视图的全局位姿信息
- **成员变量**:
  - `rotations`: 所有视图的旋转矩阵数组(`std::vector<Matrix3d>`)
  - `translations`: 所有视图的平移向量数组(`std::vector<Vector3d>`)
  - `est_info`: 估计状态信息
  - `pose_format`: 位姿格式，默认为RwTw
- **主要方法**:
  - `Init(size_t num_views)`: 初始化位姿数据
  - `GetPoseFormat()`: 获取当前位姿格式
  - `SetPoseFormat(PoseFormat format)`: 设置位姿格式
  - `GetRotation(ViewId original_id)`: 获取视图的旋转矩阵(使用原始ID)
  - `GetTranslation(ViewId original_id)`: 获取视图的平移向量(使用原始ID)
  - `GetRotationByEstId(ViewId est_id)`: 获取视图的旋转矩阵(使用估计ID)
  - `GetTranslationByEstId(ViewId est_id)`: 获取视图的平移向量(使用估计ID)
  - `SetRotation(ViewId original_id, const Matrix3d& rotation)`: 设置视图的旋转矩阵
  - `SetTranslation(ViewId original_id, const Vector3d& translation)`: 设置视图的平移向量
  - `SetRotationByEstId(ViewId est_id, const Matrix3d& rotation)`: 使用估计ID设置旋转
  - `SetTranslationByEstId(ViewId est_id, const Vector3d& translation)`: 使用估计ID设置平移
  - `Size()`: 获取位姿数量
  - `GetEstInfo()`: 获取EstInfo引用
  - `BuildEstInfoFromTracks(const TracksPtr& tracks_ptr, const ViewId fixed_id=0)`: 从Tracks构建EstInfo


###  相机模型

#### CameraIntrinsics
- **含义**: 相机内参数据结构
- **用途**: 存储相机的内参信息
- **成员变量**:
  - `fx, fy`: x、y方向焦距
  - `cx, cy`: x、y方向主点偏移
  - `width, height`: 图像宽度和高度
  - `model_type`: 相机模型类型，默认为针孔模型
  - `distortion_type`: 畸变类型，默认为无畸变
  - `radial_distortion`: 径向畸变参数数组
  - `tangential_distortion`: 切向畸变参数数组
- **主要方法**:
  - `GetK(Matrix3d& K)`: 获取相机内参矩阵
  - `SetK(const Matrix3d& K)`: 设置相机内参矩阵
  - `SetCameraIntrinsics(...)`: 多种重载方法设置内参

#### CameraModel
- **含义**: 相机模型完整定义
- **用途**: 包含相机内参和元数据信息
- **成员变量**:
  - `intrinsics`: 相机内参
  - `camera_make`: 相机制造商
  - `camera_model`: 相机型号
  - `serial_number`: 序列号
- **主要方法**:
  - `PixelToNormalized(const Vector2d& pixel_coord)`: 像素坐标转归一化坐标
  - `NormalizedToPixel(const Vector2d& normalized_coord)`: 归一化坐标转像素坐标
  - `SetKMat(const Matrix3d& K)`: 设置相机内参矩阵
  - `GetKMat(Matrix3d& K)`: 获取相机内参矩阵
  - `SetDistortionParams(...)`: 设置畸变参数
  - `SetModelType(const CameraModelType& model_type)`: 设置相机模型类型
  - `SetCameraInfo(...)`: 设置相机元数据信息
  - `SetCameraIntrinsics(...)`: 多种重载方法设置内参

#### CameraModels
- **定义**: `using CameraModels = std::vector<CameraModel>;`
- **含义**: 相机模型集合
- **用途**: 管理多个相机模型，支持单相机和多相机配置
- **辅助函数**:
  - `GetCameraModel(CameraModels& camera_models, ViewId view_id)`: 获取指定视图相机模型
  - `GetCameraModel(const CameraModels& camera_models, ViewId view_id)`: 常量版本

###  其他实用类型

#### ImagePaths
- **定义**: `using ImagePaths = std::vector<std::pair<std::string, bool>>;`
- **含义**: 图像路径集合，每项包含路径和可用性标志
- **用途**: 存储项目中使用的所有图像文件路径和状态

#### BearingVectors
- **定义**: `using BearingVectors = Eigen::Matrix<double,3,Eigen::Dynamic>;`
- **含义**: 归一化观测向量，3×N矩阵
- **用途**: 存储相机归一化坐标系中的方向向量

#### BearingPairs
- **定义**: `using BearingPairs = std::vector<Eigen::Matrix<double,6,1>>;`
- **含义**: 匹配的归一化观测向量对
- **用途**: 存储两个相机观测的配对方向向量，用于相对位姿估计

###  工具函数

#### 位姿格式转换
- **RwTw_to_RwTc**: 将位姿从RwTw格式转换为RwTc格式
  - 参数:
    - `poses`: 需要转换的全局位姿
    - `ref_id`: 参考视图ID(默认为0)
    - `fixed_id`: 用于尺度归一化的固定视图ID(默认为最远视图)
  - 返回: 是否转换成功

- **RwTc_to_RwTw**: 将位姿从RwTc格式转换为RwTw格式
  - 参数与RwTw_to_RwTc相同



---


## B. 常用工具函数

### >>配置参数获取

以下工具函数用于从方法配置(MethodOptions)中安全地获取各种类型的参数值：

#### GetOptionAsIndexT
- 功能: 从方法配置获取无符号整数值(IndexT/uint32_t)
- 参数:
  - options: const MethodOptions& - 方法配置字典
  - key: const MethodParams& - 参数键名
  - default_value: IndexT - 默认值，当键不存在或转换失败时返回，默认为0
- 返回值: IndexT类型的参数值
- 使用示例:
  ```cpp
  // 获取迭代次数，默认为100
  IndexT max_iterations = GetOptionAsIndexT(method_options_, "max_iterations", 100);
  ```

#### GetOptionAsFloat
- 功能: 从方法配置获取浮点数值
- 参数:
  - options: const MethodOptions& - 方法配置字典
  - key: const MethodParams& - 参数键名
  - default_value: float - 默认值，当键不存在或转换失败时返回，默认为0.0f
- 返回值: float类型的参数值
- 使用示例:
  ```cpp
  // 获取阈值参数，默认为0.5
  float threshold = GetOptionAsFloat(method_options_, "threshold", 0.5f);
  ```

#### GetOptionAsBool
- 功能: 从方法配置获取布尔值
- 参数:
  - options: const MethodOptions& - 方法配置字典
  - key: const MethodParams& - 参数键名
  - default_value: bool - 默认值，当键不存在或转换失败时返回，默认为false
- 返回值: bool类型的参数值
- 注意: 配置中的"true","yes","1","on"会被识别为true；"false","no","0","off"会被识别为false
- 使用示例:
  ```cpp
  // 获取是否启用特定功能，默认为false
  bool enable_feature = GetOptionAsBool(method_options_, "enable_feature", false);
  ```

#### GetOptionAsString
- 功能: 从方法配置获取字符串值
- 参数:
  - options: const MethodOptions& - 方法配置字典
  - key: const MethodParams& - 参数键名
  - default_value: const std::string& - 默认值，当键不存在时返回，默认为空字符串
- 返回值: std::string类型的参数值
- 使用示例:
  ```cpp
  // 获取算法模式，默认为"default"
  std::string mode = GetOptionAsString(method_options_, "algorithm_mode", "default");
  ```


###  >> 数据类型转换

#### GetDataPtr<T>函数
- 功能: 安全地获取并转换数据指针到指定类型
- 模板参数:
  - T: 要转换到的目标数据类型
- 参数:
  - data_ptr: const DataPtr& - 输入数据指针
- 返回值: 指向类型T的指针，如果转换失败则返回nullptr
- 工作原理: 
  1. 检查输入是否为nullptr
  2. 使用dynamic_cast进行安全类型转换
  3. 如果转换成功，返回指向底层数据的指针
- 使用示例:
  ```cpp
  // 转换数据指针到GlobalPoses类型
  auto poses = GetDataPtr<GlobalPoses>(data_ptr);
  if (poses) {
      // 现在可以安全地使用poses访问位姿数据
      size_t num_poses = poses->Size();
  }
  ```

#### String2IndexT
- 功能: 将字符串转换为IndexT类型(uint32_t)
- 参数:
  - value: const std::string& - 输入字符串
  - default_value: IndexT - 转换失败时返回的默认值，默认为0
- 返回值: 转换后的IndexT值
- 使用场景: 用于配置文件字符串值到整数的安全转换
- 使用示例:
  ```cpp
  IndexT max_iter = String2IndexT("1000", 100); // 返回1000
  IndexT invalid = String2IndexT("abc", 100);   // 返回100
  ```

#### String2Float
- 功能: 将字符串转换为float类型
- 参数:
  - value: const std::string& - 输入字符串
  - default_value: float - 转换失败时返回的默认值，默认为0.0f
- 返回值: 转换后的float值
- 使用场景: 用于配置文件字符串值到浮点数的安全转换
- 使用示例:
  ```cpp
  float threshold = String2Float("0.75", 0.5f); // 返回0.75
  float invalid = String2Float("xyz", 0.5f);    // 返回0.5
  ```

#### String2Bool
- 功能: 将字符串转换为bool类型
- 参数:
  - str: const std::string& - 输入字符串
  - default_value: bool - 转换失败时返回的默认值，默认为false
- 返回值: 转换后的bool值
- 注意: "true","yes","1","on"会被转换为true；"false","no","0","off"会被转换为false
- 使用示例:
  ```cpp
  bool enabled = String2Bool("yes", false);    // 返回true
  bool disabled = String2Bool("off", true);    // 返回false
  bool invalid = String2Bool("maybe", false);  // 返回默认值false
  ```

###  位姿转换函数

#### RwTw_to_RwTc
- 功能: 将位姿从RwTw格式转换为RwTc格式
- 参数:
  - poses: GlobalPoses& - 需要转换的位姿数据
  - ref_id: ViewId - 参考视图ID，默认为0
  - fixed_id: ViewId - 固定视图ID，用于尺度标定，默认为最大值(不使用)
- 返回值: bool - 转换是否成功
- 说明: 
  - RwTw: [R|t]，其中t是相机中心在世界坐标系中的位置
  - RwTc: [R|t]，其中t是从世界坐标系原点到相机中心的向量

#### RwTc_to_RwTw
- 功能: 将位姿从RwTc格式转换为RwTw格式
- 参数:
  - poses: GlobalPoses& - 需要转换的位姿数据
  - ref_id: ViewId - 参考视图ID，默认为0
  - fixed_id: ViewId - 固定视图ID，用于尺度标定，默认为最大值(不使用)
- 返回值: bool - 转换是否成功

#### ConvertPoseFormat
- 功能: 在不同位姿格式之间转换
- 参数:
  - poses: GlobalPoses& - 需要转换的位姿数据
  - target_format: PoseFormat - 目标位姿格式
  - ref_id: ViewId - 参考视图ID，默认为0
  - fixed_id: ViewId - 固定视图ID，默认为最大值(不使用)
- 返回值: bool - 转换是否成功
- 使用示例:
  ```cpp
  // 将位姿从RwTc格式转换为RwTw格式
  bool success = ConvertPoseFormat(global_poses, PoseFormat::RwTw);
  ```

---

## C. 插件命名建议

为了保持整个系统的一致性，建议按照以下规则命名您的插件：

###  数据插件: data_[功能]
- 例如: data_tracks, data_camera_model, data_images
- 遵循这一命名约定可以帮助系统自动识别插件类型

###  方法插件: method_[功能]
- 例如: method_sift, method_matches2tracks, method_pose_estimation
- 功能部分应简明扼要地描述方法的用途

###  行为插件: behavior_[功能]
- 例如: behavior_sfm, behavior_localization
- 通常用于表示更大的处理管线或工作流程

#### 注意事项
- 插件命名应使用小写字母和下划线
- 保持名称简短但具有描述性
- 类名可以使用大写字母开头的驼峰命名法，但GetType()返回的字符串应遵循上述命名规则


---
## D. 性能分析

MethodPresetProfiler类提供的性能分析功能是PoSDK中监控和优化算法的重要工具。这个框架不仅记录了方法的执行时间和内存使用情况，还允许开发者记录算法内部各个阶段的详细性能数据。

###  当前功能

#### 基础指标收集
- **执行时间追踪**：
  - 总执行时间(total_time_ms)：从Build开始到结束的时间
  - 计算时间(compute_time_ms)：Run()方法的执行时间
  - 输入检查时间(input_check)：CheckInput的执行时间
  
- **内存监控**：
  - 当前内存使用(current_memory_usage)：Build结束时的内存占用
  - 峰值内存使用(peak_memory_usage)：Build过程中的峰值内存占用

#### 自定义阶段计时
可以为算法中的关键阶段添加自定义计时：
```cpp
auto stage_start = std::chrono::high_resolution_clock::now();
// 执行某个阶段的代码...
auto stage_end = std::chrono::high_resolution_clock::now();
profile.stage_timings["feature_extraction"] = 
    std::chrono::duration<double, std::milli>(stage_end - stage_start).count();
```

#### 数据导出与报告
- **CSV文件导出**：自动将性能数据追加到CSV文件，便于长期趋势分析
- **控制台报告**：Build结束后自动打印性能摘要信息


###  后续拓展方向

#### 1. 高级内存分析
- **内存分配追踪**：记录每个阶段的内存分配和释放
- **内存泄漏检测**：集成内存泄漏检测工具
- **数据结构内存分析**：分析关键数据结构的内存占用

#### 2. 算法复杂度评估
- **计算复杂度分析**：自动估计算法的时间复杂度（如O(n²)、O(n log n)）
- **规模扩展性测试**：自动测试不同输入规模下的性能变化
- **瓶颈识别**：自动识别算法中的性能瓶颈

#### 3. 硬件利用率监控
- **CPU利用率**：监控多核CPU的利用情况
- **GPU资源监控**：对GPU加速算法监控GPU内存和计算资源使用
- **并行效率评估**：评估并行算法的加速比和效率


#### 4. 可视化增强
- **交互式性能仪表板**：提供Web界面查看性能数据
- **性能热图**：可视化代码中的性能热点
- **时间线可视化**：直观显示各阶段执行时间和重叠情况

#### 5. 持续集成与监控
- **性能回归测试**：自动检测代码更改导致的性能下降
- **阈值警报系统**：当性能指标超出预设阈值时发出警报
- **历史趋势分析**：长期追踪性能变化趋势

###  使用方法

要在您的插件中启用和使用这些性能分析功能：

1. **继承正确的基类**：
   ```cpp
   class MyMethod : public MethodPresetProfiler {
       // ...
   };
   ```

2. **配置启用分析**：
   ```cpp
   // 在构造函数中
   MyMethod::MyMethod() {
       // 默认启用性能分析
       // 也可以在配置文件中使用enable_profiling=false禁用
       InitializeLogDir(); // 设置日志目录
   }
   ```

3. **添加自定义阶段计时**：
   ```cpp
   DataPtr MyMethod::Run() {
       // 阶段1计时
       auto stage1_start = std::chrono::high_resolution_clock::now();
       // 执行阶段1...
       auto stage1_end = std::chrono::high_resolution_clock::now();
       
       // 记录时间（将在Build中自动收集）
       profile_->stage_timings["stage1"] = 
           std::chrono::duration<double, std::milli>(stage1_end - stage1_start).count();
           
       // 阶段2...
       // ...
       
       return result;
   }
   ```

4. **分析性能报告**：
   查看输出的CSV文件和控制台报告，识别性能瓶颈和优化机会。

这些性能分析功能将帮助开发者深入了解算法行为，指导优化方向，并在不同场景和数据集上评估算法的适用性。
