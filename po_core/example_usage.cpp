#include <iostream>
#include <string>
#include "internal/inifile.hpp"

int main()
{
    std::cout << "=== PoMVG ConfigurationTools 注释功能演示 ===" << std::endl;

    // 创建配置工具实例
    PoSDK::ConfigurationTools config;

    // 加载示例配置文件
    std::string config_file = "example_config.ini";
    int result = config.Init(config_file);

    if (result != 0)
    {
        std::cerr << "无法加载配置文件: " << config_file << std::endl;
        return -1;
    }

    std::cout << "✓ 成功加载配置文件: " << config_file << std::endl;
    std::cout << "\n注意：配置文件中的注释行已被自动忽略\n"
              << std::endl;

    // 读取一些配置项进行演示
    std::string value;

    std::cout << "=== 读取相机参数 ===" << std::endl;

    if (config.ReadItem("camera_params", "focal_length", "0", value) == 0)
    {
        std::cout << "焦距: " << value << std::endl;
    }

    if (config.ReadItem("camera_params", "cx", "0", value) == 0)
    {
        std::cout << "主点x坐标: " << value << std::endl;
    }

    if (config.ReadItem("camera_params", "cy", "0", value) == 0)
    {
        std::cout << "主点y坐标: " << value << std::endl;
    }

    // 尝试读取被注释掉的参数
    if (config.ReadItem("camera_params", "k3", "未找到", value) == 0)
    {
        std::cout << "径向畸变系数k3: " << value << std::endl;
    }
    else
    {
        std::cout << "✓ 被注释的参数k3正确被忽略，返回默认值: " << value << std::endl;
    }

    std::cout << "\n=== 读取特征提取参数 ===" << std::endl;

    if (config.ReadItem("feature_extraction", "method", "UNKNOWN", value) == 0)
    {
        std::cout << "特征提取方法: " << value << std::endl;
    }

    if (config.ReadItem("feature_extraction", "max_features", "0", value) == 0)
    {
        std::cout << "最大特征点数: " << value << std::endl;
    }

    // 尝试读取被注释掉的参数
    if (config.ReadItem("feature_extraction", "min_hessian", "未找到", value) == 0)
    {
        std::cout << "最小Hessian阈值: " << value << std::endl;
    }
    else
    {
        std::cout << "✓ 被注释的参数min_hessian正确被忽略，返回默认值: " << value << std::endl;
    }

    std::cout << "\n=== 读取输出设置 ===" << std::endl;

    if (config.ReadItem("output", "save_intermediate", "false", value) == 0)
    {
        std::cout << "保存中间结果: " << value << std::endl;
    }

    if (config.ReadItem("output", "output_format", "UNKNOWN", value) == 0)
    {
        std::cout << "输出格式: " << value << std::endl;
    }

    if (config.ReadItem("output", "verbose_level", "0", value) == 0)
    {
        std::cout << "详细程度: " << value << std::endl;
    }

    // 尝试读取被注释掉的参数
    if (config.ReadItem("output", "debug_mode", "未找到", value) == 0)
    {
        std::cout << "调试模式: " << value << std::endl;
    }
    else
    {
        std::cout << "✓ 被注释的参数debug_mode正确被忽略，返回默认值: " << value << std::endl;
    }

    std::cout << "\n=== 转储所有有效配置 ===" << std::endl;
    std::cout << "以下是所有被成功读取的配置项（注释行已被过滤）：\n"
              << std::endl;

    config.Dump();

    std::cout << "\n=== 测试写入功能 ===" << std::endl;

    // 添加一个新的配置项
    if (config.WriteItem("test_section", "test_param", "test_value") == 0)
    {
        std::cout << "✓ 成功写入新配置项" << std::endl;

        // 验证写入
        if (config.ReadItem("test_section", "test_param", "default", value) == 0)
        {
            std::cout << "验证新配置项: test_param = " << value << std::endl;
        }
    }

    std::cout << "\n=== 功能特性总结 ===" << std::endl;
    std::cout << "✓ 支持 # 和 ; 两种注释格式" << std::endl;
    std::cout << "✓ 自动忽略注释行" << std::endl;
    std::cout << "✓ 正确处理行内注释" << std::endl;
    std::cout << "✓ 保持原有API兼容性" << std::endl;
    std::cout << "✓ 线程安全的读写操作" << std::endl;

    return 0;
}