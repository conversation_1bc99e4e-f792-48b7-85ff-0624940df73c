# MEX模块构建配置

# 设置第三方库路径
set(SPECTRA_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/spectra-master/include)

message(STATUS "Spectra directory: ${SPECTRA_DIRS}")

# 添加核心子目录
add_subdirectory(LiGT_algorithm)

# 可选的子目录（根据需要启用）
# add_subdirectory(test)
# add_subdirectory(CostFuncPA)
# add_subdirectory(ananalytical_reconstruction)
# add_subdirectory(direct_method)
# add_subdirectory(bal_outlier_detect)

message(STATUS "MEX modules configured successfully")

# MEX接口和依赖项
#
# 这个CMakeLists.txt文件用于管理MEX构建相关的子目录和依赖项

# 添加包含Spectra库的子目录
# 注意：Spectra是头文件库，通常不需要编译，但我们可能需要将其包含路径添加到项目中
set(SPECTRA_INCLUDE_DIRS "${CMAKE_CURRENT_SOURCE_DIR}/spectra-master/include")
message(STATUS "Spectra include directory: ${SPECTRA_INCLUDE_DIRS}")

set(POVGLIB_SOURCES ${POVGLIB_SOURCES} PARENT_SCOPE)
