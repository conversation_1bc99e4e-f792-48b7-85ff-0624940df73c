// #define EIGEN_USE_BLAS
// #define EIGEN_USE_LAPACKE
// #define ARMA_USE_BLAS
// #define ARMA_USE_NEWARP
// #define ARMA_USE_LAPACK
// #define EIGEN_USE_MKL_ALL
#define EIGEN_NO_DEBUG

#include <math.h>
#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include <type_traits>
#include <limits>
#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>
#include <math.h>
#include <unordered_map>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <chrono>
#include <set>
#include <Eigen/Core>
#include <Spectra/SymEigsShiftSolver.h>
// <Spectra/MatOp/DenseSymShiftSolve.h> is implicitly included
#include <Spectra/MatOp/SparseSymShiftSolve.h>

#define IsNonZero(d) (fabs(d) > 1e-15)
#define T_size 3 * (est_view_size)
#define max(a, b) (a) < (b) ? (b) : (a)
// #define ISDEBUG
DEFINE_string(tracks_file,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_507/Simulative_Test_of_Noise/Output_ForZhang_2Circles_5Image_500Points_10.00Dt_0.10noise/matches/Matches_5Image_500Points_10.00Dt_0.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(gR_file,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_507/Simulative_Test_of_Noise/Output_ForZhang_2Circles_5Image_500Points_10.00Dt_0.10noise/global_R_1.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(output_file,
              "output_file.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(output_pts_file,
              "output_pts_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(time_file,
              "time_output_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_bool(issparse,
            true,
            "Type of SfM reconstruction estimation to use.");

using namespace std;
using namespace chrono;
using namespace Eigen;

using namespace Spectra;
typedef unsigned int ViewId;
typedef unsigned int PtsId;
typedef unsigned int Size;
typedef SparseMatrix<double> spMatrix;
typedef Eigen::Triplet<double> ele_triplet;
struct Obs_info
{
    ViewId view_id;
    PtsId pts_id;
    Vector3d coord;
};

struct BaseFrames
{
    double *base_s;
    ViewId *lbase_frames;
    ViewId *rbase_frames;
};

typedef vector<Obs_info> Track;
struct Track_info
{
    Track track;
    bool is_used = true;
};
bool compare_track_info(
    const Track_info &track_info1,
    const Track_info &track_info2)
{
    return track_info1.track.size() > track_info2.track.size();
}

typedef vector<Track_info> Tracks;

typedef Matrix3d *Attitude;

typedef Eigen::SparseMatrix<double> EigenSpMat; // declares a column-major sparse matrix type of double
typedef Eigen::Triplet<double> T;

typedef vector<ViewId> Est_View;

typedef Vector3d *G_Translations;
typedef Vector3d *G_Points;

struct Est_Info
{
    Est_View est_view;
    ViewId *origin2est_id;
};

unsigned int num_view = 0;
unsigned int num_pts = 0;
unsigned int num_obs = 0;
double time_use = 0;

void load_tracks(const char *filename, Tracks &tracks, Est_Info &est_info);
void SetupOrientation(const char *filename, Attitude &global_R);

void SetupBaseFrame(Tracks &tracks, Attitude &global_R, Est_Info &est_info, BaseFrames &base_frames);

void ConstructF(Tracks &tracks, Attitude &global_R, Est_Info &est_info, spMatrix &ftf);
void Solution_Est2Origin(Est_Info &est_info, VectorXd &solution, G_Translations &translation);

void Direct_algorithm(Tracks &tracks, Est_Info &est_info, Attitude &global_R, G_Translations &translation, G_Points &points);

void Obtain_Solution_Eigen(MatrixXd &est_FtF, VectorXd &t_solution);
void Obtain_Solution_Eigen(spMatrix &est_FtF, VectorXd &t_solution);
void Obtain_Solution_Eigen(Map<MatrixXd> &est_FtF, RowVectorXd &H, VectorXd &t_solution);

void WriteTranslation(const char *filename, Attitude &orintations, G_Translations &translation);
void WriteTime(const char *filename);

typedef Vector3d *PtsCoor;

void combine_view_set(vector<set<ViewId>> &recovery_views,
                      set<ViewId> recovery_view)
{
    set<ViewId> tmp_recovery_view;
    vector<int> id_rec;
    //    cout<<"insert views:"<<*recovery_view.begin()<<","<<*(++recovery_view.begin())<<endl;

    for (int i = 0; i < recovery_views.size(); ++i)
    {
        tmp_recovery_view.clear();
        set_intersection(recovery_views[i].begin(),
                         recovery_views[i].end(),
                         recovery_view.begin(),
                         recovery_view.end(),
                         inserter(tmp_recovery_view,
                                  tmp_recovery_view.begin()));
        if (tmp_recovery_view.size() > 0)
        {
            id_rec.emplace_back(i);
        }
    }

    if (id_rec.size() == 0)
    {
        recovery_views.emplace_back(recovery_view);
    }
    else if (id_rec.size() == 1)
    {
        //        recovery_views[id_rec[0]].insert(*recovery_view.begin());
        //        recovery_views[id_rec[0]].insert(*(++recovery_view.end()));
        recovery_views[id_rec[0]].insert(recovery_view.begin(), recovery_view.end());
    }
    else if (id_rec.size() == 2)
    {
        recovery_views[id_rec[0]].insert(
            recovery_views[id_rec[1]].begin(),
            recovery_views[id_rec[1]].end());
        recovery_views.erase(recovery_views.begin() + id_rec[1]);
    }

    set<ViewId> &test = recovery_views[0];
    //    for(auto i=test.begin();i!=test.end();++i){
    //        cout<<*i;
    //    }
    //    cout<<endl;
}

void simplier_tracks(Tracks &tracks)
{
    //    auto start_time=steady_clock::now();

    MatrixXi recovery_view_map;

    recovery_view_map.setZero(num_view, num_view);
    unsigned int max_view = 100;

    for (int i = 0; i < tracks.size(); ++i)
    {
        Track &track = tracks[i].track;
        unsigned int tmp_sum = 0;
        for (int j = 0; j < track.size() - 1; ++j)
        {
            for (int k = j + 1; k < track.size(); ++k)
            {
                if (track[j].view_id < track[k].view_id)
                {
                    if (recovery_view_map(track[j].view_id, track[k].view_id) < max_view && recovery_view_map(track[j].view_id, track[k].view_id) > -1)
                    {
                        recovery_view_map(track[j].view_id, track[k].view_id)++;
                        tmp_sum++;
                    }
                }
                else
                {
                    if (recovery_view_map(track[k].view_id, track[j].view_id) < max_view && recovery_view_map(track[k].view_id, track[j].view_id) > -1)
                    {
                        recovery_view_map(track[k].view_id, track[j].view_id)++;
                        tmp_sum++;
                    }
                }
            }
        }

        if (tmp_sum > 0)
        {
            tracks[i].is_used = true;
        }

        //        cout<<"tracks stat:"<<tracks[i].is_used<<" ,tmp_sum="<<tmp_sum<<endl;
    }
    //    auto end_time=steady_clock::now();
    //    auto duration = duration_cast<microseconds>(end_time - start_time);
    //    cout <<  "===time cost for simplier agorithm"
    //          << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //          << "s" << endl;
}
void simplier_tracks2(Tracks &tracks)
{
    //    auto start_time=steady_clock::now();

    MatrixXi recovery_view_map;
    set<ViewId> recovery_view;
    vector<set<ViewId>> recovery_views;
    recovery_views.reserve(1);
    recovery_view_map.setZero(num_view, num_view);
    unsigned int max_view = 50;

    for (int i = 0; i < tracks.size(); ++i)
    {
        Track &track = tracks[i].track;
        if (recovery_views[0].size() == num_view)
        {
            break;
        }

        unsigned int tmp_sum = 0;
        for (int j = 0; j < track.size() - 1; ++j)
        {
            for (int k = j + 1; k < track.size(); ++k)
            {
                if (track[j].view_id < track[k].view_id)
                {
                    if (recovery_view_map(track[j].view_id, track[k].view_id) < max_view && recovery_view_map(track[j].view_id, track[k].view_id) > -1)
                    {
                        recovery_view_map(track[j].view_id, track[k].view_id)++;
                        tmp_sum++;
                    }
                    else if (recovery_view_map(track[j].view_id, track[k].view_id) == max_view)
                    {
                        recovery_view.clear();
                        recovery_view.insert(track[j].view_id);
                        recovery_view.insert(track[k].view_id);
                        combine_view_set(recovery_views, recovery_view);
                        recovery_view_map(track[j].view_id, track[k].view_id) = -1;
                    }
                }
                else
                {
                    if (recovery_view_map(track[k].view_id, track[j].view_id) < max_view && recovery_view_map(track[k].view_id, track[j].view_id) > -1)
                    {
                        recovery_view_map(track[k].view_id, track[j].view_id)++;
                        tmp_sum++;
                    }
                    else if (recovery_view_map(track[k].view_id, track[j].view_id) == max_view)
                    {
                        recovery_view.clear();
                        recovery_view.insert(track[k].view_id);
                        recovery_view.insert(track[j].view_id);
                        combine_view_set(recovery_views, recovery_view);
                        recovery_view_map(track[k].view_id, track[j].view_id) = -1;
                    }
                }
            }
        }
        if (tmp_sum > 0)
        {
            tracks[i].is_used = true;
        }

        //        cout<<"tracks stat:"<<tracks[i].is_used<<" ,tmp_sum="<<tmp_sum<<endl;
    }

    //    auto end_time=steady_clock::now();
    //    auto duration = duration_cast<microseconds>(end_time - start_time);
    //    cout <<  "===time cost for simplier agorithm"
    //          << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //          << "s" << endl;
}

void load_tracks(
    const char *filename,
    Tracks &tracks,
    Est_Info &est_info)
{
    auto start_time = steady_clock::now();

    // read tracks file

    fstream match_file(filename, ios::in);
    if (!match_file.is_open())
    {
        std::cout << "tracks file cannot load" << std::endl;
        return;
    }

    match_file >> num_view >> num_pts >> num_obs;

    int record_pts_id = -1;
    Track_info track_info;
    Obs_info tmp_obs;
    est_info.origin2est_id = new ViewId[num_view];

    tracks.reserve(num_pts);
    for (int i = 0; i < num_obs; i++)
    {
        match_file >> tmp_obs.view_id >> tmp_obs.pts_id >> tmp_obs.coord(0) >> tmp_obs.coord(1);
        tmp_obs.coord(0) = -tmp_obs.coord(0);
        tmp_obs.coord(1) = -tmp_obs.coord(1);
        tmp_obs.coord(2) = 1;

        if (tmp_obs.pts_id != record_pts_id)
        {
            if (i > 0)
            {

                if (track_info.track.size() < 3)
                {
                    track_info.is_used = 1; // only need use observation of tracking length>2
                }
                else
                {
                    track_info.is_used = 1;
                }
                tracks.emplace_back(track_info);
            }
            track_info.track.clear();
        }
        track_info.track.emplace_back(tmp_obs);
        record_pts_id = tmp_obs.pts_id;
    }
    match_file.close();
    tracks.emplace_back(track_info);

    for (int i = 0; i < num_view; ++i)
    {
        est_info.est_view.emplace_back(i);
        est_info.origin2est_id[i] = i;
    }

    //    sort(tracks.begin(),
    //         tracks.end(),compare_track_info);

    //    simplier_tracks(tracks);

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time for loading tracks file: "
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

// void load_tracks(
//         const char* filename,
//         Tracks& tracks,
//         Est_Info& est_info)
//{
//     auto start_time=steady_clock::now();

//    //read match file
//    std::cout<<"match_file is :"<<filename<<std::endl;
//    fstream match_file(filename,ios::in);
//    if (!match_file.is_open())
//    {
//        std::cout<<"match file cannot load"<<std::endl;
//        return;
//    }
//    int num_obs;
//    match_file>>num_view>>num_pts>>num_obs;

//    cout<<"#view="<<num_view<<", Pts="<<num_pts<<", Obs="<<num_obs<<endl;

//    int record_pts_id=-1;
//    Track_info track_info;
//    Obs_info tmp_obs;
//    est_info.origin2est_id=new ViewId[num_view];
//    for (int i=0;i<num_obs;i++){
//        match_file>>tmp_obs.view_id>>tmp_obs.pts_id>>tmp_obs.coord(0)>>tmp_obs.coord(1);
//        tmp_obs.coord(0)=-tmp_obs.coord(0);
//        tmp_obs.coord(1)=-tmp_obs.coord(1);
//        tmp_obs.coord(2)=1;

//        if(find(est_info.est_view.begin(),est_info.est_view.end(),tmp_obs.view_id)==est_info.est_view.end()){
//            est_info.origin2est_id[tmp_obs.view_id]=est_info.est_view.size();
//            est_info.est_view.emplace_back(tmp_obs.view_id);
//            //cout<<"inset img:"<<tmp_obs.view_id<<endl;
//        }

//        if( tmp_obs.pts_id != record_pts_id)
//        {
//            if(i>0)
//            {
//                track_info.is_used=true;
//                tracks.emplace_back(track_info);
//            }
//            track_info.track.clear();
//        }
//        track_info.track.emplace_back(tmp_obs);
//        record_pts_id=tmp_obs.pts_id;
//    }
//    match_file.close();
//    tracks.emplace_back(track_info);

//    auto end_time=steady_clock::now();
//    auto duration = duration_cast<microseconds>(end_time - start_time);
//    cout <<  "===time cost for load match file"
//          << double(duration.count()) * microseconds::period::num / microseconds::period::den
//          << "s" << endl;

//}

void SetupOrientationFromPAResults(
    const char *filename,
    Attitude &global_R)
{

    auto start_time = steady_clock::now();
    std::cout << "##start load global R from PA results file<<<" << endl;
    fstream cq_in;
    cq_in.open(filename, std::ios::in);
    if (!cq_in.is_open())
    {
        cout << "file cannot open" << endl;
        return;
    }
    cout << "file name:" << filename << endl;
    int cq_i = 0;
    cq_in >> num_view;
    global_R = new Matrix3d[num_view];
    Vector3d tmp_trans;
    for (int cq_i = 0; cq_i < num_view; cq_i++)
    {
        Eigen::Matrix3d tmp_R;
        cq_in >> tmp_R(0, 0) >> tmp_R(1, 0) >> tmp_R(2, 0);
        cq_in >> tmp_R(0, 1) >> tmp_R(1, 1) >> tmp_R(2, 1);
        cq_in >> tmp_R(0, 2) >> tmp_R(1, 2) >> tmp_R(2, 2);
        cq_in >> tmp_trans(0) >> tmp_trans(1) >> tmp_trans(2);
#ifdef ISDEBUG
        cout << "load global R id:" << cq_i << endl;
        cout << tmp_R << endl;
#endif
        // global_R.emplace_back(tmp_R);
        global_R[cq_i] = (tmp_R);
    }
    cq_in.close();
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "===time cost for setup global rotation"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void SetupOrientation(
    const char *filename,
    Attitude &global_R)
{

    auto start_time = steady_clock::now();
    std::cout << "##new test start load global R<<<" << endl;
    fstream cq_in;
    cq_in.open(filename, std::ios::in);
    if (!cq_in.is_open())
    {
        cout << "gR_file cannot open" << endl;
        return;
    }
    cout << "global Rotation file name:" << filename << endl;
    int cq_i = 0;
    cq_in >> num_view;
    global_R = new Matrix3d[num_view];
    for (int cq_i = 0; cq_i < num_view; cq_i++)
    {
        Eigen::Matrix3d tmp_R;
        cq_in >> tmp_R(0, 0) >> tmp_R(1, 0) >> tmp_R(2, 0);
        cq_in >> tmp_R(0, 1) >> tmp_R(1, 1) >> tmp_R(2, 1);
        cq_in >> tmp_R(0, 2) >> tmp_R(1, 2) >> tmp_R(2, 2);
#ifdef ISDEBUG
        cout << "load global R id:" << cq_i << endl;
        cout << tmp_R << endl;
#endif
        // global_R.emplace_back(tmp_R);
        global_R[cq_i] = (tmp_R);
    }
    cq_in.close();
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "===time cost for setup global rotation"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void SetupBaseFrame(
    Tracks &tracks,
    Attitude &global_R,
    Est_Info &est_info,
    BaseFrames &base_frames)
{
    auto start_time = steady_clock::now();
    Vector3d Rx;
    Vector3d xRx;
    Matrix3d R_lr;

    double s;

    base_frames.lbase_frames = new ViewId[num_pts]();
    base_frames.rbase_frames = new ViewId[num_pts]();
    base_frames.base_s = new double[num_pts]();

    unsigned int i = 0;
    unsigned int j = 0;
    unsigned int k = 0;

    unsigned int max_id_l = 0;
    unsigned int max_id_r = 0;
    double max_s = 0;
    for (i = 0; i < num_pts; ++i)
    {
        if (!tracks[i].is_used)
            continue;
        Track &ptr_track = tracks[i].track;
        const unsigned int &track_length = ptr_track.size();
        max_id_l = 0;
        max_id_r = 0;
        max_s = 0;
        for (j = 0; j < track_length - 1; ++j)
        {
            const ViewId &left_view = ptr_track[j].view_id;
            const Vector3d &x_l = ptr_track[j].coord;
            const Vector3d Rl_xl = global_R[left_view].transpose() * x_l;
            for (k = j + 1; k < track_length; ++k)
            {
                const ViewId &right_view = ptr_track[k].view_id;
                Matrix3d &R_r = global_R[right_view];
                Rx = R_r * Rl_xl;
                xRx = ptr_track[k].coord.cross(Rx);
                s = xRx.norm();
                if (s > max_s)
                {
                    max_s = s;
                    max_id_l = j;
                    max_id_r = k;
                }
            }
        }
        base_frames.base_s[i] = max_s;
        base_frames.lbase_frames[i] = max_id_l;
        base_frames.rbase_frames[i] = max_id_r; // base_frame index in tracks
    }

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "===time cost for SetupBaseFrames function:"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;

    //    int n_test=10;
    //    int *ia=new int[n_test*n_test];
    //    int *ja=new int[n_test*n_test];
    //    double *a=new double[n_test*n_test];
    //    int rec=0;
    //    for(i=0;i<n_test;++i){
    //        for(j=0;j<n_test;++j){
    //            ia[rec]=i;
    //            ja[rec]=j;
    //            a[rec]=rec;
    //            rec++;
    //        }
    //    }

    //    start_time=steady_clock::now();
    // #include<Eigen/SparseCore>
    //    Map<SparseMatrix<double>> sp_test(10,10,rec,ia,ja,a,1);

    //     end_time=steady_clock::now();
    //     duration = duration_cast<microseconds>(end_time - start_time);
    //    cout <<  "===time cost for Setup Triplet sparse :"
    //          << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //          << "s" << endl;
    //    cout<<sp_test<<endl;
}

#include <Eigen/Cholesky>
void linearSolveView(Tracks &tracks,
                     Attitude &global_R,
                     G_Translations &translation,
                     Est_Info &est_info,
                     BaseFrames &base_frames,
                     const ViewId &view_id)
{
    auto start_time = steady_clock::now();
    Size est_view_size = est_info.est_view.size();
    Size est_t_length = 3 * est_view_size;

    ViewId *origin2est_id = est_info.origin2est_id;

    Matrix3d cross_xr, cross_xc, cross_Rl_xl, cross_Rl_tl;

    double s_lr, s_lc;
    Matrix3d R_lr, R_lc;
    Vector3d Rlc_xl;
    Vector3d m3_lr, m3_lc;
    RowVector3d tmp_a;
    Matrix3d tmp_B, tmp_C, tmp_D;
    unsigned int track_len;
    unsigned int i = 0;
    unsigned int j;
    PtsId pts_len = num_pts;
    Vector3d Rlr_xl, R_lc_x_l, xc_R_lc_x_l;
    RowVector3d tmp_A_R_lr;
    ViewId id_r, id_l, id_c;
    Matrix<double, 3, 6> J;
    Matrix<double, 6, 6> JtJ;
    Matrix<double, 6, 1> Jtb;
    JtJ.setZero(6, 6);
    Jtb.setZero(6, 1);
    int rec_id = -1;
    for (i = 0; i < pts_len; ++i)
    {
        Track &ptr_track = tracks[i].track;

        // find if exist view_id in this track of point
        rec_id = -1;
        track_len = ptr_track.size();
        for (j = 0; j < track_len; ++j)
        {
            ViewId &current_view = ptr_track[j].view_id;
            ViewId &new_c_view = origin2est_id[current_view];
            if (new_c_view == view_id)
            {
                rec_id = j;
            }
        }

        ViewId &left_base = base_frames.lbase_frames[i];
        ViewId &right_base = base_frames.rbase_frames[i];
        ViewId &lbase_view = ptr_track[left_base].view_id;
        ViewId &rbase_view = ptr_track[right_base].view_id;

        if (rec_id == -1 || left_base == rec_id)
            continue;

        if (rec_id == rbase_view)
        {
            ViewId &current_view = ptr_track[rec_id].view_id;

            ViewId &new_c_view = origin2est_id[current_view];
            ViewId &new_l_view = origin2est_id[lbase_view];
            ViewId &new_r_view = origin2est_id[rbase_view];

            Matrix3d &R_l_base = global_R[lbase_view];
            Matrix3d &R_r_base = global_R[rbase_view];
            Matrix3d &R_c = global_R[current_view];

            Vector3d &t_l = translation[lbase_view];
            Vector3d &t_r = translation[rbase_view];
            Vector3d &t_c = translation[current_view];

            Vector3d &x_l = ptr_track[left_base].coord;
            Vector3d &x_r = ptr_track[right_base].coord;
            Vector3d &x_c = ptr_track[rec_id].coord;

            R_lr.noalias() = R_r_base * R_l_base.transpose();
            R_lc.noalias() = R_c * R_l_base.transpose();

            Rlr_xl.noalias() = R_lr * x_l;
            Rlc_xl.noalias() = R_lc * x_l;
            auto Rlc_tl = R_lc * t_l;
            auto Rlr_tl = R_lr * t_l;

            auto Rl_xl = R_l_base.transpose() * x_l;
            auto Rl_tl = R_l_base.transpose() * t_l;

            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;
            cross_xc << 0, -x_c(2), x_c(1),
                x_c(2), 0, -x_c(0),
                -x_c(1), x_c(0), 0;
            cross_Rl_xl << 0, -Rl_xl(2), Rl_xl(1),
                Rl_xl(2), 0, -Rl_xl(0),
                -Rl_xl(1), Rl_xl(0), 0;
            cross_Rl_tl << 0, -Rl_tl(2), Rl_tl(1),
                Rl_tl(2), 0, -Rl_tl(0),
                -Rl_tl(1), Rl_tl(0), 0;

            m3_lr.noalias() = x_r.cross(Rlr_xl);
            m3_lc.noalias() = x_c.cross(Rlc_xl);

            s_lr = m3_lr.norm();
            s_lc = m3_lc.norm();

            //            tmp_a.noalias()=m3_lr.transpose()*cross_xr;
            //            tmp_a=-tmp_a;

            //            tmp_B.noalias()=m3_lc*tmp_a;
            //            tmp_C.noalias()=s_lr*s_lr*cross_xc;
            //            tmp_D.noalias()=-(tmp_B*R_lr+tmp_C*R_lc);

            //            double a_tr=tmp_a*t_r;
            //            double a_Rlr_tl=tmp_a*R_lr*t_l;

            //            Matrix3d dm3lc_dc=-cross_xc*R_c*cross_Rl_xl;
            //            Matrix3d dm3lr_dr=-cross_xr*R_r_base*cross_Rl_xl;

            //            Matrix3d dBtr_dc=a_tr*dm3lc_dc;
            //            Matrix3d dBtr_dr=-m3_lc*(cross_xr*t_r).transpose()*dm3lr_dr;
            //            dBtr_dc+=dBtr_dr;

            //            RowVector3d dm3_dr=(m3_lr.transpose()*dm3lr_dr)*2;

            ////            //auto dCtc_dc=0;
            //            Matrix3d dCtc_dr=(cross_xc*t_c)*dm3_dr;
            //            Matrix3d dCtc_dc=dCtc_dr;

            //            Matrix3d dBRlr_tl_dc=a_Rlr_tl*dm3lc_dc;
            //            Matrix3d dCRlc_tl_dc=-tmp_C*R_c*cross_Rl_tl;

            //            Matrix3d dBRlr_tl_dr=-m3_lc*(cross_xr*Rlr_tl).transpose()*dm3lr_dr;
            //            dBRlr_tl_dr+=-tmp_B*R_r_base*cross_Rl_tl;

            //            Matrix3d dCRlc_tl_dr=(cross_xc*Rlc_tl)*dm3_dr;

            //            Matrix3d dDtl_dc=-(dCRlc_tl_dc+dBRlr_tl_dc);
            //            Matrix3d dDtl_dr=-(dCRlc_tl_dr+dBRlr_tl_dr);
            //            dDtl_dc+=dDtl_dr;

            //            Matrix3d df_dc=dBtr_dc+dCtc_dc+dDtl_dc;
            //            Matrix3d df_dt=tmp_B+tmp_C;

            //            Vector3d b=tmp_B*t_r+tmp_C*t_c+tmp_D*t_l;
            //            J<<df_dc,df_dt;
            //            JtJ+=J.transpose()*J;
            //            Jtb+=J.transpose()*b;
            ////            cout<<"J:"<<endl<<J<<endl;
            ////            cout<<"JtJ:"<<endl<<JtJ<<endl;
            ////            cout<<"Jtb"<<endl<<Jtb<<endl;
        }
        else
        {
        }
    }

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time cost for linearSolve_Direct:"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

#define assignment_sp_cq(ftf, id_l, id_r, source_mat)                 \
    ftf.coeffRef(3 * (id_l) + 0, 3 * (id_r) + 0) += source_mat(0, 0); \
    ftf.coeffRef(3 * (id_l) + 1, 3 * (id_r) + 0) += source_mat(1, 0); \
    ftf.coeffRef(3 * (id_l) + 2, 3 * (id_r) + 0) += source_mat(2, 0); \
    ftf.coeffRef(3 * (id_l) + 0, 3 * (id_r) + 1) += source_mat(0, 1); \
    ftf.coeffRef(3 * (id_l) + 1, 3 * (id_r) + 1) += source_mat(1, 1); \
    ftf.coeffRef(3 * (id_l) + 2, 3 * (id_r) + 1) += source_mat(2, 1); \
    ftf.coeffRef(3 * (id_l) + 0, 3 * (id_r) + 2) += source_mat(0, 2); \
    ftf.coeffRef(3 * (id_l) + 1, 3 * (id_r) + 2) += source_mat(1, 2); \
    ftf.coeffRef(3 * (id_l) + 2, 3 * (id_r) + 2) += source_mat(2, 2);

void ConstructF(Tracks &tracks,
                Attitude &global_R,
                Est_Info &est_info,
                spMatrix &ftf)
{
    auto start_time = steady_clock::now();
    Size est_view_size = est_info.est_view.size();
    Size est_length = 3 * (est_view_size + num_pts);

    Matrix3d cross_xr, cross_xc;

    Matrix3d R_lr, R_lc;
    Vector3d Rlc_xl;
    Vector3d m3_lr, m3_lc;
    RowVector3d tmp_A;
    Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;
    Matrix3d FtF_ll, FtF_rr, FtF_cc;
    Matrix3d FtF_coview;

    unsigned int i = 0;
    unsigned int j = 0;

    PtsId pts_len = num_pts;
    Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
    RowVector3d tmp_A_R_lr;

    unsigned int used_pts = 0;
    unsigned int nnz = 9 * num_pts + 2 * 9 * num_pts * num_view + 9 * num_view * num_view;

    ftf.resize(est_length, est_length);
    ftf.reserve(nnz);

    for (i = 0; i < pts_len; ++i)
    {
        if (!tracks[i].is_used)
            continue;
        used_pts++;
        Track &ptr_track = tracks[i].track;
        const unsigned int &track_length = ptr_track.size();

        for (j = 0; j < track_length; ++j)
        {
            const ViewId &id_view = ptr_track[j].view_id;
            const PtsId &id_pt = ptr_track[j].pts_id;
            const Matrix3d &tmp_R = global_R[id_view];

            RowVector3d m1 = tmp_R.row(0);
            RowVector3d m2 = tmp_R.row(1);
            RowVector3d m3 = tmp_R.row(2);

            double &x = ptr_track[j].coord(0);
            double &y = ptr_track[j].coord(1);

            Matrix<double, 2, 3> A1;
            A1.setZero();
            A1.row(0) = x * m3 - m1;
            A1.row(1) = y * m3 - m2;

            Matrix<double, 2, 3> A2;
            A2 << -1, 0, x,
                0, -1, y;

            Matrix3d tmp_data = A1.transpose() * A1;
            assignment_sp_cq(ftf, id_pt, id_pt, tmp_data);

            tmp_data = A1.transpose() * A2;
            assignment_sp_cq(ftf, id_pt, num_pts + id_view, tmp_data);

            tmp_data = A2.transpose() * A1;
            assignment_sp_cq(ftf, num_pts + id_view, id_pt, tmp_data);

            tmp_data = A2.transpose() * A2;
            assignment_sp_cq(ftf, num_pts + id_view, num_pts + id_view, tmp_data);
        }
        //        cout<<"processing:"<<i<<endl;
    }
    //    MatrixXd ftf_test;
    //    ftf_test.setZero(3*(num_pts+num_view),3*(num_pts+num_view));
    //    ftf_test=ftf;
    //    fstream output("ftf.txt",std::ios::out | ios::trunc);
    //    if(!output.is_open()){
    //        cout<<"time file cannot create, please check path"<<endl;
    //        return;
    //    }
    //    output<<setprecision(16)<<ftf_test<<endl;
    //    output.close();
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time cost for construct L matrix:"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}
#define assignment_sp_cq_vector(triplet_list, id_l, id_r, source_mat)                      \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 0, 3 * (id_r) + 0, source_mat(0, 0))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 1, 3 * (id_r) + 0, source_mat(1, 0))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 2, 3 * (id_r) + 0, source_mat(2, 0))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 0, 3 * (id_r) + 1, source_mat(0, 1))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 1, 3 * (id_r) + 1, source_mat(1, 1))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 2, 3 * (id_r) + 1, source_mat(2, 1))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 0, 3 * (id_r) + 2, source_mat(0, 2))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 1, 3 * (id_r) + 2, source_mat(1, 2))); \
    triplet_list.push_back(ele_triplet(3 * (id_l) + 2, 3 * (id_r) + 2, source_mat(2, 2)));
void ConstructF_2(Tracks &tracks,
                  Attitude &global_R,
                  Est_Info &est_info,
                  spMatrix &A,
                  spMatrix &B,
                  spMatrix &C)
{
    auto start_time = steady_clock::now();
    Size est_view_size = est_info.est_view.size();
    Size est_length = 3 * (est_view_size + num_pts);

    unsigned int i = 0;
    unsigned int j = 0;

    PtsId pts_len = num_pts;
    Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
    RowVector3d tmp_A_R_lr;

    unsigned int used_pts = 0;
    unsigned int nnz_A = 9 * num_pts;
    unsigned int nnz_B = 2 * 9 * num_pts * num_view;
    unsigned int nnz_C = 9 * num_view;

    A.resize(3 * num_pts, 3 * num_pts);
    A.reserve(nnz_A);

    B.resize(3 * num_pts, 3 * num_view);
    B.reserve(nnz_B);

    C.resize(3 * num_view, 3 * num_view);
    C.reserve(nnz_C);

    // setting of sparse matrix A
    Matrix3d tmp_data_A;
    vector<ele_triplet> A_triplet_list;

    // setting of sparse matrix B
    Matrix3d tmp_data_B;
    vector<ele_triplet> B_triplet_list;

    // setting of sparse matrix C
    vector<Matrix3d> tmp_data_C;
    vector<ele_triplet> C_triplet_list;

    tmp_data_C.reserve(num_view);
    for (i = 0; i < num_view; ++i)
    {
        tmp_data_C.emplace_back(Matrix3d::Zero());
    }

    for (i = 0; i < pts_len; ++i)
    {
        if (!tracks[i].is_used)
            continue;
        used_pts++;
        Track &ptr_track = tracks[i].track;
        const unsigned int &track_length = ptr_track.size();

        tmp_data_A.setZero();
        for (j = 0; j < track_length; ++j)
        {
            ViewId &id_view = ptr_track[j].view_id;
            PtsId &id_pt = ptr_track[j].pts_id;
            const Matrix3d &tmp_R = global_R[id_view];

            RowVector3d m1 = tmp_R.row(0);
            RowVector3d m2 = tmp_R.row(1);
            RowVector3d m3 = tmp_R.row(2);

            double &x = ptr_track[j].coord(0);
            double &y = ptr_track[j].coord(1);

            Matrix<double, 2, 3> A1;
            A1.setZero();
            A1.row(0) = x * m3 - m1;
            A1.row(1) = y * m3 - m2;

            Matrix<double, 2, 3> A2;
            A2 << -1, 0, x,
                0, -1, y;

            Matrix3d tmp_data = A1.transpose() * A1;
            tmp_data_A += tmp_data;

            // construct B
            tmp_data = A1.transpose() * A2;
            // assigment for B
            assignment_sp_cq_vector(B_triplet_list, i, id_view, tmp_data);

            // construct C
            tmp_data = A2.transpose() * A2;
            tmp_data_C[id_view] += tmp_data;
        }
        // assigment for A
        assignment_sp_cq_vector(A_triplet_list, i, i, tmp_data_A);
    }

    for (i = 0; i < num_view; ++i)
    {
        // assigment for C
        assignment_sp_cq_vector(C_triplet_list, i, i, tmp_data_C[i]);
    }
    A.setFromTriplets(A_triplet_list.begin(), A_triplet_list.end());
    B.setFromTriplets(B_triplet_list.begin(), B_triplet_list.end());
    C.setFromTriplets(C_triplet_list.begin(), C_triplet_list.end());

    //    MatrixXd A_out;
    //    A_out=B;
    //    fstream output("ftf.txt",std::ios::out | ios::trunc);
    //    if(!output.is_open()){
    //        cout<<"time file cannot create, please check path"<<endl;
    //        return;
    //    }
    //    output<<setprecision(16)<<A_out<<endl;
    //    output.close();

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time cost for construct L matrix:"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}
void Solution_Est2Origin(
    Est_Info &est_info,
    VectorXd &solution,
    G_Translations &translation)
{
    auto start_time = steady_clock::now();

    Est_View re_est_view = est_info.est_view;
    cout << "re_est_view size:" << re_est_view.size() << endl;
    // est part
    translation = new Vector3d[num_view];
    for (int i = 0; i < num_view; i++)
    {
        translation[i] = solution.middleRows<3>(3 * i);
    }
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time cost for recovery solution:"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void Obtain_Solution_Eigen(MatrixXd &est_FtF, VectorXd &t_solution)
{

    auto start_time = steady_clock::now();
    DenseSymShiftSolve<double> op(est_FtF);
    // Construct eigen solver object with shift 0
    // This will find eigenvalues that are closest to 0
    SymEigsShiftSolver<double, LARGEST_MAGN,
                       DenseSymShiftSolve<double>>
        eigs(&op, 1, 8, -1e-8);

    eigs.init();
    eigs.compute();

    Eigen::VectorXd evalues = eigs.eigenvalues();
    cout << evalues << endl;
    int remain_size = est_FtF.cols();
    t_solution.setZero(remain_size + 3);

    t_solution.bottomRows(remain_size) = eigs.eigenvectors(1); // wait for change

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time cost for Eigen (dense)Decomposition :"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void Obtain_Solution_Eigen(spMatrix &est_FtF, VectorXd &t_solution)
{
    auto start_time = steady_clock::now();
    SparseSymShiftSolve<double> op(est_FtF);
    // Construct eigen solver object with shift 0
    // This will find eigenvalues that are closest to 0
    SymEigsShiftSolver<double, LARGEST_MAGN,
                       SparseSymShiftSolve<double>>
        eigs(&op, 1, 8, -1e-8);

    //    auto inner_start_time=steady_clock::now();
    eigs.init();
    eigs.compute();
    //    auto inner_end_time=steady_clock::now();
    //    auto inner_duration = duration_cast<microseconds>(inner_end_time - inner_start_time);
    //    cout <<  "time cost for Eigen (sparse_inner)Decomposition :"
    //          << double(inner_duration.count()) * microseconds::period::num / microseconds::period::den
    //          << "s" << endl;

    Eigen::VectorXd evalues = eigs.eigenvalues();
    cout << "eigenvalue:" << evalues << endl;

    int remain_size = est_FtF.cols();
    t_solution.setZero(remain_size + 3);
    t_solution.bottomRows(remain_size) = eigs.eigenvectors(1);

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    cout << "time cost for Eigen (sparse)Decomposition :"
         << double(duration.count()) * microseconds::period::num / microseconds::period::den
         << "s" << endl;
}

void Direct_algorithm(
    Tracks &tracks,
    Est_Info &est_info,
    Attitude &global_R,
    G_Translations &translation,
    G_Points &points)
{
    auto start_time = steady_clock::now();

    if (est_info.est_view.size() > 1)
    {

        Size est_view_size = est_info.est_view.size();
        Size est_length = 3 * (est_view_size + num_pts);
        int est_t_length = 3 * est_info.est_view.size();
        int remain_size = est_t_length - 3;

        spMatrix FtF;
        unsigned int nnz = 9 * num_pts + 2 * 9 * num_pts * num_view + 9 * num_view * num_view;

        FtF.resize(est_length, est_length);
        FtF.reserve(nnz);
        spMatrix A, B1, C1;

        ConstructF_2(tracks, global_R, est_info, A, B1, C1);

        spMatrix B, C;
        B = B1.rightCols(3 * (num_view - 1));
        C = C1.bottomRightCorner(3 * num_view - 3, 3 * num_view - 3);

        //            ConstructF(tracks,global_R,est_info,FtF);

        //            VectorXd t_solution;
        //            cout<<endl<<"==================Eigen Time======================"<<endl;
        //            Obtain_Solution_Eigen(est_FtF,H,t_solution);
        //            Solution_Est2Origin(est_info,global_R,t_solution,translation);

        //            spMatrix A=FtF.topLeftCorner(3*num_pts,3*num_pts);
        //            spMatrix B=FtF.topRightCorner(3*num_pts,3*num_view-3);
        //            spMatrix C=FtF.bottomRightCorner(3*num_view-3,3*num_view-3);

        //            MatrixXd ftf_test;
        //            ftf_test.setZero(3*(num_pts+num_view),3*(num_pts+num_view));
        //            ftf_test=C;
        //            fstream output("ftf.txt",std::ios::out | ios::trunc);
        //            if(!output.is_open()){
        //                cout<<"time file cannot create, please check path"<<endl;
        //                return;
        //            }
        //            output<<setprecision(16)<<ftf_test<<endl;
        //            output.close();

        //            fstream output("FtF.txt",std::ios::out|ios::trunc);
        //            if(!output.is_open()){
        //                cout<<"output file cannot create, please check path"<<endl;
        //                return;
        //            }

        //            output<<setprecision(16)<<est_FtF<<endl;
        //            output.close();

        VectorXd t_solution, X_solution;
        cout << endl
             << "==================Eigen Time======================" << endl;

        MatrixXd A_T;
        Eigen::SimplicialCholesky<spMatrix> chol(A);
        MatrixXd CBtAB = C - B.transpose() * chol.solve(B);

        Obtain_Solution_Eigen(CBtAB, t_solution);
        Solution_Est2Origin(est_info, t_solution, translation);

        Eigen::VectorXd Bt = -B * t_solution.tail(3 * num_view - 3);
        X_solution = chol.solve(Bt);

        points = new Vector3d[num_pts];
        for (int i = 0; i < num_pts; i++)
        {
            points[i] = X_solution.middleRows<3>(3 * i);
        }
    }
    else
    {
        cout << "only one image" << endl;
        VectorXd t_solution, X_solution;
        t_solution.setZero(num_view);
        X_solution.setZero(num_pts);
    }
    auto end_time = steady_clock::now();
    double dr_ms = std::chrono::duration<double>(end_time - start_time).count();
    cout << "time cost for Direct algorithm:"
         << dr_ms << "s" << endl;
    cout << "*********************************************************" << endl;
    time_use = dr_ms;

    //    start_time=steady_clock::now();
    //    Vector3d pts[227615];
    //    for(int i=0;i<227615;++i){
    //        obtain_3dpt(tracks,global_R,translation,pts[i],i);
    //    }

    // //    analytical_reconstruction(tracks,global_R,translation,pts);
    //    end_time=steady_clock::now();
    //         dr_ms=std::chrono::duration<double>(end_time-start_time).count();
    //        cout <<  "time cost for obtain 3dpts algorithm:"
    //              << dr_ms<< "s" << endl;
    //        cout <<  "*********************************************************"<<endl;
}

void WriteTranslation(
    const char *filename,
    Attitude &orintations,
    G_Translations &translation)
{
    fstream output(filename, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        cout << "output file cannot create, please check path" << endl;
        return;
    }

    Size t_size = num_view;
    Vector3d *tmp_position = NULL;
    Matrix3d *tmp_R = NULL;
    unsigned int i = 0;

    output << t_size << endl;

    for (i = 0; i < t_size; ++i)
    {
        tmp_position = &translation[i];
        tmp_R = &orintations[i];

        output << setprecision(16) << endl
               << (*tmp_R)(0, 0) << " " << (*tmp_R)(1, 0) << " " << (*tmp_R)(2, 0) << " ";
        output << setprecision(16) << (*tmp_R)(0, 1) << " " << (*tmp_R)(1, 1) << " " << (*tmp_R)(2, 1) << " ";
        output << setprecision(16) << (*tmp_R)(0, 2) << " " << (*tmp_R)(1, 2) << " " << (*tmp_R)(2, 2) << " ";

        output << setprecision(16) << (*tmp_position)(0) << " " << (*tmp_position)(1) << " " << (*tmp_position)(2);
    }
    output.close();
}

void WritePoints(
    const char *filename,
    G_Points &points)
{
    fstream output(filename, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        cout << "output file cannot create, please check path" << endl;
        return;
    }

    Vector3d *tmp_position = NULL;

    unsigned int i = 0;

    for (i = 0; i < num_pts; ++i)
    {
        tmp_position = &points[i];

        output << setprecision(16) << (*tmp_position)(0) << " " << (*tmp_position)(1) << " " << (*tmp_position)(2) << endl;
    }
    output.close();
}

void WriteTime(const char *filename)
{
    fstream output(filename, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        cout << "time file cannot create, please check path" << endl;
        return;
    }
    output << setprecision(16) << time_use << ' ' << 1 << endl;
    output.close();
}
int main(int argc, char *argv[])
{
    //    if( argc<5){
    //        cout<<"usage: "<<
    //              "--tracks_file=? "<<
    //              "--gR_file=? "<<
    //              "--output_file=?"<<
    //              "--time_file=?"<<endl;
    //        cout<<"tracks_file: tracks file supported by user or dataset"<<endl;
    //        cout<<"gR_file: global rotation file"<<endl;
    //        cout<<"output_file: storaging output global R&t"<<endl;
    //        cout<<"output_pts_file: storaging output 3dpts"<<endl;
    //        cout<<"time_file: time for running Direct algorithm"<<endl;
    //        return 1;
    //    }

    google::ParseCommandLineFlags(&argc, &argv, true);

    Tracks tracks;

    Attitude global_R;
    G_Translations translation;
    G_Points points;
    Est_Info est_info;
    SetupOrientation(FLAGS_gR_file.c_str(), global_R);
    load_tracks(FLAGS_tracks_file.c_str(), tracks, est_info);
    Direct_algorithm(tracks, est_info, global_R, translation, points);

    WriteTranslation(FLAGS_output_file.c_str(), global_R, translation);
    WritePoints(FLAGS_output_pts_file.c_str(), points);
    WriteTime(FLAGS_time_file.c_str());

    google::ShutDownCommandLineFlags();
    return 0;
}
