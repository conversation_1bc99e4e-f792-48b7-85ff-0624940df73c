// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <fstream>
#include <iomanip>
#include <cstdio>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <glog/logging.h>

#include "povg_timer.hpp"
#include "relative_residuals.hpp"

#include <Eigen/Dense>
#include <unsupported/Eigen/Polynomials>
#include <complex>

#include <algorithm>
#include <numeric>
#include <vector>
#include <cmath>

#include <limits>



using namespace std;
using namespace Eigen;


enum RelativeResidualTypes {
    RESIDUAL_BA,
    RESIDUAL_COPLANAR,
    RESIDUAL_KNEIP,
    RESIDUAL_OPENGV,
    RESIDUAL_OPENGV_WITH_POINTS3D, // Since there's a function overload, differentiate by context
    RESIDUAL_PPO,
    RESIDUAL_PPO_INVD,
    RESIDUAL_PPO_BVA_INVD,
    RESIDUAL_LIGT_DIRECT,
    RESIDUAL_LIGT_D3,
    RESIDUAL_LIGT_LMAT,
    RESIDUAL_LIGT,
    RESIDUAL_LIRT,
    RESIDUAL_ALL
};



DEFINE_string(mode,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/matches/Matches_10Image_1000Points_10.00Dt_0.txt",
              "file of matching point pairs to use.");

DEFINE_string(relative_pose_file,
              "/home/<USER>/relative_pose_file.txt",
              "file of 3 smallest eigenvectors for A matrix");

DEFINE_string(bearing_vector_file,
              "/home/<USER>/bearing_vector_file.txt",
              "file of bearing vectors");

DEFINE_string(weights_file,
              "/home/<USER>/weights_file.txt",
              "file of bearing vectors");

DEFINE_string(output_file,
              "/home/<USER>/Error.csv",
              "Type of SfM reconstruction estimation to use.");


DEFINE_string(time_file,
              "time_output_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(solve_mode,
              "eigen_solve",
              "0: polynomial eigenvalue solve method");

DEFINE_bool(debug_info,
            false,
            "0: polynomial eigenvalue solve method");

using namespace std;

using namespace  POVG;

#define PI 3.141592653589793


RelativeResidualTypes stringToEnum(const std::string& method) {
    static const std::map<std::string, RelativeResidualTypes> stringToEnumMap = {
        {"residual_ba", RESIDUAL_BA},
        {"residual_coplanar", RESIDUAL_COPLANAR},
        {"residual_kneip", RESIDUAL_KNEIP},
        {"residual_opengv", RESIDUAL_OPENGV},
        {"residual_opengv_with_points3d", RESIDUAL_OPENGV_WITH_POINTS3D},
        {"residual_ppo", RESIDUAL_PPO},
        {"residual_ppo_invd", RESIDUAL_PPO_INVD},
        {"residual_ppo_bva_invd", RESIDUAL_PPO_BVA_INVD},
        {"residual_ligt_direct", RESIDUAL_LIGT_DIRECT},
        {"residual_ligt_d3", RESIDUAL_LIGT_D3},
        {"residual_ligt_lmat", RESIDUAL_LIGT_LMAT},
        {"residual_ligt", RESIDUAL_LIGT},
        {"residual_lirt", RESIDUAL_LIRT},
        {"all", RESIDUAL_ALL}
    };

    auto it = stringToEnumMap.find(method);
    if (it != stringToEnumMap.end()) {
        return it->second;
    } else {
        // Handle the error case, for example:
        throw std::runtime_error("Invalid method string provided.");
    }
}

double findMedian(VectorXd a);
double findMedian(vector<double> a);


void load_weights_file(const std::string& weights_file,
                       VectorXd& weights) {


    POVG::Timer timer;

    // load weights file
    std::fstream sfile(weights_file, ios::in);
    if (!sfile.is_open()) {
        std::cerr << "weights file cannot load";
        return;
    }

    int num_matches;
    sfile >> num_matches;

    weights.setZero(num_matches,1);

    for (unsigned int i = 0; i < num_matches; ++i) {
        sfile >> weights(i);
    }

    sfile.close();

    // print time cost information
    //    cout << ">> time for loading eigen sols file: "
    //         << timer.Duration()
    //         << "s"
    //         << endl;
}



void load_bearing_info(const std::string& bearing_file,
                       BearingVector& v1,
                       BearingVector& v2) {

    POVG::Timer timer;

    std::fstream sfile(bearing_file, ios::in);
    if (!sfile.is_open()) {
        std::cerr << "bearing file cannot load";
        return;
    }

    int num_matches;
    sfile >> num_matches;

    v1.setZero(3,num_matches);
    v2.setZero(3,num_matches);

    for (unsigned int i = 0; i < num_matches; ++i) {
        sfile >> v1(0,i) >> v1(1,i) >> v1(2,i)
                         >> v2(0,i) >> v2(1,i) >> v2(2,i);
    }

    sfile.close();

}
auto findRoots(const Eigen::VectorXd& coefficients) {

    Eigen::VectorXd p = coefficients;
    p = p/p(0);		//转换为最高次系数为1的标准形式

    MatrixXd matrixXd(p.size()-1,p.size()-1);
    for (int i = 0; i < p.size()-1; ++i) {
        matrixXd(i,0) = -p(i+1);
        for (int j = 1; j < p.size()-1; ++j) {
            if(i+1 == j )
                matrixXd(i,j) = 1;
            else
                matrixXd (i,j) = 0;
        }
    }

    auto evalue = matrixXd.eigenvalues();

    return evalue;
}


double findMedian(VectorXd a) {
    int n = a.size();

    // 创建一个临时向量并复制数据
    VectorXd temp = a;

    // 排序临时向量
    std::sort(temp.data(), temp.data() + temp.size());

    // 如果数组大小是偶数
    if (n % 2 == 0) {
        return (temp[(n - 1) / 2] + temp[n / 2]) / 2.0;
    }
    // 如果数组大小是奇数
    else {
        return temp[n / 2];
    }
}

double findMedian(vector<double> a)
{

    int n = a.size();
    // If size of the arr[] is even
    if (n % 2 == 0) {

        // Applying nth_element
        // on n/2th index
        nth_element(a.begin(),
                    a.begin() + n / 2,
                    a.end());

        // Applying nth_element
        // on (n-1)/2 th index
        nth_element(a.begin(),
                    a.begin() + (n - 1) / 2,
                    a.end());

        // Find the average of value at
        // index N/2 and (N-1)/2
        return (double)(a[(n - 1) / 2]
                + a[n / 2])
                / 2.0;
    }

    // If size of the arr[] is odd
    else {

        // Applying nth_element
        // on n/2
        nth_element(a.begin(),
                    a.begin() + n / 2,
                    a.end());

        // Value at index (N/2)th
        // is the median
        return (double)a[n / 2];
    }
}

void load_pose(const std::string& pose_file, Pose& pose) {

    POVG::Timer timer;

    std::ifstream file(pose_file);
    if (!file.is_open()) {
        std::cerr << "Pose file cannot be opened." << std::endl;
        return;
    }

    for (int i = 0; i < 3; ++i) {
        for (int j = 0; j < 3; ++j) {
            file >> pose.rotations(j,i);  // 注意这里使用了转置
        }
    }

    for (int i = 0; i < 3; ++i) {
        file >> pose.translations(i);
    }

    cout << ">> time for loading pose file: "
         << timer.Duration()
         << "s"
         << endl;
}
void writeErrorTable(const MethodErrors& methodErrs, const string& filePath) {
    ofstream csvFile(filePath, ios::out);  // 使用输出模式，每次调用都重写文件

    // 首先构造表头
    string header;
    for (const auto& methodError : methodErrs) {
        // 为每个错误指标添加方法名作为前缀
        header += methodError.method + "_meanErr,";
        header += methodError.method + "_medErr,";
        header += methodError.method + "_meanZErr,";
    }
    if (!header.empty()) {
        header.pop_back();  // 移除最后一个逗号
    }
    csvFile << header << endl;

    // 然后在一行内写入所有数据
    for (const auto& methodError : methodErrs) {
        // 写入数据：method_meanErr, method_medErr, method_meanZErr
        csvFile << fixed << setprecision(10);  // 设置固定的浮点数显示格式，保留6位小数
        csvFile << methodError.meanErr << ",";
        csvFile << methodError.medErr << ",";
        csvFile << methodError.meanZErr << ",";
    }
    csvFile.seekp(-1, ios::cur);  // 回退一个字符位置以移除最后一个逗号
    csvFile << endl;

    csvFile.close();
}

void writeErrorTable2(const MethodErrors& methodErrs, const string& filePath) {
    ofstream csvFile(filePath, ios::out);  // 使用输出模式，每次调用都重写文件

    // 首先构造每种错误指标的表头
    string meanErrHeader, medErrHeader, meanZErrHeader;
    for (const auto& methodError : methodErrs) {
        meanErrHeader += methodError.method + "_meanErr,";
        medErrHeader += methodError.method + "_medErr,";
        meanZErrHeader += methodError.method + "_meanZErr,";
    }
    // 移除最后一个逗号并合并表头
    if (!meanErrHeader.empty()) meanErrHeader.pop_back();
    if (!medErrHeader.empty()) medErrHeader.pop_back();
    if (!meanZErrHeader.empty()) meanZErrHeader.pop_back();

    string header = meanErrHeader + "," + medErrHeader + "," + meanZErrHeader;
    csvFile << header << endl;

    // 在一行内按顺序写入所有数据
    csvFile << fixed << setprecision(10);  // 设置固定的浮点数显示格式，保留6位小数

    // 写入meanErr数据
    for (const auto& methodError : methodErrs) {
        csvFile << methodError.meanErr << ",";
    }
    // 写入medErr数据
    for (const auto& methodError : methodErrs) {
        csvFile << methodError.medErr << ",";
    }
    // 写入meanZErr数据
    for (const auto& methodError : methodErrs) {
        if (&methodError != &methodErrs.back()) {  // 如果不是最后一个元素
            csvFile << methodError.meanZErr << ",";
        } else {
            csvFile << methodError.meanZErr;  // 对于最后一个元素，不添加逗号
        }
    }

    csvFile << endl;
    csvFile.close();
}


void parseKMat(const string& kStr, Matrix3d& K) {
    istringstream iss(kStr.substr(1, kStr.size() - 2)); // Remove '[' and ']'
    vector<double> kValues;
    double value;
    while (iss >> value) {  // 使用空格分隔
        kValues.push_back(value);
    }
    if (kValues.size() == 9) {
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                K(i, j) = kValues[i + 3 * j];
            }
        }
    }
}
void load_matches(const string& filePath, MatchesInfo& matches_info, IntrinsicMats& Kmats) {
    ifstream file(filePath);
    if (!file.is_open()) {
        cerr << "无法打开文件: " << filePath << endl;
        return;
    }

    int numPairs;
    file >> numPairs;
    file.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略直到下一行的剩余部分

    string line;
    for (int i = 0; i < numPairs; ++i) {
        getline(file, line); // 读取包含匹配对ID的行

        // 提取匹配对ID
        size_t start = line.find('(') + 1;
        size_t end = line.find(')');
        string pairStr = line.substr(start, end - start);
        istringstream iss(pairStr);
        int a, b;
        char comma;
        iss >> a >> comma >> b;

        // 处理K矩阵
        start = line.find("K_left=");
        end = line.find(", K_right=");
        if (start != string::npos && end != string::npos) {
            string kLeftStr = line.substr(start + 7, end - (start + 7));
            string kRightStr = line.substr(end + 10, line.find(']', end) - (end + 10) + 1);

            // 仅当Kmats中尚不存在对应的键值时，添加新的内参数矩阵
            if (Kmats.find(a) == Kmats.end()) {
                Matrix3d K_left;
                parseKMat(kLeftStr, K_left);
                Kmats[a] = K_left;
//                cout<<"K_left="<<K_left<<endl;
            }
            if (Kmats.find(b) == Kmats.end()) {
                Matrix3d K_right;
                parseKMat(kRightStr, K_right);
                Kmats[b] = K_right;
            }

        }

        vector<Vector3d> tempPoints1, tempPoints2;
        // 读取匹配点，直到遇到带#的行
        streampos oldPos;
        while (true) {
            oldPos = file.tellg();  // 记住当前位置
            if (!getline(file, line)) {  // 如果读取失败（例如到达文件末尾）
                break;  // 跳出循环
            }

            if (!line.empty() && line[0] == '#') {
                file.seekg(oldPos);  // 回退到带#的行的开始
                break;  // 停止读取当前匹配对的匹配点
            }
            if (line.empty()) continue;  // 忽略空行

            istringstream iss(line);
            double x1, y1, x2, y2;
            iss >> x1 >> y1 >> x2 >> y2;

            Vector3d p1(x1, y1, 1.0);  // 假设z坐标为1
            Vector3d p2(x2, y2, 1.0);  // 假设z坐标为1
//            cout<<p1.transpose()<<"  "<<p2.transpose()<<endl;
            p1 = Kmats[a].inverse()*p1;
            p2 = Kmats[b].inverse()*p2;

            p1.normalize();  // 归一化点
            p2.normalize();  // 归一化点

            tempPoints1.push_back(p1);
            tempPoints2.push_back(p2);
        }

        BearingVector points1(3, tempPoints1.size());
        BearingVector points2(3, tempPoints2.size());
        for (size_t j = 0; j < tempPoints1.size(); ++j) {
            points1.col(j) = tempPoints1[j];
            points2.col(j) = tempPoints2[j];
        }

        matches_info[make_pair(a, b)] = MatchPair(points1, points2);
    }

    file.close();
}


void load_relative_poses(const std::string& filePath, RelativePoses& relative_poses) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filePath << std::endl;
        return;
    }

    std::string line;
    while (std::getline(file, line)) {
        if (line.empty()) continue;  // 跳过空行

        std::istringstream iss(line);
        ViewId leftViewId, rightViewId;
        Matrix3d rotations;
        Vector3d translations;
        bool readError = false;

        iss >> leftViewId >> rightViewId;
        if (iss.fail()) continue;  // 如果读取视图ID失败，跳过此行

        // 读取旋转矩阵
        for (int i = 0; i < 3 && !readError; ++i) {
            for (int j = 0; j < 3; ++j) {
                iss >> rotations(j, i);
                if (iss.fail()) {
                    readError = true;
                    break;
                }
            }
        }

        // 读取平移向量
        for (int i = 0; i < 3 && !readError; ++i) {
            iss >> translations(i);
            if (iss.fail()) {
                readError = true;
                break;
            }
        }

        if (!readError) {
            // 使用MatchInd作为键存储姿态
            relative_poses[{leftViewId, rightViewId}] = Pose(rotations, translations);
        }
        // 如果遇到读取错误，此行将被跳过，并继续读取下一行
    }
}


VectorXd computeResidual(RelativeResidualTypes method,
                         const BearingVector& v1,
                         const BearingVector& v2,
                         const Pose& pose,
                         const VectorXd* ptr_weights = nullptr,
                         const Matrix<double, 3, Dynamic>* points3D = nullptr) {
    switch (method) {
        case RESIDUAL_BA:
            return residual_BA(v1, v2, pose, ptr_weights);
        case RESIDUAL_COPLANAR:
            return residual_coplanar(v1, v2, pose, ptr_weights);
        case RESIDUAL_KNEIP:
            return residual_Kneip(v1, v2, pose, ptr_weights);
        case RESIDUAL_OPENGV:
            return residual_opengv(v1, v2, pose, ptr_weights);
        case RESIDUAL_PPO:
            return residual_PPO(v1, v2, pose, ptr_weights);
        case RESIDUAL_PPO_INVD:
            return residual_PPO_invd(v1, v2, pose, ptr_weights);
        case RESIDUAL_PPO_BVA_INVD:
            return residual_PPO_bva_invd(v1, v2, pose, ptr_weights);
        case RESIDUAL_LIGT_DIRECT:
            return residual_LiGT_direct(v1, v2, pose, ptr_weights);
        case RESIDUAL_LIGT_D3:
            return residual_LiGT_d3(v1, v2, pose, ptr_weights);
        case RESIDUAL_LIGT:
            return residual_LiGT(v1, v2, pose, ptr_weights);
        case RESIDUAL_LIRT:
            return residual_LiRT(v1, v2, pose, ptr_weights);
        default:
            throw std::invalid_argument("Unknown residual method.");
    }
}

MethodErrors computeAndStatResiduals(const MatchesInfo& matches_info, const RelativePoses& relative_poses, const string& method_name) {
    vector<double> medErrors, meanErrors, zScores;

    for (const auto& match : matches_info) {
        const MatchInd& match_ind = match.first;
        const MatchPair& match_pair = match.second;

        auto it = relative_poses.find(match_ind);
        if (it == relative_poses.end()) continue; // 如果没有找到相对姿态，跳过这对匹配

        const Pose& pose = it->second;

        RelativeResidualTypes method = stringToEnum(method_name);
        VectorXd residuals = computeResidual(method, match_pair.points2, match_pair.points1, pose, nullptr);

        double medErr = findMedian(residuals);
        double meanErr = residuals.mean();
        medErrors.push_back(medErr);
        meanErrors.push_back(meanErr);

//        cout<<"(i,j)="<<"("<<match_ind.first<<","<<match_ind.second<<"), meanErr = "<<meanErr<<", medErr = "<<medErr<<endl;
        VectorXd mad = (residuals.array() - medErr).abs();
        double madErr = findMedian(mad);
        VectorXd zScore = (residuals.array() - medErr) / madErr;
        zScores.push_back(zScore.mean());
    }

    double finalMedErr = findMedian(medErrors);

    // 将std::vector映射为Eigen::VectorXd
    Eigen::Map<Eigen::VectorXd> meanErrorsMap(meanErrors.data(), meanErrors.size());
    Eigen::Map<Eigen::VectorXd> zScoresMap(zScores.data(), zScores.size());

    // 计算平均值
    double finalMeanErr = meanErrorsMap.mean();
    double finalMeanZScore = zScoresMap.mean();

    // 处理 method_name，去掉 "residual_" 前缀
    string processed_method_name = method_name.substr(method_name.find("_") + 1);

    MethodErrors methodErrors;
    methodErrors.emplace_back(processed_method_name, finalMedErr, finalMeanErr, finalMeanZScore);

    return methodErrors;
}




int main(int argc, char *argv[]) {

    google::ParseCommandLineFlags(&argc, &argv, true);

    RelativeResidualTypes method = stringToEnum(FLAGS_solve_mode.c_str());

    if (FLAGS_debug_info){
        LOG(INFO) << "TwoViewSolveMethod = " << FLAGS_solve_mode.c_str();
    }

    double time_algorithm;

    BearingVector v1,v2;
    VectorXd weights;
    RelativePoses relative_poses;

    IntrinsicMats Kmats;

    MatchesInfo matches_info;

    load_relative_poses(FLAGS_relative_pose_file.c_str(),relative_poses);
//    cout<<"FLAGS_bearing_vector_file="<<FLAGS_bearing_vector_file.c_str()<<endl;
    load_matches(FLAGS_bearing_vector_file.c_str(),matches_info,Kmats);

    VectorXd residuals;
    string method_str = FLAGS_solve_mode.c_str();
    MethodErrors method_errors;
    if (method_str == "all") {
        // 定义需要计算的所有方法
        vector<string> methods = {"residual_ba", "residual_coplanar", "residual_opengv", "residual_ppo", "residual_lirt"};

        // 遍历并计算每种方法的残差
        for (const auto& method_name : methods) {
//            cout<<"========================   "<<method_name<<"  ========================"<<endl;
            // 调用 computeAndStatResiduals 函数计算当前方法的残差并累积结果
            MethodErrors current_errors = computeAndStatResiduals(matches_info, relative_poses, method_name);
            method_errors.insert(method_errors.end(), current_errors.begin(), current_errors.end());
        }
    } else {
        // 直接使用指定的方法计算残差
        method_errors = computeAndStatResiduals(matches_info, relative_poses, method_str);
    }
//    // Write to CSV
    writeErrorTable2(method_errors,FLAGS_output_file);


    if (FLAGS_debug_info){

        google::ShutdownGoogleLogging();
    }
    google::ShutDownCommandLineFlags();

    return 0;
}

