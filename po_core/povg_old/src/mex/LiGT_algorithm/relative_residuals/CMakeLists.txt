INCLUDE_DIRECTORIES(
    ${LIGT_DIRS}
    ../six_point_algorithm
    .
)

find_package(Glog REQUIRED)

# 启用调试符号和 gprof 支持
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pg -g")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -pg -g")

# 查找 povg_ligt_algorithm 库的所有源文件
#file(GLOB_RECURSE LIGT_ALGORITHM_SRC /home/<USER>/ap_build/mex_cmake/src/mex/LiGT_algorithm/six_point_algorithm/*.cpp)


# 添加 povg_relative_residuals 可执行文件
add_executable(povg_relative_residuals /home/<USER>/ap_build/mex_cmake/src/mex/LiGT_algorithm/relative_residuals/relative_residuals_main.cpp)
target_link_libraries(povg_relative_residuals
    povg_ligt_algorithm
    ${GFLAGS_LIBRARIES}
    ${GLOG_LIBRARY}
)

# 添加 povg_relative_identification 可执行文件
add_executable(povg_relative_identification /home/<USER>/ap_build/mex_cmake/src/mex/LiGT_algorithm/relative_residuals/relative_identification_main.cpp)
target_link_libraries(povg_relative_identification
    povg_ligt_algorithm
    ${GFLAGS_LIBRARIES}
    ${GLOG_LIBRARY}
)
