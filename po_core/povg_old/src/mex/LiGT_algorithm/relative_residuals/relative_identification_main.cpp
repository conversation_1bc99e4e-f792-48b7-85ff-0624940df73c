// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <fstream>
#include <iomanip>
#include <cstdio>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <glog/logging.h>

#include "povg_timer.hpp"
#include "relative_residuals.hpp"

#include <Eigen/Dense>
#include <unsupported/Eigen/Polynomials>
#include <complex>

#include <algorithm>
#include <numeric>
#include <vector>
#include <cmath>

#include <limits>



using namespace std;
using namespace Eigen;


enum RelativeResidualTypes {
    RESIDUAL_BA,
    RESIDUAL_COPLANAR,
    RESIDUAL_KNEIP,
    RESIDUAL_OPENGV,
    RESIDUAL_OPENGV_WITH_POINTS3D, // Since there's a function overload, differentiate by context
    RESIDUAL_PPO,
    RESIDUAL_PPO_INVD,
    RESIDUAL_PPO_BVA_INVD,
    RESIDUAL_LIGT_DIRECT,
    RESIDUAL_LIGT_D3,
    RESIDUAL_LIGT_LMAT,
    RESIDUAL_LIGT,
    RESIDUAL_LIRT,
    RESIDUAL_ALL
};



DEFINE_string(mode,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/matches/Matches_10Image_1000Points_10.00Dt_0.txt",
              "file of matching point pairs to use.");

DEFINE_string(relative_pose_file,
              "/home/<USER>/relative_pose_file.txt",
              "file of 3 smallest eigenvectors for A matrix");

DEFINE_string(bearing_vector_file,
              "/home/<USER>/bearing_vector_file.txt",
              "file of bearing vectors");

DEFINE_string(weights_file,
              "/home/<USER>/weights_file.txt",
              "file of bearing vectors");

DEFINE_string(output_file,
              "/home/<USER>/Error.csv",
              "Type of SfM reconstruction estimation to use.");


DEFINE_string(time_file,
              "time_output_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(solve_mode,
              "eigen_solve",
              "0: polynomial eigenvalue solve method");

DEFINE_bool(debug_info,
            false,
            "0: polynomial eigenvalue solve method");
DEFINE_bool(computeMode,
            true,
            " ");
DEFINE_string(identify_mode,
              "PPO",
              "opengv_c;PPO;PPOG");

DEFINE_string(Evec_file,
              "/home/<USER>/EvecFile.txt",
              "file of 3 smallest eigenvectors for A matrix");
DEFINE_bool(med_or_sum,
            true,
            "false: med");

using namespace std;

using namespace  POVG;

#define PI 3.141592653589793
// 残差估计函数指针
typedef VectorXd (*Eval_Residual_func)(const BearingVector& v1,
                                       const BearingVector& v2,
                                       const Pose& pose,
                                       const VectorXd* ptr_weigths);
RelativeResidualTypes stringToEnum(const std::string& method) {
    static const std::map<std::string, RelativeResidualTypes> stringToEnumMap = {
        {"residual_ba", RESIDUAL_BA},
        {"residual_coplanar", RESIDUAL_COPLANAR},
        {"residual_kneip", RESIDUAL_KNEIP},
        {"residual_opengv", RESIDUAL_OPENGV},
        {"residual_opengv_with_points3d", RESIDUAL_OPENGV_WITH_POINTS3D},
        {"residual_ppo", RESIDUAL_PPO},
        {"residual_ppo_invd", RESIDUAL_PPO_INVD},
        {"residual_ppo_bva_invd", RESIDUAL_PPO_BVA_INVD},
        {"residual_ligt_direct", RESIDUAL_LIGT_DIRECT},
        {"residual_ligt_d3", RESIDUAL_LIGT_D3},
        {"residual_ligt_lmat", RESIDUAL_LIGT_LMAT},
        {"residual_ligt", RESIDUAL_LIGT},
        {"residual_lirt", RESIDUAL_LIRT},
        {"all", RESIDUAL_ALL}
    };

    auto it = stringToEnumMap.find(method);
    if (it != stringToEnumMap.end()) {
        return it->second;
    } else {
        // Handle the error case, for example:
        throw std::runtime_error("Invalid method string provided.");
    }
}

double findMedian(VectorXd a);
double findMedian(vector<double> a);


void load_weights_file(const std::string& weights_file,
                       VectorXd& weights) {


    POVG::Timer timer;

    // load weights file
    std::fstream sfile(weights_file, ios::in);
    if (!sfile.is_open()) {
        std::cerr << "weights file cannot load";
        return;
    }

    int num_matches;
    sfile >> num_matches;

    weights.setZero(num_matches,1);

    for (unsigned int i = 0; i < num_matches; ++i) {
        sfile >> weights(i);
    }

    sfile.close();

    // print time cost information
    //    cout << ">> time for loading eigen sols file: "
    //         << timer.Duration()
    //         << "s"
    //         << endl;
}



void load_bearing_info(const std::string& bearing_file,
                       BearingVector& v1,
                       BearingVector& v2) {

    POVG::Timer timer;

    std::fstream sfile(bearing_file, ios::in);
    if (!sfile.is_open()) {
        std::cerr << "bearing file cannot load";
        return;
    }

    int num_matches;
    sfile >> num_matches;

    v1.setZero(3,num_matches);
    v2.setZero(3,num_matches);

    for (unsigned int i = 0; i < num_matches; ++i) {
        sfile >> v1(0,i) >> v1(1,i) >> v1(2,i)
                         >> v2(0,i) >> v2(1,i) >> v2(2,i);
    }

    sfile.close();

}
auto findRoots(const Eigen::VectorXd& coefficients) {

    Eigen::VectorXd p = coefficients;
    p = p/p(0);		//转换为最高次系数为1的标准形式

    MatrixXd matrixXd(p.size()-1,p.size()-1);
    for (int i = 0; i < p.size()-1; ++i) {
        matrixXd(i,0) = -p(i+1);
        for (int j = 1; j < p.size()-1; ++j) {
            if(i+1 == j )
                matrixXd(i,j) = 1;
            else
                matrixXd (i,j) = 0;
        }
    }

    auto evalue = matrixXd.eigenvalues();

    return evalue;
}


double findMedian(VectorXd a) {
    int n = a.size();

    // 创建一个临时向量并复制数据
    VectorXd temp = a;

    // 排序临时向量
    std::sort(temp.data(), temp.data() + temp.size());

    // 如果数组大小是偶数
    if (n % 2 == 0) {
        return (temp[(n - 1) / 2] + temp[n / 2]) / 2.0;
    }
    // 如果数组大小是奇数
    else {
        return temp[n / 2];
    }
}

double findMedian(vector<double> a)
{

    int n = a.size();
    // If size of the arr[] is even
    if (n % 2 == 0) {

        // Applying nth_element
        // on n/2th index
        nth_element(a.begin(),
                    a.begin() + n / 2,
                    a.end());

        // Applying nth_element
        // on (n-1)/2 th index
        nth_element(a.begin(),
                    a.begin() + (n - 1) / 2,
                    a.end());

        // Find the average of value at
        // index N/2 and (N-1)/2
        return (double)(a[(n - 1) / 2]
                + a[n / 2])
                / 2.0;
    }

    // If size of the arr[] is odd
    else {

        // Applying nth_element
        // on n/2
        nth_element(a.begin(),
                    a.begin() + n / 2,
                    a.end());

        // Value at index (N/2)th
        // is the median
        return (double)a[n / 2];
    }
}

void load_pose(const std::string& pose_file, Pose& pose) {

    POVG::Timer timer;

    std::ifstream file(pose_file);
    if (!file.is_open()) {
        std::cerr << "Pose file cannot be opened." << std::endl;
        return;
    }

    for (int i = 0; i < 3; ++i) {
        for (int j = 0; j < 3; ++j) {
            file >> pose.rotations(j,i);  // 注意这里使用了转置
        }
    }

    for (int i = 0; i < 3; ++i) {
        file >> pose.translations(i);
    }

    cout << ">> time for loading pose file: "
         << timer.Duration()
         << "s"
         << endl;
}
void writeErrorTable(const MethodErrors& methodErrs, const string& filePath) {
    ofstream csvFile(filePath, ios::out);  // 使用输出模式，每次调用都重写文件

    // 首先构造表头
    string header;
    for (const auto& methodError : methodErrs) {
        // 为每个错误指标添加方法名作为前缀
        header += methodError.method + "_meanErr,";
        header += methodError.method + "_medErr,";
        header += methodError.method + "_meanZErr,";
    }
    if (!header.empty()) {
        header.pop_back();  // 移除最后一个逗号
    }
    csvFile << header << endl;

    // 然后在一行内写入所有数据
    for (const auto& methodError : methodErrs) {
        // 写入数据：method_meanErr, method_medErr, method_meanZErr
        csvFile << fixed << setprecision(10);  // 设置固定的浮点数显示格式，保留6位小数
        csvFile << methodError.meanErr << ",";
        csvFile << methodError.medErr << ",";
        csvFile << methodError.meanZErr << ",";
    }
    csvFile.seekp(-1, ios::cur);  // 回退一个字符位置以移除最后一个逗号
    csvFile << endl;

    csvFile.close();
}

void writeErrorTable2(const MethodErrors& methodErrs, const string& filePath) {
    ofstream csvFile(filePath, ios::out);  // 使用输出模式，每次调用都重写文件

    // 首先构造每种错误指标的表头
    string meanErrHeader, medErrHeader, meanZErrHeader;
    for (const auto& methodError : methodErrs) {
        meanErrHeader += methodError.method + "_meanErr,";
        medErrHeader += methodError.method + "_medErr,";
        meanZErrHeader += methodError.method + "_meanZErr,";
    }
    // 移除最后一个逗号并合并表头
    if (!meanErrHeader.empty()) meanErrHeader.pop_back();
    if (!medErrHeader.empty()) medErrHeader.pop_back();
    if (!meanZErrHeader.empty()) meanZErrHeader.pop_back();

    string header = meanErrHeader + "," + medErrHeader + "," + meanZErrHeader;
    csvFile << header << endl;

    // 在一行内按顺序写入所有数据
    csvFile << fixed << setprecision(10);  // 设置固定的浮点数显示格式，保留6位小数

    // 写入meanErr数据
    for (const auto& methodError : methodErrs) {
        csvFile << methodError.meanErr << ",";
    }
    // 写入medErr数据
    for (const auto& methodError : methodErrs) {
        csvFile << methodError.medErr << ",";
    }
    // 写入meanZErr数据
    for (const auto& methodError : methodErrs) {
        if (&methodError != &methodErrs.back()) {  // 如果不是最后一个元素
            csvFile << methodError.meanZErr << ",";
        } else {
            csvFile << methodError.meanZErr;  // 对于最后一个元素，不添加逗号
        }
    }

    csvFile << endl;
    csvFile.close();
}


void parseKMat(const string& kStr, Matrix3d& K) {
    istringstream iss(kStr.substr(1, kStr.size() - 2)); // Remove '[' and ']'
    vector<double> kValues;
    double value;
    while (iss >> value) {  // 使用空格分隔
        kValues.push_back(value);
    }
    if (kValues.size() == 9) {
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                K(i, j) = kValues[i + 3 * j];
            }
        }
    }
}
void load_matches(const string& filePath, MatchesInfo& matches_info, IntrinsicMats& Kmats) {
    ifstream file(filePath);
    if (!file.is_open()) {
        cerr << "无法打开文件: " << filePath << endl;
        return;
    }

    int numPairs;
    file >> numPairs;
    file.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略直到下一行的剩余部分

    string line;
    for (int i = 0; i < numPairs; ++i) {
        getline(file, line); // 读取包含匹配对ID的行

        // 提取匹配对ID
        size_t start = line.find('(') + 1;
        size_t end = line.find(')');
        string pairStr = line.substr(start, end - start);
        istringstream iss(pairStr);
        int a, b;
        char comma;
        iss >> a >> comma >> b;

        // 处理K矩阵
        start = line.find("K_left=");
        end = line.find(", K_right=");
        if (start != string::npos && end != string::npos) {
            string kLeftStr = line.substr(start + 7, end - (start + 7));
            string kRightStr = line.substr(end + 10, line.find(']', end) - (end + 10) + 1);

            // 仅当Kmats中尚不存在对应的键值时，添加新的内参数矩阵
            if (Kmats.find(a) == Kmats.end()) {
                Matrix3d K_left;
                parseKMat(kLeftStr, K_left);
                Kmats[a] = K_left;
                //                cout<<"K_left="<<K_left<<endl;
            }
            if (Kmats.find(b) == Kmats.end()) {
                Matrix3d K_right;
                parseKMat(kRightStr, K_right);
                Kmats[b] = K_right;
            }

        }

        vector<Vector3d> tempPoints1, tempPoints2;
        // 读取匹配点，直到遇到带#的行
        streampos oldPos;
        while (true) {
            oldPos = file.tellg();  // 记住当前位置
            if (!getline(file, line)) {  // 如果读取失败（例如到达文件末尾）
                break;  // 跳出循环
            }

            if (!line.empty() && line[0] == '#') {
                file.seekg(oldPos);  // 回退到带#的行的开始
                break;  // 停止读取当前匹配对的匹配点
            }
            if (line.empty()) continue;  // 忽略空行

            istringstream iss(line);
            double x1, y1, x2, y2;
            iss >> x1 >> y1 >> x2 >> y2;

            Vector3d p1(x1, y1, 1.0);  // 假设z坐标为1
            Vector3d p2(x2, y2, 1.0);  // 假设z坐标为1
            //            cout<<p1.transpose()<<"  "<<p2.transpose()<<endl;
            p1 = Kmats[a].inverse()*p1;
            p2 = Kmats[b].inverse()*p2;

            p1.normalize();  // 归一化点
            p2.normalize();  // 归一化点

            tempPoints1.push_back(p1);
            tempPoints2.push_back(p2);
        }

        BearingVector points1(3, tempPoints1.size());
        BearingVector points2(3, tempPoints2.size());
        for (size_t j = 0; j < tempPoints1.size(); ++j) {
            points1.col(j) = tempPoints1[j];
            points2.col(j) = tempPoints2[j];
        }

        matches_info[make_pair(a, b)] = MatchPair(points1, points2);
    }

    file.close();
}


void load_relative_poses(const std::string& filePath, RelativePoses& relative_poses) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "无法打开文件: " << filePath << std::endl;
        return;
    }

    std::string line;
    while (std::getline(file, line)) {
        if (line.empty()) continue;  // 跳过空行

        std::istringstream iss(line);
        ViewId leftViewId, rightViewId;
        Matrix3d rotations;
        Vector3d translations;
        bool readError = false;

        iss >> leftViewId >> rightViewId;
        if (iss.fail()) continue;  // 如果读取视图ID失败，跳过此行

        // 读取旋转矩阵
        for (int i = 0; i < 3 && !readError; ++i) {
            for (int j = 0; j < 3; ++j) {
                iss >> rotations(j, i);
                if (iss.fail()) {
                    readError = true;
                    break;
                }
            }
        }

        // 读取平移向量
        for (int i = 0; i < 3 && !readError; ++i) {
            iss >> translations(i);
            if (iss.fail()) {
                readError = true;
                break;
            }
        }

        if (!readError) {
            // 使用MatchInd作为键存储姿态
            relative_poses[{leftViewId, rightViewId}] = Pose(rotations, translations);
        }
        // 如果遇到读取错误，此行将被跳过，并继续读取下一行
    }
}


VectorXd computeResidual(RelativeResidualTypes method,
                         const BearingVector& v1,
                         const BearingVector& v2,
                         const Pose& pose,
                         const VectorXd* ptr_weights = nullptr,
                         const Matrix<double, 3, Dynamic>* points3D = nullptr) {
    switch (method) {
    case RESIDUAL_BA:
        return residual_BA(v1, v2, pose, ptr_weights);
    case RESIDUAL_COPLANAR:
        return residual_coplanar(v1, v2, pose, ptr_weights);
    case RESIDUAL_KNEIP:
        return residual_Kneip(v1, v2, pose, ptr_weights);
    case RESIDUAL_OPENGV:
        return residual_opengv(v1, v2, pose, ptr_weights);
    case RESIDUAL_PPO:
        return residual_PPO(v1, v2, pose, ptr_weights);
    case RESIDUAL_PPO_INVD:
        return residual_PPO_invd(v1, v2, pose, ptr_weights);
    case RESIDUAL_PPO_BVA_INVD:
        return residual_PPO_bva_invd(v1, v2, pose, ptr_weights);
    case RESIDUAL_LIGT_DIRECT:
        return residual_LiGT_direct(v1, v2, pose, ptr_weights);
    case RESIDUAL_LIGT_D3:
        return residual_LiGT_d3(v1, v2, pose, ptr_weights);
    case RESIDUAL_LIGT:
        return residual_LiGT(v1, v2, pose, ptr_weights);
    case RESIDUAL_LIRT:
        return residual_LiRT(v1, v2, pose, ptr_weights);
    default:
        throw std::invalid_argument("Unknown residual method.");
    }
}

MethodErrors computeAndStatResiduals(const MatchesInfo& matches_info, const RelativePoses& relative_poses, const string& method_name) {
    vector<double> medErrors, meanErrors, zScores;

    for (const auto& match : matches_info) {
        const MatchInd& match_ind = match.first;
        const MatchPair& match_pair = match.second;

        auto it = relative_poses.find(match_ind);
        if (it == relative_poses.end()) continue; // 如果没有找到相对姿态，跳过这对匹配

        const Pose& pose = it->second;

        RelativeResidualTypes method = stringToEnum(method_name);
        VectorXd residuals = computeResidual(method, match_pair.points2, match_pair.points1, pose, nullptr);

        double medErr = findMedian(residuals);
        double meanErr = residuals.mean();
        medErrors.push_back(medErr);
        meanErrors.push_back(meanErr);

        //        cout<<"(i,j)="<<"("<<match_ind.first<<","<<match_ind.second<<"), meanErr = "<<meanErr<<", medErr = "<<medErr<<endl;
        VectorXd mad = (residuals.array() - medErr).abs();
        double madErr = findMedian(mad);
        VectorXd zScore = (residuals.array() - medErr) / madErr;
        zScores.push_back(zScore.mean());
    }

    double finalMedErr = findMedian(medErrors);

    // 将std::vector映射为Eigen::VectorXd
    Eigen::Map<Eigen::VectorXd> meanErrorsMap(meanErrors.data(), meanErrors.size());
    Eigen::Map<Eigen::VectorXd> zScoresMap(zScores.data(), zScores.size());

    // 计算平均值
    double finalMeanErr = meanErrorsMap.mean();
    double finalMeanZScore = zScoresMap.mean();

    // 处理 method_name，去掉 "residual_" 前缀
    string processed_method_name = method_name.substr(method_name.find("_") + 1);

    MethodErrors methodErrors;
    methodErrors.emplace_back(processed_method_name, finalMedErr, finalMeanErr, finalMeanZScore);

    return methodErrors;
}


void essential2RT(const Matrix3d& E,
                  vector<Matrix3d>& R,
                  vector<Vector3d>& t) {
    Matrix3d normalizedE = E / E.norm();

    Matrix3d W1;
    W1 << 0, -1, 0,
            1, 0, 0,
            0, 0, 1;

    Matrix3d W2;
    W2 << 0, -1, 0,
            1, 0, 0,
            0, 0, -1;

    JacobiSVD<Matrix3d> svd(normalizedE, ComputeFullU | ComputeFullV);
    Matrix3d U = svd.matrixU();
    Matrix3d V = svd.matrixV();

    R.resize(2);
    if (U.determinant() * V.determinant() > 0) {
        R[0] = U * W1 * V.transpose();
        R[1] = U * W1.transpose() * V.transpose();
    } else {
        R[0] = U * W2 * V.transpose();
        R[1] = U * W2.transpose() * V.transpose();
    }

    t.resize(2);
    t[0] = U.col(2);
    t[1] = -U.col(2);

}


std::vector<int> findTwoSmallestIndices(const Eigen::VectorXd& vec) {
    int index1 = -1, index2 = -1;
    double smallest = std::numeric_limits<double>::max();
    double secondSmallest = std::numeric_limits<double>::max();

    // Find the index of the smallest and second smallest value
    for (int i = 0; i < vec.size(); ++i) {
        if (vec[i] < smallest) {
            secondSmallest = smallest;
            index2 = index1;

            smallest = vec[i];
            index1 = i;
        } else if (vec[i] < secondSmallest) {
            secondSmallest = vec[i];
            index2 = i;
        }
    }

    std::vector<int> indices = {index1, index2};
    return indices;
}

Eigen::Matrix3d crossMatrix(const Eigen::Vector3d& x) {
    Eigen::Matrix3d y;

    y <<  0,   -x(2),  x(1),
            x(2),   0,  -x(0),
            -x(1),  x(0),   0;

    return y;
}

//更新了Rt_check中的ratio部分
Pose RT_Check2(const MatrixXd& A,
               const BearingVector& v1,
               const BearingVector& v2,
               const vector<Matrix3d>& R_sols,
               const vector<Vector3d>& t_sols,
               const VectorXd* ptr_weigths,
               double& ratio) {
    int n = v1.cols();

    // use normalized coordinates [not bearing vec]
    Matrix<double,3,Dynamic> x, xmatch;
    x.setZero(3,v2.cols());
    xmatch.setZero(3,v1.cols());

    for (int i = 0; i < n; ++i){
        x.col(i) = v2.col(i)/v2(2,i);
        xmatch.col(i) = v1.col(i)/v1(2,i);
    }

    // formulate H matrix
    MatrixXd H(n, 6);
    for (int i = 0; i < n; ++i) {
        H.row(i) << xmatch.col(i).norm()*x.col(i).transpose(),x.col(i).norm()*xmatch.col(i).transpose();
        if (ptr_weigths) H.row(i) *= (*ptr_weigths)[i];
    }
    //    cout<<"Hmat:\n"<<H<<endl;

    vector<int> SR(R_sols.size(), 0); // the number of pair points satisfied with M1(R)
    vector<int> ST(t_sols.size(), 0); // the number of pair points satisfied with M2(t)

    // M1(R) constraint
    for (size_t i = 0; i < 2; ++i) {
        Matrix3d tx;
        Vector3d t = t_sols[i];

        tx << 0, -t(2), t(1),
                t(2), 0, -t(0),
                -t(1), t(0), 0;

        Matrix3d Q = tx.transpose() * tx * R_sols[i];

        SR[i] = (A * Map<VectorXd>(Q.data(), 9)).array().cast<double>().unaryExpr([](double val) { return val > 0 ? 1 : 0 ;}).sum();

    }

    int index_R = SR[0]>SR[1]?0:1;

    Pose right_pose;
    right_pose.rotations = R_sols[index_R];

    // M2(t)

    Vector2d tmp_ratio;
    for (int i = 0; i < 2; ++i) {
        Matrix<double,6,1> combinedVector; // 6x1 vector to store the combined result
        combinedVector.head(3) = -right_pose.rotations.transpose() * t_sols[i];
        combinedVector.tail(3) = Matrix3d::Identity() * t_sols[i];

        VectorXd result = H * combinedVector;
        //        ST[i] = (result.array() > 0).count();
        //        tmp_ratio[i] = result.sum();
        ST[i] = 0;
        for(int j = 0; j < n; ++j){
            Vector3d tmp_1 = crossMatrix(t_sols[i])*right_pose.rotations*x.col(j);
            Vector3d tmp_2 = crossMatrix(xmatch.col(j))*right_pose.rotations*x.col(j);
            Vector3d tmp_3 = crossMatrix(t_sols[i])*xmatch.col(j);
            double sside1_ratio = tmp_1.transpose()*tmp_2 > 0 ? 1 : 0;
            double sside2_ratio = tmp_2.transpose()*tmp_3 > 0 ? 1 : 0;
            ST[i] += sside1_ratio + sside2_ratio;
        }

    }

    ratio = max(ST[0],ST[1]);

    int index_t = ST[0] > ST[1] ? 0 : 1;
    right_pose.translations = t_sols[index_t];
    //    ratio = tmp_ratio[index_t];

    return right_pose;
}

double computeSolutionCosts(const Matrix<double, 9, Eigen::Dynamic>& Evec,
                            const MatrixXd& A,
                            const BearingVector& v1,
                            const BearingVector& v2,
                            const bool& evec_mode,
                            const VectorXd* ptr_weigths,
                            const Eval_Residual_func& residual_func,
                            Matrix<double, 3, 4>& out) {
    int numSolutions = Evec.cols();
    int num_matches = v1.cols();

    double selected_cost = 0;
    double selected_test_cost = 0;
    vector<double> cost(num_matches);
    vector<double> cost2(num_matches);

    VectorXd ratios(numSolutions);

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);

    VectorXd total_cost = MatrixXd::Zero(numSolutions,1);
    VectorXd test_cost = MatrixXd::Zero(numSolutions,1);

    vector<Pose> right_Rts;

    // load gt Rt
    Pose gt_pose;


    //start
    for (int i = 0; i < numSolutions; ++i) {
        Matrix3d E;
        for (int col = 0; col < 3; ++col) {
            for (int row = 0; row < 3; ++row) {
                E(row, col) = Evec(row + 3 * col, i);
            }
        }

        if (E.norm()<1e-10){
            ratios(i) = 0;
            total_cost[i] = 1e10;
            continue;
        }

        E /= E.norm();

        essential2RT(E, R_sol, t_sol);

        double ratio = 0;
        Pose right_pose = RT_Check2(A, v1, v2, R_sol, t_sol, ptr_weigths,ratio);
        ratios(i) = ratio;

        right_pose.translations /= right_pose.translations.norm();

        VectorXd tmp_cost2;

        auto tmp_cost = (residual_func)(v1, v2, right_pose,ptr_weigths);

        for (int k = 0; k < num_matches; ++k) {
            if (ptr_weigths)
                cost[k] = (*ptr_weigths)(k) * tmp_cost[k];
            else
                cost[k] = tmp_cost[k];
        }

        double med_cost = findMedian(cost);
        double sum_cost = tmp_cost.sum();


        if (FLAGS_med_or_sum){
            total_cost[i] = med_cost;
            test_cost[i] = sum_cost;
        }
        else{
            total_cost[i] = sum_cost;
            test_cost[i] = med_cost;

        }

        right_Rts.emplace_back(right_pose);
//        cout<<"[Sol "<<i<<"] cost = "<<total_cost[i]<<endl;
    }


    int selected_id;

    if (evec_mode){
        int min_id;
        total_cost.minCoeff(&min_id);
        selected_id = min_id;
    }
    else{

        vector<int> candidate_ids = findTwoSmallestIndices(total_cost);
        double max_value = -1e5;
        for(int j =0; j< candidate_ids.size(); ++j){
            int id = candidate_ids[j];
            if (ratios(id) > max_value){
                selected_id = id;
                max_value = ratios(id);
            }
        }
        //        selected_cost = ratios(selected_id);
    }
    selected_cost = total_cost(selected_id);
    selected_test_cost = test_cost(selected_id);

    out.block<3, 3>(0, 0) = right_Rts[selected_id].rotations;
    out.col(3) = right_Rts[selected_id].translations;

    return selected_cost;
}

double computeSolutionCosts2(const Matrix<double, 9, Eigen::Dynamic>& Evec,
                             const MatrixXd& A,
                             const BearingVector& v1,
                             const BearingVector& v2,
                             const bool& evec_mode,
                             const VectorXd* ptr_weigths,
                             const Eval_Residual_func& residual_func,
                             Matrix<double, 3, 4>& out) {

    int numSolutions = Evec.cols();
    int num_matches = v1.cols();

    double selected_cost = 0;
    double selected_test_cost = 0;
    vector<double> cost(num_matches);
    vector<double> cost2(num_matches);

    VectorXd ratios(numSolutions);

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);

    VectorXd total_cost = MatrixXd::Zero(numSolutions,1);
    VectorXd test_cost = MatrixXd::Zero(numSolutions,1);

    vector<Pose> right_Rts;


    //start
    right_Rts.resize(numSolutions);

    for (int i = 0; i < numSolutions; ++i) {
        Matrix3d E;
        for (int col = 0; col < 3; ++col) {
            for (int row = 0; row < 3; ++row) {
                E(row, col) = Evec(row + 3 * col, i);
            }
        }

        if (E.norm()<1e-10){
            ratios(i) = 0;
            total_cost[i] = 1e10;
            continue;
        }

        E /= E.norm();

        essential2RT(E, R_sol, t_sol);

        double ratio = 0;

        double min_Rt_costs = 1e10;

        for (Matrix3d& R_candidate :R_sol){
            for (Vector3d& t_candidate :t_sol){
                t_candidate = t_candidate/t_candidate.norm();
                Pose tmp_pose(R_candidate,t_candidate);

                auto tmp_cost = (residual_func)(v1, v2, tmp_pose,ptr_weigths);

                for (int k = 0; k < num_matches; ++k) {
                    if (ptr_weigths)
                        cost[k] = (*ptr_weigths)(k) * tmp_cost[k];
                    else
                        cost[k] = tmp_cost[k];
                }

                double med_cost = findMedian(cost);
                double sum_cost = tmp_cost.sum();

                double Rt_cost;
                if (FLAGS_med_or_sum){
                    Rt_cost = med_cost;
                }
                else{
                    Rt_cost = sum_cost;
                }

                if (Rt_cost < min_Rt_costs){
                    min_Rt_costs = Rt_cost;
                    right_Rts[i] = tmp_pose;
                }
            }
        }

        total_cost[i] = min_Rt_costs;
        cout<<"[Sol "<<i<<"] cost = "<<total_cost[i]<<endl;
    }

    int selected_id;
    total_cost.minCoeff(&selected_id);

    selected_cost = total_cost(selected_id);
    selected_test_cost = test_cost(selected_id);

    out.block<3, 3>(0, 0) = right_Rts[selected_id].rotations;
    out.col(3) = right_Rts[selected_id].translations;

    return selected_cost;
}


Matrix<double, 3, 4> processEvec2(const MatrixXd& Evec_sols,
                                  const BearingVector& v1,
                                  const BearingVector& v2,
                                  const MatrixXd& A,
                                  const VectorXd* ptr_weigths,
                                  const Eval_Residual_func& residual_func) {
    int num_matches = v1.cols();
    // Filter out columns in Evec with NaN
    vector<int> I;
    for (int i = 0; i < Evec_sols.cols(); ++i) {
        if (!isnan(Evec_sols.col(i).sum())) {  // Check if no element is NaN
            I.push_back(i);
        }
    }

    // Select valid columns only
    Matrix<double,9,Dynamic> Evec(9, I.size());
    for (size_t i = 0; i < I.size(); ++i) {
        Evec.col(i) = Evec_sols.col(I[i]);
    }

    int numSolutions = Evec.cols();
    if (numSolutions == 0) {
        Matrix<double, 3, 4> out = Matrix<double, 3, 4>::Constant(numeric_limits<double>::quiet_NaN());
        // Assuming 'residual' is a vector that should be returned or modified as well. Its declaration is needed outside this code.
        // residual.clear();
        return out;
    }

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);
    vector<double> cost;
    cost.resize(num_matches);

    VectorXd total_cost = MatrixXd::Zero(numSolutions,1);
    vector<Pose> right_Rts;

    VectorXd ratios(numSolutions);

    // solutions: 6, 6, 3, 3 = 18

    vector<Matrix<double, 3, 4>> outs;
    Vector4d selected_cost;

    Matrix<double, 3, 4> tmp_Rt;

    //    echo_on = 1;

    POVG::Timer check_time;
    //    cout<<"compute_mode = "<<compute_mode<<", use_med = "<<FLAGS_med_or_sum<<endl;

    if (FLAGS_computeMode){

        selected_cost(0) = computeSolutionCosts(Evec.middleCols<6>(0), A, v1, v2, false, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);


        selected_cost(1) = computeSolutionCosts(Evec.middleCols<6>(6), A, v1, v2, false, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);


        selected_cost(2) = computeSolutionCosts(Evec.middleCols<3>(12), A, v1, v2, true, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);


        selected_cost(3) = computeSolutionCosts(Evec.middleCols<3>(15), A, v1, v2, true, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);
    }
    else{

        selected_cost(0) = computeSolutionCosts2(Evec.middleCols<6>(0), A, v1, v2, true, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);


        selected_cost(1) = computeSolutionCosts2(Evec.middleCols<6>(6), A, v1, v2, true, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);


        selected_cost(2) = computeSolutionCosts2(Evec.middleCols<3>(12), A, v1, v2, true, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);

        selected_cost(3) = computeSolutionCosts2(Evec.middleCols<3>(15), A, v1, v2, true, ptr_weigths,residual_func,tmp_Rt);
        outs.emplace_back(tmp_Rt);
    }


    int min_id;
    selected_cost.minCoeff(&min_id);
    return outs[min_id];
}


typedef Matrix<double,9,Eigen::Dynamic> EVecs;
#include <sstream>

void load_Evecs_info(const std::string& evecs_file, EVecs& evecs) {
    // 计时器（假设 POVG::Timer 是一个已定义的计时器类）
    // POVG::Timer timer;

    // 打开文件
    std::fstream sfile(evecs_file, std::ios::in);
    if (!sfile.is_open()) {
        std::cerr << "EVecs file cannot load" << std::endl;
        return;
    }

    // 读取文件并存储到 vector 中
    std::vector<std::vector<double>> data;
    std::string line;
    while (std::getline(sfile, line)) {
        std::istringstream iss(line);
        std::vector<double> row;
        double value;
        while (iss >> value) {
            row.push_back(value);
        }
        if (row.size() == 9) {
            data.push_back(row);
        } else {
            std::cerr << "Invalid row format in EVecs file" << std::endl;
            sfile.close();
            return;
        }
    }

    sfile.close();

    // 检查文件内容是否符合预期
    size_t numCols = data.size();
    if (numCols == 0) {
        std::cerr << "EVecs file is empty or invalid" << std::endl;
        return;
    }

    // 初始化 EVecs 矩阵
    evecs.resize(9, numCols);

    // 填充 EVecs 矩阵
    for (size_t i = 0; i < numCols; ++i) {
        for (size_t j = 0; j < 9; ++j) {
            evecs(j, i) = data[i][j];
        }
    }

    // 打印时间成本信息
    // std::cout << ">> time for loading EVecs file: " << timer.Duration() << "s" << std::endl;
}
// Function to compute the Kronecker product of two matrices
Eigen::MatrixXd kroneckerProduct(const Eigen::MatrixXd &A, const Eigen::MatrixXd &B) {
    Eigen::MatrixXd Kronecker(A.rows()*B.rows(), A.cols()*B.cols());
    for(int i = 0; i < A.rows(); ++i) {
        for(int j = 0; j < A.cols(); ++j) {
            Kronecker.block(i*B.rows(), j*B.cols(), B.rows(), B.cols()) = A(i, j) * B;
        }
    }
    return Kronecker;
}


void write_pose(const std::string output_file,
                const Pose& pose_est){

    fstream output(output_file, std::ios::out | ios::trunc);
    if (!output.is_open()) {
        std::cerr << "output file cannot create, please check path";
        return;
    }


    output << setprecision(16) << pose_est.rotations.transpose() << endl;
    output << setprecision(16) << pose_est.translations.transpose();

    output.close();

}


int main(int argc, char *argv[]) {

    google::ParseCommandLineFlags(&argc, &argv, true);


    BearingVector v1,v2;
    VectorXd weights;
    VectorXd* ptr_weights;
    RelativePoses relative_poses;

    MatchesInfo matches_info;
    EVecs evecs;

    load_Evecs_info(FLAGS_Evec_file.c_str(),evecs);

    load_bearing_info(FLAGS_bearing_vector_file,v1,v2);
    load_weights_file(FLAGS_weights_file,weights);

    if (weights.rows()>0)
        ptr_weights = &weights;
    else
        ptr_weights = nullptr;

    int num_matches = v1.cols();

    Matrix<double,Dynamic,9> A(num_matches,9);

    for (int i = 0; i < num_matches; ++i) {
        Matrix<double,1,9> tmp_A;
        if ( ptr_weights != nullptr) {

            A.row(i) = (*ptr_weights)(i) * kroneckerProduct(v2.col(i).transpose(),v1.col(i).transpose());
        }
        else{
            A.row(i) = kroneckerProduct(v2.col(i).transpose(),v1.col(i).transpose());
        }

    }

    POVG::Timer timer;
    Matrix<double,3,4> Out;
    for(int i =0; i < 1000; i++)
    {

//    cout<<"==========  solve check ["<<FLAGS_identify_mode.c_str()<<"] ========="<<endl;
    if (strcmp(FLAGS_identify_mode.c_str(),"opengv_c")==0){
    cout<<"==========  solve check ["<<FLAGS_identify_mode.c_str()<<"] ========="<<endl;
         computeSolutionCosts(evecs,A,v1,v2,true,ptr_weights,&residual_opengv,Out);
    }
    else if (strcmp(FLAGS_identify_mode.c_str(),"PPObvcInvd")==0){

        computeSolutionCosts(evecs,A,v1,v2,true,ptr_weights,&residual_PPO_bvc_invd,Out);

    }
    else if (strcmp(FLAGS_identify_mode.c_str(),"PPO")==0){

        computeSolutionCosts(evecs,A,v1,v2,true,ptr_weights,&residual_PPO,Out);

    }
    else if (strcmp(FLAGS_identify_mode.c_str(),"PPOG")==0){

        computeSolutionCosts(evecs,A,v1,v2,true,ptr_weights,&residual_PPOG,Out);

    }
    else if (strcmp(FLAGS_identify_mode.c_str(),"sampson")==0){

        computeSolutionCosts(evecs,A,v1,v2,true,ptr_weights,&residual_sampson,Out);

    }
    }
    timer.Duration();

    Pose pose_est;
    pose_est.rotations = Out.block(0,0,3,3);
    pose_est.translations = Out.rightCols(1);

    timer.WriteTime(FLAGS_time_file,0);

    write_pose(FLAGS_output_file, pose_est);

    if (FLAGS_debug_info){

        google::ShutdownGoogleLogging();
    }
    google::ShutDownCommandLineFlags();

    return 0;
}

