// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>


#include "robust_LiGT.hpp"

DEFINE_string(tracks_file,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/matches/Matches_10Image_1000Points_10.00Dt_0.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(gR_file,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/global_R_1.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(output_file,
              "/home/<USER>/output_file.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(time_file,
              "time_output_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_bool(reprocess_mode,
            false,
            "Type of SfM reconstruction estimation to use.");

DEFINE_uint64(fixed_id,
              0,
              "fixed id to estimate translation"
              );

DEFINE_uint32(min_obs_per_view,
              2,
              "fixed id to estimate translation"
              );


DEFINE_string(remove_mode,
              "L1",
              "mode of removing outliers by LiGT residuals: normally, directly");

DEFINE_uint32(ltl_mode,
              0,
              "0 : FROM_TRACKS | 1 :FROM_FEATURES");


DEFINE_string(robust_track_file,
              " ",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(ori_folder,
              "/home/<USER>/",
              "Type of SfM reconstruction estimation to use.");


// set the minimal number of image observations in a track (1000000 for developed version)
// recommend value: 2~3
#define MIN_TRACKING_LENGTH 2


using namespace std;



int main(int argc, char *argv[]) {

    google::ParseCommandLineFlags(&argc, &argv, true);

    POVG::RobustLiGT problem(FLAGS_gR_file.c_str(),
                              FLAGS_tracks_file.c_str(),
                              FLAGS_output_file.c_str(),
                              FLAGS_time_file.c_str(),
                              FLAGS_fixed_id,
                             FLAGS_min_obs_per_view);


    problem.SetOriFolder(FLAGS_ori_folder);
//    problem.Solution();
////    problem.RobustSolution();
////    problem.NAGSolution();
////    problem.L1HomotopySolution();
//    problem.SolutionRemovingOutlier();

    cout << "POVG:" << FLAGS_remove_mode << endl;

    if( FLAGS_remove_mode == "RobustLiGT"){

        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::ROBUST_LIGT);
    }
    else if( FLAGS_remove_mode == "GNCLiGT"){

        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::GNC_LIGT);
    }
    else if( FLAGS_remove_mode == "Origin"){

        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::ORIGIN);
    }
    else if( FLAGS_remove_mode == "PA"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::PA);
    }
    else if( FLAGS_remove_mode == "ANAL_BA"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::ANAL_BA);
    }
    else if( FLAGS_remove_mode == "DLT_BA"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::DLT_BA);
    }
    else if( FLAGS_remove_mode == "ROBUST_LIGT_V2"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::ROBUST_LIGT_V2);
    }
    else if( FLAGS_remove_mode == "PA_V2"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::PA_V2);
    }
    else if( FLAGS_remove_mode == "OPENMVG_BA"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::OPENMVG_BA);
    }
    else if( FLAGS_remove_mode == "PTSPA_V2"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::PTSPA_V2);
    }
    else if( FLAGS_remove_mode == "NONE"){
        problem.SetOutlierRemovalTypes(POVG::OutlierRemovalTypes::NONE);
    }

    problem.SetLTLBuildMode(POVG::LTLMode(FLAGS_ltl_mode));

    if (FLAGS_reprocess_mode){
        problem.SolutionRemovingOutlier();
    }
    else {
        problem.RobustSolution();
    }


    problem.WriteTranslation();
    problem.WriteTime();


    google::ShutDownCommandLineFlags();

    return 0;
}

