// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>


#include "LiGT_algorithm.hpp"

DEFINE_string(tracks_file,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/matches/Matches_10Image_1000Points_10.00Dt_0.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(gR_file,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/global_R_1.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(output_file,
              "/home/<USER>/output_file.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(pro_file,
              "/home/<USER>/LiGT_outlier.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(time_file,
              "time_output_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_bool(reprocess_mode,
            false,
            "Type of SfM reconstruction estimation to use.");

DEFINE_bool(bearing_mode,
            true,
            "Type of SfM reconstruction estimation to use.");

DEFINE_uint32(pro_process_mode,
            5,
            "see PRD_PROCESS_MODE");

DEFINE_uint64(fixed_id,
              0,
              "fixed id to estimate translation"
              );

DEFINE_uint32(min_obs_per_view,
              3,
              "fixed id to estimate translation"
              );

DEFINE_string(remove_mode,
              "directly",
              "mode of removing outliers by LiGT residuals: normally, directly");

DEFINE_string(robust_track_file,
              " ",
              "Type of SfM reconstruction estimation to use.");


using namespace std;
using namespace POVG;



int main(int argc, char *argv[]) {

    google::ParseCommandLineFlags(&argc, &argv, true);
    PRO_PROCESS_MODE mode = static_cast<PRO_PROCESS_MODE>(FLAGS_pro_process_mode);

    cout<<"## Pure Rotational Outlier Process Mode = " << mode << endl;
    POVG::LiGTProblem problem(FLAGS_gR_file.c_str(),
                              FLAGS_tracks_file.c_str(),
                              FLAGS_output_file.c_str(),
                              FLAGS_time_file.c_str(),
                              FLAGS_fixed_id,
                              FLAGS_min_obs_per_view,
                              FLAGS_bearing_mode,
                              mode);

    problem.SetPROFile(FLAGS_pro_file);

    if (FLAGS_pro_process_mode == OPENMVG_MODE ||
        FLAGS_pro_process_mode == PRI_REMOVE_MODE ||
        FLAGS_pro_process_mode == M3_REMOVE_MODE){
        problem.PROSolution();
    }
    else{
        problem.Solution();
    }

    problem.WriteTranslation();
    problem.WriteTime();

    cout << "POVG:" << FLAGS_remove_mode << endl;

    if( FLAGS_remove_mode == "directly"){


    }
    if( FLAGS_remove_mode == "remove_obs"){


    }
    if( FLAGS_remove_mode == "remove_obs_PA"){


    }

    google::ShutDownCommandLineFlags();

    return 0;
}

