#ifndef ANALYTICAL3D_HPP
#define ANALYTICAL3D_HPP

#include "povg_types.hpp" // 包含POVG类型定义
#include <Eigen/Dense>
#include <string>
#include <vector>
#include <memory>

using namespace POVG;

class Analytical3D {
public:
    Analytical3D(){};
    ~Analytical3D(){
        tracksInfo_=nullptr;
        globalPoses_.clear();
        pts_3d_.clear();
    };
    void loadTracks(const std::string& filename, const Size& min_track_length, bool bearingMode = false);
    void setupOrientation(const std::string& filename);
    void setupTrans(const std::string& filename);
    void analyticalReconstruction();
    void writePts(const std::string& filename);
    void writePLY(const std::string& filename);

    bool obtain3DPoint(Eigen::Vector3d &world_coor, const PtsId& pts_id);
    bool obtain3DPoint(POVG::WorldPoint& world_point,const PtsId& pts_id);

    void setMinNumObsPerPoint(const unsigned int threshold);
    void setPrdM3Threshold(const double threshold);

    // outlier process
    bool PrdM3CheckPt(const PtsId& pts_id);
    bool removeOutierPts();
    void writeOutlierPtIds(const std::string& filename);
private:
    POVG::TracksInfoPtr tracksInfo_; // 使用POVG命名空间中的轨迹信息
    POVG::Poses globalPoses_; // 全局位姿信息
    POVG::WorldPoints pts_3d_; // 存储重建的3D点

    size_t num_view_;

    unsigned int min_num_obs_per_point_ = 2;
    double prd_m3_threshold_ = 0.05;

};


#endif // ANALYTICAL3D_HPP
