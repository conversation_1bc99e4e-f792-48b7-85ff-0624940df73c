#define EIGEN_NO_DEBUG

#include <math.h>
#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include <type_traits>
#include <limits>
#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>
#include <cmath>
#include <unordered_map>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <chrono>
#include <set>
#include <Eigen/Core>
#include <memory>
#include "anal3d.h"
#include "povg_tracks.hpp"
#include "povg_timer.hpp"

#define IsNonZero(d) (fabs(d) > 1e-15)
#define T_size 3 * (est_view_size)
// #define ISDEBUG
DEFINE_string(pts_file,
              "pts_file",
              "Type of SfM reconstruction estimation to use.");

DEFINE_double(m3_ratio,
              0,
              "Type of SfM reconstruction estimation to use.");
DEFINE_int32(min_num_observations_per_point,
             2,
             "Type of SfM reconstruction estimation to use.");

DEFINE_string(tracks_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/Matches_10Image_1000Points_10.00Dt_0.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(robust_tracks_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/Matches_10Image_1000Points_10.00Dt_0.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(gR_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/global_R_1.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(trans_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/global_t_1.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(outlier_3dptIds_file,
              "/home/<USER>/arecon_outlier_ids.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(mode,
              "normal",
              "mode of SfM reconstruction estimation to use: normal, remove_reconstruction_outliers,robust_reconstruction");

using namespace std;
using namespace chrono;
using namespace Eigen;

Matrix<double, 3, Dynamic> ConvertToWorldMatrix(const WorldPoints &worldPoints)
{
    Matrix<double, 3, Dynamic> matrix(3, worldPoints.size());
    int col = 0;
    for (const auto &wp : worldPoints)
    {
        if (wp.is_used)
        {
            matrix.col(col++) = wp.world_pts;
        }
    }
    matrix.conservativeResize(3, col); // 调整大小以去除未使用的列
    return matrix;
}

bool write_ply_file(const char *filename, const Matrix<double, 3, Dynamic> &points3D)
{
    ofstream outFile(filename);

    if (!outFile.is_open())
    {
        cerr << "Unable to open file: " << filename << endl;
        return false;
    }

    // Writing the PLY header
    outFile << "ply\n";
    outFile << "format ascii 1.0\n";
    outFile << "element vertex " << points3D.cols() << "\n";
    outFile << "property float x\n";
    outFile << "property float y\n";
    outFile << "property float z\n";
    outFile << "end_header\n";

    // Writing the point data
    for (int i = 0; i < points3D.cols(); ++i)
    {
        outFile << points3D(0, i) << " " << points3D(1, i) << " " << points3D(2, i) << "\n";
    }

    outFile.close();
    cout << "PLY file written: " << filename << endl;
    return true;
}

bool write_ply_file(const char *filename, const Matrix<double, 3, Dynamic> &points3D, const Pose &pose)
{
    ofstream outFile(filename);

    if (!outFile.is_open())
    {
        cerr << "Unable to open file: " << filename << endl;
        return false;
    }

    // Writing the PLY header
    outFile << "ply\n";
    outFile << "format ascii 1.0\n";
    outFile << "element vertex " << points3D.cols() + 2 << "\n"; // +1 for the camera position
    outFile << "property float x\n";
    outFile << "property float y\n";
    outFile << "property float z\n";
    outFile << "property uchar red\n"; // Adding color properties
    outFile << "property uchar green\n";
    outFile << "property uchar blue\n";
    outFile << "end_header\n";

    // Writing the point data
    for (int i = 0; i < points3D.cols(); ++i)
    {
        // Assuming default color for points (e.g., white: 255, 255, 255)
        outFile << points3D(0, i) << " " << points3D(1, i) << " " << points3D(2, i) << " 0 0 0\n";
    }

    // Writing the camera position in red color
    Vector3d camera_position = pose.translations;
    outFile << camera_position(0) << " " << camera_position(1) << " " << camera_position(2) << " 255 0 0\n";
    outFile << 0 << " " << 0 << " " << 0 << " 0 255 0\n";

    outFile.close();
    cout << "PLY file written: " << filename << endl;
    return true;
}

void Analytical3D::loadTracks(const std::string &filename, const Size &min_track_length, bool bearingMode)
{
    // 假设已经在POVG命名空间内有一个相应的函数可以调用
    POVG::load_tracks_info(filename, min_track_length, this->tracksInfo_, bearingMode);
}

void Analytical3D::setupOrientation(const std::string &filename)
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        std::cerr << "Unable to open file: " << filename << std::endl;
        return;
    }

    Eigen::Matrix3d rotation;

    file >> num_view_;

    globalPoses_.reserve(num_view_);

    for (size_t id_view = 0; id_view < num_view_; ++id_view)
    {

        // check the global pose
        if (globalPoses_[id_view].is_used == false)
            continue;

        file >> rotation(0, 0) >> rotation(1, 0) >> rotation(2, 0);
        file >> rotation(0, 1) >> rotation(1, 1) >> rotation(2, 1);
        file >> rotation(0, 2) >> rotation(1, 2) >> rotation(2, 2);

        // check the global rotation
        if (checkRotation(rotation))
        {
            globalPoses_[id_view].rotations = rotation;
            globalPoses_[id_view].is_used = true;
        }
        else
        {
            globalPoses_[id_view].is_used = false;
        }
    }
    file.close();
}

void Analytical3D::setupTrans(const std::string &filename)
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        std::cerr << "Unable to open translation file: " << filename << std::endl;
        return;
    }

    Eigen::Vector3d translation;

    for (size_t id_view = 0; id_view < num_view_; ++id_view)
    {

        // check the global pose
        if (globalPoses_[id_view].is_used == false)
            continue;

        file >> translation(0) >> translation(1) >> translation(2);

        // check the global translation
        if (checkTranslation(translation))
        {
            globalPoses_[id_view].translations = translation;
            globalPoses_[id_view].is_used = true;
        }
        else
        {
            globalPoses_[id_view].is_used = false;
        }
    }
    file.close();
}

void Analytical3D::analyticalReconstruction()
{
    POVG::Timer timer; // 使用POVG::Timer来测量执行时间

    size_t num_3dpts = tracksInfo_->tracks.size(); // 获取轨迹点的数量

    pts_3d_.clear();           // 准备容器以存储新的数据
    pts_3d_.resize(num_3dpts); // 调整容器大小以匹配轨迹点数量

    cout << "caculating 3dpts..., num_pts= " << num_3dpts << endl;

    size_t num_recon_pts = 0;

    for (size_t pts_id = 0; pts_id < num_3dpts; ++pts_id)
    {

        // 遍历并解析重建轨迹中的3D点
        if (obtain3DPoint(pts_3d_[pts_id], pts_id))
        {
            num_recon_pts++;
        }
    }

    std::cout << " num pts =  " << num_3dpts << ", num reconstructed pts = " << num_recon_pts << std::endl;

    // 使用POVG::Timer打印执行时间
    std::cout << "解析重建的时间消耗: " << timer.Duration() << "秒" << std::endl;
}

bool Analytical3D::removeOutierPts()
{
    POVG::Timer timer; // 使用POVG::Timer来测量执行时间

    size_t num_3dpts = tracksInfo_->tracks.size(); // 获取轨迹点的数量

    pts_3d_.clear();           // 准备容器以存储新的数据
    pts_3d_.resize(num_3dpts); // 调整容器大小以匹配轨迹点数量

    cout << "[anal3d] removing outlier(m3 prd) 3dpts..." << endl;

    size_t num_removed_pts = 0;
    for (size_t pts_id = 0; pts_id < num_3dpts; ++pts_id)
    {
        // 遍历检测异常3D点
        if (PrdM3CheckPt(pts_id) == false)
        {
            num_removed_pts++;
        }
    }

    std::cout << "num_pts= " << num_3dpts << ", num removed pts = " << num_removed_pts << std::endl;

    // 使用POVG::Timer打印执行时间
    std::cout << "异常3D检测的时间消耗: " << timer.Duration() << "秒" << std::endl;
    return true;
}

void Analytical3D::writeOutlierPtIds(const std::string &filename)
{
    // save
    fstream outlier_file(filename, ios::out | ios::trunc);
    for (int i = 0; i < pts_3d_.size(); ++i)
    {
        if (pts_3d_[i].is_used == false)
        {
            outlier_file << i << std::endl;
        }
    }
    outlier_file.close();
}

void Analytical3D::writePts(const std::string &filename)
{
    fstream out(filename, ios::out | ios::trunc);
    if (!out)
    {
        std::cerr << "Cannot open output file." << filename << std::endl;
        return;
    }

    cout << "[anal3d] writting pts file..." << endl;
    for (const auto &wp : pts_3d_)
    {
        out << setprecision(16) << wp.world_pts.transpose() << std::endl;
        //        if (wp.is_used) {
        //            out << setprecision(16) << wp.world_pts.transpose() << std::endl;
        //        }
    }
}

void Analytical3D::writePLY(const std::string &filename)
{

    Matrix<double, 3, Dynamic> temp_3d = ConvertToWorldMatrix(pts_3d_);
    write_ply_file(filename.c_str(), temp_3d);
}

bool Analytical3D::PrdM3CheckPt(const PtsId &pts_id)
{
    double max_s = 0;
    const Track &ptr_track = this->tracksInfo_->tracks[pts_id].track;
    size_t track_length = ptr_track.size();

    for (size_t j = 0; j < track_length - 1; ++j)
    {
        const ObsInfo &left_obs = ptr_track[j];
        for (size_t k = j + 1; k < track_length; ++k)
        {
            const ObsInfo &right_obs = ptr_track[k];

            Eigen::Vector3d Rx = this->globalPoses_[right_obs.view_id].rotations * this->globalPoses_[left_obs.view_id].rotations.transpose() * left_obs.coord;
            Eigen::Vector3d xRx = right_obs.coord.cross(Rx);
            double s = xRx.norm() / (right_obs.coord.norm() * Rx.norm()) + std::numeric_limits<double>::epsilon();

            max_s = std::max(max_s, s);
        }
    }

    if (track_length < min_num_obs_per_point_ || max_s < prd_m3_threshold_)
    {
        pts_3d_[pts_id].is_used = false; // 如果轨迹点数量不足/prd不足重建，则标记为无效
        return false;
    }
    else
    {
        pts_3d_[pts_id].is_used = true;
        return true;
    }
}

void Analytical3D::setMinNumObsPerPoint(const unsigned int threshold)
{
    min_num_obs_per_point_ = threshold;
}

void Analytical3D::setPrdM3Threshold(const double threshold)
{
    prd_m3_threshold_ = threshold;
}

bool Analytical3D::obtain3DPoint(Eigen::Vector3d &world_coor, const PtsId &pts_id)
{
    // 初始化所需的变量
    Eigen::Vector3d Rx, xRx, Rl_xl, Rl_tl, tx_xr, r_t, cam_coor;
    double s, sum_s = 0, norm_tx_xr, scale = 1;
    double max_s = 0; // 用于存储最大的s值

    // 检查轨迹点的数量是否满足最小观测要求
    const auto &ptr_track = this->tracksInfo_->tracks[pts_id];
    if (ptr_track.track.size() < min_num_obs_per_point_)
    {
        world_coor.setZero();
        return false; // 如果不满足，则直接返回
    }

    for (size_t j = 0; j < ptr_track.track.size() - 1; ++j)
    {
        const ObsInfo &left_obs = ptr_track.track[j];
        const Pose &pose_left = this->globalPoses_[left_obs.view_id];

        for (size_t k = j + 1; k < ptr_track.track.size(); ++k)
        {
            const ObsInfo &right_obs = ptr_track.track[k];
            const Pose &pose_right = this->globalPoses_[right_obs.view_id];

            // 计算所需的向量和矩阵
            Rl_xl = pose_left.rotations.transpose() * left_obs.coord;
            Rl_tl = pose_left.rotations.transpose() * pose_left.translations;
            r_t = pose_right.translations - pose_right.rotations * Rl_tl;

            Rx = pose_right.rotations * Rl_xl;
            xRx = right_obs.coord.cross(Rx);
            s = xRx.norm();
            sum_s += s * s; // 累加s值的平方

            tx_xr = r_t.cross(right_obs.coord);
            norm_tx_xr = tx_xr.norm();

            // 应用缩放因子并更新最大s值
            s *= scale;
            max_s = std::max(max_s, s);

            // 计算相机坐标点并累加到世界坐标点
            double depth = 1;
            cam_coor = left_obs.coord * depth; // 假设深度为1
            world_coor += pose_left.rotations.transpose() * (s * (norm_tx_xr * cam_coor - s * pose_left.translations));
        }
    }

    // 根据最大s值和FLAGS_m3_ratio判断该点是否可重建
    if (max_s < prd_m3_threshold_)
    {

        world_coor.setZero();
    }
    else
    {

        world_coor /= sum_s * scale; // 计算最终的世界坐标点
    }
    return true;
}

bool Analytical3D::obtain3DPoint(POVG::WorldPoint &world_point, const PtsId &pts_id)
{

    double s, max_s = 0, sum_s = 0, norm_tx_xr, scale = 1;
    Eigen::Vector3d Rx, xRx, Rl_xl, Rl_tl, tx_xr, r_t, cam_coor;
    world_point.world_pts.setZero();
    world_point.is_used = false; // 默认认为3D点是无效重建

    const auto &ptr_track = this->tracksInfo_->tracks[pts_id];
    if (ptr_track.track.size() < min_num_obs_per_point_)
    {
        // 如果轨迹点数量不足，则标记为无效
        return false;
    }

    for (size_t j = 0; j < ptr_track.track.size() - 1; ++j)
    {
        const ObsInfo &left_obs = ptr_track.track[j];
        const Pose &pose_left = this->globalPoses_[left_obs.view_id];

        if (pose_left.is_used == false)
            continue;

        for (size_t k = j + 1; k < ptr_track.track.size(); ++k)
        {
            const ObsInfo &right_obs = ptr_track.track[k];
            const Pose &pose_right = this->globalPoses_[right_obs.view_id];

            if (pose_right.is_used == false)
                continue;

            // 使用全局位姿信息计算所需的向量
            Rl_xl = pose_left.rotations.transpose() * left_obs.coord;
            Rl_tl = pose_left.rotations.transpose() * pose_left.translations;
            r_t = pose_right.translations - pose_right.rotations * Rl_tl;

            Rx = pose_right.rotations * Rl_xl;
            xRx = right_obs.coord.cross(Rx);
            s = xRx.norm();
            sum_s += s * s; // 累加s值的平方，用于最后计算平均值

            tx_xr = r_t.cross(right_obs.coord);
            norm_tx_xr = tx_xr.norm();
            s = s * scale;              // 应用缩放因子
            max_s = std::max(max_s, s); // 更新最大s值

            // 根据深度计算相机坐标点，并累加到世界坐标点中
            cam_coor = left_obs.coord; // 这里假设深度为1，实际情况根据需要调整
            world_point.world_pts += pose_left.rotations.transpose() * (s * (norm_tx_xr * cam_coor - s * pose_left.translations));
            world_point.is_used = true;
        }
    }

    if (prd_m3_threshold_ > max_s)
    {
        world_point.is_used = false; // 如果最大s值小于阈值，标记点为无效
    }
    else
    {
        world_point.world_pts /= sum_s * scale; // 计算最终的世界坐标点
    }

    return true;
}

void PrintAnal3dCopyright()
{

    cout << "\n===============================================================\n"
         << "    The Analytical Reconstruction Algorithm for 3D points\n"
         << "[Conditions of Use] The Analytical Reconstruction algorithm is distributed under the License\n"
         << "of Attribution-ShareAlike 4.0 International\n"
         << "(https://creativecommons.org/licenses/by-sa/4.0/).\n"
         << "If you use it for a publication, please see [Notes] in header file.\n"
         << "---------------------------------------------------------------\n";
}

int main(int argc, char *argv[])
{
    if (argc < 4)
    {
        cout << "usage: " << "--tracks_file=? " << "--gR_file=? " << "--trans_file=?" << "--pts_file=?" << endl;
        cout << "tracks_file: tracks file supported by user or dataset" << endl;
        cout << "gR_file: global rotation file" << endl;
        cout << "trans_file: global rotation file" << endl;
        cout << "pts_file: output file for 3D points" << endl;
        return 1;
    }

    google::ParseCommandLineFlags(&argc, &argv, true);

    PrintAnal3dCopyright();

    cout << "[anal3d] Pr degree (M3) = " << FLAGS_m3_ratio << endl;
    cout << "[anal3d] min track length = " << FLAGS_min_num_observations_per_point << endl;
    Analytical3D anal_3d;
    anal_3d.setupOrientation(FLAGS_gR_file.c_str());
    anal_3d.setPrdM3Threshold(FLAGS_m3_ratio);

    anal_3d.loadTracks(FLAGS_tracks_file.c_str(), FLAGS_min_num_observations_per_point);

    if (FLAGS_mode == "normal")
    {
        anal_3d.setupTrans(FLAGS_trans_file.c_str());
        anal_3d.analyticalReconstruction();
        anal_3d.writePts(FLAGS_pts_file.c_str());
        anal_3d.writePLY("/home/<USER>/test_3dpts.ply");
    }
    else if (FLAGS_mode == "remove_outliers")
    {

        anal_3d.removeOutierPts();
        anal_3d.writeOutlierPtIds(FLAGS_outlier_3dptIds_file.c_str());
    }
    else if (FLAGS_mode == "a")
    {
    }

    google::ShutDownCommandLineFlags();
    return 0;
}
