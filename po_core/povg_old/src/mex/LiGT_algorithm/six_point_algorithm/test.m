clearvars -except seq
close all;
%% 配置matlab的python语言环境
% pyenv('Version', '/usr/local/bin/python3.9','ExecutionMode', 'OutOfProcess');
% !ln -s '/media/qicai/0F7D0D870F7D0D87/ransac2020/RANSAC-Tutorial-Data-ValOnly/val' val
%% 动态添加utils模块路径到Python搜索路径中
utilsDir = '/home/<USER>/ransac-tutorial-2020-data-master/';
if count(py.sys.path, utilsDir) == 0
    insert(py.sys.path, int32(0), utilsDir);  % 添加utils目录
end

%% 初始化Python环境
np = py.importlib.import_module('numpy');
h5py = py.importlib.import_module('h5py');
cv2 = py.importlib.import_module('cv2');
% 尝试加载utils模块，但我们将实现自己的h5加载函数
try
    utils = py.importlib.import_module('utils');
    utils_loaded = true;
catch
    warning('无法加载utils模块，将使用自定义函数');
    utils_loaded = false;
end
% 导入pymagsac模块
pymagsac = py.importlib.import_module('pymagsac');
%% 设置目录
DIR = 'val';
seq = 'sacre_coeur';%%2000会崩溃
% seq = 'st_peters_square'; 
folder = createRansacTestFolder();

method_configs = load_local_config(folder);

%% 创建符号链接（在MATLAB中可能需要管理员权限或手动执行）
data_src_folder = fullfile('val',seq);
link_folder = fullfile(folder,DIR);

% 加载数据
% 使用MATLAB原生函数加载h5文件
matches = load_h5_matlab(strcat(link_folder, '/', seq, '/matches.h5'));
F_gt = load_h5_matlab(strcat(link_folder, '/', seq, '/Fgt.h5'));
E_gt = load_h5_matlab(strcat(link_folder, '/', seq, '/Egt.h5'));
matches_scores = load_h5_matlab(strcat(link_folder, '/', seq, '/match_conf.h5'));
K1_K2 = load_h5_matlab(strcat(link_folder, '/', seq, '/K1_K2.h5'));
R = load_h5_matlab(strcat(link_folder, '/', seq, '/R.h5'));
T = load_h5_matlab(strcat(link_folder, '/', seq, '/T.h5'));

% 图像处理和显示
count = 0;
pair_to_show = 0;
keys = cell(py.list(F_gt.keys()));

num_method = size(method_configs,1);

num_test = 10;
% num_test = length(keys);
R_error = ones(num_test,num_method) * nan;
icount=0;
matches_test=[];
id_ij = [];
Rt= [];
inlier_sets = [];
residual_tables=cell(num_method,1);
for idx = (1:num_test)
    k = keys{idx};
    % 将Python字符串转换为MATLAB字符串
    k_str = char(k);
    F = F_gt(k_str);

    m = matches(k_str);
    key_pair = k_str;
    split_str = string(split(k_str, '-'));
    img1_fname = strcat(link_folder, '/', seq, '/images/', split_str{1}, '.jpg');
    img2_fname = strcat(link_folder, '/', seq, '/images/', split_str{2}, '.jpg');

    img1 = cv2.cvtColor(cv2.imread(img1_fname), cv2.COLOR_BGR2RGB);
    img2 = cv2.cvtColor(cv2.imread(img2_fname), cv2.COLOR_BGR2RGB);
    m_mat = double(m)';
    pts1 = m_mat(:, 1:2);  % 提取前两列作为pts1
    pts2 = m_mat(:, 3:end);  % 提取剩余列作为pts2
    % 假设pts1和pts2已经是MATLAB数组，需要转换为NumPy数组
    pts1_np = py.numpy.array(pts1);
    pts2_np = py.numpy.array(pts2);

    ms = matches_scores(k_str);
    ms_mat = double(ms);
    good_matches = ms_mat <= 0.9;%%%%%%%
    pts1_good = pts1(good_matches, :);
    pts2_good = pts2(good_matches, :);
    pts1_good_np = py.numpy.array(pts1_good);
    pts2_good_np = py.numpy.array(pts2_good);

    [K1_np,K2_np] = obtainKmats(double(K1_K2(k_str)));

        p1n = py.numpy.array(normalize_keypoints_matlab(pts1_good, double(K1_np)));
        p2n = py.numpy.array(normalize_keypoints_matlab(pts2_good, double(K2_np)));
    
    p_combined = np.hstack(py.tuple({p1n, p2n}));

    
    % 首先，分割字符串k_str
    split_str = split(k_str, '-');
    keyToCheck1 = char(split_str{1});
    keyToCheck2 = char(split_str{2});
    % Step 4: Get all keys from the Python dict
    R_keys = py.list(R.keys());
    
    % Step 5: Check if the key is in the dictionary
%     RkeyExists = ismember(keyToCheck, cellfun(@char, cell(R_keys)));
    RkeyExists1 = ismember(keyToCheck1, cellfun(@char, cell(R_keys), 'UniformOutput', false));
    RkeyExists2 = ismember(keyToCheck2, cellfun(@char, cell(R_keys), 'UniformOutput', false));
    % Display the result
    if RkeyExists1 && RkeyExists2

        R1 = R(keyToCheck1);
        R2 = R(keyToCheck2);

        T1 = T(keyToCheck1);
        T2 = T(keyToCheck2);
    else
        disp(['The key "', keyToCheck1, '" does not exist in the dictionary.']);
        disp(['The key "', keyToCheck2, '" does not exist in the dictionary.']);
        continue;
    end

    % 然后，使用修正后的索引方式从结构体中获取元素
    

    dR = np.dot(R2, R1');
    dT = T2 - np.dot(dR, T1);

    E_mat = crossmatrix(double(dT))*double(dR);

    Matches = double(p_combined)';
    [~,n]=size(Matches);
    x=[Matches(1:2,1:n);ones(1,n)];
    xmatch=[Matches(3:4,1:n);ones(1,n)];
    [x1, x2] = obtain_bearing_vectors(x,xmatch);

    if n <= 20 
        R_error(idx,id_method) = nan;
        continue;
    end
    icount = icount +1;
%     id_ij{icount,1} = [1,icount+1];
    

    for id_method = 1:num_method
        callback = method_configs.callback{id_method};
        language = method_configs.language{id_method};
        method_name = method_configs.method_name{id_method};
        nIter = method_configs.iterations(id_method);
        if strcmp(language,'matlab')
            [Rt_new,~,~,inliers] = ImageProcessing(double(p_combined)',eye(3),eye(3),callback);
            if sum(double(isnan(Rt_new(:))))>0
                continue;
            end
            pomvg_E_mat = crossmatrix(Rt_new(:,4))*Rt_new(1:3,1:3);
            E_eval = py.numpy.array(pomvg_E_mat);

                    
        elseif strcmp(language,'OpenCV')
            tic;
            output = cell(cv2.findEssentialMat(p1n, p2n, np.eye(uint32(3)), cv2.(callback) , 0.99, 1e-3, uint32(nIter)));
            time_cst=toc;
            tmpstr= sprintf('time cost in OpenCV:%.3f s',time_cst);
            disp(tmpstr);
            E_eval = output{1};
            inl_mask = output{2};
            inliers = logical(double(inl_mask));
            [Right_R,Right_t] = identify_right_solution(reshape(double(E_eval),[],1),x2(:,inliers),x1(:,inliers),'opengv_c');
            Rt_new = [Right_R,Right_t];

        elseif strcmp(language,'python')
            tic;
            K1_np = np.ascontiguousarray(K1_np);
            K2_np = np.ascontiguousarray(K2_np);
            K1 = double(K1_np);
            K2 = double(K2_np);

            Kx = K1*x;
            Kxmatch = K2*xmatch;
            correspondences = [Kx(1:2, :)', Kxmatch(1:2, :)'];
            correspondences = np.ascontiguousarray(np.array(correspondences));
            
             
            w1 = K1(1,3)*2;
            h1 = K1(2,3)*2;
            w2 = K2(1,3)*2;
            h2 = K2(2,3)*2;
            % 计算概率（这里假设每个匹配点的概率相同）
            
            sampler_id = int32(4);
            if sampler_id == 3 || sampler_id == 4
                % 计算概率（这里假设每个匹配点的概率相同）
                probabilities = get_probabilities(n);
                probabilities = np.ascontiguousarray(np.array(probabilities));
            else
                % 使用空列表
                probabilities = py.list();
            end
            
            output = cell(pymagsac.findEssentialMatrix(...)}

            time_cst=toc;
            tmpstr= sprintf('time cost in MAGSAC++:%.3f s',time_cst);
            disp(tmpstr);
            E_eval = output{1};
            % inl_mask = output{2};
            mask_np = np.ascontiguousarray(output{2});
            inliers = logical(mask_np);
            disp([num2str(sum(inliers)), ' inliers found']);
            % inliers = logical(double(inl_mask));
            try
            [Right_R,Right_t] = identify_right_solution(reshape(double(E_eval),[],1),x2(:,inliers),x1(:,inliers),'opengv_c');
            Rt_new = [Right_R,Right_t];
            catch
                continue;
            end
        end
%         [Rt_new1,~,~,~] = ImageProcessing(Matches(:,inliers),eye(3),eye(3),'LiRP');
%          [Rt_new1,~,~,~] = ImageProcessing(Matches(:,inliers),eye(3),eye(3),'fivept_stewenius');
%             pomvg_E_mat1 = crossmatrix(Rt_new1(:,4))*Rt_new1(1:3,1:3);
%             E_eval1 = py.numpy.array(pomvg_E_mat1);

        % ransac2020 (x2,x1)> opengv(v1,v2) >> pomvg (x2,x1)
        r_opengv_gt = mean(residual_opengv(x2(:,inliers),x1(:,inliers),double(dR),double(dT)));
        r_ppo_gt = mean(residual_PPO(x2(:,inliers),x1(:,inliers),double(dR),double(dT)));
        r_ba_gt = mean(residual_BA(x2(:,inliers),x1(:,inliers),double(dR),double(dT)));
%         r_LiGT_gt = median(residual_LiGT(x2(:,inliers),x1(:,inliers),double(dR),double(dT)));
        
        r_opengv = mean(residual_opengv(x2(:,inliers),x1(:,inliers),Rt_new(:,1:3),Rt_new(:,4)));
        r_ppo = mean(residual_PPO(x2(:,inliers),x1(:,inliers),Rt_new(:,1:3),Rt_new(:,4)));
        r_ba = mean(residual_BA(x2(:,inliers),x1(:,inliers),Rt_new(:,1:3),Rt_new(:,4)));
%         r_LiGT = median(residual_LiGT(x2(:,inliers),x1(:,inliers),Rt_new(:,1:3),Rt_new(:,4)));
    
%         r_opengv_1 = mean(residual_opengv(x2(:,inliers),x1(:,inliers),Rt_new1(:,1:3),Rt_new1(:,4)));
%         r_ppo_1 = mean(residual_PPO(x2(:,inliers),x1(:,inliers),Rt_new1(:,1:3),Rt_new1(:,4)));
%         r_ba_1 = mean(residual_BA(x2(:,inliers),x1(:,inliers),Rt_new1(:,1:3),Rt_new1(:,4)));
%         r_LiGT_1 = median(residual_LiGT(x2(:,inliers),x1(:,inliers),Rt_new1(:,1:3),Rt_new1(:,4)));

            [r_opengv,r_ppo,r_ba;
%             r_opengv_1,r_ppo_1,r_ba_1;
            r_opengv_gt,r_ppo_gt,r_ba_gt]
        

        % Rt_error = utils.eval_essential_matrix(p1n, p2n, E_eval, dR, dT);
        Rt_error{1} = 0;
        R_error(idx,id_method) = double(0);

        cprintf('Comments','[Rotation angular error, radians,%s]: %s\n',method_name,string(Rt_error{1}));

%         Rt_error1 = utils.eval_essential_matrix(p1n, p2n, E_eval1, dR, dT);
%         cprintf('Comments','[Rotation angular error, radians,%s， LiRP]: %s\n',method_name,string(Rt_error1{1}));
%         
        img1 = imread(img1_fname);
        img2 = imread(img2_fname);
        loc1 = double(pts1_good);
        loc2 = double(pts2_good);
%         drawMatched(img1,img2,loc1(:,:)',loc2(:,:)')
%         drawMatched(img1,img2,loc1(inliers,:)',loc2(inliers,:)')
%         drawMatched(img1,img2,loc1(inlier_sets{2,icount+1},:)',loc2(inlier_sets{2,icount+1},:)')
        if R_error(idx,id_method) > 0.1 && id_method==4
            a=9;
        end
        tmp_matches = double(p_combined)';

%         matches_test{id_method,icount+1} = tmp_matches(:,inliers);
%         inlier_sets{id_method,icount+1} = inliers;
%         Rt{icount,id_method} = Rt_new;

        % 设置输出文件路径
        output_file = fullfile(folder, ['relative_residuals_',num2str(id_method),'.csv']);
        if idx == 1 
            if exist(output_file,'file')
                delete(output_file);
            end
        end
        % 写入相对位姿和匹配数据
        tmp_id_ij = {[1,2]};
        write_relative_pose('test_relative_pose.txt', {Rt_new}, tmp_id_ij);
        write_matches_bin(tmp_id_ij, {[],tmp_matches(:,inliers)}, 'test_bearing_vectors.txt', {eye(3), eye(3)});
        
        % 构建并执行系统命令
        cmd = sprintf(['povg_relative_residuals ', ...
            '--solve_mode=all ', ...
            '--bearing_vector_file=%s ', ...
            '--output_file=%s ', ...
            '--relative_pose_file=%s '], ...
            'test_bearing_vectors.txt', ...
            output_file, ...
            'test_relative_pose.txt');
        system(cmd);

        % 读取输出文件并增加新的表头
        residual_table = readtable(output_file);
        
        residual_table.method = repmat({method_name}, height(residual_table), 1);

        % 将新列放置在表格的首列
        residual_table = [residual_table(:, end), residual_table(:, 1:end-1)];

        residual_tables{id_method} = [residual_tables{id_method};residual_table];

    end
    
    if R_error(idx,3)>0.3
        aaaaa=0;
    end
    
    cprintf('Comments','----------- paris: %d ------------\n',idx);

    R_error(idx,:)
end

% 在第一个循环结束后，保存每个 id_method 对应的表格，并计算平均值和中值
final_summary = [];

for id_method = 1:num_method
    combined_residual_table = vertcat(residual_tables{id_method});
    output_file = fullfile(folder, ['combined_relative_residuals_', num2str(id_method), '.csv']);
    writetable(combined_residual_table, output_file);

    % 计算平均值和中值
    mean_row = varfun(@mean, combined_residual_table(:,2:6));
    median_row = varfun(@median, combined_residual_table(:,7:11));


    % 合并平均值和中值到最终的 summary 表格
    final_summary = [final_summary;[combined_residual_table(1,1), mean_row, median_row]];
end

save_folder = folder;
save_name = sprintf('%s_data',seq);
save(fullfile(save_folder,[save_name,'.mat']))

% 将最终合并后的表格保存到一个文件中
final_output_file = fullfile(folder, [seq, '_combined_residuals_v2.xls']);
writetable(final_summary, final_output_file);


R_error_file = fullfile(folder,[seq '_R_error.xls']);
if exist(R_error_file,'file')
    delete(R_error_file)
end

% row_nan = ~isnan(R_error(:,end));
row_nan = 1:size(R_error,1);
T_R_error = array2table(R_error(row_nan,:), 'VariableNames', method_configs.method_name);
writetable(T_R_error,fullfile(folder,[seq '_R_error.xls']));

% 假设T_R_error已经在工作区中，并且是一个table
% 如果 T_R_error 是一个表格，转换为数值数组
if istable(T_R_error)
    T_R_error = table2array(T_R_error);
end

%% boxchart
% 获取方法名称并去除空格
method_names = method_configs.method_name;
method_names = replace_underscore(method_names);
method_names = removeSpaces(method_names);
method_names(end)=[];
num_method= num_method-1;
% 提取(n)中的数值
group_nums = regexp(method_names, '\((\d+)\)', 'tokens');
group_nums = cellfun(@(x) str2double(x{1}), group_nums);

% 获取唯一组别和分配颜色
unique_groups = unique(group_nums);
colors = lines(numel(unique_groups)); % 使用lines函数生成不同颜色

% 创建分类向量
group_indices = arrayfun(@(x) find(unique_groups == x), group_nums);

% 初始化图表
hfig = figure;
hold on;

% 绘制箱体图
for i = 1:num_method
    group_data = T_R_error(:, i);
    boxchart(repmat(i, size(group_data)), group_data, 'BoxFaceColor', colors(group_indices(i), :), 'MarkerStyle', 'none');
end

% 设置x轴标签
xticks(1:num_method);
xticklabels(method_names);

% 设置标题和轴标签
ylabel('R error');
set(gca, 'YScale', 'log');
set(gca, 'FontSize', 20, 'FontWeight', 'bold', 'XTickLabelRotation', 90);

% 显示网格线
grid on;
% 
% % 保存图像
% save_folder = 'your_save_folder'; % 请替换为实际的保存文件夹路径
% save_name = sprintf('%s_R_error_box', 'your_sequence'); % 请替换为实际的序列名称
% save_name = fullfile(save_folder, save_name);
% saveas(hfig, [save_name '.png']);
% saveas(hfig, [save_name '.fig']);
hold off;
%%


% 计算均值和中位数
mean_R_error = mean(R_error,'omitnan');
median_R_error = median(R_error,'omitnan');

% 创建一个包含均值和中位数的矩阵
final_errors = [mean_R_error; median_R_error];

% 将矩阵转换为表格
temp_T = array2table(final_errors, 'VariableNames', method_configs.method_name);

% 添加一个新列作为行标签
row_labels = {'mean_error'; 'median_error'};
temp_T = addvars(temp_T, row_labels, 'Before', 1, 'NewVariableNames', 'ErrorType');

% 保存表格到文件
writetable(temp_T, fullfile(folder, [seq '_mean_median_R_error.xls']));


figure;
for test_id = 1:4
subplot(3, 2, test_id); % 创建2x2的子图布局
hold on;
test_R = R_error(:, test_id);
judge = isnan(R_error(:, 5));
plot(test_R, 'b');
ids = 1:num_test;
ids = ids(judge);
judge2 = test_R(judge, 1) / pi * 180 > 4;
plot(ids(judge2), test_R(ids(judge2), 1), 'ro');
plot(ids(~judge2), test_R(ids(~judge2), 1), 'go');
plot(1:num_test, ones(1, num_test) * 4 / 180 * pi, 'k');
title(method_names{test_id});
set(gca, 'YScale', 'log');
end
subplot(3, 2, 5);
hold on;
plot(R_error(:, 5), 'b');
set(gca, 'YScale', 'log');
plot(1:1000, ones(1, 1000) * 4 / 180 * pi, 'k');
subplot(3, 2, 6);
hold on;
plot(1:1000, ones(1, 1000) * 4 / 180 * pi, 'k');
plot(R_error(:, 6), 'b');
set(gca, 'YScale', 'log');


function newFolderPath = createRansacTestFolder()
% 获取当前脚本的完整路径
currentScriptFullPath = mfilename('fullpath');

% 从完整路径中提取目录路径
[currentScriptDir, ~, ~] = fileparts(currentScriptFullPath);

% 定义新文件夹的名称
newFolderName = 'test_ransac2020';

% 创建新文件夹的完整路径
newFolderPath = fullfile(currentScriptDir, newFolderName);

% 创建文件夹
createFolderIfNotExist(newFolderPath);

end

function [K1_np,K2_np] = obtainKmats(K1_K2_mat)

K1_mat=zeros(3);K2_mat=K1_mat;
for i = 1:3
    for j = 1:3
        K1_mat(j,i)=K1_K2_mat(i,j,1);
        K2_mat(j,i)=K1_K2_mat(i,j,2);
    end
end

K1_np = py.numpy.array(K1_mat);
K2_np = py.numpy.array(K2_mat);
end


function probabilities = get_probabilities(num_matches)
probabilities = zeros(1, num_matches);
for i = 1:num_matches
    probabilities(i) = 1.0 - (i - 1) / num_matches;
end
end

function data = load_h5_matlab(filename)
    % 使用MATLAB原生函数加载H5文件
    data = containers.Map();
    try
        % 获取文件信息
        info = h5info(filename);
        % 遍历所有数据集
        for i = 1:length(info.Datasets)
            % 获取数据集名称
            datasetName = info.Datasets(i).Name;
            % 读取数据
            value = h5read(filename, ['/', datasetName]);
            % 存储到Map中
            data(datasetName) = value;
        end
        
        % 如果有组，处理组内的数据集
        for i = 1:length(info.Groups)
            processGroup(info.Groups(i), data, filename);
        end
    catch e
        warning('无法加载H5文件: %s, 错误: %s', filename, e.message);
    end
end

function processGroup(group, dataMap, filename)
    % 处理H5文件中的组及其数据集
    groupPath = group.Name;
    
    % 处理当前组中的数据集
    for i = 1:length(group.Datasets)
        datasetName = group.Datasets(i).Name;
        fullPath = [groupPath, '/', datasetName];
        value = h5read(filename, fullPath);
        
        % 从路径中提取键名（去除前导'/'）
        keyName = datasetName;
        if groupPath(1) == '/'
            groupPathKey = groupPath(2:end);
        else
            groupPathKey = groupPath;
        end
        
        if ~isempty(groupPathKey)
            keyName = [groupPathKey, '/', datasetName];
        end
        
        dataMap(keyName) = value;
    end
    
    % 递归处理子组
    for i = 1:length(group.Groups)
        processGroup(group.Groups(i), dataMap, filename);
    end
end

function normalized_points = normalize_keypoints_matlab(points, K)
    % 实现点的归一化变换
    % points: nx2的点坐标数组
    % K: 3x3的相机内参矩阵
    
    % 获取内参矩阵的逆
    K_inv = inv(K);
    
    % 转换为齐次坐标
    n = size(points, 1);
    homogeneous_points = [points, ones(n, 1)];
    
    % 归一化变换
    normalized_homogeneous = (K_inv * homogeneous_points')';
    
    % 返回非齐次坐标
    normalized_points = normalized_homogeneous(:, 1:2) ./ repmat(normalized_homogeneous(:, 3), 1, 2);
end