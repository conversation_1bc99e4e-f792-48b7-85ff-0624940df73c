def shuffle_ransac(self, data1, data2, num_min_score_ids=None):
        """
        RAPIDS算法实现 - 完全匹配C++版本的GNCInfo::RAPIDS函数
        
        Parameters:
        -----------
        data1, data2 : numpy.ndarray
            输入数据点
        num_min_score_ids : int, optional
            解池大小，默认使用self.pool_size
            
        Returns:
        --------
        best_model : object
            找到的最佳模型
        best_inliers : list
            最佳模型的内点索引
        """
        # 使用初始化时的pool_size如果未提供num_min_score_ids
        if num_min_score_ids is None:
            num_min_score_ids = self.pool_size
            
        # 初始化bad_scores数组，用于跟踪点的质量
        num_matches = len(data1)
        bad_scores = np.zeros(num_matches, dtype=int)  # 与C++实现中的bad_scores一致
        
        # 初始化最佳模型相关变量
        self.best_inlier_count = -1  # C++中使用-1初始化
        self.best_model = None
        
        # RANSAC迭代计算参数
        k = 1.0  # 默认所需迭代次数
        max_iterations = self.max_iterations
        iterations = 0
        
        # 失败计数器
        failed_number = 0
        
        # 内点比例(γ)初始化为1，表示假设所有点都是外点
        gamma = 1.0
        flag_update_pool = True
        # 主循环 - 匹配C++中的while (i < k || gamma > 0.3)
        while iterations < k or gamma > 0.3:
            # 更新solve_pool，选择bad_scores最小的点作为解池
            if flag_update_pool:
                solve_pool_ids = find_min_k_indices(bad_scores, num_min_score_ids)
                flag_update_pool = False

            # 从解池中随机抽样
            indices = []
            data1_sample = np.zeros((self.sample_size, data1.shape[1]))
            data2_sample = np.zeros((self.sample_size, data2.shape[1]))
            
            for j in range(self.sample_size):
                # 随机选择解池中的一个索引
                rand_index = np.random.randint(0, len(solve_pool_ids))
                idx = solve_pool_ids[rand_index]
                indices.append(idx)
                data1_sample[j] = data1[idx]
                data2_sample[j] = data2[idx]
                
            # 检查是否有足够的样本点
            if len(indices) < self.sample_size:
                print("解池中没有足够的数据")
                break
                
            # 使用GNC-IRLS计算模型参数
            success, model, weights = self.gnc_irls(data1_sample, data2_sample)
            
            #处理失败情况 - 匹配C++代码中的失败处理逻辑
            if not success:
                # 增加所有采样点的bad_scores
                for j in range(len(indices)):
                    bad_scores[indices[j]] += 1

                failed_number += 1

                if failed_number > 20:
                    # 如果失败次数大于10，则认为当前求解池质量太差，全部solve_pool_ids索引的观测标记为外点
                    for j in range(num_min_score_ids):
                        bad_scores[solve_pool_ids[j]] += 1
                    flag_update_pool = True
                continue
            else:
                # 成功则重置失败计数器
                failed_number = 0
                flag_update_pool = True
                # 计算所有点与当前模型的残差
                residuals = self.compute_residuals(data1, data2, model)
                
                # 增加所有残差大于gnc_sigma的点的bad_scores
                for j in range(num_matches):
                    if residuals[j] > 2*self.gnc_sigma:
                        bad_scores[j] += 1
            
            # 计算解池中的内点数量和内点比例
            solve_pool_inliers = 0
            tmp_inliers = []
            
            for idx in solve_pool_ids:
                if residuals[idx] <= self.inlier_threshold:
                    solve_pool_inliers += 1
                    tmp_inliers.append(idx)

            # 计算解池中的内点比例w
            w = solve_pool_inliers / len(solve_pool_ids) if len(solve_pool_ids) > 0 else 0
            
            # 更新所有匹配点的bad_scores - 对应C++代码中的外点分数更新
            for j in range(num_matches):
                if residuals[j] > 2 * self.inlier_threshold:
                    bad_scores[j] += 1  # 对每个外点，分数累加1

            # #计算求解池中的残差（根据solve_pool_ids索引）， 不求全部的残差
            # # 提取求解池中的数据
            # data1_pool = data1[solve_pool_ids]
            # data2_pool = data2[solve_pool_ids]
            
            # # 计算求解池中的残差
            # residuals = self.compute_residuals(data1_pool, data2_pool, model)

            # # 计算解池中的内点数量和内点比例
            # solve_pool_inliers = 0
            # tmp_inliers = []

            # for i in range(len(residuals)):
            #     if residuals[i] <= self.inlier_threshold:
            #         solve_pool_inliers += 1
            #         tmp_inliers.append(solve_pool_ids[i])
                    
            # # 计算解池中的内点比例w
            # w = solve_pool_inliers / len(solve_pool_ids) if len(solve_pool_ids) > 0 else 0
                    
            # 使用当前内点数作为模型评价标准
            num_inliers = solve_pool_inliers
            
            # 如果当前模型的内点数更多，则更新最佳模型
            if num_inliers > self.best_inlier_count:
                # 临时保存当前的内点数量和索引
                self.best_inlier_count = num_inliers
                self.best_inliers = tmp_inliers.copy()  # 保存当前内点索引列表
                
                # 使用当前找到的所有内点重新估计模型 - 这与C++实现一致
                if len(tmp_inliers) >= self.sample_size:
                    try:
                        # 从原始数据中提取内点数据
                        data1_inliers = np.array([data1[idx] for idx in tmp_inliers])
                        data2_inliers = np.array([data2[idx] for idx in tmp_inliers])
                        
                        # 保存当前GNC最大迭代次数设置
                        original_gnc_max_iter = self.gnc_max_iter
                        
                        # 设置更大的迭代次数用于内部模型重新估计
                        self.gnc_max_iter = 20
                        
                        # 使用GNC-IRLS重新估计模型（使用更多迭代次数）
                        success, refined_model, _ = self.gnc_irls(data1_inliers, data2_inliers)
                        
                        # 还原原始GNC最大迭代次数设置
                        self.gnc_max_iter = original_gnc_max_iter
                        
                        if success:
                            # 使用精确估计的模型
                            self.best_model = refined_model
                        else:
                            # 如果重新估计失败，则保留原始模型
                            self.best_model = model
                    except Exception as e:
                        print(f"重新估计最佳模型时出错: {e}")
                        self.best_model = model  # 回退到原始模型
                else:
                    # 内点不足，使用当前模型
                    self.best_model = model
                
                # 计算γ值 (外点比例)
                gamma = 1.0 - w
                
                # 更新k值 (所需迭代次数) - 使用与C++相同的方法
                tau = 0.99  # 置信度
                n_s = self.sample_size  # 采样大小
                kappa = 40  # 异常容忍度 (C++中使用30，但配置参数使用40)
                n = num_min_score_ids  # 解池大小
                
                # 使用与C++完全相同的计算方法，但传入正确的min_solve_points
                new_k = calculateRANSACIterations(tau, gamma, n_s, kappa, n, n_min=self.min_solve_points)
                if np.isfinite(new_k) and new_k > 0:
                    k = min(max_iterations, int(new_k))
                
                print(f"内点数={num_inliers}, gamma={gamma}, Nmax迭代次数={new_k}")
                
            # 检查是否达到最大迭代次数
            if iterations >= max_iterations:
                break
                
            iterations += 1
            
        # 使用最佳模型重新计算所有点的残差
        if self.best_model is not None:
            # 使用最佳模型计算最终残差
            final_residuals = self.compute_residuals(data1, data2, self.best_model)
            
            # 重新确定最终内点集
            self.best_inliers = []
            for i in range(num_matches):
                if final_residuals[i] < 2*self.inlier_threshold:
                    self.best_inliers.append(i)
                    
            self.best_inlier_count = len(self.best_inliers)
            
            # 使用最终确定的所有内点再次应用GNC-IRLS优化模型
            if len(self.best_inliers) >= self.sample_size:
                try:
                    # 从原始数据中提取最终内点
                    data1_final_inliers = np.array([data1[idx] for idx in self.best_inliers])
                    data2_final_inliers = np.array([data2[idx] for idx in self.best_inliers])
                    
                    # 保存当前GNC最大迭代次数设置
                    original_gnc_max_iter = self.gnc_max_iter
                    
                    # 设置更大的迭代次数用于最终优化
                    self.gnc_max_iter = 20
                    
                    # 对所有内点应用GNC-IRLS进行最终优化
                    success, final_refined_model, _ = self.gnc_irls(data1_final_inliers, data2_final_inliers)
                    
                    # 还原原始GNC最大迭代次数设置
                    self.gnc_max_iter = original_gnc_max_iter
                    
                    if success:
                        print(f"使用全部{len(self.best_inliers)}个内点进行最终模型优化 (20次迭代)")
                        # 更新为优化后的最终模型
                        self.best_model = final_refined_model
                except Exception as e:
                    print(f"最终模型优化时出错: {e}")
                    # 保留现有的最佳模型，不做改变
            
            print(f"RAPIDS算法完成: 找到{self.best_inlier_count}个内点，共迭代{iterations}次")
            
            return self.best_model, self.best_inliers
        else:
            print("未能找到有效模型")
            return None, []