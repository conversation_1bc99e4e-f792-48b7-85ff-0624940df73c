# 确保必要的导入
import numpy as np
from time import time
import cv2  # 用于OpenCV相关操作
import matplotlib.pyplot as plt  # 用于可视化
try:
    from copy import deepcopy
except ImportError:
    def deepcopy(x):
        return x.copy() if hasattr(x, 'copy') else x

try:
    import pymagsac
except ImportError:
    print("警告: pymagsac模块未找到，请确保已正确安装")

# 辅助函数：格式化打印numpy数组
def format_array(arr, max_items=5):
    """以可读方式打印数组，仅显示前max_items个元素"""
    arr = np.asarray(arr)
    if arr.size <= max_items:
        return str(arr)
    else:
        if arr.ndim == 1:
            return str(arr[:max_items]) + f" ... (共{arr.size}个元素)"
        else:
            return str(arr[:max_items]) + f" ... (形状: {arr.shape})"

def get_probabilities(tentatives):
    probabilities = []
    # Since the correspondences are assumed to be ordered by their SNN ratio a priori,
    # we just assign a probability according to their order.
    for i in range(len(tentatives)):
        probabilities.append(1.0 - i / len(tentatives))
    return probabilities
    
def verify_cv2_ess(kps1, kps2, tentatives, K1, K2, h1, w1, h2, w2):
    src_pts = np.float32([ kps1[m.queryIdx].pt for m in tentatives ]).reshape(-1,2)
    dst_pts = np.float32([ kps2[m.trainIdx].pt for m in tentatives ]).reshape(-1,2)
    
    # Normalize the threshold
    threshold = 0.75
    avgDiagonal = (K1[0][0] + K1[1][1] + K2[0][0] + K2[1][1]) / 4; 
    normalizedThreshold = threshold / avgDiagonal
            
    # Normalize the point coordinates
    normalizedSourcePoints = cv2.undistortPoints(np.expand_dims(src_pts, axis=1), cameraMatrix=K1, distCoeffs=None)
    normalizedDestinationPoints = cv2.undistortPoints(np.expand_dims(dst_pts, axis=1), cameraMatrix=K2, distCoeffs=None)
    
    # Estimate the essential matrix from the normalized coordinates
    # using the normalized threshold.
    E, mask = cv2.findEssentialMat(normalizedSourcePoints, 
                                   normalizedDestinationPoints, 
                                   focal=1.0, 
                                   pp=(0., 0.),
                                   method=cv2.RANSAC, 
                                   prob=0.99,
                                   threshold=normalizedThreshold)

    print (deepcopy(mask).astype(np.float32).sum(), 'inliers found')
    return E, mask

def verify_pymagsac_ess(kps1, kps2, tentatives, K1, K2, use_magsac_plus_plus, h1, w1, h2, w2, sampler_id):
    correspondences = np.float32([ (kps1[m.queryIdx].pt + kps2[m.trainIdx].pt) for m in tentatives ]).reshape(-1,4)
    probabilities = []
    
    # NG-RANSAC and AR-Sampler require an inlier probability to be provided for each point.
    # Since deep learning-based prediction is not applied here, we calculate the probabilities
    # from the SNN ratio ranks.  
    if sampler_id == 3 or sampler_id == 4:
        probabilities = get_probabilities(tentatives)

    # 打印每个参数的详细信息
    print("\n" + "="*80)
    print("【pymagsac.findEssentialMatrix参数详细信息】".center(80))
    print("="*80)
    
    # 对应点信息
    print(f"1. 对应点(correspondences)信息:")
    print(f"   - 形状: {np.ascontiguousarray(correspondences).shape}")
    print(f"   - 数据类型: {np.ascontiguousarray(correspondences).dtype}")
    print(f"   - 前5个点(x1,y1,x2,y2格式): ")
    for i, corr in enumerate(correspondences[:5]):
        print(f"     点{i+1}: ({corr[0]:.2f}, {corr[1]:.2f}) -> ({corr[2]:.2f}, {corr[3]:.2f})")
    
    # 相机内参矩阵
    print("\n2. 相机内参矩阵:")
    print(f"   - K1(相机1内参):\n{np.ascontiguousarray(K1)}")
    print(f"   - K2(相机2内参):\n{np.ascontiguousarray(K2)}")
    
    # 图像尺寸信息
    print("\n3. 图像尺寸信息:")
    print(f"   - 图像1: 宽={w1}, 高={h1}")
    print(f"   - 图像2: 宽={w2}, 高={h2}")
    
    # 概率信息
    print("\n4. 概率(probabilities)信息:")
    print(f"   - 数量: {len(probabilities)}")
    if len(probabilities) > 0:
        print(f"   - 前5个值: {format_array(probabilities[:5])}")
        print(f"   - 最大值: {max(probabilities):.4f}, 最小值: {min(probabilities):.4f}, 平均值: {sum(probabilities)/len(probabilities):.4f}")
    else:
        print("   - 未提供概率值")
    
    # 其他参数
    print("\n5. 其他参数:")
    sampler_names = {
        0: "RANSAC",
        1: "PROSAC", 
        2: "P-NAPSAC",
        3: "NG-RANSAC",
        4: "AR-Sampler"
    }
    sampler_name = sampler_names.get(sampler_id, f"未知采样器(ID={sampler_id})")
    print(f"   - 采样器(sampler): {sampler_id} ({sampler_name})")
    print(f"   - 使用MAGSAC++(use_magsac_plus_plus): {use_magsac_plus_plus}")
    print(f"   - sigma阈值(sigma_th): 1.5")
    
    print("="*80)

    # 调用pymagsac.findEssentialMatrix函数
    print("\n正在调用pymagsac.findEssentialMatrix()...")
    E, mask = pymagsac.findEssentialMatrix(
        np.ascontiguousarray(correspondences), 
        np.ascontiguousarray(K1), 
        np.ascontiguousarray(K2),
        w1, h1, w2, h2,
        probabilities = probabilities,
        sampler = sampler_id,
        use_magsac_plus_plus = use_magsac_plus_plus,
        sigma_th = 1.5)
    
    # 结果信息
    inlier_count = deepcopy(mask).astype(np.float32).sum()
    inlier_ratio = inlier_count / len(mask) if len(mask) > 0 else 0
    print("\n【结果信息】")
    print(f"- 发现内点数量: {inlier_count} (占比: {inlier_ratio:.2%})")
    print(f"- Essential矩阵形状: {E.shape}")
    print(f"- Essential矩阵:\n{E}")
    print("="*80 + "\n")
    
    return E, mask

t=time()
cv2_E, gc_cv2_mask = verify_cv2_ess(kps1, kps2, tentatives, K1, K2, 
    img1.shape[0], img1.shape[1], img2.shape[0], img2.shape[1])
print (time()-t, 'sec cv2')

t=time()
magpp_E, magpp_E_mask = verify_pymagsac_ess(kps1, kps2, tentatives, K1, K2, True, 
    img1.shape[0], img1.shape[1], img2.shape[0], img2.shape[1], 4)
print (time()-t, 'sec magsac++')

t=time()
mag_E, mag_E_mask = verify_pymagsac_ess(kps1, kps2, tentatives, K1, K2, False, 
    img1.shape[0], img1.shape[1], img2.shape[0], img2.shape[1], 4)
print (time()-t, 'sec magsac')

draw_matches(kps1, kps2, tentatives, img1, img2, gc_cv2_mask)
draw_matches(kps1, kps2, tentatives, img1, img2, magpp_E_mask)
draw_matches(kps1, kps2, tentatives, img1, img2, mag_E_mask)

# 绘制匹配点函数
def draw_matches(kps1, kps2, tentatives, img1, img2, mask=None):
    """
    绘制两张图像之间的匹配点
    
    参数:
        kps1, kps2: 两幅图像的关键点
        tentatives: 匹配关系
        img1, img2: 原始图像
        mask: 内点掩码，None表示所有点都显示
    """
    # 创建匹配图像
    if mask is not None:
        # 只绘制内点（标记为1的点）
        good_matches = [match for i, match in enumerate(tentatives) if mask[i] > 0]
        print(f"绘制 {len(good_matches)} 个内点 (共 {len(tentatives)} 个匹配点)")
        img_matches = cv2.drawMatches(img1, kps1, img2, kps2, good_matches, None,
                                     matchColor=(0, 255, 0),  # 绿色
                                     singlePointColor=(255, 0, 0),  # 红色
                                     flags=cv2.DrawMatchesFlags_NOT_DRAW_SINGLE_POINTS)
    else:
        # 绘制所有匹配点
        img_matches = cv2.drawMatches(img1, kps1, img2, kps2, tentatives, None)
    
    # 显示结果
    plt.figure(figsize=(15, 10))
    plt.imshow(cv2.cvtColor(img_matches, cv2.COLOR_BGR2RGB))
    plt.title(f"匹配点 - {len(tentatives)} 对点")
    plt.axis('off')
    plt.tight_layout()
    plt.show()
  