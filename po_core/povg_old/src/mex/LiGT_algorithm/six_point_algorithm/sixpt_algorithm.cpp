// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <fstream>
#include <iomanip>
#include <cstdio>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <glog/logging.h>

#include "LiGT_algorithm.hpp"
#include "povg_timer.hpp"
#include "relative_residuals.hpp"

#include <Eigen/Dense>
#include <unsupported/Eigen/Polynomials>
#include <complex>

#include <algorithm>
#include <numeric>
#include <vector>
#include <cmath>
#include <random>
#include <limits>

#include "Spectra/SymEigsShiftSolver.h"
#include "Spectra/MatOp/SparseSymShiftSolve.h"

#include "coefficients.hpp"

// 全局变量
#define min_test_ids 20
bool echo_on = false;
bool use_med = true;
int prefix_3dpts = 0;
int compute_mode = 0;
vector<int> min_six_ids;
vector<int> max_six_ids;
vector<int> min_matches_ids;
vector<int> max_matches_ids;
vector<double> process_times;
vector<string> time_labels;
double pose_check_cost = 0;

enum TwoViewSolveMethod
{
    EIGEN_SOLVE,
    PIZARRO,
    PIZARRO2,
    PIZARRO_REAL,
    LIRP_6PTS_ESOLS,
    EIGEN_SOLVE_MAIN,
    GNC_6PTS_LIGT,
    GNCRANSAC_6PTS_LIGT,
    SHUFFLE_RANSAC,
    GNC_6PTS_BA,
    GNC_6PTS_OPENGV,
    GNC_6PTS_PPO,
    GNC_6PTS_LIGT_DIRECT,
    GNC_6PTS_COPLANAR,
    GNC_6PTS_LIGT_INVD,
    GNC_HOMO_8PTS_LIGT,
    MMS_TLS_LiRP
};

DEFINE_string(point_match_files,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/matches/Matches_10Image_1000Points_10.00Dt_0.txt",
              "file of matching point pairs to use.");

DEFINE_int32(sigma_mode,
             0,
             "file of matching point pairs to use.");

DEFINE_int32(max_gncransac_niter,
             50,
             "file of matching point pairs to use.");
DEFINE_int32(gnc_samplesize,
             30,
             "file of matching point pairs to use.");
DEFINE_double(inlier_threhold,
              5 * 1e-3,
              "file of matching point pairs to use.");

DEFINE_bool(computeMode,
            true,
            " ");

DEFINE_string(identify_mode,
              "PPO",
              "opengv_c;PPO;PPOG");

DEFINE_string(gnc_residual_func,
              "residual_PPO",
              "opengv_c;PPO;PPOG");

DEFINE_string(gnc_dist_func,
              "residual_PPO",
              "opengv_c;PPO;PPOG");

DEFINE_string(mode,
              "/home/<USER>/code/code_upgrade/code_git_update/Simulative_TestID_591/Simulative_Test_of_Noise/Output_ForZhang_2Circles_10Image_1000Points_10.00Dt_0.00noise/matches/Matches_10Image_1000Points_10.00Dt_0.txt",
              "file of matching point pairs to use.");

DEFINE_string(eigenvec_sols_file,
              "/home/<USER>/eigen_sols_test.txt",
              "file of 3 smallest eigenvectors for A matrix");

DEFINE_string(bearing_vector_file,
              "/home/<USER>/bearing_vector_file.txt",
              "file of bearing vectors");

DEFINE_string(weights_file,
              "",
              "file of bearing vectors");

DEFINE_string(output_file,
              "/home/<USER>/Evecoutput_file.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(inliers_file,
              "/home/<USER>/inliers_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(time_file,
              "time_output_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(idm_time_file,
              "time_idm_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(solve_mode,
              "eigen_solve",
              "0: polynomial eigenvalue solve method");

DEFINE_bool(debug_info,
            false,
            "0: polynomial eigenvalue solve method");

DEFINE_bool(med_or_sum,
            true,
            "false: med");

using namespace std;
using namespace Spectra;

#define PI 3.141592653589793

TwoViewSolveMethod stringToEnum(const std::string &method)
{
    static const std::map<std::string, TwoViewSolveMethod> stringToEnumMap = {
        {"gncransac_6pts_LiGT", GNCRANSAC_6PTS_LIGT},
        {"shuffle_ransac", SHUFFLE_RANSAC},
        {"girls_6pts_LiGT", GNC_6PTS_LIGT},
        {"eigen_solve_main", EIGEN_SOLVE_MAIN}};

    auto it = stringToEnumMap.find(method);
    if (it != stringToEnumMap.end())
    {
        return it->second;
    }
    else
    {
        throw std::runtime_error("Invalid method string provided. Supported: gncransac_6pts_LiGT, shuffle_ransac, girls_6pts_LiGT");
    }
}

using namespace POVG;

// 双视图估计函数指针
typedef Pose (*Solve_Relative_Main)(const BearingVector &v1,
                                    const BearingVector &v2,
                                    const VectorXd *ptr_weigths);

// 初值估计函数指针
typedef Pose (*Eval_Rt_func)(const BearingVector &v1,
                             const BearingVector &v2,
                             const VectorXd *ptr_weigths);

// 残差估计函数指针
typedef VectorXd (*Eval_Residual_func)(const BearingVector &v1,
                                       const BearingVector &v2,
                                       const Pose &pose,
                                       const VectorXd *ptr_weigths);

Eval_Residual_func getResidualFunction(const std::string &func_name)
{
    static std::unordered_map<std::string, Eval_Residual_func> func_map = {
        {"residual_coplanar", &POVG::residual_coplanar},
        {"residual_sampson", &POVG::residual_sampson},
        {"residual_Kneip", &POVG::residual_Kneip},
        {"residual_BA", &POVG::residual_BA},
        {"residual_opengv", static_cast<Eval_Residual_func>(&POVG::residual_opengv)},
        {"residual_PPO", &POVG::residual_PPO},
        {"residual_PPOG", &POVG::residual_PPOG},
        {"residual_PPO_invd", &POVG::residual_PPO_invd},
        {"residual_PPO_bva_invd", &POVG::residual_PPO_bva_invd},
        {"residual_PPO_bvc_invd", &POVG::residual_PPO_bvc_invd},
        {"residual_LiGT_direct", &POVG::residual_LiGT_direct},
        {"residual_LiGT_d3", &POVG::residual_LiGT_d3},
        {"residual_LiGT", &POVG::residual_LiGT},
        {"residual_LiRT", &POVG::residual_LiRT}};

    auto it = func_map.find(func_name);
    if (it != func_map.end())
    {
        return it->second;
    }
    else
    {
        throw std::runtime_error("Unknown residual function: " + func_name);
    }
}

BearingVector *ptr_verified_v1 = nullptr;
BearingVector *ptr_verified_v2 = nullptr;
BearingVector min_six_obs1(3, min_test_ids);
BearingVector min_six_obs2(3, min_test_ids);

void load_pose(const std::string &pose_file, Pose &pose);
Eigen::Matrix3d crossMatrix(const Eigen::Vector3d &x);

double findMedian(VectorXd a);
double findMedian(vector<double> a);

Pose LiRP_process(const BearingVector &v1,
                  const BearingVector &v2,
                  vector<int> &final_inliers);

struct ResidualIndex
{
    double residual;
    int index;

    ResidualIndex(double r, int i) : residual(r), index(i) {}

    bool operator<(const ResidualIndex &other) const
    {
        return residual < other.residual;
    }
};

class GNCInfo
{
public:
    GNCInfo(Eval_Rt_func eval_Rt_func,
            Eval_Residual_func eval_residual_func)
    {

        eval_Rt_func_ = eval_Rt_func;
        eval_residual_func_ = eval_residual_func;
        eval_dist_func_ = eval_residual_func;
        rng_dist_.reset(new std::uniform_int_distribution<>(0, std::numeric_limits<int>::max()));
        // Create a random number generator object
        rng_alg_.seed(static_cast<unsigned>(time(0)) + static_cast<unsigned>(clock()));

        rng_gen_.reset(new std::function<int()>(std::bind(*rng_dist_, rng_alg_)));
    };

    GNCInfo(Eval_Rt_func eval_Rt_func,
            Eval_Residual_func eval_residual_func,
            Eval_Residual_func eval_dist_func)
    {

        eval_Rt_func_ = eval_Rt_func;
        eval_residual_func_ = eval_residual_func;
        eval_dist_func_ = eval_dist_func;

        rng_dist_.reset(new std::uniform_int_distribution<>(0, std::numeric_limits<int>::max()));
        // Create a random number generator object
        rng_alg_.seed(static_cast<unsigned>(time(0)) + static_cast<unsigned>(clock()));

        rng_gen_.reset(new std::function<int()>(std::bind(*rng_dist_, rng_alg_)));
    }

    double FastScaleParameter(const VectorXd &residuals, double iter_ratio);
    double ScaleParameter(const VectorXd &residuals, double iter_ratio);
    double ScaleParameter(const VectorXd &residuals);
    bool GNC_IRLS(const BearingVector &v1, const BearingVector &v2);
    bool GNC_IRLS2(const BearingVector &v1, const BearingVector &v2); // 每次迭代中记录残差最小的pose，并输出内点
    bool GNC_IRLSp(const BearingVector &v1, const BearingVector &v2);
    VectorXd gncWeightsUpdate(VectorXd weights, double mu, const VectorXd &residuals, double barc2);
    VectorXd M_gncWeightsUpdate(VectorXd weights, double mu, const VectorXd &residuals, double barc2);

    bool GNC_Ransac(const BearingVector &v1, const BearingVector &v2, double threshold);
    bool RansacOpenGV(const BearingVector &v1, const BearingVector &v2, double threshold);
    bool GNC_RansacOpenGV(const BearingVector &v1, const BearingVector &v2, double threshold);

    bool GNC_LMS(const BearingVector &v1, const BearingVector &v2, double threshold);

    bool GNC_Ransac_adaptive(const BearingVector &v1, const BearingVector &v2, double threshold);

    bool GNC_RansacScheme(const BearingVector &v1, const BearingVector &v2, double threshold);

    // 洗牌：大异常可用，Shuffle_Ransac（但需要精细调整流程）
    void Shuffle_Ransac(const BearingVector &v1, const BearingVector &v2, double threshold, int num_min_score_ids);
    vector<int> ShuffleAlgorithm(const BearingVector &v1, const BearingVector &v2, double threshold);
    bool multiple_GNC_v1(const BearingVector &v1,
                         const BearingVector &v2,
                         Pose &pose_est,
                         vector<int> &final_inliers);

    void multiple_GNC_v3(const BearingVector &v1,
                         const BearingVector &v2,
                         Pose &pose_est,
                         vector<int> &final_inliers);

    void multiple_GNC_v2(const BearingVector &v1,
                         const BearingVector &v2,
                         Pose &pose_est,
                         vector<int> &final_inliers);
    void multi_GNC_IRLS_v1(const BearingVector &v1,
                           const BearingVector &v2,
                           Pose &pose_est,
                           vector<int> &final_inliers);

    void multi_GNC_IRLS_v3(const BearingVector &v1,
                           const BearingVector &v2,
                           Pose &pose_est,
                           vector<int> &final_inliers);

    void SetSigmaMode(const int &mode, const double &threshold = 5 * 1e-4)
    {
        sigma_mode_ = mode;
        sigma_threshold_ = threshold;
    }

    void SetTLSMaxIter(const int &max_iter)
    {
        options_.max_iter = max_iter;
    }

    void SetTLSBounder(const double &bounder)
    {
        options_.bounder = bounder;
    }

    void SetSampleSizes(const int &sample_size)
    {
        options_.sampleSize = sample_size;
    }

    void getSamples(int &iterations, std::vector<int> &samples);
    int getSampleSize() const;

    void drawIndexSample(std::vector<int> &sample);
    int rnd();
    bool isSampleGood(const std::vector<int> &sample) const;

    void setIndices(const std::vector<int> &indices);
    void selectWithinDistance(
        const Pose &model_coefficients,
        const double threshold,
        std::vector<int> &inliers);

public:
    // opengv ransac

    /** the indices for the currently best hypothesis */
    std::vector<int> model_;

    int iterations_ = 0;
    /** The maximum number of times we try to extract a valid set of samples */
    int max_sample_checks_ = 10;

    /** The indices of the samples we are using for solving the entire
     *  problem. These are not the indices for generating a hypothesis, but
     *  all indices for model verification
     */
    std::shared_ptr<std::vector<int>> indices_;

    /** A shuffled version of the indices used for random sample drawing */
    std::vector<int> shuffled_indices_;

    /** \brief std-based random number generator algorithm. */
    std::mt19937 rng_alg_;

    /** \brief std-based random number generator distribution. */
    std::shared_ptr<std::uniform_int_distribution<>> rng_dist_;

    /** \brief std-based random number generator. */
    std::shared_ptr<std::function<int()>> rng_gen_;

    // input vars
    Eval_Rt_func eval_Rt_func_ = nullptr;
    Eval_Residual_func eval_residual_func_ = nullptr;
    Eval_Residual_func eval_dist_func_ = nullptr;

    // gnc-ransac params
    Pose bestModel_;
    int bestInlierNum_ = 0;
    double bestInlierValue_ = 1e5;
    double best_sigma_ = 0.0;
    double best_mean_sigma_ = 0.0;
    vector<int> bestInlier_indices_; // 初始化最佳内点集合为空

    // gnc-irls params
    Pose Rt_est_;
    bool is_pose_worked_ = true;

    int iter_;
    bool is_reweighted = true;

    VectorXd weights_;
    VectorXi inlier_indices_;
    double gnc_indicator_ = 0;

    int sigma_mode_ = 0;
    double sigma_threshold_;
    double gnc_sigma_ = 0;

    double gnc_mean_sigma_ = 0;
    double gnc_max_residual_ = 0;
    double gnc_med_sigma_ = 0;

    int num_buffer_ids_ = 20;

    // options
    struct options
    {
        int max_iter = 10;
        int majorize = 1;

        double stopTh = 1e-10;
        int superlinear = 1;
        int sampleSize = 20;
        double bounder = 3;
    } options_;
};

bool MSTLS_process(const BearingVector &v1,
                   const BearingVector &v2,
                   const double &bounder,
                   const double &sigma,
                   GNCInfo &gnc_info,
                   vector<int> &final_inliers);

bool MSTLS_process(const BearingVector &v1,
                   const BearingVector &v2,
                   GNCInfo &gnc_info,
                   vector<int> &final_inliers);

bool IRLS_process(const BearingVector &v1,
                  const BearingVector &v2,
                  const double &bounder,
                  const double &sigma,
                  GNCInfo &gnc_info,
                  vector<int> &final_inliers);
// 检查文件是否为空
bool isFileEmpty(const std::string &filename)
{
    std::ifstream file(filename);
    return file.peek() == std::ifstream::traits_type::eof();
}

void updateTimerAndLabel(std::vector<double> &timer, std::vector<std::string> &label, const std::string &newLabel, double newTimer)
{
    auto it = std::find(label.begin(), label.end(), newLabel); // 查找新标签是否存在

    if (it != label.end())
    {
        // 如果找到了标签，更新对应的 timer 值
        size_t index = std::distance(label.begin(), it);
        timer[index] += newTimer;
    }
    else
    {
        // 如果没有找到标签，添加新的标签和 timer 值
        label.emplace_back(newLabel);
        timer.emplace_back(newTimer);
    }
}

void appendTimeToCSV(const std::vector<double> &timer, const std::vector<std::string> &label, const std::string &filename)
{
    // 检查文件是否为空或不存在
    bool emptyFile = isFileEmpty(filename);

    // 以追加模式打开文件
    std::ofstream file(filename, std::ios::app);

    // 检查文件是否成功打开
    if (!file.is_open())
    {
        std::cerr << "Failed to open file: " << filename << std::endl;
        return;
    }

    // 如果文件为空，写入列标签
    if (emptyFile)
    {
        for (size_t i = 0; i < label.size(); ++i)
        {
            file << label[i];
            if (i < label.size() - 1)
                file << ","; // 在最后一个标签后不添加逗号
        }
        file << "\n";
    }

    // 写入数据
    for (size_t i = 0; i < timer.size(); ++i)
    {
        file << timer[i];
        if (i < timer.size() - 1)
            file << ","; // 在最后一个值后不添加逗号
    }
    file << "\n"; // 每行数据后添加换行符

    file.close(); // 关闭文件
}

// 函数定义：合并两个 vector<int> 并去除重复元素
std::vector<int> mergeVectorsUnique(const std::vector<int> &vec1, const std::vector<int> &vec2)
{
    std::set<int> uniqueElements; // 使用 set 来自动去除重复元素

    // 将第一个向量的元素添加到 set 中
    uniqueElements.insert(vec1.begin(), vec1.end());

    // 将第二个向量的元素添加到 set 中
    uniqueElements.insert(vec2.begin(), vec2.end());

    // 将 set 转换回 vector
    std::vector<int> result(uniqueElements.begin(), uniqueElements.end());

    return result; // 返回去除重复元素后的向量
}

// 函数定义
void removeMatchingElements(std::vector<int> &final_inliers, const std::vector<int> &min_matches_ids)
{
    // 首先对 min_matches_ids 进行排序以便使用二分查找
    std::vector<int> sorted_min_matches = min_matches_ids; // 创建 min_matches_ids 的副本并排序
    std::sort(sorted_min_matches.begin(), sorted_min_matches.end());

    std::vector<int> new_inliers; // 存储未出现在 min_matches_ids 中的元素
    for (int elem : final_inliers)
    {
        // 使用二分查找检查 elem 是否不在 sorted_min_matches 中
        if (!std::binary_search(sorted_min_matches.begin(), sorted_min_matches.end(), elem))
        {
            new_inliers.push_back(elem); // 如果未找到，添加到 new_inliers
        }
    }

    // 用新的 inliers 替换原来的 final_inliers
    final_inliers = new_inliers;
}

long double logComb(int n, int k)
{
    if (k > n)
        return -std::numeric_limits<long double>::infinity();
    if (k == 0 || k == n)
        return 0.0L;
    if (k > n - k)
        k = n - k; // Improve efficiency
    long double logResult = 0.0L;
    for (int i = 1; i <= k; ++i)
    {
        logResult += std::log(n - k + i) - std::log(i);
    }

    //    cout<<"[logComb("<<n<<","<<k<<")]:"<<logResult<<endl;
    return logResult;
}

long double hypergeometricCDF(int k, int n_s, int n_o, int n)
{
    long double logCDF = -std::numeric_limits<long double>::infinity(); // Initialize with negative infinity
    for (int i = 0; i <= k; ++i)
    {
        long double logTerm = logComb(n_o, i) + logComb(n - n_o, n_s - i) - logComb(n, n_s);
        logCDF = logCDF == -std::numeric_limits<long double>::infinity() ? logTerm : std::log1p(std::exp(logTerm - logCDF)) + logCDF;
    }
    //    cout<<"[hypergeometricCDF]:"<<std::exp(std::min(logCDF, 20.0L))<<endl;
    return std::exp(std::min(logCDF, 20.0L)); // Convert log CDF back to regular space, capping to prevent overflow
}

long double calculateRANSACIterations(double tau, double gamma, int n_s, double kappa, int n)
{
    int n_o = int(n * gamma); // Number of outliers
    int n_min = 6;            // Minimum number of samples needed for model fitting
    double kappa_max = (1.0 - double(n_min) / n_s) * 100.0 - 10.0;
    //    int k = int(std::min(kappa_max, kappa) * n_s / 100.0); // Calculate k
    int k = int(kappa_max * n_s / 100.0); // Calculate k

    long double cdf = hypergeometricCDF(k, n_s, n_o, n);

    //    cout<<"[calculateRANSACIterations]: cdf="<<cdf<<endl;
    // Ensure cdf is within (0,1) to prevent log(0)
    cdf = std::max(std::min(cdf, 1.0L - 1e-10L), 1e-10L);
    //    cout<<"[calculateRANSACIterations]: cdf = std::max(std::min(cdf, 1.0L - 1e-10L), 1e-10L); ="<<cdf<<endl;

    //    long double logN = std::log(1 - tau) / std::log(1 - cdf); // Corrected to use std::log for numerical stability
    long double logN = std::log(1 - tau) / std::log(1 - cdf * 0.5); // Corrected to use std::log for numerical stability
    return logN;                                                    // Cap the iteration number to prevent overflow
}

bool write_ply_file(const char *filename, const Matrix<double, 3, Dynamic> &points3D)
{
    ofstream outFile(filename);

    if (!outFile.is_open())
    {
        cerr << "Unable to open file: " << filename << endl;
        return false;
    }

    // Writing the PLY header
    outFile << "ply\n";
    outFile << "format ascii 1.0\n";
    outFile << "element vertex " << points3D.cols() << "\n";
    outFile << "property float x\n";
    outFile << "property float y\n";
    outFile << "property float z\n";
    outFile << "end_header\n";

    // Writing the point data
    for (int i = 0; i < points3D.cols(); ++i)
    {
        outFile << points3D(0, i) << " " << points3D(1, i) << " " << points3D(2, i) << "\n";
    }

    outFile.close();
    cout << "PLY file written: " << filename << endl;
    return true;
}

bool write_ply_file(const char *filename, const Matrix<double, 3, Dynamic> &points3D, const Pose &pose)
{
    ofstream outFile(filename);

    if (!outFile.is_open())
    {
        cerr << "Unable to open file: " << filename << endl;
        return false;
    }

    // Writing the PLY header
    outFile << "ply\n";
    outFile << "format ascii 1.0\n";
    outFile << "element vertex " << points3D.cols() + 2 << "\n"; // +1 for the camera position
    outFile << "property float x\n";
    outFile << "property float y\n";
    outFile << "property float z\n";
    outFile << "property uchar red\n"; // Adding color properties
    outFile << "property uchar green\n";
    outFile << "property uchar blue\n";
    outFile << "end_header\n";

    // Writing the point data
    for (int i = 0; i < points3D.cols(); ++i)
    {
        // Assuming default color for points (e.g., white: 255, 255, 255)
        outFile << points3D(0, i) << " " << points3D(1, i) << " " << points3D(2, i) << " 0 0 0\n";
    }

    // Writing the camera position in red color
    Vector3d camera_position = pose.translations;
    outFile << camera_position(0) << " " << camera_position(1) << " " << camera_position(2) << " 255 0 0\n";
    outFile << 0 << " " << 0 << " " << 0 << " 0 255 0\n";

    outFile.close();
    cout << "PLY file written: " << filename << endl;
    return true;
}

Pose RT_Check(const MatrixXd &A,
              const BearingVector &v1,
              const BearingVector &v2,
              const vector<Matrix3d> &R_sols,
              const vector<Vector3d> &t_sols,
              double &ratio);

Pose RT_Check2(const MatrixXd &A,
               const BearingVector &v1,
               const BearingVector &v2,
               const vector<Matrix3d> &R_sols,
               const vector<Vector3d> &t_sols,
               const VectorXd *ptr_weigths,
               double &ratio);

void essential2RT(const Matrix3d &E,
                  vector<Matrix3d> &R,
                  vector<Vector3d> &t);

Matrix<double, 3, 4> processEvec(const MatrixXd &Evec_sols,
                                 const BearingVector &v1,
                                 const BearingVector &v2,
                                 const MatrixXd &A, const VectorXd *ptr_weigths);

Matrix<double, 3, 4> processEvec2(const MatrixXd &Evec_sols,
                                  const BearingVector &v1,
                                  const BearingVector &v2,
                                  const MatrixXd &A,
                                  const VectorXd *ptr_weigths,
                                  const Eval_Residual_func &residual_func);
Matrix<double, 3, 4> processEvec3(const MatrixXd &Evec_sols,
                                  const BearingVector &v1,
                                  const BearingVector &v2,
                                  const MatrixXd &A,
                                  const VectorXd *ptr_weigths);

bool MSTLS_reprocess(const BearingVector &v1,
                     const BearingVector &v2,
                     GNCInfo &gnc_info,
                     vector<int> &final_inliers);

auto findRoots(const Eigen::VectorXd &coefficients);

std::vector<int> generateRandomSample(int num_matches, int sampleSize);

void compute_Gcoefficients2(const EigenSols &eigen_sols, Matrix<double, 10, 10> &G);
void compute_Gcoefficients(const EigenSols &eigen_sols, Matrix<double, 9, 10> &G);

void compute_Bcoefficients(const EigenSols &eigen_sols, Matrix<double, 9, 10> &B);

void compute_Dcoefficients(const EigenSols &eigen_sols, Matrix<double, 1, 4> &D);

void solve_6pts(const EigenSols &eigen_sols,
                const Matrix<double, 9, 10, ColMajor> &B,
                MatrixXd &Evec_real,
                const bool &flags_keep_real = false);

void solve_6pts_x(const EigenSols &eigen_sols,
                  const Matrix<double, 9, 10, ColMajor> &B,
                  MatrixXd &Evec_real,
                  const bool &flags_keep_real = false);

void solve_pizarro_6pts(const EigenSols &eigen_sols,
                        const MatrixXd &B,
                        MatrixXd &Evec_real,
                        const bool &flags_keep_real = false);

void regular_eigen_sols(EigenSols &eigen_sols)
{
    for (int i = 0; i < eigen_sols.rows(); ++i)
    {
        if (eigen_sols(i, 0) < 0)
            eigen_sols.row(i) = -eigen_sols.row(i);
    }
}

std::vector<int> findMinKIndices(const Eigen::VectorXi &scores, int k)
{
    // 统计每个分数的出现次数
    std::unordered_map<int, std::vector<int>> scoreIndicesMap;
    for (int i = 0; i < scores.size(); ++i)
    {
        scoreIndicesMap[scores[i]].push_back(i);
    }

    // 将分数和索引对按分数排序
    std::vector<std::pair<int, std::vector<int>>> scoreIndices(scoreIndicesMap.begin(), scoreIndicesMap.end());
    std::sort(scoreIndices.begin(), scoreIndices.end(), [](const auto &a, const auto &b)
              { return a.first < b.first; });

    std::vector<int> selectedIndices;
    int accumulated = 0;

    for (const auto &[score, indices] : scoreIndices)
    {
        if (accumulated + indices.size() < k)
        {
            // 如果当前分数段的索引加上之前的累积没有超过k，全部加入
            selectedIndices.insert(selectedIndices.end(), indices.begin(), indices.end());
            accumulated += indices.size();
        }
        else
        {
            // 否则，在当前分数段进行随机抽样
            int needed = k - accumulated;
            std::vector<int> shuffledIndices = indices;
            std::shuffle(shuffledIndices.begin(), shuffledIndices.end(), std::mt19937(std::random_device()()));
            selectedIndices.insert(selectedIndices.end(), shuffledIndices.begin(), shuffledIndices.begin() + needed);
            break; // 已达到k个索引，结束循环
        }
    }

    return selectedIndices;
}

inline void show_inlier_info(const vector<int> &inliers, const string &note)
{
    cout << note << "inlier ids: ";
    for (int i = 0; i < inliers.size(); ++i)
    {
        cout << inliers[i] << " ";
    }
    cout << endl;
}

inline void obtain_inlier_matches(const BearingVector &v1,
                                  const BearingVector &v2,
                                  vector<int> &final_inliers,
                                  BearingVector &v1_inliers,
                                  BearingVector &v2_inliers)
{

    int num_inlier_indices = final_inliers.size();

    v1_inliers.setZero(3, num_inlier_indices);
    v2_inliers.setZero(3, num_inlier_indices);

    for (int i = 0; i < num_inlier_indices; i++)
    {
        int id_inlier = final_inliers[i];
        v1_inliers.col(i) = v1.col(id_inlier);
        v2_inliers.col(i) = v2.col(id_inlier);
    }
}

void check_quality(const BearingVector &v1,
                   const BearingVector &v2,
                   const double &threshold,
                   Pose &pose_est,
                   vector<int> &final_inliers)
{
    ///=======================
    int num_inlier_indices = final_inliers.size();

    if (num_inlier_indices < 6)
        return;

    BearingVector v1_inliers;
    BearingVector v2_inliers;
    v1_inliers.setZero(3, num_inlier_indices);
    v2_inliers.setZero(3, num_inlier_indices);

    for (int i = 0; i < final_inliers.size(); i++)
    {
        int id_inlier = final_inliers[i];
        v1_inliers.col(i) = v1.col(id_inlier);
        v2_inliers.col(i) = v2.col(id_inlier);
    }

    VectorXd residuals = residual_PPO(v1_inliers, v2_inliers, pose_est);

    double med_residuals = findMedian(residuals);
    cout << "med_residuals: " << med_residuals << ", mean_residuals:=" << residuals.sum() / residuals.rows() << endl;
    if (med_residuals > threshold)
    {
        pose_est.rotations.setConstant(std::numeric_limits<double>::quiet_NaN());
        pose_est.translations.setConstant(std::numeric_limits<double>::quiet_NaN());
        final_inliers.clear();
    }
}

//// Reshape a vector into a matrix
Eigen::MatrixXd reshape(const Eigen::VectorXd &v, int rows, int cols)
{
    return Eigen::Map<const Eigen::MatrixXd>(v.data(), rows, cols);
}

// Function to compute the Kronecker product of two matrices
Eigen::MatrixXd kroneckerProduct(const Eigen::MatrixXd &A, const Eigen::MatrixXd &B)
{
    Eigen::MatrixXd Kronecker(A.rows() * B.rows(), A.cols() * B.cols());
    for (int i = 0; i < A.rows(); ++i)
    {
        for (int j = 0; j < A.cols(); ++j)
        {
            Kronecker.block(i * B.rows(), j * B.cols(), B.rows(), B.cols()) = A(i, j) * B;
        }
    }
    return Kronecker;
}

// Function H2Rt_Ma translated from MATLAB to C++
std::vector<Eigen::MatrixXd> H2Rt_Ma(const Eigen::MatrixXd &G)
{
    std::vector<Eigen::MatrixXd> Rt(4, Eigen::MatrixXd(3, 4));
    Eigen::MatrixXd G_copy = G;
    if (G.determinant() < 0)
    {
        G_copy = -G;
    }

    Eigen::JacobiSVD<Eigen::MatrixXd> svd_G(G_copy, Eigen::ComputeFullV | Eigen::ComputeFullU);
    Eigen::VectorXd w = svd_G.singularValues();

    double d2 = w(1);
    G_copy /= d2; // Normalize G

    Eigen::MatrixXd AtA = G_copy.transpose() * G_copy;
    Eigen::JacobiSVD<Eigen::MatrixXd> svd_AtA(AtA, Eigen::ComputeFullV);
    Eigen::VectorXd sigmaSqua = svd_AtA.singularValues();
    Eigen::MatrixXd V = svd_AtA.matrixV();

    // Ensure V belongs to SO(3)
    if (V.determinant() < 0)
    {
        V = -V;
    }

    Eigen::Vector3d u1 = (sqrt(1 - sigmaSqua(2)) * V.col(0) + sqrt(sigmaSqua(0) - 1) * V.col(2)) / sqrt(sigmaSqua(0) - sigmaSqua(2));
    Eigen::Vector3d u2 = (sqrt(1 - sigmaSqua(2)) * V.col(0) - sqrt(sigmaSqua(0) - 1) * V.col(2)) / sqrt(sigmaSqua(0) - sigmaSqua(2));

    Eigen::Matrix3d U1, W1, U2, W2;
    U1 << V.col(1), u1, crossMatrix(V.col(1)) * u1;
    W1 << G_copy * V.col(1), G_copy * u1, crossMatrix(G_copy * V.col(1)) * G_copy * u1;
    U2 << V.col(1), u2, crossMatrix(V.col(1)) * u2;
    W2 << G_copy * V.col(1), G_copy * u2, crossMatrix(G_copy * V.col(1)) * G_copy * u2;

    Rt[0] << W1 * U1, (G_copy - W1 * U1) * U1.col(2);
    Rt[1] << W2 * U2, (G_copy - W2 * U2) * U2.col(2);
    Rt[2] << Rt[0].block(0, 0, 3, 3), -Rt[0].col(3);
    Rt[3] << Rt[1].block(0, 0, 3, 3), -Rt[1].col(3);

    return Rt;
}

Pose solve_Homo8pts_main(const BearingVector &v1,
                         const BearingVector &v2,
                         const VectorXd *ptr_weigths)
{

    cout << " ======== homo8pts method ========" << endl;
    int num_matches = v1.cols();

    Matrix<double, Dynamic, 9> A(num_matches, 9);

    for (int i = 0; i < num_matches; ++i)
    {
        Matrix<double, 1, 9> tmp_A;
        if (ptr_weigths != nullptr)
        {

            A.row(i) = (*ptr_weigths)(i)*kroneckerProduct(v2.col(i).transpose(), v1.col(i).transpose());
        }
        else
        {
            A.row(i) = kroneckerProduct(v2.col(i).transpose(), v1.col(i).transpose());
        }
    }

    // 构造 A^TA
    MatrixXd ATA = A.transpose() * A;
    //    cout << "ATA:"<<endl<<ATA<<endl;
    // 将 A^TA 转换为稀疏矩阵
    SparseMatrix<double> sparseATA = ATA.sparseView();

    // 构造矩阵操作对象
    SparseSymShiftSolve<double, Eigen::Upper> op(sparseATA);

    // 构造特征值求解器，寻找最接近0的特征值
    SymEigsShiftSolver<double, LARGEST_MAGN, SparseSymShiftSolve<double, Eigen::Upper>> eigs(&op, 3, 8, -1e-10);

    // 初始化求解器并计算
    eigs.init();
    eigs.compute();

    // 获取特征值和特征向量
    VectorXd evalues = eigs.eigenvalues();
    MatrixXd V = eigs.eigenvectors();

    MatrixXd Evec = V.col(2);

    Matrix<double, 3, 4> Out = processEvec3(Evec, v1, v2, A, ptr_weigths);

    //    //======================================================
    Eigen::MatrixXd Q1 = reshape(V.col(0), 3, 3);
    Eigen::MatrixXd Q2 = reshape(V.col(1), 3, 3);
    Eigen::MatrixXd Q3 = reshape(V.col(2), 3, 3);

    Eigen::MatrixXd I = Eigen::MatrixXd::Identity(3, 3);
    Eigen::MatrixXd A1, A2, Kmn;
    Eigen::MatrixXd A_1, A_2, A_3;

    // Compute A_1
    A1 = kroneckerProduct(I, Q1.transpose());
    A2 = kroneckerProduct(Q1.transpose(), I);
    Kmn = Eigen::MatrixXd::Zero(I.size(), I.size());
    for (int j = 0; j < 3; ++j)
    {
        Kmn += kroneckerProduct(kroneckerProduct(I.col(j).transpose(), I), I.col(j));
    }
    A2 *= Kmn;
    A_1 = A1 + A2;

    // Compute A_2
    A1 = kroneckerProduct(I, Q2.transpose());
    A2 = kroneckerProduct(Q2.transpose(), I);

    A2 *= Kmn;
    A_1 = A1 + A2;

    // Compute A_3
    A1 = kroneckerProduct(I, Q3.transpose());
    A2 = kroneckerProduct(Q3.transpose(), I);

    A2 *= Kmn;
    A_1 = A1 + A2;

    //

    Eigen::MatrixXd D = Eigen::MatrixXd(A_1.rows() * 3, A_1.cols());
    D << A_1, A_2, A_3;

    // Perform SVD on D
    Eigen::JacobiSVD<Eigen::MatrixXd> svd(D, Eigen::ComputeThinU | Eigen::ComputeThinV);
    Eigen::VectorXd vecH = svd.matrixV().col(svd.matrixV().cols() - 1);
    Eigen::MatrixXd H = reshape(vecH, 3, 3);

    std::vector<Eigen::MatrixXd> Rt;

    Rt = H2Rt_Ma(H);

    Rt.emplace_back(Out);

    VectorXd selected_costs(Rt.size(), 1);
    for (int i = 0; i < Rt.size(); ++i)
    {
        Pose tmp_pose;
        tmp_pose.rotations = Rt[i].block<3, 3>(0, 0);
        tmp_pose.translations = Rt[i].col(3);
        VectorXd residuals = residual_PPO(v1, v2, tmp_pose, nullptr);
        selected_costs(i) = findMedian(residuals);
    }

    int min_id;
    selected_costs.minCoeff(&min_id);

    Pose pose_est;
    pose_est.rotations = Rt[min_id].block(0, 0, 3, 3);
    pose_est.translations = Rt[min_id].rightCols(1);

    return pose_est;
}

Pose solve_6pts_main(const BearingVector &v1,
                     const BearingVector &v2,
                     const VectorXd *ptr_weigths)
{

    POVG::Timer solve_timer;
    int num_matches = v1.cols();

    Matrix<double, Dynamic, 9> A(num_matches, 9);

    if (FLAGS_debug_info)
    {
        if (ptr_weigths != nullptr)
        {
            LOG(INFO) << " !!!!! weight mode !!!!!";
        }
    }

    if (FLAGS_debug_info)
    {
        LOG(INFO) << " ======== 20 data of bearing vector 1 ========";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << v1.leftCols(20);

        LOG(INFO) << " ======== 20 data of bearing vector 2 ========";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << v2.leftCols(20);
    }

    for (int i = 0; i < num_matches; ++i)
    {
        Matrix<double, 1, 9> tmp_A;
        if (ptr_weigths != nullptr)
        {

            A.row(i) = (*ptr_weigths)(i)*kroneckerProduct(v2.col(i).transpose(), v1.col(i).transpose());
        }
        else
        {
            A.row(i) = kroneckerProduct(v2.col(i).transpose(), v1.col(i).transpose());
        }
    }

    if (FLAGS_debug_info)
    {
        LOG(INFO) << " ======== 20 rows of A matrix ========";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << A.topRows(20);
    }

    // 构造 A^TA ( 仅用前面一部分A来构建ATA，剩余的观测作为检测）
    int num_est_pts = max(6, num_matches);

    // 只提取前num_est_pts行的A，并且逐步计算ATA
    //    Matrix<double,9,9> ATA = Matrix<double,9,9>::Zero(); // 初始化ATA为零矩阵

    //    for (int i = 0; i < num_est_pts; ++i) {
    //        ATA += A.row(i).transpose() * A.row(i);
    //    }

    Matrix<double, 9, 9> ATA = A.transpose() * A;

    // 将 A^TA 转换为稀疏矩阵
    SparseMatrix<double> sparseATA = ATA.sparseView();

    // 构造矩阵操作对象
    SparseSymShiftSolve<double, Eigen::Upper> op(sparseATA);

    // 构造特征值求解器，寻找最接近0的特征值
    SymEigsShiftSolver<double, LARGEST_MAGN, SparseSymShiftSolve<double, Eigen::Upper>> eigs(&op, 3, 8, -1e-10);

    // 初始化求解器并计算
    eigs.init();
    eigs.compute();

    // 获取特征值和特征向量
    VectorXd evalues = eigs.eigenvalues();
    MatrixXd V = eigs.eigenvectors();

    // 输出结果
    //    std::cout << "Eigenvalues: " << evalues.transpose() << std::endl;

    //    cout << endl <<" ======== singular values of A matrix ========";
    //    cout << endl << setprecision(16) << sigma.transpose();

    EigenSols eigen_sols = V.rightCols(3).transpose(); //[1 2 3]

    MatrixXd Evec_real;

    Matrix<double, 9, 10> coeff_matrix;

    compute_Bcoefficients(eigen_sols, coeff_matrix);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "======= Solved Sols ===============";
        LOG(INFO) << endl;
    }

    solve_6pts(eigen_sols, coeff_matrix, Evec_real);

    // 构建最终解向量
    MatrixXd Evec = MatrixXd(Evec_real.rows(), Evec_real.cols() + 3);
    Evec << Evec_real, V.rightCols(3);

    solve_timer.Duration();
    solve_timer.WriteTime(FLAGS_time_file);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "Solved Evec1 from sols.[1 2 3]:";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << Evec_real;

        LOG(INFO) << endl;
    }

    Matrix<double, 3, 4> Out;
    // cout<<"==========  solve check ["<<FLAGS_identify_mode.c_str()<<"] ========="<<endl;
    if (strcmp(FLAGS_identify_mode.c_str(), "opengv_c") == 0)
    {
        //          cout<<"==========  solve check [residual_opengv] ========="<<endl;
        Out = processEvec2(Evec, v1, v2, A, ptr_weigths, &residual_opengv);
    }
    else if (strcmp(FLAGS_identify_mode.c_str(), "PPObvcInvd") == 0)
    {
        //            cout<<"==========  solve check [residual_PPO_bvc_invd] ========="<<endl;
        Out = processEvec2(Evec, v1, v2, A, ptr_weigths, &residual_PPO_bvc_invd);
    }
    else if (strcmp(FLAGS_identify_mode.c_str(), "PPO") == 0)
    {
        //            cout<<"==========  solve check [residual_PPO] ========="<<endl;
        Out = processEvec2(Evec, v1, v2, A, ptr_weigths, &residual_PPO);
    }
    else if (strcmp(FLAGS_identify_mode.c_str(), "PPOG") == 0)
    {
        //                cout<<"==========  solve check [residual_PPOG] ========="<<endl;
        Out = processEvec2(Evec, v1, v2, A, ptr_weigths, &residual_PPOG);
    }
    else if (strcmp(FLAGS_identify_mode.c_str(), "sampson") == 0)
    {
        //        cout<<"==========  solve check [residual_sampson] ========="<<endl;
        Out = processEvec2(Evec, v1, v2, A, ptr_weigths, &residual_sampson);
    }

    //    cout<<"==========  solve check [residual_LiRT] ========="<<endl;
    //    Out = processEvec2(Evec,v1,v2,A,ptr_weigths,&residual_LiRT);

    //    cout<<"==========  solve check [residual_LiGT] ========="<<endl;
    //    Out = processEvec2(Evec,v1,v2,A,ptr_weigths,&residual_LiGT);

    Pose pose_est;
    pose_est.rotations = Out.block(0, 0, 3, 3);
    pose_est.translations = Out.rightCols(1);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "Solved rotation :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << pose_est.rotations;

        LOG(INFO) << "Solved translations :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << pose_est.translations;
        LOG(INFO) << endl;
    }

    return pose_est;
}

Pose solve_6pts_main_xyz(const BearingVector &v1,
                         const BearingVector &v2,
                         const VectorXd *ptr_weigths)
{

    int num_matches = v1.cols();

    Matrix<double, Dynamic, 9> A(num_matches, 9);

    if (FLAGS_debug_info)
    {
        if (ptr_weigths != nullptr)
        {
            LOG(INFO) << " !!!!! weight mode !!!!!";
        }
    }

    if (FLAGS_debug_info)
    {
        LOG(INFO) << " ======== 20 data of bearing vector 1 ========";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << v1.leftCols(20);

        LOG(INFO) << " ======== 20 data of bearing vector 2 ========";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << v2.leftCols(20);
    }

    for (int i = 0; i < num_matches; ++i)
    {
        Matrix<double, 1, 9> tmp_A;
        if (ptr_weigths != nullptr)
        {

            A.row(i) = (*ptr_weigths)(i)*kroneckerProduct(v2.col(i).transpose(), v1.col(i).transpose());
        }
        else
        {
            A.row(i) = kroneckerProduct(v2.col(i).transpose(), v1.col(i).transpose());
        }
    }

    if (FLAGS_debug_info)
    {
        LOG(INFO) << " ======== 20 rows of A matrix ========";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << A.topRows(20);
    }

    BDCSVD<MatrixXd> svd(A, ComputeThinU | ComputeThinV);
    auto V = svd.matrixV();
    auto sigma = svd.singularValues();

    if (FLAGS_debug_info)
    {
        LOG(INFO) << " ======== singular values of A matrix ========";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << sigma;
    }

    EigenSols eigen_sols1 = V.rightCols(3).transpose(); //[1 2 3]

    EigenSols eigen_sols2; //[3 2 1]
    eigen_sols2.row(0) = V.col(8).transpose();
    eigen_sols2.row(1) = V.col(7).transpose();
    eigen_sols2.row(2) = V.col(6).transpose();

    EigenSols eigen_sols3; //[3 1 2]
    eigen_sols3.row(0) = V.col(8).transpose();
    eigen_sols3.row(1) = V.col(6).transpose();
    eigen_sols3.row(2) = V.col(7).transpose();

    regular_eigen_sols(eigen_sols1);
    regular_eigen_sols(eigen_sols2);
    regular_eigen_sols(eigen_sols3);

    MatrixXd Evec_real_1;
    MatrixXd Evec_real_2;
    MatrixXd Evec_real_3;

    Matrix<double, 9, 10> coeff_matrix1;
    Matrix<double, 9, 10> coeff_matrix2;
    Matrix<double, 9, 10> coeff_matrix3;

    compute_Bcoefficients(eigen_sols1, coeff_matrix1);
    compute_Bcoefficients(eigen_sols2, coeff_matrix2);
    compute_Bcoefficients(eigen_sols3, coeff_matrix3);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "======= Solved Sols 1 ===============";
        LOG(INFO) << endl;
    }

    solve_6pts_x(eigen_sols1, coeff_matrix1, Evec_real_1);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "======= Solved Sols 2 ===============";
        LOG(INFO) << endl;
    }

    solve_6pts_x(eigen_sols2, coeff_matrix2, Evec_real_2);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "======= Solved Sols 3 ===============";
        LOG(INFO) << endl;
    }
    solve_6pts_x(eigen_sols3, coeff_matrix3, Evec_real_3);

    // Assuming Evec1, Evec2, and V are already defined and have the same number of rows

    MatrixXd Evec = MatrixXd(Evec_real_1.rows(), Evec_real_1.cols() + Evec_real_2.cols() + Evec_real_3.cols() + 3);
    Evec << Evec_real_1, Evec_real_2, Evec_real_3, V.rightCols(3);

    //    MatrixXd Evec = MatrixXd(Evec_real_1.rows(), Evec_real_1.cols() + 3);
    //    Evec << Evec_real_1, V.rightCols(3);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "Solved Evec1 from sols.[1 2 3]:";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << Evec_real_1;

        //        LOG(INFO) << "Solved Evec2 from sols.[3 2 1]:";
        //        LOG(INFO) << endl << setfill(' ') << setprecision(16) << Evec_real_2;

        //        LOG(INFO) << "Solved Evec3 from sols.[3 1 2]:";
        //        LOG(INFO) << endl << setfill(' ') << setprecision(16) << Evec_real_3;

        LOG(INFO) << endl;
    }

    Matrix<double, 3, 4> Out = processEvec(Evec, v1, v2, A, ptr_weigths);

    Pose pose_est;
    pose_est.rotations = Out.block(0, 0, 3, 3);
    pose_est.translations = Out.rightCols(1);

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;
        LOG(INFO) << "Solved rotation :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << pose_est.rotations;

        LOG(INFO) << "Solved translations :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << pose_est.translations;
        LOG(INFO) << endl;
    }

    return pose_est;
}

double TwoViewLiGT(const Vector3d &X1,
                   const Vector3d &X2,
                   const Matrix3d &R,
                   const Vector3d &t)
{
    Vector3d tmp1 = X1.cross(R * X2);
    Vector3d tmp2 = (R * X2).cross(X1);
    Vector3d tmp3 = X1.cross(t);
    double tmp4 = tmp2.norm() * tmp2.norm();

    double tmp5 = tmp4 * t.norm() + tmp2.dot(tmp3) * (R * X2).norm();

    Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;

    return cost_vec.dot(cost_vec);
}

Eigen::Matrix3d crossMatrix(const Eigen::Vector3d &x)
{
    Eigen::Matrix3d y;

    y << 0, -x(2), x(1),
        x(2), 0, -x(0),
        -x(1), x(0), 0;

    return y;
}

std::vector<int> generateRandomSample(int num_matches, int sampleSize)
{
    if (sampleSize > num_matches)
    {
        //        throw std::runtime_error("Sample size cannot be larger than total number of matches.");
        cout << "sample size cannot be larger than total number of matches." << endl;
    }

    // 创建一个序列，范围是0到num_matches-1
    std::vector<int> indices(num_matches);
    std::iota(indices.begin(), indices.end(), 0); // 使用iota填充序列

    std::random_device rd;
    std::mt19937 g(rd());

    std::shuffle(indices.begin(), indices.end(), g); // 使用C++11的shuffle进行洗牌

    return std::vector<int>(indices.begin(), indices.begin() + sampleSize);
}

bool GNCInfo::GNC_Ransac(const BearingVector &v1,
                         const BearingVector &v2,
                         double threshold)
{

    int num_matches = v1.cols();
    if (options_.sampleSize == 0)
    {
        options_.sampleSize = num_matches;
    }

    bestInlierNum_ = -1; // 初始化最佳内点数为-1
    Matrix4d bestModel;  // 初始化最佳模型为空

    long double k = 10.0; // 初始化k值为1.0
    double probability = 0.9999;
    int maxIterations = FLAGS_max_gncransac_niter;

    int i = 0;

    // 随机数生成
    std::default_random_engine generator;
    std::uniform_int_distribution<int> distribution(0, num_matches - 1);

    VectorXd dists;

    // 样本预定义
    std::vector<int> indices(options_.sampleSize);

    Matrix<double, 3, Dynamic> v1_sample(3, options_.sampleSize);
    Matrix<double, 3, Dynamic> v2_sample(3, options_.sampleSize);
    int failed_number = 0;

    Pose tmp_pose;
    vector<int> tmp_inliers;

    bestInlierValue_ = 1e8;
    gnc_med_sigma_ = 1e3;
    bestInlier_indices_.clear();
    //    cout<<"i="<<i<<",k="<<k<<endl<<"gnc_med_sigma_="<<gnc_med_sigma_<<",=threshold"<<threshold<<endl;
    //    cout<<(i < k || gnc_med_sigma_>threshold)<<endl;
    double ini_thre = 1e-4;
    while (i < k)
    {

        if (num_matches < options_.sampleSize)
        {
            cout << "no sufficient data in gnc-ransac" << endl;
            return false;
        }

        indices = generateRandomSample(num_matches, options_.sampleSize);

        //        cout<<"random indices:"<<endl;
        for (int j = 0; j < options_.sampleSize; ++j)
        {
            v1_sample.col(j) = v1.col(indices[j]);
            v2_sample.col(j) = v2.col(indices[j]);
            //            cout << indices[j] << ", ";
        }

        // 使用GNC-eval_Rsol_func函数计算模型参数
        //        Rt_est_ = eval_Rt_func_(v1_sample,v2_sample,nullptr);
        //        bool success = true;
        bool success = GNC_IRLS(v1_sample, v2_sample); // Assuming GNC_IRLS returns a boolean indicating success

        //        bool success = multiple_GNC_v1(v1_sample, v2_sample,tmp_pose,tmp_inliers); // Assuming GNC_IRLS returns a boolean indicating success

        //        double outlier_ratio = 0;
        //        double sample_outliers = 0;
        //        for ( int j=0;j<weights_.size();++j){
        //            if (weights_(j)<0.5)
        //            sample_outliers++;
        //        }

        //        outlier_ratio = sample_outliers/weights_.size();

        //        if (!success || outlier_ratio > 0.5) {
        //            continue;
        //        }

        if (!success)
        {
            failed_number++;
            continue;
        }
        else
        {
            failed_number = 0;
        }

        if (failed_number > 10)
            break;
        // 计算数据中所有点与当前模型的差距（例如距离）
        // note: must not be weighted!
        dists = eval_residual_func_(v1, v2, Rt_est_, nullptr);

        // 找出距离在阈值以内的点（即内点）

        gnc_med_sigma_ = findMedian(dists);
        int numInliers = 0;
        for (int j = 0; j < num_matches; ++j)
        {
            double thre = 1.4826 * gnc_med_sigma_;
            if (dists(j) <= min(thre * 2, threshold))
            {
                numInliers++;
            }
        }

        //        cout<<"numInliers="<<numInliers<<",bestInlierNum_="<<bestInlierNum_<<endl<<",gnc_med_sigma_="<<gnc_med_sigma_<<",bestInlierValue_="<<bestInlierValue_<<endl;
        // 如果当前模型的内点数更多，则更新最佳模型和最佳内点
        //        if (numInliers > bestInlierNum_ && gnc_med_sigma_< bestInlierValue_){
        if (numInliers > bestInlierNum_)
        {
            best_mean_sigma_ = 0;
            bestInlierValue_ = gnc_med_sigma_;
            bestInlier_indices_.clear();
            for (int j = 0; j < num_matches; ++j)
            {
                double thre = 1.4826 * gnc_med_sigma_;
                if (dists(j) <= thre * 2)
                {
                    best_mean_sigma_ += dists(j);
                    bestInlier_indices_.emplace_back(j);
                }
            }
            best_mean_sigma_ = best_mean_sigma_ / bestInlier_indices_.size();

            //            show_inlier_info(bestInlier_indices_,"[in GNC-RANSAC]:");

            bestInlierNum_ = numInliers;
            bestModel_ = Rt_est_;
            best_sigma_ = gnc_med_sigma_;

            // 更新k值
            double w = static_cast<double>(numInliers) / num_matches;
            double p_no_outliers = 1.0 - pow(w, 6);
            p_no_outliers = max(numeric_limits<double>::epsilon(), p_no_outliers);
            p_no_outliers = min(1.0 - numeric_limits<double>::epsilon(), p_no_outliers);
            k = log(1 - probability) / log(p_no_outliers);

            //            double tau = 0.9999; // 置信水平
            //            double gamma = 1-w; // 外点比例
            //            int n_s = options_.sampleSize; // 采样大小
            //            double kappa = 50; // 异常容忍度
            //            double kappa_max = (1-6/num_matches)*100-10; // 最大异常容忍度
            //            kappa = min(kappa,kappa_max);
            //            int n = num_matches; // 总样本数
            //                        cout <<"numInliers="<<numInliers<<", tau = "<<tau<<", gamma = "<<gamma<<", n_s = "<< n_s<<",kappa = "<< kappa <<",n="<<n<<endl;
            //            k = calculateRANSACIterations(tau, gamma, n_s, kappa, n);
            cout << "numInliers=" << numInliers << ", gamma = " << 1 - w << endl;
            cout << "mean_err = " << best_mean_sigma_ << ", med_err = " << gnc_med_sigma_ << endl;

            if (k >= 0)
            { // 检查是否有错误
                std::cout << "RANSAC所需的迭代次数: " << k << std::endl;
            }
        }

        if (i > maxIterations)
        {
            break;
        }

        i++;
    }

    cout << "ransac iterr:=" << i << endl;
    if (i < k && i > maxIterations)
    {
        //        bestInlier_indices_.clear();
        return false;
    }

    //    dists = eval_residual_func_(v1, v2, bestModel_, nullptr);

    //    bestInlier_indices_.clear();

    //    for (int i = 0; i < num_matches; ++i) {
    //        if (dists(i) <= best_sigma_ * 3) {
    //            bestInlier_indices_.emplace_back(i);
    //        }
    //    }

    cout << "[GNC-ransac] bestInlierNum_=" << bestInlierNum_
         << ", bestInlier_indices_.size()=" << bestInlier_indices_.size()
         << endl;

    return true;
}

void InitialInliers(const int &num_obs,
                    vector<int> &final_inliers)
{
    final_inliers.clear();
    for (auto id = 0; id < num_obs; ++id)
    {
        final_inliers.emplace_back(id);
    }
}
int GNCInfo::rnd()
{
    return ((*rng_gen_)());
}

void GNCInfo::drawIndexSample(std::vector<int> &sample)
{
    size_t sample_size = sample.size();
    size_t index_size = shuffled_indices_.size();
    for (unsigned int i = 0; i < sample_size; ++i)
    {
        // The 1/(RAND_MAX+1.0) trick is when the random numbers are not uniformly
        // distributed and for small modulo elements, that does not matter
        // (and nowadays, random number generators are good)
        // std::swap (shuffled_indices_[i], shuffled_indices_[i + (rand () % (index_size - i))]);
        std::swap(
            shuffled_indices_[i],
            shuffled_indices_[i + (rnd() % (index_size - i))]);
    }
    std::copy(
        shuffled_indices_.begin(),
        shuffled_indices_.begin() + sample_size,
        sample.begin());
}

int GNCInfo::getSampleSize() const
{
    {
        int sampleSize = 0;

        sampleSize = FLAGS_gnc_samplesize;

        return sampleSize;
    }
}
void GNCInfo::setIndices(const std::vector<int> &indices)
{
    indices_.reset(new std::vector<int>(indices));
    shuffled_indices_ = *indices_;
}

bool GNCInfo::isSampleGood(const std::vector<int> &sample) const
{
    // Default implementation
    return true;
}

void GNCInfo::getSamples(int &iterations, std::vector<int> &samples)
{
    // We're assuming that indices_ have already been set in the constructor
    if (indices_->size() < (size_t)getSampleSize())
    {
        fprintf(stderr,
                "[sm::SampleConsensusModel::getSamples] Can not select %zu unique points out of %zu!\n",
                (size_t)getSampleSize(), indices_->size());
        // one of these will make it stop :)
        samples.clear();
        iterations = std::numeric_limits<int>::max();
        return;
    }

    // Get a second point which is different than the first
    samples.resize(getSampleSize());

    for (int iter = 0; iter < max_sample_checks_; ++iter)
    {
        drawIndexSample(samples);

        // If it's a good sample, stop here
        if (isSampleGood(samples))
            return;
    }
    fprintf(stdout,
            "[sm::SampleConsensusModel::getSamples] WARNING: Could not select %d sample points in %d iterations!\n",
            getSampleSize(), max_sample_checks_);
    samples.clear();
}

bool GNCInfo::RansacOpenGV(const BearingVector &v1,
                           const BearingVector &v2,
                           double threshold)
{

    int num_matches = v1.cols();
    int max_iterations_ = 2000;

    std::vector<int> final_inliers;
    // 给 indices_ 赋值
    for (int i = 0; i < num_matches; ++i)
    {
        final_inliers.push_back(i);
    }

    setIndices(final_inliers);

    iterations_ = 0;
    bestInlierNum_ = -INT_MAX;
    double k = 1.0;

    std::vector<int> selection;
    Pose model_coefficients;

    int n_inliers_count = 0;
    unsigned skipped_count = 0;

    // supress infinite loops by just allowing 10 x maximum allowed iterations for
    // invalid model parameters!
    const unsigned max_skip = max_iterations_ * 10;

    BearingVector v1_sample;
    BearingVector v2_sample;

    VectorXd dists(num_matches);

    double probability = 0.99;

    while (iterations_ < k && skipped_count < max_skip)
    {

        // Get X samples which satisfy the model criteria
        //        getSamples( iterations_, selection );

        selection = generateRandomSample(num_matches, getSampleSize());

        if (selection.empty())
        {
            fprintf(stderr,
                    "[sm::RandomSampleConsensus::computeModel] No samples could be selected!\n");
            break;
        }

        obtain_inlier_matches(v1, v2, selection, v1_sample, v2_sample);

        // Search for inliers in the point cloud for the current plane model M
        model_coefficients = eval_Rt_func_(v1_sample, v2_sample, nullptr);

        //////////////////////////////////

        // 计算数据中所有点与当前模型的差距（例如距离）
        // note: must not be weighted!
        dists = eval_residual_func_(v1, v2, model_coefficients, nullptr);

        //         gnc_med_sigma_ = findMedian(dists);
        // 找出距离在阈值以内的点（即内点）

        int numInliers = 0;
        for (int j = 0; j < num_matches; ++j)
        {
            if (dists(j) < threshold)
            {
                numInliers++;
            }
        }

        //        cout<<"numInliers="<<numInliers<<",bestInlierNum_="<<bestInlierNum_<<endl<<",gnc_med_sigma_="<<gnc_med_sigma_<<",bestInlierValue_="<<bestInlierValue_<<endl;

        // Better match ?
        if (numInliers > bestInlierNum_)
        {
            bestInlierNum_ = numInliers;

            // Save the current model/inlier/coefficients selection as being the best so far
            model_ = selection;
            bestModel_ = model_coefficients;

            // 更新k值
            double w = static_cast<double>(bestInlierNum_) / static_cast<double>(num_matches);
            double p_no_outliers = 1.0 - pow(w, static_cast<double>(selection.size()));

            p_no_outliers = max(numeric_limits<double>::epsilon(), p_no_outliers);
            p_no_outliers = min(1.0 - numeric_limits<double>::epsilon(), p_no_outliers);
            k = log(1 - probability) / log(p_no_outliers);
        }

        ++iterations_;

        if (iterations_ > max_iterations_)
        {
            break;
        }
    }

    std::cout << "[pomvg]:iterations_=" << iterations_ << std::endl;

    if (model_.empty())
    {
        bestInlier_indices_.clear();
        return false;
    }

    // Get the set of inliers that correspond to the best model found so far

    dists = eval_dist_func_(v1, v2, bestModel_, nullptr);

    bestInlier_indices_.clear();
    bestInlier_indices_.reserve(num_matches);
    for (size_t i = 0; i < dists.size(); ++i)
    {
        if (dists[i] < threshold)
            bestInlier_indices_.push_back(i);
    }

    cout << "[GNC-ransac] bestInlierNum_=" << bestInlierNum_
         << ", bestInlier_indices_.size()=" << bestInlier_indices_.size()
         << endl;

    return true;
}

bool GNCInfo::GNC_RansacOpenGV(const BearingVector &v1,
                               const BearingVector &v2,
                               double threshold)
{

    int num_matches = v1.cols();
    int max_iterations_ = FLAGS_max_gncransac_niter;

    std::vector<int> final_inliers;
    // 给 indices_ 赋值
    for (int i = 0; i < num_matches; ++i)
    {
        final_inliers.push_back(i);
    }

    setIndices(final_inliers);

    iterations_ = 0;
    bestInlierNum_ = -INT_MAX;
    double k = 1.0;

    std::vector<int> selection;
    Pose model_coefficients;

    int n_inliers_count = 0;
    unsigned skipped_count = 0;

    // supress infinite loops by just allowing 10 x maximum allowed iterations for
    // invalid model parameters!
    const unsigned max_skip = max_iterations_ * 2;
    //    const unsigned max_skip = max_iterations_ * 10; // opengv deafault

    BearingVector v1_sample;
    BearingVector v2_sample;

    VectorXd dists(num_matches);

    double probability = 0.99;

    double noise_bound = 5.54;

    SetSigmaMode(2);
    SetTLSBounder(noise_bound);
    SetTLSMaxIter(10);
    Timer timer;
    int continuous_skip = 0;
    int cumulative_count = 0;

    //    SetTLSMaxIter(20);//for ransac2020
    Pose continuous_pose;
    double max_gnc_indicator = -1;
    while (iterations_ < k && skipped_count < max_skip)
    {

        // Get X samples which satisfy the model criteria
        getSamples(iterations_, selection);

        //        if (num_matches < getSampleSize())
        //        selection = generateRandomSample(num_matches,getSampleSize());

        if (selection.empty())
        {
            fprintf(stderr,
                    "[sm::RandomSampleConsensus::computeModel] No samples could be selected!\n");
            break;
        }

        obtain_inlier_matches(v1, v2, selection, v1_sample, v2_sample);
        ++continuous_skip;
        //         检查 continuous_skip 是否是10的整数倍,不是则true
        bool is_not_multiple_of_10 = (continuous_skip % 5 != 0);

        if (continuous_skip > 100)
            break; // 如果累计100次都没法筛选出最大内点，可以考虑提前停止了

        if (!GNC_IRLS(v1_sample.leftCols(selection.size()),
                      v2_sample.leftCols(selection.size())) &&
            is_not_multiple_of_10)
        {

            if (gnc_indicator_ > max_gnc_indicator)
            {
                max_gnc_indicator = gnc_indicator_;
                model_coefficients = Rt_est_;
            }
            ++skipped_count;
            continue;
        }
        else
        {
            model_coefficients = Rt_est_;
            max_gnc_indicator = -1;
        }

        //        cout<<"skipped_coun   t="<<skipped_count<<endl;
        //        model_coefficients = Rt_est_;

        //        if (skipped_count > k && iterations_ > 0)    break;

        // 计算数据中所有点与当前模型的差距（例如距离）
        // note: must not be weighted!
        dists = eval_dist_func_(v1, v2, model_coefficients, nullptr);

        //         gnc_med_sigma_ = findMedian(dists);
        // 找出距离在阈值以内的点（即内点）

        int numInliers = 0;
        for (int j = 0; j < num_matches; ++j)
        {
            if (dists(j) < threshold)
            {
                numInliers++;
            }
        }

        //        cout<<"numInliers="<<numInliers<<",bestInlierNum_="<<bestInlierNum_<<", skipped_count="<<skipped_count<<endl;
        //        cout<<"numInliers="<<numInliers<<",bestInlierNum_="<<bestInlierNum_<<endl<<",gnc_med_sigma_="<<gnc_med_sigma_<<",bestInlierValue_="<<bestInlierValue_<<endl;
        //        cout<<"---------------------------------------"<<endl;
        // Better match ?

        // cout << "mean_dist=" << dists.mean() << std::endl;

        if (numInliers > bestInlierNum_)
        {

            bestInlierNum_ = numInliers;

            // Save the current model/inlier/coefficients selection as being the best so far
            model_ = selection;
            bestModel_ = model_coefficients;

            //            // 更新k值
            //            double w = static_cast<double>(bestInlierNum_) / static_cast<double> (num_matches);
            //            double p_no_outliers = 1.0 - pow(w, static_cast<double> (selection.size()));

            //            p_no_outliers = max(numeric_limits<double>::epsilon(), p_no_outliers);
            //            p_no_outliers = min(1.0 - numeric_limits<double>::epsilon(), p_no_outliers);
            //            k = log(1 - probability) / log(p_no_outliers);

            // 更新k值
            double w = static_cast<double>(bestInlierNum_) / static_cast<double>(num_matches);
            //            double p_no_outliers = 1.0 - pow(w, static_cast<double> (selection.size()));
            //            p_no_outliers = max(numeric_limits<double>::epsilon(), p_no_outliers);
            //            p_no_outliers = min(1.0 - numeric_limits<double>::epsilon(), p_no_outliers);
            //            k = log(1 - probability) / log(p_no_outliers);

            double tau = 0.99;                                           // 置信水平
            double gamma = 1.0 - w;                                      // 外点比例
            int n_s = selection.size();                                  // 采样大小
            double kappa = 50.0;                                         // 异常容忍度
            double kappa_max = (1.0 - 6.0 / num_matches) * 100.0 - 10.0; // 最大异常容忍度
            kappa = min(kappa, kappa_max);
            int n = num_matches; // 总样本数
            cout << "numInliers=" << numInliers << ", tau = " << tau << ", gamma = " << gamma << ", n_s = " << n_s << ",kappa = " << kappa << ",kappa_max = <<" << kappa_max << ",n=" << n << endl;
            k = calculateRANSACIterations(tau, gamma, n_s, kappa, n);

            if (k >= 0)
            { // 检查是否有错误
                std::cout << "RANSAC所需的迭代次数: " << k << ", skipped_count=" << skipped_count << std::endl;
            }

            //            if (continuous_skip > 200){//如果要超过100次迭代（跳过迭代skipped_count + 有效迭代 iterations_）才能找到一次合格的，说明质量太差
            //                break;
            //            }
            continuous_skip = 0; // 重置0,跳过间隔清空
                                 //            max_gnc_indicator = -1;//重置-1

            // 如果持续小于10个匹配点对
            if (bestInlierNum_ < 10)
            {
                ++cumulative_count;
                if (cumulative_count > 3)
                {
                    // 质量监管后，连续4次仍然选不出超过10个点，说明质量太差
                    break;
                }
            }
        }

        ++iterations_;

        if (iterations_ > max_iterations_)
        {
            break;
        }
    }

    cout << "[GNC-ransac] time cost = " << timer.Duration() << endl;

    std::cout << "[pomvg2 ransac]:iterations_=" << iterations_ << ", skipped_count=" << skipped_count << std::endl;

    if (model_.empty() || iterations_ == 0)
    {
        bestInlier_indices_.clear();
        bestModel_ = Rt_est_;
        return false;
    }

    // Get the set of inliers that correspond to the best model found so far

    dists = eval_dist_func_(v1, v2, bestModel_, nullptr);

    bestInlier_indices_.clear();
    bestInlier_indices_.reserve(num_matches);
    for (size_t i = 0; i < dists.size(); ++i)
    {
        if (dists[i] < threshold)
            bestInlier_indices_.push_back(i);
    }

    cout << "[GNC-ransac] bestInlierNum_=" << bestInlierNum_
         << ", bestInlier_indices_.size()=" << bestInlier_indices_.size()
         << endl;

    return true;
}
void GNCInfo::selectWithinDistance(const Pose &model_coefficients, const double threshold, std::vector<int> &inliers)
{
}

bool GNCInfo::GNC_LMS(const BearingVector &v1,
                      const BearingVector &v2,
                      double threshold)
{

    int num_matches = v1.cols();
    if (options_.sampleSize == 0)
    {
        options_.sampleSize = num_matches;
    }

    bestInlierNum_ = -1; // 初始化最佳内点数为-1
    Matrix4d bestModel;  // 初始化最佳模型为空

    long double k = 10.0; // 初始化k值为1.0
    double probability = 0.9999;
    int maxIterations = FLAGS_max_gncransac_niter;

    int i = 0;

    // 随机数生成
    std::default_random_engine generator;
    std::uniform_int_distribution<int> distribution(0, num_matches - 1);

    VectorXd dists;

    // 样本预定义
    std::vector<int> indices(options_.sampleSize);

    Matrix<double, 3, Dynamic> v1_sample(3, options_.sampleSize);
    Matrix<double, 3, Dynamic> v2_sample(3, options_.sampleSize);
    int failed_number = 0;

    Pose tmp_pose;
    vector<int> tmp_inliers;

    bestInlierValue_ = 1e8;
    gnc_med_sigma_ = 1e3;
    bestInlier_indices_.clear();
    //    cout<<"i="<<i<<",k="<<k<<endl<<"gnc_med_sigma_="<<gnc_med_sigma_<<",=threshold"<<threshold<<endl;
    //    cout<<(i < k || gnc_med_sigma_>threshold)<<endl;
    double ini_thre = 1e-4;
    while (i < k)
    {

        if (num_matches < options_.sampleSize)
        {
            cout << "no sufficient data in gnc-ransac" << endl;
            return false;
        }

        indices = generateRandomSample(num_matches, options_.sampleSize);

        //        cout<<"random indices:"<<endl;
        for (int j = 0; j < options_.sampleSize; ++j)
        {
            v1_sample.col(j) = v1.col(indices[j]);
            v2_sample.col(j) = v2.col(indices[j]);
            //            cout << indices[j] << ", ";
        }

        // 使用GNC-eval_Rsol_func函数计算模型参数
        //        Rt_est_ = eval_Rt_func_(v1_sample,v2_sample,nullptr);
        //        bool success = true;
        bool success = GNC_IRLS(v1_sample, v2_sample); // Assuming GNC_IRLS returns a boolean indicating success

        //        bool success = multiple_GNC_v1(v1_sample, v2_sample,tmp_pose,tmp_inliers); // Assuming GNC_IRLS returns a boolean indicating success

        //        double outlier_ratio = 0;
        //        double sample_outliers = 0;
        //        for ( int j=0;j<weights_.size();++j){
        //            if (weights_(j)<0.5)
        //            sample_outliers++;
        //        }

        //        outlier_ratio = sample_outliers/weights_.size();

        //        if (!success || outlier_ratio > 0.5) {
        //            continue;
        //        }

        if (!success)
        {
            failed_number++;
            continue;
        }
        else
        {
            failed_number = 0;
        }

        if (failed_number > 10)
            break;
        // 计算数据中所有点与当前模型的差距（例如距离）
        // note: must not be weighted!
        dists = eval_residual_func_(v1, v2, Rt_est_, nullptr);

        // 找出距离在阈值以内的点（即内点）
        int max_id = 0;
        (dists).maxCoeff(&max_id);
        double max_dist = dists(max_id);
        int numInliers = 0;
        for (int j = 0; j < num_matches; ++j)
        {
            double thre = 1.4826 * (1 + 5 / (num_matches - indices.size())) * gnc_med_sigma_;
            if (dists(j) <= min(thre * 2.5, threshold))
            {
                numInliers++;
            }
        }

        //        cout<<"numInliers="<<numInliers<<",bestInlierNum_="<<bestInlierNum_<<endl<<",gnc_med_sigma_="<<gnc_med_sigma_<<",bestInlierValue_="<<bestInlierValue_<<endl;
        // 如果当前模型的内点数更多，则更新最佳模型和最佳内点
        //        if (numInliers > bestInlierNum_ && gnc_med_sigma_< bestInlierValue_){
        if (numInliers > bestInlierNum_)
        {
            bestInlierValue_ = gnc_med_sigma_;
            bestInlier_indices_.clear();
            for (int j = 0; j < num_matches; ++j)
            {
                double thre = 1.4826 * (1 + 5 / (num_matches - indices.size())) * gnc_med_sigma_;
                if (dists(j) <= min(thre * 2.5, threshold))
                {
                    bestInlier_indices_.emplace_back(j);
                }
            }
            //            show_inlier_info(bestInlier_indices_,"[in GNC-RANSAC]:");

            bestInlierNum_ = numInliers;
            bestModel_ = Rt_est_;
            best_sigma_ = gnc_med_sigma_;

            // 更新k值
            double w = static_cast<double>(numInliers) / num_matches;
            //            double p_no_outliers = 1.0 - pow(w, 6);
            //            p_no_outliers = max(numeric_limits<double>::epsilon(), p_no_outliers);
            //            p_no_outliers = min(1.0 - numeric_limits<double>::epsilon(), p_no_outliers);
            //            k = log(1 - probability) / log(p_no_outliers);

            double tau = 0.9999;                                 // 置信水平
            double gamma = 1 - w;                                // 外点比例
            int n_s = options_.sampleSize;                       // 采样大小
            double kappa = 50;                                   // 异常容忍度
            double kappa_max = (1 - 6 / num_matches) * 100 - 10; // 最大异常容忍度
            kappa = min(kappa, kappa_max);
            int n = num_matches; // 总样本数
            cout << "numInliers=" << numInliers << ", tau = " << tau << ", gamma = " << gamma << ", n_s = " << n_s << ",kappa = " << kappa << ",n=" << n << endl;
            k = calculateRANSACIterations(tau, gamma, n_s, kappa, n);

            if (k >= 0)
            { // 检查是否有错误
                std::cout << "RANSAC所需的迭代次数: " << k << std::endl;
            }
        }

        if (i > maxIterations)
        {
            break;
        }

        i++;
    }

    cout << "ransac iterr:=" << i << endl;
    if (i < k && i > maxIterations)
    {
        //        bestInlier_indices_.clear();
        return false;
    }

    //    dists = eval_residual_func_(v1, v2, bestModel_, nullptr);

    //    bestInlier_indices_.clear();

    //    for (int i = 0; i < num_matches; ++i) {
    //        if (dists(i) <= best_sigma_ * 3) {
    //            bestInlier_indices_.emplace_back(i);
    //        }
    //    }

    cout << "[GNC-ransac] bestInlierNum_=" << bestInlierNum_
         << ", bestInlier_indices_.size()=" << bestInlier_indices_.size()
         << endl;

    return true;
}
bool GNCInfo::GNC_Ransac_adaptive(const BearingVector &v1,
                                  const BearingVector &v2,
                                  double threshold)
{

    int num_matches = v1.cols();
    if (options_.sampleSize == 0)
    {
        options_.sampleSize = num_matches;
    }

    bestInlierNum_ = -1; // 初始化最佳内点数为-1
    Matrix4d bestModel;  // 初始化最佳模型为空

    long double k = 10.0; // 初始化k值为1.0
    double probability = 0.9999;
    int maxIterations = FLAGS_max_gncransac_niter;

    int i = 0;

    // 随机数生成
    std::default_random_engine generator;
    std::uniform_int_distribution<int> distribution(0, num_matches - 1);

    VectorXd dists;

    // 样本预定义
    std::vector<int> indices(options_.sampleSize);

    Matrix<double, 3, Dynamic> v1_sample(3, options_.sampleSize);
    Matrix<double, 3, Dynamic> v2_sample(3, options_.sampleSize);
    int failed_number = 0;

    Pose tmp_pose;
    vector<int> tmp_inliers;

    bestInlierValue_ = 1e8;
    gnc_med_sigma_ = 1e3;
    //    cout<<"i="<<i<<",k="<<k<<endl<<"gnc_med_sigma_="<<gnc_med_sigma_<<",=threshold"<<threshold<<endl;
    //    cout<<(i < k || gnc_med_sigma_>threshold)<<endl;
    double ransac_thre = threshold;
    while (i < k)
    {

        indices = generateRandomSample(num_matches, options_.sampleSize);

        //        cout<<"random indices:"<<endl;
        for (int j = 0; j < options_.sampleSize; ++j)
        {
            v1_sample.col(j) = v1.col(indices[j]);
            v2_sample.col(j) = v2.col(indices[j]);
            //            cout << indices[j] << ", ";
        }

        if (indices.size() < options_.sampleSize)
        {
            cerr << "no sufficient data in gnc-ransac" << endl;
            break;
        }

        // 使用GNC-eval_Rsol_func函数计算模型参数
        //        Rt_est_ = eval_Rt_func_(v1_sample,v2_sample,nullptr);
        //        bool success = true;
        bool success = GNC_IRLS(v1_sample, v2_sample); // Assuming GNC_IRLS returns a boolean indicating success

        //        bool success = multiple_GNC_v1(v1_sample, v2_sample,tmp_pose,tmp_inliers); // Assuming GNC_IRLS returns a boolean indicating success

        //        double outlier_ratio = 0;
        //        double sample_outliers = 0;
        //        for ( int j=0;j<weights_.size();++j){
        //            if (weights_(j)<0.5)
        //            sample_outliers++;
        //        }

        //        outlier_ratio = sample_outliers/weights_.size();

        //        if (!success || outlier_ratio > 0.5) {
        //            continue;
        //        }

        if (!success)
        {
            failed_number++;
            continue;
        }
        else
        {
            failed_number = 0;
        }

        if (failed_number > 10)
            break;
        // 计算数据中所有点与当前模型的差距（例如距离）
        // note: must not be weighted!
        dists = eval_residual_func_(v1, v2, Rt_est_, nullptr);

        // 找出距离在阈值以内的点（即内点）

        gnc_med_sigma_ = findMedian(dists);
        int numInliers = 0;
        for (int j = 0; j < num_matches; ++j)
        {
            if (dists(j) <= ransac_thre)
            {
                numInliers++;
            }
        }

        //        cout<<"numInliers="<<numInliers<<",bestInlierNum_="<<bestInlierNum_<<endl<<",gnc_med_sigma_="<<gnc_med_sigma_<<",bestInlierValue_="<<bestInlierValue_<<endl;
        // 如果当前模型的内点数更多，则更新最佳模型和最佳内点
        //        if (numInliers > bestInlierNum_ && gnc_med_sigma_< bestInlierValue_){
        if (numInliers > bestInlierNum_)
        {

            bestInlierValue_ = gnc_med_sigma_;
            bestInlier_indices_.clear();
            for (int j = 0; j < num_matches; ++j)
            {
                if (dists(j) <= ransac_thre)
                {
                    numInliers++;
                }
            }
            ransac_thre = (gnc_med_sigma_ * 1.4826) * 3; // update ransac_thre
            //            show_inlier_info(bestInlier_indices_,"[in GNC-RANSAC]:");

            bestInlierNum_ = numInliers;
            bestModel_ = Rt_est_;
            best_sigma_ = gnc_med_sigma_;

            // 更新k值
            double w = static_cast<double>(numInliers) / num_matches;
            //            double p_no_outliers = 1.0 - pow(w, 6);
            //            p_no_outliers = max(numeric_limits<double>::epsilon(), p_no_outliers);
            //            p_no_outliers = min(1.0 - numeric_limits<double>::epsilon(), p_no_outliers);
            //            k = log(1 - probability) / log(p_no_outliers);

            double tau = 0.9999;           // 置信水平
            double gamma = 1 - w;          // 外点比例
            int n_s = options_.sampleSize; // 采样大小
            double kappa = 40;             // 异常容忍度
            int n = num_matches;           // 总样本数
            cout << "tau = " << tau << ", gamma = " << gamma << ", n_s = " << n_s << ",kappa = " << kappa << ",n=" << n << endl;
            k = calculateRANSACIterations(tau, gamma, n_s, kappa, n);

            if (k >= 0)
            { // 检查是否有错误
                std::cout << "RANSAC所需的迭代次数: " << k << std::endl;
            }
        }

        if (i > maxIterations)
        {
            break;
        }

        i++;
    }

    cout << "ransac iterr:=" << i << endl;
    if (i < k && i > maxIterations)
    {
        //        bestInlier_indices_.clear();
        //        return false;
    }

    //    dists = eval_residual_func_(v1, v2, bestModel_, nullptr);

    //    bestInlier_indices_.clear();

    //    for (int i = 0; i < num_matches; ++i) {
    //        if (dists(i) <= best_sigma_ * 3) {
    //            bestInlier_indices_.emplace_back(i);
    //        }
    //    }

    cout << "[GNC-ransac] bestInlierNum_=" << bestInlierNum_
         << ", bestInlier_indices_.size()=" << bestInlier_indices_.size()
         << endl;

    return true;
}

bool GNCInfo::GNC_RansacScheme(const BearingVector &v1,
                               const BearingVector &v2,
                               double threshold)
{

    vector<int> final_inliers;
    InitialInliers(v1.cols(), final_inliers);

    double noise_bound = 3;

    SetSigmaMode(0);
    SetTLSBounder(noise_bound);
    SetTLSMaxIter(10);
    SetSampleSizes(FLAGS_gnc_samplesize);
    double ini_thre = FLAGS_inlier_threhold;
    while (!GNC_Ransac(v1, v2, ini_thre))
    {
        ini_thre = ini_thre * noise_bound;
        if (ini_thre > 1e-2)
        {
            break;
            cout << "GNC_RANSACScheme: poor quality!" << endl;
            return false;
        }
    }

    cout << "GNC_RANSACScheme1: threhold = " << threshold << endl;
    cout << "GNC_RANSACScheme1: best_sigma_ = " << best_sigma_ << endl;

    final_inliers.swap(bestInlier_indices_);

    double rec_sigma = best_sigma_;
    Pose best_Rt = bestModel_;
    for (int i = 1; i < 10; ++i)
    {

        //        final_inliers.swap(bestInlier_indices_);

        //        if(false==GNC_Ransac(v1,v2,best_sigma_/3)){
        //            //if fail, then swap before indices
        //            bestInlier_indices_.swap(final_inliers);
        //            break;
        //        }

        cout << "GNC_RANSACScheme-" << i << ": threhold = " << best_sigma_ / noise_bound << endl;

        BearingVector v1_inlier;
        BearingVector v2_inliers;
        obtain_inlier_matches(v1, v2, final_inliers, v1_inlier, v2_inliers);

        if (false == GNC_Ransac(v1_inlier, v2_inliers, best_sigma_ / noise_bound))
        {
            // if fail, then swap before indices
            bestInlier_indices_.swap(final_inliers);
            break;
        }

        cout << "GNC_RANSACScheme-" << i << ": inds = " << bestInlier_indices_.size() << endl;

        int num_used_matches = bestInlier_indices_.size();

        if (best_sigma_ < 1e-6)
        {
            // accuracy enough, then swap before indices
            bestInlier_indices_.swap(final_inliers);
            break;
        }

        if (rec_sigma > best_sigma_)
        {
            best_Rt = bestModel_;
            rec_sigma = best_sigma_;
        }

        if (best_mean_sigma_ < noise_bound * best_sigma_ * 1.4826)
        {
            // accuracy enough, then swap before indices
            bestInlier_indices_.swap(final_inliers);
            break;
        }

        vector<int> tmp_inliers;

        for (auto id = 0; id < bestInlier_indices_.size(); ++id)
        {
            tmp_inliers.emplace_back(final_inliers[bestInlier_indices_[id]]);
        }
        final_inliers.swap(tmp_inliers);
        tmp_inliers.clear();
    }

    bestInlier_indices_.clear();
    auto dists = eval_residual_func_(v1, v2, best_Rt, nullptr);
    weights_ = VectorXd(v1.cols());

    bestModel_ = best_Rt;
    for (int i = 0; i < v1.cols(); ++i)
    {
        if (dists(i) <= rec_sigma * noise_bound * 1.4826)
        {
            weights_(i) = pow(abs(dists(i)), 0.5 - 2);
            if (weights_(i) > 1e4)
            {
                weights_(i) = 1e4;
            }
            bestInlier_indices_.emplace_back(i);
        }
        else
        {
            weights_(i) = 0; // 对于非内点，权重设置为0
        }
    }

    cout << " bestInlier_indices_.size()=" << bestInlier_indices_.size() << endl;

    return true;
}

vector<int> GNCInfo::ShuffleAlgorithm(const BearingVector &v1,
                                      const BearingVector &v2,
                                      double threshold)
{

    int num_matches = v1.cols();
    if (options_.sampleSize == 0)
    {
        options_.sampleSize = num_matches;
    }

    bestInlierNum_ = -1; // 初始化最佳内点数为-1
    Matrix4d bestModel;  // 初始化最佳模型为空

    long double k = 1.0; // 初始化k值为1.0
    double probability = 0.99;
    int maxIterations = 200;

    int i = 0;

    // 随机数生成
    std::default_random_engine generator;
    std::uniform_int_distribution<int> distribution(0, num_matches - 1);

    VectorXd dists;

    // 样本预定义
    std::vector<int> indices(options_.sampleSize);

    Matrix<double, 3, Dynamic> v1_sample(3, options_.sampleSize);
    Matrix<double, 3, Dynamic> v2_sample(3, options_.sampleSize);
    int failed_number = 0;

    Pose tmp_pose;
    VectorXi bad_scores = VectorXi::Zero(num_matches, 1);
    vector<int> solve_pool_ids;

    //    this->SetSigmaMode(-1,threshold);
    while (1)
    {

        // 重构求解池
        solve_pool_ids.clear();
        solve_pool_ids = findMinKIndices(bad_scores, 40);

        indices = generateRandomSample(40, options_.sampleSize);
        for (int j = 0; j < indices.size(); ++j)
        {
            indices[j] = solve_pool_ids[indices[j]];
        }

        obtain_inlier_matches(v1, v2, indices, v1_sample, v2_sample);

        // 使用GNC-eval_Rsol_func函数计算模型参数
        bool success = GNC_IRLS(v1_sample, v2_sample); // Assuming GNC_IRLS returns a boolean indicating success

        if (!success)
        {
            for (int j = 0; j < options_.sampleSize; ++j)
            {
                bad_scores(solve_pool_ids[j]) = bad_scores(solve_pool_ids[j]) + 1;
            }
        }
        else
        {

            // 计算数据中所有点与当前模型的差距（例如距离）
            // note: must not be weighted!
            dists = eval_residual_func_(v1, v2, Rt_est_, nullptr);

            // 找出外点
            for (int j = 0; j < num_matches; ++j)
            {
                if (dists(j) > gnc_sigma_)
                {
                    bad_scores(j) = bad_scores(j) + 1;
                }
            }
        }

        if (i > maxIterations)
        {
            break;
        }

        i++;
    }
    // 使用 std::sort 对 solve_pool_ids 进行升序排序
    std::sort(solve_pool_ids.begin(), solve_pool_ids.end());

    cout << "solve_pool_ids:" << endl;

    for (int i = 0; i < solve_pool_ids.size(); ++i)
    {
        cout << solve_pool_ids[i] << " ";
    }
    cout << endl;
    return solve_pool_ids;
}

bool GNCInfo::multiple_GNC_v1(const BearingVector &v1,
                              const BearingVector &v2,
                              Pose &pose_est,
                              vector<int> &final_inliers)
{
    InitialInliers(v1.cols(), final_inliers);

    /// ======================================================
    /// multiple-process v1.0: 3sigma
    /// tresholds: 1e-2>>1e-3>>1e-4
    use_med = true;
    compute_mode = 0; // use v4.0

    MSTLS_process(v1, v2, 3.0, 1e-1, *this, final_inliers);
    MSTLS_process(v1, v2, 3.0, 1e-2, *this, final_inliers);
    //    echo_on = true;
    MSTLS_process(v1, v2, 3.0, 1e-3, *this, final_inliers);

    pose_est = Rt_est_;
    bool sucessful = true;
    if (final_inliers.size() < 6)
        sucessful = false;
    return sucessful;
};

void GNCInfo::multiple_GNC_v2(const BearingVector &v1,
                              const BearingVector &v2,
                              Pose &pose_est,
                              vector<int> &final_inliers)
{
    InitialInliers(v1.cols(), final_inliers);

    /// ======================================================
    /// multiple-process v2.0(strict): 2sigma
    /// tresholds: 1e-2>>1e-3>>1e-5

    echo_on = false;
    compute_mode = 1;
    use_med = true;
    MSTLS_process(v1, v2, 3.0, 1e-2, *this, final_inliers);
    MSTLS_process(v1, v2, 2.0, 1e-2, *this, final_inliers);
    MSTLS_process(v1, v2, 1.0, 1e-2, *this, final_inliers);
    auto tmp_inliers = final_inliers;

    use_med = false;
    echo_on = true;
    MSTLS_process(v1, v2, *this, tmp_inliers);
    pose_est = Rt_est_;
    //        pose_est = LiRP_process(v1,v2,tmp_inliers);
    //        pose_est = solve_6pts_main(min_six_obs1,min_six_obs2,nullptr);
    //        check_quality(v1,v2,1e-3,pose_est,final_inliers);

    /// ======================================================
    /// multiple-process v2.0(strict): 2sigma
    /// tresholds: 1e-2>>1e-3>>1e-5

    ////        ptr_verified_v1 = &v1;
    ////        ptr_verified_v2 = &v2;

    //        echo_on = true;
    //        MSTLS_process(v1,v2,3.0,1e-3,gnc_info,final_inliers);
    //        echo_on = false;
    //        MSTLS_process(v1,v2,3.0,5*1e-4,gnc_info,final_inliers);
    //        MSTLS_process(v1,v2,3.0,1e-4,gnc_info,final_inliers);

    //        vector<int> used_inliers;
    //        for(int i = 0; i < final_inliers.size(); ++i){
    //            used_inliers.emplace_back(final_inliers[i]);
    //        }

    //        echo_on = true;
    //        compute_mode = 0;
    //        use_med = false;

    ////        MSTLS_process(v1,v2,2.0,1e-4,gnc_info,used_inliers);
    ////        pose_est = gnc_info.Rt_est_;

    //        pose_est = LiRP_process(v1,v2,used_inliers);
    //        if(used_inliers.size()<12)
    //            final_inliers.clear();

    /// ======================================================
};

void GNCInfo::multiple_GNC_v3(const BearingVector &v1,
                              const BearingVector &v2,
                              Pose &pose_est,
                              vector<int> &final_inliers)
{
    InitialInliers(v1.cols(), final_inliers);

    /// ======================================================
    /// multiple-process v1.0: 3sigma
    /// tresholds: 1e-2>>1e-3>>1e-4
    use_med = true;
    compute_mode = 0; // use v4.0

    MSTLS_process(v1, v2, 3.0, 1e-2, *this, final_inliers);
    MSTLS_process(v1, v2, 3.0, 1e-3, *this, final_inliers);

    double inlier_ratio = (1e-12 + final_inliers.size()) / v1.cols();
    cout << "2-th inlier ratio (Multi-GNC):" << 100 * inlier_ratio << " %" << endl;
    auto tmp_backup_final_inliers = final_inliers;
    echo_on = true;
    MSTLS_process(v1, v2, 3.0, 1e-4, *this, final_inliers);
    echo_on = false;

    auto tmp_ids = final_inliers;
    options_.max_iter = 20;
    MSTLS_process(v1, v2, 3.0, 1e-5, *this, tmp_ids);

    pose_est = Rt_est_;

    if (inlier_ratio > 0.5 && is_pose_worked_ == false)
    {
        cout << ">>>>> Multi-GNC: reprocess!!" << endl;
        is_pose_worked_ = true;

        vector<int> best_inliers;
        Pose best_pose;
        size_t max_inliers_size = 0;

        for (int i = 0; i < 4; ++i)
        { // 循环四次
            vector<int> current_inliers = tmp_backup_final_inliers;

            // 确保随机数生成器的种子是不同的
            srand(static_cast<unsigned int>(time(nullptr) + i)); // 添加 i 以确保每次循环种子不同

            // 随机打乱final_inliers
            std::random_device rd;
            std::mt19937 g(rd());
            std::shuffle(current_inliers.begin(), current_inliers.end(), g);

            // 计算要移除的元素数量
            size_t remove_count = current_inliers.size() / 8 > 6 ? current_inliers.size() / 5 : 6;

            // 移除最后一部分的元素
            current_inliers.resize(current_inliers.size() - remove_count);

            MSTLS_process(v1, v2, 3.0, 1e-4, *this, current_inliers);

            auto tmp_ids = current_inliers;
            options_.max_iter = 20;
            MSTLS_process(v1, v2, 3.0, 1e-5, *this, tmp_ids);

            // 比较并记录最佳结果
            if (tmp_ids.size() > max_inliers_size)
            {
                max_inliers_size = tmp_ids.size();
                best_inliers = tmp_ids;
                best_pose = Rt_est_;
            }
        }

        // 设置最终结果

        pose_est = best_pose;
        final_inliers = tmp_backup_final_inliers;
    }
    else if (inlier_ratio < 0.2)
    {
        Rt_est_.rotations.setConstant(std::numeric_limits<double>::quiet_NaN());
        Rt_est_.translations.setConstant(std::numeric_limits<double>::quiet_NaN());
        pose_est = Rt_est_;
        final_inliers.clear();
    }
};

void GNCInfo::multi_GNC_IRLS_v1(const BearingVector &v1,
                                const BearingVector &v2,
                                Pose &pose_est,
                                vector<int> &final_inliers)
{
    InitialInliers(v1.cols(), final_inliers);

    /// ======================================================
    /// multiple-process v1.0: 3sigma
    /// tresholds: 1e-2>>1e-3>>1e-4
    use_med = true;
    compute_mode = 0; // use v4.0

    IRLS_process(v1, v2, 3.0, 1e-2, *this, final_inliers);
    IRLS_process(v1, v2, 3.0, 1e-3, *this, final_inliers);
    echo_on = true;
    IRLS_process(v1, v2, 1.0, 1e-4, *this, final_inliers);

    pose_est = Rt_est_;
};

void GNCInfo::multi_GNC_IRLS_v3(const BearingVector &v1,
                                const BearingVector &v2,
                                Pose &pose_est,
                                vector<int> &final_inliers)
{
    InitialInliers(v1.cols(), final_inliers);

    /// ======================================================
    /// multiple-process v1.0: 3sigma
    /// tresholds: 1e-2>>1e-3>>1e-4
    use_med = true;
    compute_mode = 0; // use v4.0

    IRLS_process(v1, v2, 3.0, 1e-2, *this, final_inliers);
    IRLS_process(v1, v2, 3.0, 1e-3, *this, final_inliers);

    double inlier_ratio = (1e-12 + final_inliers.size()) / v1.cols();
    cout << "2-th inlier ratio (Multi-GNC):" << 100 * inlier_ratio << " %" << endl;
    auto tmp_backup_final_inliers = final_inliers;
    echo_on = true;
    IRLS_process(v1, v2, 3.0, 1e-4, *this, final_inliers);
    echo_on = false;

    auto tmp_ids = final_inliers;
    options_.max_iter = 20;
    IRLS_process(v1, v2, 3.0, 1e-5, *this, tmp_ids);

    pose_est = Rt_est_;

    //    if (inlier_ratio > 0.5 && is_pose_worked_ == false){
    //        cout << ">>>>> Multi-GNC: reprocess!!" << endl;
    //            is_pose_worked_ = true;

    //            vector<int> best_inliers;
    //            Pose best_pose;
    //            size_t max_inliers_size = 0;

    //            for (int i = 0; i < 4; ++i) {  // 循环四次
    //                vector<int> current_inliers = tmp_backup_final_inliers;

    //                // 确保随机数生成器的种子是不同的
    //                srand(static_cast<unsigned int>(time(nullptr) + i));  // 添加 i 以确保每次循环种子不同

    //                // 随机打乱final_inliers
    //                random_shuffle(current_inliers.begin(), current_inliers.end());

    //                // 计算要移除的元素数量
    //                size_t remove_count = current_inliers.size() / 8 > 6 ? current_inliers.size() / 5 : 6;

    //                // 移除最后一部分的元素
    //                current_inliers.resize(current_inliers.size() - remove_count);

    //                MSTLS_process(v1, v2, 3.0, 1e-4, *this, current_inliers);

    //                auto tmp_ids = current_inliers;
    //                options_.max_iter = 20;
    //                MSTLS_process(v1, v2, 3.0, 1e-5, *this, tmp_ids);

    //                // 比较并记录最佳结果
    //                if (tmp_ids.size() > max_inliers_size) {
    //                    max_inliers_size = tmp_ids.size();
    //                    best_inliers = tmp_ids;
    //                    best_pose = Rt_est_;
    //                }
    //            }

    //            // 设置最终结果

    //            pose_est = best_pose;
    //        final_inliers = tmp_backup_final_inliers;
    //    }
    //    else if(inlier_ratio < 0.2){
    //        Rt_est_.rotations.setConstant(std::numeric_limits<double>::quiet_NaN());
    //        Rt_est_.translations.setConstant(std::numeric_limits<double>::quiet_NaN());
    //        pose_est = Rt_est_;
    //        final_inliers.clear();
    //    }
};

void GNCInfo::Shuffle_Ransac(const BearingVector &v1,
                             const BearingVector &v2,
                             double threshold,
                             int num_min_score_ids)
{
    int num_matches = v1.cols();
    vector<double> bad_scores(num_matches, 0.0); // 使用bad_scores替代scores，命名更符合Python实现
    vector<int> solve_pool_ids;                  // 更名为solve_pool_ids，与Python保持一致

    if (options_.sampleSize == 0)
    {
        options_.sampleSize = num_matches;
    }

    bestInlierNum_ = -1; // 初始化最佳内点数为-1

    long double k = 1.0; // 初始化k值为1.0
    int maxIterations = FLAGS_max_gncransac_niter;
    int iterations = 0; // 迭代计数器

    // 随机数生成
    std::random_device rd;
    std::mt19937 generator(rd());
    std::uniform_int_distribution<int> distribution(0, num_matches - 1);

    // 样本预定义
    std::vector<int> indices;
    Matrix<double, 3, Dynamic> v1_sample(3, options_.sampleSize);
    Matrix<double, 3, Dynamic> v2_sample(3, options_.sampleSize);

    int failed_number = 0;
    vector<int> tmp_inliers;
    double gamma = 1.0;
    bool flag_update_pool = true; // 添加标志位，控制何时更新solve_pool

    // 参数设置
    double noise_bound = 5.54;
    SetSigmaMode(2);
    SetTLSBounder(noise_bound);
    SetTLSMaxIter(10);

    while (iterations < k || gamma > 0.3)
    {
        // 更新solve_pool，仅在flag_update_pool为true时更新
        if (flag_update_pool)
        {
            solve_pool_ids.clear();
            vector<pair<double, int>> score_pairs;
            for (int j = 0; j < bad_scores.size(); ++j)
            {
                score_pairs.push_back(make_pair(bad_scores[j], j));
            }
            sort(score_pairs.begin(), score_pairs.end());
            for (int j = 0; j < num_min_score_ids && j < score_pairs.size(); ++j)
            {
                solve_pool_ids.push_back(score_pairs[j].second);
            }
            flag_update_pool = false;
        }

        // 从solve_pool中随机选择样本
        indices.clear();
        for (int j = 0; j < options_.sampleSize; ++j)
        {
            int randIndex = generator() % solve_pool_ids.size();
            indices.push_back(solve_pool_ids[randIndex]);
            v1_sample.col(j) = v1.col(solve_pool_ids[randIndex]);
            v2_sample.col(j) = v2.col(solve_pool_ids[randIndex]);
        }

        if (indices.size() < options_.sampleSize)
        {
            // cerr << "解池中没有足够的数据" << endl;
            break;
        }

        // 使用GNC-IRLS计算模型参数
        bool success = GNC_IRLS(v1_sample, v2_sample);

        if (!success)
        {
            // 增加所有采样点的bad_scores
            for (int j = 0; j < indices.size(); ++j)
            {
                bad_scores[indices[j]] += 1.0;
            }

            failed_number++;

            if (failed_number > 20)
            {
                // 如果失败次数大于20，认为当前求解池质量太差，全部solve_pool_ids索引的观测标记为外点
                for (int j = 0; j < solve_pool_ids.size(); ++j)
                {
                    bad_scores[solve_pool_ids[j]] += 1.0;
                }
                flag_update_pool = true;
            }
            continue;
        }
        else
        {
            // 成功则重置失败计数器
            failed_number = 0;
            flag_update_pool = true;
        }

        // 计算数据中所有点与当前模型的差距
        VectorXd dists = eval_dist_func_(v1, v2, Rt_est_, nullptr);

        // 增加所有残差大于设定阈值的点的bad_scores
        for (int j = 0; j < num_matches; ++j)
        {
            if (dists(j) > 2 * threshold)
            {
                bad_scores[j] += 1.0;
            }
        }

        // 计算solve_pool中的内点比例
        int solve_pool_inliers = 0;
        tmp_inliers.clear();
        for (int idx : solve_pool_ids)
        {
            if (dists(idx) <= 2 * threshold)
            {
                solve_pool_inliers++;
                tmp_inliers.push_back(idx);
            }
        }

        double w = static_cast<double>(solve_pool_inliers) / solve_pool_ids.size();

        // 更新所有匹配点的bad_scores
        for (int j = 0; j < num_matches; ++j)
        {
            if (dists(j) > 2 * threshold)
            {
                bad_scores[j] += 1.0;
            }
        }

        // 使用solve_pool中的内点数作为当前模型的内点数
        int numInliers = solve_pool_inliers;

        // 如果当前模型的内点数更多，则更新最佳模型和最佳内点
        if (numInliers > bestInlierNum_)
        {
            bestInlierNum_ = numInliers;
            bestInlier_indices_ = tmp_inliers; // 保存当前内点索引列表
            bestModel_ = Rt_est_;
            best_sigma_ = gnc_sigma_;

            // 如果找到的内点数足够，使用这些内点重新估计模型
            if (tmp_inliers.size() >= options_.sampleSize)
            {
                BearingVector v1_inliers, v2_inliers;
                obtain_inlier_matches(v1, v2, tmp_inliers, v1_inliers, v2_inliers);

                // 保存当前TLS最大迭代次数
                int original_max_iter = options_.max_iter;

                // 设置更大的迭代次数用于内部模型重新估计
                SetTLSMaxIter(20);

                // 使用GNC-IRLS重新估计模型
                if (GNC_IRLS(v1_inliers, v2_inliers))
                {
                    // 使用精确估计的模型更新最佳模型
                    bestModel_ = Rt_est_;
                    best_sigma_ = gnc_sigma_;
                }

                // 还原原始TLS最大迭代次数
                SetTLSMaxIter(original_max_iter);
            }

            // 更新k值
            double tau = 0.99;             // 置信水平
            gamma = 1.0 - w;               // 外点比例
            int n_s = options_.sampleSize; // 采样大小
            double kappa = 40;             // 异常容忍度，从30调整为40，与Python一致
            int n = num_min_score_ids;     // 总样本数

            k = calculateRANSACIterations(tau, gamma, n_s, kappa, n);
            if (k >= 0)
            {
                k = std::min(static_cast<long double>(maxIterations), k);
            }

            cout << "内点数=" << numInliers << ", gamma=" << gamma
                 << ", 总大小=" << solve_pool_ids.size() << ", Nmax= " << k << std::endl;
        }

        if (iterations >= maxIterations)
        {
            break;
        }

        iterations++;
    }

    // 使用最佳模型重新计算所有点的残差
    VectorXd dists = eval_dist_func_(v1, v2, bestModel_, nullptr);

    // 重新确定最终内点集
    bestInlier_indices_.clear();
    for (int i = 0; i < num_matches; ++i)
    {
        if (dists(i) <= threshold)
        {
            bestInlier_indices_.push_back(i);
        }
    }

    bestInlierNum_ = bestInlier_indices_.size();

    // 使用最终确定的所有内点再次应用GNC-IRLS优化模型
    if (bestInlier_indices_.size() >= options_.sampleSize)
    {
        BearingVector v1_final_inliers, v2_final_inliers;
        obtain_inlier_matches(v1, v2, bestInlier_indices_, v1_final_inliers, v2_final_inliers);

        // 保存当前TLS最大迭代次数
        int original_max_iter = options_.max_iter;

        // 设置更大的迭代次数用于最终优化
        SetTLSMaxIter(20);

        // 对所有内点应用GNC-IRLS进行最终优化
        if (GNC_IRLS(v1_final_inliers, v2_final_inliers))
        {
            cout << "使用全部" << bestInlier_indices_.size() << "个内点进行最终模型优化 (20次迭代)" << endl;
            // 更新为优化后的最终模型
            bestModel_ = Rt_est_;
        }

        // 还原原始TLS最大迭代次数
        SetTLSMaxIter(original_max_iter);
    }

    cout << "RAPIDS算法完成: 找到" << bestInlierNum_ << "个内点，共迭代" << iterations << "次" << endl;
}

double GNCInfo::ScaleParameter(const VectorXd &residuals, double iter_ratio)
{
    const int min_needed_obs = 8;
    if (iter_ratio > 0.5)
    {
        iter_ratio = 0.5;
    }

    int num_obs = residuals.size();
    int used_num_obs = static_cast<int>(floor(iter_ratio * num_obs));
    if (used_num_obs < min_needed_obs)
    {
        used_num_obs = min_needed_obs;
    }

    // Extract the smallest 'used_num_obs' residuals
    std::vector<double> sorted_residuals(residuals.data(), residuals.data() + residuals.size());
    std::sort(sorted_residuals.begin(), sorted_residuals.end());
    sorted_residuals.resize(used_num_obs);

    double median;
    if (used_num_obs % 2 == 0)
    {
        median = (sorted_residuals[used_num_obs / 2 - 1] + sorted_residuals[used_num_obs / 2]) / 2.0;
    }
    else
    {
        median = sorted_residuals[used_num_obs / 2];
    }
    double sigma = 1.4826 * median;
    return sigma;
}

double GNCInfo::ScaleParameter(const VectorXd &residuals)
{

    int used_num_obs = residuals.size();

    // Extract the smallest 'used_num_obs' residuals
    std::vector<double> sorted_residuals(residuals.data(), residuals.data() + residuals.size());
    std::sort(sorted_residuals.begin(), sorted_residuals.end());
    sorted_residuals.resize(used_num_obs);

    double median;
    if (used_num_obs % 2 == 0)
    {
        median = (sorted_residuals[used_num_obs / 2 - 1] + sorted_residuals[used_num_obs / 2]) / 2.0;
    }
    else
    {
        median = sorted_residuals[used_num_obs / 2];
    }
    double sigma = 1.4826 * median;
    //    double sigma = residuals.mean();
    return sigma;
}

double GNCInfo::FastScaleParameter(const VectorXd &residuals, double iter_ratio)
{
    const int min_needed_obs = 12;
    if (iter_ratio > 0.5)
    {
        iter_ratio = 0.5;
    }
    int num_obs = residuals.size();
    int used_num_obs = static_cast<int>(floor(iter_ratio * num_obs));
    if (used_num_obs < min_needed_obs)
    {
        used_num_obs = min_needed_obs;
    }

    // Use nth_element to get the smallest 'used_num_obs' residuals
    std::vector<double> sorted_residuals(residuals.data(), residuals.data() + residuals.size());
    std::nth_element(sorted_residuals.begin(), sorted_residuals.begin() + used_num_obs, sorted_residuals.end());

    // Now, sort only the smallest 'used_num_obs' residuals
    std::sort(sorted_residuals.begin(), sorted_residuals.begin() + used_num_obs);

    // Compute the median of the smallest residuals
    double median;
    if (used_num_obs % 2 == 0)
    {
        median = (sorted_residuals[used_num_obs / 2 - 1] + sorted_residuals[used_num_obs / 2]) / 2.0;
    }
    else
    {
        median = sorted_residuals[used_num_obs / 2];
    }
    double sigma = 1.4826 * median;
    return sigma;
}

bool GNCInfo::GNC_IRLSp(const BearingVector &v1,
                        const BearingVector &v2)
{
    // Initialization
    int num_obs = v1.cols();
    MatrixXd P = MatrixXd::Identity(num_obs, num_obs);
    MatrixXd Rt_est_pre = MatrixXd::Zero(3, 4);
    VectorXd first_abscost_vec;

    weights_ = VectorXd::Ones(num_obs);

    is_pose_worked_ = true;

    double pre_residuals = numeric_limits<double>::infinity();

    double epsilon = 0.1;
    double beta = 0.8;
    double p = 0.5;
    double epsilon_min;
    double noise_bound;

    int iter;
    for (iter = 1; iter <= options_.max_iter; ++iter)
    {
        P = weights_.asDiagonal(); // Converts vector to diagonal matrix

        // Check if enough weights are greater than 0.1
        if ((weights_.array() > 0.1).count() < 10)
        {
            Rt_est_.rotations.setConstant(numeric_limits<double>::quiet_NaN());
            Rt_est_.translations.setConstant(numeric_limits<double>::quiet_NaN());
            is_pose_worked_ = false;
            return false;
        }

        Rt_est_ = eval_Rt_func_(v1, v2, &weights_);
        Matrix3d R_est = Rt_est_.rotations;
        Vector3d t_est = Rt_est_.translations;

        VectorXd residuals = eval_residual_func_(v1, v2, Rt_est_, nullptr);
        VectorXd abs_residuals = residuals.cwiseAbs();
        VectorXd squared_residual = abs_residuals.cwiseProduct(abs_residuals);

        switch (sigma_mode_)
        {

        case -1:
            gnc_sigma_ = sigma_threshold_;
            break;
        case 0:
            gnc_sigma_ = ScaleParameter(residuals, double(iter) / options_.max_iter);
            break;
        case 1:
            gnc_sigma_ = FastScaleParameter(residuals, double(iter) / options_.max_iter);
        case 2:
            gnc_sigma_ = ScaleParameter(residuals);
        default:
            gnc_sigma_ = ScaleParameter(residuals, double(iter) / options_.max_iter);
        }

        noise_bound = options_.bounder * gnc_sigma_;
        epsilon_min = noise_bound;

        //        weights_ = (abs_residuals.array().max(epsilon)).pow(p - 2);
        weights_ = (abs_residuals.array().max(epsilon)).inverse().pow(2.0 - p);

        double cost = weights_.dot(squared_residual);

        double cost_diff = abs(cost - pre_residuals);

        if (cost_diff <= options_.stopTh)
        {
            break;
        }

        pre_residuals = cost;
        epsilon = std::max(beta * pow(epsilon, 2 - p), epsilon_min);
    }

    iter_ = iter;
    weights_ /= pow(epsilon_min, p - 2);
    return true;
}

bool GNCInfo::GNC_IRLS(const BearingVector &v1, const BearingVector &v2)
{

    // Initialization
    int num_obs = v1.cols();
    MatrixXd P = MatrixXd::Identity(num_obs, num_obs);
    MatrixXd Rt_est_pre = MatrixXd::Zero(3, 4);
    VectorXd first_abscost_vec;

    weights_ = VectorXd::Ones(num_obs);

    is_pose_worked_ = true;

    double pre_residuals = numeric_limits<double>::infinity();

    int iter;
    double mu;
    double gamma = 1.4;
    double min_residual = 1e10;
    Pose best_pose;
    double tmp_best_sigma = 0;
    for (iter = 1; iter <= options_.max_iter; ++iter)
    {
        P = weights_.asDiagonal(); // Converts vector to diagonal matrix
        // Count the number of elements greater than 0.1
        int count = (weights_.array() > 0.1).count();

        if (count < 6)
        {
            //        if (count < 6) {
            // Break out of the loop or exit the function depending on your context.
            // If this is inside a loop, 'break;' is sufficient. If in a function, you might need to 'return;'
            Rt_est_.rotations.setConstant(std::numeric_limits<double>::quiet_NaN());
            Rt_est_.translations.setConstant(std::numeric_limits<double>::quiet_NaN());
            is_pose_worked_ = false;
            return false;
            //            break;
        }

        if (!is_reweighted && iter == 1)
        {
        }
        else
        {
            Rt_est_ = eval_Rt_func_(v1, v2, &weights_);
        }

        Matrix3d R_est = Rt_est_.rotations;
        Vector3d t_est = Rt_est_.translations;

        VectorXd residuals = eval_residual_func_(v1, v2, Rt_est_, nullptr);
        //        double med_residual = findMedian(residuals.cwiseAbs());
        double med_residual = 0;
        //        sigma = ScaleParameter(residuals, double(iter) / options_.max_iter);

        switch (sigma_mode_)
        {

        case -1:
            gnc_sigma_ = sigma_threshold_;
            break;
        case 0:
            gnc_sigma_ = ScaleParameter(residuals, double(iter) / options_.max_iter);
            break;
        case 1:
            gnc_sigma_ = FastScaleParameter(residuals, double(iter) / options_.max_iter);
        case 2:
            gnc_sigma_ = ScaleParameter(residuals);
        default:
            gnc_sigma_ = ScaleParameter(residuals, double(iter) / options_.max_iter);
        }
        gnc_max_residual_ = residuals.array().abs().maxCoeff();
        gnc_mean_sigma_ = residuals.array().abs().mean();
        gnc_med_sigma_ = ScaleParameter(residuals);

        //        cout<<gnc_sigma_<<endl;
        if (min_residual > pose_check_cost)
        {
            min_residual = pose_check_cost;
            best_pose = Rt_est_;
            tmp_best_sigma = gnc_med_sigma_;
        }
        VectorXd abs_residuals = (residuals.array() - med_residual).cwiseAbs();
        abs_residuals = abs_residuals.array();

        ///------------------- min test ids ------------------------
        std::vector<ResidualIndex> residual_indices;
        // 复制残差值和索引
        for (int i = 0; i < residuals.size(); ++i)
        {
            residual_indices.emplace_back(residuals[i], i);
        }

        // 使用nth_element找到第min_num小的元素
        int min_num = num_buffer_ids_ < v1.cols() ? num_buffer_ids_ : v1.cols();

        std::nth_element(residual_indices.begin(), residual_indices.begin() + min_num, residual_indices.end());

        // 创建新的 BearingVector 用于存储对应的观测
        min_six_ids.resize(min_num);

        for (int i = 0; i < min_num; ++i)
        {
            int index = residual_indices[i].index;
            min_six_ids[i] = index;
        }

        int max_num = std::min(num_buffer_ids_, static_cast<int>(v1.cols())); // 计算实际需要的元素数量

        // 使用 nth_element 以及自定义的比较函数找到第 max_num 大的元素
        std::nth_element(residual_indices.begin(), residual_indices.begin() + max_num, residual_indices.end(),
                         [](const ResidualIndex &a, const ResidualIndex &b)
                         {
                             return a.residual > b.residual; // 降序排序
                         });

        // 调整 max_six_ids 的大小
        max_six_ids.resize(max_num);

        // 从 residual_indices 中提取最大的 max_num 个残差对应的索引
        for (int i = 0; i < max_num; ++i)
        {
            int index = residual_indices[i].index;
            max_six_ids[i] = index;
        }

        ///---------------------------------------------

        VectorXd squared_residual = abs_residuals.cwiseProduct(abs_residuals); // element-wise multiplication

        double max_residual = squared_residual.maxCoeff();

        double noise_bound = options_.bounder * gnc_sigma_;
        //        double noise_bound = 3 * gnc_sigma_;
        double noise_bound_squared = noise_bound * noise_bound;

        if (iter == 1)
        {
            mu = std::max(1.0 / (5.0 * max_residual / noise_bound_squared - 1.0), 1e-6);
        }

        if (options_.majorize)
        {
            weights_ = M_gncWeightsUpdate(weights_, mu, squared_residual, noise_bound_squared);
        }
        else
        {
            weights_ = gncWeightsUpdate(weights_, mu, squared_residual, noise_bound_squared);
        }

        //        cout<<weights_.norm()<<endl;
        double cost = weights_.dot(squared_residual); // Dot product

        double cost_diff = abs(cost - pre_residuals);

        if (cost_diff <= options_.stopTh)
        {
            break;
        }

        if (options_.superlinear)
        {
            if (mu < 1)
            {
                mu = std::min(sqrt(mu) * gamma, 1e16);

                //                double p = 0.5;
                //                double tmp_value = gamma * pow(mu, 1.0 / (2.0 - p));

                //                mu = std::min(tmp_value, 1e16);
            }
            else
            {
                mu = std::min(mu * gamma, 1e16);
            }
        }
        else
        {
            mu = std::min(mu * gamma, 1e16);
        }

        pre_residuals = cost;
    }

    iter_ = iter;

    int count = (weights_.array() > 0.5).count();
    if (count < 6)
    {
        return false;
    }

    double tmp_inlier_ratio = double(count) / getSampleSize();
    gnc_indicator_ = 2 * min(0.5, tmp_inlier_ratio) / max(gnc_sigma_, FLAGS_inlier_threhold);
    double tmp_threshold = 1 / (20 * FLAGS_inlier_threhold);
    //    std::cout<<"indicator="<<gnc_indicator_<<", tmp_threshold="<<tmp_threshold<<std::endl;

    if (gnc_indicator_ < tmp_threshold)
        return false;
    else
        return true;

    //    return true;
}

bool GNCInfo::GNC_IRLS2(const BearingVector &v1, const BearingVector &v2)
{

    // Initialization
    int num_obs = v1.cols();
    MatrixXd P = MatrixXd::Identity(num_obs, num_obs);
    MatrixXd Rt_est_pre = MatrixXd::Zero(3, 4);
    VectorXd first_abscost_vec;

    weights_ = VectorXd::Ones(num_obs);

    is_pose_worked_ = true;

    double pre_residuals = numeric_limits<double>::infinity();

    int iter;
    double mu;
    double gamma = 1.4;
    double min_residual = 1e10;
    Pose best_pose;
    double tmp_best_sigma = 0;
    for (iter = 1; iter <= options_.max_iter; ++iter)
    {
        P = weights_.asDiagonal(); // Converts vector to diagonal matrix
        // Count the number of elements greater than 0.1
        int count = (weights_.array() > 0.1).count();

        if (count < 6)
        {
            // Break out of the loop or exit the function depending on your context.
            // If this is inside a loop, 'break;' is sufficient. If in a function, you might need to 'return;'
            Rt_est_.rotations.setConstant(std::numeric_limits<double>::quiet_NaN());
            Rt_est_.translations.setConstant(std::numeric_limits<double>::quiet_NaN());
            is_pose_worked_ = false;
            return false;
            //            break;
        }

        Rt_est_ = eval_Rt_func_(v1, v2, &weights_);

        Matrix3d R_est = Rt_est_.rotations;
        Vector3d t_est = Rt_est_.translations;

        VectorXd residuals = eval_residual_func_(v1, v2, Rt_est_, nullptr);
        double med_residual = findMedian(residuals.cwiseAbs());
        //        sigma = ScaleParameter(residuals, double(iter) / options_.max_iter);

        switch (sigma_mode_)
        {

        case -1:
            gnc_sigma_ = sigma_threshold_;
            break;
        case 0:
            gnc_sigma_ = ScaleParameter(residuals, double(iter) / options_.max_iter);
            break;
        case 1:
            gnc_sigma_ = FastScaleParameter(residuals, double(iter) / options_.max_iter);
        case 2:
            gnc_sigma_ = ScaleParameter(residuals);
        default:
            gnc_sigma_ = ScaleParameter(residuals, double(iter) / options_.max_iter);
        }
        gnc_max_residual_ = residuals.array().abs().maxCoeff();
        gnc_mean_sigma_ = residuals.array().abs().mean();
        gnc_med_sigma_ = ScaleParameter(residuals);

        //        cout<<gnc_sigma_<<endl;
        if (min_residual > pose_check_cost)
        {
            min_residual = pose_check_cost;
            best_pose = Rt_est_;
            tmp_best_sigma = gnc_med_sigma_;
        }
        VectorXd abs_residuals = (residuals.array() - med_residual).cwiseAbs();
        abs_residuals = abs_residuals.array();

        ///------------------- min test ids ------------------------
        std::vector<ResidualIndex> residual_indices;
        // 复制残差值和索引
        for (int i = 0; i < residuals.size(); ++i)
        {
            residual_indices.emplace_back(residuals[i], i);
        }

        // 使用nth_element找到第min_num小的元素
        int min_num = num_buffer_ids_ < v1.cols() ? num_buffer_ids_ : v1.cols();

        std::nth_element(residual_indices.begin(), residual_indices.begin() + min_num, residual_indices.end());

        // 创建新的 BearingVector 用于存储对应的观测
        min_six_ids.resize(min_num);

        for (int i = 0; i < min_num; ++i)
        {
            int index = residual_indices[i].index;
            min_six_ids[i] = index;
        }

        int max_num = std::min(num_buffer_ids_, static_cast<int>(v1.cols())); // 计算实际需要的元素数量

        // 使用 nth_element 以及自定义的比较函数找到第 max_num 大的元素
        std::nth_element(residual_indices.begin(), residual_indices.begin() + max_num, residual_indices.end(),
                         [](const ResidualIndex &a, const ResidualIndex &b)
                         {
                             return a.residual > b.residual; // 降序排序
                         });

        // 调整 max_six_ids 的大小
        max_six_ids.resize(max_num);

        // 从 residual_indices 中提取最大的 max_num 个残差对应的索引
        for (int i = 0; i < max_num; ++i)
        {
            int index = residual_indices[i].index;
            max_six_ids[i] = index;
        }

        ///---------------------------------------------

        VectorXd squared_residual = abs_residuals.cwiseProduct(abs_residuals); // element-wise multiplication

        double max_residual = squared_residual.maxCoeff();

        double noise_bound = options_.bounder * gnc_sigma_;
        //        double noise_bound = 3 * gnc_sigma_;
        double noise_bound_squared = noise_bound * noise_bound;

        if (iter == 1)
        {
            //            mu = std::max(1.0 / (5.0 * max_residual / noise_bound_squared - 1.0), 1e-6);
            mu = std::max(1.0 / (5.0 * max_residual / noise_bound_squared - 1.0), 1e-3);
        }

        if (options_.majorize)
        {
            weights_ = M_gncWeightsUpdate(weights_, mu, squared_residual, noise_bound_squared);
        }
        else
        {
            weights_ = gncWeightsUpdate(weights_, mu, squared_residual, noise_bound_squared);
        }

        //        cout<<weights_.norm()<<endl;
        double cost = weights_.dot(squared_residual); // Dot product

        double cost_diff = abs(cost - pre_residuals);

        if (cost_diff <= options_.stopTh)
        {
            break;
        }

        if (options_.superlinear)
        {
            if (mu < 1)
            {
                mu = std::min(sqrt(mu) * gamma, 1e16);

                //                double p = 0.5;
                //                double tmp_value = gamma * pow(mu, 1.0 / (2.0 - p));

                //                mu = std::min(tmp_value, 1e16);
            }
            else
            {
                mu = std::min(mu * gamma, 1e16);
            }
        }
        else
        {
            mu = std::min(mu * gamma, 1e16);
        }

        pre_residuals = cost;
    }

    //    cout<<"GNC-IRLS iter = "<<iter_<<endl;
    iter_ = iter;

    Rt_est_ = best_pose;

    auto dists = eval_residual_func_(v1, v2, Rt_est_, nullptr);

    bestInlier_indices_.clear();
    gnc_med_sigma_ = tmp_best_sigma;
    for (int i = 0; i < v1.cols(); ++i)
    {
        if (dists(i) <= tmp_best_sigma * 3)
        {
            weights_[i] = 1;
        }
        else
        {
            weights_[i] = 0;
        }
    }

    return true;
}
VectorXd GNCInfo::M_gncWeightsUpdate(VectorXd weights, double mu, const VectorXd &residuals, double barc2)
{
    double ub = pow(mu + 1, 2) / pow(mu, 2) * barc2;
    double lb = barc2;

    for (int k = 0; k < residuals.size(); ++k)
    {
        if (residuals(k) - ub >= 0)
        {
            weights(k) = 0;
        }
        else if (residuals(k) - lb <= 0)
        {
            weights(k) = 1;
        }
        else
        {
            weights(k) = sqrt(barc2 / residuals(k)) * (mu + 1) - mu;
        }
    }
    return weights;
}

VectorXd GNCInfo::gncWeightsUpdate(VectorXd weights, double mu, const VectorXd &residuals, double barc2)
{
    double ub = (mu + 1) / mu * barc2;
    double lb = mu / (mu + 1) * barc2;

    for (int k = 0; k < residuals.size(); ++k)
    {
        if (residuals(k) - ub >= 0)
        {
            weights(k) = 0;
        }
        else if (residuals(k) - lb <= 0)
        {
            weights(k) = 1;
        }
        else
        {
            weights(k) = sqrt(barc2 * mu * (mu + 1) / residuals(k)) - mu;
        }
    }
    return weights;
}

#include <iostream>
#include <vector>
#include <cmath>
#include <Eigen/Dense>
using namespace std;
using namespace Eigen;

bool MSTLS_reprocess(const BearingVector &v1,
                     const BearingVector &v2,
                     GNCInfo &gnc_info,
                     vector<int> &final_inliers)
{
    bool is_success = true;
    // --------- cvpr ------------
    //    gnc_info.SetSigmaMode(0);
    gnc_info.SetSigmaMode(2);
    gnc_info.SetTLSBounder(2);
    //    gnc_info.SetSigmaMode(-1,1e-3);
    // -------------------------

    //    double bounder = 2.0;
    //    double upper_noise_bounder = 1e-3;

    //        gnc_info.SetSigmaMode(-1,upper_noise_bounder);

    is_success = gnc_info.GNC_IRLS(v1, v2);

    // obtain inliers
    int num_inlier_indices = 0;
    final_inliers.clear();

    for (int i = 0; i < gnc_info.weights_.size(); i++)
    {
        if (gnc_info.weights_(i) > 0.1)
        {
            num_inlier_indices++;
            final_inliers.emplace_back(i);
        }
    }

    // reprocess ----------------------------
    for (int iter = 0; iter < 1; ++iter)
    {
        cout << "num_inlier_indices_ in" << iter << "-th gnc:" << final_inliers.size() << endl;

        BearingVector v1_inliers;
        BearingVector v2_inliers;
        v1_inliers.setZero(3, num_inlier_indices);
        v2_inliers.setZero(3, num_inlier_indices);

        for (int i = 0; i < final_inliers.size(); i++)
        {
            cout << final_inliers[i] << " ";
            int id_inlier = final_inliers[i];
            v1_inliers.col(i) = v1.col(id_inlier);
            v2_inliers.col(i) = v2.col(id_inlier);
        }
        cout << endl;

        gnc_info.SetSigmaMode(-1, 1e-3);
        gnc_info.SetTLSBounder(2);

        //        gnc_info.SetSigmaMode(-1,upper_noise_bounder/bounder);
        //        gnc_info.SetTLSBounder(bounder);
        is_success = gnc_info.GNC_IRLS(v1_inliers, v2_inliers);

        vector<int> tmp_inliers;

        for (auto id = 0; id < final_inliers.size(); ++id)
        {
            if (gnc_info.weights_(id) > 0.1)
            {
                tmp_inliers.emplace_back(final_inliers[id]);
            }
        }

        final_inliers.swap(tmp_inliers);
        tmp_inliers.clear();
    }

    cout << "num_inlier_indices_ in final ms-tls:" << final_inliers.size() << endl;

    for (int i = 0; i < final_inliers.size(); i++)
    {
        cout << final_inliers[i] << " ";
    }
    cout << endl;

    // reprocess end ----------------------------
    return is_success;
}

bool MSTLS_reprocess(const BearingVector &v1,
                     const BearingVector &v2,
                     const double &bounder,
                     const double &upper_noise_bounder,
                     GNCInfo &gnc_info,
                     vector<int> &final_inliers)
{
    bool is_success = true;
    // --------- cvpr ------------
    gnc_info.SetSigmaMode(0);
    //    gnc_info.SetSigmaMode(2);
    //    gnc_info.SetSigmaMode(-1,1e-3);
    // -------------------------

    ////    double bounder = 2.0;
    ////    double upper_noise_bounder = 1e-3;
    //    //    gnc_info.SetSigmaMode(0);
    ////        gnc_info.SetSigmaMode(2);
    //        gnc_info.SetSigmaMode(-1,upper_noise_bounder);

    is_success = gnc_info.GNC_IRLS(v1, v2);

    // obtain inliers
    int num_inlier_indices = 0;
    final_inliers.clear();

    for (int i = 0; i < gnc_info.weights_.size(); i++)
    {
        if (gnc_info.weights_(i) > 0.1)
        {
            num_inlier_indices++;
            final_inliers.emplace_back(i);
        }
    }

    // reprocess ----------------------------
    for (int iter = 0; iter < 1; ++iter)
    {
        cout << "num_inlier_indices_ in" << iter << "-th gnc:" << final_inliers.size() << endl;

        BearingVector v1_inliers;
        BearingVector v2_inliers;
        v1_inliers.setZero(3, num_inlier_indices);
        v2_inliers.setZero(3, num_inlier_indices);

        for (int i = 0; i < final_inliers.size(); i++)
        {
            cout << final_inliers[i] << " ";
            int id_inlier = final_inliers[i];
            v1_inliers.col(i) = v1.col(id_inlier);
            v2_inliers.col(i) = v2.col(id_inlier);
        }
        cout << endl;

        gnc_info.SetSigmaMode(2);
        //        gnc_info.SetSigmaMode(-1,upper_noise_bounder/bounder);
        gnc_info.SetTLSBounder(bounder);
        is_success = gnc_info.GNC_IRLS(v1_inliers, v2_inliers);

        vector<int> tmp_inliers;

        for (auto id = 0; id < final_inliers.size(); ++id)
        {
            if (gnc_info.weights_(id) > 0.1)
            {
                tmp_inliers.emplace_back(final_inliers[id]);
            }
        }

        final_inliers.swap(tmp_inliers);
        tmp_inliers.clear();
    }

    cout << "num_inlier_indices_ in final gnc:" << final_inliers.size() << endl;

    for (int i = 0; i < final_inliers.size(); i++)
    {
        cout << final_inliers[i] << " ";
    }
    cout << endl;

    // reprocess end ----------------------------
    return is_success;
}

Pose LiRP_process(const BearingVector &v1,
                  const BearingVector &v2,
                  vector<int> &final_inliers)
{

    int num_inlier_indices = final_inliers.size();

    // reprocess ----------------------------
    {
        BearingVector v1_inliers;
        BearingVector v2_inliers;
        v1_inliers.setZero(3, num_inlier_indices);
        v2_inliers.setZero(3, num_inlier_indices);

        for (int i = 0; i < final_inliers.size(); i++)
        {
            int id_inlier = final_inliers[i];
            v1_inliers.col(i) = v1.col(id_inlier);
            v2_inliers.col(i) = v2.col(id_inlier);
        }

        return solve_6pts_main(v1_inliers, v2_inliers, nullptr);
    }
}

bool gradually_remove(const BearingVector &v1,
                      const BearingVector &v2,
                      vector<int> &final_inliers)
{
    bool is_successful = true;

    int num_inlier_indices = final_inliers.size();

    // reprocess ----------------------------

    //        cout<<"num_inlier_indices_ in" << iter <<"-th gnc:"<<final_inliers.size()<<endl;

    BearingVector v1_inliers;
    BearingVector v2_inliers;
    obtain_inlier_matches(v1, v2, final_inliers, v1_inliers, v2_inliers);

    auto Rt_est_ = solve_6pts_main(v1_inliers, v2_inliers, nullptr);
    Matrix3d R_est = Rt_est_.rotations;
    Vector3d t_est = Rt_est_.translations;

    VectorXd residuals = residual_LiRT(v1, v2, Rt_est_, nullptr);
    double pre_med_residual = findMedian(residuals);

    vector<int> tmp_ids = final_inliers;

    for (int i = 0; i < tmp_ids.size(); i++)
    {
        tmp_ids = final_inliers;
        tmp_ids.erase(tmp_ids.begin() + i); // 删除第 i 个元素
        obtain_inlier_matches(v1, v2, tmp_ids, v1_inliers, v2_inliers);

        Rt_est_ = solve_6pts_main(v1_inliers, v2_inliers, nullptr);
        R_est = Rt_est_.rotations;
        t_est = Rt_est_.translations;

        residuals = residual_LiRT(v1, v2, Rt_est_, nullptr);
        double med_residual = findMedian(residuals);

        if (med_residual < pre_med_residual)
        {
            final_inliers = tmp_ids;
            pre_med_residual = med_residual;
        }

        if (final_inliers.size() < 6)
        {
            is_successful = false;
            break;
        }
        cout << "pre_med_residual = " << pre_med_residual << endl;
    }

    if (pre_med_residual > 1e-4)
    {
        is_successful = false;
        cout << "gradually remove inds failed!" << endl;
    }

    cout << "remained minimal inlier ids: ";
    for (int i = 0; i < final_inliers.size(); ++i)
    {
        cout << final_inliers[i] << " ";
    }
    cout << endl;

    // reprocess end ----------------------------
    return is_successful;
}

double obtain_inliers_residual(const BearingVector &v1,
                               const BearingVector &v2,
                               vector<int> &final_inliers)
{
    // reprocess ----------------------------

    //        cout<<"num_inlier_indices_ in" << iter <<"-th gnc:"<<final_inliers.size()<<endl;

    BearingVector v1_inliers;
    BearingVector v2_inliers;
    obtain_inlier_matches(v1, v2, final_inliers, v1_inliers, v2_inliers);

    auto Rt_est_ = solve_6pts_main(v1_inliers, v2_inliers, nullptr);
    Matrix3d R_est = Rt_est_.rotations;
    Vector3d t_est = Rt_est_.translations;

    VectorXd residuals = residual_LiRT(v1, v2, Rt_est_, nullptr);
    double med_residual = findMedian(residuals);

    return med_residual;
}

bool IRLS_process(const BearingVector &v1,
                  const BearingVector &v2,
                  const double &bounder,
                  const double &sigma,
                  GNCInfo &gnc_info,
                  vector<int> &final_inliers)
{
    bool is_success = true;

    int num_inlier_indices = final_inliers.size();

    // reprocess ----------------------------
    {
        //        cout<<"num_inlier_indices_ in" << iter <<"-th gnc:"<<final_inliers.size()<<endl;

        BearingVector v1_inliers;
        BearingVector v2_inliers;
        v1_inliers.setZero(3, num_inlier_indices);
        v2_inliers.setZero(3, num_inlier_indices);

        for (int i = 0; i < final_inliers.size(); i++)
        {
            int id_inlier = final_inliers[i];
            v1_inliers.col(i) = v1.col(id_inlier);
            v2_inliers.col(i) = v2.col(id_inlier);
        }

        gnc_info.SetSigmaMode(-1, sigma);
        gnc_info.SetTLSBounder(bounder);
        is_success = gnc_info.GNC_IRLSp(v1_inliers, v2_inliers);
        if (is_success)
        {
            cout << "minimal inlier ids: ";
            for (int i = 0; i < min_six_ids.size(); ++i)
            {
                cout << final_inliers[min_six_ids[i]] << " ";
            }
            cout << endl;

            cout << "maximal inlier ids: ";
            for (int i = 0; i < max_six_ids.size(); ++i)
            {
                cout << final_inliers[max_six_ids[i]] << " ";
            }
            cout << endl;
        }
        vector<int> tmp_inliers;

        for (auto id = 0; id < final_inliers.size(); ++id)
        {
            if (gnc_info.weights_(id) > 0.5)
            {
                tmp_inliers.emplace_back(final_inliers[id]);
            }
        }

        final_inliers.swap(tmp_inliers);
        tmp_inliers.clear();
    }

    cout << endl
         << "num_inlier_indices_ in final gnc:" << final_inliers.size() << endl;

    for (int i = 0; i < final_inliers.size(); i++)
    {
        cout << final_inliers[i] << " ";
    }
    cout << endl;

    // reprocess end ----------------------------
    return is_success;
}

bool MSTLS_process(const BearingVector &v1,
                   const BearingVector &v2,
                   const double &bounder,
                   const double &sigma,
                   GNCInfo &gnc_info,
                   vector<int> &final_inliers)
{
    bool is_success = true;

    int num_inlier_indices = final_inliers.size();

    // reprocess ----------------------------
    {
        //        cout<<"num_inlier_indices_ in" << iter <<"-th gnc:"<<final_inliers.size()<<endl;

        BearingVector v1_inliers;
        BearingVector v2_inliers;
        v1_inliers.setZero(3, num_inlier_indices);
        v2_inliers.setZero(3, num_inlier_indices);

        for (int i = 0; i < final_inliers.size(); i++)
        {
            int id_inlier = final_inliers[i];
            v1_inliers.col(i) = v1.col(id_inlier);
            v2_inliers.col(i) = v2.col(id_inlier);
        }

        gnc_info.SetSigmaMode(-1, sigma);
        gnc_info.SetTLSBounder(bounder);
        is_success = gnc_info.GNC_IRLS2(v1_inliers, v2_inliers);
        if (is_success)
        {
            cout << "minimal inlier ids: ";
            for (int i = 0; i < min_six_ids.size(); ++i)
            {
                cout << final_inliers[min_six_ids[i]] << " ";
                min_matches_ids.emplace_back(final_inliers[min_six_ids[i]]);
            }
            cout << endl;

            cout << "maximal inlier ids: ";
            for (int i = 0; i < max_six_ids.size(); ++i)
            {
                cout << final_inliers[max_six_ids[i]] << " ";
                max_matches_ids.emplace_back(final_inliers[max_six_ids[i]]);
            }
            cout << endl;
        }
        vector<int> tmp_inliers;

        for (auto id = 0; id < final_inliers.size(); ++id)
        {
            if (gnc_info.weights_(id) > 0.1)
            {
                tmp_inliers.emplace_back(final_inliers[id]);
            }
        }

        final_inliers.swap(tmp_inliers);
        tmp_inliers.clear();
    }

    cout << endl
         << "num_inlier_indices_ in final gnc:" << final_inliers.size() << endl;

    for (int i = 0; i < final_inliers.size(); i++)
    {
        cout << final_inliers[i] << " ";
    }
    cout << endl;

    // reprocess end ----------------------------
    return is_success;
}

bool MSTLS_process(const BearingVector &v1,
                   const BearingVector &v2,
                   GNCInfo &gnc_info,
                   vector<int> &final_inliers)
{
    bool is_success = true;

    int num_inlier_indices = final_inliers.size();

    // reprocess ----------------------------
    {
        //        cout<<"num_inlier_indices_ in" << iter <<"-th gnc:"<<final_inliers.size()<<endl;

        BearingVector v1_inliers;
        BearingVector v2_inliers;
        v1_inliers.setZero(3, num_inlier_indices);
        v2_inliers.setZero(3, num_inlier_indices);

        for (int i = 0; i < final_inliers.size(); i++)
        {
            int id_inlier = final_inliers[i];
            v1_inliers.col(i) = v1.col(id_inlier);
            v2_inliers.col(i) = v2.col(id_inlier);
        }

        gnc_info.SetSigmaMode(2);
        is_success = gnc_info.GNC_IRLS(v1_inliers, v2_inliers);

        vector<int> tmp_inliers;

        for (auto id = 0; id < final_inliers.size(); ++id)
        {
            if (gnc_info.weights_(id) > 0.1)
            {
                tmp_inliers.emplace_back(final_inliers[id]);
            }
        }

        final_inliers.swap(tmp_inliers);
        tmp_inliers.clear();
    }

    cout << endl
         << "num_inlier_indices_ in final gnc:" << final_inliers.size() << endl;

    for (int i = 0; i < final_inliers.size(); i++)
    {
        cout << final_inliers[i] << " ";
    }
    cout << endl;

    // reprocess end ----------------------------
    return is_success;
}

Pose RT_Check(const MatrixXd &A,
              const BearingVector &v1,
              const BearingVector &v2,
              const vector<Matrix3d> &R_sols,
              const vector<Vector3d> &t_sols,
              double &ratio)
{
    int n = v1.cols();

    // use normalized coordinates [not bearing vec]
    Matrix<double, 3, Dynamic> x, xmatch;
    x.setZero(3, v2.cols());
    xmatch.setZero(3, v1.cols());

    for (int i = 0; i < n; ++i)
    {
        x.col(i) = v2.col(i) / v2(2, i);
        xmatch.col(i) = v1.col(i) / v1(2, i);
    }

    // formulate H matrix
    MatrixXd H(n, 6);
    for (int i = 0; i < n; ++i)
    {
        H.row(i) << xmatch.col(i).norm() * x.col(i).transpose(), x.col(i).norm() * xmatch.col(i).transpose();
    }
    //    cout<<"Hmat:\n"<<H<<endl;

    vector<int> SR(R_sols.size(), 0); // the number of pair points satisfied with M1(R)
    vector<int> ST(t_sols.size(), 0); // the number of pair points satisfied with M2(t)

    // M1(R) constraint
    for (size_t i = 0; i < 2; ++i)
    {
        Matrix3d tx;
        Vector3d t = t_sols[i];

        tx << 0, -t(2), t(1),
            t(2), 0, -t(0),
            -t(1), t(0), 0;

        Matrix3d Q = tx.transpose() * tx * R_sols[i];

        SR[i] = (A * Map<VectorXd>(Q.data(), 9)).array().cast<double>().unaryExpr([](double val)
                                                                                  { return val > 0 ? 1 : 0; })
                    .sum();
    }
    int index_R = SR[0] > SR[1] ? 0 : 1;

    Pose right_pose;
    right_pose.rotations = R_sols[index_R];

    // M2(t)

    Vector2d tmp_ratio;
    for (int i = 0; i < 2; ++i)
    {
        Matrix<double, 6, 1> combinedVector; // 6x1 vector to store the combined result
        combinedVector.head(3) = -right_pose.rotations.transpose() * t_sols[i];
        combinedVector.tail(3) = Matrix3d::Identity() * t_sols[i];

        VectorXd result = H * combinedVector;
        ST[i] = (result.array() > 0).count();
        tmp_ratio[i] = result.sum();
    }

    //    ratio = max(ST[0],ST[1]) / n;

    int index_t = ST[0] > ST[1] ? 0 : 1;
    right_pose.translations = t_sols[index_t];
    ratio = tmp_ratio[index_t];

    return right_pose;
}

// 更新了Rt_check中的ratio部分
Pose RT_Check2(const MatrixXd &A,
               const BearingVector &v1,
               const BearingVector &v2,
               const vector<Matrix3d> &R_sols,
               const vector<Vector3d> &t_sols,
               const VectorXd *ptr_weigths,
               double &ratio)
{
    int n = v1.cols();

    // use normalized coordinates [not bearing vec]
    Matrix<double, 3, Dynamic> x, xmatch;
    x.setZero(3, v2.cols());
    xmatch.setZero(3, v1.cols());

    for (int i = 0; i < n; ++i)
    {
        x.col(i) = v2.col(i) / v2(2, i);
        xmatch.col(i) = v1.col(i) / v1(2, i);
    }

    // formulate H matrix
    MatrixXd H(n, 6);
    for (int i = 0; i < n; ++i)
    {
        H.row(i) << xmatch.col(i).norm() * x.col(i).transpose(), x.col(i).norm() * xmatch.col(i).transpose();
        if (ptr_weigths)
            H.row(i) *= (*ptr_weigths)[i];
    }
    //    cout<<"Hmat:\n"<<H<<endl;

    vector<int> SR(R_sols.size(), 0); // the number of pair points satisfied with M1(R)
    vector<int> ST(t_sols.size(), 0); // the number of pair points satisfied with M2(t)

    // M1(R) constraint
    for (size_t i = 0; i < 2; ++i)
    {
        Matrix3d tx;
        Vector3d t = t_sols[i];

        tx << 0, -t(2), t(1),
            t(2), 0, -t(0),
            -t(1), t(0), 0;

        Matrix3d Q = tx.transpose() * tx * R_sols[i];

        SR[i] = (A * Map<VectorXd>(Q.data(), 9)).array().cast<double>().unaryExpr([](double val)
                                                                                  { return val > 0 ? 1 : 0; })
                    .sum();
    }

    int index_R = SR[0] > SR[1] ? 0 : 1;

    Pose right_pose;
    right_pose.rotations = R_sols[index_R];

    // M2(t)

    Vector2d tmp_ratio;
    for (int i = 0; i < 2; ++i)
    {
        Matrix<double, 6, 1> combinedVector; // 6x1 vector to store the combined result
        combinedVector.head(3) = -right_pose.rotations.transpose() * t_sols[i];
        combinedVector.tail(3) = Matrix3d::Identity() * t_sols[i];

        VectorXd result = H * combinedVector;
        //        ST[i] = (result.array() > 0).count();
        //        tmp_ratio[i] = result.sum();
        ST[i] = 0;
        for (int j = 0; j < n; ++j)
        {
            Vector3d tmp_1 = crossMatrix(t_sols[i]) * right_pose.rotations * x.col(j);
            Vector3d tmp_2 = crossMatrix(xmatch.col(j)) * right_pose.rotations * x.col(j);
            Vector3d tmp_3 = crossMatrix(t_sols[i]) * xmatch.col(j);
            double sside1_ratio = tmp_1.transpose() * tmp_2 > 0 ? 1 : 0;
            double sside2_ratio = tmp_2.transpose() * tmp_3 > 0 ? 1 : 0;
            ST[i] += sside1_ratio + sside2_ratio;
        }
    }

    ratio = max(ST[0], ST[1]);

    int index_t = ST[0] > ST[1] ? 0 : 1;
    right_pose.translations = t_sols[index_t];
    //    ratio = tmp_ratio[index_t];

    return right_pose;
}

void essential2RT(const Matrix3d &E,
                  vector<Matrix3d> &R,
                  vector<Vector3d> &t)
{
    Matrix3d normalizedE = E / E.norm();

    Matrix3d W1;
    W1 << 0, -1, 0,
        1, 0, 0,
        0, 0, 1;

    Matrix3d W2;
    W2 << 0, -1, 0,
        1, 0, 0,
        0, 0, -1;

    JacobiSVD<Matrix3d> svd(normalizedE, ComputeFullU | ComputeFullV);
    Matrix3d U = svd.matrixU();
    Matrix3d V = svd.matrixV();

    R.resize(2);
    if (U.determinant() * V.determinant() > 0)
    {
        R[0] = U * W1 * V.transpose();
        R[1] = U * W1.transpose() * V.transpose();
    }
    else
    {
        R[0] = U * W2 * V.transpose();
        R[1] = U * W2.transpose() * V.transpose();
    }

    t.resize(2);
    t[0] = U.col(2);
    t[1] = -U.col(2);
}

void load_weights_file(const std::string &weights_file,
                       VectorXd &weights)
{

    POVG::Timer timer;

    // load weights file
    std::fstream sfile(weights_file, ios::in);
    if (!sfile.is_open())
    {
        std::cerr << "weights file cannot load";
        return;
    }

    int num_matches;
    sfile >> num_matches;

    weights.setZero(num_matches, 1);

    for (unsigned int i = 0; i < num_matches; ++i)
    {
        sfile >> weights(i);
    }

    sfile.close();

    // print time cost information
    //    cout << ">> time for loading eigen sols file: "
    //         << timer.Duration()
    //         << "s"
    //         << endl;
}

void load_eigen_sols_info(const std::string &eigen_sols_file,
                          EigenSols &eigen_sols)
{

    POVG::Timer timer;

    // load tracks file
    std::fstream sfile(eigen_sols_file, ios::in);
    if (!sfile.is_open())
    {
        std::cerr << "eigen sols file cannot load";
        return;
    }

    for (unsigned int i = 0; i < 3; ++i)
    {
        for (unsigned int j = 0; j < 9; ++j)
        {
            sfile >> eigen_sols(i, j);
        }
    }

    sfile.close();

    // print time cost information
    //    cout << ">> time for loading eigen sols file: "
    //         << timer.Duration()
    //         << "s"
    //         << endl;
}

void load_bearing_info(const std::string &bearing_file,
                       BearingVector &v1,
                       BearingVector &v2)
{

    POVG::Timer timer;

    std::fstream sfile(bearing_file, ios::in);
    if (!sfile.is_open())
    {
        std::cerr << "bearing file cannot load";
        return;
    }

    int num_matches;
    sfile >> num_matches;

    v1.setZero(3, num_matches);
    v2.setZero(3, num_matches);

    for (unsigned int i = 0; i < num_matches; ++i)
    {
        sfile >> v1(0, i) >> v1(1, i) >> v1(2, i) >> v2(0, i) >> v2(1, i) >> v2(2, i);
    }

    sfile.close();
}
auto findRoots(const Eigen::VectorXd &coefficients)
{

    Eigen::VectorXd p = coefficients;
    p = p / p(0); // 转换为最高次系数为1的标准形式

    MatrixXd matrixXd(p.size() - 1, p.size() - 1);
    for (int i = 0; i < p.size() - 1; ++i)
    {
        matrixXd(i, 0) = -p(i + 1);
        for (int j = 1; j < p.size() - 1; ++j)
        {
            if (i + 1 == j)
                matrixXd(i, j) = 1;
            else
                matrixXd(i, j) = 0;
        }
    }

    auto evalue = matrixXd.eigenvalues();

    return evalue;
}

template <typename Derived>
double minEigenvalueDistance(const Eigen::MatrixBase<Derived> &C)
{
    // Ensure that the matrix is square
    static_assert(Derived::RowsAtCompileTime == Derived::ColsAtCompileTime,
                  "Matrix must be square.");

    // Compute the eigenvalues using Eigen's Eigenvalue solver
    Eigen::EigenSolver<Eigen::Matrix<typename Derived::Scalar, Derived::RowsAtCompileTime, Derived::ColsAtCompileTime>> es(C);
    auto eigenvalues = es.eigenvalues();

    double minDist = std::numeric_limits<double>::infinity();

    // Iterate through all unique pairs of eigenvalues to find the minimum distance
    for (int i = 0; i < eigenvalues.size(); ++i)
    {
        for (int j = i + 1; j < eigenvalues.size(); ++j)
        {
            // Calculate the distance between the two complex numbers
            std::complex<double> diff = eigenvalues[i] - eigenvalues[j];
            double distance = std::abs(diff); // std::abs computes magnitude of complex number

            // Update the minimum distance if the current one is smaller
            if (distance < minDist)
            {
                minDist = distance;
            }
        }
    }

    // If all eigenvalues are the same, the distance will be zero
    if (eigenvalues.size() == 1)
    {
        minDist = 0;
    }

    return minDist;
}

void solve_6pts(const EigenSols &eigen_sols,
                const Matrix<double, 9, 10, ColMajor> &B,
                MatrixXd &Evec_real,
                const bool &flags_keep_real)
{

    Matrix<double, 4, 6, ColMajor> M;
    M = B.leftCols(4).colPivHouseholderQr().solve(B.rightCols(B.cols() - 4));

    //    // 进行SVD分解
    //    Eigen::JacobiSVD<Eigen::MatrixXd> svd(B, Eigen::ComputeThinU | Eigen::ComputeThinV);
    //    Eigen::MatrixXd Vb = svd.matrixV();
    //    Eigen::VectorXd singularValues = svd.singularValues();

    //    // 获取与最大4个奇异值对应的奇异向量
    //    Eigen::MatrixXd VbLargest4 = Vb.leftCols(4);

    //    // 将 Vb 转置
    //    VbLargest4.transposeInPlace();

    //    //    std::cout << "VbLargest4:\n" << VbLargest4.leftCols(4).colPivHouseholderQr().inverse()<< std::endl;

    //    // 计算 M
    ////    Eigen::MatrixXd M;
    //    M = VbLargest4.leftCols(4).colPivHouseholderQr().solve(VbLargest4.rightCols(VbLargest4.cols()-4));

    // solve x-basis polynomial sols
    Matrix<double, 6, 6, ColMajor> C1;

    C1.topRows<3>() = -M.topRows<3>();
    C1.bottomRows<3>() << 1, 0, 0, 0, 0, 0,
        0, 1, 0, 0, 0, 0,
        0, 0, 0, 1, 0, 0;

    EigenSolver<Matrix<double, 6, 6, ColMajor>> es1(C1);
    Matrix<double, 6, 6> V1 = es1.pseudoEigenvectors();

    Matrix<double, 2, 6> SOLS1 = V1.block(3, 0, 2, V1.cols()).array() / V1.row(5).replicate(2, 1).array();
    Matrix<double, 9, 6> Evec1 = eigen_sols.transpose() * (MatrixXd(SOLS1.rows() + 1, SOLS1.cols()) << SOLS1, MatrixXd::Ones(1, SOLS1.cols())).finished();

    // solve y-basis polynomial sols
    Matrix<double, 6, 6, ColMajor> C2;

    C2.topRows<3>() = -M.middleRows<3>(1);
    C2.bottomRows<3>() << 0, 1, 0, 0, 0, 0,
        0, 0, 1, 0, 0, 0,
        0, 0, 0, 0, 1, 0;

    EigenSolver<Matrix<double, 6, 6, ColMajor>> es2(C2);
    Matrix<double, 6, 6> V2 = es2.pseudoEigenvectors();

    Matrix<double, 2, 6> SOLS2 = V2.block(3, 0, 2, V2.cols()).array() / V2.row(5).replicate(2, 1).array();
    Matrix<double, 9, 6> Evec2 = eigen_sols.transpose() * (MatrixXd(SOLS2.rows() + 1, SOLS2.cols()) << SOLS2, MatrixXd::Ones(1, SOLS2.cols())).finished();

    //  //   solve c=0 and b=1 with Det(E)=0 to solve a

    Matrix<double, 1, 4> coeff_D_mat;
    compute_Dcoefficients(eigen_sols, coeff_D_mat);

    Matrix<double, 3, 3, ColMajor> C3;

    C3.topRows<1>() = -coeff_D_mat.rightCols(3) / coeff_D_mat(0);
    C3.bottomRows<2>() << 1, 0, 0,
        0, 1, 0;

    EigenSolver<Matrix<double, 3, 3, ColMajor>> es3(C3);

    Matrix3d V3 = es3.pseudoEigenvectors();
    Vector3d a_sols;
    a_sols << V3(1, 0) / V3(2, 0), V3(1, 1) / V3(2, 1), V3(1, 2) / V3(2, 2);

    Matrix3d SOLS3;
    SOLS3 << a_sols, Vector3d::Ones(3), Vector3d::Zero(3);

    Matrix<double, 9, 3> Evec3 = eigen_sols.transpose() * SOLS3;

    //     ------------------ module of d_min ---------------------------------
    //     for testing the singularity problem of 5pts/LiRP/npt
    //     --------------------------------------------------------------------
    //    Vector3d minDists;
    //    minDists(0) = minEigenvalueDistance(C1);
    //    minDists(1) = minEigenvalueDistance(C2);
    //    minDists(2) = minEigenvalueDistance(C3);
    //    std::cout << "Minimum eigenvalue distances: " << minDists.transpose() << std::endl;
    //    std::cout << "Minimum eigenvalue distance: " << minDists.maxCoeff() << std::endl;

    //    // Specify the file path
    //    std::string file_path = "/home/<USER>/min_eigen_dist.txt";

    //    // Create an ofstream object for writing to file
    //    std::ofstream out_file(file_path);

    //    // Check if the file is open (i.e., successfully created)
    //    if (out_file.is_open()) {
    //        // Write the minimum distance to the file
    //        out_file << minDists.maxCoeff() << std::endl;
    //        // Close the file
    //        out_file.close();
    //        std::cout << "Minimum eigenvalue distance was written to " << file_path << std::endl;
    //    } else {
    //        std::cerr << "Unable to open file at " << file_path << std::endl;
    //    }

    //    -----------------------------------------------------------------------

    // Solutions
    Matrix<double, 9, 15> Evec;
    Evec << Evec1, Evec2, Evec3;

    for (int i = 0; i < Evec.cols(); ++i)
    {
        Evec.col(i).normalize();
    }

    if (flags_keep_real)
    {
        Evec_real = Evec.real();
    }
    else
    {
        vector<int> I;
        for (size_t i = 0; i < Evec.cols(); ++i)
        {
            if ((Evec.col(i).unaryExpr([](const std::complex<double> &value)
                                       { return value.imag(); })
                     .array() == 0)
                    .all())
                I.push_back(i);
        }

        Evec_real.setZero(Evec.rows(), I.size());

        for (size_t i = 0; i < I.size(); ++i)
        {
            Evec_real.col(i) = Evec.col(I[i]);
        }
    }

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;

        LOG(INFO) << "Solved eigen_sols from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << eigen_sols;

        LOG(INFO) << "Solved B matrix from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << B;

        LOG(INFO) << "Solved M matrix from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << M;

        LOG(INFO) << "Solved Evec_real from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << Evec_real;

        LOG(INFO) << endl;
    }
}

void solve_6pts_x(const EigenSols &eigen_sols,
                  const Matrix<double, 9, 10, ColMajor> &B,
                  MatrixXd &Evec_real,
                  const bool &flags_keep_real)
{

    Matrix<double, 4, 6, ColMajor> M;
    M = B.leftCols(4).colPivHouseholderQr().solve(B.rightCols(B.cols() - 4));

    Matrix<double, 6, 6, ColMajor> C1;

    C1.topRows<3>() = -M.topRows<3>();
    C1.bottomRows<3>() << 1, 0, 0, 0, 0, 0,
        0, 1, 0, 0, 0, 0,
        0, 0, 0, 1, 0, 0;

    EigenSolver<Matrix<double, 6, 6, ColMajor>> es1(C1);
    Matrix<double, 6, 6> V1 = es1.pseudoEigenvectors();

    Matrix<double, 2, 6> SOLS1 = V1.block(3, 0, 2, V1.cols()).array() / V1.row(5).replicate(2, 1).array();
    Matrix<double, 9, 6> Evec = eigen_sols.transpose() * (MatrixXd(SOLS1.rows() + 1, SOLS1.cols()) << SOLS1, MatrixXd::Ones(1, SOLS1.cols())).finished();

    for (int i = 0; i < Evec.cols(); ++i)
    {
        Evec.col(i).normalize();
    }

    if (flags_keep_real)
    {
        Evec_real = Evec.real();
    }
    else
    {
        vector<int> I;
        for (size_t i = 0; i < Evec.cols(); ++i)
        {
            if ((Evec.col(i).unaryExpr([](const std::complex<double> &value)
                                       { return value.imag(); })
                     .array() == 0)
                    .all())
                I.push_back(i);
        }

        Evec_real.setZero(Evec.rows(), I.size());

        for (size_t i = 0; i < I.size(); ++i)
        {
            Evec_real.col(i) = Evec.col(I[i]);
        }
    }

    if (FLAGS_debug_info)
    {
        LOG(INFO) << endl;

        LOG(INFO) << "Solved eigen_sols from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << eigen_sols;

        LOG(INFO) << "Solved B matrix from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << B;

        LOG(INFO) << "Solved M matrix from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << M;

        LOG(INFO) << "Solved Evec_real from C++ :";
        LOG(INFO) << endl
                  << setfill(' ') << setprecision(16) << Evec_real;

        LOG(INFO) << endl;
    }
}

double findMedian(VectorXd a)
{
    int n = a.size();

    // 创建一个临时向量并复制数据
    VectorXd temp = a;

    // 排序临时向量
    std::sort(temp.data(), temp.data() + temp.size());

    // 如果数组大小是偶数
    if (n % 2 == 0)
    {
        return (temp[(n - 1) / 2] + temp[n / 2]) / 2.0;
    }
    // 如果数组大小是奇数
    else
    {
        return temp[n / 2];
    }
}

double findMedian(vector<double> a)
{

    int n = a.size();
    // If size of the arr[] is even
    if (n % 2 == 0)
    {

        // Applying nth_element
        // on n/2th index
        nth_element(a.begin(),
                    a.begin() + n / 2,
                    a.end());

        // Applying nth_element
        // on (n-1)/2 th index
        nth_element(a.begin(),
                    a.begin() + (n - 1) / 2,
                    a.end());

        // Find the average of value at
        // index N/2 and (N-1)/2
        return (double)(a[(n - 1) / 2] + a[n / 2]) / 2.0;
    }

    // If size of the arr[] is odd
    else
    {

        // Applying nth_element
        // on n/2
        nth_element(a.begin(),
                    a.begin() + n / 2,
                    a.end());

        // Value at index (N/2)th
        // is the median
        return (double)a[n / 2];
    }
}

Matrix<double, 3, 4> processEvec(const MatrixXd &Evec_sols,
                                 const BearingVector &v1,
                                 const BearingVector &v2,
                                 const MatrixXd &A,
                                 const VectorXd *ptr_weigths)
{
    int num_matches = v1.cols();
    // Filter out columns in Evec with NaN
    vector<int> I;
    for (int i = 0; i < Evec_sols.cols(); ++i)
    {
        if (!isnan(Evec_sols.col(i).sum()))
        { // Check if no element is NaN
            I.push_back(i);
        }
    }

    // Select valid columns only
    Matrix<double, 9, Dynamic> Evec(9, I.size());
    for (size_t i = 0; i < I.size(); ++i)
    {
        Evec.col(i) = Evec_sols.col(I[i]);
    }

    int numSolutions = Evec.cols();
    if (numSolutions == 0)
    {
        Matrix<double, 3, 4> Out = Matrix<double, 3, 4>::Constant(numeric_limits<double>::quiet_NaN());
        // Assuming 'residual' is a vector that should be returned or modified as well. Its declaration is needed outside this code.
        // residual.clear();
        return Out;
    }

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);
    vector<double> cost;
    cost.resize(num_matches);

    VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
    vector<Pose> right_Rts;

    VectorXd ratios(numSolutions);

    for (int i = 0; i < numSolutions; ++i)
    {
        // Reshape Evec column to 3x3 matrix
        Matrix3d E;

        for (int col = 0; col < 3; ++col)
        {
            for (int row = 0; row < 3; ++row)
            {
                E(row, col) = Evec(row + 3 * col, i);
            }
        }
        //        cout<<"E:\n"<<E<<endl;

        E /= E.norm();

        essential2RT(E, R_sol, t_sol); // Assuming essential2RT is available and correctly defined in C++

        //        cout<<"Rsols:\n"<<R_sol[0]<<endl<<R_sol[1]<<endl;

        double ratio = 0;
        Pose right_pose = RT_Check2(A, v1, v2, R_sol, t_sol, ptr_weigths, ratio);
        ratios(i) = ratio;

        right_pose.translations /= right_pose.translations.norm();
        auto tmp_cost = residual_PPO(v1, v2, right_pose);
        for (int k = 0; k < num_matches; ++k)
        {
            //            cost[k] = TwoViewLiGT(v1.col(k), v2.col(k), right_pose.rotations, right_pose.translations);  // Assuming TwoViewLiGT is available and correctly defined
            cost[k] = tmp_cost[k]; // Assuming TwoViewLiGT is available and correctly defined
        }

        total_cost(i) = findMedian(cost);

        right_Rts.emplace_back(right_pose);
    }

    vector<int> candidate_ids;
    for (int i = 0; i < total_cost.size(); ++i)
    {
        if (total_cost(i) < 1e-10)
            candidate_ids.emplace_back(i);
    }

    //    cout <<"total_cost="<<total_cost.transpose()<<endl;
    int min_id;
    int selected_id;

    double max_value = 0;
    if (candidate_ids.size() > 1)
    {
        for (int i = 0; i < candidate_ids.size(); ++i)
        {
            int id = candidate_ids[i];
            if (ratios(id) > max_value)
            {
                selected_id = id;
                max_value = ratios(id);
            }
        }
        cout << "warning: potential multi-solutions!" << endl;
    }
    else
    {
        double min_cost = total_cost.minCoeff(&min_id);
        selected_id = min_id;
    }

    //    double min_cost = total_cost.minCoeff(&min_id);
    //    selected_id = min_id;

    //    cout<<"total_cost:"<<total_cost.transpose()<<endl;
    Matrix<double, 3, 4> Out;
    Out.block<3, 3>(0, 0) = right_Rts[selected_id].rotations;
    Out.col(3) = right_Rts[selected_id].translations;

    return Out;
}

std::vector<int> findTwoSmallestIndices(const Eigen::VectorXd &vec)
{
    int index1 = -1, index2 = -1;
    double smallest = std::numeric_limits<double>::max();
    double secondSmallest = std::numeric_limits<double>::max();

    // Find the index of the smallest and second smallest value
    for (int i = 0; i < vec.size(); ++i)
    {
        if (vec[i] < smallest)
        {
            secondSmallest = smallest;
            index2 = index1;

            smallest = vec[i];
            index1 = i;
        }
        else if (vec[i] < secondSmallest)
        {
            secondSmallest = vec[i];
            index2 = i;
        }
    }

    std::vector<int> indices = {index1, index2};
    return indices;
}

double computeSolutionCostsOpenGV(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                                  const MatrixXd &A,
                                  const BearingVector &v1,
                                  const BearingVector &v2,
                                  const bool &evec_mode,
                                  const VectorXd *ptr_weigths,
                                  Matrix<double, 3, 4> &out)
{
    int numSolutions = Evec.cols();
    int num_matches = v1.cols();

    double selected_cost = 0;
    vector<double> cost(num_matches);
    vector<double> cost2(num_matches);

    VectorXd ratios(numSolutions);

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);

    VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);

    vector<Pose> right_Rts;

    // load gt Rt
    Pose gt_pose;
    if (echo_on)
    {
        load_pose("/home/<USER>/gt_relative_Rt.txt", gt_pose);
        cout << "========" << numSolutions << " Sols check ========" << endl;
    }

    // start
    for (int i = 0; i < numSolutions; ++i)
    {
        Matrix3d E;
        for (int col = 0; col < 3; ++col)
        {
            for (int row = 0; row < 3; ++row)
            {
                E(row, col) = Evec(row + 3 * col, i);
            }
        }

        if (E.norm() < 1e-10)
        {
            ratios(i) = 0;
            total_cost[i] = 1e10;
            continue;
        }

        E /= E.norm();

        essential2RT(E, R_sol, t_sol);

        double ratio = 0;
        Pose right_pose = RT_Check2(A, v1, v2, R_sol, t_sol, ptr_weigths, ratio);
        ratios(i) = ratio;

        right_pose.translations /= right_pose.translations.norm();

        VectorXd tmp_cost2;

        auto tmp_cost = residual_opengv(v1, v2, right_pose);

        for (int k = 0; k < num_matches; ++k)
        {
            cost[k] = tmp_cost[k];
        }

        double med_cost = findMedian(cost);
        double sum_cost = tmp_cost.sum();

        if (use_med)
        {
            total_cost[i] = med_cost;
        }
        else
        {
            total_cost[i] = sum_cost;
        }

        right_Rts.emplace_back(right_pose);

        if (echo_on)
        {
            // Step 1 and 2: Transpose and multiply
            Eigen::Matrix3d R = gt_pose.rotations.block<3, 3>(0, 0).transpose() * right_pose.rotations.block<3, 3>(0, 0);

            // Step 3: Convert to axis-angle (Rodrigues vector)
            Eigen::AngleAxisd angleAxis(R); // This will create an AngleAxis object from the rotation matrix

            // Step 4: The norm of Rodrigues vector is the angle in the axis-angle representation
            double rotation_error = angleAxis.angle(); // This is the magnitude of the Rodrigues vector

            printf("gt errors:%.3f, ratio:%.0f, sum:%.1e, med:%.1e\n",
                   rotation_error, ratios[i], sum_cost, med_cost);
        }
    }

    int selected_id;

    if (evec_mode)
    {
        int min_id;
        selected_cost = total_cost.minCoeff(&min_id);

        selected_id = min_id;
    }
    else
    {

        vector<int> candidate_ids = findTwoSmallestIndices(total_cost);
        double max_value = -1e5;
        for (int j = 0; j < candidate_ids.size(); ++j)
        {
            int id = candidate_ids[j];
            if (ratios(id) > max_value)
            {
                selected_id = id;
                max_value = ratios(id);
            }
        }

        selected_cost = total_cost(selected_id);
        //        selected_cost = ratios(selected_id);
    }

    out.block<3, 3>(0, 0) = right_Rts[selected_id].rotations;
    out.col(3) = right_Rts[selected_id].translations;

    return selected_cost;
}

double computeSolutionCosts(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                            const MatrixXd &A,
                            const BearingVector &v1,
                            const BearingVector &v2,
                            const bool &evec_mode,
                            const VectorXd *ptr_weigths,
                            Matrix<double, 3, 4> &out)
{
    int numSolutions = Evec.cols();
    int num_matches = v1.cols();

    double selected_cost = 0;
    vector<double> cost(num_matches);
    vector<double> cost2(num_matches);

    VectorXd ratios(numSolutions);

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);

    VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);

    vector<Pose> right_Rts;

    // load gt Rt
    Pose gt_pose;
    if (echo_on)
    {
        load_pose("/home/<USER>/gt_relative_Rt.txt", gt_pose);
        cout << "========" << numSolutions << " Sols check ========" << endl;
    }

    // start
    for (int i = 0; i < numSolutions; ++i)
    {
        Matrix3d E;
        for (int col = 0; col < 3; ++col)
        {
            for (int row = 0; row < 3; ++row)
            {
                E(row, col) = Evec(row + 3 * col, i);
            }
        }

        if (E.norm() < 1e-10)
        {
            ratios(i) = 0;
            total_cost[i] = 1e10;
            continue;
        }

        E /= E.norm();

        essential2RT(E, R_sol, t_sol);

        double ratio = 0;
        Pose right_pose = RT_Check2(A, v1, v2, R_sol, t_sol, ptr_weigths, ratio);
        ratios(i) = ratio;

        right_pose.translations /= right_pose.translations.norm();

        VectorXd tmp_cost2;

        auto tmp_cost = residual_PPO(v1, v2, right_pose);
        //        auto tmp_cost = residual_LiRT(v1, v2, right_pose);
        for (int k = 0; k < num_matches; ++k)
        {
            if (ptr_weigths)
                cost[k] = (*ptr_weigths)(k)*tmp_cost[k];
            else
                cost[k] = tmp_cost[k];
        }

        double med_cost = findMedian(cost);
        double sum_cost = tmp_cost.sum();

        if (FLAGS_med_or_sum)
        {
            total_cost[i] = med_cost;
        }
        else
        {
            total_cost[i] = sum_cost;
        }

        right_Rts.emplace_back(right_pose);

        if (echo_on)
        {
            // Step 1 and 2: Transpose and multiply
            Eigen::Matrix3d R = gt_pose.rotations.block<3, 3>(0, 0).transpose() * right_pose.rotations.block<3, 3>(0, 0);

            // Step 3: Convert to axis-angle (Rodrigues vector)
            Eigen::AngleAxisd angleAxis(R); // This will create an AngleAxis object from the rotation matrix

            // Step 4: The norm of Rodrigues vector is the angle in the axis-angle representation
            double rotation_error = angleAxis.angle(); // This is the magnitude of the Rodrigues vector

            printf("gt errors:%.3f, ratio:%.0f, sum:%.1e, med:%.1e\n",
                   rotation_error, ratios[i], sum_cost, med_cost);
        }
    }

    int selected_id;

    if (evec_mode)
    {
        int min_id;
        selected_cost = total_cost.minCoeff(&min_id);

        selected_id = min_id;
    }
    else
    {

        vector<int> candidate_ids = findTwoSmallestIndices(total_cost);
        double max_value = -1e5;
        for (int j = 0; j < candidate_ids.size(); ++j)
        {
            int id = candidate_ids[j];
            if (ratios(id) > max_value)
            {
                selected_id = id;
                max_value = ratios(id);
            }
        }

        selected_cost = total_cost(selected_id);
        //        selected_cost = ratios(selected_id);
    }

    out.block<3, 3>(0, 0) = right_Rts[selected_id].rotations;
    out.col(3) = right_Rts[selected_id].translations;

    return selected_cost;
}

double computeSolutionCosts(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                            const MatrixXd &A,
                            const BearingVector &v1,
                            const BearingVector &v2,
                            const bool &evec_mode,
                            const VectorXd *ptr_weigths,
                            const Eval_Residual_func &residual_func,
                            Matrix<double, 3, 4> &out)
{
    int numSolutions = Evec.cols();
    int num_matches = v1.cols();

    double selected_cost = 0;
    double selected_test_cost = 0;
    vector<double> cost(num_matches);
    vector<double> cost2(num_matches);

    VectorXd ratios(numSolutions);

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);

    VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
    VectorXd test_cost = MatrixXd::Zero(numSolutions, 1);

    vector<Pose> right_Rts;

    // load gt Rt
    Pose gt_pose;
    if (echo_on)
    {
        load_pose("/home/<USER>/gt_relative_Rt.txt", gt_pose);
        //        cout<<"========"<< numSolutions << " Sols check ========"<<endl;
    }

    // start
    for (int i = 0; i < numSolutions; ++i)
    {
        Matrix3d E;
        for (int col = 0; col < 3; ++col)
        {
            for (int row = 0; row < 3; ++row)
            {
                E(row, col) = Evec(row + 3 * col, i);
            }
        }

        if (E.norm() < 1e-10)
        {
            ratios(i) = 0;
            total_cost[i] = 1e10;
            continue;
        }

        E /= E.norm();

        essential2RT(E, R_sol, t_sol);

        double ratio = 0;
        Pose right_pose = RT_Check2(A, v1, v2, R_sol, t_sol, ptr_weigths, ratio);
        ratios(i) = ratio;

        right_pose.translations /= right_pose.translations.norm();

        VectorXd tmp_cost2;

        auto tmp_cost = (residual_func)(v1, v2, right_pose, ptr_weigths);

        for (int k = 0; k < num_matches; ++k)
        {
            if (ptr_weigths)
                cost[k] = (*ptr_weigths)(k)*tmp_cost[k];
            else
                cost[k] = tmp_cost[k];
        }

        double med_cost = findMedian(cost);
        double sum_cost = tmp_cost.sum();

        if (FLAGS_med_or_sum)
        {
            total_cost[i] = med_cost;
            test_cost[i] = sum_cost;
        }
        else
        {
            test_cost[i] = med_cost;
            total_cost[i] = sum_cost;
        }

        right_Rts.emplace_back(right_pose);

        //        if (echo_on){
        //            // Step 1 and 2: Transpose and multiply
        //            Eigen::Matrix3d R = gt_pose.rotations.block<3, 3>(0, 0).transpose() * right_pose.rotations.block<3, 3>(0, 0);

        //            // Step 3: Convert to axis-angle (Rodrigues vector)
        //            Eigen::AngleAxisd angleAxis(R); // This will create an AngleAxis object from the rotation matrix

        //            // Step 4: The norm of Rodrigues vector is the angle in the axis-angle representation
        //            double rotation_error = angleAxis.angle(); // This is the magnitude of the Rodrigues vector

        //            printf("gt errors:%.3f, ratio:%.0f, sum:%.1e, med:%.1e\n",
        //                   rotation_error, ratios[i], sum_cost, med_cost);
        //        }
    }

    int selected_id;

    if (evec_mode)
    {
        int min_id;
        total_cost.minCoeff(&min_id);
        selected_id = min_id;
    }
    else
    {

        vector<int> candidate_ids = findTwoSmallestIndices(total_cost);
        double max_value = -1e5;
        for (int j = 0; j < candidate_ids.size(); ++j)
        {
            int id = candidate_ids[j];
            if (ratios(id) > max_value)
            {
                selected_id = id;
                max_value = ratios(id);
            }
        }
        //        selected_cost = ratios(selected_id);
    }
    selected_cost = total_cost(selected_id);
    selected_test_cost = test_cost(selected_id);

    out.block<3, 3>(0, 0) = right_Rts[selected_id].rotations;
    out.col(3) = right_Rts[selected_id].translations;

    if (echo_on)
    {
        // Step 1 and 2: Transpose and multiply
        Eigen::Matrix3d R = gt_pose.rotations.block<3, 3>(0, 0).transpose() * out.block<3, 3>(0, 0);

        // Step 3: Convert to axis-angle (Rodrigues vector)
        Eigen::AngleAxisd angleAxis(R); // This will create an AngleAxis object from the rotation matrix

        // Step 4: The norm of Rodrigues vector is the angle in the axis-angle representation
        double rotation_error = angleAxis.angle(); // This is the magnitude of the Rodrigues vector

        printf("gt errors:%.6f, ratio:%.0f, selected cost:%.6f, test cost:%.6f\n",
               rotation_error, ratios[selected_id], selected_cost, selected_test_cost);
    }

    return selected_cost;
}

double computeSolutionCosts2(const Matrix<double, 9, Eigen::Dynamic> &Evec,
                             const MatrixXd &A,
                             const BearingVector &v1,
                             const BearingVector &v2,
                             const bool &evec_mode,
                             const VectorXd *ptr_weigths,
                             const Eval_Residual_func &residual_func,
                             Matrix<double, 3, 4> &out)
{

    int numSolutions = Evec.cols();
    int num_matches = v1.cols();

    double selected_cost = 0;
    double selected_test_cost = 0;
    vector<double> cost(num_matches);
    vector<double> cost2(num_matches);

    VectorXd ratios(numSolutions);

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);

    VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
    VectorXd test_cost = MatrixXd::Zero(numSolutions, 1);

    vector<Pose> right_Rts;

    // load gt Rt
    Pose gt_pose;
    if (echo_on)
    {
        load_pose("/home/<USER>/gt_relative_Rt.txt", gt_pose);
        //        cout<<"========"<< numSolutions << " Sols check ========"<<endl;
    }

    // start
    right_Rts.resize(numSolutions);

    for (int i = 0; i < numSolutions; ++i)
    {
        Matrix3d E;
        for (int col = 0; col < 3; ++col)
        {
            for (int row = 0; row < 3; ++row)
            {
                E(row, col) = Evec(row + 3 * col, i);
            }
        }

        if (E.norm() < 1e-10)
        {
            ratios(i) = 0;
            total_cost[i] = 1e10;
            continue;
        }

        E /= E.norm();

        essential2RT(E, R_sol, t_sol);

        double ratio = 0;

        double min_Rt_costs = 1e10;

        for (Matrix3d &R_candidate : R_sol)
        {
            for (Vector3d &t_candidate : t_sol)
            {
                t_candidate = t_candidate / t_candidate.norm();
                Pose tmp_pose(R_candidate, t_candidate);

                auto tmp_cost = (residual_func)(v1, v2, tmp_pose, ptr_weigths);

                for (int k = 0; k < num_matches; ++k)
                {
                    if (ptr_weigths)
                        cost[k] = (*ptr_weigths)(k)*tmp_cost[k];
                    else
                        cost[k] = tmp_cost[k];
                }

                double med_cost = findMedian(cost);
                double sum_cost = tmp_cost.sum();

                double Rt_cost;
                if (FLAGS_med_or_sum)
                {
                    Rt_cost = med_cost;
                }
                else
                {
                    Rt_cost = sum_cost;
                }

                if (Rt_cost < min_Rt_costs)
                {
                    min_Rt_costs = Rt_cost;
                    right_Rts[i] = tmp_pose;
                }
            }
        }

        total_cost[i] = min_Rt_costs;
    }

    int selected_id;
    total_cost.minCoeff(&selected_id);

    selected_cost = total_cost(selected_id);
    selected_test_cost = test_cost(selected_id);

    out.block<3, 3>(0, 0) = right_Rts[selected_id].rotations;
    out.col(3) = right_Rts[selected_id].translations;

    if (echo_on)
    {
        // Step 1 and 2: Transpose and multiply
        Eigen::Matrix3d R = gt_pose.rotations.block<3, 3>(0, 0).transpose() * out.block<3, 3>(0, 0);

        // Step 3: Convert to axis-angle (Rodrigues vector)
        Eigen::AngleAxisd angleAxis(R); // This will create an AngleAxis object from the rotation matrix

        // Step 4: The norm of Rodrigues vector is the angle in the axis-angle representation
        double rotation_error = angleAxis.angle(); // This is the magnitude of the Rodrigues vector

        printf("gt errors:%.6f, ratio:%.0f, selected cost:%.6f, test cost:%.6f\n",
               rotation_error, ratios[selected_id], selected_cost, selected_test_cost);
    }

    return selected_cost;
}

// 函数定义：计算最优旋转矩阵
Matrix<double, 3, 4> computeOptimalRt(const vector<Matrix<double, 3, 4>> &Rts,
                                      const Vector4d &cost,
                                      int &used_num_Rts)
{

    Matrix<double, 3, 4> out;
    Matrix3d M = Matrix3d::Zero();

    // 使用 minCoeff 找到最小值及其索引
    int min_index;
    double min_cost = cost.minCoeff(&min_index);

    // 提取前两个元素的旋转矩阵
    double sum_w = 0;
    used_num_Rts = 0;
    Vector3d t_opt = Vector3d::Zero();
    for (int i = 0; i < Rts.size(); ++i)
    {
        if (cost[i] <= 2 * min_cost)
        {
            Matrix3d tmp_R = Rts[i].block<3, 3>(0, 0); // 提取 3x3 部分
            double w_i = 1 / (cost[i] + 1e-8);

            M += w_i * tmp_R;
            t_opt += w_i * Rts[i].col(3);
            sum_w += w_i;
            used_num_Rts++;
        }
    }

    M = M / sum_w;
    t_opt = t_opt / sum_w;

    // 计算 SVD
    JacobiSVD<Matrix3d> svd(M, ComputeFullU | ComputeFullV);
    Matrix3d U = svd.matrixU();
    Matrix3d V = svd.matrixV();

    // 构造最优旋转矩阵
    Matrix3d R_opt = U * V.transpose();

    // 确保 R_opt 是一个有效的旋转矩阵
    if (R_opt.determinant() < 0)
    {
        U.col(2) *= -1;
        R_opt = U * V.transpose();
    }

    out.block<3, 3>(0, 0) = R_opt;
    out.col(3) = t_opt;

    return out;
}

Matrix<double, 3, 4> processEvec2(const MatrixXd &Evec_sols,
                                  const BearingVector &v1,
                                  const BearingVector &v2,
                                  const MatrixXd &A,
                                  const VectorXd *ptr_weigths,
                                  const Eval_Residual_func &residual_func)
{
    int num_matches = v1.cols();
    // Filter out columns in Evec with NaN
    vector<int> I;
    for (int i = 0; i < Evec_sols.cols(); ++i)
    {
        if (!isnan(Evec_sols.col(i).sum()))
        { // Check if no element is NaN
            I.push_back(i);
        }
    }

    // Select valid columns only
    Matrix<double, 9, Dynamic> Evec(9, I.size());
    for (size_t i = 0; i < I.size(); ++i)
    {
        Evec.col(i) = Evec_sols.col(I[i]);
    }

    int numSolutions = Evec.cols();
    if (numSolutions == 0)
    {
        Matrix<double, 3, 4> out = Matrix<double, 3, 4>::Constant(numeric_limits<double>::quiet_NaN());
        // Assuming 'residual' is a vector that should be returned or modified as well. Its declaration is needed outside this code.
        // residual.clear();
        return out;
    }

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);
    vector<double> cost;
    cost.resize(num_matches);

    VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
    vector<Pose> right_Rts;

    VectorXd ratios(numSolutions);

    // solutions: 6, 6, 3, 3 = 18

    vector<Matrix<double, 3, 4>> outs;
    Vector4d selected_cost;

    Matrix<double, 3, 4> tmp_Rt;

    //    echo_on = 1;

    POVG::Timer check_time;
    //    cout<<"compute_mode = "<<compute_mode<<", use_med = "<<FLAGS_med_or_sum<<endl;

    if (FLAGS_computeMode)
    {
        prefix_3dpts = 1;
        selected_cost(0) = computeSolutionCosts(Evec.middleCols<6>(0), A, v1, v2, false, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);

        prefix_3dpts = 2;
        selected_cost(1) = computeSolutionCosts(Evec.middleCols<6>(6), A, v1, v2, false, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);

        prefix_3dpts = 3;
        selected_cost(2) = computeSolutionCosts(Evec.middleCols<3>(12), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);

        prefix_3dpts = 4;
        selected_cost(3) = computeSolutionCosts(Evec.middleCols<3>(15), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);
    }
    else
    {
        prefix_3dpts = 1;
        selected_cost(0) = computeSolutionCosts2(Evec.middleCols<6>(0), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);

        prefix_3dpts = 2;
        selected_cost(1) = computeSolutionCosts2(Evec.middleCols<6>(6), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);

        prefix_3dpts = 3;
        selected_cost(2) = computeSolutionCosts2(Evec.middleCols<3>(12), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);

        prefix_3dpts = 4;
        selected_cost(3) = computeSolutionCosts2(Evec.middleCols<3>(15), A, v1, v2, true, ptr_weigths, residual_func, tmp_Rt);
        outs.emplace_back(tmp_Rt);
    }

    check_time.Duration();
    check_time.WriteTime(FLAGS_idm_time_file);

    //    updateTimerAndLabel(process_times,time_labels,"PPO check time",check_time.Duration());

    int min_id;
    selected_cost.minCoeff(&min_id);

    pose_check_cost = selected_cost(min_id);

    //    // load gt Rt
    //    Pose gt_pose;
    //    load_pose("/home/<USER>/gt_relative_Rt.txt",gt_pose);

    Matrix<double, 3, 4> est_Rt;
    //    int used_num_Rts = 0;

    //    Matrix<double, 3, 4> Rt_opt= computeOptimalRt(outs,selected_cost,used_num_Rts);
    //    if (used_num_Rts == 3){
    //        est_Rt = Rt_opt;
    //    }
    //    else{
    //        est_Rt = outs[min_id];
    //    }
    est_Rt = outs[min_id];

    //    Eigen::Matrix3d Rerr = gt_pose.rotations.block<3, 3>(0, 0).transpose() * est_Rt.block<3,3>(0,0);

    //    // Step 3: Convert to axis-angle (Rodrigues vector)
    //    Eigen::AngleAxisd angleAxis(Rerr); // This will create an AngleAxis object from the rotation matrix

    //    // Step 4: The norm of Rodrigues vector is the angle in the axis-angle representation
    //    double rotation_error = angleAxis.angle(); // This is the magnitude of the Rodrigues vector

    //    cout <<"estR's gt_error = "<<rotation_error<<", min_id = "<<min_id+1<<",used_num_Rts="<<used_num_Rts<<endl;
    //    cout <<"=================================== "<<endl;

    //    {
    //        POVG::Timer check_time2;
    //        prefix_3dpts = 1;
    //        selected_cost(0) = computeSolutionCostsOpenGV(Evec.middleCols<6>(0), A, v1, v2, true, tmp_Rt);
    //        outs.emplace_back(tmp_Rt);

    //        prefix_3dpts = 2;
    //        selected_cost(1) = computeSolutionCostsOpenGV(Evec.middleCols<6>(6), A, v1, v2, true, tmp_Rt);
    //        outs.emplace_back(tmp_Rt);

    //        prefix_3dpts = 3;
    //        selected_cost(2) = computeSolutionCostsOpenGV(Evec.middleCols<3>(12), A, v1, v2, true, tmp_Rt);
    //        outs.emplace_back(tmp_Rt);

    //        prefix_3dpts = 4;
    //        selected_cost(3) = computeSolutionCostsOpenGV(Evec.middleCols<3>(15), A, v1, v2, true, tmp_Rt);
    //        outs.emplace_back(tmp_Rt);

    //        updateTimerAndLabel(process_times,time_labels,"OpenGV check time",check_time2.Duration());
    //    }

    return est_Rt;
}

Matrix<double, 3, 4> processEvec3(const MatrixXd &Evec_sols,
                                  const BearingVector &v1,
                                  const BearingVector &v2,
                                  const MatrixXd &A,
                                  const VectorXd *ptr_weigths)
{
    int num_matches = v1.cols();
    // Filter out columns in Evec with NaN
    vector<int> I;
    for (int i = 0; i < Evec_sols.cols(); ++i)
    {
        if (!isnan(Evec_sols.col(i).sum()))
        { // Check if no element is NaN
            I.push_back(i);
        }
    }

    // Select valid columns only
    Matrix<double, 9, Dynamic> Evec(9, I.size());
    for (size_t i = 0; i < I.size(); ++i)
    {
        Evec.col(i) = Evec_sols.col(I[i]);
    }

    int numSolutions = Evec.cols();
    if (numSolutions == 0)
    {
        Matrix<double, 3, 4> out = Matrix<double, 3, 4>::Constant(numeric_limits<double>::quiet_NaN());
        // Assuming 'residual' is a vector that should be returned or modified as well. Its declaration is needed outside this code.
        // residual.clear();
        return out;
    }

    vector<Matrix3d> R_sol(numSolutions);
    vector<Vector3d> t_sol(numSolutions);
    vector<double> cost;
    cost.resize(num_matches);

    VectorXd total_cost = MatrixXd::Zero(numSolutions, 1);
    vector<Pose> right_Rts;

    VectorXd ratios(numSolutions);

    // solutions: 6, 6, 3, 3 = 18

    vector<Matrix<double, 3, 4>> outs;
    VectorXd selected_cost(numSolutions, 1);

    Matrix<double, 3, 4> tmp_Rt;

    for (int i = 0; i < numSolutions; i++)
    {
        selected_cost(i) = computeSolutionCosts(Evec.col(i), A, v1, v2, false, ptr_weigths, tmp_Rt);
        outs.emplace_back(tmp_Rt);
    }

    int min_id;
    selected_cost.minCoeff(&min_id);

    return outs[min_id];
}
void solve_pizarro_6pts(const EigenSols &eigen_sols,
                        const MatrixXd &B,
                        MatrixXd &Evec_real,
                        const bool &flags_keep_real)
{

    // 进行SVD分解
    Eigen::JacobiSVD<Eigen::MatrixXd> svd(B, Eigen::ComputeThinU | Eigen::ComputeThinV);
    Eigen::MatrixXd Vb = svd.matrixV();
    Eigen::VectorXd singularValues = svd.singularValues();

    // 获取与最大4个奇异值对应的奇异向量
    Eigen::MatrixXd VbLargest4 = Vb.leftCols(4);

    // 将 Vb 转置
    VbLargest4.transposeInPlace();

    //    std::cout << "VbLargest4:\n" << VbLargest4.leftCols(4).colPivHouseholderQr().inverse()<< std::endl;

    // 计算 M
    Eigen::MatrixXd M;
    M = VbLargest4.leftCols(4).colPivHouseholderQr().solve(VbLargest4.rightCols(VbLargest4.cols() - 4));

    //    std::cout << "ParzzioB:\n" << B << std::endl;
    //    std::cout << "M:\n" << M << std::endl;

    Matrix<double, 1, 7> A0;

    A0(0, 0) = -M(2, 2);
    A0(0, 1) = M(1, 2) - M(2, 3) + M(2, 0) * M(3, 2) - M(2, 2) * M(3, 0);
    A0(0, 2) = M(1, 3) - M(2, 4) - M(1, 0) * M(3, 2) + M(1, 2) * M(3, 0) + M(2, 0) * M(3, 3) + M(2, 1) * M(3, 2) - M(2, 2) * M(3, 1) - M(2, 3) * M(3, 0);
    A0(0, 3) = M(1, 4) - M(2, 5) - M(1, 0) * M(3, 3) - M(1, 1) * M(3, 2) + M(1, 2) * M(3, 1) + M(1, 3) * M(3, 0) + M(2, 0) * M(3, 4) + M(2, 1) * M(3, 3) - M(2, 3) * M(3, 1) - M(2, 4) * M(3, 0);
    A0(0, 4) = M(1, 5) - M(1, 0) * M(3, 4) - M(1, 1) * M(3, 3) + M(1, 3) * M(3, 1) + M(1, 4) * M(3, 0) + M(2, 0) * M(3, 5) + M(2, 1) * M(3, 4) - M(2, 4) * M(3, 1) - M(2, 5) * M(3, 0);
    A0(0, 5) = -M(1, 0) * M(3, 5) - M(1, 1) * M(3, 4) + M(1, 4) * M(3, 1) + M(1, 5) * M(3, 0) + M(2, 1) * M(3, 5) - M(2, 5) * M(3, 1);
    A0(0, 6) = -M(1, 1) * M(3, 5) + M(1, 5) * M(3, 1);

    //    cout <<"A0:\n"<<A0<<endl;
    auto roots_beta = findRoots(A0);
    Matrix<double, 2, Eigen::Dynamic> SOLS;

    int real_sol_num = 0;
    for (int i = 0; i < roots_beta.size(); ++i)
    {
        if (roots_beta(i).imag() == 0)
        {
            real_sol_num++;
        }
    }

    SOLS = Eigen::MatrixXd::Zero(2, real_sol_num);
    int real_sol_id = 0;
    for (int i = 0; i < roots_beta.size(); ++i)
    {

        if (roots_beta(i).imag() == 0)
        {
            double b = roots_beta(i).real();
            double t2 = b * b;
            double p0 = M(3, 5) + M(3, 4) * b + M(3, 3) * t2 + M(3, 2) * b * t2;

            double p1 = M(3, 1) + M(3, 0) * b + b * b;

            SOLS(0, real_sol_id) = -p0 / p1;
            SOLS(1, real_sol_id) = b;
            real_sol_id++;
        }
    }

    //    std::cout <<"SOlS:\n"<<SOLS<<endl;
    Matrix<double, 9, Eigen::Dynamic> Evec = Eigen::VectorXd::Zero(9, roots_beta.size());
    Evec = eigen_sols.transpose() * (MatrixXd(SOLS.rows() + 1, SOLS.cols()) << SOLS, MatrixXd::Ones(1, SOLS.cols())).finished();

    for (int i = 0; i < Evec.cols(); ++i)
    {
        Evec.col(i).normalize();
    }

    if (flags_keep_real)
    {
        Evec_real = Evec.real();
    }
    else
    {
        vector<int> I;
        for (size_t i = 0; i < Evec.cols(); ++i)
        {
            if ((Evec.col(i).unaryExpr([](const std::complex<double> &value)
                                       { return value.imag(); })
                     .array() == 0)
                    .all())
                I.push_back(i);
        }

        Evec_real.setZero(Evec.rows(), I.size());

        for (size_t i = 0; i < I.size(); ++i)
        {
            Evec_real.col(i) = Evec.col(I[i]);
        }
    }
}

void write_evec_solutions(const std::string output_file,
                          const MatrixXd &Evec_real)
{

    fstream output(output_file, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        std::cerr << "output file cannot create, please check path";
        return;
    }

    unsigned int num_sols = Evec_real.cols();
    output << num_sols << endl;

    output << setprecision(16) << Evec_real;

    output.close();
}

void load_pose(const std::string &pose_file, Pose &pose)
{

    POVG::Timer timer;

    std::ifstream file(pose_file);
    if (!file.is_open())
    {
        std::cerr << "Pose file cannot be opened." << std::endl;
        return;
    }

    for (int i = 0; i < 3; ++i)
    {
        for (int j = 0; j < 3; ++j)
        {
            file >> pose.rotations(j, i); // 注意这里使用了转置
        }
    }

    for (int i = 0; i < 3; ++i)
    {
        file >> pose.translations(i);
    }

    //    cout << ">> time for loading pose file: "
    //         << timer.Duration()
    //         << "s"
    //         << endl;
}

void write_pose(const std::string output_file,
                const Pose &pose_est)
{

    fstream output(output_file, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        std::cerr << "output file cannot create, please check path";
        return;
    }

    output << setprecision(16) << pose_est.rotations.transpose() << endl;
    output << setprecision(16) << pose_est.translations.transpose();

    output.close();
}

void write_inliers(const std::string output_file,
                   const VectorXi &inliers)
{

    fstream output(output_file, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        std::cerr << "output file cannot create, please check path";
        return;
    }

    output << setprecision(16) << inliers;

    output.close();
}
void write_inliers(const std::string output_file,
                   const vector<int> &inliers)
{

    fstream output(output_file, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        std::cerr << "output file cannot create, please check path";
        return;
    }

    for (auto id : inliers)
        output << setprecision(16) << id << endl;

    output.close();
}

// bool process_bearing_vectors(const BearingVector& v1,
//                              const BearingVector& v2,
//                              const VectorXd& weights,
//                              const string& output_file,
//                              const string& inliers_file,
//                              const vector<double>& process_times,
//                              const vector<string>& time_labels,
//                              double inlier_threshold,
//                              int gnc_samplesize) {

//    Pose pose_est;
//    GNCInfo gnc_info(&solve_6pts_main, &residual_LiGT);

//    vector<int> final_inliers;
//    final_inliers.clear();

//    double threshold = 1e-4; // best

//    POVG::Timer method_time;
//    bool use_med = false;

//    if (v1.cols() > gnc_info.options_.sampleSize) {
//        gnc_info.SetSigmaMode(2);
//        gnc_info.SetTLSBounder(3);
//        gnc_info.SetTLSMaxIter(10);
//        gnc_info.SetSampleSizes(gnc_samplesize);

//        gnc_info.GNC_Ransac(v1, v2, inlier_threshold);

//        cout << "GNC_RANSAC: threshold = " << threshold << endl;
//        cout << "GNC_RANSAC: best_sigma_ = " << gnc_info.best_sigma_ << endl;

//        final_inliers = gnc_info.bestInlier_indices_;
//        pose_est = gnc_info.bestModel_;
//    } else {
//        final_inliers.clear();
//    }

//    cout << "GNCIRLS before: number of inliers = " << final_inliers.size() << endl;
//    show_inlier_info(final_inliers, "after gnc-ransac ");

//    GNCInfo gnc_info2(&solve_6pts_main, &residual_LiGT);

//    if (final_inliers.size() > 12) {
//        BearingVector v1_inliers, v2_inliers;
//        obtain_inlier_matches(v1, v2, final_inliers, v1_inliers, v2_inliers);

//        gnc_info2.SetSigmaMode(2);
//        gnc_info.SetTLSBounder(3);
//        gnc_info2.SetTLSMaxIter(20);
//        gnc_info2.GNC_IRLS2(v1_inliers, v2_inliers);
//        cout << "gnc_info2.gnc_med_sigma = " << gnc_info2.gnc_med_sigma_ << endl;

//        vector<int> tmp_inliers;
//        for (auto id = 0; id < final_inliers.size(); ++id) {
//            if (gnc_info2.weights_(id) > 0.1) {
//                tmp_inliers.emplace_back(final_inliers[id]);
//            }
//        }
//        final_inliers.swap(tmp_inliers);
//        tmp_inliers.clear();

//        pose_est = gnc_info2.Rt_est_;

//        updateTimerAndLabel(process_times, time_labels, "GNC-RANSAC+LiRP", method_time.Duration());

//        int num_inlier_indices = final_inliers.size();
//        cout << "num_inlier_indices_ in final gnc: " << num_inlier_indices << ", num_matches: " << v1.cols() << endl;

//        double tmp_ratio = (1e-10 + num_inlier_indices) / v1.cols();
//        cout << "inlier ratio in final gnc: " << tmp_ratio << endl;
//    }

//    if (final_inliers.size() < 12 || gnc_info.best_sigma_ < gnc_info2.gnc_med_sigma_) {
//        final_inliers.clear();
//    }

//    write_pose(output_file, pose_est);
//    write_inliers(inliers_file, final_inliers);

//    return true;
//}

int main(int argc, char *argv[])
{
    google::ParseCommandLineFlags(&argc, &argv, true);

    TwoViewSolveMethod method = stringToEnum(FLAGS_solve_mode.c_str());

    switch (method)
    {
    case EIGEN_SOLVE_MAIN:
    {
        BearingVector v1, v2;
        VectorXd weights;
        Pose pose_est;

        load_bearing_info(FLAGS_bearing_vector_file, v1, v2);
        load_weights_file(FLAGS_weights_file, weights);

        POVG::Timer timer;
        //        echo_on = true;
        if (weights.rows() > 0)
            pose_est = solve_6pts_main(v1, v2, &weights);
        else
            pose_est = solve_6pts_main(v1, v2, nullptr);

        time_algorithm = timer.Duration();
        cout << ">> time for LiRP algorithm: ";

        write_pose(FLAGS_output_file, pose_est);
    }
    break;
    case GNCRANSAC_6PTS_LIGT:
    {
        BearingVector v1, v2;
        Pose pose_est;
        vector<int> final_inliers;

        GNCInfo gnc_info(&solve_6pts_main, getResidualFunction(FLAGS_gnc_residual_func));
        load_bearing_info(FLAGS_bearing_vector_file, v1, v2);

        if (v1.cols() > gnc_info.options_.sampleSize)
        {
            gnc_info.SetSigmaMode(2);
            gnc_info.SetTLSBounder(3);
            gnc_info.SetTLSMaxIter(10);
            gnc_info.SetSampleSizes(FLAGS_gnc_samplesize);
            gnc_info.GNC_Ransac(v1, v2, FLAGS_inlier_threhold);

            final_inliers.swap(gnc_info.bestInlier_indices_);
            pose_est = gnc_info.bestModel_;
        }

        if (final_inliers.size() < 12)
            final_inliers.clear();

        write_pose(FLAGS_output_file, pose_est);
        write_inliers(FLAGS_inliers_file, final_inliers);
    }
    break;
    case SHUFFLE_RANSAC:
    {
        BearingVector v1, v2;
        Pose pose_est;
        vector<int> final_inliers;

        GNCInfo gnc_info(&solve_6pts_main,
                         getResidualFunction(FLAGS_gnc_residual_func),
                         getResidualFunction(FLAGS_gnc_dist_func));

        load_bearing_info(FLAGS_bearing_vector_file, v1, v2);

        if (v1.cols() > gnc_info.options_.sampleSize)
        {
            gnc_info.SetSigmaMode(2);
            gnc_info.SetTLSBounder(3);
            gnc_info.SetTLSMaxIter(10);
            gnc_info.SetSampleSizes(FLAGS_gnc_samplesize);

            gnc_info.Shuffle_Ransac(v1, v2, FLAGS_inlier_threhold, 100);

            final_inliers.swap(gnc_info.bestInlier_indices_);
            pose_est = gnc_info.bestModel_;
        }

        if (final_inliers.size() < 12)
            final_inliers.clear();

        write_pose(FLAGS_output_file, pose_est);
        write_inliers(FLAGS_inliers_file, final_inliers);
    }
    break;

    case GNC_6PTS_LIGT:
    {
        BearingVector v1, v2;
        vector<int> final_inliers;
        Pose pose_est;

        GNCInfo gnc_info(&solve_6pts_main,
                         getResidualFunction(FLAGS_gnc_residual_func),
                         getResidualFunction(FLAGS_gnc_dist_func));

        load_bearing_info(FLAGS_bearing_vector_file, v1, v2);
        gnc_info.GNC_RansacOpenGV(v1, v2, FLAGS_inlier_threhold);
        final_inliers.swap(gnc_info.bestInlier_indices_);
        if (final_inliers.size() > 12)
        {
            // refine model using LM optimization with PPO residuals
            cout << "Refining model with " << final_inliers.size() << " inliers using LM+PPO..." << endl;

            // 提取内点数据
            BearingVector v1_inliers, v2_inliers;
            obtain_inlier_matches(v1, v2, final_inliers, v1_inliers, v2_inliers);

            // 使用LM算法进行PPO优化（启用Huber损失函数）
            // 使用自适应Huber阈值：基于初始残差的统计特性
            VectorXd init_residuals = residual_PPO(v1_inliers, v2_inliers, gnc_info.bestModel_);
            double adaptive_delta = 1.5 * init_residuals.array().abs().mean(); // 1.5倍平均绝对残差
            adaptive_delta = std::max(adaptive_delta, 0.01);                   // 最小阈值保护
            adaptive_delta = std::min(adaptive_delta, 0.1);                    // 最大阈值保护

            Pose refined_pose = optimizePoseWithLM(v1_inliers, v2_inliers, gnc_info.bestModel_,
                                                   nullptr, true, adaptive_delta);

            cout << "LM refinement completed. Original vs Refined pose:" << endl;
            cout << "Original R: " << endl
                 << gnc_info.bestModel_.rotations << endl;
            cout << "Refined R: " << endl
                 << refined_pose.rotations << endl;
            cout << "Original t: " << gnc_info.bestModel_.translations.transpose() << endl;
            cout << "Refined t: " << refined_pose.translations.transpose() << endl;

            // 使用精炼后的位姿重新计算内点
            VectorXd refined_residuals = residual_PPO(v1, v2, refined_pose);
            vector<int> refined_inliers;
            for (int i = 0; i < refined_residuals.size(); ++i)
            {
                if (refined_residuals(i) < FLAGS_inlier_threhold)
                {
                    refined_inliers.push_back(i);
                }
            }

            cout << "Inliers after LM refinement: " << refined_inliers.size()
                 << " (was " << final_inliers.size() << ")" << endl;

            // 更新最终结果
            if (refined_inliers.size() >= final_inliers.size() * 0.8) // 至少保持80%的内点
            {
                final_inliers = refined_inliers;
                pose_est = refined_pose;
                cout << "LM refinement accepted." << endl;
            }
            else
            {
                final_inliers.clear();
                pose_est = gnc_info.bestModel_;
                cout << "LM refinement rejected, using original result." << endl;
            }
        }
        else
        {
            final_inliers.clear();
            pose_est = gnc_info.bestModel_;
        }

        write_pose(FLAGS_output_file, pose_est);
        write_inliers(FLAGS_inliers_file, final_inliers);
    }
    break;
    default:
        break;
    }

    return 0;
}
