#ifndef SIXPT_ALGORITHM_HPP
#define SIXPT_ALGORITHM_HPP

#include <vector>
#include <functional>
#include <memory>
#include <random>
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include "../LiGT/povg_types.hpp"

namespace POVG
{
    using namespace Eigen;

    // 枚举类型定义
    enum TwoViewSolveMethod
    {
        EIGEN_SOLVE,
        PIZARRO,
        PIZARRO2,
        PIZARRO_REAL,
        LIRP_6PTS_ESOLS,
        EIGEN_SOLVE_MAIN,
        GNC_6PTS_LIGT,
        GNCRANSAC_6PTS_LIGT,
        SHUFFLE_RANSAC,
        GNC_6PTS_BA,
        GNC_6PTS_OPENGV,
        GNC_6PTS_PPO,
        GNC_6PTS_LIGT_DIRECT,
        GNC_6PTS_COPLANAR,
        GNC_6PTS_LIGT_INVD,
        GNC_HOMO_8PTS_LIGT,
        MMS_TLS_LiRP
    };

    // 类型别名
    using EigenSols = Matrix<double, 3, 9>;
    using Eval_Rt_func = std::function<Pose(const BearingVector &, const BearingVector &, const VectorXd *)>;
    using Eval_Residual_func = std::function<double(const Vector3d &, const Vector3d &, const Matrix3d &, const Vector3d &)>;

    // 残差索引结构
    struct ResidualIndex
    {
        double residual;
        int index;

        ResidualIndex(double r, int i) : residual(r), index(i) {}

        bool operator<(const ResidualIndex &other) const
        {
            return residual < other.residual;
        }
    };

    // GNCInfo类声明
    class GNCInfo
    {
    public:
        // 构造函数
        GNCInfo(Eval_Rt_func eval_Rt_func, Eval_Residual_func eval_residual_func);
        GNCInfo(Eval_Rt_func eval_Rt_func, Eval_Residual_func eval_residual_func, Eval_Residual_func eval_dist_func);

        // 主要算法方法
        bool GNC_IRLS(const BearingVector &v1, const BearingVector &v2);
        bool GNC_IRLS2(const BearingVector &v1, const BearingVector &v2);
        bool GNC_IRLSp(const BearingVector &v1, const BearingVector &v2);
        bool GNC_Ransac(const BearingVector &v1, const BearingVector &v2, double threshold);
        bool RansacOpenGV(const BearingVector &v1, const BearingVector &v2, double threshold);
        bool GNC_RansacOpenGV(const BearingVector &v1, const BearingVector &v2, double threshold);
        bool GNC_LMS(const BearingVector &v1, const BearingVector &v2, double threshold);
        bool GNC_Ransac_adaptive(const BearingVector &v1, const BearingVector &v2, double threshold);
        bool GNC_RansacScheme(const BearingVector &v1, const BearingVector &v2, double threshold);
        void Shuffle_Ransac(const BearingVector &v1, const BearingVector &v2, double threshold, int num_min_score_ids);
        std::vector<int> ShuffleAlgorithm(const BearingVector &v1, const BearingVector &v2, double threshold);

        // 多GNC方法
        bool multiple_GNC_v1(const BearingVector &v1, const BearingVector &v2, double threshold, int num_attempts = 3);
        void multiple_GNC_v3(const BearingVector &v1, const BearingVector &v2, double threshold, int num_attempts = 3);
        void multiple_GNC_v2(const BearingVector &v1, const BearingVector &v2, double threshold, int num_attempts = 3);
        void multi_GNC_IRLS_v1(const BearingVector &v1, const BearingVector &v2, double threshold, int num_attempts = 3);
        void multi_GNC_IRLS_v3(const BearingVector &v1, const BearingVector &v2, double threshold, int num_attempts = 3);

        // 设置参数方法
        void SetSigmaMode(const int &mode, const double &threshold = 5 * 1e-4);
        void SetTLSMaxIter(const int &max_iter);
        void SetTLSBounder(const double &bounder);
        void SetSampleSizes(const int &sample_size);

        // RANSAC相关方法
        void getSamples(int &iterations, std::vector<int> &samples);
        int getSampleSize() const;
        void drawIndexSample(std::vector<int> &sample);
        int rnd();
        bool isSampleGood(const std::vector<int> &sample) const;
        void setIndices(const std::vector<int> &indices);
        void selectWithinDistance(const Pose &model_coefficients, const double threshold, std::vector<int> &inliers);

        // 权重更新方法
        VectorXd gncWeightsUpdate(VectorXd weights, double mu, const VectorXd &residuals, double barc2);
        VectorXd M_gncWeightsUpdate(VectorXd weights, double mu, const VectorXd &residuals, double barc2);

        // 尺度参数计算
        double FastScaleParameter(const VectorXd &residuals, double iter_ratio);
        double ScaleParameter(const VectorXd &residuals, double iter_ratio);
        double ScaleParameter(const VectorXd &residuals);

        // 公共成员变量
        std::vector<int> model_;
        int iterations_ = 0;
        int max_sample_checks_ = 10;
        std::shared_ptr<std::vector<int>> indices_;
        std::vector<int> shuffled_indices_;
        std::mt19937 rng_alg_;
        std::shared_ptr<std::uniform_int_distribution<>> rng_dist_;
        std::shared_ptr<std::function<int()>> rng_gen_;

        // 算法状态
        Eval_Rt_func eval_Rt_func_ = nullptr;
        Eval_Residual_func eval_residual_func_ = nullptr;
        Eval_Residual_func eval_dist_func_ = nullptr;

        // 最优结果
        Pose bestModel_;
        int bestInlierNum_ = 0;
        double bestInlierValue_ = 1e5;
        double best_sigma_ = 0.0;
        double best_mean_sigma_ = 0.0;
        std::vector<int> bestInlier_indices_;

        // 当前状态
        Pose Rt_est_;
        bool is_pose_worked_ = true;
        int iter_;
        bool is_reweighted = true;
        VectorXd weights_;
        VectorXi inlier_indices_;
        double gnc_indicator_ = 0;

        // 参数设置
        int sigma_mode_ = 0;
        double sigma_threshold_;
        double gnc_sigma_ = 0;
        double gnc_mean_sigma_ = 0;
        double gnc_max_residual_ = 0;
        double gnc_med_sigma_ = 0;
        int num_buffer_ids_ = 20;

        // GNC-IRLS specific parameters
        int gnc_irls_iterations_ = 10;
        double gnc_irls_threshold_ratio_ = 2.0;
        double irls_initial_weights_threshold_ = 0.1;

        // Shuffle Ransac specific parameters
        int shuffle_iterations_ = 10;
        int shuffle_subset_size_ = 100;

        // GNC-RANSAC specific parameters
        bool logging_ = false;
        double mu_step_ = 1.4;
        int max_iterations_ = 20;
        int min_inliers_ = 20;
        int max_inliers_ = 500;
        double threshold_ = 1e-4;
        double gnc_stop_threshold_ = 1e-6;
        double noise_bound_ = 0.0;
        double noise_bound_2 = 0.0;
        bool use_perspective_correction = false;

        // 选项结构
        struct options
        {
            int max_iter = 10;
            int majorize = 1;
            double stopTh = 1e-10;
            int superlinear = 1;
            int sampleSize = 20;
            double bounder = 3;
        } options_;
    };

    // 核心求解函数声明
    Pose solve_6pts_main(const BearingVector &v1, const BearingVector &v2, const VectorXd *ptr_weigths);
    Pose solve_Homo8pts_main(const BearingVector &v1, const BearingVector &v2, const VectorXd *ptr_weigths);

    // 工具函数声明
    TwoViewSolveMethod stringToEnum(const std::string &method);
    Eval_Residual_func getResidualFunction(const std::string &func_name);
    double TwoViewLiGT(const Vector3d &X1, const Vector3d &X2, const Matrix3d &R, const Vector3d &t);

    // 处理相关函数
    bool MSTLS_process(const BearingVector &v1, const BearingVector &v2, const double &bounder, const double &sigma, GNCInfo &gnc_info, std::vector<int> &final_inliers);
    bool MSTLS_process(const BearingVector &v1, const BearingVector &v2, GNCInfo &gnc_info, std::vector<int> &final_inliers);
    bool IRLS_process(const BearingVector &v1, const BearingVector &v2, const double &bounder, const double &sigma, GNCInfo &gnc_info, std::vector<int> &final_inliers);

    // 数据加载函数
    void load_bearing_info(const std::string &bearing_file, BearingVector &v1, BearingVector &v2);
    void load_weights_file(const std::string &weights_file, VectorXd &weights);
    void load_pose(const std::string &pose_file, Pose &pose);

    // 数据输出函数
    void write_pose(const std::string output_file, const Pose &pose_est);
    void write_inliers(const std::string output_file, const std::vector<int> &inliers);

    // 辅助函数
    std::vector<int> generateRandomSample(int num_matches, int sampleSize);
    double findMedian(VectorXd a);
    double findMedian(std::vector<double> a);
    Eigen::Matrix3d crossMatrix(const Eigen::Vector3d &x);

} // namespace POVG

#endif // SIXPT_ALGORITHM_HPP