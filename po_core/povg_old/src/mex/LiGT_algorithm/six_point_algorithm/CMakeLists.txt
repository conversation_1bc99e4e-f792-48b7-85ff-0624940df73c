# 6点算法模块构建配置

# 基础包含目录
INCLUDE_DIRECTORIES(
    ${LIGT_DIRS}
    ${SPECTRA_DIRS}
    ${EIGEN_INCLUDE_DIRS}
)

# 查找必需库
find_package(Eigen3 REQUIRED)
find_package(OpenCV QUIET)

# 如果找到OpenCV则包含相关设置
if(OpenCV_FOUND)
    include_directories(${OpenCV_INCLUDE_DIRS})
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
else()
    message(STATUS "OpenCV not found, skipping OpenCV-dependent features")
endif()

# 添加核心源文件到静态库
set(SIXPT_SOURCES
    sixpt_algorithm_strecha.cpp
)



# 将源文件添加到主静态库目标
# target_sources(povg_old_lib PRIVATE
#     ${CMAKE_CURRENT_SOURCE_DIR}/sixpt_algorithm.cpp
# )
set(POVGLIB_SOURCES ${POVGLIB_SOURCES} ${CMAKE_CURRENT_SOURCE_DIR}/sixpt_algorithm_strecha.cpp PARENT_SCOPE)

# 可选：如果需要构建可执行文件进行测试
set(BUILD_EXECUTABLES true)
if(BUILD_EXECUTABLES)
    add_executable(povg_sixpt_algorithm ${SIXPT_SOURCES})
    
    target_link_libraries(povg_sixpt_algorithm
        povg_ligt_algorithm
        ${GFLAGS_LIBRARIES}
        ${GLOG_LIBRARY}
    )
    
    # 如果OpenCV可用，链接OpenCV库
    if(OpenCV_FOUND)
        target_link_libraries(povg_sixpt_algorithm ${OpenCV_LIBS})
    endif()
    
    # 设置编译标志
    set_target_properties(povg_sixpt_algorithm PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
    )
    
    target_compile_options(povg_sixpt_algorithm PRIVATE 
        -Wall -fPIC -funroll-all-loops
    )
endif()

# 设置包含目录
# 为sixpt_algorithm.cpp设置包含目录
# target_include_directories(povg_old_lib PRIVATE
#     ${CMAKE_CURRENT_SOURCE_DIR}
#     ${LIGT_DIRS}
#     ${SPECTRA_DIRS}
#     ${EIGEN_INCLUDE_DIRS}
# )

# 如果OpenCV可用，添加其包含目录
# if(OpenCV_FOUND)
#     target_include_directories(povg_old_lib PRIVATE ${OpenCV_INCLUDE_DIRS})
# endif()

message(STATUS "Six-point algorithm module configured successfully")




