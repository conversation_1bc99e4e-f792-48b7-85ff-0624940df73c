// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <fstream>

#include <iomanip>
#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <Eigen/SVD>

#include "robust_LiGT.hpp"
#include "povg_timer.hpp"

#include "Spectra/SymEigsShiftSolver.h"
#include "Spectra/MatOp/SparseSymShiftSolve.h"

#include <Eigen/Eigenvalues>
namespace POVG
{
    using namespace Spectra;

    // 辅助函数，用于连接路径和文件名，考虑了跨平台的路径分隔符
    string joinPath(const string &folder, const string &fileName)
    {
        if (folder.empty())
        {
            return fileName;
        }

        char sep = '/'; // 默认使用Unix样式的路径分隔符
#ifdef _WIN32
        sep = '\\'; // 在Windows上，使用反斜杠作为路径分隔符
#endif

        // 检查路径是否以分隔符结束，如果不是，则添加分隔符
        string fullPath = folder;
        if (fullPath.back() != sep)
        {
            fullPath += sep;
        }

        fullPath += fileName;
        return fullPath;
    }

    double findMedian(vector<double> a)
    {

        int n = a.size();
        // If size of the arr[] is even
        if (n % 2 == 0)
        {

            // Applying nth_element
            // on n/2th index
            nth_element(a.begin(),
                        a.begin() + n / 2,
                        a.end());

            // Applying nth_element
            // on (n-1)/2 th index
            nth_element(a.begin(),
                        a.begin() + (n - 1) / 2,
                        a.end());

            // Find the average of value at
            // index N/2 and (N-1)/2
            return (double)(a[(n - 1) / 2] + a[n / 2]) / 2.0;
        }

        // If size of the arr[] is odd
        else
        {

            // Applying nth_element
            // on n/2
            nth_element(a.begin(),
                        a.begin() + n / 2,
                        a.end());

            // Value at index (N/2)th
            // is the median
            return (double)a[n / 2];
        }
    }

    // ============== The LiGT Algorithm (Fast Version 1.1) =============
    // [Version History]
    // v1.0: first release; parallelism by Pierre Moulon.
    // v1.1: Spectra replaces Eigen; Block manipulation to implement LTL matrix.
    //
    // Coded by: Drs. Qi Cai
    // Email: <EMAIL>, <EMAIL>
    //
    // [Conditions of Use]: the LiGT algorithm is distributed under
    // the License of Attribution-ShareAlike 4.0 International
    // (https://creativecommons.org/licenses/by-sa/4.0/).
    //
    //------------------
    //-- Bibliography --
    //------------------
    // If you use it for a publication, please cite the following paper:
    //- [1] "A Pose-only Solution to Visual Reconstruction and Navigation".
    //- Authors: <AUTHORS>
    //- Date: December 2021.
    //- Journal: IEEE T-PAMI.
    //
    //- [2] "Equivalent constraints for two-view geometry: pose solution/pure rotation identification and 3D reconstruction".
    //- Authors: <AUTHORS>
    //- Date: December 2019.
    //- Journal: IJCV.
    //
    // This is a dedicated version of the LiGT algorithm for openMVG, which is supported by
    // the Inertial and Visual Fusion (VINF) research group in Shanghai Jiao Tong University
    // @ https://www.researchgate.net/lab/Inertial-visual-Fusion-VINF-Yuanxin-Wu
    //
    // Note:
    //
    // 1. It does not consider the rank condition in Proposition 6 of the T-PAMI paper.
    //

    void RobustLiGT::SetOutlierRemovalTypes(const OutlierRemovalTypes &outlier_removal_types)
    {
        outlier_removal_types_ = outlier_removal_types;
    }

    void RobustLiGT::SetLTLBuildMode(const LTLMode &ltl_mode)
    {
        ltl_mode_ = ltl_mode;
    }
    const Vector3d RobustLiGT::DLTBuild3Dpts(Track &track)
    {

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_x;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d world_coord;
        world_coord.setZero();

        Vector3d cam_coor = world_coord;

        const unsigned int track_length = track.size();

        double m3_norm = 0;
        double sum_m3 = 0;

        Matrix<double, 3, 4> pose;

        MatrixXd A;
        A.setZero(track_length * 3, 4);

        for (unsigned int i = 0; i < track_length; ++i)
        {

            ViewId &current_view = track[i].view_id;
            Matrix3d &R = global_rotations_[current_view];
            Vector3d &t = global_translations_[current_view];
            Vector3d &x = track[i].coord;

            pose << R(0, 0), R(0, 1), R(0, 2), t(0),
                R(1, 0), R(1, 1), R(1, 2), t(1),
                R(2, 0), R(2, 1), R(2, 2), t(2);

            cross_x << 0, -x(2), x(1),
                x(2), 0, -x(0),
                -x(1), x(0), 0;

            auto tmp_A = cross_x * pose;

            A.middleRows<3>(3 * i) = tmp_A;
        }

        Matrix4d AtA = A.transpose() * A;

        Eigen::EigenSolver<Matrix<double, 4, 4>> es(AtA);

        MatrixXcd evecs = es.eigenvectors();
        MatrixXcd evalues = es.eigenvalues();
        MatrixXd::Index evalsMin;
        MatrixXd evalues_real = evalues.real();
        evalues_real.rowwise().sum().minCoeff(&evalsMin);

        Vector4d tmp_world_coord;
        tmp_world_coord << evecs.real()(0, evalsMin),
            evecs.real()(1, evalsMin),
            evecs.real()(2, evalsMin),
            evecs.real()(3, evalsMin);

        world_coord = tmp_world_coord.topRows(3) / tmp_world_coord(3);

        return world_coord;
    }

    const Vector3d RobustLiGT::AnalyticalBuild3Dpts(Track &track)
    {

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_xr, cross_xc;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d world_coord;
        world_coord.setZero();

        Vector3d cam_coor = world_coord;

        const unsigned int track_length = track.size();

        double m3_norm = 0;
        double sum_m3 = 0;

        for (unsigned int i = 0; i < track_length; ++i)
        {

            for (unsigned int j = 0; j < track_length; ++j)
            {
                if (i == j)
                    continue;
                ViewId &lbase_view = track[i].view_id;
                ViewId &rbase_view = track[j].view_id;

                Matrix3d &R_l_base = global_rotations_[lbase_view];
                Matrix3d &R_r_base = global_rotations_[rbase_view];

                Vector3d &t_l = global_translations_[lbase_view];
                Vector3d &t_r = global_translations_[rbase_view];

                R_lr.noalias() = R_r_base * R_l_base.transpose();
                auto t_lr = t_r - R_r_base * (R_l_base.transpose() * t_l);

                Vector3d &x_l = track[i].coord;
                Vector3d &x_r = track[j].coord;

                cross_xr << 0, -x_r(2), x_r(1),
                    x_r(2), 0, -x_r(0),
                    -x_r(1), x_r(0), 0;

                R_lr_x_l.noalias() = R_lr * x_l;

                double d_lr = (cross_xr * t_lr).norm();
                m3_lr.noalias() = cross_xr * R_lr_x_l;

                m3_norm = m3_lr.norm();
                sum_m3 += m3_norm * m3_norm;

                world_coord += R_l_base.transpose() * m3_norm *
                               (d_lr * x_l - m3_norm * t_l);
            }
        }

        world_coord /= sum_m3;

        return world_coord;
    }
    void RobustLiGT::SetupOrientationFromPAResults(const std::string &PA_file)
    {

        Timer timer;
        std::cout << "##start load global R from PA results file<<<" << endl;

        fstream file;
        file.open(PA_file, std::ios::in);
        if (!file.is_open())
        {
            cout << "file cannot open" << endl;
            return;
        }

        cout << "PA_file name:" << PA_file << endl;

        int cq_i = 0;

        Size num_view;
        file >> num_view;

        if (num_view != num_est_view_)
            std::cerr << " number of views might be wrong, please check!\n";

        global_rotations_.clear();

        Vector3d tmp_trans;
        for (ViewId i = 0; i < num_view; ++i)
        {
            Eigen::Matrix3d tmp_R;

            file >> tmp_R(0, 0) >> tmp_R(1, 0) >> tmp_R(2, 0);
            file >> tmp_R(0, 1) >> tmp_R(1, 1) >> tmp_R(2, 1);
            file >> tmp_R(0, 2) >> tmp_R(1, 2) >> tmp_R(2, 2);

            file >> tmp_trans(0) >> tmp_trans(1) >> tmp_trans(2);

            global_rotations_.emplace_back(tmp_R);
        }

        file.close();

        cout << "time for loading pa results: "
             << timer.Duration()
             << "s" << endl;
    }

    void RobustLiGT::SetOriFolder(const string &folder)
    {
        ori_folder_ = folder;
    }

    void RobustLiGT::WriteOutlierInfo(const char *file_name,
                                      const int &id,
                                      const vector<ObsId> *outlier_obs_ids,
                                      const char *output_folder,
                                      const pair<vector<double>, vector<unsigned int>> *ptr_residual_info)
    {

        string fileName = "residuals_" + string(file_name) + to_string(id) + ".ori";

        // 使用joinPath函数构建完整的文件路径
        string output_path = joinPath(ori_folder_, fileName);
        cout << output_path << endl;
        fstream output(output_path, std::ios::out | ios::trunc);
        if (!output.is_open())
        {
            cout << "ori file cannot create, please check path" << endl;
        }

        if (!ptr_residual_info)
        {
            for (int i = 0; i < residuals_info_.first.size(); ++i)
            {

                output << setprecision(16)
                       << residuals_info_.first[i]
                       << " "
                       << residuals_info_.second[i]
                       << endl;
            }
        }
        else
        {
            for (int i = 0; i < ptr_residual_info->first.size(); ++i)
            {

                output << setprecision(16)
                       << ptr_residual_info->first[i]
                       << " "
                       << ptr_residual_info->second[i]
                       << endl;
            }
        }

        output.close();

        //========================== output outlier ids to check ==========================
        fileName = "obs_outlier_ids_" + string(file_name) + to_string(id) + ".ori";

        // 使用joinPath函数构建完整的文件路径
        output_path = joinPath(ori_folder_, fileName);

        fstream output2(output_path, std::ios::out | ios::trunc);
        if (!output2.is_open())
        {
            cout << "output file cannot create, please check path" << endl;
            return;
        }

        if (!outlier_obs_ids)
        {

            for (PtsId i = 0; i < num_pts_; ++i)
            {
                Track &ptr_track = tracks_info_ptr_->tracks[i].track;
                for (ObsId j = 0; j < ptr_track.size(); ++j)
                {
                    if (!ptr_track[j].is_used)
                    {

                        output2 << setprecision(16)
                                << ptr_track[j].obs_id
                                << endl;
                    }
                }
            }
        }
        else
        {
            for (int i = 0; i < outlier_obs_ids->size(); ++i)
            {

                output2 << setprecision(16)
                        << (*outlier_obs_ids)[i]
                        << endl;
            }
        }
        output2.close();
    }

    void RobustLiGT::WriteCoviews(const int &id)
    {

        std::string fname;
        fname += "/home/<USER>/Coviews_";
        fname += to_string(id);
        fname += ".txt";

        //======================= Debug: output residuals to check ==========================
        fstream output(fname, std::ios::out | ios::trunc);
        if (!output.is_open())
        {
            cout << "output file cannot create, please check path" << endl;
        }
        output << setprecision(16) << coviews_ << endl;
        output.close();
    }
    void RobustLiGT::RemoveOutlierAnalBA(int id)
    {
        Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        ObsId id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        Vector3d world_coord;

        for (i = 0; i < pts_len; ++i)
        {

            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            track_len = ptr_track.size();
            if (track_len < 2)
                continue;

            world_coord = AnalyticalBuild3Dpts(ptr_track);

            for (j = 0; j < track_len; ++j)
            {
                if (!ptr_track[j].is_used)
                    continue;

                ViewId &current_view = ptr_track[j].view_id;

                Matrix3d &R_c = global_rotations_[current_view];

                Vector3d &t_c = global_translations_[current_view];

                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                Vector3d predict_x_r = R_c * world_coord + t_c;
                predict_x_r = predict_x_r / predict_x_r(2);

                Vector3d tmp_residual = predict_x_r - x_c;

                residuals_info_.first.emplace_back(tmp_residual.norm());
                residuals_info_.second.emplace_back(ptr_track[j].obs_id);
                id_cur_obs++;
            }
        }

        //    DetectAndRemoveOutlier(THREE_SIGMA);
        DetectAndRemoveOutlier();
        std::cout << "time for [LiGT-AnalBA] removing outliers: "
                  << timer.Duration()
                  << "s"
                  << endl
                  << "---------------------------------"
                  << endl;
    }

    void RobustLiGT::DetectAndRemoveOutlier(OutlierDetectionMode mode, double threshold)
    {
        switch (mode)
        {
        case THREE_SIGMA:
        {
            mean_residuals_ = 0;

            for (auto &residual : residuals_info_.first)
            {
                mean_residuals_ += residual;
            }

            mean_residuals_ /= residuals_info_.first.size();

            std::cout << "residual.mean = " << mean_residuals_
                      << endl;

            std_redisuals_ = 0;
            for (auto &residual : residuals_info_.first)
            {
                std_redisuals_ += (residual - mean_residuals_) * (residual - mean_residuals_);
            }
            std_redisuals_ = sqrt(std_redisuals_ / residuals_info_.first.size());

            cout << "std_residual = " << std_redisuals_ << std::endl;

            int num_obs_removed = 0;
            set<PtsId> removed_pts;
            vector<ObsId> outlier_ids;

            for (unsigned int i = 0; i < residuals_info_.first.size(); ++i)
            {
                if (residuals_info_.first[i] > threshold * std_redisuals_ + mean_residuals_)
                {

                    ObsId &outlier_obs_id = residuals_info_.second[i];
                    obs_map_[outlier_obs_id]->is_used = false;

                    num_obs_removed++;
                    removed_pts.insert(obs_map_[outlier_obs_id]->pts_id);
                }
            }

            cout << "[DetectAndRemoveOutlier(THREE_SIGMA)]removed obs outliers: " << num_obs_removed << endl;
            cout << "[DetectAndRemoveOutlier(THREE_SIGMA)]removed pts outliers: " << removed_pts.size() << endl;
            break;
        }
        case MID_METHOD:
        {
            double median_residual = findMedian(residuals_info_.first);
            vector<double> MAD_data;
            for (auto &residual : residuals_info_.first)
            {
                MAD_data.emplace_back(abs(residual - median_residual));
            }
            double MAD = findMedian(MAD_data);
            MAD = 1.4826 * MAD;
            cout << "MAD = " << MAD << std::endl;
            cout << "median_residual = " << median_residual << std::endl;

            int num_obs_removed = 0;
            set<PtsId> removed_pts;
            vector<ObsId> outlier_ids;

            //        MAD = 1e-4;
            for (int i = 0; i < MAD_data.size(); ++i)
            {

                if (abs(MAD_data[i] - median_residual) > 3 * MAD)
                {
                    ObsId &outlier_obs_id = residuals_info_.second[i];
                    obs_map_[outlier_obs_id]->is_used = false;

                    num_obs_removed++;
                    removed_pts.insert(obs_map_[outlier_obs_id]->pts_id);
                }
            }

            cout << "[DetectAndRemoveOutlier(MID_METHOD)]removed obs outliers: " << num_obs_removed << endl;
            cout << "[DetectAndRemoveOutlier(MID_METHOD)]removed pts outliers: " << removed_pts.size() << endl;

            break;
        }

        case MID_3DPTS:
        {

            double median_residual = findMedian(residuals_info_.first);
            double MAD = 0;
            for (auto &residual : residuals_info_.first)
            {
                MAD += abs(residual - median_residual);
            }
            MAD /= residuals_info_.first.size();

            cout << "MAD = " << MAD << std::endl;

            int num_obs_removed = 0;
            set<PtsId> removed_pts;
            vector<ObsId> outlier_ids;

            for (int i = 0; i < residuals_info_.first.size(); ++i)
            {
                auto tmp_value = 0.6745 * abs(residuals_info_.first[i] - median_residual) / MAD;
                if (tmp_value > 3.5)
                {

                    PtsId &outlier_pts_id = residuals_info_.second[i];
                    Track &ptr_track = tracks_info_ptr_->tracks[outlier_pts_id].track;

                    tracks_info_ptr_->tracks[outlier_pts_id].is_used = false;

                    int track_len = ptr_track.size();
                    for (int j = 0; j < track_len; ++j)
                    {
                        ptr_track[j].is_used = false;

                        num_obs_removed++;
                        removed_pts.insert(ptr_track[j].pts_id);
                    }
                }
            }

            cout << "[DetectAndRemoveOutlier(MID_3DPTS)]removed obs outliers: " << num_obs_removed << endl;
            cout << "[DetectAndRemoveOutlier(MID_3DPTS)]removed pts outliers: " << removed_pts.size() << endl;

            break;
        }
        default:

            break;
        }
    }

    void RobustLiGT::RemoveOutlierDLTBA(int id)
    {
        Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        ObsId id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        Vector3d world_coord;

        for (i = 0; i < pts_len; ++i)
        {

            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            track_len = ptr_track.size();

            int estimated_num = 0;

            for (ObsId i = 0; i < track_len; ++i)
            {
                if (ptr_track[i].is_used)
                    estimated_num++;
            }

            if (estimated_num < 2)
            {
                tracks_info_ptr_->tracks[i].is_used = false;
                continue;
            }

            if (track_len < 2)
                continue;

            world_coord = DLTBuild3Dpts(ptr_track);

            for (j = 0; j < track_len; ++j)
            {
                if (!ptr_track[j].is_used)
                    continue;

                ViewId &current_view = ptr_track[j].view_id;

                Matrix3d &R_c = global_rotations_[current_view];

                Vector3d &t_c = global_translations_[current_view];

                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                Vector3d predict_x_r = R_c * world_coord + t_c;
                predict_x_r = predict_x_r / predict_x_r(2);

                Vector3d tmp_residual = predict_x_r - x_c;

                residuals_info_.first.emplace_back(tmp_residual.squaredNorm());
                residuals_info_.second.emplace_back(ptr_track[j].obs_id);
                id_cur_obs++;
            }
        }

        DetectAndRemoveOutlier(THREE_SIGMA);
        //    DetectAndRemoveOutlier();
        std::cout << "time for [LiGT-DLTBA] removing outliers: "
                  << timer.Duration()
                  << "s"
                  << endl
                  << "---------------------------------"
                  << endl;

        //    WriteOutlierInfo( "", id);
    }

    void RobustLiGT::RemoveOutlierOpenMVGBA(int id)
    {
        Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        ObsId id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        Vector3d world_coord;

        for (i = 0; i < pts_len; ++i)
        {

            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            track_len = ptr_track.size();

            int estimated_num = 0;

            for (ObsId i = 0; i < track_len; ++i)
            {
                if (ptr_track[i].is_used)
                    estimated_num++;
            }

            if (estimated_num < 2)
            {
                tracks_info_ptr_->tracks[i].is_used = false;
                continue;
            }

            if (track_len < 2)
                continue;

            world_coord = DLTBuild3Dpts(ptr_track);

            double pts_residual = 0;
            int tmp_used_obs = 0;
            for (j = 0; j < track_len; ++j)
            {
                if (!ptr_track[j].is_used)
                    continue;

                ViewId &current_view = ptr_track[j].view_id;

                Matrix3d &R_c = global_rotations_[current_view];

                Vector3d &t_c = global_translations_[current_view];

                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                Vector3d predict_x_r = R_c * world_coord + t_c;
                predict_x_r = predict_x_r / predict_x_r(2);

                Vector3d tmp_residual = predict_x_r - x_c;
                pts_residual += tmp_residual.norm();

                id_cur_obs++;
                tmp_used_obs++;
            }

            pts_residual /= tmp_used_obs;
            residuals_info_.first.emplace_back(pts_residual);
            residuals_info_.second.emplace_back(ptr_track[0].pts_id);
        }

        //    for (int i = 0; i < residuals_info_.first.size(); ++i){

        //            PtsId& outlier_pts_id = residuals_info_.second[i];
        //            cout<<"outlier_pts_id="<<outlier_pts_id<<endl;
        //           }

        pair<vector<double>, vector<unsigned int>> tmp_residuals_info_;
        for (int i = 0; i < residuals_info_.first.size(); ++i)
        {

            PtsId &outlier_pts_id = residuals_info_.second[i];
            Track &ptr_track = tracks_info_ptr_->tracks[outlier_pts_id].track;

            int track_len = ptr_track.size();
            for (int j = 0; j < track_len; ++j)
            {
                tmp_residuals_info_.first.emplace_back(residuals_info_.first[i]);
                tmp_residuals_info_.second.emplace_back(ptr_track[j].obs_id);
            }
        }

        //    DetectAndRemoveOutlier(THREE_SIGMA);
        DetectAndRemoveOutlier(MID_3DPTS);

        std::cout << "time for [OPENMVG_BA] removing outliers: "
                  << timer.Duration()
                  << "s"
                  << endl
                  << "---------------------------------"
                  << endl;

        //    WriteOutlierInfo( "", id,nullptr,nullptr,&tmp_residuals_info_);
    }

    void RobustLiGT::RemoveObsOutliersOrigin(int id)
    {

        Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        ObsId id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        for (i = 0; i < pts_len; ++i)
        {

            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            if (!SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s))
                continue;

            ViewId &left_base = max_id_l;
            ViewId &right_base = max_id_r;

            ViewId &lbase_view = ptr_track[left_base].view_id;
            ViewId &rbase_view = ptr_track[right_base].view_id;

            double &base_s = max_s;
            total_max_m3[i] = max_s;

            Matrix3d &R_l_base = global_rotations_[lbase_view];
            Matrix3d &R_r_base = global_rotations_[rbase_view];

            R_lr.noalias() = R_r_base * R_l_base.transpose();

            Vector3d &x_l = ptr_track[left_base].coord;
            Vector3d &x_r = ptr_track[right_base].coord;

            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

            R_lr_x_l.noalias() = R_lr * x_l;
            m3_lr.noalias() = cross_xr * R_lr_x_l;

            tmp_A.noalias() = m3_lr.transpose() * cross_xr;
            tmp_A.noalias() = -tmp_A;
            tmp_A_R_lr.noalias() = -tmp_A * R_lr;

            id_l = 3 * lbase_view - 3;
            id_r = 3 * rbase_view - 3;

            track_len = ptr_track.size();

            for (j = 0; j < track_len; ++j)
            {
                if (!ptr_track[j].is_used)
                    continue;

                ViewId &current_view = ptr_track[j].view_id;

                Matrix3d &R_c = global_rotations_[current_view];
                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                R_lc.noalias() = R_c * R_l_base.transpose();
                Rlc_xl.noalias() = R_lc * x_l;

                m3_lc.noalias() = x_c.cross(Rlc_xl);

                s = m3_lc.norm();

                R_lc_x_l.noalias() = R_lc * x_l;
                xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                tmp_residual_l.noalias() = tmp_F_lbase * global_translations_[lbase_view];
                tmp_residual_r.noalias() = tmp_F_rbase * global_translations_[rbase_view];
                tmp_residual_c.noalias() = tmp_F_cur * global_translations_[current_view];

                tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                //            tmp_residual = tmp_residual / base_s / base_s;

                residuals_info_.first.emplace_back(tmp_residual.norm());
                residuals_info_.second.emplace_back(ptr_track[j].obs_id);
                id_cur_obs++;
            }
        }

        cout << "[remove outliers] used_pts = " << used_pts << "used_obs = " << residuals_info_.first.size() << endl;
        //    DetectAndRemoveOutlier(THREE_SIGMA);
        DetectAndRemoveOutlier();

        std::cout << "time for [Origin] removing outliers: "
                  << timer.Duration()
                  << "s"
                  << endl
                  << "---------------------------------"
                  << endl;

        //    WriteOutlierInfo( ori_folder_.c_str(), id);
    }
    void RobustLiGT::RemoveObsOutliersPA(int id)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        ViewId left_base = 0;
        ViewId right_base = 0;

        PtsId pts_id;
        for (pts_id = 0; pts_id < pts_len; ++pts_id)
        {
            // 计算残差的时候,可以全部都用
            //        if(!tracks_info_ptr_->tracks[i].is_used)
            //            continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[pts_id].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            Size track_size = ptr_track.size();

            int estimated_num = 0;

            for (ObsId i = 0; i < track_size; ++i)
            {
                if (ptr_track[i].is_used)
                    estimated_num++;
            }

            if (estimated_num < 2)
            {
                tracks_info_ptr_->tracks[i].is_used = false;
                continue;
            }

            for (ObsId i = 0; i < track_size; ++i)
            {
                if (!ptr_track[i].is_used)
                    continue;

                residual_value = 0;
                vector<double> residual_values;

                ViewId &left_base = i;
                ViewId &lbase_view = ptr_track[left_base].view_id;

                Matrix3d &R_l_base = global_rotations_[lbase_view];
                Vector3d &t_l = global_translations_[lbase_view];
                Vector3d &x_l = ptr_track[left_base].coord;

                track_len = ptr_track.size();

                int used_obs = 0;

                double d_lc = 0;
                double m3 = 0;

                for (j = 0; j < track_len; ++j)
                {
                    if (!ptr_track[j].is_used || j == i)
                        continue;

                    ViewId &current_view = ptr_track[j].view_id;
                    Matrix3d &R_c = global_rotations_[current_view];
                    Vector3d &x_c = ptr_track[j].coord;

                    Vector3d &t_c = global_translations_[current_view];
                    auto t_lc = t_c - R_c * (R_l_base.transpose() * t_l);

                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    R_lc.noalias() = R_c * R_l_base.transpose();
                    Rlc_xl.noalias() = R_lc * x_l;

                    m3_lc.noalias() = x_c.cross(Rlc_xl);

                    tmp_A.noalias() = m3_lc.transpose() * cross_xc;
                    d_lc += -tmp_A * t_lc;

                    m3 += m3_lc.squaredNorm();
                }

                double max_residual = 0;
                for (j = 0; j < track_len; ++j)
                {
                    if (!ptr_track[j].is_used || j == i)
                        continue;

                    ViewId &current_view = ptr_track[j].view_id;
                    Matrix3d &R_c = global_rotations_[current_view];
                    Vector3d &x_c = ptr_track[j].coord;

                    Vector3d &t_c = global_translations_[current_view];
                    auto t_lc = t_c - R_c * (R_l_base.transpose() * t_l);

                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    R_lc.noalias() = R_c * R_l_base.transpose();
                    Rlc_xl.noalias() = R_lc * x_l;

                    Vector3d predict_x = d_lc * Rlc_xl + m3 * t_lc;
                    predict_x = predict_x / predict_x(2);

                    Vector3d tmp_residual = predict_x - x_c;

                    residual_value += tmp_residual.squaredNorm();
                    used_obs++;

                    residual_values.emplace_back(tmp_residual.norm());

                    if (max_residual < tmp_residual.norm())
                    {
                        max_residual = tmp_residual.norm();
                    }
                }
                residuals_info_.first.emplace_back(findMedian(residual_values));
                //            residuals_info_.first.emplace_back(residual_value/used_obs);
                //            residuals_info_.first.emplace_back(sqrt(residual_value / used_obs));
                residuals_info_.second.emplace_back(ptr_track[i].obs_id);

                id_cur_obs++;
            }
        }
        //    DetectAndRemoveOutlier(THREE_SIGMA,1);
        DetectAndRemoveOutlier();
        cout << "residual_value = " << residual_value << endl;
        cout << "svd_value_ = " << svd_value_ << endl;
        cout << "id_cur_obs = " << id_cur_obs << endl;

        //    WriteOutlierInfo(ori_folder_.c_str(),id);

        cout << "time for [LiGT-PA] removing outliers: "
             << timer.Duration()
             << "s"
             << endl
             << "---------------------------------"
             << endl;
    }

    // void RobustLiGT::RemoveObsOutliersPA(int id){

    //    Timer timer;

    //    Size est_view_size = est_info_ptr_->estimated_view_ids.size();
    //    Size est_t_length = 3 * est_view_size - 3;

    //    VectorXd total_max_m3;
    //    total_max_m3.setZero(num_pts_);

    //    Matrix3d cross_xr, cross_xc;

    //    double s;

    //    Matrix3d R_lr, R_lc;

    //    Vector3d Rlc_xl;
    //    Vector3d m3_lr, m3_lc;

    //    RowVector3d tmp_A;

    //    Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

    //    Matrix3d FtF_ll,FtF_rr,FtF_cc;
    //    Matrix3d FtF_lr,FtF_lc,FtF_rc;

    //    unsigned int track_len;
    //    unsigned int i = 0;
    //    unsigned int j = 0;
    //    unsigned int k = 0;

    //    PtsId pts_len = num_pts_;
    //    Vector3d R_lr_x_l,R_lc_x_l,xc_R_lc_x_l;
    //    RowVector3d tmp_A_R_lr;

    //    int id_r,id_l,id_c;

    //    unsigned int used_pts = 0;

    //    ObsId max_id_l = 0;
    //    ObsId max_id_r = 0;

    //    double max_s = 0;

    //    Vector3d Rx;
    //    Vector3d xRx;

    //    Vector3d tmp_residual_l,
    //             tmp_residual_r,
    //             tmp_residual_c,
    //             tmp_residual;

    //    ObsId id_cur_obs = 0;

    //    // reset residuals_info_
    //    residuals_info_.first.clear();
    //    residuals_info_.second.clear();

    //    for(i = 0;i < pts_len; ++i){

    //        used_pts++;

    //        Track& ptr_track = tracks_info_ptr_->tracks[i].track;

    //        max_id_l = 0;
    //        max_id_r = 0;
    //        max_s = 0;

    //        if (!SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s))
    //            continue;

    //        ViewId& left_base = max_id_l;
    //        ViewId& right_base = max_id_r;

    //        ViewId& lbase_view = ptr_track[left_base].view_id;
    //        ViewId& rbase_view = ptr_track[right_base].view_id;

    //        double& base_s = max_s;
    //        total_max_m3[i] = max_s;

    //        Matrix3d& R_l_base = global_rotations_[lbase_view];
    //        Matrix3d& R_r_base = global_rotations_[rbase_view];

    //        Vector3d& t_l  = global_translations_[lbase_view];
    //        Vector3d& t_r  = global_translations_[rbase_view];

    //        R_lr.noalias() = R_r_base * R_l_base.transpose();
    //        auto t_lr = t_r - R_r_base * (R_l_base.transpose()*t_l);

    //        Vector3d& x_l = ptr_track[left_base].coord;
    //        Vector3d& x_r = ptr_track[right_base].coord;

    //        cross_xr<< 0, -x_r(2), x_r(1),
    //                x_r(2), 0, -x_r(0),
    //                -x_r(1), x_r(0), 0;

    //        double d_lr = (cross_xr*t_lr).norm();

    //        R_lr_x_l.noalias() = R_lr * x_l;
    //        m3_lr.noalias() = cross_xr * R_lr_x_l;

    //        tmp_A.noalias() = m3_lr.transpose() * cross_xr;
    //        tmp_A.noalias() = -tmp_A;
    //        tmp_A_R_lr.noalias() = -tmp_A * R_lr;

    //        id_l = 3 * lbase_view - 3;
    //        id_r = 3 * rbase_view - 3;

    //        track_len = ptr_track.size();

    //        for(j = 0; j < track_len; ++j){
    //            if (!ptr_track[j].is_used)
    //                continue;

    //            ViewId& current_view = ptr_track[j].view_id;
    //            Matrix3d& R_c = global_rotations_[current_view];

    //            Vector3d& t_c  = global_translations_[current_view];
    //            auto t_lc = t_c - R_c * (R_l_base.transpose()*t_l);

    //            Vector3d& x_c = ptr_track[j].coord;

    //            cross_xc << 0, -x_c(2), x_c(1),
    //                    x_c(2), 0, -x_c(0),
    //                    -x_c(1), x_c(0), 0;

    //            R_lc.noalias() = R_c * R_l_base.transpose();
    //            Rlc_xl.noalias() = R_lc * x_l;

    //            m3_lc.noalias() = x_c.cross(Rlc_xl);

    //            s = m3_lc.norm();

    //            R_lc_x_l.noalias() = R_lc * x_l;
    //            xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

    //            Vector3d predict_x_r = d_lr * Rlc_xl + max_s * t_lc;
    //            predict_x_r = predict_x_r/predict_x_r(2);

    //            Vector3d tmp_residual = predict_x_r - x_c;

    //            residuals_info_.first.emplace_back(tmp_residual.norm());
    //            residuals_info_.second.emplace_back(ptr_track[j].obs_id);
    //            id_cur_obs++;
    //        }
    //    }

    ////    DetectAndRemoveOutlier(THREE_SIGMA);
    //    DetectAndRemoveOutlier();

    //    std::cout << "time for [LiGT_PA] removing outliers: "
    //              << timer.Duration()
    //              << "s"
    //              << endl
    //              << "---------------------------------"
    //              << endl;

    //    WriteOutlierInfo( "", id);

    //}

    void RobustLiGT::RemoveObsOutliersPAV2(int id)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        ViewId left_base = 0;
        ViewId right_base = 0;

        PtsId pts_id;
        for (pts_id = 0; pts_id < pts_len; ++pts_id)
        {
            // 计算残差的时候,可以全部都用
            //        if(!tracks_info_ptr_->tracks[i].is_used)
            //            continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[pts_id].track;
            Size track_size = ptr_track.size();

            int estimated_num = 0;

            for (ObsId i = 0; i < track_size; ++i)
            {
                if (ptr_track[i].is_used)
                    estimated_num++;
            }

            if (estimated_num < 2)
            {
                tracks_info_ptr_->tracks[i].is_used = false;
                continue;
            }

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            for (ObsId i = 0; i < track_size; ++i)
            {
                if (!ptr_track[i].is_used)
                    continue;

                residual_value = 0;
                vector<double> residual_values;

                ViewId &lbase_view = ptr_track[i].view_id;
                Vector3d &x_l = ptr_track[i].coord;

                Matrix3d &R_l_base = global_rotations_[lbase_view];
                Vector3d &t_l = global_translations_[lbase_view];

                track_len = ptr_track.size();

                int used_obs = 0;
                for (j = 0; j < track_len; ++j)
                {
                    if (!ptr_track[j].is_used || j == i)
                        continue;

                    ViewId &current_view = ptr_track[j].view_id;
                    Matrix3d &R_c = global_rotations_[current_view];
                    Vector3d &x_c = ptr_track[j].coord;

                    Vector3d &t_c = global_translations_[current_view];
                    auto t_lc = t_c - R_c * (R_l_base.transpose() * t_l);

                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    R_lc.noalias() = R_c * R_l_base.transpose();
                    Rlc_xl.noalias() = R_lc * x_l;

                    m3_lc.noalias() = x_c.cross(Rlc_xl);
                    double d_lc = (cross_xc * t_lc).norm();

                    s = m3_lc.norm();

                    R_lc_x_l.noalias() = R_lc * x_l;
                    xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                    tmp_A.noalias() = m3_lc.transpose() * cross_xc;
                    tmp_A.noalias() = -tmp_A;

                    Vector3d predict_x = d_lc * Rlc_xl + s * t_lc;
                    predict_x = predict_x / predict_x(2);

                    Vector3d tmp_residual = predict_x - x_c;

                    residual_value += tmp_residual.squaredNorm();
                    used_obs++;

                    residual_values.emplace_back(tmp_residual.norm());
                }
                residuals_info_.first.emplace_back(findMedian(residual_values));
                //            residuals_info_.first.emplace_back((residual_value / used_obs));
                //            residuals_info_.first.emplace_back(sqrt(residual_value / used_obs));
                residuals_info_.second.emplace_back(ptr_track[i].obs_id);

                id_cur_obs++;
            }
        }
        DetectAndRemoveOutlier(THREE_SIGMA);
        //    DetectAndRemoveOutlier();
        cout << "residual_value = " << residual_value << endl;
        cout << "svd_value_ = " << svd_value_ << endl;
        cout << "id_cur_obs = " << id_cur_obs << endl;

        //    WriteOutlierInfo(ori_folder_.c_str(),id);

        cout << "time for [LiGT-PA v2] removing outliers: "
             << timer.Duration()
             << "s"
             << endl
             << "---------------------------------"
             << endl;
    }

    void RobustLiGT::RemovePtsOutliersPA(int id)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        ViewId left_base = 0;
        ViewId right_base = 0;

        PtsId pts_id;
        for (pts_id = 0; pts_id < pts_len; ++pts_id)
        {
            // 计算残差的时候,可以全部都用
            //        if(!tracks_info_ptr_->tracks[i].is_used)
            //            continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[pts_id].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            Size track_size = ptr_track.size();

            double pts_residual = 0;
            int used_obs = 0;
            for (ObsId i = 0; i < track_size; ++i)
            {
                if (!ptr_track[i].is_used)
                    continue;

                residual_value = 0;
                vector<double> residual_values;

                ViewId &lbase_view = ptr_track[i].view_id;
                Vector3d &x_l = ptr_track[i].coord;

                Matrix3d &R_l_base = global_rotations_[lbase_view];
                Vector3d &t_l = global_translations_[lbase_view];

                track_len = ptr_track.size();

                for (j = 0; j < track_len; ++j)
                {
                    if (!ptr_track[j].is_used || j == i)
                        continue;

                    ViewId &current_view = ptr_track[j].view_id;
                    Matrix3d &R_c = global_rotations_[current_view];
                    Vector3d &x_c = ptr_track[j].coord;

                    Vector3d &t_c = global_translations_[current_view];
                    auto t_lc = t_c - R_c * (R_l_base.transpose() * t_l);

                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    R_lc.noalias() = R_c * R_l_base.transpose();
                    Rlc_xl.noalias() = R_lc * x_l;

                    m3_lc.noalias() = x_c.cross(Rlc_xl);
                    double d_lc = (cross_xc * t_lc).norm();

                    s = m3_lc.norm();

                    R_lc_x_l.noalias() = R_lc * x_l;
                    xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                    tmp_A.noalias() = m3_lc.transpose() * cross_xc;
                    tmp_A.noalias() = -tmp_A;

                    Vector3d predict_x = d_lc * Rlc_xl + s * t_lc;
                    predict_x = predict_x / predict_x(2);

                    Vector3d tmp_residual = predict_x - x_c;

                    pts_residual += tmp_residual.norm();

                    residual_value += tmp_residual.norm();
                    used_obs++;

                    residual_values.emplace_back(tmp_residual.norm());
                }

                id_cur_obs++;
            }

            pts_residual /= used_obs;
            residuals_info_.first.emplace_back(pts_residual);
            residuals_info_.second.emplace_back(ptr_track[0].pts_id);
        }

        pair<vector<double>, vector<unsigned int>> tmp_residuals_info_;
        for (int i = 0; i < residuals_info_.first.size(); ++i)
        {

            PtsId &outlier_pts_id = residuals_info_.second[i];
            Track &ptr_track = tracks_info_ptr_->tracks[outlier_pts_id].track;

            int track_len = ptr_track.size();
            for (int j = 0; j < track_len; ++j)
            {
                tmp_residuals_info_.first.emplace_back(residuals_info_.first[i]);
                tmp_residuals_info_.second.emplace_back(ptr_track[j].obs_id);
            }
        }

        //    DetectAndRemoveOutlier(THREE_SIGMA);
        DetectAndRemoveOutlier(MID_3DPTS);

        std::cout << "time for [PtsPAV2] removing outliers: "
                  << timer.Duration()
                  << "s"
                  << endl
                  << "---------------------------------"
                  << endl;

        //    WriteOutlierInfo( ori_folder_.c_str(), id,nullptr,nullptr,&tmp_residuals_info_);
    }

    void RobustLiGT::CalculateResidual(int id)
    {

        Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        residuals_info_.first.clear();
        residuals_info_.second.clear();

        VectorXd pts_residuals;
        pts_residuals.setZero(num_pts_);

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        ObsId id_cur_obs = 0;

        for (i = 0; i < pts_len; ++i)
        {

            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            if (!SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s))
                continue;

            ViewId &left_base = max_id_l;
            ViewId &right_base = max_id_r;

            ViewId &lbase_view = ptr_track[left_base].view_id;
            ViewId &rbase_view = ptr_track[right_base].view_id;

            double &base_s = max_s;
            total_max_m3[i] = max_s;

            Matrix3d &R_l_base = global_rotations_[lbase_view];
            Matrix3d &R_r_base = global_rotations_[rbase_view];

            R_lr.noalias() = R_r_base * R_l_base.transpose();

            Vector3d &x_l = ptr_track[left_base].coord;
            Vector3d &x_r = ptr_track[right_base].coord;

            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

            R_lr_x_l.noalias() = R_lr * x_l;
            m3_lr.noalias() = cross_xr * R_lr_x_l;

            tmp_A.noalias() = m3_lr.transpose() * cross_xr;
            tmp_A.noalias() = -tmp_A;
            tmp_A_R_lr.noalias() = -tmp_A * R_lr;

            id_l = 3 * lbase_view - 3;
            id_r = 3 * rbase_view - 3;

            track_len = ptr_track.size();

            for (j = 0; j < track_len; ++j)
            {

                ViewId &current_view = ptr_track[j].view_id;

                if (!ptr_track[j].is_used)
                    continue;

                Matrix3d &R_c = global_rotations_[current_view];
                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                R_lc.noalias() = R_c * R_l_base.transpose();
                Rlc_xl.noalias() = R_lc * x_l;

                m3_lc.noalias() = x_c.cross(Rlc_xl);

                s = m3_lc.norm();

                R_lc_x_l.noalias() = R_lc * x_l;
                xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                tmp_residual_l.noalias() = tmp_F_lbase * global_translations_[lbase_view];
                tmp_residual_r.noalias() = tmp_F_rbase * global_translations_[rbase_view];
                tmp_residual_c.noalias() = tmp_F_cur * global_translations_[current_view];

                tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                //            tmp_residual = tmp_residual / base_s / base_s;

                residuals_info_.first.emplace_back(tmp_residual.norm());
                residuals_info_.second.emplace_back(ptr_track[j].obs_id);
                id_cur_obs++;
            }
        }

        mean_residuals_ = 0;

        for (auto &residual : residuals_info_.first)
        {
            mean_residuals_ += residual;
        }

        mean_residuals_ /= residuals_info_.first.size();

        std::cout << "residual.mean = " << mean_residuals_
                  << endl;

        std_redisuals_ = 0;
        for (auto &residual : residuals_info_.first)
        {
            std_redisuals_ += (residual - mean_residuals_) * (residual - mean_residuals_);
        }
        std_redisuals_ /= sqrt(residuals_info_.first.size());

        cout << "std_residual = " << std_redisuals_ << std::endl;

        int num_removed = 0;
        vector<ObsId> outlier_ids;
        for (i = 0; i < residuals_info_.first.size(); ++i)
        {
            if (residuals_info_.first[i] > 3 * std_redisuals_ + mean_residuals_)
            {
                num_removed++;
                ObsId &outlier_obs_id = residuals_info_.second[i];
                outlier_ids.emplace_back(outlier_obs_id);
            }
        }
        cout << "removed outliers: " << num_removed << ", num obs = " << id_cur_obs << endl;
        std::cout << "time for calculating residuals: "
                  << timer.Duration()
                  << "s"
                  << endl
                  << "---------------------------------"
                  << endl;

        //    WriteOutlierInfo( "old_", id, &outlier_ids);

        ////    //======================= Debug: output residuals to check ==========================

        //    string output_path = "/home/<USER>/residuals_old_";
        //    output_path += to_string(id);
        //    output_path += ".txt";

        //    fstream output(output_path,std::ios::out|ios::trunc);
        //    if(!output.is_open()){
        //        cout<<"output file cannot create, please check path" << endl;
        //    }
        //    for (auto& residual : residuals_)
        //    output<<setprecision(16) << residual << endl;
        //    output.close();

        //    output_path.clear();
        //    output_path = "/home/<USER>/obs_outlier_ids_old_";
        //    output_path += to_string(id);
        //    output_path += ".txt";

        //    fstream output3(output_path,std::ios::out|ios::trunc);
        //    if(!output3.is_open()){
        //        cout<<"output file cannot create, please check path"<<endl;
        //        return;
        //    }
        //    for ( int i =0; i<outlier_ids.size(); ++i){
        //        output3<<setprecision(16)<<outlier_ids[i]<<endl;
        //    }
        //    output3.close();

        //    //======================= Debug: output residuals to check ==========================
        //    fstream output2("/home/<USER>/total_residuals.txt",std::ios::out|ios::trunc);
        //    if(!output2.is_open()){
        //        cout<<"output file cannot create, please check path" << endl;
        //    }
        //    output2 << setprecision(16) << total_residual << endl;
        //    output2.close();

        //    //======================= Debug: output residuals to check ==========================
        //    fstream output3("/home/<USER>/total_max_m3.txt",std::ios::out|ios::trunc);
        //    if(!output3.is_open()){
        //        cout<<"output file cannot create, please check path"<<endl;
        //    }
        //    output3 << setprecision(16) << total_max_m3 << endl;
        //    output3.close();

        //    // Debug: output pts_residuals to check ==========================

        //    fstream output4("/home/<USER>/pts_residuals.txt",std::ios::out|ios::trunc);
        //    if(!output4.is_open()){
        //        cout<<"output file cannot create, please check path" << endl;
        //    }
        //    output4 << setprecision(16) << pts_residuals << endl;
        //    output4.close();
    }

    void RobustLiGT::CalculatePtsResidual()
    {

        Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        VectorXd residuals;
        residuals.setZero(num_obs_);

        VectorXd pts_residuals;
        pts_residuals.setZero(num_pts_);

        VectorXd total_max_m3;
        total_max_m3.setZero(num_pts_);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;

        RowVector3d tmp_A;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        ObsId id_cur_obs = 0;

        for (i = 0; i < pts_len; ++i)
        {
            if (!tracks_info_ptr_->tracks[i].is_used)
                continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s);

            ViewId &left_base = max_id_l;
            ViewId &right_base = max_id_r;

            ViewId &lbase_view = ptr_track[left_base].view_id;
            ViewId &rbase_view = ptr_track[right_base].view_id;

            double &base_s = max_s;
            total_max_m3[i] = max_s;

            Matrix3d &R_l_base = global_rotations_[lbase_view];
            Matrix3d &R_r_base = global_rotations_[rbase_view];

            R_lr.noalias() = R_r_base * R_l_base.transpose();

            Vector3d &x_l = ptr_track[left_base].coord;
            Vector3d &x_r = ptr_track[right_base].coord;

            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

            R_lr_x_l.noalias() = R_lr * x_l;
            m3_lr.noalias() = cross_xr * R_lr_x_l;

            tmp_A.noalias() = m3_lr.transpose() * cross_xr;
            tmp_A.noalias() = -tmp_A;
            tmp_A_R_lr.noalias() = -tmp_A * R_lr;

            id_l = 3 * lbase_view - 3;
            id_r = 3 * rbase_view - 3;

            track_len = ptr_track.size();
            double tmp_pts_residual = 0;

            for (j = 0; j < track_len; ++j)
            {

                ViewId &current_view = ptr_track[j].view_id;

                if (current_view == lbase_view)
                    continue;

                Matrix3d &R_c = global_rotations_[current_view];
                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                R_lc.noalias() = R_c * R_l_base.transpose();
                Rlc_xl.noalias() = R_lc * x_l;

                m3_lc.noalias() = x_c.cross(Rlc_xl);

                s = m3_lc.norm();

                R_lc_x_l.noalias() = R_lc * x_l;
                xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                tmp_residual_l.noalias() = tmp_F_lbase * global_translations_[lbase_view];
                tmp_residual_r.noalias() = tmp_F_rbase * global_translations_[rbase_view];
                tmp_residual_c.noalias() = tmp_F_cur * global_translations_[current_view];

                tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                tmp_residual = tmp_residual / base_s / base_s;

                tmp_pts_residual += tmp_residual.norm();
            }

            pts_residuals[i] = tmp_pts_residual / track_len;
        }

        double total_residual = pts_residuals.mean();

        std::cout << "pts_residual = " << total_residual
                  << endl;

        std::cout << "time for calculating pts residuals: "
                  << timer.Duration()
                  << "s" << endl;
    }

    void RobustLiGT::BuildLTLRemovingOutlier(Eigen::MatrixXd &LTL,
                                             RowVectorXd &A_lr,
                                             const int &threshold)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        A_lr.setZero(est_t_length);
        LTL.setZero(est_t_length, est_t_length);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;
        unsigned int used_obs = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        int num_removed = 0;

        InitCoviews();

        for (i = 0; i < pts_len; ++i)
        {
            if (!tracks_info_ptr_->tracks[i].is_used)
                continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s);

            ViewId &left_base = max_id_l;
            ViewId &right_base = max_id_r;

            ViewId &lbase_view = ptr_track[left_base].view_id;
            ViewId &rbase_view = ptr_track[right_base].view_id;

            double &base_s = max_s;

            Matrix3d &R_l_base = global_rotations_[lbase_view];
            Matrix3d &R_r_base = global_rotations_[rbase_view];

            R_lr.noalias() = R_r_base * R_l_base.transpose();

            Vector3d &x_l = ptr_track[left_base].coord;
            Vector3d &x_r = ptr_track[right_base].coord;

            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

            R_lr_x_l.noalias() = R_lr * x_l;
            m3_lr.noalias() = cross_xr * R_lr_x_l;

            tmp_A.noalias() = m3_lr.transpose() * cross_xr;
            tmp_A.noalias() = -tmp_A;
            tmp_A_R_lr.noalias() = -tmp_A * R_lr;

            id_l = 3 * lbase_view - 3;
            id_r = 3 * rbase_view - 3;

            if (id_l >= 0)
            {
                A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
            }

            if (id_r >= 0)
            {
                A_lr.middleCols(id_r, 3) += tmp_A;
            }

            track_len = ptr_track.size();
            double tmp_weight = 0;

            for (j = 0; j < track_len; ++j)
            {

                used_obs++;
                ViewId &current_view = ptr_track[j].view_id;

                //            if (current_view == lbase_view)
                if (current_view == lbase_view || !ptr_track[j].is_used)
                    continue;

                Matrix3d &R_c = global_rotations_[current_view];
                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                R_lc.noalias() = R_c * R_l_base.transpose();
                Rlc_xl.noalias() = R_lc * x_l;

                m3_lc.noalias() = x_c.cross(Rlc_xl);

                s = m3_lc.norm();

                R_lc_x_l.noalias() = R_lc * x_l;
                xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                tmp_residual_l.noalias() = tmp_F_lbase * global_translations_[lbase_view];
                tmp_residual_r.noalias() = tmp_F_rbase * global_translations_[rbase_view];
                tmp_residual_c.noalias() = tmp_F_cur * global_translations_[current_view];

                tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                tmp_residual = tmp_residual / base_s / base_s;

                if (tmp_residual.norm() > threshold * std_redisuals_ + mean_residuals_)
                {
                    num_removed++;
                    ptr_track[j].is_used = false;
                    continue;
                }

                BuildCoviews(lbase_view,
                             rbase_view,
                             current_view);

                FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
                FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
                FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

                id_c = 3 * current_view - 3;

                if (id_l >= 0)
                {

                    LTL.block<3, 3>(id_l, id_l) += FtF_ll;
                }

                if (id_r >= 0)
                {

                    LTL.block<3, 3>(id_r, id_r) += FtF_rr;
                }

                if (id_c >= 0)
                {

                    LTL.block<3, 3>(id_c, id_c) += FtF_cc;
                }

                FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
                if (id_l >= 0 && id_r >= 0)
                {
                    if (id_l < id_r)
                    {
                        LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                    }
                    else
                    {
                        LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                    }
                }

                FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
                if (id_l >= 0 && id_c >= 0)
                {
                    if (id_l < id_c)
                    {

                        LTL.block<3, 3>(id_l, id_c) += FtF_lc;
                    }
                    else
                    {

                        LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();
                    }
                }

                FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
                if (id_r >= 0 && id_c >= 0)
                {
                    if (id_r < id_c)
                    {

                        LTL.block<3, 3>(id_r, id_c) += FtF_rc;
                    }
                    else if (id_r == id_c)
                    {

                        LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;
                    }
                    else
                    {

                        LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();
                    }
                }
            }
        }

        cout << "## removed outliers: " << num_removed
             << ", used_obs = " << used_obs
             << endl;

        cout << "time for constructing LTL matrix: "
             << timer.Duration()
             << "s"
             << endl;
    }

    void RobustLiGT::BuildRobustLTL(Eigen::MatrixXd &LTL,
                                    RowVectorXd &A_lr)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        A_lr.setZero(est_t_length);
        LTL.setZero(est_t_length, est_t_length);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int used_obs = 0;
        residuals_.clear();

        for (i = 0; i < pts_len; ++i)
        {
            if (!tracks_info_ptr_->tracks[i].is_used)
                continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;
            track_len = ptr_track.size();

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            if (!SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s))
                continue;

            ViewId &left_base = max_id_l;
            ViewId &right_base = max_id_r;

            ViewId &lbase_view = ptr_track[left_base].view_id;
            ViewId &rbase_view = ptr_track[right_base].view_id;

            double &base_s = max_s;

            Matrix3d &R_l_base = global_rotations_[lbase_view];
            Matrix3d &R_r_base = global_rotations_[rbase_view];

            R_lr.noalias() = R_r_base * R_l_base.transpose();

            Vector3d &x_l = ptr_track[left_base].coord;
            Vector3d &x_r = ptr_track[right_base].coord;

            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

            R_lr_x_l.noalias() = R_lr * x_l;
            m3_lr.noalias() = cross_xr * R_lr_x_l;

            tmp_A.noalias() = m3_lr.transpose() * cross_xr;
            tmp_A.noalias() = -tmp_A;
            tmp_A_R_lr.noalias() = -tmp_A * R_lr;

            id_l = 3 * lbase_view - 3;
            id_r = 3 * rbase_view - 3;

            if (id_l >= 0)
            {
                A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
            }

            if (id_r >= 0)
            {
                A_lr.middleCols(id_r, 3) += tmp_A;
            }

            for (j = 0; j < track_len; ++j)
            {

                ViewId &current_view = ptr_track[j].view_id;

                if (!ptr_track[j].is_used)
                    continue;

                if (current_view == lbase_view)
                {
                    continue;
                }

                used_obs++;

                Matrix3d &R_c = global_rotations_[current_view];
                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                R_lc.noalias() = R_c * R_l_base.transpose();
                Rlc_xl.noalias() = R_lc * x_l;

                m3_lc.noalias() = x_c.cross(Rlc_xl);

                s = m3_lc.norm();

                R_lc_x_l.noalias() = R_lc * x_l;
                xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
                FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
                FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

                //            cout<<"FtF_ll.det="<<FtF_ll.determinant()<<", ";
                //            cout<<"FtF_rr.det="<<FtF_rr.determinant()<<", ";
                //            cout<<"FtF_cc.det="<<FtF_cc.determinant()<<endl;
                //            Matrix<double,3,9> test;
                //            test.block<3,3>(0,0) = tmp_F_lbase;
                //            test.block<3,3>(0,3) = tmp_F_rbase;
                //            test.block<3,3>(0,6) = tmp_F_cur;

                //            Matrix3d test2 = test * test.transpose();
                ////            cout<<(test2).determinant()<<endl;
                //            if ((test2).determinant() > 1e-10) continue;

                id_c = 3 * current_view - 3;

                if (id_l >= 0)
                {

                    LTL.block<3, 3>(id_l, id_l) += FtF_ll;
                }

                if (id_r >= 0)
                {

                    LTL.block<3, 3>(id_r, id_r) += FtF_rr;
                }

                if (id_c >= 0)
                {

                    LTL.block<3, 3>(id_c, id_c) += FtF_cc;
                }

                FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
                if (id_l >= 0 && id_r >= 0)
                {
                    if (id_l < id_r)
                    {
                        LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                    }
                    else
                    {
                        LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                    }
                }

                FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
                if (id_l >= 0 && id_c >= 0)
                {
                    if (id_l < id_c)
                    {

                        LTL.block<3, 3>(id_l, id_c) += FtF_lc;
                    }
                    else
                    {

                        LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();
                    }
                }

                FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
                if (id_r >= 0 && id_c >= 0)
                {
                    if (id_r < id_c)
                    {

                        LTL.block<3, 3>(id_r, id_c) += FtF_rc;
                    }
                    else if (id_r == id_c)
                    {

                        LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;
                    }
                    else
                    {

                        LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();
                    }
                }
            }
        }

        cout << "used_pts=" << used_pts << endl;
        cout << "used_obs=" << used_obs << endl;

        cout << "time for constructing robust LTL matrix: "
             << timer.Duration()
             << "s"
             << endl;
    }

    void RobustLiGT::BuildFullyRobustLTL(Eigen::MatrixXd &LTL,
                                         RowVectorXd &A_lr)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        A_lr.setZero(est_t_length);
        LTL.setZero(est_t_length, est_t_length);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int used_obs = 0;
        residuals_.clear();

        for (PtsId pts_id = 0; pts_id < pts_len; ++pts_id)
        {
            if (!tracks_info_ptr_->tracks[pts_id].is_used)
                continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[pts_id].track;
            track_len = ptr_track.size();

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            for (ObsId i = 0; i < track_len; ++i)
            {
                if (!ptr_track[i].is_used)
                    continue;

                ViewId &left_base = i;
                ViewId &lbase_view = ptr_track[left_base].view_id;

                if (!SelectRightBaseView(ptr_track, left_base, max_id_r))
                    continue;

                ViewId &right_base = max_id_r;
                ViewId &rbase_view = ptr_track[right_base].view_id;

                Matrix3d &R_l_base = global_rotations_[lbase_view];
                Matrix3d &R_r_base = global_rotations_[rbase_view];

                R_lr.noalias() = R_r_base * R_l_base.transpose();

                Vector3d &x_l = ptr_track[left_base].coord;
                Vector3d &x_r = ptr_track[right_base].coord;

                cross_xr << 0, -x_r(2), x_r(1),
                    x_r(2), 0, -x_r(0),
                    -x_r(1), x_r(0), 0;

                R_lr_x_l.noalias() = R_lr * x_l;
                m3_lr.noalias() = cross_xr * R_lr_x_l;
                double base_s = m3_lr.norm();

                tmp_A.noalias() = m3_lr.transpose() * cross_xr;
                tmp_A.noalias() = -tmp_A;
                tmp_A_R_lr.noalias() = -tmp_A * R_lr;

                id_l = 3 * lbase_view - 3;
                id_r = 3 * rbase_view - 3;

                if (id_l >= 0)
                {
                    A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
                }

                if (id_r >= 0)
                {
                    A_lr.middleCols(id_r, 3) += tmp_A;
                }

                for (j = 0; j < track_len; ++j)
                {

                    ViewId &current_view = ptr_track[j].view_id;

                    if (!ptr_track[j].is_used)
                        continue;

                    if (current_view == lbase_view)
                    {
                        continue;
                    }

                    used_obs++;

                    Matrix3d &R_c = global_rotations_[current_view];
                    Vector3d &x_c = ptr_track[j].coord;

                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    R_lc.noalias() = R_c * R_l_base.transpose();
                    Rlc_xl.noalias() = R_lc * x_l;

                    m3_lc.noalias() = x_c.cross(Rlc_xl);

                    s = m3_lc.norm();

                    R_lc_x_l.noalias() = R_lc * x_l;
                    xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                    tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                    tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                    tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                    FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
                    FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
                    FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

                    id_c = 3 * current_view - 3;

                    if (id_l >= 0)
                    {

                        LTL.block<3, 3>(id_l, id_l) += FtF_ll;
                    }

                    if (id_r >= 0)
                    {

                        LTL.block<3, 3>(id_r, id_r) += FtF_rr;
                    }

                    if (id_c >= 0)
                    {

                        LTL.block<3, 3>(id_c, id_c) += FtF_cc;
                    }

                    FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
                    if (id_l >= 0 && id_r >= 0)
                    {
                        if (id_l < id_r)
                        {
                            LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                        }
                        else
                        {
                            LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                        }
                    }

                    FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
                    if (id_l >= 0 && id_c >= 0)
                    {
                        if (id_l < id_c)
                        {

                            LTL.block<3, 3>(id_l, id_c) += FtF_lc;
                        }
                        else
                        {

                            LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();
                        }
                    }

                    FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
                    if (id_r >= 0 && id_c >= 0)
                    {
                        if (id_r < id_c)
                        {

                            LTL.block<3, 3>(id_r, id_c) += FtF_rc;
                        }
                        else if (id_r == id_c)
                        {

                            LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;
                        }
                        else
                        {

                            LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();
                        }
                    }
                }
            }
        }

        cout << "used_pts=" << used_pts << endl;
        cout << "used_obs=" << used_obs << endl;

        cout << "time for constructing [fully] robust LTL matrix: "
             << timer.Duration()
             << "s"
             << endl;
    }

    void RobustLiGT::CheckPenalty(Eigen::MatrixXd &LTL)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_B_l, tmp_B_r;

        Matrix3d BtB_ll, BtB_rr;
        Matrix3d BtB_lr;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int used_obs = 0;
        residuals_.clear();

        double PRI_threshold = 0.02;
        double total_RPI = 0;
        int count = 0;

        for (i = 0; i < features_.size(); ++i)
        {
            const auto &left_feature_vec = features_[i];
            const ViewId &left_view_id = left_feature_vec.feature[0].obs_ptr->view_id;

            Matrix3d &R_l = global_rotations_[left_view_id];

            for (j = i + 1; j < features_.size(); ++j)
            {

                const auto &right_feature_vec = features_[j];
                const ViewId &right_view_id = right_feature_vec.feature[0].obs_ptr->view_id;

                Matrix3d &R_r = global_rotations_[right_view_id];
                Matrix3d R_lr = R_r * R_l.transpose();

                double PRI_value = 0;
                int num_matches = PRIFromTwoViews(left_feature_vec, right_feature_vec, PRI_value);
                if (num_matches)
                {
                    // build penaly matrix: A_lr*t = tlr = tr - Rlr * tl;
                    int id_r, id_l;

                    id_l = 3 * left_view_id - 3;
                    id_r = 3 * right_view_id - 3;

                    PRIInfo pri_info;
                    pri_info.left_view_id = left_view_id;
                    pri_info.right_view_id = right_view_id;
                    pri_info.PRI_value = PRI_value;
                    pri_info.num_matches = num_matches;

                    PRI_vec_.emplace_back(pri_info);
                    double ratio = 0;
                    ratio = 0.0005 * PRI_value;

                    total_RPI += PRI_value;

                    tmp_B_l = -R_lr;
                    tmp_B_r = Eigen::Matrix3d::Identity();

                    BtB_ll.noalias() = tmp_B_l.transpose() * tmp_B_l;
                    BtB_rr.noalias() = tmp_B_r.transpose() * tmp_B_r;

                    if (id_l >= 0)
                    {

                        LTL.block<3, 3>(id_l, id_l) -= BtB_ll * ratio;
                    }

                    if (id_r >= 0)
                    {

                        LTL.block<3, 3>(id_r, id_r) -= BtB_rr * ratio;
                    }

                    BtB_lr = tmp_B_l.transpose() * tmp_B_r;
                    if (id_l >= 0 && id_r >= 0)
                    {
                        if (id_l < id_r)
                        {
                            LTL.block<3, 3>(id_l, id_r) -= BtB_lr * ratio;
                        }
                        else
                        {
                            LTL.block<3, 3>(id_r, id_l) -= BtB_lr.transpose() * ratio;
                        }
                    }
                }
            }
        }

        cout << "total_RPI = " << total_RPI << endl;

        fstream output("/home/<USER>/pri_test.txt", std::ios::out | ios::trunc);
        if (!output.is_open())
        {
            cout << "output file cannot create, please check path" << endl;
        }

        for (int i = 0; i < PRI_vec_.size(); ++i)
        {

            output << setprecision(16)
                   << PRI_vec_[i].left_view_id
                   << " "
                   << PRI_vec_[i].right_view_id
                   << " "
                   << PRI_vec_[i].PRI_value
                   << " "
                   << PRI_vec_[i].num_matches
                   << endl;
        }
        output.close();

        cout << "time for BuildCollapsePenalty: "
             << timer.Duration()
             << "s"
             << endl;
    }

    void RobustLiGT::BuildCollapsePenalty(Eigen::MatrixXd &LTL)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_B_l, tmp_B_r;

        Matrix3d BtB_ll, BtB_rr;
        Matrix3d BtB_lr;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int used_obs = 0;
        residuals_.clear();

        double PRI_threshold = 0.02;
        double total_RPI = 0;
        int count = 0;

        for (i = 0; i < features_.size(); ++i)
        {
            const auto &left_feature_vec = features_[i];
            const ViewId &left_view_id = left_feature_vec.feature[0].obs_ptr->view_id;

            Matrix3d &R_l = global_rotations_[left_view_id];

            for (j = i + 1; j < features_.size(); ++j)
            {

                const auto &right_feature_vec = features_[j];
                const ViewId &right_view_id = right_feature_vec.feature[0].obs_ptr->view_id;

                Matrix3d &R_r = global_rotations_[right_view_id];
                Matrix3d R_lr = R_r * R_l.transpose();

                double PRI_value = 0;
                int num_matches = PRIFromTwoViews(left_feature_vec, right_feature_vec, PRI_value);
                int num_features = left_feature_vec.feature.size();
                if (num_matches)
                {
                    // build penaly matrix: A_lr*t = tlr = tr - Rlr * tl;
                    int id_r, id_l;

                    id_l = 3 * left_view_id - 3;
                    id_r = 3 * right_view_id - 3;

                    PRIInfo pri_info;
                    pri_info.left_view_id = left_view_id;
                    pri_info.right_view_id = right_view_id;
                    //                pri_info.PRI_value = PRI_value;
                    double matches_ratio = (0.0 + num_matches) / (0.0 + num_features);
                    pri_info.PRI_value = PRI_value / matches_ratio;
                    pri_info.num_matches = num_matches;

                    PRI_vec_.emplace_back(pri_info);
                    double ratio = 0;
                    ratio = 0.0005 * PRI_value;

                    total_RPI += PRI_value;

                    tmp_B_l = -R_lr;
                    tmp_B_r = Eigen::Matrix3d::Identity();

                    BtB_ll.noalias() = tmp_B_l.transpose() * tmp_B_l;
                    BtB_rr.noalias() = tmp_B_r.transpose() * tmp_B_r;

                    if (id_l >= 0)
                    {

                        LTL.block<3, 3>(id_l, id_l) -= BtB_ll * ratio;
                    }

                    if (id_r >= 0)
                    {

                        LTL.block<3, 3>(id_r, id_r) -= BtB_rr * ratio;
                    }

                    BtB_lr = tmp_B_l.transpose() * tmp_B_r;
                    if (id_l >= 0 && id_r >= 0)
                    {
                        if (id_l < id_r)
                        {
                            LTL.block<3, 3>(id_l, id_r) -= BtB_lr * ratio;
                        }
                        else
                        {
                            LTL.block<3, 3>(id_r, id_l) -= BtB_lr.transpose() * ratio;
                        }
                    }
                }
            }
        }

        cout << "total_RPI = " << total_RPI << endl;

        fstream output("/home/<USER>/pri_test.txt", std::ios::out | ios::trunc);
        if (!output.is_open())
        {
            cout << "output file cannot create, please check path" << endl;
        }

        for (int i = 0; i < PRI_vec_.size(); ++i)
        {

            output << setprecision(16)
                   << PRI_vec_[i].left_view_id
                   << " "
                   << PRI_vec_[i].right_view_id
                   << " "
                   << PRI_vec_[i].PRI_value
                   << " "
                   << PRI_vec_[i].num_matches
                   << endl;
        }
        output.close();

        cout << "time for BuildCollapsePenalty: "
             << timer.Duration()
             << "s"
             << endl;
    }

    // void RobustLiGT::BuildCollapsePenalty(Eigen::MatrixXd &LTL){

    //    POVG::Timer timer;

    //    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

    //    Size est_t_length = 3 * est_view_size-3;

    //    Matrix3d cross_xr, cross_xc;

    //    double s;

    //    Matrix3d R_lr, R_lc;

    //    Vector3d Rlc_xl;
    //    Vector3d m3_lr, m3_lc;
    //    RowVector3d tmp_A;
    //    Matrix3d tmp_B_l, tmp_B_r;

    //    Matrix3d BtB_ll,BtB_rr;
    //    Matrix3d BtB_lr;

    //    unsigned int track_len;
    //    unsigned int i = 0;
    //    unsigned int j = 0;
    //    unsigned int k = 0;

    //    PtsId pts_len = num_pts_;
    //    Vector3d R_lr_x_l,R_lc_x_l,xc_R_lc_x_l;
    //    RowVector3d tmp_A_R_lr;

    //    int id_r,id_l,id_c;

    //    unsigned int used_pts = 0;

    //    ObsId max_id_l = 0;
    //    ObsId max_id_r = 0;

    //    double max_s = 0;

    //    Vector3d Rx;
    //    Vector3d xRx;

    //    Vector3d tmp_residual_l,
    //             tmp_residual_r,
    //             tmp_residual_c,
    //             tmp_residual;

    //    double residual_value = 0;
    //    int num_removed = 0;
    //    int used_obs =0;
    //    residuals_.clear();

    //    double PRI_threshold = 0.00 ;
    //    double total_RPI = 0;
    //    int count = 0;

    //    for (i = 0; i < features_.size(); ++i){
    //        const auto& left_feature_vec = features_[i];
    //        const ViewId& left_view_id = left_feature_vec.feature[0].obs_ptr->view_id;

    //        Matrix3d& R_l = global_rotations_[left_view_id];

    //        double max_PRI_value = 0;
    //        ViewId max_id = 0;

    //        for (j = i + 1; j < features_.size(); ++j){

    //            const auto& right_feature_vec = features_[j];
    //            const ViewId& right_view_id = right_feature_vec.feature[0].obs_ptr->view_id;

    //            Matrix3d& R_r = global_rotations_[right_view_id];
    //            Matrix3d R_lr = R_r * R_l.transpose();

    //            double PRI_value = 0;
    //            int num_matches = PRIFromTwoViews(left_feature_vec,right_feature_vec,PRI_value);

    //            if (num_matches){
    //                PRIInfo pri_info;
    //                pri_info.left_view_id = left_view_id;
    //                pri_info.right_view_id = right_view_id;
    //                pri_info.PRI_value = PRI_value;
    //                pri_info.num_matches = num_matches;
    //                PRI_vec_.emplace_back(pri_info);

    //                if (max_PRI_value < PRI_value){
    //                    max_PRI_value = PRI_value;
    //                    max_id = right_view_id;
    //                }
    //            }

    //        }

    //        if (max_PRI_value)
    //        {
    //            const ViewId& right_view_id = max_id;
    //            // build penaly matrix: A_lr*t = tlr = tr - Rlr * tl;
    //            int id_r,id_l;

    //            id_l = 3 * left_view_id - 3;
    //            id_r = 3 * right_view_id - 3;

    //            double ratio = 0.0001;

    //            total_RPI += max_PRI_value;

    //            tmp_B_l = -R_lr;
    //            tmp_B_r = Eigen::Matrix3d::Identity();

    //            BtB_ll.noalias() = tmp_B_l.transpose() * tmp_B_l;
    //            BtB_rr.noalias() = tmp_B_r.transpose() * tmp_B_r;

    //            if (id_l >= 0){

    //                LTL.block<3, 3>(id_l, id_l) -= BtB_ll * ratio;
    //            }

    //            if (id_r >= 0){

    //                LTL.block<3, 3>(id_r, id_r) -= BtB_rr * ratio;

    //            }

    //            BtB_lr = tmp_B_l.transpose() * tmp_B_r;
    //            if (id_l >= 0 && id_r >= 0){
    //                if(id_l < id_r){
    //                    LTL.block<3, 3>(id_l, id_r) -= BtB_lr * ratio;
    //                }
    //                else{
    //                    LTL.block<3, 3>(id_r, id_l) -= BtB_lr.transpose() * ratio;
    //                }
    //            }
    //        }

    //    }

    //    cout<<"total_RPI = "<<total_RPI<<endl;

    //    fstream output("/home/<USER>/pri_test.txt",std::ios::out|ios::trunc);
    //    if(!output.is_open()){
    //        cout<<"output file cannot create, please check path" << endl;
    //    }

    //    for (int i = 0; i < PRI_vec_.size(); ++i){

    //        output << setprecision(16)
    //               << PRI_vec_[i].left_view_id
    //                  << " "
    //                  << PRI_vec_[i].right_view_id
    //                  << " "
    //                  << PRI_vec_[i].PRI_value
    //                  << " "
    //                  << PRI_vec_[i].num_matches
    //                  << endl;
    //    }
    //    output.close();

    //    cout << "time for BuildCollapsePenalty: "
    //         << timer.Duration()
    //         << "s"
    //         << endl;
    //}

    void RobustLiGT::BuildPRIVector()
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_B_l, tmp_B_r;

        Matrix3d BtB_ll, BtB_rr;
        Matrix3d BtB_lr;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int used_obs = 0;
        residuals_.clear();

        for (i = 0; i < features_.size(); ++i)
        {
            const auto &left_feature_vec = features_[i];
            const ViewId &left_view_id = left_feature_vec.feature[i].obs_ptr->view_id;

            Matrix3d &R_l = global_rotations_[left_view_id];

            for (j = 0; j < features_.size(); ++j)
            {

                const auto &right_feature_vec = features_[j];
                const ViewId &right_view_id = right_feature_vec.feature[j].obs_ptr->view_id;

                Matrix3d &R_r = global_rotations_[right_view_id];
                Matrix3d R_lr = R_r * R_l.transpose();

                double PRI_value = 0;
                if (PRIFromTwoViews(left_feature_vec, right_feature_vec, PRI_value))
                {
                    // build penaly matrix: A_lr*t = tlr = tr - Rlr * tl;
                    int id_r, id_l, id_c;

                    id_l = 3 * left_view_id - 3;
                    id_r = 3 * right_view_id - 3;

                    tmp_B_l = -R_lr;
                    tmp_B_r = Eigen::Matrix3d::Identity();

                    BtB_ll.noalias() = tmp_B_l.transpose() * tmp_B_l;
                    BtB_rr.noalias() = tmp_B_r.transpose() * tmp_B_r;
                }
            }
        }

        cout << "time for constructing robust(from features) LTL matrix: "
             << timer.Duration()
             << "s"
             << endl;
    }

    void RobustLiGT::BuildSingleLTL(Eigen::MatrixXd &LTL,
                                    RowVectorXd &A_lr,
                                    Track &track,
                                    const ObsInfo *obs_ptr,
                                    const double &weight)
    {
        double base_s = 0;
        ViewId left_base = 0;
        ViewId right_base = 0;

        if (!SelectBaseViews(track, left_base, right_base, base_s))
            return;

        Size track_len = track.size();

        ViewId &lbase_view = track[left_base].view_id;
        ViewId &rbase_view = track[right_base].view_id;

        Matrix3d &R_l_base = global_rotations_[lbase_view];
        Matrix3d &R_r_base = global_rotations_[rbase_view];

        Matrix3d R_lr = R_r_base * R_l_base.transpose();

        Vector3d &x_l = track[left_base].coord;
        Vector3d &x_r = track[right_base].coord;

        Matrix3d cross_xr, cross_xc;
        cross_xr << 0, -x_r(2), x_r(1),
            x_r(2), 0, -x_r(0),
            -x_r(1), x_r(0), 0;

        Vector3d R_lr_x_l = R_lr * x_l;
        Vector3d m3_lr = cross_xr * R_lr_x_l;

        RowVector3d tmp_A = m3_lr.transpose() * cross_xr;
        tmp_A.noalias() = -tmp_A;

        RowVector3d tmp_A_R_lr = -tmp_A * R_lr;

        int id_r, id_l, id_c;

        id_l = 3 * lbase_view - 3;
        id_r = 3 * rbase_view - 3;

        if (id_l >= 0)
        {
            A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
        }

        if (id_r >= 0)
        {
            A_lr.middleCols(id_r, 3) += tmp_A;
        }

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;
        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        for (int j = 0; j < track_len; ++j)
        {

            ViewId &current_view = track[j].view_id;

            if (!track[j].is_used || current_view == lbase_view)
                continue;

            if (obs_ptr)
                if (current_view != obs_ptr->view_id)
                    continue;

            Matrix3d &R_c = global_rotations_[current_view];
            Vector3d &x_c = track[j].coord;

            cross_xc << 0, -x_c(2), x_c(1),
                x_c(2), 0, -x_c(0),
                -x_c(1), x_c(0), 0;

            const Matrix3d &R_lc = R_c * R_l_base.transpose();
            const Vector3d &Rlc_xl = R_lc * x_l;

            const Vector3d &m3_lc = x_c.cross(Rlc_xl);

            const double &s = m3_lc.norm();

            const Vector3d &R_lc_x_l = R_lc * x_l;
            const Vector3d &xc_R_lc_x_l = cross_xc * R_lc_x_l;

            tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A * weight;

            tmp_F_cur.noalias() = cross_xc * (base_s * base_s) * weight;

            tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

            //       if (base_s > 0.1){

            //       }
            //       else{
            //           cout<<"PRI detection!" << endl;
            //           tmp_F_rbase.setZero();
            //           // [xc]x * tc - [xc]x * Rlc * tl
            //           tmp_F_cur = cross_xc ;

            //           tmp_F_lbase.noalias() = -(tmp_F_cur * R_lc);
            //       }

            FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
            FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
            FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

            id_c = 3 * current_view - 3;

            if (id_l >= 0)
            {

                LTL.block<3, 3>(id_l, id_l) += FtF_ll;
            }

            if (id_r >= 0)
            {

                LTL.block<3, 3>(id_r, id_r) += FtF_rr;
            }

            if (id_c >= 0)
            {

                LTL.block<3, 3>(id_c, id_c) += FtF_cc;
            }

            FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
            if (id_l >= 0 && id_r >= 0)
            {
                if (id_l < id_r)
                {
                    LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                }
                else
                {
                    LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                }
            }

            FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
            if (id_l >= 0 && id_c >= 0)
            {
                if (id_l < id_c)
                {

                    LTL.block<3, 3>(id_l, id_c) += FtF_lc;
                }
                else
                {

                    LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();
                }
            }

            FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
            if (id_r >= 0 && id_c >= 0)
            {
                if (id_r < id_c)
                {

                    LTL.block<3, 3>(id_r, id_c) += FtF_rc;
                }
                else if (id_r == id_c)
                {

                    LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;
                }
                else
                {

                    LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();
                }
            }
        }
    }

    void RobustLiGT::BuildSingleLTLPureRotation(Eigen::MatrixXd &LTL,
                                                Track &track,
                                                const ObsInfo *obs_ptr)
    {
        double base_s = 0;
        ViewId left_base = 0;
        ViewId right_base = 0;

        if (!SelectBaseViews(track, left_base, right_base, base_s))
            return;

        Size track_len = track.size();

        ViewId &lbase_view = track[left_base].view_id;
        ViewId &rbase_view = track[right_base].view_id;

        Matrix3d &R_l_base = global_rotations_[lbase_view];
        Matrix3d &R_r_base = global_rotations_[rbase_view];

        Matrix3d R_lr = R_r_base * R_l_base.transpose();

        Vector3d &x_l = track[left_base].coord;
        Vector3d &x_r = track[right_base].coord;

        Matrix3d cross_xr, cross_xc;
        cross_xr << 0, -x_r(2), x_r(1),
            x_r(2), 0, -x_r(0),
            -x_r(1), x_r(0), 0;

        Vector3d R_lr_x_l = R_lr * x_l;
        Vector3d m3_lr = cross_xr * R_lr_x_l;

        RowVector3d tmp_A = m3_lr.transpose() * cross_xr;
        tmp_A.noalias() = -tmp_A;

        RowVector3d tmp_A_R_lr = -tmp_A * R_lr;

        int id_r, id_l, id_c;

        id_l = 3 * lbase_view - 3;
        id_r = 3 * rbase_view - 3;

        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;
        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        for (int j = 0; j < track_len; ++j)
        {

            ViewId &current_view = track[j].view_id;

            if (!track[j].is_used || current_view == lbase_view)
                continue;

            if (obs_ptr)
                if (current_view != obs_ptr->view_id)
                    continue;

            Matrix3d &R_c = global_rotations_[current_view];
            Vector3d &x_c = track[j].coord;

            cross_xc << 0, -x_c(2), x_c(1),
                x_c(2), 0, -x_c(0),
                -x_c(1), x_c(0), 0;

            const Matrix3d &R_lc = R_c * R_l_base.transpose();
            const Vector3d &Rlc_xl = R_lc * x_l;

            const Vector3d &m3_lc = x_c.cross(Rlc_xl);

            const double &s = m3_lc.norm();

            const Vector3d &R_lc_x_l = R_lc * x_l;
            const Vector3d &xc_R_lc_x_l = cross_xc * R_lc_x_l;

            //       tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A / base_s;

            //       tmp_F_cur.noalias() = cross_xc * base_s;

            tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A / (base_s * base_s);

            tmp_F_cur.noalias() = cross_xc;

            tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

            //       if (base_s > 0.1){

            //       }
            //       else{
            //           cout<<"PRI detection!" << endl;
            //           tmp_F_rbase.setZero();
            //           // [xc]x * tc - [xc]x * Rlc * tl
            //           tmp_F_cur = cross_xc ;

            //           tmp_F_lbase.noalias() = -(tmp_F_cur * R_lc);
            //       }

            FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
            FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
            FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

            id_c = 3 * current_view - 3;

            if (id_l >= 0)
            {

                LTL.block<3, 3>(id_l, id_l) += FtF_ll;
            }

            if (id_r >= 0)
            {

                LTL.block<3, 3>(id_r, id_r) += FtF_rr;
            }

            if (id_c >= 0)
            {

                LTL.block<3, 3>(id_c, id_c) += FtF_cc;
            }

            FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
            if (id_l >= 0 && id_r >= 0)
            {
                if (id_l < id_r)
                {
                    LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                }
                else
                {
                    LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                }
            }

            FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
            if (id_l >= 0 && id_c >= 0)
            {
                if (id_l < id_c)
                {

                    LTL.block<3, 3>(id_l, id_c) += FtF_lc;
                }
                else
                {

                    LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();
                }
            }

            FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
            if (id_r >= 0 && id_c >= 0)
            {
                if (id_r < id_c)
                {

                    LTL.block<3, 3>(id_r, id_c) += FtF_rc;
                }
                else if (id_r == id_c)
                {

                    LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;
                }
                else
                {

                    LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();
                }
            }
        }
    }

    void RobustLiGT::BuildRobustLTLFromFeatures(Eigen::MatrixXd &LTL,
                                                RowVectorXd &A_lr)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        A_lr.setZero(est_t_length);
        LTL.setZero(est_t_length, est_t_length);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double PRI_threshold = 0.015;

        double residual_value = 0;
        int num_removed = 0;
        int used_obs = 0;
        residuals_.clear();

        for (i = 0; i < features_.size(); ++i)
        {
            auto &tmp_feature_vec = features_[i];
            used_obs = 0;
            for (j = 0; j < tmp_feature_vec.feature.size(); ++j)
            {
                const auto &tmp_feature = tmp_feature_vec.feature[j];
                ObsInfo *obs_ptr = tmp_feature.obs_ptr;
                if (!obs_ptr->is_used)
                    continue;
                used_obs++;
            }

            for (j = 0; j < tmp_feature_vec.feature.size(); ++j)
            {

                const auto &tmp_feature = tmp_feature_vec.feature[j];

                ObsInfo *obs_ptr = tmp_feature.obs_ptr;
                PtsId pts_id = obs_ptr->pts_id;

                if (!obs_ptr->is_used)
                    continue;

                //            if (used_obs >= 10) break;

                Track &ptr_track = tracks_info_ptr_->tracks[pts_id].track;

                double value = tmp_feature_vec.mean_m3_value;

                double weight = 1 / value / value;

                BuildSingleLTL(LTL, A_lr, ptr_track, obs_ptr, weight);
            }
        }

        cout << "used_obs = " << used_obs << endl;

        cout << "time for constructing robust(from features) LTL matrix: "
             << timer.Duration()
             << "s"
             << endl;
    }

    void RobustLiGT::RemoveObsOutliersV2(int id)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        ViewId left_base = 0;
        ViewId right_base = 0;

        PtsId pts_id;
        for (pts_id = 0; pts_id < pts_len; ++pts_id)
        {
            // 计算残差的时候,可以全部都用
            //        if(!tracks_info_ptr_->tracks[i].is_used)
            //            continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[pts_id].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            Size track_size = ptr_track.size();

            int estimated_num = 0;

            for (ObsId i = 0; i < track_size; ++i)
            {
                if (ptr_track[i].is_used)
                    estimated_num++;
            }

            if (estimated_num < 2)
            {
                tracks_info_ptr_->tracks[i].is_used = false;
                continue;
            }

            for (ObsId i = 0; i < track_size; ++i)
            {
                if (!ptr_track[i].is_used)
                    continue;

                residual_value = 0;
                vector<double> residual_values;

                ViewId &lbase_view = ptr_track[i].view_id;

                Matrix3d &R_l_base = global_rotations_[lbase_view];

                Vector3d &x_l = ptr_track[i].coord;

                track_len = ptr_track.size();
                int used_obs = 0;
                for (j = 0; j < track_len; ++j)
                {
                    if (!ptr_track[j].is_used || j == i)
                        continue;

                    ViewId &current_view = ptr_track[j].view_id;
                    Matrix3d &R_c = global_rotations_[current_view];
                    Vector3d &x_c = ptr_track[j].coord;

                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    R_lc.noalias() = R_c * R_l_base.transpose();
                    Rlc_xl.noalias() = R_lc * x_l;

                    m3_lc.noalias() = x_c.cross(Rlc_xl);

                    s = m3_lc.norm();

                    R_lc_x_l.noalias() = R_lc * x_l;
                    xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                    tmp_A.noalias() = m3_lc.transpose() * cross_xc;
                    tmp_A.noalias() = -tmp_A;

                    tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                    tmp_F_cur.noalias() = cross_xc * s * s;
                    tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lc + tmp_F_cur * R_lc);

                    tmp_residual_l.noalias() = tmp_F_lbase * global_translations_[lbase_view];
                    tmp_residual_r.noalias() = tmp_F_rbase * global_translations_[current_view];
                    tmp_residual_c.noalias() = tmp_F_cur * global_translations_[current_view];

                    tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;
                    //                tmp_residual /= s*s;
                    //                residual_value += tmp_residual.squaredNorm();
                    residual_value += tmp_residual.norm();

                    residual_values.emplace_back(tmp_residual.norm());
                    used_obs++;
                    //                auto normalized_residual = tmp_residual / (svd_value_/sqrt(num_used_obs_));
                }

                //            residuals_info_.first.emplace_back(findMedian(residual_values));
                residuals_info_.first.emplace_back((residual_value / used_obs));
                //            residuals_info_.first.emplace_back(sqrt(residual_value / used_obs));
                residuals_info_.second.emplace_back(ptr_track[i].obs_id);

                id_cur_obs++;
            }
        }

        DetectAndRemoveOutlier();

        cout << "residual_value = " << residual_value << endl;
        cout << "svd_value_ = " << svd_value_ << endl;
        cout << "id_cur_obs = " << id_cur_obs << endl;

        //    WriteOutlierInfo("",id);

        cout << "time for [Robust method v2] removing outliers: "
             << timer.Duration()
             << "s"
             << endl
             << "---------------------------------"
             << endl;
    }

    void RobustLiGT::GNC_RemoveObsOutliers(int id)
    {
    }

    void RobustLiGT::RemoveObsOutliers(int id)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();
        Size est_t_length = 3 * est_view_size - 3;

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        double residual_value = 0;
        int num_removed = 0;
        int id_cur_obs = 0;

        // reset residuals_info_
        residuals_info_.first.clear();
        residuals_info_.second.clear();

        ViewId left_base = 0;
        ViewId right_base = 0;

        PtsId pts_id;
        for (pts_id = 0; pts_id < pts_len; ++pts_id)
        {
            // 计算残差的时候,可以全部都用
            //        if(!tracks_info_ptr_->tracks[i].is_used)
            //            continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[pts_id].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            Size track_size = ptr_track.size();

            // 先检查Track里面跟踪长度是否<2，如果是，则不用构建LiGT误差
            int estimated_num = 0;

            for (ObsId i = 0; i < track_size; ++i)
            {
                if (ptr_track[i].is_used)
                    estimated_num++;
            }

            if (estimated_num < 2)
            {
                tracks_info_ptr_->tracks[i].is_used = false;
                continue;
            }

            // 对于Track长度>=2的观测，进行处理
            for (ObsId i = 0; i < track_size; ++i)
            {
                // 左视图处理
                if (!ptr_track[i].is_used)
                    continue;
                const ViewId &i_view_id = ptr_track[i].view_id;
                const Vector3d &i_coord = ptr_track[i].coord;

                Matrix3d &R_i = global_rotations_[i_view_id];
                left_base = i;
                double max_s = 1e-8;
                bool succussful = false;

                residual_value = 0;
                vector<double> residual_values;

                for (ObsId j = 0; j < track_size; ++j)
                {
                    // 右视图处理
                    if (!ptr_track[j].is_used || j == i)
                        continue;

                    const ViewId &j_view_id = ptr_track[j].view_id;
                    const Vector3d &j_coord = ptr_track[j].coord;

                    Matrix3d &R_j = global_rotations_[j_view_id];

                    Matrix3d R_ij = R_j * R_i.transpose();
                    Vector3d theta_ij = j_coord.cross(R_ij * i_coord);

                    double criterion_value = theta_ij.norm();

                    if (criterion_value > max_s)
                    {
                        succussful = true;
                        max_s = criterion_value;
                        right_base = j;
                    }
                }

                // 如果Track中的最大视差角小于max_s(1e-8)，则这组Track不用构建LiGT误差
                if (!succussful)
                    continue;

                ViewId &lbase_view = ptr_track[left_base].view_id;
                ViewId &rbase_view = ptr_track[right_base].view_id;

                double &base_s = max_s;

                Matrix3d &R_l_base = global_rotations_[lbase_view];
                Matrix3d &R_r_base = global_rotations_[rbase_view];

                R_lr.noalias() = R_r_base * R_l_base.transpose();

                Vector3d &x_l = ptr_track[left_base].coord;
                Vector3d &x_r = ptr_track[right_base].coord;

                cross_xr << 0, -x_r(2), x_r(1),
                    x_r(2), 0, -x_r(0),
                    -x_r(1), x_r(0), 0;

                R_lr_x_l.noalias() = R_lr * x_l;
                m3_lr.noalias() = cross_xr * R_lr_x_l;

                tmp_A.noalias() = m3_lr.transpose() * cross_xr;
                tmp_A.noalias() = -tmp_A;
                tmp_A_R_lr.noalias() = -tmp_A * R_lr;

                id_l = 3 * lbase_view - 3;
                id_r = 3 * rbase_view - 3;

                track_len = ptr_track.size();
                int used_obs = 0;
                for (j = 0; j < track_len; ++j)
                {

                    if (!ptr_track[j].is_used)
                        continue;

                    ViewId &current_view = ptr_track[j].view_id;
                    Matrix3d &R_c = global_rotations_[current_view];
                    Vector3d &x_c = ptr_track[j].coord;

                    cross_xc << 0, -x_c(2), x_c(1),
                        x_c(2), 0, -x_c(0),
                        -x_c(1), x_c(0), 0;

                    R_lc.noalias() = R_c * R_l_base.transpose();
                    Rlc_xl.noalias() = R_lc * x_l;

                    m3_lc.noalias() = x_c.cross(Rlc_xl);

                    s = m3_lc.norm();

                    R_lc_x_l.noalias() = R_lc * x_l;
                    xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                    tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                    tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                    tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                    tmp_residual_l.noalias() = tmp_F_lbase * global_translations_[lbase_view];
                    tmp_residual_r.noalias() = tmp_F_rbase * global_translations_[rbase_view];
                    tmp_residual_c.noalias() = tmp_F_cur * global_translations_[current_view];

                    tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                    // 按照基准视图（最大视差角）的倒数进行加权
                    tmp_residual /= base_s * base_s;

                    residual_value += tmp_residual.squaredNorm();
                    residual_values.emplace_back(tmp_residual.norm());
                    //                auto normalized_residual = tmp_residual / (svd_value_/sqrt(num_used_obs_));
                    used_obs++;
                }

                //            residuals_info_.first.emplace_back(findMedian(residual_values));
                residuals_info_.first.emplace_back(sqrt(residual_value / used_obs));
                residuals_info_.second.emplace_back(ptr_track[i].obs_id);

                id_cur_obs++;
            }
        }

        DetectAndRemoveOutlier();
        cout << "residual_value = " << residual_value << endl;
        cout << "svd_value_ = " << svd_value_ << endl;
        cout << "id_cur_obs = " << id_cur_obs << endl;

        //===================================================================================

        cout << "time for [Robust method] removing outliers: "
             << timer.Duration()
             << "s"
             << endl
             << "---------------------------------"
             << endl;
    }

    void RobustLiGT::RemoveOutlierNone(int id)
    {

        POVG::Timer timer;

        //    WriteOutlierInfo("",id);

        //===================================================================================

        cout << "time for [NONE] removing outliers: "
             << timer.Duration()
             << "s"
             << endl
             << "---------------------------------"
             << endl;
    }

    void RobustLiGT::BuildCoviews()
    {

        POVG::Timer timer;

        unsigned int i = 0;
        unsigned int j = 0;
        PtsId pts_len = num_pts_;
        unsigned int track_len;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        coviews_.setZero(num_est_view_ - 1, num_est_view_ - 1);

        for (i = 0; i < pts_len; ++i)
        {
            if (!tracks_info_ptr_->tracks[i].is_used)
                continue;
            Track &ptr_track = tracks_info_ptr_->tracks[i].track;
            track_len = ptr_track.size();
            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s);

            ViewId &left_base = max_id_l;
            ViewId &right_base = max_id_r;

            ViewId &lbase_view = ptr_track[left_base].view_id;
            ViewId &rbase_view = ptr_track[right_base].view_id;

            for (j = 0; j < track_len; ++j)
            {

                ViewId &current_view = ptr_track[j].view_id;

                if (current_view == lbase_view || !ptr_track[j].is_used)
                    continue;

                if (lbase_view > 0)
                    coviews_(lbase_view - 1, lbase_view - 1)++;

                if (rbase_view > 0)
                    coviews_(rbase_view - 1, rbase_view - 1)++;

                if (current_view > 0)
                    coviews_(current_view - 1, current_view - 1)++;

                if (lbase_view > 0 && rbase_view > 0)
                {
                    if (lbase_view > rbase_view)
                        coviews_(lbase_view - 1, rbase_view - 1)++;
                    else
                        coviews_(rbase_view - 1, lbase_view - 1)++;
                }

                if (lbase_view > 0 && current_view > 0)
                {
                    if (lbase_view > current_view)
                        coviews_(lbase_view - 1, current_view - 1)++;
                    else
                        coviews_(current_view - 1, lbase_view - 1)++;
                }

                if (rbase_view > 0 && current_view > 0)
                {
                    if (rbase_view > current_view)
                        coviews_(rbase_view - 1, current_view - 1)++;
                    else
                        coviews_(current_view - 1, rbase_view - 1)++;
                }
            }
        }

        cout << "time for BuildCoviews(): "
             << timer.Duration()
             << "s"
             << endl;
    }

    void RobustLiGT::BuildCoviews(const ViewId &lbase_view,
                                  const ViewId &rbase_view,
                                  const ViewId &current_view)
    {

        if (lbase_view > 0)
            coviews_(lbase_view - 1, lbase_view - 1)++;

        if (rbase_view > 0)
            coviews_(rbase_view - 1, rbase_view - 1)++;

        if (current_view > 0)
            coviews_(current_view - 1, current_view - 1)++;

        if (lbase_view > 0 && rbase_view > 0)
        {
            if (lbase_view < rbase_view)
                coviews_(lbase_view - 1, rbase_view - 1)++;
            else
                coviews_(rbase_view - 1, lbase_view - 1)++;
        }

        if (lbase_view > 0 && current_view > 0)
        {
            if (lbase_view < current_view)
                coviews_(lbase_view - 1, current_view - 1)++;
            else
                coviews_(current_view - 1, lbase_view - 1)++;
        }

        if (rbase_view > 0 && current_view > 0)
        {
            if (rbase_view <= current_view)
                coviews_(rbase_view - 1, current_view - 1)++;
            else
                coviews_(current_view - 1, rbase_view - 1)++;
        }
    }

    void RobustLiGT::CheckCoviews()
    {

        VectorXi sum_coviews = coviews_.colwise().sum();
        for (ViewId i = 0; i < sum_coviews.rows(); ++i)
        {

            if (sum_coviews(i) <= 2)
            {
                outlier_view_ids_.insert(i + 1);
            }
        }

        cout << " outlier view ids " << outlier_view_ids_.size() << " :";
        for (auto &outlier_view_id : outlier_view_ids_)
        {
            cout << outlier_view_id << ";";
        }

        cout << endl;
    }

    void RobustLiGT::UpdateLTL(MatrixXd &LTL)
    {

        for (auto &outlier_view_id : outlier_view_ids_)
        {
            LTL.block<3, 3>(3 * outlier_view_id - 3, 3 * outlier_view_id - 3) += 100 * Eigen::Matrix3d::Identity();
        }
    }

    void RobustLiGT::BuildEquallyWeightedLTL(Eigen::MatrixXd &LTL,
                                             RowVectorXd &A_lr)
    {

        POVG::Timer timer;

        Size est_view_size = est_info_ptr_->estimated_view_ids.size();

        Size est_t_length = 3 * est_view_size - 3;

        A_lr.setZero(est_t_length);
        LTL.setZero(est_t_length, est_t_length);

        Matrix3d cross_xr, cross_xc;

        double s;

        Matrix3d R_lr, R_lc;

        Vector3d Rlc_xl;
        Vector3d m3_lr, m3_lc;
        RowVector3d tmp_A;
        Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

        Matrix3d FtF_ll, FtF_rr, FtF_cc;
        Matrix3d FtF_lr, FtF_lc, FtF_rc;

        unsigned int track_len;
        unsigned int i = 0;
        unsigned int j = 0;
        unsigned int k = 0;

        PtsId pts_len = num_pts_;
        Vector3d R_lr_x_l, R_lc_x_l, xc_R_lc_x_l;
        RowVector3d tmp_A_R_lr;

        int id_r, id_l, id_c;

        unsigned int used_pts = 0;

        ObsId max_id_l = 0;
        ObsId max_id_r = 0;

        double max_s = 0;

        Vector3d Rx;
        Vector3d xRx;

        Vector3d tmp_residual_l,
            tmp_residual_r,
            tmp_residual_c,
            tmp_residual;

        for (i = 0; i < pts_len; ++i)
        {
            if (!tracks_info_ptr_->tracks[i].is_used)
                continue;
            used_pts++;

            Track &ptr_track = tracks_info_ptr_->tracks[i].track;

            max_id_l = 0;
            max_id_r = 0;
            max_s = 0;

            SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s);

            ViewId &left_base = max_id_l;
            ViewId &right_base = max_id_r;

            ViewId &lbase_view = ptr_track[left_base].view_id;
            ViewId &rbase_view = ptr_track[right_base].view_id;

            double &base_s = max_s;

            Matrix3d &R_l_base = global_rotations_[lbase_view];
            Matrix3d &R_r_base = global_rotations_[rbase_view];

            R_lr.noalias() = R_r_base * R_l_base.transpose();

            Vector3d &x_l = ptr_track[left_base].coord;
            Vector3d &x_r = ptr_track[right_base].coord;

            cross_xr << 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

            R_lr_x_l.noalias() = R_lr * x_l;
            m3_lr.noalias() = cross_xr * R_lr_x_l;

            tmp_A.noalias() = m3_lr.transpose() * cross_xr;
            tmp_A.noalias() = -tmp_A;
            tmp_A_R_lr.noalias() = -tmp_A * R_lr;

            id_l = 3 * lbase_view - 3;
            id_r = 3 * rbase_view - 3;

            if (id_l >= 0)
            {
                A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
            }

            if (id_r >= 0)
            {
                A_lr.middleCols(id_r, 3) += tmp_A;
            }

            track_len = ptr_track.size();
            double tmp_weight = 0;

            for (j = 0; j < track_len; ++j)
            {

                ViewId &current_view = ptr_track[j].view_id;

                if (current_view == lbase_view)
                    continue;

                Matrix3d &R_c = global_rotations_[current_view];
                Vector3d &x_c = ptr_track[j].coord;

                cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

                R_lc.noalias() = R_c * R_l_base.transpose();
                Rlc_xl.noalias() = R_lc * x_l;

                m3_lc.noalias() = x_c.cross(Rlc_xl);

                s = m3_lc.norm();

                R_lc_x_l.noalias() = R_lc * x_l;
                xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

                tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A;

                tmp_F_cur.noalias() = cross_xc * base_s * base_s;
                tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

                tmp_residual_l.noalias() = tmp_F_lbase * global_translations_[lbase_view];
                tmp_residual_r.noalias() = tmp_F_rbase * global_translations_[rbase_view];
                tmp_residual_c.noalias() = tmp_F_cur * global_translations_[current_view];

                tmp_residual.noalias() = tmp_residual_r + tmp_residual_c + tmp_residual_l;

                tmp_residual = tmp_residual / base_s / base_s;

                tmp_weight = 1 / tmp_residual.lpNorm<1>() / num_obs_;

                if (tmp_weight > 1e2)
                {
                    tmp_weight = 1e2;
                }

                tmp_F_lbase *= tmp_weight;
                tmp_F_rbase *= tmp_weight;
                tmp_F_cur *= tmp_weight;

                FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
                FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
                FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

                id_c = 3 * current_view - 3;

                if (id_l >= 0)
                {

                    LTL.block<3, 3>(id_l, id_l) += FtF_ll;
                }

                if (id_r >= 0)
                {

                    LTL.block<3, 3>(id_r, id_r) += FtF_rr;
                }

                if (id_c >= 0)
                {

                    LTL.block<3, 3>(id_c, id_c) += FtF_cc;
                }

                FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
                if (id_l >= 0 && id_r >= 0)
                {
                    if (id_l < id_r)
                    {
                        LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                    }
                    else
                    {
                        LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                    }
                }

                FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
                if (id_l >= 0 && id_c >= 0)
                {
                    if (id_l < id_c)
                    {

                        LTL.block<3, 3>(id_l, id_c) += FtF_lc;
                    }
                    else
                    {

                        LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();
                    }
                }

                FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
                if (id_r >= 0 && id_c >= 0)
                {
                    if (id_r < id_c)
                    {

                        LTL.block<3, 3>(id_r, id_c) += FtF_rc;
                    }
                    else if (id_r == id_c)
                    {

                        LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;
                    }
                    else
                    {

                        LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();
                    }
                }
            }
        }

        cout << "time for constructing LTL matrix: "
             << timer.Duration()
             << "s"
             << endl;
    }

    Translations RobustLiGT::Solve(const Eigen::MatrixXd &LTL,
                                   const Eigen::RowVectorXd &A_lr)
    {

        //[Step.4 in Pose-only Algorithm]: obtain the translation solution by using SVD
        VectorXd evectors = VectorXd::Zero(3 * num_est_view_ - 3);
        spMatrix sp_LTL = LTL.sparseView();
        SolveLiGT(sp_LTL, evectors);

        //[Step.5 in Pose-only Algorithm]: identify the right global translation solution
        IdentifySign(A_lr, evectors);

        // (optional) transform solution evectors into global_translations_
        Translations pre_solution;
        pre_solution.swap(global_translations_);

        global_translations_.clear();
        global_translations_.resize(num_est_view_);

        global_translations_[0].setZero();

        for (ViewId i = 1; i < num_est_view_; ++i)
        {
            global_translations_[i] = evectors.middleRows<3>(3 * i - 3);
        }

        cout << "-----------------------------------------\n";
        return pre_solution;
    }

    // #include <nag.h>

    // void RobustLiGT::NAGSolveLiGT(const MatrixXd &LTL,
    //                               VectorXd& evectors){
    //     POVG::Timer timer;

    //    // ========================= Solve Problem by Spectra's Eigs =======================
    //    // Construct matrix operation object using the wrapper class

    //    Nag_OrderType order;
    //    Integer exit_status, i, iter, m, rank, n, nplus2, pda;
    //    NagError fail;

    //    double resid, tol;

    //    m = num_view_ * 3 - 3;
    //    nplus2 = m + 2;

    //    double *a = 0, *b = 0, *x = 0;

    //    a = NAG_ALLOC((m + 2) * nplus2, double);
    //    b = NAG_ALLOC(m, double);
    //    x = NAG_ALLOC(nplus2, double);

    //    tol = 0.0;

    // #define NAG_COLUMN_MAJOR

    // #ifdef NAG_COLUMN_MAJOR
    // #define A(I, J) a[(J - 1) * pda + I - 1]
    //   order = Nag_ColMajor;
    // #else
    // #define A(I, J) a[(I - 1) * pda + J - 1]
    //   order = Nag_RowMajor;
    // #endif

    //  if (order == Nag_ColMajor)
    //        pda = m + 2;
    //      else
    //        pda = nplus2;

    //    for (int i = 1; i <= m; ++i) {
    //        for ( int j = 1; j <= m; ++j){
    //            A(i, j) = LTL(i-1,j-1);
    //        }

    //          b[i - 1] = 0;
    //        }

    //    nag_fit_glin_l1sol(order, m, a, b, nplus2, tol, x, &resid, &rank, &iter,
    //                           &fail);

    //    if (fail.code == NE_INT || fail.code == NE_INT_2 ||
    //            fail.code == NE_TOO_MANY_ITER || fail.code == NE_NO_LICENCE) {
    //          printf("Error from nag_fit_glin_l1sol (e02gac).\n%s\n", fail.message);
    //          exit_status = 1;
    //        } else {
    //          printf("\n");
    //          printf("resid = %11.2e  Rank = %5" NAG_IFMT "  Iterations ="
    //                 " %5" NAG_IFMT "\n",
    //                 resid, rank, iter);

    //          printf("\n");
    //          printf("Solution\n");

    //          for (i = 1; i <= n; ++i){
    //            printf("%10.4f", x[i - 1]);
    //            evectors(i-1) = x[i - 1];
    //          }

    //          printf("\n");
    //        }

    //    cout << "time for eigs solver : "
    //         << timer.Duration()
    //         << "s"
    //         << endl;

    //}

    // Translations RobustLiGT::NAGSolve(const Eigen::MatrixXd& LTL,
    //                                   const Eigen::RowVectorXd& A_lr){

    //    //[Step.4 in Pose-only Algorithm]: obtain the translation solution by using SVD
    //    VectorXd evectors = VectorXd::Zero( 3 * num_view_ - 3);
    //    NAGSolveLiGT(LTL, evectors);

    //    //[Step.5 in Pose-only Algorithm]: identify the right global translation solution
    //    IdentifySign(A_lr, evectors);

    //    // (optional) transform solution evectors into global_translations_
    //    Translations pre_solution;
    //    pre_solution.swap(global_translations_);

    //    global_translations_.clear();
    //    global_translations_.resize(num_view_);

    //    global_translations_[0].setZero();

    //    for (ViewId i = 1; i < num_view_; ++i) {
    //        global_translations_[i] = evectors.middleRows<3>(3 * i - 3);
    //    }

    //    return pre_solution;

    //}

    void RobustLiGT::RobustSolution()
    {

        PrintCopyright();

        cout << "\n************  Robust LiGT Solve Summary  **************\n"
             << "num_view = " << num_est_view_ << "; num_pts = " << num_pts_ << "; num_obs = " << num_obs_
             << endl;

        // start time clock
        POVG::Timer timer;

        // allocate memory for LTL matrix where Lt=0
        Eigen::MatrixXd LTL;

        // use A_lr * t > 0 to identify the correct sign of the translation result
        Eigen::RowVectorXd A_lr;

        // construct LTL and A_lr matrix from 3D points
        switch (ltl_mode_)
        {

        case FROM_TRACKS:
            BuildRobustLTL(LTL, A_lr);
            break;
        case FROM_FEATURES:
            BuildRobustLTLFromFeatures(LTL, A_lr);
            break;
        case FROM_FULLY_TRACKS:
            BuildFullyRobustLTL(LTL, A_lr);
            break;
        }

        //******************** Spectra使用Chol分解，可能存在问题 *****************************
        //    double lambda = 10;
        //    LTL = LTL + 10 * MatrixXd::Identity(LTL.rows(),LTL.cols());

        //*************************************************

        //    BuildCollapsePenalty(LTL);
        //    WriteLTL(LTL, -1);

        fstream output2("/home/<USER>/pri_each_view.txt", std::ios::out | ios::trunc);
        if (!output2.is_open())
        {
            cout << "output file cannot create, please check path" << endl;
        }

        for (int i = 0; i < features_.size(); ++i)
        {

            output2 << setprecision(16)
                    << features_[i].mean_m3_value
                    << endl;
        }
        output2.close();

        fstream output3("/home/<USER>/ndf_info.txt", std::ios::out | ios::trunc);
        if (!output3.is_open())
        {
            cout << "output file cannot create, please check path" << endl;
        }

        for (int i = 0; i < features_.size(); ++i)
        {

            output3 << setprecision(16)
                    << features_[i].num_depth_features
                    << " "
                    << features_[i].mean_ndf
                    << endl;
        }
        output3.close();

        // obtain translation solution
        //    OutlierCheck(LTL); //PRO detector
        Solve(LTL, A_lr);

        int num_iter = 1;
        Translations pre_solution;

        double solution_step;
        double solution_mean;

        double residual_step;
        double pre_mean_residual = 0;

        int i = 0;
        cout << "outlier_removal_types_=" << outlier_removal_types_ << endl;
        switch (outlier_removal_types_)
        {
        case ROBUST_LIGT:
            RemoveObsOutliers(i);
            break;

        case GNC_LIGT:
            GNC_RemoveObsOutliers(i);
            break;

        case PA:
            RemoveObsOutliersPA(i);
            break;

        case ORIGIN:
            RemoveObsOutliersOrigin(i);
            break;

        case ANAL_BA:
            RemoveOutlierAnalBA(i);
            break;

        case DLT_BA:
            RemoveOutlierDLTBA(i);
            break;

        case ROBUST_LIGT_V2:
            RemoveObsOutliersV2(i);
            break;

        case PA_V2:
            RemoveObsOutliersPAV2(i);
            break;

        case OPENMVG_BA:
            RemoveOutlierOpenMVGBA(i);
            break;

        case PTSPA_V2:
            RemovePtsOutliersPA(i);
            break;
        case NONE:
            RemoveOutlierNone(i);
            break;
        default:
            RemoveOutlierNone(i);
            break;
        }

        //    for ( int i = 0; i < num_iter; ++i){

        //        switch (outlier_removal_types_) {
        //        case ROBUST_LIGT:
        //            RemoveObsOutliers(i);
        //            break;

        //        case PA:
        //            RemoveObsOutliersPA(i);
        //            break;

        //        case ORIGIN:
        //            RemoveObsOutliersOrigin(i);
        //            break;

        //        case ANAL_BA:
        //            RemoveOutlierAnalBA(i);
        //            break;

        //        case DLT_BA:
        //            RemoveOutlierDLTBA(i);
        //            break;

        //        case ROBUST_LIGT_V2:
        //            RemoveObsOutliersV2(i);
        //            break;

        //        case PA_V2:
        //            RemoveObsOutliersPAV2(i);
        //            break;

        //        case OPENMVG_BA:
        //            RemoveOutlierOpenMVGBA(i);
        //            break;

        //        case PTSPA_V2:
        //            RemovePtsOutliersPA(i);
        //            break;

        //        default:
        //            RemoveObsOutliers(i);
        //            break;
        //        }

        //        BuildRobustLTL(LTL, A_lr);
        //        OutlierCheck(LTL);
        //        WriteLTL(LTL, i);

        //        pre_solution = Solve(LTL, A_lr);
        //        solution_step = 0;
        //        solution_mean = 0;
        //        for ( ViewId id = 0;  id < pre_solution.size(); ++id){
        //            solution_step += (pre_solution[id] - global_translations_[id]).lpNorm<1>();
        //            solution_mean += global_translations_[id].norm();
        //        }

        //        solution_step /= pre_solution.size();
        //        solution_mean /= pre_solution.size();

        //        double mean_residual = 0;
        //        for ( auto& tmp_residual : residuals_info_.first){
        //            mean_residual += abs(tmp_residual);
        //        }
        //        mean_residual /= residuals_info_.first.size();
        //        residual_step = abs(mean_residual - pre_mean_residual);
        //        pre_mean_residual = mean_residual;
        //        cout << " iter " << i
        //             << ": mean_residual = " << mean_residual
        //             << "| residual_step = " << residual_step
        //             << endl;
        //        if ( residual_step < 1e-6){
        //            break;
        //        }

        //    }

        // (optional) translation recovery
        RecoverViewIds();

        WriteOutlierInfo();
    }

    void RobustLiGT::SolutionRemovingOutlier()
    {

        PrintCopyright();

        cout << "\n************  Robust LiGT Solve Summary  **************\n"
             << "num_view = " << num_est_view_ << "; num_pts = " << num_pts_ << "; num_obs = " << num_obs_
             << endl;

        // start time clock
        POVG::Timer timer;

        // allocate memory for LTL matrix where Lt=0
        Eigen::MatrixXd LTL;

        // use A_lr * t > 0 to identify the correct sign of the translation result
        Eigen::RowVectorXd A_lr;

        // construct LTL and A_lr matrix from 3D points
        BuildLTL(LTL, A_lr);

        // obtain translation solution
        Solve(LTL, A_lr);

        int num_iter = 10;
        Translations pre_solution;

        double solution_step;
        double solution_mean;

        for (int i = 0; i < num_iter; ++i)
        {

            CalculateResidual();
            BuildLTLRemovingOutlier(LTL, A_lr, 1);
            CheckCoviews();
            UpdateLTL(LTL);

            WriteLTL(LTL, i);
            WriteCoviews(i);

            pre_solution = Solve(LTL, A_lr);

            solution_step = 0;
            solution_mean = 0;

            for (ViewId id = 0; id < pre_solution.size(); ++id)
            {
                solution_step += (pre_solution[id] - global_translations_[id]).lpNorm<1>();
                solution_mean += global_translations_[id].lpNorm<1>() / 3;
            }

            solution_step /= pre_solution.size();
            solution_mean /= pre_solution.size();

            cout << " iter " << i
                 << ": solution_step = " << solution_step
                 << "| solution_mean = " << solution_mean
                 << endl;

            if (solution_step < 1e-3 * solution_mean)
            {

                break;
            }
        }

        // (optional) translation recovery
        RecoverViewIds();

        WriteOutlierInfo();
    }

    // void RobustLiGT::NAGSolution() {

    //    PrintCopyright();

    //    cout << "\n************  Robust LiGT Solve Summary  **************\n"
    //         << "num_view = " << num_view_ << "; num_pts = " << num_pts_ << "; num_obs = " << num_obs_
    //         << endl;

    //    // start time clock
    //    POVG::Timer timer;

    //    // allocate memory for LTL matrix where Lt=0
    //    Eigen::MatrixXd LTL;

    //    // use A_lr * t > 0 to identify the correct sign of the translation result
    //    Eigen::RowVectorXd A_lr;

    //    // construct LTL and A_lr matrix from 3D points
    //    BuildLTL(LTL, A_lr);

    //    // obtain translation solution
    //    NAGSolve(LTL, A_lr);

    //    // (optional) translation recovery
    //    RecoverViewIds();

    //}

}
