// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#ifndef LIGT_ALGORITHM
#define LIGT_ALGORITHM

#pragma once
#include "povg_types.hpp"
#include <string>

namespace POVG {

// ============== The LiGT Algorithm (Fast Version 2.0.0) =============
// [Version History]
// v1.0.0: first release; parallelism by <PERSON>.
// v1.1.0: <PERSON><PERSON><PERSON> replaces <PERSON>igen; Block manipulation to implement LTL matrix.
// v2.0.0: merge the latest version of LiGT into this version.
//
// Coded by: Drs. <PERSON>
// Email: <EMAIL>, <EMAIL>
//
// [Conditions of Use]: the LiGT algorithm is distributed under
// the License of Attribution-ShareAlike 4.0 International
// (https://creativecommons.org/licenses/by-sa/4.0/).
//
//------------------
//-- Bibliography --
//------------------
// If you use it for a publication, please cite the following paper:
//- [1] "A Pose-only Solution to Visual Reconstruction and Navigation".
//- Authors: <AUTHORS>
//- Date: December 2021.
//- Journal: IEEE T-PAMI.
//
//- [2] "Equivalent constraints for two-view geometry: pose solution/pure rotation identification and 3D reconstruction".
//- Authors: <AUTHORS>
//- Date: December 2019.
//- Journal: IJCV.
//
// This is a dedicated version of the LiGT algorithm for openMVG, which is supported by
// the Inertial and Visual Fusion (VINF) research group in Shanghai Jiao Tong University
// @ https://www.researchgate.net/lab/Inertial-visual-Fusion-VINF-Yuanxin-Wu
//
// Note:
//
// 1. It does not consider the rank condition in Proposition 6 of the T-PAMI paper.
//

enum PRO_PROCESS_MODE{
    PRI_REMOVE_MODE=1,
    M3_REMOVE_MODE=2,
    OPENMVG_MODE=3,
    LIGT_PLUS_MODE=4,
    LIGT_ORIGIN_MODE=5
};

class LiGTProblem {
public:

    LiGTProblem();

    explicit LiGTProblem(LiGTProblem& problem);

    LiGTProblem(Tracks &tracks,
                Attitudes &global_R);

    LiGTProblem(const std::string& globalR_file,
                const std::string& track_file,
                const std::string& output_file,
                const std::string& time_file,
                const int& fixed_id,
                const int& min_tracking_length,
                const bool& bearing_mode = false,
                const PRO_PROCESS_MODE& pro_mode = LIGT_ORIGIN_MODE);

    virtual ~LiGTProblem(){
        if (tracks_info_ptr_)
            tracks_info_ptr_ = nullptr;

        if (est_info_ptr_)
            est_info_ptr_ = nullptr;
    };

    // initialize the LiGT problem
    void Init(const int& fixed_id = 0);

    // set global rotation input
    void SetupOrientation(const std::string& globalR_file);

    void BuildFeatures();

    // load tracks from a bal-format track file
    // [web details in bal format: https://grail.cs.washington.edu/projects/bal/]
    void LoadTracks(const std::string& track_file);

    // write translation result
    void WriteTranslation();

    // write time result
    void WriteTime();

    // [Step.2 in Pose-only Algorithm]: select the left/right-base views
    void SelectBaseViews(const Track& track,
                         ViewId& lbase_view_id,
                         ViewId& rbase_view_id,
                         ObsId& id_lbase,
                         ObsId& id_rbase);

    bool SelectBaseViews(const Track& track,
                         ObsId& id_lbase,
                         ObsId& id_rbase,
                         double &best_criterion_value);

    bool SelectRightBaseView(const Track& track,
                             const ObsId &id_lbase,
                             ObsId& id_rbase);

    // [Step.3 in Pose-only algorithm]: calculate local L matrix, update LTL and A_lr matrix
    void BuildLTL(Eigen::MatrixXd& LTL,
                  RowVectorXd& A_lr);

    void BuildLTLAcceptableParallaxAngle(Eigen::MatrixXd& LTL,
                                         RowVectorXd& A_lr);

    //[Step.4 in Pose-only Algorithm]: obtain the translation solution by using SVD
    void SolveLiGT(spMatrix& LTL,
                   VectorXd &evectors);

    // [Step.5 in Pose-only Algorithm]: identify the correct sign of the translation solution after using SVD
    void IdentifySign(const RowVectorXd& A_lr,
                      VectorXd& evectors);

    // LiGT solution
    void Solution();

    // LiGT solution (for Standard PRO Process)
    void PROSolution();

    // Pure Rotational Outlier check
    void OutlierCheck(MatrixXd &LTL);
    void OutlierCheckForOpenMVG(MatrixXd &LTL);
    void OutlierCheckfromPRI(MatrixXd &LTL);
    void OutlierCheckfromM3(MatrixXd &LTL);
    //
    void BuildSingleLTL(Eigen::MatrixXd &LTL,
                    RowVectorXd &A_lr,
                    Track& track,
                    const ObsInfo* obs_ptr,
                    const double& weight = 1);
    // get tracks pointer (help to set track inputs)
    Tracks GetTracks();

    // get rotations (help to set rotation inputs)
    Attitudes GetRotations();

    // get poses
    Poses GetPoses();

    // check information in tracks (such as num_view/num_pts/num_obs)
    // and build estimated information EstInfo
    void CheckTracks();

    // recover the view id into original view ids
    void RecoverViewIds();

    // print copyright claim
    void PrintCopyright();

    void SetMinTrackingLength(unsigned int length);

    void BuildObsMap();

    void SetPROFile(std::string file);
    void SetPROProcessMode(PRO_PROCESS_MODE mode);
    void WriteLTL(const Eigen::MatrixXd& LTL,
                  const int& id);

    // 纯旋转问题
    void BuildPureRotationalPenalty(Eigen::MatrixXd& LTL);

    int PRIFromTwoViews(const FeatureInfo& left_feature_vec,
                         const FeatureInfo& right_feature_vec,
                         double& PRI_value);

protected:

    std::vector<PRIInfo> PRI_vec_;

    //针对origin view ids中，是否存在纯旋转异常view id
    vector<ViewId> pro_view_ids_;
    vector<ViewId> outlier_view_ids_;
    PRO_PROCESS_MODE pro_mode_ = LIGT_ORIGIN_MODE;

    unsigned int min_tracking_length_;
    unsigned int num_view_;
    unsigned int num_est_view_;

    unsigned int num_pts_;
    unsigned int num_obs_;
    unsigned int num_used_obs_;
    double time_use_;
    bool bearing_mode_ = false;

    // output files
    std::string output_file_;
    std::string time_file_;
    std::string pro_file_ = "/home/<USER>/LiGT_outlier.txt";

    // tracks
    TracksInfoPtr tracks_info_ptr_ = nullptr;

    // estimated view information
    EstInfoPtr est_info_ptr_;

    // [Note]: global rotations are sorted by estimated view ids
    Attitudes global_rotations_;

    // [Note]: global translations are sorted by estimated view ids
    // defined in camera coordinate system
    Translations global_translations_;


    // [Note]: global poses are sorted by original view ids
    Poses poses_;

    // reference view id
    ViewId fixed_id_;

    double svd_value_;

    unordered_map<ObsId, ObsInfo*> obs_map_;

    Features features_;
};


}
#endif
