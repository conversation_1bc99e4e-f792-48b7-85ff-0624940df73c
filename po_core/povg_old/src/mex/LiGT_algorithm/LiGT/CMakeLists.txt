# LiGT算法库构建配置

# 收集头文件和源文件
file(GLOB_RECURSE ligt_headers *.hpp)
file(GLOB_RECURSE ligt_cpps *.cpp)

# 设置包含目录
INCLUDE_DIRECTORIES(
    ${EIGEN_INCLUDE_DIRS}
    ${SPECTRA_DIRS}
)

# 创建LiGT静态库
add_library(povg_ligt_algorithm STATIC ${ligt_headers} ${ligt_cpps})

# 设置目标属性
set_target_properties(povg_ligt_algorithm PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    POSITION_INDEPENDENT_CODE ON
)

# 设置包含目录
target_include_directories(povg_ligt_algorithm PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${EIGEN_INCLUDE_DIRS}
    ${SPECTRA_DIRS}
)

# 将源文件也添加到主静态库目标
# target_sources(povg_old_lib PRIVATE ${ligt_cpps})
set(POVGLIB_SOURCES ${POVGLIB_SOURCES} ${ligt_cpps} PARENT_SCOPE)

# 设置安装目录（可选）
if(CMAKE_INSTALL_PREFIX)
    install(TARGETS povg_ligt_algorithm DESTINATION lib)
    install(FILES ${ligt_headers} DESTINATION include/povg_ligt)
endif()

message(STATUS "LiGT algorithm library configured successfully")




