// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <fstream>
#include <iomanip>
#include <cstdio>

#include "povg_timer.hpp"
#include "relative_residuals.hpp"
#include <Eigen/Dense>
#include <unsupported/Eigen/LevenbergMarquardt>

#include <algorithm>
#include <numeric>
#include <vector>
#include <cmath>

using namespace std;

#define PI 3.141592653589793
namespace POVG
{

    Eigen::Matrix3d crossMatrix(const Eigen::Vector3d &x);

    double findMedian(VectorXd a);
    double findMedian(vector<double> a);

    //// Reshape a vector into a matrix
    Eigen::MatrixXd reshape(const Eigen::VectorXd &v, int rows, int cols)
    {
        return Eigen::Map<const Eigen::MatrixXd>(v.data(), rows, cols);
    }

    // Function to compute the Kronecker product of two matrices
    Eigen::MatrixXd kroneckerProduct(const Eigen::MatrixXd &A, const Eigen::MatrixXd &B)
    {
        Eigen::MatrixXd Kronecker(A.rows() * B.rows(), A.cols() * B.cols());
        for (int i = 0; i < A.rows(); ++i)
        {
            for (int j = 0; j < A.cols(); ++j)
            {
                Kronecker.block(i * B.rows(), j * B.cols(), B.rows(), B.cols()) = A(i, j) * B;
            }
        }
        return Kronecker;
    }

    Eigen::Vector3d triangulate2(const Eigen::Matrix3d &R,
                                 const Eigen::Vector3d &t,
                                 const Eigen::Vector3d &point1,
                                 const Eigen::Vector3d &point2)
    {
        // 将第二个点的方向向量旋转到第一个相机的坐标系下
        Eigen::Vector3d point2_unrotated = R * point2;

        // 构造矩阵 A 和向量 b
        Eigen::Vector2d b;
        b[0] = t.dot(point1);
        b[1] = t.dot(point2_unrotated);

        Eigen::Matrix2d A;
        A(0, 0) = point1.dot(point1);
        A(1, 0) = point1.dot(point2_unrotated);
        A(0, 1) = -A(1, 0);
        A(1, 1) = -point2_unrotated.dot(point2_unrotated);

        // 计算拉格朗日乘数
        Eigen::Vector2d lambda = A.inverse() * b;

        // 计算在各自相机坐标系下的3D点
        Eigen::Vector3d xm = lambda[0] * point1;
        Eigen::Vector3d xn = t + lambda[1] * point2_unrotated;

        // 返回世界坐标系下的3D点
        Eigen::Vector3d worldPoint = (xm + xn) / 2;
        return worldPoint;
    }

    Eigen::Vector3d triangulateOnePoint(const Eigen::Matrix3d &R,
                                        const Eigen::Vector3d &t,
                                        const Eigen::Vector3d &point1,
                                        const Eigen::Vector3d &point2)
    {
        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        MatrixXd A(4, 4);
        A.row(0) = point1(0) * P1.row(2) - point1(2) * P1.row(0);
        A.row(1) = point1(1) * P1.row(2) - point1(2) * P1.row(1);
        A.row(2) = point2(0) * P2.row(2) - point2(2) * P2.row(0);
        A.row(3) = point2(1) * P2.row(2) - point2(2) * P2.row(1);

        Eigen::JacobiSVD<MatrixXd> svd(A, Eigen::ComputeFullV);
        VectorXd X = svd.matrixV().col(3);
        Eigen::Vector3d worldPoint = X.head<3>() / X(3);

        return worldPoint;
    }

    VectorXd residual_BA(const BearingVector &v1,
                         const BearingVector &v2,
                         const Pose &pose,
                         const VectorXd *ptr_weigths)
    {
        int num_points = v1.cols();
        MatrixXd points3D(3, num_points);

        Matrix3d R = pose.rotations;
        Vector3d t = pose.translations;

        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            points3D.col(i) = triangulateOnePoint(R, t, point1, point2);
        }

        MatrixXd reproj_v1 = points3D;
        MatrixXd reproj_v2 = R.transpose() * (points3D.colwise() - t);

        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1(2, i);
            reproj_v2.col(i) = reproj_v2.col(i) / reproj_v2(2, i);
        }

        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            errors(i) = (v1.col(i) / v1(2, i) - reproj_v1.col(i)).squaredNorm() +
                        (v2.col(i) / v2(2, i) - reproj_v2.col(i)).squaredNorm();
        }

        //    std::cout<<"errors:"<<errors.transpose()<<endl;
        return errors.array().sqrt();
    }

    VectorXd residual_BA(const BearingVector &v1,
                         const BearingVector &v2,
                         const Pose &pose,
                         const Matrix3d &Kmat,
                         const VectorXd *ptr_weights)
    {
        int num_points = v1.cols();
        MatrixXd points3D(3, num_points);

        Matrix3d R = pose.rotations;
        Vector3d t = pose.translations;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            // 使用三角测量计算三维点
            points3D.col(i) = triangulateOnePoint(R, t, point1, point2);
        }

        // 使用内参数矩阵将三维点投影回图像平面
        MatrixXd reproj_v1 = Kmat * points3D;
        MatrixXd reproj_v2 = Kmat * (R.transpose() * (points3D.colwise() - t));

        // 归一化投影点，使其第三个坐标为 1
        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) /= reproj_v1(2, i);
            reproj_v2.col(i) /= reproj_v2(2, i);
        }

        // 计算像素级别的重投影误差
        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            // 从同质坐标归一化到二维坐标
            Vector2d original_v1 = v1.block<2, 1>(0, i) / v1(2, i);
            Vector2d original_v2 = v2.block<2, 1>(0, i) / v2(2, i);

            // 计算重投影误差
            errors(i) = (original_v1 - reproj_v1.block<2, 1>(0, i)).squaredNorm() +
                        (original_v2 - reproj_v2.block<2, 1>(0, i)).squaredNorm();
        }

        return errors.array().sqrt(); // 返回每个点的重投影误差的平方根
    }

    VectorXd residual_opengv(const BearingVector &v1,
                             const BearingVector &v2,
                             const Pose &pose,
                             const VectorXd *ptr_weights)
    {
        int num_points = v1.cols();
        MatrixXd points3D(3, num_points);

        Matrix3d R = pose.rotations;
        Vector3d t = pose.translations;

        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            points3D.col(i) = triangulate2(R, t, point1, point2);
        }

        MatrixXd reproj_v1 = points3D;
        MatrixXd reproj_v2 = R.transpose() * (points3D.colwise() - t);

        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1.col(i).norm();
            reproj_v2.col(i) = reproj_v2.col(i) / reproj_v2.col(i).norm();
        }

        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            errors(i) = (1 - v1.col(i).dot(reproj_v1.col(i))) + (1 - v2.col(i).dot(reproj_v2.col(i)));
        }

        //    std::cout<<"errors:"<<errors.mean()<<endl;
        return errors;
    }

    VectorXd residual_LiGT_Lmat(const BearingVector &v1,
                                const BearingVector &v2,
                                const Pose &pose,
                                Matrix3d &Lmat)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        Lmat.setZero();

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            double tmp5 = tmp4 * t.norm() + tmp2.dot(tmp3) * (R * X2).norm();

            Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
            Matrix3d tmp_L = tmp4 * crossMatrix(X1) + crossMatrix(X1) * (R * X2) * tmp2.transpose() * crossMatrix(X1);
            Lmat += tmp_L.transpose() * tmp_L;
            residual(k) = cost_vec.dot(cost_vec);
        }

        JacobiSVD<Matrix3d> svd(Lmat, Eigen::ComputeFullU | Eigen::ComputeFullV);
        MatrixXd U = svd.matrixU();
        MatrixXd S = svd.singularValues().asDiagonal();
        MatrixXd V = svd.matrixV();

        // 获取并打印最小奇异值
        double minSingularValue = svd.singularValues().minCoeff();
        cout << "Lmat Singular Values: " << svd.singularValues().transpose() << endl;

        return residual.array().sqrt();
    }

    VectorXd residual_LiRT(const BearingVector &v1,
                           const BearingVector &v2,
                           const Pose &pose,
                           const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp = X1.cross(R * X2);

            Matrix3d tmp1 = X1 * tmp.transpose() * crossMatrix(R * X2);
            Matrix3d tmp2 = (R * X2) * tmp.transpose() * crossMatrix(X1);
            Matrix3d tmp3 = tmp.squaredNorm() * Matrix3d::Identity();
            Vector3d cost_vec = (tmp1 - tmp2 + tmp3) * t;

            residual(k) = cost_vec.dot(cost_vec);
        }

        return residual.array().sqrt();
    }
    VectorXd residual_LiGT(const BearingVector &v1,
                           const BearingVector &v2,
                           const Pose &pose,
                           const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            double tmp5 = tmp4 * t.norm() + tmp2.dot(tmp3) * (R * X2).norm();

            Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
            cost_vec = cost_vec / tmp3.squaredNorm() * tmp2.norm(); // matlab LiGTlr,best
            residual(k) = cost_vec.dot(cost_vec);
        }

        return residual.array().sqrt();
    }

    VectorXd residual_Kneip(const BearingVector &v1,
                            const BearingVector &v2,
                            const Pose &pose,
                            const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp = (X1).cross(R * X2);
            //        Vector3d cost_vec = crossMatrix(tmp) * crossMatrix(tmp) * crossMatrix(X1) * t;
            //        Vector3d cost_vec = tmp * tmp.transpose() * t;
            residual(k) = tmp.norm() * tmp.transpose() * t;
            if (k < 10)
                cout << "tmp.norm()=" << asin(tmp.norm()) * 180 / PI << endl;
            //        residual(k) = tmp.transpose()*t;
            //        residual(k) = cost_vec.dot(cost_vec);
        }

        return residual.array().abs();
        //    return residual.array().sqrt();
    }

    VectorXd residual_opengv(const BearingVector &v1,
                             const BearingVector &v2,
                             const Pose &pose,
                             Matrix<double, 3, Dynamic> &points3D,
                             const VectorXd *ptr_weights)
    {
        int num_points = v1.cols();
        points3D.setZero(3, num_points);

        Matrix3d R = pose.rotations;
        Vector3d t = pose.translations;

        Matrix<double, 3, 4> P1 = Matrix<double, 3, 4>::Zero();
        P1.block<3, 3>(0, 0) = Matrix3d::Identity();

        Matrix<double, 3, 4> P2 = Matrix<double, 3, 4>::Zero();
        P2.block<3, 3>(0, 0) = R.transpose();
        P2.block<3, 1>(0, 3) = -R.transpose() * t;

        for (int i = 0; i < num_points; ++i)
        {
            Vector3d point1 = v1.col(i);
            Vector3d point2 = v2.col(i);

            points3D.col(i) = triangulateOnePoint(R, t, point1, point2);
        }

        MatrixXd reproj_v1 = points3D;
        MatrixXd reproj_v2 = R.transpose() * (points3D.colwise() - t);

        for (int i = 0; i < num_points; ++i)
        {
            reproj_v1.col(i) = reproj_v1.col(i) / reproj_v1.col(i).norm();
            reproj_v2.col(i) = reproj_v2.col(i) / reproj_v2.col(i).norm();
        }

        VectorXd errors(num_points);
        for (int i = 0; i < num_points; ++i)
        {
            errors(i) = (1 - v1.col(i).dot(reproj_v1.col(i))) + (1 - v2.col(i).dot(reproj_v2.col(i)));
        }

        //    std::cout<<"errors:"<<errors.transpose()<<endl;
        return errors;
    }

    VectorXd residual_LiGT_d3(const BearingVector &v1,
                              const BearingVector &v2,
                              const Pose &pose,
                              const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {
            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            double tmp5 = tmp4 * t.norm() + tmp2.dot(tmp3) * (R * X2).norm();

            Vector3d cost_vec = tmp4 * tmp3 + tmp2.dot(tmp3) * tmp1;
            cost_vec = cost_vec / tmp3.norm();
            residual(k) = cost_vec.norm();
        }

        return residual;
    }

    Eigen::Matrix3d crossMatrix(const Eigen::Vector3d &x)
    {
        Eigen::Matrix3d y;

        y << 0, -x(2), x(1),
            x(2), 0, -x(0),
            -x(1), x(0), 0;

        return y;
    }

    // VectorXd residual_LiGT_direct(const BearingVector& v1,
    //                        const BearingVector& v2,
    //                        const Pose& pose,
    //                        const VectorXd* ptr_weigths = nullptr) {
    //     int num_matches = v1.cols();
    //     VectorXd residual(num_matches);

    //    //随机数生成
    //    std::vector<int> indices(5);

    //    for (int k = 0; k < num_matches; ++k) {

    //        Matrix3d L = Matrix3d::Zero();

    //        const Vector3d& X1 = v1.col(k);
    //        const Vector3d& X2 = v2.col(k);

    //        const Matrix3d& R = pose.rotations;
    //        const Vector3d& t = pose.translations;

    //        Vector3d tmp1 = X1.cross(R * X2);
    //        Vector3d tmp2 = (R * X2).cross(X1);
    //        Vector3d tmp3 = X1.cross(t);
    //        double tmp4 = tmp2.norm() * tmp2.norm();

    //        RowVector3d h1 = tmp2.transpose() * crossMatrix(R * X2);
    //        RowVector3d h2 = tmp2.transpose() * crossMatrix(X1);

    //        h1 /= tmp4;
    //        h2 /= tmp4;

    //        L = X1 * h1 - R*X2 * h2 - Matrix3d::Identity();
    //        residual(k) = (L * t).squaredNorm();

    //    }
    //    std::cout<<"residuals:"<<residual.transpose()<<endl;
    //    return residual.array().sqrt();
    //}

    VectorXd residual_LiGT_direct(const BearingVector &v1,
                                  const BearingVector &v2,
                                  const Pose &pose,
                                  const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        // 随机数生成
        std::vector<int> indices(5);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp1 = X1.cross(R * X2);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            double tmp4 = tmp2.norm() * tmp2.norm();

            Vector3d y = tmp4 * t + tmp2.dot(tmp3) * (R * X2);
            y = y / y.norm();

            residual(k) = 1 - X1.transpose() * y;
            //        residual(k) = cost_vec.dot(cost_vec) * tmp4;
        }
        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual.array().sqrt();
    }

    VectorXd residual_PPO(const BearingVector &v1,
                          const BearingVector &v2,
                          const Pose &pose,
                          const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        // 随机数生成
        std::vector<int> indices(5);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            //        residual(k) = ((reproj_coord/reproj_coord.norm() - X1)/ tmp3.squaredNorm() * tmp2.norm()).norm();
            residual(k) = (reproj_coord / reproj_coord.norm() - X1).norm();
        }
        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual.array();
    }

    VectorXd residual_PPO_bvc_invd(const BearingVector &v1,
                                   const BearingVector &v2,
                                   const Pose &pose,
                                   const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        // 随机数生成
        std::vector<int> indices(5);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp1 = (R * X2).cross(t);
            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            //        residual(k) = ((reproj_coord/reproj_coord.norm() - X1)/ tmp3.squaredNorm() * tmp2.norm()).norm();
            residual(k) = (reproj_coord / reproj_coord.norm() - X1).norm();
            residual(k) = residual(k) / tmp1.norm() / tmp3.norm() * tmp2.norm();
        }
        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual.array();
    }

    VectorXd residual_PPOG(const BearingVector &v1,
                           const BearingVector &v2,
                           const Pose &pose,
                           const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        // 随机数生成
        std::vector<int> indices(5);

        //  cout<<"PPOGtest"<<endl;
        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);

            //        residual(k) = reproj_coord.cross(X1).norm();
            residual(k) = (reproj_coord - X1 * (t.cross(R * X2)).norm()).norm();
        }
        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual.array();
    }

    VectorXd residual_PPO_invd(const BearingVector &v1,
                               const BearingVector &v2,
                               const Pose &pose,
                               const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d tmp4 = (R * X2).cross(t);
            double inv_depth = max(tmp2.norm() / tmp3.norm(), 1e-6);
            Vector3d reproj_coord = inv_depth * t + (R * X2);
            residual(k) = (reproj_coord.normalized() - X1.normalized()).squaredNorm();
        }
        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual.array().sqrt();
    }

    VectorXd residual_PPO_bva_invd(const BearingVector &v1,
                                   const BearingVector &v2,
                                   const Pose &pose,
                                   const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);
            const Matrix3d &R = pose.rotations;
            const Vector3d &t = pose.translations;

            Vector3d tmp2 = (R * X2).cross(X1);
            Vector3d tmp3 = X1.cross(t);
            Vector3d reproj_coord = tmp2.norm() * t + tmp3.norm() * (R * X2);
            reproj_coord = reproj_coord.normalized();

            residual(k) = (1 - reproj_coord.transpose() * X1);
            double eps_limit = 1e-6;
            double tmp_3_norm = (tmp3.norm() > eps_limit) ? tmp3.norm() : eps_limit;
            double tmp_2_norm2 = (tmp2.norm() * tmp2.norm() > eps_limit) ? tmp2.norm() * tmp2.norm() : eps_limit;
            residual(k) = residual(k) / tmp_2_norm2;
            double a = 0;
            if (std::isnan(residual(k)))
            {
                cout << "nan" << endl;
                a = 1;
            }
        }
        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual;
    }

    VectorXd residual_coplanar(const BearingVector &v1,
                               const BearingVector &v2,
                               const Pose &pose,
                               const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        // 随机数生成
        std::vector<int> indices(5);

        const Matrix3d &R = pose.rotations;
        const Vector3d &t = pose.translations;

        Matrix3d E = crossMatrix(t) * R;
        //    cout<<"Emat="<<E<<endl;
        for (int k = 0; k < num_matches; ++k)
        {

            const Vector3d &X1 = v1.col(k);
            const Vector3d &X2 = v2.col(k);

            residual(k) = abs(X1.transpose() * E * X2);
        }
        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual;
    }

    VectorXd residual_sampson(const BearingVector &v1,
                              const BearingVector &v2,
                              const Pose &pose,
                              const VectorXd *ptr_weigths)
    {
        int num_matches = v1.cols();
        VectorXd residual(num_matches);

        const Matrix3d &R = pose.rotations;
        const Vector3d &t = pose.translations;

        Matrix3d E = crossMatrix(t) * R;
        //    cout<<"residual_sampson test"<<endl;
        for (int k = 0; k < num_matches; ++k)
        {

            // 获取并标准化 v1 和 v2
            Vector3d X1 = v1.col(k) / v1(2, k);
            Vector3d X2 = v2.col(k) / v2(2, k);

            // 计算 tmp1 和 tmp2，只取前两行
            double tmp1 = (E.block<2, 3>(0, 0) * X2).squaredNorm();
            double tmp2 = (E.transpose().block<2, 3>(0, 0) * X1).squaredNorm();

            //        const Vector3d& X1 = v1.col(k);
            //        const Vector3d& X2 = v2.col(k);

            //        double tmp1 = (E * X2).squaredNorm();
            //        double tmp2 = (E.transpose() * X1).squaredNorm();
            residual(k) = (X1.transpose() * E * X2).squaredNorm() / (tmp1 + tmp2);
        }

        //    error =  epipolarDists.^2 ./ ...
        //        (sum(Fx1(1:2, :).^2) +  sum(Ftx2(1:2,:).^2));

        //    std::cout<<"residuals:"<<residual.transpose()<<endl;
        return residual;
    }

    // ==================== LM优化器实现 ====================

    /**
     * @brief 位姿参数转换：从轴角+平移到位姿
     */
    Pose parametersToePose(const Eigen::VectorXd &params)
    {
        Pose pose;

        // 提取旋转轴角参数
        Vector3d axis_angle = params.head<3>();
        double angle = axis_angle.norm();

        if (angle < 1e-8)
        {
            pose.rotations = Matrix3d::Identity();
        }
        else
        {
            Vector3d axis = axis_angle / angle;
            // 使用罗德里格斯公式计算旋转矩阵
            Matrix3d K = crossMatrix(axis);
            pose.rotations = Matrix3d::Identity() + sin(angle) * K + (1 - cos(angle)) * K * K;
        }

        // 提取平移参数
        pose.translations = params.tail<3>();

        return pose;
    }

    /**
     * @brief 位姿转换：从位姿到轴角+平移参数
     */
    Eigen::VectorXd poseToParameters(const Pose &pose)
    {
        Eigen::VectorXd params(6);

        // 从旋转矩阵提取轴角参数
        Eigen::AngleAxisd angleAxis(pose.rotations);
        Vector3d axis_angle = angleAxis.angle() * angleAxis.axis();

        params.head<3>() = axis_angle;
        params.tail<3>() = pose.translations;

        return params;
    }

    // PPOResidualFunctor的Huber损失相关函数实现
    double PPOResidualFunctor::huberWeight(double residual) const
    {
        double abs_residual = std::abs(residual);
        if (abs_residual <= huber_delta_)
        {
            return 1.0; // 对于小残差，权重为1（二次损失）
        }
        else
        {
            return huber_delta_ / abs_residual; // 对于大残差，权重递减（线性损失）
        }
    }

    double PPOResidualFunctor::huberPseudoResidual(double residual) const
    {
        double abs_residual = std::abs(residual);
        if (abs_residual <= huber_delta_)
        {
            return residual; // 二次区域，返回原始残差
        }
        else
        {
            // 线性区域，返回调整后的伪残差
            return (residual >= 0 ? 1.0 : -1.0) * sqrt(2.0 * huber_delta_ * abs_residual - huber_delta_ * huber_delta_);
        }
    }

    // PPOResidualFunctor实现
    int PPOResidualFunctor::operator()(const InputType &x, ValueType &fvec) const
    {
        // 将参数转换为位姿
        Pose pose = parametersToePose(x);

        // 计算PPO残差
        VectorXd residuals = residual_PPO(v1_, v2_, pose, weights_);

        // 应用Huber损失（如果启用）
        if (use_huber_)
        {
            for (int i = 0; i < residuals.size(); ++i)
            {
                fvec(i) = huberPseudoResidual(residuals(i));
            }
        }
        else
        {
            // 标准二次损失
            for (int i = 0; i < residuals.size(); ++i)
            {
                fvec(i) = residuals(i);
            }
        }

        return 0;
    }

    int PPOResidualFunctor::df(const InputType &x, JacobianType &fjac) const
    {
        // 使用数值求导计算雅可比矩阵
        const double eps = 1e-8;

        if (use_huber_)
        {
            // 对于Huber损失，需要考虑权重的影响
            // 先计算原始残差用于权重计算
            Pose pose = parametersToePose(x);
            VectorXd original_residuals = residual_PPO(v1_, v2_, pose, weights_);

            // 对每个参数进行数值求导
            for (int j = 0; j < inputs(); ++j)
            {
                InputType x_plus = x;
                InputType x_minus = x;
                x_plus(j) += eps;
                x_minus(j) -= eps;

                ValueType fvec_plus(values());
                ValueType fvec_minus(values());

                operator()(x_plus, fvec_plus);
                operator()(x_minus, fvec_minus);

                // 中心差分，对于Huber损失已经在operator()中处理
                for (int i = 0; i < values(); ++i)
                {
                    fjac(i, j) = (fvec_plus(i) - fvec_minus(i)) / (2.0 * eps);
                }
            }
        }
        else
        {
            // 标准二次损失的雅可比计算
            // 计算当前位置的函数值
            ValueType fvec_center(values());
            operator()(x, fvec_center);

            // 对每个参数进行数值求导
            for (int j = 0; j < inputs(); ++j)
            {
                InputType x_plus = x;
                InputType x_minus = x;
                x_plus(j) += eps;
                x_minus(j) -= eps;

                ValueType fvec_plus(values());
                ValueType fvec_minus(values());

                operator()(x_plus, fvec_plus);
                operator()(x_minus, fvec_minus);

                // 中心差分
                for (int i = 0; i < values(); ++i)
                {
                    fjac(i, j) = (fvec_plus(i) - fvec_minus(i)) / (2.0 * eps);
                }
            }
        }

        return 0;
    }

    Pose optimizePoseWithLM(const BearingVector &v1,
                            const BearingVector &v2,
                            const Pose &initial_pose,
                            const VectorXd *weights,
                            bool use_huber,
                            double huber_delta)
    {
        // 创建优化函数对象（支持Huber损失）
        PPOResidualFunctor functor(v1, v2, weights, use_huber, huber_delta);

        // 创建LM优化器
        Eigen::LevenbergMarquardt<PPOResidualFunctor> lm(functor);

        // 设置初始参数
        Eigen::VectorXd x = poseToParameters(initial_pose);

        // 计算初始残差以评估Huber阈值的合理性
        if (use_huber)
        {
            VectorXd initial_residuals = residual_PPO(v1, v2, initial_pose, weights);
            double median_residual = 0.0;

            // 计算残差中位数作为参考
            std::vector<double> residual_vec(initial_residuals.data(),
                                             initial_residuals.data() + initial_residuals.size());
            std::sort(residual_vec.begin(), residual_vec.end());
            median_residual = residual_vec[residual_vec.size() / 2];

            cout << "Using Huber Loss - Delta: " << huber_delta
                 << ", Median Residual: " << median_residual << endl;
        }
        else
        {
            cout << "Using Standard L2 Loss" << endl;
        }

        // 执行优化（使用默认参数）
        Eigen::LevenbergMarquardtSpace::Status status = lm.minimize(x);

        // 输出优化结果信息
        cout << "LM Optimization Status: " << static_cast<int>(status) << endl;
        cout << "LM Function Evaluations: " << lm.nfev() << endl;
        cout << "LM Final FNORM: " << lm.fnorm() << endl;

        // 转换优化结果为位姿
        Pose optimized_pose = parametersToePose(x);

        // 输出优化前后的残差统计
        VectorXd final_residuals = residual_PPO(v1, v2, optimized_pose, weights);
        VectorXd initial_residuals = residual_PPO(v1, v2, initial_pose, weights);

        cout << "Initial Mean Residual: " << initial_residuals.mean() << endl;
        cout << "Final Mean Residual: " << final_residuals.mean() << endl;

        if (use_huber)
        {
            // 统计有多少残差超过Huber阈值
            int outliers_initial = 0, outliers_final = 0;
            for (int i = 0; i < initial_residuals.size(); ++i)
            {
                if (std::abs(initial_residuals(i)) > huber_delta)
                    outliers_initial++;
                if (std::abs(final_residuals(i)) > huber_delta)
                    outliers_final++;
            }
            cout << "Outliers (>delta): Initial=" << outliers_initial
                 << ", Final=" << outliers_final << " (out of " << initial_residuals.size() << ")" << endl;
        }

        return optimized_pose;
    }

}
