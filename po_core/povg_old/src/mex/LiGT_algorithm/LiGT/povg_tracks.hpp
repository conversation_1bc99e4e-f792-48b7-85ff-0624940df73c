// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#ifndef POVG_TRACKS_FUNCTIONS
#define POVG_TRACKS_FUNCTIONS


#include "povg_types.hpp"

using namespace POVG;

namespace POVG {


void check_tracks_info(const ViewId fixed_id,
                       TracksInfoPtr& tracks_info_ptr,
                       EstInfo& est_info);

void load_tracks_info(const std::string& track_file,
                      const Size& min_track_length,
                      TracksInfoPtr& tracks_info_ptr,
                      const bool& bearing_mode = false);



}




#endif
