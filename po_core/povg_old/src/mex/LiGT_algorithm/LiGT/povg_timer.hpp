#ifndef POVG_TIMER
#define POVG_TIMER

#include <chrono>
#include <fstream>
#include <iomanip> // for setprecision
#include <iostream>
#include <string>
namespace POVG {



class Timer{

    typedef std::chrono::system_clock::time_point TimePoint;

public:
    Timer(){start_time_ = timer_.now();};

    const double Duration(){

        end_time_ = timer_.now();
        auto duration = std::chrono::duration_cast
                <std::chrono::microseconds>
                (end_time_ - start_time_);

        time_cost_ = double(duration.count()) *
                std::chrono::microseconds::period::num /
                std::chrono::microseconds::period::den;

        return time_cost_;

    };

    void WriteTime(const std::string& time_file,const int& num_iter=0) {

        std::fstream output(time_file, std::ios::out | std::ios::trunc);
        if (!output.is_open()) {
            std::cerr << "time file cannot create, please check path" << std::endl;
            return;
        }
        output << std::setprecision(16) << time_cost_ << ' ' << num_iter << std::endl;
        output.close();
    }

    void Reset() {
        start_time_ = timer_.now();
        time_cost_ = 0.0;
    }

private:

    std::chrono::system_clock timer_;

    TimePoint start_time_;
    TimePoint end_time_;
    double time_cost_;

};

}












#endif
