// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <cstdio>
#include <fstream>
#include <iostream>
#include <string>
#include <vector>
#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <Eigen/SVD>
#include <chrono>
#include <fstream>
#include <iomanip>


#ifdef OPENMVG_USE_OPENMP
#include <omp.h>
#endif

#include "LiGT_algorithm.hpp"
#include "povg_tracks.hpp"
#include "povg_timer.hpp"

#include "Spectra/SymEigsShiftSolver.h"
#include "Spectra/MatOp/SparseSymShiftSolve.h"


using namespace std;
using namespace chrono;
using namespace Spectra;

namespace POVG {

// ====================   LiGT Problem ========================

LiGTProblem::LiGTProblem() {
    num_view_ = 0;
    num_pts_ = 0;
    num_obs_ = 0;
    time_use_ = 0;
    fixed_id_ = 0;
    num_est_view_ = 0;

    output_file_ = "";
    time_file_ = "";

    est_info_ptr_ = nullptr;
    tracks_info_ptr_ = nullptr;

}

LiGTProblem::LiGTProblem(const std::string& globalR_file,
                         const std::string& track_file,
                         const std::string& output_file,
                         const std::string& time_file,
                         const int& fixed_id,
                         const int& min_tracking_length,
                         const bool& bearing_mode,
                         const PRO_PROCESS_MODE& pro_mode) {
    num_view_ = 0;
    num_pts_ = 0;
    num_obs_ = 0;
    time_use_ = 0;
    num_est_view_ = 0;
    fixed_id_ = fixed_id;
    min_tracking_length_ = min_tracking_length;

    bearing_mode_ = bearing_mode;
    pro_mode_ = pro_mode;

    output_file_ = output_file;
    time_file_ = time_file;

    std::cout<<"min_track_length = "<<min_tracking_length<<std::endl;

    LoadTracks(track_file);
    SetupOrientation(globalR_file);
    BuildObsMap();

    // build features
    BuildFeatures();
}


LiGTProblem::LiGTProblem(LiGTProblem& problem){

    min_tracking_length_ = problem.min_tracking_length_;
    time_use_ = problem.time_use_;
    fixed_id_ = problem.fixed_id_;
    num_view_ = problem.num_view_;
    num_pts_ = problem.num_pts_;
    num_obs_ = problem.num_obs_;
    output_file_ = problem.output_file_;
    time_file_ = problem.time_file_;
    bearing_mode_ = problem.bearing_mode_;

    est_info_ptr_ = problem.est_info_ptr_;
    tracks_info_ptr_ = problem.tracks_info_ptr_;

    global_rotations_ = problem.global_rotations_;
    global_translations_ = problem.global_translations_;
    poses_ = problem.poses_;

}
LiGTProblem::LiGTProblem(Tracks& tracks,
                         Attitudes& global_R){

    tracks_info_ptr_->tracks.swap(tracks);
    global_rotations_.swap(global_R);

    // check tracks and build estimated information [EstInfo]
    if (!tracks_info_ptr_->tracks.size()){
        std::cerr <<"error: wrong track inputs to run LiGT";
        return;
    }

    if (!global_rotations_.size()){
        std::cerr <<"error: wrong rotation inputs to run LiGT";
        return;
    }

    time_use_ = 0;
    fixed_id_ = 0;

    CheckTracks();
}

void LiGTProblem::Init(const int& fixed_id){

    // check tracks and build estimated information [EstInfo]
    if (!tracks_info_ptr_->tracks.size()){
        std::cerr <<"error: wrong track inputs to run LiGT";
        return;
    }

    if (!global_rotations_.size()){
        std::cerr <<"error: wrong rotation inputs to run LiGT";
        return;
    }

    time_use_ = 0;
    fixed_id_ = fixed_id;

    CheckTracks();
}

void LiGTProblem::SetupOrientation(const std::string& globalR_file) {

    POVG::Timer timer;

    // load global rotation file
    std::ifstream infile(globalR_file);
    if (!infile.is_open()) {
        std::cerr << "gR_file cannot open";
        return;
    }

    infile >> num_view_;

    global_rotations_.clear();
    global_rotations_.resize(num_est_view_);

    ViewId est_view_id;

    for (ViewId i = 0; i < num_view_; i++) {
        Eigen::Matrix3d rotation;

        // 尝试读取rotation矩阵
        if (!(infile >> rotation(0,0) >> rotation(1,0) >> rotation(2,0) >>
              rotation(0,1) >> rotation(1,1) >> rotation(2,1) >>
              rotation(0,2) >> rotation(1,2) >> rotation(2,2))) {
            // 如果读取失败，跳过该行
            cout << "读取到非法数值，跳过该行" << endl;
            infile.clear(); // 清除错误标志
            infile.ignore(numeric_limits<streamsize>::max(), '\n'); // 忽略当前行剩余部分
             outlier_view_ids_.emplace_back(i);// origin view id
            continue; // 继续读取下一行
        }

//        cout <<"rotation = "<<endl<<rotation<<endl;
        // update view id in orientations
        auto it = est_info_ptr_->origin2est_view_ids.find(i);
        if (it == est_info_ptr_->origin2est_view_ids.end()) {
            // 键 i 不存在于 unordered_map 中, 异常视图，跳过
            outlier_view_ids_.emplace_back(i);// origin view id
            continue;
        }

        est_view_id = it->second;

        if (!checkRotation(rotation)){
            cout<<"[SetupOrientation]:姿态输入存在问题，track文件与姿态输入文件数据不对齐！"<<endl;
        }

        global_rotations_[est_view_id] = rotation;
    }

    infile.close();

    // print time cost information
    cout << ">> time for loading global rotation file: "
         << timer.Duration()
         << "s"
         << endl;

}

void LiGTProblem::LoadTracks(const std::string& track_file) {

    POVG::Timer timer;

    // load tracks file
    load_tracks_info(track_file,
                     min_tracking_length_,
                     tracks_info_ptr_,
                     bearing_mode_);

    num_est_view_ = tracks_info_ptr_->num_view;
    num_pts_ = tracks_info_ptr_->num_pts;
    num_obs_ = tracks_info_ptr_->num_obs;

    // check tracks and build estimated information [EstInfo]
    CheckTracks();



}

bool FeatureCompare(Feature feature1, Feature feature2){
    return feature1.m3_value > feature2.m3_value;
}

void LiGTProblem::BuildFeatures(){

    int pts_len = tracks_info_ptr_->tracks.size();

    ObsId max_id_l = 0;
    ObsId max_id_r = 0;

    double max_s = 0;

    for ( int id_pts = 0; id_pts < pts_len; ++id_pts){

        Track& tmp_track = tracks_info_ptr_->tracks[id_pts].track;

        if (!SelectBaseViews(tmp_track, max_id_l, max_id_r, max_s))
            continue;

        for ( int id_obs = 0; id_obs < tmp_track.size(); ++id_obs){

            Feature tmp_feature;
            tmp_feature.track_length = tmp_track.size();
            tmp_feature.obs_ptr = &tmp_track[id_obs];
            tmp_feature.m3_value = max_s;
            tmp_feature.is_used = tmp_feature.obs_ptr->is_used;

             // 注意：已经经过转换，因此，这里是est view id (not original view id)
            ViewId tmp_view_id = tmp_track[id_obs].view_id;

            features_[tmp_view_id].feature.emplace_back(tmp_feature);

            if (tmp_feature.is_used)
                features_[tmp_view_id].num_depth_features++;
        }
    }

    for (auto& feature : features_){
        sort(feature.second.feature.begin(),feature.second.feature.end(),FeatureCompare);
        double mean_m3_value = 0;
        double mean_ndf = 0;
        for (auto& observation : feature.second.feature){
            mean_m3_value += observation.m3_value;
            mean_ndf += observation.track_length;
        }
        mean_m3_value = mean_m3_value / feature.second.feature.size();
        mean_ndf = mean_ndf / feature.second.feature.size();

        feature.second.mean_m3_value = mean_m3_value;
        feature.second.mean_ndf = mean_ndf;
    }

}


void LiGTProblem::CheckTracks(){

    if (!est_info_ptr_)
        est_info_ptr_ = std::make_shared<EstInfo>();

    return check_tracks_info(fixed_id_,tracks_info_ptr_,*est_info_ptr_);

}

void LiGTProblem::SetMinTrackingLength(unsigned int length){
    min_tracking_length_ = length;
}


void LiGTProblem::RecoverViewIds(){

    cout << "recover the estimated view ids into original view ids\n";

    for (ViewId i = 0; i < num_est_view_; ++i){
        ViewId origin_id = est_info_ptr_->est2origin_view_ids[i];
        Pose tmp_pose;
        tmp_pose.rotations = global_rotations_[i];
        tmp_pose.translations = global_translations_[i];

        poses_.insert({est_info_ptr_->est2origin_view_ids[i], tmp_pose});

    }

}

void LiGTProblem::WriteTranslation() {

    fstream output(output_file_, std::ios::out | ios::trunc);
    if (!output.is_open()) {
        std::cerr << "output file cannot create, please check path";
        return;
    }

    Size t_size = num_view_;
    Vector3d* tmp_position = nullptr;
    Matrix3d* tmp_R = nullptr;
    unsigned int i = 0;

    output << t_size << endl;

    for (i = 0; i < t_size; ++i) {
        auto it = std::find(outlier_view_ids_.begin(), outlier_view_ids_.end(), i);

        if (it != outlier_view_ids_.end()) {
            //std::cout << "Value " << i << " found in vector." << std::endl;
            tmp_position = &poses_[i].translations;
            tmp_R = &poses_[i].rotations;

            output << setprecision(16) << endl << "NAN" << " " << "NAN" << " " << "NAN" << " ";
            output << setprecision(16) << "NAN" << " " << "NAN" << " " << "NAN" << " ";
            output << setprecision(16) << "NAN" << " " << "NAN" << " " << "NAN" << " ";

            output << setprecision(16) << "NAN" << " " << "NAN" << " " << "NAN";
        } else {
            //std::cout << "Value " << i << " not found in vector." << std::endl;
            tmp_position = &poses_[i].translations;
            tmp_R = &poses_[i].rotations;

            output << setprecision(16) << endl << (*tmp_R)(0, 0) << " " << (*tmp_R)(1, 0) << " " << (*tmp_R)(2, 0) << " ";
            output << setprecision(16) << (*tmp_R)(0, 1) << " " << (*tmp_R)(1, 1) << " " << (*tmp_R)(2, 1) << " ";
            output << setprecision(16) << (*tmp_R)(0, 2) << " " << (*tmp_R)(1, 2) << " " << (*tmp_R)(2, 2) << " ";

            output << setprecision(16) << (*tmp_position)(0) << " " << (*tmp_position)(1) << " " << (*tmp_position)(2);
        }


    }
    output.close();

}

void LiGTProblem::WriteTime() {

    fstream output(time_file_, std::ios::out | ios::trunc);
    if (!output.is_open()) {
        std::cerr << "time file cannot create, please check path";
        return;
    }
    output << setprecision(16) << time_use_ << ' ' << 1 << endl;
    output.close();

}

void LiGTProblem::IdentifySign(const RowVectorXd &A_lr,
                               VectorXd& evectors) {
    VectorXd judgeValue = A_lr * evectors;
    //    int positive_count = (judgeValue.array() > 0).sum();
    //    int negative_count = judgeValue.size() - positive_count;

    int positive_count = 0;
    int negative_count = 0;

    for (int i = 0; i < judgeValue.rows(); ++i){
        if (judgeValue[i]>0)
            positive_count++;
        else
            negative_count++;
    }

    cout << "positive count = " << positive_count
         << ", negative count = "<< negative_count
         << endl;

    if (positive_count < negative_count) {
        evectors = -evectors;
        cout << "signs are reversed!\n";
    }
}


void LiGTProblem::SelectBaseViews(const Track& track,
                                  ViewId& lbase_view_id,
                                  ViewId& rbase_view_id,
                                  ObsId& id_lbase,
                                  ObsId& id_rbase){

    double best_criterion_value = 0;

    Size track_size = track.size();

    // [Step.2 in Pose-only Algorithm]: select the left/right-base views
    for (ObsId i = 0; i < track_size - 1; ++i) {
        for (ObsId j = i + 1; j < track_size; ++j) {

            const ViewId& i_view_id = track[i].view_id;
            const ViewId& j_view_id = track[j].view_id;

            const Vector3d& i_coord = track[i].coord;
            const Vector3d& j_coord = track[j].coord;

            Matrix3d& R_i = global_rotations_[i_view_id];
            Matrix3d& R_j = global_rotations_[j_view_id];

            Matrix3d R_ij = R_j * R_i.transpose();
            Vector3d theta_ij = j_coord.cross(R_ij * i_coord);

            double criterion_value = theta_ij.norm();

            if (criterion_value > best_criterion_value) {

                best_criterion_value = criterion_value;

                lbase_view_id = track[i].view_id;
                rbase_view_id = track[j].view_id;

                id_lbase = i;
                id_rbase = j;
            }
        }
    }
}

bool LiGTProblem::SelectRightBaseView(const Track &track,
                                      const ObsId& id_lbase,
                                      ObsId &id_rbase){
    bool succussful = false;

    Size track_size = track.size();
    double best_criterion_value = 0;

    // [Step.2 in Pose-only Algorithm]: select the right-base views
    if (!track[id_lbase].is_used)
        return succussful;

    const ViewId& i_view_id = track[id_lbase].view_id;
    const Vector3d& i_coord = track[id_lbase].coord;

    Matrix3d& R_i = global_rotations_[i_view_id];

    for (ObsId j = 1; j < track_size; ++j) {
        if (!track[j].is_used || j == id_lbase)
            continue;

        const ViewId& j_view_id = track[j].view_id;
        const Vector3d& j_coord = track[j].coord;

        Matrix3d& R_j = global_rotations_[j_view_id];

        Matrix3d R_ij = R_j * R_i.transpose();
        Vector3d theta_ij = j_coord.cross(R_ij * i_coord);

        double criterion_value = theta_ij.norm();

        if (criterion_value > best_criterion_value) {

            succussful = true;
            id_rbase = j;

        }
    }


    return succussful;
}


void LiGTProblem::BuildObsMap(){

    int id_current_obs = 0;

    for (int i = 0; i < num_pts_; ++i){

        Track& ptr_track = tracks_info_ptr_->tracks[i].track;

        for (int j = 0; j < ptr_track.size(); ++j){

            obs_map_.insert({id_current_obs, &ptr_track[j]});
            id_current_obs++;
        }
    }
    \
}

bool LiGTProblem::SelectBaseViews(const Track& track,
                                  ObsId& id_lbase,
                                  ObsId& id_rbase,
                                  double& best_criterion_value){
    bool succussful = false;

    best_criterion_value = 0;

    Size track_size = track.size();

    // [Step.2 in Pose-only Algorithm]: select the left/right-base views
    for (ObsId i = 0; i < track_size - 1; ++i) {
        if (!track[i].is_used)
            continue;

        const ViewId& i_view_id = track[i].view_id;
        const Vector3d& i_coord = track[i].coord;

        Matrix3d& R_i = global_rotations_[i_view_id];

        for (ObsId j = i + 1; j < track_size; ++j) {
            if (!track[j].is_used)
                continue;

            const ViewId& j_view_id = track[j].view_id;            
            const Vector3d& j_coord = track[j].coord;

            Matrix3d& R_j = global_rotations_[j_view_id];

            Matrix3d R_ij = R_j * R_i.transpose();
            Vector3d theta_ij = j_coord.cross(R_ij * i_coord);

            double criterion_value = theta_ij.norm();

            if (criterion_value > best_criterion_value) {

                succussful = true;
                best_criterion_value = criterion_value;

                if (i_view_id > j_view_id){
                    id_lbase = j;
                    id_rbase = i;
                }
                else{
                    id_lbase = i;
                    id_rbase = j;
                }

            }
        }
    }

    return succussful;
}

void LiGTProblem::BuildLTLAcceptableParallaxAngle(
                           Eigen::MatrixXd& LTL,
                           RowVectorXd& A_lr){

    POVG::Timer timer;

    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

    Size est_t_length = 3 * est_view_size-3;

    A_lr.setZero(est_t_length);
    LTL.setZero(est_t_length, est_t_length);

    Matrix3d cross_xr, cross_xc;

    double s;

    Matrix3d R_lr, R_lc;

    Vector3d Rlc_xl;
    Vector3d m3_lr, m3_lc;
    RowVector3d tmp_A;
    Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

    Matrix3d FtF_ll,FtF_rr,FtF_cc;
    Matrix3d FtF_lr,FtF_lc,FtF_rc;

    unsigned int track_len;
    unsigned int i = 0;
    unsigned int j = 0;
    unsigned int k = 0;

    PtsId pts_len = num_pts_;
    num_used_obs_ = 0;
    Vector3d R_lr_x_l,R_lc_x_l,xc_R_lc_x_l;
    RowVector3d tmp_A_R_lr;

    int id_r,id_l,id_c;

    unsigned int used_pts = 0;

    ObsId max_id_l = 0;
    ObsId max_id_r = 0;

    double max_s = 0;

    Vector3d Rx;
    Vector3d xRx;

    double max_parallax_angle = 0.0;
    for(i = 0;i < pts_len; ++i){
        if(!tracks_info_ptr_->tracks[i].is_used)
            continue;
        used_pts++;

        Track& ptr_track = tracks_info_ptr_->tracks[i].track;

        max_id_l = 0;
        max_id_r = 0;
        max_s = 0;

        SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s);

        ViewId& left_base = max_id_l;
        ViewId& right_base = max_id_r;

        ViewId& lbase_view = ptr_track[left_base].view_id;
        ViewId& rbase_view = ptr_track[right_base].view_id;

        double& base_s = max_s;

        Matrix3d& R_l_base = global_rotations_[lbase_view];
        Matrix3d& R_r_base = global_rotations_[rbase_view];

        R_lr.noalias() = R_r_base * R_l_base.transpose();

        Vector3d& x_l = ptr_track[left_base].coord;
        Vector3d& x_r = ptr_track[right_base].coord;

        max_parallax_angle = asin(max_s / (x_r.norm() * x_l.norm())) * (180.0 / M_PI);

        //如果最大视差角过小（阈值参考OpenMVG），则跳过该3D点观测，后面需要利用OutlierCheckForOpenMVG检测
        if (max_parallax_angle < 2.0) {
            continue;
        }

        cross_xr<< 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

        R_lr_x_l.noalias() = R_lr * x_l;
        m3_lr.noalias() = cross_xr * R_lr_x_l;

        tmp_A.noalias() = m3_lr.transpose() * cross_xr;
        tmp_A.noalias() = -tmp_A;
        tmp_A_R_lr.noalias() = -tmp_A * R_lr;

        id_l = 3 * lbase_view - 3;
        id_r = 3 * rbase_view - 3;

        if (id_l >= 0){
            A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
        }

        if (id_r >= 0){
            A_lr.middleCols(id_r,3) += tmp_A;
        }

        track_len = ptr_track.size();



        for(j = 0; j < track_len; ++j){

            if (!ptr_track[j].is_used)
                continue;

            num_used_obs_++;

            ViewId& current_view = ptr_track[j].view_id;

            if(current_view == lbase_view )
                continue;

            Matrix3d& R_c = global_rotations_[current_view];
            Vector3d& x_c = ptr_track[j].coord;

            cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

            R_lc.noalias() = R_c * R_l_base.transpose();
            Rlc_xl.noalias() = R_lc * x_l;

            m3_lc.noalias() = x_c.cross(Rlc_xl);

            s = m3_lc.norm();

            R_lc_x_l.noalias() = R_lc * x_l;
            xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

            tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A ;

            tmp_F_cur.noalias() = cross_xc *base_s *base_s;
            tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

            FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
            FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
            FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

            id_c = 3 * current_view - 3;

            if (id_l >= 0){

                LTL.block<3, 3>(id_l, id_l) += FtF_ll;

            }

            if (id_r >= 0){

                LTL.block<3, 3>(id_r, id_r) += FtF_rr;

            }

            if (id_c >= 0){

                LTL.block<3, 3>(id_c, id_c) += FtF_cc;

            }

            FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
            if (id_l >= 0 && id_r >= 0){
                if(id_l < id_r){
                    LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                }
                else{
                    LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                }
            }

            FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
            if (id_l >= 0 && id_c >= 0){
                if(id_l < id_c){

                    LTL.block<3, 3>(id_l, id_c) += FtF_lc;

                }
                else{

                    LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();

                }
            }

            FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
            if (id_r >= 0 && id_c >= 0){
                if(id_r < id_c){

                    LTL.block<3, 3>(id_r, id_c) += FtF_rc;

                }
                else if(id_r == id_c){

                    LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;

                }
                else{

                    LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();

                }
            }

        }
    }
    cout << "used pts =  "
         << used_pts
         << endl;
    cout << "time for constructing LTL matrix: "
         << timer.Duration()
         << "s"
         << endl;
}


void LiGTProblem::BuildLTL(Eigen::MatrixXd& LTL,
                           RowVectorXd& A_lr){

    POVG::Timer timer;

    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

    Size est_t_length = 3 * est_view_size-3;

    A_lr.setZero(est_t_length);
    LTL.setZero(est_t_length, est_t_length);

    Matrix3d cross_xr, cross_xc;

    double s;

    Matrix3d R_lr, R_lc;

    Vector3d Rlc_xl;
    Vector3d m3_lr, m3_lc;
    RowVector3d tmp_A;
    Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;

    Matrix3d FtF_ll,FtF_rr,FtF_cc;
    Matrix3d FtF_lr,FtF_lc,FtF_rc;

    unsigned int track_len;
    unsigned int i = 0;
    unsigned int j = 0;
    unsigned int k = 0;

    PtsId pts_len = num_pts_;
    num_used_obs_ = 0;
    Vector3d R_lr_x_l,R_lc_x_l,xc_R_lc_x_l;
    RowVector3d tmp_A_R_lr;

    int id_r,id_l,id_c;

    unsigned int used_pts = 0;

    ObsId max_id_l = 0;
    ObsId max_id_r = 0;

    double max_s = 0;

    Vector3d Rx;
    Vector3d xRx;

    for(i = 0;i < pts_len; ++i){
        if(!tracks_info_ptr_->tracks[i].is_used)
            continue;
        used_pts++;

        Track& ptr_track = tracks_info_ptr_->tracks[i].track;

        max_id_l = 0;
        max_id_r = 0;
        max_s = 0;

        SelectBaseViews(ptr_track, max_id_l, max_id_r, max_s);

        ViewId& left_base = max_id_l;
        ViewId& right_base = max_id_r;

        ViewId& lbase_view = ptr_track[left_base].view_id;
        ViewId& rbase_view = ptr_track[right_base].view_id;

        double& base_s = max_s;

        Matrix3d& R_l_base = global_rotations_[lbase_view];
        Matrix3d& R_r_base = global_rotations_[rbase_view];

        R_lr.noalias() = R_r_base * R_l_base.transpose();

        Vector3d& x_l = ptr_track[left_base].coord;
        Vector3d& x_r = ptr_track[right_base].coord;

        cross_xr<< 0, -x_r(2), x_r(1),
                x_r(2), 0, -x_r(0),
                -x_r(1), x_r(0), 0;

        R_lr_x_l.noalias() = R_lr * x_l;
        m3_lr.noalias() = cross_xr * R_lr_x_l;

        tmp_A.noalias() = m3_lr.transpose() * cross_xr;
        tmp_A.noalias() = -tmp_A;
        tmp_A_R_lr.noalias() = -tmp_A * R_lr;

        id_l = 3 * lbase_view - 3;
        id_r = 3 * rbase_view - 3;

        if (id_l >= 0){
            A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
        }

        if (id_r >= 0){
            A_lr.middleCols(id_r,3) += tmp_A;
        }

        track_len = ptr_track.size();



        for(j = 0; j < track_len; ++j){

            if (!ptr_track[j].is_used)
                continue;

            num_used_obs_++;

            ViewId& current_view = ptr_track[j].view_id;


            ViewId& origin_view = est_info_ptr_->est2origin_view_ids[current_view];

            if(pro_view_ids_.size()){
                if(find(pro_view_ids_.begin(),pro_view_ids_.end(),origin_view)!=pro_view_ids_.end()){
                    continue;
                }
            }
            if(current_view == lbase_view )
                continue;

            Matrix3d& R_c = global_rotations_[current_view];
            Vector3d& x_c = ptr_track[j].coord;

            cross_xc << 0, -x_c(2), x_c(1),
                    x_c(2), 0, -x_c(0),
                    -x_c(1), x_c(0), 0;

            R_lc.noalias() = R_c * R_l_base.transpose();
            Rlc_xl.noalias() = R_lc * x_l;

            m3_lc.noalias() = x_c.cross(Rlc_xl);

            s = m3_lc.norm();

            R_lc_x_l.noalias() = R_lc * x_l;
            xc_R_lc_x_l.noalias() = cross_xc * R_lc_x_l;

            tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A ;

            tmp_F_cur.noalias() = cross_xc *base_s *base_s;
            tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

            FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase;
            FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase;
            FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

            id_c = 3 * current_view - 3;

            if (id_l >= 0){

                LTL.block<3, 3>(id_l, id_l) += FtF_ll;

            }

            if (id_r >= 0){

                LTL.block<3, 3>(id_r, id_r) += FtF_rr;

            }

            if (id_c >= 0){

                LTL.block<3, 3>(id_c, id_c) += FtF_cc;

            }

            FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
            if (id_l >= 0 && id_r >= 0){
                if(id_l < id_r){
                    LTL.block<3, 3>(id_l, id_r) += FtF_lr;
                }
                else{
                    LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
                }
            }

            FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
            if (id_l >= 0 && id_c >= 0){
                if(id_l < id_c){

                    LTL.block<3, 3>(id_l, id_c) += FtF_lc;

                }
                else{

                    LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();

                }
            }


            FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
            if (id_r >= 0 && id_c >= 0){
                if(id_r < id_c){

                    LTL.block<3, 3>(id_r, id_c) += FtF_rc;

                }
                else if(id_r == id_c){

                    LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;

                }
                else{

                    LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();

                }
            }

        }
    }
    cout << "used pts =  "
         << used_pts
         << endl;
    cout << "time for constructing LTL matrix: "
         << timer.Duration()
         << "s"
         << endl;
}


void LiGTProblem::BuildSingleLTL(Eigen::MatrixXd &LTL,
                                RowVectorXd &A_lr,
                                Track& track,
                                const ObsInfo* obs_ptr,
                                const double& weight){
    double base_s = 0;
    ViewId left_base = 0;
    ViewId right_base = 0;

    if (!SelectBaseViews(track, left_base, right_base, base_s))
        return;

   Size track_len = track.size();

   ViewId& lbase_view = track[left_base].view_id;
   ViewId& rbase_view = track[right_base].view_id;

   Matrix3d& R_l_base = global_rotations_[lbase_view];
   Matrix3d& R_r_base = global_rotations_[rbase_view];

   Matrix3d R_lr = R_r_base * R_l_base.transpose();

   Vector3d& x_l = track[left_base].coord;
   Vector3d& x_r = track[right_base].coord;

   Matrix3d cross_xr, cross_xc;
   cross_xr<< 0, -x_r(2), x_r(1),
           x_r(2), 0, -x_r(0),
           -x_r(1), x_r(0), 0;

   Vector3d R_lr_x_l = R_lr * x_l;
   Vector3d m3_lr = cross_xr * R_lr_x_l;


   RowVector3d tmp_A = m3_lr.transpose() * cross_xr;
   tmp_A.noalias() = -tmp_A;

   RowVector3d tmp_A_R_lr = -tmp_A * R_lr;

   int id_r,id_l,id_c;

   id_l = 3 * lbase_view - 3;
   id_r = 3 * rbase_view - 3;

   if (id_l >= 0){
       A_lr.middleCols(id_l, 3) += tmp_A_R_lr;
   }

   if (id_r >= 0){
       A_lr.middleCols(id_r,3) += tmp_A;
   }

   Matrix3d tmp_F_lbase, tmp_F_rbase, tmp_F_cur;
   Matrix3d FtF_ll,FtF_rr,FtF_cc;
   Matrix3d FtF_lr,FtF_lc,FtF_rc;


   for(int j = 0; j < track_len; ++j){

       ViewId& current_view = track[j].view_id;

       if (!track[j].is_used || current_view == lbase_view) continue;

       if (obs_ptr) if (current_view != obs_ptr->view_id) continue;

       Matrix3d& R_c = global_rotations_[current_view];
       Vector3d& x_c = track[j].coord;

       cross_xc << 0, -x_c(2), x_c(1),
               x_c(2), 0, -x_c(0),
               -x_c(1), x_c(0), 0;

       const Matrix3d& R_lc = R_c * R_l_base.transpose();
       const Vector3d& Rlc_xl = R_lc * x_l;

       const Vector3d& m3_lc = x_c.cross(Rlc_xl);

       const double& s = m3_lc.norm();

       const Vector3d& R_lc_x_l = R_lc * x_l;
       const Vector3d& xc_R_lc_x_l = cross_xc * R_lc_x_l;

       tmp_F_rbase.noalias() = xc_R_lc_x_l * tmp_A * weight;

       tmp_F_cur.noalias() = cross_xc * (base_s * base_s) * weight;

       tmp_F_lbase.noalias() = -(tmp_F_rbase * R_lr + tmp_F_cur * R_lc);

//       if (base_s > 0.1){

//       }
//       else{
//           cout<<"PRI detection!" << endl;
//           tmp_F_rbase.setZero();
//           // [xc]x * tc - [xc]x * Rlc * tl
//           tmp_F_cur = cross_xc ;

//           tmp_F_lbase.noalias() = -(tmp_F_cur * R_lc);
//       }


       FtF_ll.noalias() = tmp_F_lbase.transpose() * tmp_F_lbase ;
       FtF_rr.noalias() = tmp_F_rbase.transpose() * tmp_F_rbase ;
       FtF_cc.noalias() = tmp_F_cur.transpose() * tmp_F_cur;

       id_c = 3 * current_view - 3;

       if (id_l >= 0){

           LTL.block<3, 3>(id_l, id_l) += FtF_ll;

       }

       if (id_r >= 0){

           LTL.block<3, 3>(id_r, id_r) += FtF_rr;

       }

       if (id_c >= 0){

           LTL.block<3, 3>(id_c, id_c) += FtF_cc;

       }

       FtF_lr = tmp_F_lbase.transpose() * tmp_F_rbase;
       if (id_l >= 0 && id_r >= 0){
           if(id_l < id_r){
               LTL.block<3, 3>(id_l, id_r) += FtF_lr;
           }
           else{
               LTL.block<3, 3>(id_r, id_l) += FtF_lr.transpose();
           }
       }

       FtF_lc = tmp_F_lbase.transpose() * tmp_F_cur;
       if (id_l >= 0 && id_c >= 0){
           if(id_l < id_c){

               LTL.block<3, 3>(id_l, id_c) += FtF_lc;

           }
           else{

               LTL.block<3, 3>(id_c, id_l) += FtF_lc.transpose();

           }
       }


       FtF_rc = tmp_F_rbase.transpose() * tmp_F_cur;
       if (id_r >= 0 && id_c >= 0){
           if(id_r < id_c){

               LTL.block<3, 3>(id_r, id_c) += FtF_rc;

           }
           else if(id_r == id_c){

               LTL.block<3, 3>(id_r, id_c) += 2 * FtF_rc;

           }
           else{

               LTL.block<3, 3>(id_c, id_r) += FtF_rc.transpose();

           }
       }

   }


}


void LiGTProblem::OutlierCheckfromPRI(MatrixXd &LTL){

    POVG::Timer timer;

    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

    Size est_t_length = 3 * est_view_size-3;

    int id_cur;

    double threshold = 0.9;
    VectorXd ratio;
    ratio.setZero(num_view_);

    VectorXi est_view_id;
    est_view_id.setZero(est_view_size-1);

    ifstream of("/home/<USER>/PRD_PRI_matlab.txt",std::ios::in);

    double evalues = 0;

    for (int i = 0; i < num_view_; ++i){
        of >> evalues;
        if (est_info_ptr_->origin2est_view_ids[i]<1) continue;
        est_view_id(i) = est_info_ptr_->origin2est_view_ids[i]-1;


        id_cur = 3 * est_view_id(i);


        if (evalues > threshold){
            LTL.block<3, 3>(id_cur, id_cur) += 1e-5*Matrix3d::Identity();

            ViewId outlier_view_id = est_info_ptr_->est2origin_view_ids[est_view_id(i)+1];
            if (outlier_view_id != i){
                cout<<"warning!转换函数存在问题"<<endl;
            }
            pro_view_ids_.emplace_back(i);
            cout<<"Find outlier view! Eliminated!"<<endl;
        }

    }

    // 打印pro_view_ids_中的每个元素
    for (ViewId id : pro_view_ids_) {
        std::cout << id << " ";
    }

    std::cout << std::endl; // 在末尾换行

    cout << "time for OutlierCheckfromPRI2: "
         << timer.Duration()
         << "s"
         << endl;
}



void LiGTProblem::OutlierCheckfromM3(MatrixXd &LTL){

    POVG::Timer timer;

    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

    Size est_t_length = 3 * est_view_size-3;

    int id_cur;

    double threshold = 0.9;
    VectorXd ratio;
    ratio.setZero(num_view_);

    VectorXi est_view_id;
    est_view_id.setZero(est_view_size-1);

    ifstream of("/home/<USER>/PRD_M3_matlab.txt",std::ios::in);

    double evalues = 0;

    for (int i = 0; i < num_view_; ++i){
        of >> evalues;
        if (est_info_ptr_->origin2est_view_ids[i]<1) continue;
        est_view_id(i) = est_info_ptr_->origin2est_view_ids[i]-1;

        id_cur = 3 * est_view_id(i);

        if (evalues > threshold){
            LTL.block<3, 3>(id_cur, id_cur) += 1e4*Matrix3d::Identity();

            ViewId outlier_view_id = est_info_ptr_->est2origin_view_ids[est_view_id(i)+1];
            if (outlier_view_id != i){
                cout<<"warning!转换函数存在问题"<<endl;
            }
            pro_view_ids_.emplace_back(i);
            cout<<"Find outlier view! Eliminated!"<<endl;
        }

    }

    // 打印pro_view_ids_中的每个元素
    for (ViewId id : pro_view_ids_) {
        std::cout << id << " ";
    }

    std::cout << std::endl; // 在末尾换行

    cout << "time for OutlierCheckfromPRI2: "
         << timer.Duration()
         << "s"
         << endl;
}


void LiGTProblem::WriteLTL(const Eigen::MatrixXd& LTL,
                          const int &id){
    std::string fname;
    fname += "/home/<USER>/LTL_";
    fname += to_string(id);
    fname += ".txt";

    //======================= Debug: output residuals to check ==========================
    fstream output(fname, std::ios::out|ios::trunc);
    if(!output.is_open()){
        cout<<"LTL file cannot create, please check path" << endl;
    }
    output << setprecision(16) << LTL << endl;
    output.close();

}

//void LiGTProblem::OutlierCheck(MatrixXd &LTL){

//    POVG::Timer timer;

//    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

//    Size est_t_length = 3 * est_view_size-3;

//    int id_cur;

//    double lambda = 1e-4;
//    VectorXd ratio;
//    ratio.setZero(est_view_size-1);

//    VectorXi view_id;
//    view_id.setZero(est_view_size-1);

//    //不包括第一幅待估视图（参考视图）,故而视图总数est_view_size-1
//    for (ViewId i = 0; i < est_view_size-1; ++i){
//        auto& tmp_feature_vec = features_[i];

//        id_cur = 3 * i;
//        Matrix3d tmp_LTL = Matrix3d::Zero(); // 初始化tmp_LTL为零矩阵

//        // 累加第i行的3x3块
//        for (int row = 0; row < est_view_size-1; ++row) {
//            if (row <= i) {
//                tmp_LTL += LTL.block<3, 3>(3 * row, id_cur).cwiseAbs();
//            }
//        }

//        // 累加第i列的3x3块，从对角线块的下一个开始
//        for (int col = i + 1; col < est_view_size-1; ++col) {
//            tmp_LTL += LTL.block<3, 3>(id_cur, 3 * col).cwiseAbs();
//        }

////        cout<<"tmp_LTL-"<<i<<":"<<endl<<tmp_LTL<<endl;
//        DenseSymShiftSolve<double,Eigen::Upper> op(tmp_LTL);

//        // Construct eigen solver object with shift 0
//        // This will find eigenvalues that are closest to 0
//        SymEigsShiftSolver< double, LARGEST_MAGN,
//                DenseSymShiftSolve<double,Eigen::Upper> > eigs(&op, 2, 3, -1e-5);

//        eigs.init();
//        eigs.compute();//evalues(0)>evalues(1)

//        const Eigen::VectorXd evalues = eigs.eigenvalues();
////        cout<<"evals="<<evalues(0)<<", "<<evalues(1)<<endl;
////        ratio(i) = 1 - (evalues(1)+lambda)/(evalues(0)+lambda);
//        ratio(i) = evalues(1);

//        view_id(i) = est_info_ptr_->est2origin_view_ids[i+1];
//        if (ratio(i) < 1e-6){
//            LTL.block<3, 3>(id_cur, id_cur) += 1e-6*Matrix3d::Identity();

//            for ( PtsId pts_id =0; pts_id < num_pts_; ++pts_id){
//                Track& ptr_track = tracks_info_ptr_->tracks[pts_id].track;
//                for (ObsId obs_id = 0; obs_id < ptr_track.size(); ++obs_id) {
//                    if (ptr_track[obs_id].view_id == (i+1)){
//                        ptr_track[obs_id].is_used = false;
//                    }
//                }
//            }

//            ViewId outlier_view_id = est_info_ptr_->est2origin_view_ids[i+1];
//            pro_view_ids_.emplace_back(outlier_view_id);
//            cout<<"Find outlier view! Eliminated!"<<endl;
//        }

//    }


//    //========================== output residuals to check ==========================
//    fstream output(pro_file_,std::ios::out|ios::trunc);
//    if(!output.is_open()){
//        cout<<"output file cannot create, please check path" << endl;
//    }

//    for (int i = 0; i < est_view_size-1; i++){
//        output << setprecision(16)
//               << ratio(i) << "  " << view_id(i)<< endl;
//    }

//    WriteLTL(LTL,0);


//    cout << "time for prod check of LTL matrix: "
//         << timer.Duration()
//         << "s"
//         << endl;
//}

void LiGTProblem::OutlierCheck(MatrixXd &LTL){

    POVG::Timer timer;

    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

    Size est_t_length = 3 * est_view_size-3;

    int id_cur;

    double lambda = 1e-4;

    VectorXd ratio;
    VectorXd ratio2;
    VectorXd ratio3;
    ratio.setZero(num_view_);
    ratio2.setZero(num_view_);
    ratio3.setZero(num_view_);

    VectorXi est_view_id;
    est_view_id.setZero(est_view_size-1);


    for (int i = 0; i < num_view_; ++i){

        //============================ Note =====================
        //之所以减1，是因为LTL已经去掉est_view_ids中第一幅视图（参考视图）
        //因此，在LTL中的id相当于原本est_view_id -1
        //=======================================================

        if (est_info_ptr_->origin2est_view_ids[i] < 1) {
            ratio(i) = std::numeric_limits<double>::quiet_NaN();  // 使用NaN填充
            ratio2(i) = std::numeric_limits<double>::quiet_NaN();
            ratio3(i) = std::numeric_limits<double>::quiet_NaN();
            continue;
        }

        const ViewId& est_view_id = est_info_ptr_->origin2est_view_ids[i] - 1;
//        cout<< est_info_ptr_->origin2est_view_ids[i]<<endl;
        id_cur = 3 * est_view_id;

        // 创建一个自伴随特征求解器的实例
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> eigenSolver(LTL.block<3, 3>(id_cur, id_cur));
        Eigen::Vector3d evalues;
        if (eigenSolver.info() == Eigen::Success) {
            evalues = eigenSolver.eigenvalues();
//            std::cout << "The eigenvalues of tmp_LTL are:" << std::endl << evalues << std::endl;
        } else {
            std::cout << "Eigenvalue computation was not successful." << std::endl;
        }

        ratio(i) = evalues(2);//largest
        ratio2(i) = evalues(1);
        ratio3(i) = evalues(0);//smallest

        //如果当前视图对应LTL分块矩阵的最大eigenvalue 都小于 阈值， 则说明当前view存在PRO

        if (ratio(i) < 1e-1){
//            LTL.block<3, 3>(id_cur, id_cur) += 1e-6*Matrix3d::Identity();

            ViewId outlier_view_id = est_info_ptr_->est2origin_view_ids[est_view_id+1];
            if (outlier_view_id != i){
                cout<<"warning!转换函数存在问题"<<endl;
            }
            pro_view_ids_.emplace_back(i);
            cout<<"Find outlier view! Eliminated!"<<endl;
        }
    }

//    for (ViewId id : pro_view_ids_) {
////        std::cout << id << " ："<<ratio(id)<<endl;
//    }

    std::cout << std::endl;

    cout << "time for prod check of LTL matrix: "
         << timer.Duration()
         << "s"
         << endl;

    //========================== output evalues to check ==========================
    fstream output(pro_file_,std::ios::out|ios::trunc);
    if(!output.is_open()){
        cout<<"output file cannot create, please check path" << endl;
    }

    for (int i = 1; i < ratio.rows(); i++){
        output << setprecision(16)
               << ratio(i) << "  "<< ratio2(i) << "  "<< ratio3(i) << "  " << i << endl;
    }
    output.close();
    //===============================================================================

}


void LiGTProblem::OutlierCheckForOpenMVG(MatrixXd &LTL){

    POVG::Timer timer;

    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

    Size est_t_length = 3 * est_view_size-3;

    int id_cur;

    double lambda = 1e-4;

    VectorXd ratio;
    VectorXd ratio2;
    VectorXd ratio3;
    ratio.setZero(num_view_);
    ratio2.setZero(num_view_);
    ratio3.setZero(num_view_);

    VectorXi est_view_id;
    est_view_id.setZero(est_view_size-1);


    for (int i = 0; i < num_view_; ++i){
        //之所以减1，是因为LTL已经去掉est_view_ids中第一幅视图（参考视图）
        //因此，在LTL中的id相当于原本est_view_id -1
        if (est_info_ptr_->origin2est_view_ids[i]<1) continue;
        const ViewId& est_view_id = est_info_ptr_->origin2est_view_ids[i] - 1;

        id_cur = 3 * est_view_id;

        // 创建一个自伴随特征求解器的实例
        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> eigenSolver(LTL.block<3, 3>(id_cur, id_cur));
        Eigen::Vector3d evalues;
        if (eigenSolver.info() == Eigen::Success) {
            evalues = eigenSolver.eigenvalues();
//            std::cout << "The eigenvalues of tmp_LTL are:" << std::endl << evalues << std::endl;
        } else {
            std::cout << "Eigenvalue computation was not successful." << std::endl;
        }

        ratio(i) = evalues(2);//largest
        ratio2(i) = evalues(1);
        ratio3(i) = evalues(0);//smallest

        if (ratio3(i) < 1e-3){
//            LTL.block<3, 3>(id_cur, id_cur) = 10000*Matrix3d::Identity();

            ViewId outlier_view_id = est_info_ptr_->est2origin_view_ids[est_view_id+1];
            if (outlier_view_id != i){
                cout<<"warning!转换函数存在问题"<<endl;
            }
            pro_view_ids_.emplace_back(i); //存储的是原始view id，不是est view id
            cout<<"Find outlier view! Eliminated!"<<endl;
        }
    }
    // 打印pro_view_ids_中的每个元素
    std::cout << "[POMVG LiGT Condition Check] num of PRO views:"<< pro_view_ids_.size()<<endl;
//    for (ViewId id : pro_view_ids_) {
//        std::cout << id << " ";
//    }

    std::cout << std::endl; // 在末尾换行


    cout << "time for prod check of LTL matrix: "
         << timer.Duration()
         << "s"
         << endl;

    //========================== output residuals to check ==========================
    fstream output(pro_file_,std::ios::out|ios::trunc);
    if(!output.is_open()){
        cout<<"output file cannot create, please check path" << endl;
    }

    for (int i = 1; i < ratio.rows(); i++){
        output << setprecision(16)
               << ratio(i) << "  "<< ratio2(i) << "  "<< ratio3(i) << "  " << i << endl;
    }

}


//int LiGTProblem::PRIFromTwoViews(const FeatureInfo& left_feature_vec,
//                                 const FeatureInfo& right_feature_vec,
//                                 double& PRI_value){

//    PRI_value = 0;
//    int count = 0;

//    for (int i = 0; i < left_feature_vec.feature.size(); ++i){

//        if(!left_feature_vec.feature[i].obs_ptr->is_used)
//            continue;

//        const auto& left_pts_id = left_feature_vec.feature[i].obs_ptr->pts_id;
//        const ViewId& left_view_id = left_feature_vec.feature[i].obs_ptr->view_id;
//        const Vector3d& x_l = left_feature_vec.feature[i].obs_ptr->coord;

//        Matrix3d& R_l = global_rotations_[left_view_id];

//        for (int j = 0; j < right_feature_vec.feature.size(); ++j){

//            if(!right_feature_vec.feature[j].obs_ptr->is_used)
//                continue;

//            const auto& right_pts_id = right_feature_vec.feature[j].obs_ptr->pts_id;

//            if (left_pts_id == right_pts_id){

//                // calculate PRI value

//                const ViewId& right_view_id = right_feature_vec.feature[j].obs_ptr->view_id;

//                const Vector3d& x_r = right_feature_vec.feature[j].obs_ptr->coord;
//                Matrix3d& R_r = global_rotations_[right_view_id];
//                Matrix3d R_lr = R_r * R_l.transpose();

//                Vector3d theta_ij = x_r.cross(R_lr * x_l);

//                PRI_value += theta_ij.norm();
//                count++;
//            }
//        }
//    }



//    if (count >= 10){
//        PRI_value /= count;
//        return count;
//    }
//    else{
//        return false;
//    }

//}

int LiGTProblem::PRIFromTwoViews(const FeatureInfo& left_feature_vec,
                                 const FeatureInfo& right_feature_vec,
                                 double& PRI_value) {
    PRI_value = 0;
    int count = 0;

    for (const auto& left_feature : left_feature_vec.feature) {
        if (!left_feature.obs_ptr->is_used)
            continue;

        const auto& left_pts_id = left_feature.obs_ptr->pts_id;
        const ViewId& left_view_id = left_feature.obs_ptr->view_id;
        const Vector3d& x_l = left_feature.obs_ptr->coord;
        Matrix3d R_l = global_rotations_[left_view_id];

        for (const auto& right_feature : right_feature_vec.feature) {
            if (!right_feature.obs_ptr->is_used)
                continue;

            const auto& right_pts_id = right_feature.obs_ptr->pts_id;
            if (left_pts_id == right_pts_id) {
                const ViewId& right_view_id = right_feature.obs_ptr->view_id;
                const Vector3d& x_r = right_feature.obs_ptr->coord;
                Matrix3d R_r = global_rotations_[right_view_id];
                Matrix3d R_lr = R_r * R_l.transpose();

                Vector3d theta_ij = x_r.cross(R_lr * x_l);

                PRI_value += theta_ij.norm();
                count++;
            }
        }
    }

    if (count >= 10) {
        PRI_value /= count;
        return count;
    } else {
        return false;
    }
}

//void LiGTProblem::BuildPureRotationalPenalty(Eigen::MatrixXd &LTL){

//    POVG::Timer timer;

//    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

//    Size est_t_length = 3 * est_view_size-3;

//    Matrix3d cross_xr, cross_xc;

//    double s;

//    Matrix3d R_lr, R_lc;

//    Vector3d Rlc_xl;
//    Vector3d m3_lr, m3_lc;
//    RowVector3d tmp_A;
//    Matrix3d tmp_B_l, tmp_B_r;

//    Matrix3d BtB_ll,BtB_rr;
//    Matrix3d BtB_lr;

//    unsigned int track_len;
//    unsigned int i = 0;
//    unsigned int j = 0;
//    unsigned int k = 0;

//    PtsId pts_len = num_pts_;
//    Vector3d R_lr_x_l,R_lc_x_l,xc_R_lc_x_l;
//    RowVector3d tmp_A_R_lr;

//    int id_r,id_l,id_c;

//    unsigned int used_pts = 0;

//    ObsId max_id_l = 0;
//    ObsId max_id_r = 0;

//    double max_s = 0;

//    Vector3d Rx;
//    Vector3d xRx;

//    Vector3d tmp_residual_l,
//             tmp_residual_r,
//             tmp_residual_c,
//             tmp_residual;

//    double residual_value = 0;
//    int num_removed = 0;
//    int used_obs =0;

//    double PRI_threshold = 0.02 ;
//    double total_RPI = 0;
//    int count = 0;

//    // 打印pro_view_ids_中的每个元素
//    for (ViewId id_pro_view : pro_view_ids_) {
//        //之所以减1，是因为LTL已经去掉est_view_ids中第一幅视图（参考视图）
//        //因此，在LTL中的id相当于原本est_view_id -1
//        const ViewId& est_view_id = est_info_ptr_->origin2est_view_ids[id_pro_view];
//        const ViewId& id_in_LTL = est_view_id-1;

//        //feature容器是对应est_view_ids存储的
//        const auto& left_feature_vec = features_[est_view_id];
//        const ViewId& left_view_id = left_feature_vec.feature[0].obs_ptr->view_id;

//        // 如果left_view_id和est_view_id不相同，则抛出异常
//        if (left_view_id != est_view_id) {
//            throw std::runtime_error("[Anomaly detected]: left_view_id does not match est_view_id");
//        }

//        Matrix3d& R_l = global_rotations_[left_view_id];

//        for (ViewId id_co_view = 0; id_co_view < features_.size(); ++id_co_view){
//            if(id_co_view == id_pro_view) continue;

//            const auto& right_feature_vec = features_[id_co_view];
//            const ViewId& right_view_id = right_feature_vec.feature[0].obs_ptr->view_id;

//            Matrix3d& R_r = global_rotations_[right_view_id];
//            Matrix3d R_lr = R_r * R_l.transpose();

//            double PRI_value = 0;
//            int num_matches = PRIFromTwoViews(left_feature_vec,right_feature_vec,PRI_value);

//            if (num_matches && PRI_value < 0.1){
//                // build penaly matrix: A_lr*t = tlr = tr - Rlr * tl;
//                int id_r,id_l;

//                id_l = 3 * left_view_id - 3;
//                id_r = 3 * right_view_id - 3;


//                tmp_B_l = -R_lr;
//                tmp_B_r = Eigen::Matrix3d::Identity();

//                BtB_ll.noalias() = tmp_B_l.transpose() * tmp_B_l;
//                BtB_rr.noalias() = tmp_B_r.transpose() * tmp_B_r;

//                double ratio = 1/(max(1e-2,PRI_value));
//                if (id_l >= 0){

//                    LTL.block<3, 3>(id_l, id_l) += BtB_ll * ratio;
//                }

//                if (id_r >= 0){

//                    LTL.block<3, 3>(id_r, id_r) += BtB_rr * ratio;

//                }

//                BtB_lr = tmp_B_l.transpose() * tmp_B_r;
//                if (id_l >= 0 && id_r >= 0){
//                    if(id_l < id_r){
//                        LTL.block<3, 3>(id_l, id_r) += BtB_lr  * ratio;
//                    }
//                    else{
//                        LTL.block<3, 3>(id_r, id_l) += BtB_lr.transpose()  * ratio;
//                    }
//                }
//            }

//        }

//    }


//    cout << "time for BuildPureRotationalPenalty: "
//         << timer.Duration()
//         << "s"
//         << endl;
//}


void LiGTProblem::BuildPureRotationalPenalty(Eigen::MatrixXd &LTL) {
    POVG::Timer timer;

    const Eigen::Matrix3d identity_matrix = Eigen::Matrix3d::Identity();
    const Size est_view_size = est_info_ptr_->estimated_view_ids.size();

//    double time_pri = 0.0;
    for (ViewId id_pro_view : pro_view_ids_) {


        const ViewId est_view_id = est_info_ptr_->origin2est_view_ids[id_pro_view];
        if (est_info_ptr_->origin2est_view_ids[id_pro_view]<1) continue;
        const ViewId id_in_LTL = est_view_id - 1;
        const auto& left_feature_vec = features_[est_view_id];
        const ViewId left_view_id = left_feature_vec.feature[0].obs_ptr->view_id;

        if (left_view_id != est_view_id) {
            throw std::runtime_error("[Anomaly detected]: left_view_id does not match est_view_id");
        }

        Matrix3d R_l = global_rotations_[left_view_id];

        for (ViewId id_co_view = 0; id_co_view < features_.size(); ++id_co_view) {
            if (id_co_view == id_pro_view) continue;

            const auto& right_feature_vec = features_[id_co_view];
            const ViewId right_view_id = right_feature_vec.feature[0].obs_ptr->view_id;
            const Matrix3d& R_r = global_rotations_[right_view_id];
            const Matrix3d& R_lr = R_r * R_l.transpose();

            double PRI_value = 0;
//            POVG::Timer timer_inner;

            PRI_value = 0;
            int num_matches = 0;
            // test
            for (const auto& left_feature : left_feature_vec.feature) {
                if (!left_feature.obs_ptr->is_used)
                    continue;

                const auto& left_pts_id = left_feature.obs_ptr->pts_id;
                const Vector3d& x_l = left_feature.obs_ptr->coord;

                for (const auto& right_feature : right_feature_vec.feature) {
                    if (!right_feature.obs_ptr->is_used)
                        continue;

                    const auto& right_pts_id = right_feature.obs_ptr->pts_id;
                    if (left_pts_id == right_pts_id) {
                        const Vector3d& x_r = right_feature.obs_ptr->coord;

                        // m3 ratio
                        PRI_value += (x_r.cross(R_lr * x_l)).norm();

                        num_matches++;
                    }
                }
            }

            if (num_matches < 1) continue;

            PRI_value /= num_matches;
//            time_pri += timer_inner.Duration();

            if (PRI_value < 0.1) {
                int id_l = 3 * left_view_id - 3;
                int id_r = 3 * right_view_id - 3;

                Matrix3d tmp_B_l = -R_lr;
                Matrix3d tmp_B_r = identity_matrix;

                Matrix3d BtB_ll = tmp_B_l.transpose() * tmp_B_l;
                Matrix3d BtB_rr = tmp_B_r.transpose() * tmp_B_r;

                double ratio = 1 / std::max(1e-2, PRI_value);

                if (id_l >= 0) {
                    LTL.block<3, 3>(id_l, id_l).noalias() += BtB_ll * ratio;
                }

                if (id_r >= 0) {
                    LTL.block<3, 3>(id_r, id_r).noalias() += BtB_rr * ratio;
                }

                Matrix3d BtB_lr = tmp_B_l.transpose() * tmp_B_r;
                if (id_l >= 0 && id_r >= 0) {
                    if (id_l < id_r) {
                        LTL.block<3, 3>(id_l, id_r).noalias() += BtB_lr * ratio;
                    } else {
                        LTL.block<3, 3>(id_r, id_l).noalias() += BtB_lr.transpose() * ratio;
                    }
                }
            }
        }
    }

    std::cout << "time for BuildPureRotationalPenalty: "
              << timer.Duration()
              << "s"
//              << ",time for PRI compute: "
//                            << time_pri
//                            << "s"
              << std::endl;
}


//void LiGTProblem::OutlierCheck(MatrixXd &LTL){

//    POVG::Timer timer;

//    Size est_view_size = est_info_ptr_->estimated_view_ids.size();

//    Size est_t_length = 3 * est_view_size-3;

//    int id_cur;

//    double lambda = 1e-4;
//    VectorXd ratio;
//    VectorXd ratio2;
//    VectorXd ratio3;
//    ratio.setZero(est_view_size-1);
//    ratio2.setZero(est_view_size-1);
//    ratio3.setZero(est_view_size-1);

//    VectorXi view_id;
//    view_id.setZero(est_view_size-1);

//    //不包括第一幅待估视图（参考视图）
//    for (ViewId i = 0; i < est_view_size-1; ++i){
//        auto& tmp_feature_vec = features_[i];

//        id_cur = 3 * i;
//        auto tmp_LTL = LTL.block<3, 3>(id_cur, id_cur);

//        DenseSymShiftSolve<double,Eigen::Upper> op(tmp_LTL);

//        // 创建一个自伴随特征求解器的实例
//        Eigen::SelfAdjointEigenSolver<Eigen::Matrix3d> eigenSolver(tmp_LTL);
//        Eigen::Vector3d evalues;
//        if (eigenSolver.info() == Eigen::Success) {
//             evalues = eigenSolver.eigenvalues();
//            std::cout << "The eigenvalues of tmp_LTL are:" << std::endl << evalues << std::endl;
//        } else {
//            std::cout << "Eigenvalue computation was not successful." << std::endl;
//        }

////        // Construct eigen solver object with shift 0
////        // This will find eigenvalues that are closest to 0
////        SymEigsShiftSolver< double, LARGEST_MAGN,
////                DenseSymShiftSolve<double,Eigen::Upper> > eigs(&op, 3, 3, -1e-5);

////        eigs.init();
////        eigs.compute();//evalues(0)>evalues(1)

////        const Eigen::VectorXd evalues = eigs.eigenvalues();
////        cout<<"evals="<<evalues(0)<<", "<<evalues(1)<<endl;
////        ratio(i) = 1 - (evalues(1)+lambda)/(evalues(0)+lambda);

//        ratio(i) = evalues(2);//largest
//        ratio2(i) = evalues(1);
//        ratio3(i) = evalues(0);//smallest

//        view_id(i) = est_info_ptr_->est2origin_view_ids[i+1];
//        if (ratio(i) < 1e-3){//if largets cannot ...
////            LTL.block<3, 3>(id_cur, id_cur) = 0.1*Matrix3d::Identity();
//            LTL.block<3, 3>(id_cur, id_cur) += 1e-5*Matrix3d::Identity();

//            for ( PtsId pts_id =0; pts_id < num_pts_; ++pts_id){
//                Track& ptr_track = tracks_info_ptr_->tracks[pts_id].track;
//                for (ObsId obs_id = 0; obs_id < ptr_track.size(); ++obs_id) {
//                    if (ptr_track[obs_id].view_id == (i+1)){
//                        ptr_track[obs_id].is_used = false;
//                    }
//                }
//            }

//            ViewId outlier_view_id = est_info_ptr_->est2origin_view_ids[i+1];
//            pro_view_ids_.emplace_back(outlier_view_id);
//            cout<<"Find outlier view! Eliminated!"<<endl;
//        }

//    }
//    // 打印pro_view_ids_中的每个元素
//    for (ViewId id : pro_view_ids_) {
//        std::cout << id << " ";
//    }

//    std::cout << std::endl; // 在末尾换行

//    //========================== output residuals to check ==========================
//    fstream output(pro_file_,std::ios::out|ios::trunc);
//    if(!output.is_open()){
//        cout<<"output file cannot create, please check path" << endl;
//    }

//    for (int i = 0; i < est_view_size-1; i++){
//        output << setprecision(16)
//               << ratio(i) << "  "<< ratio2(i) << "  "<< ratio3(i) << "  " << view_id(i)<< endl;
//    }

//    WriteLTL(LTL,0);


//    cout << "time for prod check of LTL matrix: "
//         << timer.Duration()
//         << "s"
//         << endl;
//}


void LiGTProblem::SetPROFile(std::string file){
    pro_file_ = file;
}

void LiGTProblem::SetPROProcessMode(PRO_PROCESS_MODE mode){
    pro_mode_ = mode;
}

void LiGTProblem::SolveLiGT(spMatrix& LTL,
                            VectorXd& evectors){
    POVG::Timer timer;

    // ========================= Solve Problem by Spectra's Eigs =======================
    // Construct matrix operation object using the wrapper class

    SparseSymShiftSolve<double,Eigen::Upper> op(LTL);

    // Construct eigen solver object with shift 0
    // This will find eigenvalues that are closest to 0
    SymEigsShiftSolver< double, LARGEST_MAGN,
            SparseSymShiftSolve<double,Eigen::Upper> > eigs(&op, 2, 8, -1e-3);

    eigs.init();
    eigs.compute();

    const Eigen::VectorXd evalues = eigs.eigenvalues();

    cout<<"eigen values := "<<evalues.transpose()<<" ,"<<evalues.rows()<<endl;
    int min_id = evalues.rows()-1;
    svd_value_ = evalues(min_id);

    cout << "svd value = " << evalues.transpose()
         << endl;


    auto tmp_evectors = eigs.eigenvectors();
    evectors = tmp_evectors.col(min_id);


    //========================== output ratio to check ==========================
    string output_path = "/home/<USER>/";
    output_path += "LiGT_svd_ratio";
    output_path += ".txt";

    fstream output(output_path,std::ios::out|ios::trunc);
    if(!output.is_open()){
        cout<<"output file cannot create, please check path" << endl;
    }

    double lambda = 1e-7;
    output << setprecision(16)
           << (evalues(1)+lambda)/((evalues(0)+lambda));

    output.close();

    cout << "time for eigs solver : "
         << timer.Duration()
         << "s"
         << endl;


}

void LiGTProblem::PROSolution() {

    PrintCopyright();

    cout << "\n************  LiGT Solve Summary  **************\n"
         << "num_est_view_ = " << num_est_view_ << "; num_pts = " << num_pts_ << "; num_obs = " << num_obs_
         << endl;

    // start time clock
    POVG::Timer timer;

    // allocate memory for LTL matrix where Lt=0
    Eigen::MatrixXd LTL;

    // use A_lr * t > 0 to identify the correct sign of the translation result
    Eigen::RowVectorXd A_lr;

    // construct LTL and A_lr matrix from 3D points

    BuildLTL(LTL,A_lr);

    switch (pro_mode_) {

    case M3_REMOVE_MODE:
        OutlierCheckfromM3(LTL);
        break;

    case PRI_REMOVE_MODE:
        OutlierCheckfromPRI(LTL);
        break;

    case OPENMVG_MODE:
        BuildLTLAcceptableParallaxAngle(LTL, A_lr);//openMVG usage
        OutlierCheckForOpenMVG(LTL);
        break;

    default:
        break;
    }
    WriteLTL(LTL,0);


    LTL.diagonal().array() +=1e-8;
    //[Step.4 in Pose-only Algorithm]: obtain the translation solution by using SVD
       int modified_size = (num_est_view_ - 1 - pro_view_ids_.size()) * 3;
    VectorXd evectors = VectorXd::Zero( modified_size);

    // Step 4.1: Modify LTL matrix

    Eigen::MatrixXd modified_LTL(modified_size, modified_size);
    int row_offset = 0;
    for (int i = 0; i < num_est_view_-1; ++i) {
        const ViewId& origin_view_id_i = est_info_ptr_->est2origin_view_ids[i+1];

        if (std::find(pro_view_ids_.begin(), pro_view_ids_.end(), origin_view_id_i) != pro_view_ids_.end()) { // i+1 because the first view is already removed
            row_offset += 3;
            continue;
        }
        int col_offset = 0;
        for (int j = 0; j < num_est_view_-1; ++j) {
            const ViewId& origin_view_id_j = est_info_ptr_->est2origin_view_ids[j+1];
            if (std::find(pro_view_ids_.begin(), pro_view_ids_.end(), origin_view_id_j) != pro_view_ids_.end()) { // j+1 because the first view is already removed
                col_offset += 3;
                continue;
            }
            modified_LTL.block<3, 3>(3 * i - row_offset, 3 * j - col_offset) = LTL.block<3, 3>(3 * i, 3 * j);
        }
    }


    WriteLTL(modified_LTL,1);
    std::vector<int> cols_to_remove;
    for (ViewId view_id : pro_view_ids_) {
        int col_start = 3 * (view_id - 1); // -1 因为视图索引从1开始，但列索引从0开始
        for (int i = 0; i < 3; ++i) {
            cols_to_remove.push_back(col_start + i);
        }
    }


    // 根据需要剔除的列数创建新矩阵
    Eigen::MatrixXd modified_A_lr(A_lr.rows(), A_lr.cols() - cols_to_remove.size());

    int col_index = 0;
    for (int i = 0; i < A_lr.cols(); ++i) {
        if (std::find(cols_to_remove.begin(), cols_to_remove.end(), i) == cols_to_remove.end()) {
            // 如果当前列不在剔除列表中，则复制到新矩阵
            modified_A_lr.col(col_index++) = A_lr.col(i);
        }
    }

    // modified_A_lr 现在是剔除了 pro_view_ids_ 对应列的 A_lr 矩阵

    // Step 4.2: Adjust evectors
    VectorXd modified_evectors = VectorXd::Zero(modified_size);

    spMatrix sp_LTL = modified_LTL.sparseView();
    SolveLiGT(sp_LTL, modified_evectors);

    //[Step.5 in Pose-only Algorithm]: identify the right global translation solution
    IdentifySign(modified_A_lr, modified_evectors);

    // algorithm time cost
    time_use_ = timer.Duration();

    cout << ">> time for the LiGT algorithm: "
         << time_use_
         << " ms"
         << endl;


    // (optional) transform solution evectors into global_translations_

    // Step 3: Reconstruct global_translations_
    global_translations_.clear();
    global_translations_.resize(num_est_view_);
    global_translations_[0].setZero();  // Assuming the first view is always zero

    int vector_index = 0;
    for (ViewId i = 1; i < num_est_view_; ++i) {
        if (std::find(pro_view_ids_.begin(), pro_view_ids_.end(), i) != pro_view_ids_.end()) {
            global_translations_[i] = Eigen::Vector3d::Constant(NAN);  // Set to NaN for pro_view_ids_
        } else {
            global_translations_[i] = modified_evectors.segment<3>(vector_index);
            vector_index += 3;
        }
    }


    // (optional) translation recovery
    RecoverViewIds();
}


void LiGTProblem::Solution() {

    PrintCopyright();

    cout << "\n************  LiGT Solve Summary  **************\n"
         << "num_est_view_ = " << num_est_view_ << "; num_pts = " << num_pts_ << "; num_obs = " << num_obs_
         << endl;

    // start time clock
    POVG::Timer timer;

    // allocate memory for LTL matrix where Lt=0
    Eigen::MatrixXd LTL;

    // use A_lr * t > 0 to identify the correct sign of the translation result
    Eigen::RowVectorXd A_lr;

    // construct LTL and A_lr matrix from 3D points
    BuildLTL(LTL, A_lr);

    WriteLTL(LTL,0);

    switch (pro_mode_) {

    case LIGT_PLUS_MODE:
        OutlierCheck(LTL);
        BuildPureRotationalPenalty(LTL);

        break;
    case LIGT_ORIGIN_MODE:

        break;

    default:
        break;
    }

    // （标准log) 打印 pro_view_ids_ 信息
    std::cout << "[POMVG LiGT Condition Check] num of PRO views:"<< pro_view_ids_.size()<<endl;

//    LTL.diagonal().array() +=1e3;

    // (debug log) 输出当前LTL矩阵
    WriteLTL(LTL,1);

    //[Step.4 in Pose-only Algorithm]: obtain the translation solution by using SVD
    VectorXd evectors = VectorXd::Zero( 3 * num_est_view_ - 3);
    spMatrix sp_LTL = LTL.sparseView();
    SolveLiGT(sp_LTL, evectors);

    //[Step.5 in Pose-only Algorithm]: identify the right global translation solution
    IdentifySign(A_lr, evectors);

    // algorithm time cost
    time_use_ = timer.Duration();

    cout << ">> time for the LiGT algorithm: "
         << time_use_
         << " ms"
         << endl;


    // transform solution evectors into global_translations_
    global_translations_.clear();
    global_translations_.resize(num_est_view_);

    global_translations_[0].setZero();

    for (ViewId i = 1; i < num_est_view_; ++i) {

        global_translations_[i] = evectors.middleRows<3>(3 * i - 3);
    }

    // (optional) translation recovery
    RecoverViewIds();
}


Tracks LiGTProblem::GetTracks(){

    return tracks_info_ptr_->tracks;

}

Poses LiGTProblem::GetPoses(){

    return poses_;

}

Attitudes LiGTProblem::GetRotations(){

    return global_rotations_;
}

void LiGTProblem::PrintCopyright(){

    cout << "\n===============================================================\n"
         << "    The LiGT Algorithm (Fast Version 1.1) for global translation\n"
         << "[Conditions of Use] The LiGT algorithm is distributed under the License\n"
         << "of Attribution-ShareAlike 4.0 International\n"
         << "(https://creativecommons.org/licenses/by-sa/4.0/).\n"
         << "If you use it for a publication, please see [Notes] in header file.\n"
         << "---------------------------------------------------------------\n";
}

}
