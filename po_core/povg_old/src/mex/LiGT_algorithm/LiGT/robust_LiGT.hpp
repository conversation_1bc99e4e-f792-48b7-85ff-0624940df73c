// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#ifndef ROBUST_LIGT_ALGORITHM
#define ROBUST_LIGT_ALGORITHM

#pragma once
#include <string>

#include "povg_types.hpp"
#include "LiGT_algorithm.hpp"


namespace POVG {

// ============== The Robust LiGT Algorithm (Fast Version 2.0.1) =============
// [Version History]
// v1.0: first release; parallelism by <PERSON>.
// v1.1: Spectra replaces <PERSON>igen; Block manipulation to implement LTL matrix.
// v2.0.0: merge the latest version of LiGT into this version.
// v2.0.1: add the robust process for LiGT.
//
// Coded by: Dr<PERSON><PERSON> <PERSON>
// Email: <EMAIL>, <EMAIL>
//
// [Conditions of Use]: the LiGT algorithm is distributed under
// the License of Attribution-ShareAlike 4.0 International
// (https://creativecommons.org/licenses/by-sa/4.0/).
//
//------------------
//-- Bibliography --
//------------------
// If you use it for a publication, please cite the following paper:
//- [1] "A Pose-only Solution to Visual Reconstruction and Navigation".
//- Authors: <AUTHORS>
//- Date: December 2021.
//- Journal: IEEE T-PAMI.
//
//- [2] "Equivalent constraints for two-view geometry: pose solution/pure rotation identification and 3D reconstruction".
//- Authors: <AUTHORS>
//- Date: December 2019.
//- Journal: IJCV.
//
// This is a dedicated version of the LiGT algorithm for openMVG, which is supported by
// the Inertial and Visual Fusion (VINF) research group in Shanghai Jiao Tong University
// @ https://www.researchgate.net/lab/Inertial-visual-Fusion-VINF-Yuanxin-Wu
//
// Note:
// 1. The LiGT algorithm is subject to the University proprietary right with patent protection.
// The current version of the LiGT algorithm is generally consistent with
// the above-mentioned T-PAMI paper in terms of accuracy.
//
// 2. It does not consider the rank condition in Proposition 6 of the T-PAMI paper.
//

enum OutlierRemovalTypes{
    ROBUST_LIGT,
    GNC_LIGT,
    ORIGIN,
    PA,
    ANAL_BA,
    DLT_BA,
    ROBUST_LIGT_V2,
    PA_V2,
    OPENMVG_BA,
    PTSPA_V2,
    NONE
};

enum OutlierDetectionMode{
    THREE_SIGMA,
    MID_METHOD,
    MID_3DPTS
};

enum LTLMode{
    FROM_TRACKS,
    FROM_FEATURES,
    FROM_FULLY_TRACKS
};
// 辅助函数，用于连接路径和文件名，考虑了跨平台的路径分隔符
string joinPath(const string& folder, const string& fileName);

class RobustLiGT : public LiGTProblem{
public:
    RobustLiGT(const std::string& globalR_file,
               const std::string& track_file,
               const std::string& output_file,
               const std::string& time_file,
               const int& fixed_id,
               const int& min_tracking_length)
        : LiGTProblem(globalR_file,
                      track_file,
                      output_file,
                      time_file,
                      fixed_id,
                      min_tracking_length){

    }


    void SetupOrientationFromPAResults(const std::string& PA_file);

    void CalculateResidual(int id = 0);

    void CalculatePtsResidual();


    Translations Solve(const Eigen::MatrixXd& LTL,
                       const Eigen::RowVectorXd& A_lr);

    // robust solution using IRLS with L1-norm
    // status: success but unsatisfactory
    void RobustSolution();

    void BuildRobustLTL(Eigen::MatrixXd& LTL,
                        RowVectorXd& A_lr);

    void BuildFullyRobustLTL(Eigen::MatrixXd& LTL,
                             RowVectorXd& A_lr);

    void BuildSingleLTL(Eigen::MatrixXd& LTL,
                        RowVectorXd& A_lr, Track &track,
                        const ObsInfo* obs_ptr = nullptr, const double &weight =1);

    void BuildSingleLTLPureRotation(Eigen::MatrixXd& LTL, Track &track,
                        const ObsInfo* obs_ptr = nullptr);

    void BuildRobustLTLFromFeatures(Eigen::MatrixXd& LTL,
                                    RowVectorXd& A_lr);

    // 坍缩问题
    void BuildCollapsePenalty(Eigen::MatrixXd& LTL);

    // 坍缩问题
    void CheckPenalty(Eigen::MatrixXd& LTL);


    // v2版本:采用类似改进PA的基准视图评估策略, 对噪声进行重新统计
    // v2 version: re-calculate noise

    void GNC_RemoveObsOutliers(int id = 0);
    void GNC_ReweightObs();//

    void RemoveObsOutliers(int id = 0);
    void RemoveObsOutliersV2(int id = 0);

    void RemoveObsOutliersOrigin(int id = 0);

    void RemoveObsOutliersPA(int id = 0);
    void RemoveObsOutliersPAV2(int id = 0);
    void RemovePtsOutliersPA(int id = 0);

    void RemoveOutlierAnalBA(int id = 0);

    void RemoveOutlierDLTBA(int id = 0);
    void RemoveOutlierOpenMVGBA(int id = 0);

    void RemoveOutlierNone(int id = 0);

    void DetectAndRemoveOutlier(OutlierDetectionMode mode = MID_METHOD,double threshold=3);
    // robust solution using NAG library
    // status: failed!
//    void NAGSolution();
//    Translations NAGSolve(const Eigen::MatrixXd& LTL,
//                          const Eigen::RowVectorXd& A_lr);

//    void NAGSolveLiGT(const MatrixXd &LTL,
//                      VectorXd& evectors);

    // robust solution using L1-homotopy
    // status: failed!

    // robust solution using outlier removing
    // status: success, but just so so!
    // need check graph
    void BuildLTLRemovingOutlier(Eigen::MatrixXd& LTL,
                                 RowVectorXd& A_lr,
                                 const int &threshold);

    void SolutionRemovingOutlier();

    // robust solution using
    // status: waiting
    void BuildEquallyWeightedLTL(Eigen::MatrixXd& LTL,
                                 RowVectorXd& A_lr);

    void InitCoviews(){coviews_.setZero(num_est_view_ - 1, num_est_view_ - 1);}

    void BuildCoviews();
    void BuildCoviews(const ViewId& lbase_view,
                      const ViewId& rbase_view,
                      const ViewId& current_view);

    void CheckCoviews();
    void UpdateLTL(MatrixXd& LTL);

    void WriteCoviews(const int &id = 0);
    void SetOriFolder(const string& folder);
    void WriteOutlierInfo(const char *file_name="",
                          const int &id = 0,
                          const vector<ObsId>* outlier_obs_ids = nullptr,
                          const char* output_folder = nullptr,
                          const pair<vector<double>,vector<unsigned int>>* ptr_residual_info = nullptr);

    void SetOutlierRemovalTypes(const OutlierRemovalTypes& outlier_removal_types);

    const Vector3d AnalyticalBuild3Dpts(Track &track);
    const Vector3d DLTBuild3Dpts(Track& track);

    void SetLTLBuildMode(const LTLMode& ltl_mode);

    void BuildPRIVector();
private:
    vector<double> residuals_;
    pair<vector<double>,vector<ObsId>> residuals_info_;


    double mean_residuals_ = 0;
    double std_redisuals_ = 0;

    vector<ViewId> num_obs_in_per_view_;



    MatrixXi coviews_;

    // except for the first estimated view id
    set<ViewId> outlier_view_ids_;

//    vector<vector<unsigned int>>

    OutlierRemovalTypes outlier_removal_types_ = OutlierRemovalTypes::ROBUST_LIGT;

    LTLMode ltl_mode_ = LTLMode::FROM_TRACKS;



    //folder path for outlier ORI files
    std::string ori_folder_= "/home/<USER>/";
};

}
#endif
