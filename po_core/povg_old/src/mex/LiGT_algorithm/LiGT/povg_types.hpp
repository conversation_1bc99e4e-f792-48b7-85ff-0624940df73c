// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#ifndef POVG_TYPES
#define POVG_TYPES

#include <vector>
#include <Eigen/Core>
#include <Eigen/Sparse>

#include <set>
#include <unordered_map>
#include <memory>

using namespace Eigen;
using namespace std;

namespace POVG
{

    using IndexT = uint32_t;

    typedef SparseMatrix<double> spMatrix;

    typedef unsigned long ViewId;
    typedef unsigned long PtsId;
    typedef unsigned long ObsId;

    typedef unsigned int Size;

    // pose storage
    typedef vector<Eigen::Matrix3d> Attitudes;
    typedef vector<Eigen::Vector3d> Translations;

    struct Pose
    {
        Matrix3d rotations;
        Vector3d translations;
        bool is_used = true;
        Pose()
        {
            rotations.setIdentity();
            translations.setZero();
            is_used = true;
        }

        Pose(const Matrix3d &rot, const Vector3d &trans)
            : rotations(rot), translations(trans) {}
    };

    using Poses = unordered_map<ViewId, Pose>;

    // ================== relative types =========================
    using BearingVector = Matrix<double, 3, Dynamic>;
    using IntrinsicMats = unordered_map<ViewId, Matrix3d>;

    struct MatchPair
    {
        BearingVector points1;
        BearingVector points2;

        // 默认构造函数
        MatchPair() : points1(BearingVector(3, 0)), points2(BearingVector(3, 0)) {}

        // 参数化构造函数
        MatchPair(const BearingVector &p1, const BearingVector &p2)
            : points1(p1), points2(p2) {}
    };
    using MatchInd = std::pair<ViewId, ViewId>;
    using MatchesInfo = std::unordered_map<MatchInd, MatchPair>;

    using RelativePoses = std::unordered_map<MatchInd, Pose>;

    // ================== Error Summary ===============
    struct MethodError
    {
        string method;
        double medErr;
        double meanErr;
        double meanZErr; // Z 分数的平均值

        MethodError(string m, double medE, double meanE, double mZErr)
            : method(std::move(m)), medErr(medE), meanErr(meanE), meanZErr(mZErr) {}
    };
    using MethodErrors = vector<MethodError>;

    // ================== 3D pts ======================
    struct WorldPoint
    {
        Vector3d world_pts;
        bool is_used = true;
    };
    typedef vector<WorldPoint> WorldPoints;

    // image observation information
    struct ObsInfo
    {
        ViewId view_id;
        PtsId pts_id;
        Eigen::Vector3d coord;
        bool is_used = true;
        ObsId obs_id;
    };

    // track information
    typedef vector<ObsInfo> Track;

    struct TrackInfo
    {
        Track track;
        bool is_used = true;
    };

    typedef vector<TrackInfo> Tracks;

    struct TracksInfo
    {
        Tracks tracks;

        Size num_view = 0;
        Size num_pts = 0;
        Size num_obs = 0;
    };

    struct Feature
    {
        ObsInfo *obs_ptr = nullptr;
        int track_length = 0;
        double m3_value;
        bool is_used = true;
    };
    struct FeatureInfo
    {
        vector<Feature> feature;
        double mean_m3_value = 0;
        Size num_depth_features = 0;
        double mean_ndf = 0;
    };

    struct PRIInfo
    {
        int left_view_id;
        int right_view_id;
        double PRI_value;
        int num_matches;
    };

    typedef unordered_map<ViewId, FeatureInfo> Features;
    typedef std::shared_ptr<TracksInfo> TracksInfoPtr;

    // estimated information
    typedef vector<ViewId> EstimatedViewIds;
    typedef vector<ViewId> OriginViewIds;
    typedef unordered_map<ViewId, ViewId> Origin2EstViewIds;
    typedef unordered_map<ViewId, ViewId> Est2OriginViewIds;

    struct EstInfo
    {
        EstimatedViewIds estimated_view_ids;
        OriginViewIds origin_view_ids;
        Origin2EstViewIds origin2est_view_ids;
        Est2OriginViewIds est2origin_view_ids;

        void BuildMap()
        {

            origin2est_view_ids.clear();
            est2origin_view_ids.clear();

            if (estimated_view_ids.size() > 0 && origin_view_ids.size() > 0 && estimated_view_ids.size() == origin_view_ids.size())
            {
                for (ViewId i = 0; i < estimated_view_ids.size(); ++i)
                {
                    origin2est_view_ids.insert({origin_view_ids[i], estimated_view_ids[i]});
                    est2origin_view_ids.insert({estimated_view_ids[i], origin_view_ids[i]});
                }
            }
        }
    };

    typedef std::shared_ptr<EstInfo> EstInfoPtr;

    bool checkRotation(const Eigen::Matrix3d &rotation);
    bool checkTranslation(const Eigen::Vector3d &translation);

}

namespace std
{
    template <>
    struct hash<pair<POVG::ViewId, POVG::ViewId>>
    {
        size_t operator()(const pair<POVG::ViewId, POVG::ViewId> &p) const noexcept
        {
            auto hash1 = hash<POVG::ViewId>{}(p.first);
            auto hash2 = hash<POVG::ViewId>{}(p.second);
            return hash1 ^ (hash2 << 1); // 或者使用其他组合哈希方法
        }
    };
}

#endif
