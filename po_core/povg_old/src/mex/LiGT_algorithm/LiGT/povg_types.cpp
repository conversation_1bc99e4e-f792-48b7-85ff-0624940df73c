// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/

#include <iostream>
#include <fstream>
#include <Eigen/Dense>
#include <Eigen/SVD>
#include "povg_types.hpp"

using namespace POVG;

namespace POVG
{

    bool checkRotation(const Eigen::Matrix3d &rotation)
    {
        // 检查是否为3x3矩阵（通过类型已保证）

        // 检查行列式是否接近1
        double det = rotation.determinant();
        if (std::abs(det - 1.0) > 1e-6)
            return false;

        // 检查是否为正交矩阵 R * R^T = I
        Eigen::Matrix3d should_be_identity = rotation * rotation.transpose();
        Eigen::Matrix3d identity = Eigen::Matrix3d::Identity();

        for (int i = 0; i < 3; ++i)
        {
            for (int j = 0; j < 3; ++j)
            {
                if (std::abs(should_be_identity(i, j) - identity(i, j)) > 1e-6)
                    return false;
            }
        }

        return true;
    }

    bool checkTranslation(const Eigen::Vector3d &translation)
    {
        // 检查是否包含NaN或Inf
        for (int i = 0; i < 3; ++i)
        {
            if (!std::isfinite(translation(i)))
                return false;
        }

        // 检查是否为零向量（可选，根据具体需求）
        // if (translation.norm() < 1e-12)
        //     return false;

        return true;
    }

}
