// This file is part of a pose-only algorithm of Linear Global Translation (LiGT)

// Copyright (c) 2022, <PERSON> and <PERSON><PERSON>

// This Source Code Form is subject to the license terms of
// Creative Commons Attribution Share Alike 4.0 International.
// Details are available at https://choosealicense.com/licenses/cc-by-sa-4.0/



#include <iostream>
#include <fstream>

#include "povg_tracks.hpp"
#include "povg_timer.hpp"

using namespace POVG;

namespace POVG {



// function implements
void check_tracks_info(const ViewId fixed_id,
                       TracksInfoPtr& tracks_info_ptr,
                       EstInfo& est_info){

    cout << "checking tracks information...\n";

    Size tmp_num_obs = 0;

    std::set<ViewId> est_view;
    for ( PtsId i = 0; i < tracks_info_ptr->tracks.size(); ++i){
        Track& track = tracks_info_ptr->tracks[i].track;
        for (ObsId j = 0; j < track.size(); ++j){
            est_view.insert(track[j].view_id);
        }
        tmp_num_obs += track.size();
    }

    // check information
    if (tracks_info_ptr->num_view != est_view.size())
    {
        tracks_info_ptr->num_view = est_view.size();
    }

    if (tracks_info_ptr->num_pts != tracks_info_ptr->tracks.size())
    {
        tracks_info_ptr->num_pts = tracks_info_ptr->tracks.size();
    }

    if (tracks_info_ptr->num_obs != tmp_num_obs)
    {
        tracks_info_ptr->num_obs = tmp_num_obs;
    }

    // build estimated view information
    ViewId id = 0;
    for ( auto& view_id : est_view){
        est_info.origin_view_ids.emplace_back(view_id);
        est_info.estimated_view_ids.emplace_back(id);
        id ++;
    }

    // select the reference view id by the fixed camera id
    auto origin_view_id = est_info.origin_view_ids[0];
    for ( ViewId i = 0; i < est_info.origin_view_ids.size(); ++i){
        if ( est_info.origin_view_ids[i] == fixed_id){
            est_info.origin_view_ids[0] = est_info.origin_view_ids[i];
            est_info.origin_view_ids[i] = origin_view_id;
        }
    }

    est_info.BuildMap();

    // update view id in track
    for ( PtsId i = 0; i < tracks_info_ptr->tracks.size(); ++i){
        Track& track = tracks_info_ptr->tracks[i].track;
        for ( ObsId j = 0; j < track.size(); ++j){
            track[j].view_id = est_info.origin2est_view_ids[track[j].view_id];
        }
    }
}

void load_tracks_info(const std::string& track_file,
                      const Size& min_track_length,
                      TracksInfoPtr& tracks_info_ptr,
                      const bool& bearing_mode) {

    POVG::Timer timer;

    // load tracks file
    std::fstream tracks_file(track_file, ios::in);
    if (!tracks_file.is_open()) {
        std::cerr << "tracks file cannot load";
        return;
    }

    // create a TracksInfo object
    tracks_info_ptr = std::make_shared<TracksInfo>();

    // load header info
    Size num_view, num_pts, num_obs;

    tracks_file >> num_view >> num_pts >> num_obs;

    tracks_info_ptr->num_view = num_view;
    tracks_info_ptr->num_pts = num_pts;
    tracks_info_ptr->num_obs = num_obs;

    // load tracks
    int record_pts_id = -1;

    TrackInfo track_info;
    ObsInfo tmp_obs;

    tracks_info_ptr->tracks.reserve(num_pts);

    cout<<"min_tracking_length_="<<min_track_length<<endl;

    if (bearing_mode)
        cout<<"!! bearing mode open !!"<<endl;

    for (ObsId i = 0; i < num_obs; i++) {

        tracks_file >> tmp_obs.view_id
                >> tmp_obs.pts_id
                >> tmp_obs.coord(0)
                >> tmp_obs.coord(1);


        if (bearing_mode){

            tmp_obs.coord(0) = -tmp_obs.coord(0);
            tmp_obs.coord(1) = -tmp_obs.coord(1);
            tmp_obs.coord(2) = 1;
            tmp_obs.coord = tmp_obs.coord/tmp_obs.coord.norm();
        }
        else{
            tmp_obs.coord(0) = -tmp_obs.coord(0);
            tmp_obs.coord(1) = -tmp_obs.coord(1);
            tmp_obs.coord(2) = 1;

        }

        tmp_obs.is_used = true;
        tmp_obs.obs_id = i;

        if (tmp_obs.pts_id != record_pts_id) {
            if (i > 0) {
                if (track_info.track.size() < min_track_length) {
                    //only need use observation of tracking length >= MIN_TRACKING_LENGTH
                    track_info.is_used = 0;
                    for (auto& tmp_info : track_info.track){
                        tmp_info.is_used = false;
                    }
                } else {
                    track_info.is_used = 1;
                }
                tracks_info_ptr->tracks.emplace_back(track_info);
            }
            track_info.track.clear();
        }
        track_info.track.emplace_back(tmp_obs);
        record_pts_id = tmp_obs.pts_id;
    }
    tracks_file.close();
    tracks_info_ptr->tracks.emplace_back(track_info);

    // print time cost information
    cout << ">> time for loading tracks file: "
         << timer.Duration()
         << "s"
         << endl;
}

}


