#ifndef RELATIVE_RESIDUALS_H
#define RELATIVE_RESIDUALS_H

#pragma once
#include "povg_types.hpp"
#include <string>
#include <fstream>
#include <sstream>
#include <utility>
#include <iostream>
#include <memory>

namespace POVG
{

    typedef Matrix<double, 3, Dynamic> BearingVector;

    VectorXd residual_coplanar(const BearingVector &v1,
                               const BearingVector &v2,
                               const Pose &pose,
                               const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_sampson(const BearingVector &v1,
                              const BearingVector &v2,
                              const Pose &pose,
                              const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_Kneip(const BearingVector &v1,
                            const BearingVector &v2,
                            const Pose &pose,
                            const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_BA(const BearingVector &v1,
                         const BearingVector &v2,
                         const Pose &pose,
                         const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_BA(const BearingVector &v1,
                         const BearingVector &v2,
                         const Pose &pose,
                         const Matrix3d &Kmat,
                         const VectorXd *ptr_weights = nullptr);

    VectorXd residual_opengv(const BearingVector &v1,
                             const BearingVector &v2,
                             const Pose &pose,
                             Matrix<double, 3, Dynamic> &points3D,
                             const VectorXd *ptr_weights = nullptr);

    VectorXd residual_opengv(const BearingVector &v1,
                             const BearingVector &v2,
                             const Pose &pose,
                             const VectorXd *ptr_weights = nullptr);

    VectorXd residual_PPO(const BearingVector &v1,
                          const BearingVector &v2,
                          const Pose &pose,
                          const VectorXd *ptr_weigths = nullptr);
    VectorXd residual_PPOG(const BearingVector &v1,
                           const BearingVector &v2,
                           const Pose &pose,
                           const VectorXd *ptr_weigths = nullptr);
    VectorXd residual_PPO_invd(const BearingVector &v1,
                               const BearingVector &v2,
                               const Pose &pose,
                               const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_PPO_bvc_invd(const BearingVector &v1,
                                   const BearingVector &v2,
                                   const Pose &pose,
                                   const VectorXd *ptr_weigths = nullptr);
    VectorXd residual_PPO_cross(const BearingVector &v1,
                                const BearingVector &v2,
                                const Pose &pose,
                                const VectorXd *ptr_weigths = nullptr);
    VectorXd residual_PPO_bva_invd(const BearingVector &v1,
                                   const BearingVector &v2,
                                   const Pose &pose,
                                   const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT_direct(const BearingVector &v1,
                                  const BearingVector &v2,
                                  const Pose &pose,
                                  const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT_d3(const BearingVector &v1,
                              const BearingVector &v2,
                              const Pose &pose,
                              const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT(const BearingVector &v1,
                           const BearingVector &v2,
                           const Pose &pose,
                           const VectorXd *ptr_weigths = nullptr);

    VectorXd residual_LiGT_Lmat(const BearingVector &v1,
                                const BearingVector &v2,
                                const Pose &pose,
                                Matrix3d &Lmat);

    VectorXd residual_LiRT(const BearingVector &v1,
                           const BearingVector &v2,
                           const Pose &pose,
                           const VectorXd *ptr_weigths = nullptr);

    // ==================== LM优化器相关声明 ====================

    /**
     * @brief 基于Eigen LevenbergMarquardt的PPO优化器
     * @details 使用residual_PPO函数进行相对位姿的精细化优化
     */
    template <typename _Scalar, int NX = Eigen::Dynamic, int NY = Eigen::Dynamic>
    struct PPOLMFunctor
    {
        typedef _Scalar Scalar;
        enum
        {
            InputsAtCompileTime = NX,
            ValuesAtCompileTime = NY
        };
        typedef Eigen::Matrix<Scalar, InputsAtCompileTime, 1> InputType;
        typedef Eigen::Matrix<Scalar, ValuesAtCompileTime, 1> ValueType;
        typedef Eigen::Matrix<Scalar, ValuesAtCompileTime, InputsAtCompileTime> JacobianType;
        typedef Eigen::ColPivHouseholderQR<JacobianType> QRSolver;

        int m_inputs, m_values;

        // 构造函数
        PPOLMFunctor() : m_inputs(InputsAtCompileTime), m_values(ValuesAtCompileTime) {}
        PPOLMFunctor(int inputs, int values) : m_inputs(inputs), m_values(values) {}

        int inputs() const { return m_inputs; }
        int values() const { return m_values; }
    };

    /**
     * @brief PPO残差函数的LM优化器实现（支持Huber损失）
     */
    struct PPOResidualFunctor : PPOLMFunctor<double>
    {
        typedef double Scalar;
        typedef Eigen::Matrix<Scalar, Eigen::Dynamic, 1> InputType;
        typedef Eigen::Matrix<Scalar, Eigen::Dynamic, 1> ValueType;
        typedef Eigen::Matrix<Scalar, Eigen::Dynamic, Eigen::Dynamic> JacobianType;
        typedef Eigen::ColPivHouseholderQR<JacobianType> QRSolver;
        enum
        {
            InputsAtCompileTime = 6,
            ValuesAtCompileTime = Eigen::Dynamic
        };

        const BearingVector &v1_;
        const BearingVector &v2_;
        const VectorXd *weights_;
        double huber_delta_; // Huber损失函数的阈值参数
        bool use_huber_;     // 是否使用Huber损失函数

        PPOResidualFunctor(const BearingVector &v1, const BearingVector &v2,
                           const VectorXd *weights = nullptr,
                           bool use_huber = true,
                           double huber_delta = 1.345) // 默认使用95%效率的阈值
            : PPOLMFunctor<double>(6, v1.cols()), v1_(v1), v2_(v2), weights_(weights),
              huber_delta_(huber_delta), use_huber_(use_huber)
        {
        }

        // 计算残差函数（支持Huber损失）
        int operator()(const InputType &x, ValueType &fvec) const;

        // 计算雅可比矩阵（支持Huber损失）
        int df(const InputType &x, JacobianType &fjac) const;

    private:
        // Huber权重函数
        double huberWeight(double residual) const;

        // Huber伪残差函数
        double huberPseudoResidual(double residual) const;
    };

    /**
     * @brief 使用LM算法优化相对位姿
     * @param v1 第一个视图的bearing vectors
     * @param v2 第二个视图的bearing vectors
     * @param initial_pose 初始位姿估计
     * @param weights 权重向量（可选）
     * @param use_huber 是否使用Huber损失函数
     * @param huber_delta Huber损失函数的阈值参数
     * @return 优化后的位姿
     */
    Pose optimizePoseWithLM(const BearingVector &v1,
                            const BearingVector &v2,
                            const Pose &initial_pose,
                            const VectorXd *weights = nullptr,
                            bool use_huber = true,
                            double huber_delta = 1.345);

}

#endif // RELATIVE_RESIDUALS_H
