//#define EIGEN_USE_BLAS
//#define EIGEN_USE_LAPACKE
//#define ARMA_USE_BLAS
//#define ARMA_USE_NEWARP
//#define ARMA_USE_LAPACK
//#define EIGEN_USE_MKL_ALL
#define EIGEN_NO_DEBUG


#include <math.h>
#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include <type_traits>
#include <limits>
#include<iostream>
#include<fstream>
#include<string>
#include<iomanip>
#include<math.h>
#include <unordered_map>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <chrono>
#include <ceres/rotation.h>
#include <Eigen/Core>

#define IsNonZero(d) (fabs(d)>1e-15)
#define max(a, b) (a) < (b) ? (b): (a)
//#define ISDEBUG
DEFINE_string(bal_file,
              "/media/qicai/software/server_ali/tmp_save/dataset/Malaga/Malaga_global-on-0.5/ForPA_Malaga_global-on-0.5.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(output_file,
              "pa_residuals.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_double(baseline_angle_threshold,
              0.5,
              "Type of SfM reconstruction estimation to use.");

using namespace std;
using namespace chrono;
using namespace Eigen;

typedef unsigned int ViewId;
typedef unsigned int PtsId;
typedef unsigned int Size;
typedef SparseMatrix<double> spMatrix;

struct Obs_info{
    ViewId view_id;
    PtsId pts_id;
    Vector3d coord;
};

struct BaseFrames{
    double *base_s;
    ViewId *lbase_frames;
    ViewId *rbase_frames;
};


typedef vector<Obs_info> Track_data;
struct Track{
    Track_data track_data;
    bool is_used;
};

typedef vector<Track> Tracks;


typedef Matrix3d* Attitude;


typedef Eigen::SparseMatrix<double> EigenSpMat; // declares a column-major sparse matrix type of double
typedef Eigen::Triplet<double> T;

typedef vector<ViewId> Est_View;


typedef Vector3d* GTL_Translation;

struct Est_Info{
    Est_View est_view;
    ViewId* origin2est_id;
};


unsigned int num_view=0;
unsigned int num_pts=0;
unsigned int num_obs=0;
double time_use=0;

void load_tracks(const char* filename, Tracks& tracks, Est_Info &est_info);
void SetupBaseFrame(Tracks& tracks, Attitude& global_R, Est_Info& est_info, BaseFrames& base_frames);
void WriteBAL(const char* filename,Tracks& tracks,Attitude& orintations,GTL_Translation& translation);

void load_tracks(
        const char* filename,
        Tracks& tracks,
        Attitude& global_R,
        GTL_Translation& global_t)
{
//    auto start_time=steady_clock::now();

    //read match file
//    std::cout<<"bal_file is :"<<filename<<std::endl;
    fstream bal_file(filename,ios::in);
    if (!bal_file.is_open())
    {
        std::cout<<"match file cannot load"<<std::endl;
        return;
    }

    bal_file>>num_view>>num_pts>>num_obs;

//    cout<<"#view="<<num_view<<", Pts="<<num_pts<<", Obs="<<num_obs<<endl;

    int record_pts_id=-1;
    Track track;
    Obs_info tmp_obs;

    for (int i=0;i<num_obs;i++){
        bal_file>>tmp_obs.view_id>>tmp_obs.pts_id>>tmp_obs.coord(0)>>tmp_obs.coord(1);
        tmp_obs.coord(0)=-tmp_obs.coord(0);
        tmp_obs.coord(1)=-tmp_obs.coord(1);
        tmp_obs.coord(2)=1;

        if( tmp_obs.pts_id != record_pts_id)
        {
            if(i>0)
            {
                track.is_used=true;
                tracks.emplace_back(track);
            }
            track.track_data.clear();
        }
        track.track_data.emplace_back(tmp_obs);
        record_pts_id=tmp_obs.pts_id;
    }
    track.is_used=true;
    tracks.emplace_back(track);

    Eigen::Vector3d rot_angle;
    Eigen::Vector3d translation;
    Eigen::Vector3d tmp_camera_intrinsics;
    global_R=new Eigen::Matrix3d[num_view];
    global_t=new Eigen::Vector3d[num_view];

    for (int i = 0; i < num_view; i++) {
        bal_file>>rot_angle[0]>>rot_angle[1]>>rot_angle[2]
                >>translation[0]>>translation[1]>>translation[2]
                >>tmp_camera_intrinsics[0]>>tmp_camera_intrinsics[1]>>tmp_camera_intrinsics[2];
        global_t[i]=translation;
        ceres::AngleAxisToRotationMatrix(rot_angle.data(),global_R[i].data());
        //        cout<<"camera "<<i<<endl<<global_R[i]<<endl;
    }

//    auto end_time=steady_clock::now();
//    auto duration = duration_cast<microseconds>(end_time - start_time);
//    cout <<  "===time cost for load match file"
//          << double(duration.count()) * microseconds::period::num / microseconds::period::den
//          << "s" << endl;

}

void pa_objective_function(
        Tracks& tracks,
        Attitude& global_R,
        GTL_Translation& global_t,
        vector<Eigen::Vector2d>& residual){
//    auto start_time=steady_clock::now();
    Vector3d Rx;
    Vector3d xRx;
    Matrix3d R_lr;
    Vector3d t_lr;
    double s1,s2,s3;
    double baseline_angle_sin;

    unsigned int i=0;
    unsigned int j=0;
    unsigned int k=0;
    VectorXd::Index max_id_l;
    VectorXd::Index max_id_r;

    double max_s1=0;
    double max_s2=0;
    double max_s3=0;
    double max_baseline_angle_sin=0;

    Vector3d y;//debug
    double v_sum;//debug

    Vector3d tmp_residual;
    Vector2d reprojection_error;
    for(i=0;i<tracks.size();++i){
        Track_data &ptr_track=tracks[i].track_data;
        const unsigned int &track_length=ptr_track.size();
        max_id_l=0;
        max_id_r=0;
        max_s1=0;
        max_s2=0;
        max_s3=0;
        max_baseline_angle_sin=0;
        MatrixXd tmp_d2;
        tmp_d2.setZero(track_length,track_length);

        for(j=0;j<track_length-1;++j){
            const ViewId &left_view=ptr_track[j].view_id;
            const Vector3d &x_l=ptr_track[j].coord;

            Matrix3d & R_l=global_R[left_view];
            Vector3d & t_l=global_t[left_view];

            const Vector3d Rl_xl=R_l.transpose()*x_l;

            for(k=j+1;k<track_length;++k){
                const ViewId &right_view=ptr_track[k].view_id;
                Matrix3d & R_r=global_R[right_view];
                Vector3d & t_r=global_t[right_view];

                t_lr=t_r-R_r*(R_l.transpose()*t_l);
                s1=(t_lr.cross(ptr_track[k].coord)).norm();

                Rx=R_r*Rl_xl;
                xRx=ptr_track[k].coord.cross(Rx);
                s2=xRx.norm();

                tmp_d2(j,k)=s2;
                tmp_d2(k,j)=s2;
            }
        }

        tmp_d2.colwise().sum().maxCoeff(&max_id_l);
        tmp_d2.row(max_id_l).maxCoeff(&max_id_r);
        j=max_id_l;
        k=max_id_r;
        const ViewId &left_view=ptr_track[j].view_id;
        const Vector3d &x_l=ptr_track[j].coord;

        Matrix3d & R_l=global_R[left_view];
        Vector3d & t_l=global_t[left_view];

        const Vector3d Rl_xl=R_l.transpose()*x_l;

        const ViewId &right_view=ptr_track[k].view_id;
        Matrix3d & R_r=global_R[right_view];
        Vector3d & t_r=global_t[right_view];

        t_lr=t_r-R_r*(R_l.transpose()*t_l);
        s1=(t_lr.cross(ptr_track[k].coord)).norm();
        s2=tmp_d2(max_id_l,max_id_r);
        for(k=0;k<track_length;++k){
            if(k==j)continue;
            const ViewId &right_view=ptr_track[k].view_id;
            Matrix3d & R_r=global_R[right_view];
            Vector3d & t_r=global_t[right_view];
            t_lr=t_r-R_r*(R_l.transpose()*t_l);
            Rx=R_r*Rl_xl;
            y=s1*Rx+s2*t_lr;
            y=y/(y(2));

            tmp_residual=ptr_track[k].coord-y;
            reprojection_error(0)=tmp_residual(0);
            reprojection_error(1)=tmp_residual(1);
            v_sum+=pow(reprojection_error.norm(),2);
            residual.emplace_back(reprojection_error);
        }
    }
//    cout<<"residual is :"<<(v_sum)<<endl;
//    auto end_time=steady_clock::now();
//    auto duration = duration_cast<microseconds>(end_time - start_time);
//    cout <<  "===time cost for baseline_outlier_detect function:"
//          << double(duration.count()) * microseconds::period::num / microseconds::period::den
//          << "s" << endl;
}


void WriteResidual(
        const char* filename,
        vector<Eigen::Vector2d>& residual){
//    auto start_time=steady_clock::now();
    //export to output file (bal forms with normalized and undistorted points
    fstream f_output(filename,ios::out);
    if(!f_output.is_open()){
        cout<<"output file cannot create, please check path"<<endl;
        return;
    }
    unsigned tmp_pts_id=0;
    for( int i=0;i<residual.size();++i){
        f_output<<setprecision(16)<<residual[i]<<endl;
    }

    f_output.close();

//    auto end_time=steady_clock::now();
//    auto duration = duration_cast<microseconds>(end_time - start_time);
//    cout <<  "===time cost for WriteBAL function:"
//          << double(duration.count()) * microseconds::period::num / microseconds::period::den
//          << "s" << endl;
}

int main(int argc, char *argv[]) {
    google::ParseCommandLineFlags(&argc, &argv, true);
    Tracks tracks;
    Attitude global_R;
    GTL_Translation global_t;
    vector<Eigen::Vector2d> residual;
//    cout<<"##bal_baseline_outlier dectect..."<<endl;
    load_tracks(FLAGS_bal_file.c_str(),tracks,global_R,global_t);
    pa_objective_function(tracks,global_R,global_t,residual);
    WriteResidual(FLAGS_output_file.c_str(),residual);
    google::ShutDownCommandLineFlags();
    return 0;
}
