# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/ap_build/mex_cmake/src/mex -B/home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /home/<USER>/ap_build/mex_cmake/src/mex/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target LiGT


#############################################
# Order-only phony target for LiGT

build cmake_object_order_depends_target_LiGT: phony || LiGT/CMakeFiles/LiGT.dir

build LiGT/CMakeFiles/LiGT.dir/LiGT.o: CXX_COMPILER__LiGT_unscanned_Debug /home/<USER>/ap_build/mex_cmake/src/mex/LiGT/LiGT.cpp || cmake_object_order_depends_target_LiGT
  DEP_FILE = LiGT/CMakeFiles/LiGT.dir/LiGT.o.d
  FLAGS = -g -fdiagnostics-color=always
  INCLUDES = -I/home/<USER>/ap_build/mex_cmake/src/eigen3 -I/home/<USER>/spectra-master/include
  OBJECT_DIR = LiGT/CMakeFiles/LiGT.dir
  OBJECT_FILE_DIR = LiGT/CMakeFiles/LiGT.dir


# =============================================================================
# Link build statements for EXECUTABLE target LiGT


#############################################
# Link the executable LiGT/LiGT

build LiGT/LiGT: CXX_EXECUTABLE_LINKER__LiGT_Debug LiGT/CMakeFiles/LiGT.dir/LiGT.o | /usr/local/lib/Pose-only/libgflags.so /usr/local/lib/Pose-only/libstdc++.so.6
  FLAGS = -g
  LINK_FLAGS = -rdynamic
  LINK_LIBRARIES = -Wl,-rpath,/usr/local/lib/Pose-only  -lgflags  -lstdc++
  LINK_PATH = -L/usr/local/lib/Pose-only
  OBJECT_DIR = LiGT/CMakeFiles/LiGT.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = LiGT/LiGT
  TARGET_PDB = LiGT.dbg


#############################################
# Utility command for edit_cache

build LiGT/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug/LiGT && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build LiGT/edit_cache: phony LiGT/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build LiGT/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug/LiGT && /usr/bin/cmake --regenerate-during-build -S/home/<USER>/ap_build/mex_cmake/src/mex -B/home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build LiGT/rebuild_cache: phony LiGT/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build LiGT: phony LiGT/LiGT

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug

build all: phony LiGT/all

# =============================================================================

#############################################
# Folder: /home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug/LiGT

build LiGT/all: phony LiGT/LiGT

# =============================================================================
# Unknown Build Time Dependencies.
# Tell Ninja that they may appear as side effects of build rules
# otherwise ordered by order-only dependencies.

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc/package-manager/auto-setup.cmake /home/<USER>/ap_build/mex_cmake/src/mex/CMakeLists.txt /home/<USER>/ap_build/mex_cmake/src/mex/LiGT/CMakeLists.txt /usr/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCCompiler.cmake CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc/package-manager/auto-setup.cmake /home/<USER>/ap_build/mex_cmake/src/mex/CMakeLists.txt /home/<USER>/ap_build/mex_cmake/src/mex/LiGT/CMakeLists.txt /usr/share/cmake-3.28/Modules/CMakeCInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.28/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-C.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Compiler/GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-C.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.28/Modules/Platform/Linux-Initialize.cmake /usr/share/cmake-3.28/Modules/Platform/Linux.cmake /usr/share/cmake-3.28/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.28.3/CMakeCCompiler.cmake CMakeFiles/3.28.3/CMakeCXXCompiler.cmake CMakeFiles/3.28.3/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
