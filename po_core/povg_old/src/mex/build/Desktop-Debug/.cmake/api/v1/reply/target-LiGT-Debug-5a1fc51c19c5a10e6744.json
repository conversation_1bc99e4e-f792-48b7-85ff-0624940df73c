{"artifacts": [{"path": "LiGT/LiGT"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "INCLUDE_DIRECTORIES"], "files": ["LiGT/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 16, "parent": 0}, {"command": 1, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "includes": [{"backtrace": 2, "path": "/home/<USER>/ap_build/mex_cmake/src/eigen3"}, {"backtrace": 2, "path": "/home/<USER>/spectra-master/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "LiGT::@c8348162d94442adf4e7", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "-rdynamic", "role": "flags"}, {"fragment": "-L/usr/local/lib/Pose-only", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/usr/local/lib/Pose-only", "role": "libraries"}, {"fragment": "-lgflags", "role": "libraries"}, {"fragment": "-lstdc++", "role": "libraries"}], "language": "CXX"}, "name": "LiGT", "nameOnDisk": "LiGT", "paths": {"build": "LiGT", "source": "LiGT"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "LiGT/LiGT.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}