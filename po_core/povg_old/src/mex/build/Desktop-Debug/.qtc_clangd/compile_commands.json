[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-std=gnu++17", "-DQ_CREATOR_RUN", "-I/home/<USER>/ap_build/mex_cmake/src/eigen3", "-I/home/<USER>/spectra-master/include", "-isystem", "/usr/include/c++/13", "-isystem", "/usr/include/x86_64-linux-gnu/c++/13", "-isystem", "/usr/include/c++/13/backward", "-isystem", "/usr/local/include", "-isystem", "/usr/lib/llvm-18/lib/clang/18/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/ap_build/mex_cmake/src/mex/LiGT/LiGT.cpp"], "directory": "/home/<USER>/ap_build/mex_cmake/src/mex/build/Desktop-Debug/.qtc_clangd", "file": "/home/<USER>/ap_build/mex_cmake/src/mex/LiGT/LiGT.cpp"}]