The files

- `include/Spectra/LinAlg/TridiagEigen.h`
- `include/Spectra/LinAlg/UpperHessenbergEigen.h`

were adapted from

- `Eigen/src/Eigenvaleus/SelfAdjointEigenSolver.h`
- `Eigen/src/Eigenvaleus/EigenSolver.h`

in the [Eigen](http://eigen.tuxfamily.org/) library.

The authors for these two files were <PERSON><PERSON> <<EMAIL>>
and <PERSON><PERSON><PERSON> <<EMAIL>>.

The file `include/contrib/LOBPCGSolver.h` was originally contributed by <PERSON>.

The [Catch](https://github.com/philsquared/Catch) library included for unit testing
was written by <PERSON> <<EMAIL>>.

Other part of Spectra was written by <PERSON><PERSON><PERSON> <<EMAIL>>.
