//#define EIGEN_USE_BLAS
//#define EIGEN_USE_LAPACKE
//#define ARMA_USE_BLAS
//#define ARMA_USE_NEWARP
//#define ARMA_USE_LAPACK
//#define EIGEN_USE_MKL_ALL
#define EIGEN_NO_DEBUG


#include <math.h>
#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include <type_traits>
#include <limits>
#include<iostream>
#include<fstream>
#include<string>
#include<iomanip>
#include<math.h>
#include <unordered_map>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <chrono>
#include <set>
#include <Eigen/Core>
#include <Spectra/SymEigsShiftSolver.h>
// <Spectra/MatOp/DenseSymShiftSolve.h> is implicitly included
#include<Spectra/MatOp/SparseSymShiftSolve.h>


#define IsNonZero(d) (fabs(d)>1e-15)
#define T_size 3*(est_view_size)
#define max(a, b) (a) < (b) ? (b): (a)
//#define ISDEBUG
DEFINE_string(pts_file,
              "/media/qicai/software/server_ali/tmp_save/tmp_save/Usyd_mainquad/Usyd_mainquad_global-on-0.01/ForPA_Usyd_mainquad_global-on-0.01.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(match_file,
              "/media/qicai/0F7D0D870F7D0D87/SERVER/SERVER0907/lund_test/lund_results/Basilica_di_Santa_Maria_del_Fiore/matches/Basilica_di_Santa_Maria_del_Fiore_gtl.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(gR_file,
              "/media/qicai/0F7D0D870F7D0D87/SERVER/SERVER0907/lund_test/lund_results/Basilica_di_Santa_Maria_del_Fiore/matches/global_R_0.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(output_file,
              "output_file.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(time_file,
              "time_output_file.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_bool(issparse,
              true,
              "Type of SfM reconstruction estimation to use.");

using namespace std;
using namespace chrono;
using namespace Eigen;

using namespace Spectra;
typedef unsigned int ViewId;
typedef unsigned int PtsId;
typedef unsigned int Size;
typedef SparseMatrix<double> spMatrix;

struct Obs_info{
    ViewId view_id;
    PtsId pts_id;
    Vector3d coord;
};

struct BaseFrames{
    double *base_s;
    ViewId *lbase_frames;
    ViewId *rbase_frames;
};


typedef vector<Obs_info> Track;
struct Track_info{
    Track track;
    bool is_used=false;
};
bool compare_track_info(
        const Track_info& track_info1,
        const Track_info& track_info2){
    return track_info1.track.size()>track_info2.track.size();
}

typedef vector<Track_info> Tracks;


typedef Matrix3d* Attitude;


typedef Eigen::SparseMatrix<double> EigenSpMat; // declares a column-major sparse matrix type of double
typedef Eigen::Triplet<double> T;

typedef vector<ViewId> Est_View;


typedef Vector3d* GTL_Translation;

struct Est_Info{
    Est_View est_view;
    ViewId* origin2est_id;
};


int main(int argc, char *argv[]) {
    google::ParseCommandLineFlags(&argc, &argv, true);

    Tracks tracks,tracks2;

    Attitude global_R;
    GTL_Translation translation;
    Est_Info est_info,est_info2;
    SetupOrientation(FLAGS_gR_file.c_str(),global_R);
//    load_tracks(FLAGS_match_file.c_str(),tracks,est_info);
    load_tracks_2(FLAGS_match_file.c_str(),tracks2,est_info2);
//    GTL_algorithm(tracks,est_info,global_R,translation);
    GTL_algorithm(tracks2,est_info2,global_R,translation);

    WriteTranslation(FLAGS_output_file.c_str(),global_R,translation);
    WriteTime(FLAGS_time_file.c_str());

    google::ShutDownCommandLineFlags();
    return 0;
}
