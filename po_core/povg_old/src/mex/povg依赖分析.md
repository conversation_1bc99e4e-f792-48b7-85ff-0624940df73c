# POVG Dependencies

## Libraries

### povg_ligt_algorithm
- **Source Files**: 
  - `*.hpp`, `*.cpp` (GLOB_RECURSE)
- **Dependencies**:
  - l1homotopy_cpp
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}
  - ${SPECTRA_DIRS}
  - ${L1Homotopy_DIRS}

## Executables

### povg_relative_residuals
- **Source File**: `relative_residuals_main.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
  - ${GLOG_LIBRARY}
- **Include Directories**:
  - ${LIGT_DIRS}
  - ../six_point_algorithm
  - ../relative_residuals   

### povg_relative_identification
- **Source File**: `relative_identification_main.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
  - ${GLOG_LIBRARY}
- **Include Directories**:
  - ${LIGT_DIRS}
  - ../six_point_algorithm
  - ../relative_residuals

### povg_sixpt_algorithm
- **Source Files**: 
  - `sixpt_algorithm.cpp`
  - `OpticalFlow.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
  - ${GLOG_LIBRARY}
- **Include Directories**:
  - ${LIGT_DIRS}
  - ${SPECTRA_DIRS}
  - ${NPT_DIR}
  - ${SDPA_INCLUDE_DIRS}
  - ${OpenCV_INCLUDE_DIRS}
  - ${EIGEN3_INCLUDE_DIR}

### npt_algorithm
- **Source Files**: 
  - `npt_algorithm.cpp`
  - `/home/<USER>/npt-pose-master/src/npt_pose.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
  - ${NPT_LIB}
  - ${SDPA_LIBRARIES}
  - ${BLAS_LIBRARIES}
- **Include Directories**:
  - ${LIGT_DIRS}
  - ${SPECTRA_DIRS}
  - ${NPT_DIR}
  - ${SDPA_INCLUDE_DIRS}
  - ${OpenCV_INCLUDE_DIRS}
  - ${EIGEN3_INCLUDE_DIR}

### povg_anal3D
- **Source File**: `analytical_reconstruction.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
- **Include Directories**:
  - ${LIGT_DIRS}

### povg_bin_LiGT
- **Source File**: `povg_bin_LiGT.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
- **Include Directories**:
  - ${LIGT_DIRS}

### povg_bin_robust_LiGT
- **Source File**: `povg_bin_robust_LiGT.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
- **Include Directories**:
  - ${LIGT_DIRS}

### LiGT_modified
- **Source Files**: 
  - `*.hpp`, `*.cpp` (GLOB_RECURSE)
- **Dependencies**:
  - ${gflags_lib}
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}
  - ${SPECTRA_DIRS}

### LiGT
- **Source File**: `LiGT.cpp`
- **Dependencies**:
  - gflags
  - glog
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}
  - ${gflags_INCLUDE_DIRS}
  - ${GLOG_INCLUDE_DIR}
  - "/home/<USER>/spectra-master/include"

### test
- **Source File**: `test.cpp`
- **Dependencies**:
  - gflags
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}
  - ${SPECTRA_DIRS}

### direct
- **Source File**: `direct.cpp`
- **Dependencies**:
  - gflags
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}
  - ${SPECTRA_DIRS}

### CostFuncPA
- **Source File**: `pose_adjustment.cpp`
- **Dependencies**:
  - gflags
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}
  - ${SPECTRA_DIRS}
  - ${CERES_INCLUDE_DIRS}

### bal_outlier_detect
- **Source File**: `bal_outlier_detect.cpp`
- **Dependencies**:
  - povg_ligt_algorithm
  - ${GFLAGS_LIBRARIES}
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}
  - ${SPECTRA_DIRS}

### analytical_reconstruction
- **Source File**: `analytical_reconstruction.cpp`
- **Dependencies**:
  - gflags
- **Include Directories**:
  - ${EIGEN_INCLUDE_DIRS}