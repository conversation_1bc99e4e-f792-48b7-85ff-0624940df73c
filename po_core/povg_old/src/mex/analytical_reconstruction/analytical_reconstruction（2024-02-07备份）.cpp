#define EIGEN_NO_DEBUG

#include <math.h>
#include <eigen3/Eigen/Dense>
#include <eigen3/Eigen/Sparse>
#include <type_traits>
#include <limits>
#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>
#include <math.h>
#include <unordered_map>
#include <gflags/gflags.h>
#include <gflags/gflags_completions.h>
#include <chrono>
#include <set>
#include <Eigen/Core>

#define IsNonZero(d) (fabs(d) > 1e-15)
#define T_size 3 * (est_view_size)
#define max(a, b) (a) < (b) ? (b) : (a)
// #define ISDEBUG
DEFINE_string(pts_file,
              "pts_file",
              "Type of SfM reconstruction estimation to use.");

DEFINE_double(m3_ratio,
              0,
              "Type of SfM reconstruction estimation to use.");
DEFINE_int32(min_num_observations_per_point,
             2,
             "Type of SfM reconstruction estimation to use.");

DEFINE_string(tracks_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/Matches_10Image_1000Points_10.00Dt_0.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(robust_tracks_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/Matches_10Image_1000Points_10.00Dt_0.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(gR_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/global_R_1.txt",
              "Type of SfM reconstruction estimation to use.");
DEFINE_string(trans_file,
              "/home/<USER>/ap_build/mex_cmake/build-src-unknown-Debug/mex/analytical_reconstruction/global_t_1.txt",
              "Type of SfM reconstruction estimation to use.");

DEFINE_string(mode,
              "normal",
              "mode of SfM reconstruction estimation to use: normal, remove_reconstruction_outliers,robust_reconstruction");

using namespace std;
using namespace chrono;
using namespace Eigen;

typedef unsigned int ViewId;
typedef unsigned int PtsId;
typedef unsigned int Size;
typedef SparseMatrix<double> spMatrix;

struct Obs_info
{
    ViewId view_id;
    PtsId pts_id;
    Vector3d coord;
};

struct WorldPoint
{
    Vector3d world_pts;
    bool is_used = true;
};
typedef vector<WorldPoint> WorldPoints;

typedef vector<Obs_info> Track;
typedef vector<Track> Tracks;
typedef Matrix3d *Attitude;
typedef Vector3d *Trans;
typedef Vector3d *Pts_3d;

typedef Eigen::SparseMatrix<double> EigenSpMat; // declares a column-major sparse matrix type of double
typedef vector<ViewId> Est_View;

struct Est_Info
{
    Est_View est_view;
    ViewId *origin2est_id;
};

unsigned int num_view = 0;
unsigned int num_pts = 0;
unsigned int num_obs = 0;

double time_use = 0;

void load_tracks(const char *filename, Tracks &tracks, Est_Info &est_info);
void SetupOrientation(const char *filename, Attitude &global_R);
void SetupTrans(const char *filename, Trans &global_t);
void WritePts(const char *filename, Pts_3d &pts_3d);

typedef Vector3d *PtsCoor;

void analytical_reconstruction(
    Tracks &tracks,
    Attitude &global_R,
    Trans &Trans,
    Vector3d *pts_data)
{
    auto start_time = steady_clock::now();

    Vector3d Rx;
    Vector3d xRx;

    Size num_3dpts = tracks.size();

    double s;

    unsigned int i = 0;
    unsigned int j = 0;
    unsigned int k = 0;

    Vector3d Rl_xl, Rl_tl;
    Vector3d x_r;
    Vector3d tx_xr;
    Vector3d r_t;
    Vector3d cam_coor, world_coor;
    world_coor.setZero(3);
    double sum_s = 0;
    double norm_tx_xr;
    double depth;
    //    double pts_data[3*seg_length];
    for (i = 0; i < num_3dpts; ++i)
    {
        Track &ptr_track = tracks[i];
        const unsigned int &track_length = ptr_track.size();
        sum_s = 0;
        for (j = 0; j < track_length - 1; ++j)
        {
            const ViewId &left_view = ptr_track[j].view_id;
            const Vector3d &x_l = ptr_track[j].coord;
            const Matrix3d &R_l = global_R[left_view];
            const Vector3d &t_l = Trans[left_view];

            Rl_xl.noalias() = R_l.transpose() * x_l;
            Rl_tl.noalias() = R_l.transpose() * t_l;

            for (k = j + 1; k < track_length; ++k)
            {
                const ViewId &right_view = ptr_track[k].view_id;
                const Matrix3d &R_r = global_R[right_view];
                const Vector3d &t_r = Trans[right_view];
                const Vector3d &x_r = ptr_track[k].coord;
                r_t.noalias() = t_r - R_r * Rl_tl;
                // calculate s
                Rx.noalias() = R_r * Rl_xl;
                xRx.noalias() = x_r.cross(Rx);
                s = xRx.norm();
                sum_s += s;
                // calculate d1
                tx_xr.noalias() = r_t.cross(x_r);
                norm_tx_xr = tx_xr.norm();
                // calculate depth
                depth = norm_tx_xr / s;
                // obtain camera pts coor
                cam_coor.noalias() = x_l * depth;
                // obtain world pts coor
                world_coor.noalias() += R_l.transpose() * (cam_coor - t_l);
            }
        }
        pts_data[i] = world_coor;
    }
    //    memcpy(test,pts_data,sizeof(double)*pts_length);

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    double time_cost = double(duration.count()) * microseconds::period::num / microseconds::period::den;
    //    cout <<  "===time cost for analytical_reconstruction :"
    //          << time_cost
    //          << "s" << endl;
}

bool check_3dpt(
    Tracks &tracks,
    Attitude &global_R,
    const double &m3_ratio,
    const int &i)
{
    // auto start_time=steady_clock::now();

    Vector3d Rx;
    Vector3d xRx;

    double s;

    unsigned int j = 0;
    unsigned int k = 0;

    Vector3d Rl_xl, Rl_tl;
    Vector3d tx_xr;
    Vector3d r_t;
    Vector3d cam_coor;

    double max_s = 0;

    Track &ptr_track = tracks[i];
    const unsigned int &track_length = ptr_track.size();

    for (j = 0; j < track_length - 1; ++j)
    {
        const ViewId &left_view = ptr_track[j].view_id;
        const Vector3d &x_l = ptr_track[j].coord;
        const Matrix3d &R_l = global_R[left_view];

        Rl_xl.noalias() = R_l.transpose() * x_l;

        for (k = j + 1; k < track_length; ++k)
        {
            const ViewId &right_view = ptr_track[k].view_id;
            const Matrix3d &R_r = global_R[right_view];

            const Vector3d &x_r = ptr_track[k].coord;

            // calculate s
            Rx.noalias() = R_r * Rl_xl;
            xRx.noalias() = x_r.cross(Rx);
            s = xRx.norm();
            s = s / x_r.norm() / Rx.norm();

            max_s = (max_s < s) ? s : max_s;
        }
    }

    if (m3_ratio > max_s)
    {
        return false;
    }
    else
    {
        return true;
    }
}

void obtain_3dpt(
    Tracks &tracks,
    Attitude &global_R,
    Trans &Trans,
    Vector3d &world_coor,
    const int &i)
{
    // auto start_time=steady_clock::now();

    Vector3d Rx;
    Vector3d xRx;

    double s;

    unsigned int j = 0;
    unsigned int k = 0;

    Vector3d Rl_xl, Rl_tl;
    Vector3d tx_xr;
    Vector3d r_t;
    Vector3d cam_coor;
    world_coor.setZero(3);
    double sum_s = 0;
    double norm_tx_xr;
    double depth;
    double max_s = 0;
    double scale = 1;

    Track &ptr_track = tracks[i];
    const unsigned int &track_length = ptr_track.size();
    sum_s = 0;
    if (ptr_track.size() < FLAGS_min_num_observations_per_point)
    {
        world_coor.setZero(3);
        return;
    }
    for (j = 0; j < track_length - 1; ++j)
    {
        const ViewId &left_view = ptr_track[j].view_id;
        const Vector3d &x_l = ptr_track[j].coord;
        const Matrix3d &R_l = global_R[left_view];
        const Vector3d &t_l = Trans[left_view];

        Rl_xl.noalias() = R_l.transpose() * x_l;
        Rl_tl.noalias() = R_l.transpose() * t_l;

        for (k = j + 1; k < track_length; ++k)
        {
            const ViewId &right_view = ptr_track[k].view_id;
            const Matrix3d &R_r = global_R[right_view];
            const Vector3d &t_r = Trans[right_view];
            const Vector3d &x_r = ptr_track[k].coord;
            r_t.noalias() = t_r - R_r * Rl_tl;
            // calculate s
            Rx.noalias() = R_r * Rl_xl;
            xRx.noalias() = x_r.cross(Rx);
            s = xRx.norm();
            sum_s += s * s;
            // calculate d1
            tx_xr.noalias() = r_t.cross(x_r);
            norm_tx_xr = tx_xr.norm();
            //            //calculate depth
            //            s = (s<1e-8)?1e-8:s;
            //            depth=norm_tx_xr/s;
            //            //obtain camera pts coor
            //            cam_coor.noalias()=x_l*depth;
            //            //obtain world pts coor
            //            world_coor.noalias()+=s*R_l.transpose()*(cam_coor-t_l);

            // calculate depth
            s = s * scale;
            double tmp_s = s;
            max_s = (max_s < tmp_s) ? tmp_s : max_s;

            norm_tx_xr = norm_tx_xr * scale;
            depth = 1;
            // obtain camera pts coor
            cam_coor.noalias() = x_l * depth;
            // obtain world pts coor
            world_coor.noalias() += R_l.transpose() * s * (norm_tx_xr * cam_coor - s * t_l);
        }
    }

    if (FLAGS_m3_ratio > max_s)
    {
        //        cout << "FLAGS_m3_ratio=" << FLAGS_m3_ratio
        //             <<", pts " << i << " cannot be reconstructed!" << endl;
        world_coor.setZero(3);
    }
    else
    {
        //    world_coor=world_coor/sum_s;
        world_coor = world_coor / sum_s / scale;
        //    auto end_time=steady_clock::now();
        //    auto duration = duration_cast<microseconds>(end_time - start_time);
        //    double time_cost=double(duration.count()) * microseconds::period::num / microseconds::period::den;
        //    cout <<  "===time cost for analytical_reconstruction :"
        //          << time_cost
        //          << "s" << endl;
    }
}
void obtain_robust_3dpt(
    Tracks &tracks,
    Attitude &global_R,
    Trans &Trans,
    WorldPoint &world_point,
    const int &i)
{
    // auto start_time=steady_clock::now();

    Vector3d Rx;
    Vector3d xRx;

    double s;

    unsigned int j = 0;
    unsigned int k = 0;

    Vector3d Rl_xl, Rl_tl;
    Vector3d tx_xr;
    Vector3d r_t;
    Vector3d cam_coor;
    world_point.world_pts.setZero(3);
    double sum_s = 0;
    double norm_tx_xr;
    double depth;
    double max_s = 0;
    double scale = 1;

    Track &ptr_track = tracks[i];
    const unsigned int &track_length = ptr_track.size();
    sum_s = 0;

    if (ptr_track.size() < FLAGS_min_num_observations_per_point)
    {
        // do not reconstruct
        world_point.is_used = false;
        return;
    }
    for (j = 0; j < track_length - 1; ++j)
    {
        const ViewId &left_view = ptr_track[j].view_id;
        const Vector3d &x_l = ptr_track[j].coord;
        const Matrix3d &R_l = global_R[left_view];
        const Vector3d &t_l = Trans[left_view];

        Rl_xl.noalias() = R_l.transpose() * x_l;
        Rl_tl.noalias() = R_l.transpose() * t_l;

        for (k = j + 1; k < track_length; ++k)
        {
            const ViewId &right_view = ptr_track[k].view_id;
            const Matrix3d &R_r = global_R[right_view];
            const Vector3d &t_r = Trans[right_view];
            const Vector3d &x_r = ptr_track[k].coord;
            r_t.noalias() = t_r - R_r * Rl_tl;
            // calculate s
            Rx.noalias() = R_r * Rl_xl;
            xRx.noalias() = x_r.cross(Rx);
            s = xRx.norm();
            sum_s += s * s;
            // calculate d1
            tx_xr.noalias() = r_t.cross(x_r);
            norm_tx_xr = tx_xr.norm();

            // calculate depth
            s = s * scale;
            max_s = (max_s < s) ? s : max_s;

            norm_tx_xr = norm_tx_xr * scale;
            depth = 1;
            // obtain camera pts coor
            cam_coor.noalias() = x_l * depth;
            // obtain world pts coor
            world_point.world_pts.noalias() += R_l.transpose() * s * (norm_tx_xr * cam_coor - s * t_l);
        }
    }

    if (FLAGS_m3_ratio > max_s)
    {
        world_point.is_used = false;
    }
    else
    {
        //    world_coor=world_coor/sum_s;
        world_point.world_pts = world_point.world_pts / sum_s / scale;
        //    auto end_time=steady_clock::now();
        //    auto duration = duration_cast<microseconds>(end_time - start_time);
        //    double time_cost=double(duration.count()) * microseconds::period::num / microseconds::period::den;
        //    cout <<  "===time cost for analytical_reconstruction :"
        //          << time_cost
        //          << "s" << endl;
    }
}

void load_tracks(
    const char *filename,
    Tracks &tracks,
    Est_Info &est_info)
{
    auto start_time = steady_clock::now();

    // read match file
    // std::cout<<"match_file is :"<<filename<<std::endl;
    fstream match_file(filename, ios::in);
    if (!match_file.is_open())
    {
        // std::cout<<"match file cannot load"<<std::endl;
        return;
    }

    match_file >> num_view >> num_pts >> num_obs;

    // cout<<"#view="<<num_view<<", Pts="<<num_pts<<", Obs="<<num_obs<<endl;

    int record_pts_id = -1;
    Track track;
    Obs_info tmp_obs;
    est_info.origin2est_id = new ViewId[num_view];
    for (int i = 0; i < num_obs; i++)
    {
        match_file >> tmp_obs.view_id >> tmp_obs.pts_id >> tmp_obs.coord(0) >> tmp_obs.coord(1);
        tmp_obs.coord(0) = -tmp_obs.coord(0);
        tmp_obs.coord(1) = -tmp_obs.coord(1);
        tmp_obs.coord(2) = 1;

        if (find(est_info.est_view.begin(), est_info.est_view.end(), tmp_obs.view_id) == est_info.est_view.end())
        {
            est_info.origin2est_id[tmp_obs.view_id] = est_info.est_view.size();
            est_info.est_view.emplace_back(tmp_obs.view_id);
            // cout<<"inset img:"<<tmp_obs.view_id<<endl;
        }

        if (tmp_obs.pts_id != record_pts_id)
        {
            if (i > 0)
            {
                tracks.emplace_back(track);
            }
            track.clear();
        }
        track.emplace_back(tmp_obs);
        record_pts_id = tmp_obs.pts_id;
    }
    match_file.close();
    tracks.emplace_back(track);

    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    // cout <<  "===time cost for load match file"
    //       << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //       << "s" << endl;
}

void SetupOrientation(
    const char *filename,
    Attitude &global_R)
{

    auto start_time = steady_clock::now();
    // std::cout<<"##start load global R<<<"<<endl;
    fstream cq_in;
    cq_in.open(filename, std::ios::in);
    if (!cq_in.is_open())
    {
        cout << "gR_file cannot open" << endl;
        return;
    }
    // cout<<"global Rotation file name:"<<filename<<endl;
    int cq_i = 0;
    cq_in >> num_view;
    // cout<<">>global_R, num_view:"<<num_view<<endl;
    global_R = new Matrix3d[num_view];
    for (int cq_i = 0; cq_i < num_view; cq_i++)
    {
        Eigen::Matrix3d tmp_R;
        cq_in >> tmp_R(0, 0) >> tmp_R(1, 0) >> tmp_R(2, 0);
        cq_in >> tmp_R(0, 1) >> tmp_R(1, 1) >> tmp_R(2, 1);
        cq_in >> tmp_R(0, 2) >> tmp_R(1, 2) >> tmp_R(2, 2);

        // global_R.emplace_back(tmp_R);
        global_R[cq_i] = (tmp_R);
    }
    cq_in.close();
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    //    cout <<  "===time cost for setup global rotation"
    //          << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //          << "s" << endl;
}

void SetupTrans(
    const char *filename,
    Trans &global_t)
{

    auto start_time = steady_clock::now();
    //    std::cout<<"##start load global t<<<"<<endl;
    fstream cq_in;
    cq_in.open(filename, std::ios::in);
    if (!cq_in.is_open())
    {
        cout << "gt_file cannot open" << endl;
        return;
    }
    //    cout<<"global translation file name:"<<filename<<endl;
    int cq_i = 0;
    global_t = new Vector3d[num_view];
    //    cout<<">>load global t, num_view = "<<num_view<<endl;
    for (int cq_i = 0; cq_i < num_view; cq_i++)
    {
        Eigen::Vector3d tmp_t;
        cq_in >> tmp_t(0) >> tmp_t(1) >> tmp_t(2);
        global_t[cq_i] = (tmp_t);
        //        cout<<global_t[cq_i].transpose()<<endl;
    }
    cq_in.close();
    auto end_time = steady_clock::now();
    auto duration = duration_cast<microseconds>(end_time - start_time);
    //    cout <<  "===time cost for setup global translation"
    //          << double(duration.count()) * microseconds::period::num / microseconds::period::den
    //          << "s" << endl;
}

void WritePts(
    const char *filename,
    Pts_3d &pts_3d)
{
    fstream output(filename, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        cout << "output file cannot create, please check path" << endl;
        return;
    }
    //    cout<<"writting pts file..."<<endl;
    Vector3d *tmp_pts = NULL;
    unsigned int i = 0;

    for (i = 0; i < num_pts; ++i)
    {
        tmp_pts = &pts_3d[i];
        //        cout<<pts_3d[i].transpose()<<endl;
        output << setprecision(16) << (*tmp_pts)(0) << " " << (*tmp_pts)(1) << " " << (*tmp_pts)(2) << std::endl;
    }
    output.close();
}

void WritePtsWithID(
    const char *filename,
    Pts_3d &pts_3d)
{
    fstream output(filename, std::ios::out | ios::trunc);
    if (!output.is_open())
    {
        cout << "output file cannot create, please check path" << endl;
        return;
    }
    //    cout<<"writting pts file..."<<endl;
    Vector3d *tmp_pts = NULL;
    unsigned int i = 0;

    for (i = 0; i < num_pts; ++i)
    {
        tmp_pts = &pts_3d[i];
        //        cout<<pts_3d[i].transpose()<<endl;
        output << setprecision(16) << (*tmp_pts)(0) << " " << (*tmp_pts)(1) << " " << (*tmp_pts)(2) << std::endl;
    }
    output.close();
}

int main(int argc, char *argv[])
{
    if (argc < 4)
    {
        cout << "usage: " << "--tracks_file=? " << "--gR_file=? " << "--trans_file=?" << "--pts_file=?" << endl;
        cout << "tracks_file: tracks file supported by user or dataset" << endl;
        cout << "gR_file: global rotation file" << endl;
        cout << "trans_file: global rotation file" << endl;
        cout << "pts_file: output file for 3D points" << endl;
        return 1;
    }

    google::ParseCommandLineFlags(&argc, &argv, true);

    cout << "FLAGS_m3_ratio=" << FLAGS_m3_ratio << endl;
    if (FLAGS_mode == "normal")
    {
        Tracks tracks;
        Attitude global_R;
        Trans Trans;
        Est_Info est_info;
        Pts_3d pts;

        SetupOrientation(FLAGS_gR_file.c_str(), global_R);
        SetupTrans(FLAGS_trans_file.c_str(), Trans);
        load_tracks(FLAGS_tracks_file.c_str(), tracks, est_info);
        cout << "caculating 3dpts..., num_pts= " << num_pts << endl;
        pts = new Vector3d[num_pts];
        int num_recon_pts = 0;
        for (int i = 0; i < num_pts; ++i)
        {
            obtain_3dpt(tracks, global_R, Trans, pts[i], i);
            if (pts[i].norm() > 1e-10)
            {
                num_recon_pts++;
            }
        }
        std::cout << " num pts =  " << num_pts << ", num reconstructed pts1 = " << num_recon_pts << std::endl;

        //    analytical_reconstruction(tracks,global_R,Trans,pts);
        WritePts(FLAGS_pts_file.c_str(), pts);
    }
    else if (FLAGS_mode == "remove_reconstruction_outliers")
    {
        Tracks tracks;
        Attitude global_R;
        Est_Info est_info;
        SetupOrientation(FLAGS_gR_file.c_str(), global_R);
        load_tracks(FLAGS_tracks_file.c_str(), tracks, est_info);
        cout << "load tracks..., num_tracks= " << num_pts << endl;

        // check pts
        std::set<unsigned int> remain_view_id;
        int remain_views = 0;
        int remain_pts = 0;
        int remain_obs = 0;

        Tracks tracks_robust;
        vector<int> outlier_ids;
        for (int i = 0; i < num_pts; ++i)
        {
            auto &track = tracks[i];
            if (check_3dpt(tracks, global_R, FLAGS_m3_ratio, i))
            {
                for (int j = 0; j < track.size(); ++j)
                {
                    track[j].pts_id = remain_pts;
                    remain_obs++;
                    remain_view_id.insert(track[j].view_id);
                }
                remain_pts++;
                tracks_robust.emplace_back(track);
            }
            else
            {
                outlier_ids.emplace_back(i);
            }
        }
        remain_views = remain_view_id.size();

        cout << "rewrite tracks..., remain tracks= " << remain_pts << endl;

        ////        // start saving tracks
        ////        fstream tracks_file(FLAGS_robust_tracks_file,ios::out|ios::trunc);

        ////        // write header
        ////        tracks_file<< remain_views << " "
        ////                   << remain_pts << " "
        ////                   << remain_obs << std::endl;

        ////        for(int i = 0;i < remain_pts; ++i){
        ////            auto& track = tracks_robust[i];
        ////            for(int j = 0; j < track.size(); ++j){
        ////                tracks_file << track[j].view_id << " ";
        ////                tracks_file << track[j].pts_id << " ";
        ////                tracks_file << -track[j].coord(0) << " ";
        ////                tracks_file << -track[j].coord(1) << " ";
        ////                tracks_file<< std::endl;
        ////            }
        ////        }
        ////        tracks_file.close();

        // start saving tracks
        fstream outlier_file("/home/<USER>/arecon_outlier_ids.txt", ios::out | ios::trunc);
        for (int i = 0; i < outlier_ids.size(); ++i)
        {
            outlier_file << outlier_ids[i] << std::endl;
        }
        outlier_file.close();
    }
    else if (FLAGS_mode == "a")
    {
    }

    google::ShutDownCommandLineFlags();
    return 0;
}
