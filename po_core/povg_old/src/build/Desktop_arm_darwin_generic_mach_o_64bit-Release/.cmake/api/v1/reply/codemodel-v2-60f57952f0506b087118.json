{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Release-231fb992b70fca842966.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [1]}, {"build": "mex", "childIndexes": [2], "hasInstallRule": true, "jsonFile": "directory-mex-Release-6d0c13e12deb323540b1.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "mex"}, {"build": "mex/LiGT_algorithm", "childIndexes": [3, 4], "hasInstallRule": true, "jsonFile": "directory-mex.LiGT_algorithm-Release-35fcb30c70d95ec11555.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 1, "projectIndex": 0, "source": "mex/LiGT_algorithm"}, {"build": "mex/LiGT_algorithm/LiGT", "hasInstallRule": true, "jsonFile": "directory-mex.LiGT_algorithm.LiGT-Release-c8edf1c6a76190ccaffd.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 0, "source": "mex/LiGT_algorithm/LiGT", "targetIndexes": [0]}, {"build": "mex/LiGT_algorithm/six_point_algorithm", "jsonFile": "directory-mex.LiGT_algorithm.six_point_algorithm-Release-a3526c93f392dafc3afa.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 2, "projectIndex": 0, "source": "mex/LiGT_algorithm/six_point_algorithm", "targetIndexes": [2]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4], "name": "povg_old_lib", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 3, "id": "povg_ligt_algorithm::@00f5ba032a321ec38856", "jsonFile": "target-povg_ligt_algorithm-Release-ab4e58e9003ce9fd4d38.json", "name": "povg_ligt_algorithm", "projectIndex": 0}, {"directoryIndex": 0, "id": "povg_old_lib::@6890427a1f51a3e7e1df", "jsonFile": "target-povg_old_lib-Release-8b848f13d4fa3698e873.json", "name": "povg_old_lib", "projectIndex": 0}, {"directoryIndex": 4, "id": "povg_sixpt_algorithm::@6bd890efafee78942f1b", "jsonFile": "target-povg_sixpt_algorithm-Release-89e73f2ed2bd5ae12451.json", "name": "povg_sixpt_algorithm", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release", "source": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src"}, "version": {"major": 2, "minor": 7}}