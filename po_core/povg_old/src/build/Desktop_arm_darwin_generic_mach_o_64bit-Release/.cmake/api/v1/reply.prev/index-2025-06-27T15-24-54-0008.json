{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/Cellar/cmake/3.31.4/bin/cmake", "cpack": "/opt/homebrew/Cellar/cmake/3.31.4/bin/cpack", "ctest": "/opt/homebrew/Cellar/cmake/3.31.4/bin/ctest", "root": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 4, "string": "3.31.4", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-60f57952f0506b087118.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-82e39197bd7ef7f88dae.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-968cd499b3cec71283b4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-82e39197bd7ef7f88dae.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-968cd499b3cec71283b4.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-60f57952f0506b087118.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}