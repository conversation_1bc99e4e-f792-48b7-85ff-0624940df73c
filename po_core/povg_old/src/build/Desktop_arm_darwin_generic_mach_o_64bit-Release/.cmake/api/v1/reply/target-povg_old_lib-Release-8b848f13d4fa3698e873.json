{"archive": {}, "artifacts": [{"path": "lib/libpovg_old_lib.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_compile_options", "target_link_libraries", "target_compile_definitions", "target_include_directories", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 140, "parent": 0}, {"command": 1, "file": 0, "line": 241, "parent": 0}, {"command": 2, "file": 0, "line": 228, "parent": 0}, {"command": 3, "file": 0, "line": 209, "parent": 0}, {"command": 4, "file": 0, "line": 220, "parent": 0}, {"command": 4, "file": 0, "line": 224, "parent": 0}, {"command": 5, "file": 0, "line": 152, "parent": 0}, {"command": 6, "file": 0, "line": 121, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -fcolor-diagnostics"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-O3"}], "defines": [{"backtrace": 4, "define": "GFLAGS_IS_A_DLL=0"}, {"backtrace": 4, "define": "GLOG_CUSTOM_PREFIX_SUPPORT"}, {"backtrace": 5, "define": "HAVE_GFLAGS"}, {"backtrace": 6, "define": "HAVE_GLOG"}], "frameworks": [{"path": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/Accelerate.framework"}], "includes": [{"backtrace": 7, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src"}, {"backtrace": 7, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT"}, {"backtrace": 7, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm"}, {"backtrace": 7, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include"}, {"backtrace": 8, "isSystem": true, "path": "/opt/homebrew/include/eigen3"}, {"backtrace": 4, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "id": "povg_old_lib::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src"}}, "name": "povg_old_lib", "nameOnDisk": "libpovg_old_lib.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/povg_tracks.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/povg_types.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/relative_residuals.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/robust_LiGT.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm_strecha.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}