{"artifacts": [{"path": "Darwin-arm64-Release/povg_sixpt_algorithm"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "target_compile_options", "include_directories", "INCLUDE_DIRECTORIES"], "files": ["mex/LiGT_algorithm/six_point_algorithm/CMakeLists.txt", "/opt/homebrew/lib/cmake/glog/glog-targets.cmake", "/opt/homebrew/lib/cmake/glog/glog-config.cmake", "cmake/FindGlog.cmake", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 38, "parent": 0}, {"command": 1, "file": 0, "line": 40, "parent": 0}, {"command": 1, "file": 0, "line": 48, "parent": 0}, {"file": 4}, {"command": 4, "file": 4, "line": 116, "parent": 4}, {"file": 3, "parent": 5}, {"command": 4, "file": 3, "line": 177, "parent": 6}, {"file": 2, "parent": 7}, {"command": 3, "file": 2, "line": 27, "parent": 8}, {"file": 1, "parent": 9}, {"command": 2, "file": 1, "line": 61, "parent": 10}, {"command": 5, "file": 0, "line": 57, "parent": 0}, {"command": 6, "file": 4, "line": 121, "parent": 4}, {"command": 7, "file": 0, "line": 4, "parent": 0}, {"command": 6, "file": 0, "line": 16, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fcolor-diagnostics"}, {"backtrace": 12, "fragment": "-Wall"}, {"backtrace": 12, "fragment": "-fPIC"}, {"backtrace": 12, "fragment": "-funroll-all-loops"}], "defines": [{"backtrace": 2, "define": "GFLAGS_IS_A_DLL=0"}, {"backtrace": 2, "define": "GLOG_CUSTOM_PREFIX_SUPPORT"}], "includes": [{"backtrace": 13, "path": "/opt/homebrew/include/eigen3"}, {"backtrace": 14, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT"}, {"backtrace": 14, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include"}, {"backtrace": 15, "isSystem": true, "path": "/opt/homebrew/Cellar/opencv/4.11.0/include/opencv4"}, {"backtrace": 2, "isSystem": true, "path": "/opt/homebrew/include"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "17"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 2, "id": "povg_ligt_algorithm::@00f5ba032a321ec38856"}], "id": "povg_sixpt_algorithm::@6bd890efafee78942f1b", "link": {"commandFragments": [{"fragment": "-Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/opt/homebrew/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "Darwin-arm64-Release/libpovg_ligt_algorithm.a", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_gapi.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_stitching.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_alphamat.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_aruco.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_bgsegm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_bioinspired.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_ccalib.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_dnn_objdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_dnn_superres.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_dpm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_face.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_freetype.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_fuzzy.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_hfs.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_img_hash.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_intensity_transform.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_line_descriptor.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_mcc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_quality.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_rapid.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_reg.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_rgbd.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_saliency.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_sfm.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_signal.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_stereo.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_structured_light.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_superres.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_surface_matching.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_tracking.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_videostab.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_viz.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_wechat_qrcode.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_xfeatures2d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_xobjdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_xphoto.4.11.0.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libgflags.2.2.2.dylib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/homebrew/lib/libglog.0.6.0.dylib", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/homebrew/lib/libgflags.2.2.2.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_shape.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_highgui.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_datasets.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_plot.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_text.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_ml.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_phase_unwrapping.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_optflow.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_ximgproc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_video.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_videoio.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_imgcodecs.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_objdetect.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_calib3d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_dnn.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_features2d.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_flann.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_photo.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_imgproc.4.11.0.dylib", "role": "libraries"}, {"backtrace": 3, "fragment": "/opt/homebrew/lib/libopencv_core.4.11.0.dylib", "role": "libraries"}], "language": "CXX"}, "name": "povg_sixpt_algorithm", "nameOnDisk": "povg_sixpt_algorithm", "paths": {"build": "mex/LiGT_algorithm/six_point_algorithm", "source": "mex/LiGT_algorithm/six_point_algorithm"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm_strecha.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}