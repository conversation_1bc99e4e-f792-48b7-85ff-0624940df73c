# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: povg_old_lib
# Configurations: Release
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Release
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/
# =============================================================================
# Object build statements for STATIC_LIBRARY target povg_old_lib


#############################################
# Order-only phony target for povg_old_lib

build cmake_object_order_depends_target_povg_old_lib: phony || .

build CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp.o: CXX_COMPILER__povg_old_lib_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp || cmake_object_order_depends_target_povg_old_lib
  DEFINES = -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DHAVE_GFLAGS -DHAVE_GLOG
  DEP_FILE = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -fcolor-diagnostics -Wall -Wextra -O3
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -F/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = CMakeFiles/povg_old_lib.dir
  OBJECT_FILE_DIR = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT
  TARGET_COMPILE_PDB = CMakeFiles/povg_old_lib.dir/povg_old_lib.pdb
  TARGET_PDB = lib/libpovg_old_lib.pdb

build CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/povg_tracks.cpp.o: CXX_COMPILER__povg_old_lib_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.cpp || cmake_object_order_depends_target_povg_old_lib
  DEFINES = -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DHAVE_GFLAGS -DHAVE_GLOG
  DEP_FILE = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/povg_tracks.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -fcolor-diagnostics -Wall -Wextra -O3
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -F/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = CMakeFiles/povg_old_lib.dir
  OBJECT_FILE_DIR = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT
  TARGET_COMPILE_PDB = CMakeFiles/povg_old_lib.dir/povg_old_lib.pdb
  TARGET_PDB = lib/libpovg_old_lib.pdb

build CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/povg_types.cpp.o: CXX_COMPILER__povg_old_lib_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.cpp || cmake_object_order_depends_target_povg_old_lib
  DEFINES = -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DHAVE_GFLAGS -DHAVE_GLOG
  DEP_FILE = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/povg_types.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -fcolor-diagnostics -Wall -Wextra -O3
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -F/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = CMakeFiles/povg_old_lib.dir
  OBJECT_FILE_DIR = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT
  TARGET_COMPILE_PDB = CMakeFiles/povg_old_lib.dir/povg_old_lib.pdb
  TARGET_PDB = lib/libpovg_old_lib.pdb

build CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/relative_residuals.cpp.o: CXX_COMPILER__povg_old_lib_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.cpp || cmake_object_order_depends_target_povg_old_lib
  DEFINES = -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DHAVE_GFLAGS -DHAVE_GLOG
  DEP_FILE = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/relative_residuals.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -fcolor-diagnostics -Wall -Wextra -O3
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -F/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = CMakeFiles/povg_old_lib.dir
  OBJECT_FILE_DIR = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT
  TARGET_COMPILE_PDB = CMakeFiles/povg_old_lib.dir/povg_old_lib.pdb
  TARGET_PDB = lib/libpovg_old_lib.pdb

build CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp.o: CXX_COMPILER__povg_old_lib_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp || cmake_object_order_depends_target_povg_old_lib
  DEFINES = -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DHAVE_GFLAGS -DHAVE_GLOG
  DEP_FILE = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -fcolor-diagnostics -Wall -Wextra -O3
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -F/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = CMakeFiles/povg_old_lib.dir
  OBJECT_FILE_DIR = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT
  TARGET_COMPILE_PDB = CMakeFiles/povg_old_lib.dir/povg_old_lib.pdb
  TARGET_PDB = lib/libpovg_old_lib.pdb

build CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm_strecha.cpp.o: CXX_COMPILER__povg_old_lib_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm_strecha.cpp || cmake_object_order_depends_target_povg_old_lib
  DEFINES = -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT -DHAVE_GFLAGS -DHAVE_GLOG
  DEP_FILE = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm_strecha.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -fcolor-diagnostics -Wall -Wextra -O3
  INCLUDES = -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -F/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks -isystem /opt/homebrew/include/eigen3 -isystem /opt/homebrew/include
  OBJECT_DIR = CMakeFiles/povg_old_lib.dir
  OBJECT_FILE_DIR = CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/six_point_algorithm
  TARGET_COMPILE_PDB = CMakeFiles/povg_old_lib.dir/povg_old_lib.pdb
  TARGET_PDB = lib/libpovg_old_lib.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target povg_old_lib


#############################################
# Link the static library lib/libpovg_old_lib.a

build lib/libpovg_old_lib.a: CXX_STATIC_LIBRARY_LINKER__povg_old_lib_Release CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp.o CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/povg_tracks.cpp.o CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/povg_types.cpp.o CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/relative_residuals.cpp.o CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp.o CMakeFiles/povg_old_lib.dir/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm_strecha.cpp.o
  ARCH_FLAGS = -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15
  LANGUAGE_COMPILE_FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG
  OBJECT_DIR = CMakeFiles/povg_old_lib.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = CMakeFiles/povg_old_lib.dir/povg_old_lib.pdb
  TARGET_FILE = lib/libpovg_old_lib.a
  TARGET_PDB = lib/libpovg_old_lib.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release && /opt/homebrew/Cellar/cmake/3.31.4/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build mex/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex && /opt/homebrew/Cellar/cmake/3.31.4/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build mex/edit_cache: phony mex/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build mex/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build mex/rebuild_cache: phony mex/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build mex/list_install_components: phony


#############################################
# Utility command for install

build mex/CMakeFiles/install.util: CUSTOM_COMMAND mex/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build mex/install: phony mex/CMakeFiles/install.util


#############################################
# Utility command for install/local

build mex/CMakeFiles/install/local.util: CUSTOM_COMMAND mex/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build mex/install/local: phony mex/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build mex/CMakeFiles/install/strip.util: CUSTOM_COMMAND mex/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build mex/install/strip: phony mex/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build mex/LiGT_algorithm/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm && /opt/homebrew/Cellar/cmake/3.31.4/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build mex/LiGT_algorithm/edit_cache: phony mex/LiGT_algorithm/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build mex/LiGT_algorithm/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build mex/LiGT_algorithm/rebuild_cache: phony mex/LiGT_algorithm/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build mex/LiGT_algorithm/list_install_components: phony


#############################################
# Utility command for install

build mex/LiGT_algorithm/CMakeFiles/install.util: CUSTOM_COMMAND mex/LiGT_algorithm/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build mex/LiGT_algorithm/install: phony mex/LiGT_algorithm/CMakeFiles/install.util


#############################################
# Utility command for install/local

build mex/LiGT_algorithm/CMakeFiles/install/local.util: CUSTOM_COMMAND mex/LiGT_algorithm/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build mex/LiGT_algorithm/install/local: phony mex/LiGT_algorithm/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build mex/LiGT_algorithm/CMakeFiles/install/strip.util: CUSTOM_COMMAND mex/LiGT_algorithm/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build mex/LiGT_algorithm/install/strip: phony mex/LiGT_algorithm/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target povg_ligt_algorithm


#############################################
# Order-only phony target for povg_ligt_algorithm

build cmake_object_order_depends_target_povg_ligt_algorithm: phony || .

build mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/LiGT_algorithm.cpp.o: CXX_COMPILER__povg_ligt_algorithm_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp || cmake_object_order_depends_target_povg_ligt_algorithm
  DEP_FILE = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/LiGT_algorithm.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics
  INCLUDES = -I/opt/homebrew/include/eigen3 -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT
  OBJECT_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  OBJECT_FILE_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_ligt_algorithm.pdb
  TARGET_PDB = Darwin-arm64-Release/libpovg_ligt_algorithm.pdb

build mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_tracks.cpp.o: CXX_COMPILER__povg_ligt_algorithm_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.cpp || cmake_object_order_depends_target_povg_ligt_algorithm
  DEP_FILE = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_tracks.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics
  INCLUDES = -I/opt/homebrew/include/eigen3 -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT
  OBJECT_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  OBJECT_FILE_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_ligt_algorithm.pdb
  TARGET_PDB = Darwin-arm64-Release/libpovg_ligt_algorithm.pdb

build mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_types.cpp.o: CXX_COMPILER__povg_ligt_algorithm_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.cpp || cmake_object_order_depends_target_povg_ligt_algorithm
  DEP_FILE = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_types.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics
  INCLUDES = -I/opt/homebrew/include/eigen3 -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT
  OBJECT_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  OBJECT_FILE_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_ligt_algorithm.pdb
  TARGET_PDB = Darwin-arm64-Release/libpovg_ligt_algorithm.pdb

build mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/relative_residuals.cpp.o: CXX_COMPILER__povg_ligt_algorithm_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.cpp || cmake_object_order_depends_target_povg_ligt_algorithm
  DEP_FILE = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/relative_residuals.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics
  INCLUDES = -I/opt/homebrew/include/eigen3 -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT
  OBJECT_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  OBJECT_FILE_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_ligt_algorithm.pdb
  TARGET_PDB = Darwin-arm64-Release/libpovg_ligt_algorithm.pdb

build mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/robust_LiGT.cpp.o: CXX_COMPILER__povg_ligt_algorithm_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp || cmake_object_order_depends_target_povg_ligt_algorithm
  DEP_FILE = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/robust_LiGT.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics
  INCLUDES = -I/opt/homebrew/include/eigen3 -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT
  OBJECT_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  OBJECT_FILE_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_ligt_algorithm.pdb
  TARGET_PDB = Darwin-arm64-Release/libpovg_ligt_algorithm.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target povg_ligt_algorithm


#############################################
# Link the static library Darwin-arm64-Release/libpovg_ligt_algorithm.a

build Darwin-arm64-Release/libpovg_ligt_algorithm.a: CXX_STATIC_LIBRARY_LINKER__povg_ligt_algorithm_Release mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/LiGT_algorithm.cpp.o mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_tracks.cpp.o mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_types.cpp.o mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/relative_residuals.cpp.o mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/robust_LiGT.cpp.o
  ARCH_FLAGS = -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  LANGUAGE_COMPILE_FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG
  OBJECT_DIR = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/LiGT/CMakeFiles/povg_ligt_algorithm.dir/povg_ligt_algorithm.pdb
  TARGET_FILE = Darwin-arm64-Release/libpovg_ligt_algorithm.a
  TARGET_PDB = Darwin-arm64-Release/libpovg_ligt_algorithm.pdb


#############################################
# Utility command for edit_cache

build mex/LiGT_algorithm/LiGT/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/LiGT && /opt/homebrew/Cellar/cmake/3.31.4/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build mex/LiGT_algorithm/LiGT/edit_cache: phony mex/LiGT_algorithm/LiGT/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build mex/LiGT_algorithm/LiGT/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/LiGT && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build mex/LiGT_algorithm/LiGT/rebuild_cache: phony mex/LiGT_algorithm/LiGT/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build mex/LiGT_algorithm/LiGT/list_install_components: phony


#############################################
# Utility command for install

build mex/LiGT_algorithm/LiGT/CMakeFiles/install.util: CUSTOM_COMMAND mex/LiGT_algorithm/LiGT/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/LiGT && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build mex/LiGT_algorithm/LiGT/install: phony mex/LiGT_algorithm/LiGT/CMakeFiles/install.util


#############################################
# Utility command for install/local

build mex/LiGT_algorithm/LiGT/CMakeFiles/install/local.util: CUSTOM_COMMAND mex/LiGT_algorithm/LiGT/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/LiGT && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build mex/LiGT_algorithm/LiGT/install/local: phony mex/LiGT_algorithm/LiGT/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build mex/LiGT_algorithm/LiGT/CMakeFiles/install/strip.util: CUSTOM_COMMAND mex/LiGT_algorithm/LiGT/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/LiGT && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build mex/LiGT_algorithm/LiGT/install/strip: phony mex/LiGT_algorithm/LiGT/CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for EXECUTABLE target povg_sixpt_algorithm


#############################################
# Order-only phony target for povg_sixpt_algorithm

build cmake_object_order_depends_target_povg_sixpt_algorithm: phony || cmake_object_order_depends_target_povg_ligt_algorithm

build mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir/sixpt_algorithm_strecha.cpp.o: CXX_COMPILER__povg_sixpt_algorithm_unscanned_Release /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm_strecha.cpp || cmake_object_order_depends_target_povg_sixpt_algorithm
  DEFINES = -DGFLAGS_IS_A_DLL=0 -DGLOG_CUSTOM_PREFIX_SUPPORT
  DEP_FILE = mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir/sixpt_algorithm_strecha.cpp.o.d
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fcolor-diagnostics -Wall -fPIC -funroll-all-loops
  INCLUDES = -I/opt/homebrew/include/eigen3 -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT -I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include -isystem /opt/homebrew/Cellar/opencv/4.11.0/include/opencv4 -isystem /opt/homebrew/include
  OBJECT_DIR = mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir
  OBJECT_FILE_DIR = mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir/
  TARGET_PDB = Darwin-arm64-Release/povg_sixpt_algorithm.pdb


# =============================================================================
# Link build statements for EXECUTABLE target povg_sixpt_algorithm


#############################################
# Link the executable Darwin-arm64-Release/povg_sixpt_algorithm

build Darwin-arm64-Release/povg_sixpt_algorithm: CXX_EXECUTABLE_LINKER__povg_sixpt_algorithm_Release mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir/sixpt_algorithm_strecha.cpp.o | Darwin-arm64-Release/libpovg_ligt_algorithm.a /opt/homebrew/lib/libopencv_gapi.4.11.0.dylib /opt/homebrew/lib/libopencv_stitching.4.11.0.dylib /opt/homebrew/lib/libopencv_alphamat.4.11.0.dylib /opt/homebrew/lib/libopencv_aruco.4.11.0.dylib /opt/homebrew/lib/libopencv_bgsegm.4.11.0.dylib /opt/homebrew/lib/libopencv_bioinspired.4.11.0.dylib /opt/homebrew/lib/libopencv_ccalib.4.11.0.dylib /opt/homebrew/lib/libopencv_dnn_objdetect.4.11.0.dylib /opt/homebrew/lib/libopencv_dnn_superres.4.11.0.dylib /opt/homebrew/lib/libopencv_dpm.4.11.0.dylib /opt/homebrew/lib/libopencv_face.4.11.0.dylib /opt/homebrew/lib/libopencv_freetype.4.11.0.dylib /opt/homebrew/lib/libopencv_fuzzy.4.11.0.dylib /opt/homebrew/lib/libopencv_hfs.4.11.0.dylib /opt/homebrew/lib/libopencv_img_hash.4.11.0.dylib /opt/homebrew/lib/libopencv_intensity_transform.4.11.0.dylib /opt/homebrew/lib/libopencv_line_descriptor.4.11.0.dylib /opt/homebrew/lib/libopencv_mcc.4.11.0.dylib /opt/homebrew/lib/libopencv_quality.4.11.0.dylib /opt/homebrew/lib/libopencv_rapid.4.11.0.dylib /opt/homebrew/lib/libopencv_reg.4.11.0.dylib /opt/homebrew/lib/libopencv_rgbd.4.11.0.dylib /opt/homebrew/lib/libopencv_saliency.4.11.0.dylib /opt/homebrew/lib/libopencv_sfm.4.11.0.dylib /opt/homebrew/lib/libopencv_signal.4.11.0.dylib /opt/homebrew/lib/libopencv_stereo.4.11.0.dylib /opt/homebrew/lib/libopencv_structured_light.4.11.0.dylib /opt/homebrew/lib/libopencv_superres.4.11.0.dylib /opt/homebrew/lib/libopencv_surface_matching.4.11.0.dylib /opt/homebrew/lib/libopencv_tracking.4.11.0.dylib /opt/homebrew/lib/libopencv_videostab.4.11.0.dylib /opt/homebrew/lib/libopencv_viz.4.11.0.dylib /opt/homebrew/lib/libopencv_wechat_qrcode.4.11.0.dylib /opt/homebrew/lib/libopencv_xfeatures2d.4.11.0.dylib /opt/homebrew/lib/libopencv_xobjdetect.4.11.0.dylib /opt/homebrew/lib/libopencv_xphoto.4.11.0.dylib /opt/homebrew/lib/libgflags.2.2.2.dylib /opt/homebrew/lib/libglog.0.6.0.dylib /opt/homebrew/lib/libgflags.2.2.2.dylib /opt/homebrew/lib/libopencv_shape.4.11.0.dylib /opt/homebrew/lib/libopencv_highgui.4.11.0.dylib /opt/homebrew/lib/libopencv_datasets.4.11.0.dylib /opt/homebrew/lib/libopencv_plot.4.11.0.dylib /opt/homebrew/lib/libopencv_text.4.11.0.dylib /opt/homebrew/lib/libopencv_ml.4.11.0.dylib /opt/homebrew/lib/libopencv_phase_unwrapping.4.11.0.dylib /opt/homebrew/lib/libopencv_optflow.4.11.0.dylib /opt/homebrew/lib/libopencv_ximgproc.4.11.0.dylib /opt/homebrew/lib/libopencv_video.4.11.0.dylib /opt/homebrew/lib/libopencv_videoio.4.11.0.dylib /opt/homebrew/lib/libopencv_imgcodecs.4.11.0.dylib /opt/homebrew/lib/libopencv_objdetect.4.11.0.dylib /opt/homebrew/lib/libopencv_calib3d.4.11.0.dylib /opt/homebrew/lib/libopencv_dnn.4.11.0.dylib /opt/homebrew/lib/libopencv_features2d.4.11.0.dylib /opt/homebrew/lib/libopencv_flann.4.11.0.dylib /opt/homebrew/lib/libopencv_photo.4.11.0.dylib /opt/homebrew/lib/libopencv_imgproc.4.11.0.dylib /opt/homebrew/lib/libopencv_core.4.11.0.dylib || Darwin-arm64-Release/libpovg_ligt_algorithm.a
  FLAGS = -Wall -O3 -ffast-math -funroll-all-loops -fPIC -O3 -DNDEBUG -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk
  LINK_LIBRARIES = -Wl,-rpath,/opt/homebrew/lib  Darwin-arm64-Release/libpovg_ligt_algorithm.a  /opt/homebrew/lib/libopencv_gapi.4.11.0.dylib  /opt/homebrew/lib/libopencv_stitching.4.11.0.dylib  /opt/homebrew/lib/libopencv_alphamat.4.11.0.dylib  /opt/homebrew/lib/libopencv_aruco.4.11.0.dylib  /opt/homebrew/lib/libopencv_bgsegm.4.11.0.dylib  /opt/homebrew/lib/libopencv_bioinspired.4.11.0.dylib  /opt/homebrew/lib/libopencv_ccalib.4.11.0.dylib  /opt/homebrew/lib/libopencv_dnn_objdetect.4.11.0.dylib  /opt/homebrew/lib/libopencv_dnn_superres.4.11.0.dylib  /opt/homebrew/lib/libopencv_dpm.4.11.0.dylib  /opt/homebrew/lib/libopencv_face.4.11.0.dylib  /opt/homebrew/lib/libopencv_freetype.4.11.0.dylib  /opt/homebrew/lib/libopencv_fuzzy.4.11.0.dylib  /opt/homebrew/lib/libopencv_hfs.4.11.0.dylib  /opt/homebrew/lib/libopencv_img_hash.4.11.0.dylib  /opt/homebrew/lib/libopencv_intensity_transform.4.11.0.dylib  /opt/homebrew/lib/libopencv_line_descriptor.4.11.0.dylib  /opt/homebrew/lib/libopencv_mcc.4.11.0.dylib  /opt/homebrew/lib/libopencv_quality.4.11.0.dylib  /opt/homebrew/lib/libopencv_rapid.4.11.0.dylib  /opt/homebrew/lib/libopencv_reg.4.11.0.dylib  /opt/homebrew/lib/libopencv_rgbd.4.11.0.dylib  /opt/homebrew/lib/libopencv_saliency.4.11.0.dylib  /opt/homebrew/lib/libopencv_sfm.4.11.0.dylib  /opt/homebrew/lib/libopencv_signal.4.11.0.dylib  /opt/homebrew/lib/libopencv_stereo.4.11.0.dylib  /opt/homebrew/lib/libopencv_structured_light.4.11.0.dylib  /opt/homebrew/lib/libopencv_superres.4.11.0.dylib  /opt/homebrew/lib/libopencv_surface_matching.4.11.0.dylib  /opt/homebrew/lib/libopencv_tracking.4.11.0.dylib  /opt/homebrew/lib/libopencv_videostab.4.11.0.dylib  /opt/homebrew/lib/libopencv_viz.4.11.0.dylib  /opt/homebrew/lib/libopencv_wechat_qrcode.4.11.0.dylib  /opt/homebrew/lib/libopencv_xfeatures2d.4.11.0.dylib  /opt/homebrew/lib/libopencv_xobjdetect.4.11.0.dylib  /opt/homebrew/lib/libopencv_xphoto.4.11.0.dylib  /opt/homebrew/lib/libgflags.2.2.2.dylib  /opt/homebrew/lib/libglog.0.6.0.dylib  /opt/homebrew/lib/libgflags.2.2.2.dylib  /opt/homebrew/lib/libopencv_shape.4.11.0.dylib  /opt/homebrew/lib/libopencv_highgui.4.11.0.dylib  /opt/homebrew/lib/libopencv_datasets.4.11.0.dylib  /opt/homebrew/lib/libopencv_plot.4.11.0.dylib  /opt/homebrew/lib/libopencv_text.4.11.0.dylib  /opt/homebrew/lib/libopencv_ml.4.11.0.dylib  /opt/homebrew/lib/libopencv_phase_unwrapping.4.11.0.dylib  /opt/homebrew/lib/libopencv_optflow.4.11.0.dylib  /opt/homebrew/lib/libopencv_ximgproc.4.11.0.dylib  /opt/homebrew/lib/libopencv_video.4.11.0.dylib  /opt/homebrew/lib/libopencv_videoio.4.11.0.dylib  /opt/homebrew/lib/libopencv_imgcodecs.4.11.0.dylib  /opt/homebrew/lib/libopencv_objdetect.4.11.0.dylib  /opt/homebrew/lib/libopencv_calib3d.4.11.0.dylib  /opt/homebrew/lib/libopencv_dnn.4.11.0.dylib  /opt/homebrew/lib/libopencv_features2d.4.11.0.dylib  /opt/homebrew/lib/libopencv_flann.4.11.0.dylib  /opt/homebrew/lib/libopencv_photo.4.11.0.dylib  /opt/homebrew/lib/libopencv_imgproc.4.11.0.dylib  /opt/homebrew/lib/libopencv_core.4.11.0.dylib
  OBJECT_DIR = mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_COMPILE_PDB = mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/povg_sixpt_algorithm.dir/
  TARGET_FILE = Darwin-arm64-Release/povg_sixpt_algorithm
  TARGET_PDB = Darwin-arm64-Release/povg_sixpt_algorithm.pdb


#############################################
# Utility command for edit_cache

build mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/six_point_algorithm && /opt/homebrew/Cellar/cmake/3.31.4/bin/ccmake -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build mex/LiGT_algorithm/six_point_algorithm/edit_cache: phony mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/six_point_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake --regenerate-during-build -S/Users/<USER>/Documents/PoMVG/po_core/povg_old/src -B/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build mex/LiGT_algorithm/six_point_algorithm/rebuild_cache: phony mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build mex/LiGT_algorithm/six_point_algorithm/list_install_components: phony


#############################################
# Utility command for install

build mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/install.util: CUSTOM_COMMAND mex/LiGT_algorithm/six_point_algorithm/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/six_point_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build mex/LiGT_algorithm/six_point_algorithm/install: phony mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/install.util


#############################################
# Utility command for install/local

build mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/install/local.util: CUSTOM_COMMAND mex/LiGT_algorithm/six_point_algorithm/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/six_point_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build mex/LiGT_algorithm/six_point_algorithm/install/local: phony mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/install/strip.util: CUSTOM_COMMAND mex/LiGT_algorithm/six_point_algorithm/all
  COMMAND = cd /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/six_point_algorithm && /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build mex/LiGT_algorithm/six_point_algorithm/install/strip: phony mex/LiGT_algorithm/six_point_algorithm/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build libpovg_ligt_algorithm.a: phony Darwin-arm64-Release/libpovg_ligt_algorithm.a

build libpovg_old_lib.a: phony lib/libpovg_old_lib.a

build povg_ligt_algorithm: phony Darwin-arm64-Release/libpovg_ligt_algorithm.a

build povg_old_lib: phony lib/libpovg_old_lib.a

build povg_sixpt_algorithm: phony Darwin-arm64-Release/povg_sixpt_algorithm

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release

build all: phony lib/libpovg_old_lib.a mex/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex

build mex/all: phony mex/LiGT_algorithm/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm

build mex/LiGT_algorithm/all: phony mex/LiGT_algorithm/LiGT/all mex/LiGT_algorithm/six_point_algorithm/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/LiGT

build mex/LiGT_algorithm/LiGT/all: phony Darwin-arm64-Release/libpovg_ligt_algorithm.a

# =============================================================================

#############################################
# Folder: /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/mex/LiGT_algorithm/six_point_algorithm

build mex/LiGT_algorithm/six_point_algorithm/all: phony Darwin-arm64-Release/povg_sixpt_algorithm

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc/package-manager/auto-setup.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/FindGFlags.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/FindGlog.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/Findgflags.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/Findglog.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/CMakeLists.txt /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCCompiler.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCCompilerABI.c /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXCompiler.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXCompilerABI.cpp /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCompilerIdDetection.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompilerABI.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompilerId.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineSystem.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeFindBinUtils.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeFindDependencyMacro.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystem.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeTestCCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeTestCXXCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeTestCompilerCommon.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckCSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckFunctionExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckIncludeFile.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckLibraryExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindBLAS.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindOpenMP.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindThreads.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/FeatureTesting.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin-Determine-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/UnixPaths.cmake /opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake /opt/homebrew/lib/cmake/gflags/gflags-config.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets-release.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake /opt/homebrew/lib/cmake/glog/glog-config-version.cmake /opt/homebrew/lib/cmake/glog/glog-config.cmake /opt/homebrew/lib/cmake/glog/glog-modules.cmake /opt/homebrew/lib/cmake/glog/glog-targets-release.cmake /opt/homebrew/lib/cmake/glog/glog-targets.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVConfig-version.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVConfig.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVModules-release.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVModules.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake /opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeCCompiler.cmake CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc/package-manager/auto-setup.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/FindGFlags.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/FindGlog.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/Findgflags.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake/Findglog.cmake /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/CMakeLists.txt /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/CMakeLists.txt /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCCompiler.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCCompilerABI.c /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXCompiler.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXCompilerABI.cpp /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeCompilerIdDetection.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompilerABI.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompilerId.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeDetermineSystem.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeFindBinUtils.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeFindDependencyMacro.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystem.cmake.in /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeTestCCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeTestCXXCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CMakeTestCompilerCommon.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckCSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckFunctionExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckIncludeFile.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/CheckLibraryExists.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindBLAS.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindOpenMP.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageHandleStandardArgs.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindPackageMessage.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/FindThreads.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/CheckSourceCompiles.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Internal/FeatureTesting.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Linker/AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Apple-Clang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin-Determine-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin-Initialize.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Darwin.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake /opt/homebrew/Cellar/cmake/3.31.5/share/cmake/Modules/Platform/UnixPaths.cmake /opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake /opt/homebrew/lib/cmake/gflags/gflags-config.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets-release.cmake /opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake /opt/homebrew/lib/cmake/glog/glog-config-version.cmake /opt/homebrew/lib/cmake/glog/glog-config.cmake /opt/homebrew/lib/cmake/glog/glog-modules.cmake /opt/homebrew/lib/cmake/glog/glog-targets-release.cmake /opt/homebrew/lib/cmake/glog/glog-targets.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVConfig-version.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVConfig.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVModules-release.cmake /opt/homebrew/lib/cmake/opencv4/OpenCVModules.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake /opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake /opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeCCompiler.cmake CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
