# This is the CMakeCache file.
# For build in directory: /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
# It was generated by CMake: /opt/homebrew/Cellar/cmake/3.31.5/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a library.
BLAS_Accelerate_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/Accelerate.framework

//Path to a library.
BLAS_acml_LIBRARY:FILEPATH=BLAS_acml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_acml_mp_LIBRARY:FILEPATH=BLAS_acml_mp_LIBRARY-NOTFOUND

//Path to a library.
BLAS_armpl_lp64_LIBRARY:FILEPATH=BLAS_armpl_lp64_LIBRARY-NOTFOUND

//Path to a library.
BLAS_blas_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/libblas.tbd

//Path to a library.
BLAS_blis_LIBRARY:FILEPATH=BLAS_blis_LIBRARY-NOTFOUND

//Path to a library.
BLAS_complib_sgimath_LIBRARY:FILEPATH=BLAS_complib_sgimath_LIBRARY-NOTFOUND

//Path to a library.
BLAS_cxml_LIBRARY:FILEPATH=BLAS_cxml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_dxml_LIBRARY:FILEPATH=BLAS_dxml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_essl_LIBRARY:FILEPATH=BLAS_essl_LIBRARY-NOTFOUND

//Path to a library.
BLAS_f77blas_LIBRARY:FILEPATH=BLAS_f77blas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_flexiblas_LIBRARY:FILEPATH=BLAS_flexiblas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_goto2_LIBRARY:FILEPATH=BLAS_goto2_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_LIBRARY:FILEPATH=BLAS_mkl_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_em64t_LIBRARY:FILEPATH=BLAS_mkl_em64t_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_ia32_LIBRARY:FILEPATH=BLAS_mkl_ia32_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_intel_LIBRARY:FILEPATH=BLAS_mkl_intel_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_intel_lp64_LIBRARY:FILEPATH=BLAS_mkl_intel_lp64_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_rt_LIBRARY:FILEPATH=BLAS_mkl_rt_LIBRARY-NOTFOUND

//Path to a library.
BLAS_openblas_LIBRARY:FILEPATH=BLAS_openblas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_scs_LIBRARY:FILEPATH=BLAS_scs_LIBRARY-NOTFOUND

//Path to a library.
BLAS_sgemm_LIBRARY:FILEPATH=BLAS_sgemm_LIBRARY-NOTFOUND

//Path to a library.
BLAS_sunperf_LIBRARY:FILEPATH=BLAS_sunperf_LIBRARY-NOTFOUND

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar

//No help, variable specified on the command line.
CMAKE_BUILD_TYPE:STRING=Release

//No help, variable specified on the command line.
CMAKE_COLOR_DIAGNOSTICS:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

CMAKE_CXX_OUTPUT_EXTENSION:STRING=.o

//C compiler
CMAKE_C_COMPILER:STRING=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

CMAKE_C_OUTPUT_EXTENSION:STRING=.o

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of build database during the build.
CMAKE_EXPORT_BUILD_DATABASE:BOOL=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/CMakeFiles/pkgRedirects

//No help, variable specified on the command line.
CMAKE_GENERATOR:STRING=Ninja

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=/Users/<USER>/Qt/Tools/Ninja/ninja

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk

//No help, variable specified on the command line.
CMAKE_PREFIX_PATH:PATH=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//No help, variable specified on the command line.
CMAKE_PROJECT_INCLUDE_BEFORE:FILEPATH=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/.qtc/package-manager/auto-setup.cmake

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=povg_old_lib

//Path to a program.
CMAKE_RANLIB:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/opt/homebrew/share/eigen3/cmake

//The directory containing a CMake configuration file for OpenCV.
OpenCV_DIR:PATH=/opt/homebrew/lib/cmake/opencv4

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=NOTFOUND

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=NOTFOUND

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=NOTFOUND

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=NOTFOUND

//Path to a library.
OpenMP_libomp_LIBRARY:FILEPATH=OpenMP_libomp_LIBRARY-NOTFOUND

//Skip Qt Creator's package manager auto-setup
QT_CREATOR_SKIP_PACKAGE_MANAGER_SETUP:BOOL=OFF

//Qt Creator source groups extensions
QT_CREATOR_SOURCE_GROUPS:BOOL=ON

//No help, variable specified on the command line.
QT_QMAKE_EXECUTABLE:FILEPATH=

//The directory containing a CMake configuration file for gflags.
gflags_DIR:PATH=/opt/homebrew/lib/cmake/gflags

//The directory containing a CMake configuration file for glog.
glog_DIR:PATH=/opt/homebrew/lib/cmake/glog

//Value Computed by CMake
povg_old_lib_BINARY_DIR:STATIC=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release

//Value Computed by CMake
povg_old_lib_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
povg_old_lib_SOURCE_DIR:STATIC=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: BLAS_Accelerate_LIBRARY
BLAS_Accelerate_LIBRARY-ADVANCED:INTERNAL=1
//Have function dgemm_
BLAS_Accelerate_WORKS:INTERNAL=1
//Have function sgemm_
BLAS_WORKS:INTERNAL=
//ADVANCED property for variable: BLAS_acml_LIBRARY
BLAS_acml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_acml_mp_LIBRARY
BLAS_acml_mp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_armpl_lp64_LIBRARY
BLAS_armpl_lp64_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blas_LIBRARY
BLAS_blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blis_LIBRARY
BLAS_blis_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_complib_sgimath_LIBRARY
BLAS_complib_sgimath_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_cxml_LIBRARY
BLAS_cxml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_dxml_LIBRARY
BLAS_dxml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_essl_LIBRARY
BLAS_essl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_f77blas_LIBRARY
BLAS_f77blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_flexiblas_LIBRARY
BLAS_flexiblas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_goto2_LIBRARY
BLAS_goto2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_LIBRARY
BLAS_mkl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_em64t_LIBRARY
BLAS_mkl_em64t_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_ia32_LIBRARY
BLAS_mkl_ia32_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_intel_LIBRARY
BLAS_mkl_intel_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_intel_lp64_LIBRARY
BLAS_mkl_intel_lp64_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_rt_LIBRARY
BLAS_mkl_rt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_openblas_LIBRARY
BLAS_openblas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_scs_LIBRARY
BLAS_scs_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_sgemm_LIBRARY
BLAS_sgemm_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_sunperf_LIBRARY
BLAS_sunperf_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Release
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=5
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.31.5/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.31.5/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.31.5/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/Cellar/cmake/3.31.4/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_BUILD_DATABASE
CMAKE_EXPORT_BUILD_DATABASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=5
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/Cellar/cmake/3.31.5/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding BLAS
FIND_PACKAGE_MESSAGE_DETAILS_BLAS:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/Accelerate.framework][v()]
//Details about finding Gflags
FIND_PACKAGE_MESSAGE_DETAILS_Gflags:INTERNAL=[/opt/homebrew/include][gflags_shared][google][v()]
//Details about finding Glog
FIND_PACKAGE_MESSAGE_DETAILS_Glog:INTERNAL=[glog::glog][v()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp:INTERNAL=FALSE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_Xclang fopenmp:INTERNAL=FALSE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_libomp_LIBRARY
OpenMP_libomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: gflags_DIR
gflags_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: glog_DIR
glog_DIR-ADVANCED:INTERNAL=1

