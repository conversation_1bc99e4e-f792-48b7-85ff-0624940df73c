
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:5 (project)"
    message: |
      The system is: Darwin - 24.3.0 - arm64
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      ld: library 'System' not found
      clang: error: linker command failed with exit code 1 (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is AppleClang, found in:
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/3.31.4/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      ld: library 'c++' not found
      clang++: error: linker command failed with exit code 1 (use -v to see invocation)
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is AppleClang, found in:
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/3.31.4/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_4516f
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        [2/2] : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names -v -Wl,-v CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -o cmTC_4516f   && :
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_4516f -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1115.7.3
        BUILD 23:52:02 Dec  5 2024
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
        Library search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:113 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG']
        ignore line: []
        ignore line: [Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_4516f]
        ignore line: [[1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdebug-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-wC3tJG -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl -search_paths_first -Wl -headerpad_max_install_names -v -Wl -v CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -o cmTC_4516f   && :]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_4516f -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.2] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_4516f] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_4516f.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Running the C compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1115.7.3
      BUILD 23:52:02 Dec  5 2024
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_02c90
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk   -v -Wl,-v -MD -MT CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
        clang++: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include"
        ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include
         /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
         /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        [2/2] : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names -v -Wl,-v CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_02c90   && :
        Apple clang version 16.0.0 (clang-1600.0.26.6)
        Target: arm64-apple-darwin24.3.0
        Thread model: posix
        InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin
         "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_02c90 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1115.7.3
        BUILD 23:52:02 Dec  5 2024
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
        Library search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift
        Framework search paths:
        	/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:113 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
          add: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include]
          add: [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        end of search list found
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include]
        collapse include dir [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include] ==> [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        implicit include dirs: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include;/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ']
        ignore line: []
        ignore line: [Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_02c90]
        ignore line: [[1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk   -v -Wl -v -MD -MT CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        ignore line: [clang++: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/../include/c++/v1"]
        ignore line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -mrelax-all -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.2 -fvisibility-inlines-hidden-static-local-var -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +v8.5a -target-feature +aes -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +lse -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sha2 -target-feature +sha3 -target-feature +neon -target-feature +zcm -target-feature +zcz -target-feature +fullfp16 -target-abi darwinpcs -debugger-tuning=lldb -target-linker-version 1115.7.3 -v -fcoverage-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16 -dependency-file CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1 -internal-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include -internal-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include -internal-externc-isystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -fdebug-compilation-dir=/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-js35WZ -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 16.0.0 (clang-1600.0.26.6) default target arm64-apple-darwin24.3.0]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include/c++/v1]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include]
        ignore line: [ /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [[2/2] : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++ -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl -search_paths_first -Wl -headerpad_max_install_names -v -Wl -v CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_02c90   && :]
        ignore line: [Apple clang version 16.0.0 (clang-1600.0.26.6)]
        ignore line: [Target: arm64-apple-darwin24.3.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin]
        link line: [ "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" -demangle -lto_library /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.2 -syslibroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_02c90 -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.2] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_02c90] ==> ignore
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_02c90.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a] ==> lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        linker tool for 'CXX': /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld
        Library search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift]
        Framework search paths: [;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/16/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib]
        collapse library dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift]
        collapse framework dir [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks] ==> [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib;/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/swift]
        implicit fwks: [/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:5 (project)"
    message: |
      Running the CXX compiler's linker: "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1115.7.3
      BUILD 23:52:02 Dec  5 2024
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 16.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 16.0.0 (tapi-1600.0.11.9)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:54 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-45llH0"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-45llH0"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-45llH0'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_e9834
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -DCMAKE_HAVE_LIBC_PTHREAD  -Wall -O3 -ffast-math -funroll-all-loops -fPIC  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -MD -MT CMakeFiles/cmTC_e9834.dir/src.c.o -MF CMakeFiles/cmTC_e9834.dir/src.c.o.d -o CMakeFiles/cmTC_e9834.dir/src.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-45llH0/src.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        [2/2] : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Wall -O3 -ffast-math -funroll-all-loops -fPIC  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_e9834.dir/src.c.o -o cmTC_e9834   && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindBLAS.cmake:379 (check_function_exists)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindBLAS.cmake:423 (check_blas_libraries)"
      - "CMakeLists.txt:65 (find_package)"
    checks:
      - "Looking for sgemm_"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HxqKzB"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HxqKzB"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "BLAS_WORKS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HxqKzB'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_4e4b1
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -DCHECK_FUNCTION_EXISTS=sgemm_ -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -MD -MT CMakeFiles/cmTC_4e4b1.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_4e4b1.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_4e4b1.dir/CheckFunctionExists.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HxqKzB/CheckFunctionExists.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        [2/2] : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Wall -O3 -ffast-math -funroll-all-loops -fPIC -DCHECK_FUNCTION_EXISTS=sgemm_ -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4e4b1.dir/CheckFunctionExists.c.o -o cmTC_4e4b1   && :
        \x1b[31mFAILED: \x1b[0mcmTC_4e4b1 
        : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Wall -O3 -ffast-math -funroll-all-loops -fPIC -DCHECK_FUNCTION_EXISTS=sgemm_ -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_4e4b1.dir/CheckFunctionExists.c.o -o cmTC_4e4b1   && :
        Undefined symbols for architecture arm64:
          "_sgemm_", referenced from:
              _main in CheckFunctionExists.c.o
        ld: symbol(s) not found for architecture arm64
        clang: error: linker command failed with exit code 1 (use -v to see invocation)
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindBLAS.cmake:379 (check_function_exists)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindBLAS.cmake:1218 (check_blas_libraries)"
      - "CMakeLists.txt:65 (find_package)"
    checks:
      - "Looking for dgemm_"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-JxuhAB"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-JxuhAB"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "BLAS_Accelerate_WORKS"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-JxuhAB'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_c9b67
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang  -F/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks -Wall -O3 -ffast-math -funroll-all-loops -fPIC -DCHECK_FUNCTION_EXISTS=dgemm_ -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -MD -MT CMakeFiles/cmTC_c9b67.dir/CheckFunctionExists.c.o -MF CMakeFiles/cmTC_c9b67.dir/CheckFunctionExists.c.o.d -o CMakeFiles/cmTC_c9b67.dir/CheckFunctionExists.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-JxuhAB/CheckFunctionExists.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        [2/2] : && /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -Wall -O3 -ffast-math -funroll-all-loops -fPIC -DCHECK_FUNCTION_EXISTS=dgemm_ -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names  CMakeFiles/cmTC_c9b67.dir/CheckFunctionExists.c.o -o cmTC_c9b67  -framework Accelerate && :
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-axmFye"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-axmFye"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-axmFye'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_d8eea
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_d8eea.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_d8eea.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_d8eea.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-axmFye/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_d8eea.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_d8eea.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_d8eea.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_d8eea.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-axmFye/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-axmFye/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-hHyMRB"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-hHyMRB"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-hHyMRB'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_bf359
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_bf359.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_bf359.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_bf359.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-hHyMRB/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_bf359.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_bf359.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_bf359.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_bf359.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-hHyMRB/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-hHyMRB/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-QbDP6t"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-QbDP6t"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-QbDP6t'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_ddcde
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_ddcde.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_ddcde.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_ddcde.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-QbDP6t/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_ddcde.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_ddcde.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_ddcde.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_ddcde.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-QbDP6t/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-QbDP6t/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-rqrtpJ"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-rqrtpJ"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-rqrtpJ'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_a860a
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_a860a.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_a860a.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_a860a.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-rqrtpJ/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_a860a.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_a860a.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_a860a.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_a860a.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-rqrtpJ/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-rqrtpJ/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Z7jOwe"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Z7jOwe"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Z7jOwe'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_95257
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_95257.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_95257.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_95257.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Z7jOwe/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_95257.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_95257.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_95257.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_95257.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Z7jOwe/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Z7jOwe/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-b9DAc5"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-b9DAc5"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-b9DAc5'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_dba93
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_dba93.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_dba93.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_dba93.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-b9DAc5/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_dba93.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_dba93.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_dba93.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_dba93.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-b9DAc5/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-b9DAc5/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ae40Yj"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ae40Yj"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ae40Yj'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_c52c3
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_c52c3.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c52c3.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c52c3.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ae40Yj/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_c52c3.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_c52c3.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_c52c3.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_c52c3.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ae40Yj/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-ae40Yj/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-0zpjq7"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-0zpjq7"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-0zpjq7'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_9b9c1
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_9b9c1.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_9b9c1.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_9b9c1.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-0zpjq7/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_9b9c1.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_9b9c1.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_9b9c1.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_9b9c1.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-0zpjq7/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-0zpjq7/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-lNy7S2"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-lNy7S2"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-lNy7S2'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_42d79
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_42d79.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_42d79.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_42d79.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-lNy7S2/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_42d79.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_42d79.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_42d79.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_42d79.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-lNy7S2/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-lNy7S2/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HO9Zcu"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HO9Zcu"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HO9Zcu'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_31476
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_31476.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_31476.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_31476.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HO9Zcu/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_31476.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_31476.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_31476.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_31476.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HO9Zcu/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-HO9Zcu/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Sn6Bhu"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Sn6Bhu"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Sn6Bhu'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_0130d
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_0130d.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_0130d.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_0130d.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Sn6Bhu/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_0130d.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_0130d.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_0130d.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_0130d.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Sn6Bhu/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Sn6Bhu/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-jDhklS"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-jDhklS"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-jDhklS'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_ddf0e
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_ddf0e.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_ddf0e.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_ddf0e.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-jDhklS/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_ddf0e.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_ddf0e.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_ddf0e.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_ddf0e.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-jDhklS/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-jDhklS/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-KsGwKD"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-KsGwKD"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-KsGwKD'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_d3a7c
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_d3a7c.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_d3a7c.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_d3a7c.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-KsGwKD/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_d3a7c.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_d3a7c.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_d3a7c.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_d3a7c.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-KsGwKD/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-KsGwKD/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-2zj4ve"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-2zj4ve"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-2zj4ve'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_e3f58
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_e3f58.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_e3f58.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_e3f58.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-2zj4ve/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_e3f58.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_e3f58.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_e3f58.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_e3f58.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-2zj4ve/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-2zj4ve/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Xvstcy"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Xvstcy"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Xvstcy'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_8eba0
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_8eba0.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_8eba0.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_8eba0.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Xvstcy/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_8eba0.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_8eba0.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_8eba0.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_8eba0.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Xvstcy/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-Xvstcy/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-PtmYTU"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-PtmYTU"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-PtmYTU'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_9df54
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_9df54.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_9df54.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_9df54.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-PtmYTU/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_9df54.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_9df54.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_9df54.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_9df54.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-PtmYTU/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-PtmYTU/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-aFgojb"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-aFgojb"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-aFgojb'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_e6331
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_e6331.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_e6331.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_e6331.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-aFgojb/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_e6331.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_e6331.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_e6331.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_e6331.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-aFgojb/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-aFgojb/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-VSXZWc"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-VSXZWc"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-VSXZWc'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_f5330
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_f5330.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_f5330.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_f5330.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-VSXZWc/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_f5330.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_f5330.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_f5330.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_f5330.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-VSXZWc/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-VSXZWc/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cmR6Fl"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cmR6Fl"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cmR6Fl'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_536ce
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_536ce.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_536ce.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_536ce.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cmR6Fl/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_536ce.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_536ce.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_536ce.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_536ce.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cmR6Fl/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-cmR6Fl/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-nVsCAm"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-nVsCAm"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-nVsCAm'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_b7d38
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_b7d38.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_b7d38.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_b7d38.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-nVsCAm/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_b7d38.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_b7d38.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_b7d38.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_b7d38.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-nVsCAm/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-nVsCAm/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-dMLBrI"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-dMLBrI"
    cmakeVariables:
      CMAKE_C_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-dMLBrI'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_51c2e
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_51c2e.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_51c2e.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_51c2e.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-dMLBrI/OpenMPTryFlag.c
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_51c2e.dir/OpenMPTryFlag.c.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_51c2e.dir/OpenMPTryFlag.c.o -MF CMakeFiles/cmTC_51c2e.dir/OpenMPTryFlag.c.o.d -o CMakeFiles/cmTC_51c2e.dir/OpenMPTryFlag.c.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-dMLBrI/OpenMPTryFlag.c
        clang: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-dMLBrI/OpenMPTryFlag.c:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:252 (try_compile)"
      - "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake:522 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:179 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-qp65nD"
      binary: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-qp65nD"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -Wall -O3 -ffast-math -funroll-all-loops -fPIC"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/cmake;/opt/homebrew/lib/cmake/glog/../../../share/glog/cmake"
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: "10.15"
      CMAKE_OSX_SYSROOT: "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp"
      cached: true
      stdout: |
        Change Dir: '/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-qp65nD'
        
        Run Build Command(s): /Users/<USER>/Qt/Tools/Ninja/ninja -v cmTC_9f83c
        [1/2] /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_9f83c.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_9f83c.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_9f83c.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-qp65nD/OpenMPTryFlag.cpp
        \x1b[31mFAILED: \x1b[0mCMakeFiles/cmTC_9f83c.dir/OpenMPTryFlag.cpp.o 
        /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++   -Wall -O3 -ffast-math -funroll-all-loops -fPIC -Xclang -fopenmp -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -mmacosx-version-min=10.15 -MD -MT CMakeFiles/cmTC_9f83c.dir/OpenMPTryFlag.cpp.o -MF CMakeFiles/cmTC_9f83c.dir/OpenMPTryFlag.cpp.o.d -o CMakeFiles/cmTC_9f83c.dir/OpenMPTryFlag.cpp.o -c /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-qp65nD/OpenMPTryFlag.cpp
        clang++: warning: optimization flag '-funroll-all-loops' is not supported [-Wignored-optimization-argument]
        /Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/CMakeScratch/TryCompile-qp65nD/OpenMPTryFlag.cpp:2:10: fatal error: 'omp.h' file not found
            2 | #include <omp.h>
              |          ^~~~~~~
        1 error generated.
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...
