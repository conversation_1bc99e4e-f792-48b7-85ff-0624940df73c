{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/Cellar/cmake/3.31.4/bin/cmake", "cpack": "/opt/homebrew/Cellar/cmake/3.31.4/bin/cpack", "ctest": "/opt/homebrew/Cellar/cmake/3.31.4/bin/ctest", "root": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 4, "string": "3.31.4", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-366dc06328ecf5eda088.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-c736d893279feab0615d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-88e2764d5578e9e9193c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-c736d893279feab0615d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-88e2764d5578e9e9193c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-366dc06328ecf5eda088.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}