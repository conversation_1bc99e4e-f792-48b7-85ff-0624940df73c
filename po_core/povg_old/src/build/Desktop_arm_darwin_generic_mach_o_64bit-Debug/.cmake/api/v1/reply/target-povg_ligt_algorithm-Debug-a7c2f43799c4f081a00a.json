{"archive": {}, "artifacts": [{"path": "Darwin-arm64-Debug/libpovg_ligt_algorithm.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "include_directories", "INCLUDE_DIRECTORIES", "target_include_directories", "set_target_properties"], "files": ["mex/LiGT_algorithm/LiGT/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 14, "parent": 0}, {"command": 1, "file": 0, "line": 36, "parent": 0}, {"file": 1}, {"command": 2, "file": 1, "line": 121, "parent": 3}, {"command": 3, "file": 0, "line": 8, "parent": 0}, {"command": 4, "file": 0, "line": 24, "parent": 0}, {"command": 5, "file": 0, "line": 17, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -O3 -ffast-math -funroll-all-loops -fPIC -g -std=c++17 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -fPIC -fcolor-diagnostics"}], "includes": [{"backtrace": 4, "path": "/opt/homebrew/include/eigen3"}, {"backtrace": 5, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include"}, {"backtrace": 6, "path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT"}], "language": "CXX", "languageStandard": {"backtraces": [7], "standard": "17"}, "sourceIndexes": [6, 7, 8, 9, 10]}], "id": "povg_ligt_algorithm::@00f5ba032a321ec38856", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src"}}, "name": "povg_ligt_algorithm", "nameOnDisk": "libpovg_ligt_algorithm.a", "paths": {"build": "mex/LiGT_algorithm/LiGT", "source": "mex/LiGT_algorithm/LiGT"}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5]}, {"name": "Source Files", "sourceIndexes": [6, 7, 8, 9, 10]}], "sources": [{"backtrace": 1, "path": "mex/LiGT_algorithm/LiGT/LiGT_algorithm.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "mex/LiGT_algorithm/LiGT/povg_timer.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "mex/LiGT_algorithm/LiGT/povg_tracks.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "mex/LiGT_algorithm/LiGT/povg_types.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "mex/LiGT_algorithm/LiGT/relative_residuals.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "mex/LiGT_algorithm/LiGT/robust_LiGT.hpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/povg_tracks.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/povg_types.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/relative_residuals.cpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "mex/LiGT_algorithm/LiGT/robust_LiGT.cpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}