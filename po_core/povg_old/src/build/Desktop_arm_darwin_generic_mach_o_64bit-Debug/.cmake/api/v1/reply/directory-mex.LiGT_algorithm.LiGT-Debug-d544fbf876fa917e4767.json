{"backtraceGraph": {"commands": ["install"], "files": ["mex/LiGT_algorithm/LiGT/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"command": 0, "file": 0, "line": 37, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "lib", "paths": ["Darwin-arm64-Debug/libpovg_ligt_algorithm.a"], "targetId": "povg_ligt_algorithm::@00f5ba032a321ec38856", "targetIndex": 0, "type": "target"}, {"backtrace": 1, "component": "Unspecified", "cxxModuleBmiTarget": {"id": "povg_ligt_algorithm::@00f5ba032a321ec38856", "index": 0}, "destination": "lib", "type": "cxxModuleBmi"}, {"backtrace": 2, "component": "Unspecified", "destination": "include/povg_ligt", "paths": ["mex/LiGT_algorithm/LiGT/LiGT_algorithm.hpp", "mex/LiGT_algorithm/LiGT/povg_timer.hpp", "mex/LiGT_algorithm/LiGT/povg_tracks.hpp", "mex/LiGT_algorithm/LiGT/povg_types.hpp", "mex/LiGT_algorithm/LiGT/relative_residuals.hpp", "mex/LiGT_algorithm/LiGT/robust_LiGT.hpp"], "type": "file"}], "paths": {"build": "mex/LiGT_algorithm/LiGT", "source": "mex/LiGT_algorithm/LiGT"}}