{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc/package-manager/auto-setup.cmake"}, {"isGenerated": true, "path": "build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/3.31.4/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Darwin-Initialize.cmake"}, {"isGenerated": true, "path": "build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/3.31.4/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/3.31.4/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Darwin.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Compiler/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Apple-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Linker/AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Compiler/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Apple-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Linker/AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Linker/AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindBLAS.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckFunctionExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindThreads.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckLibraryExists.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckIncludeFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CheckCSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake"}, {"path": "cmake/FindGFlags.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "cmake/FindGlog.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-modules.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-targets-release.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindOpenMP.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "cmake/Findgflags.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "cmake/Findglog.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-config.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/CMakeFindDependencyMacro.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-modules.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/gflags/gflags-nonamespace-targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/glog/glog-targets.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"path": "mex/CMakeLists.txt"}, {"path": "mex/LiGT_algorithm/CMakeLists.txt"}, {"path": "mex/LiGT_algorithm/LiGT/CMakeLists.txt"}, {"path": "mex/LiGT_algorithm/six_point_algorithm/CMakeLists.txt"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Config.cmake"}, {"isExternal": true, "path": "/opt/homebrew/share/eigen3/cmake/Eigen3Targets.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVModules.cmake"}, {"isExternal": true, "path": "/opt/homebrew/lib/cmake/opencv4/OpenCVModules-release.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug", "source": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src"}, "version": {"major": 1, "minor": 1}}