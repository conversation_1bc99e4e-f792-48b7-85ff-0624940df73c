{"entries": [{"name": "BLAS_Accelerate_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/Accelerate.framework"}, {"name": "BLAS_Accelerate_WORKS", "properties": [{"name": "HELPSTRING", "value": "Have function dgemm_"}], "type": "INTERNAL", "value": "1"}, {"name": "BLAS_WORKS", "properties": [{"name": "HELPSTRING", "value": "Have function sgemm_"}], "type": "INTERNAL", "value": ""}, {"name": "BLAS_acml_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_acml_LIBRARY-NOTFOUND"}, {"name": "BLAS_acml_mp_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_acml_mp_LIBRARY-NOTFOUND"}, {"name": "BLAS_armpl_lp64_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_armpl_lp64_LIBRARY-NOTFOUND"}, {"name": "BLAS_blas_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/usr/lib/libblas.tbd"}, {"name": "BLAS_blis_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_blis_LIBRARY-NOTFOUND"}, {"name": "BLAS_complib_sgimath_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_complib_sgimath_LIBRARY-NOTFOUND"}, {"name": "BLAS_cxml_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_cxml_LIBRARY-NOTFOUND"}, {"name": "BLAS_dxml_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_dxml_LIBRARY-NOTFOUND"}, {"name": "BLAS_essl_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_essl_LIBRARY-NOTFOUND"}, {"name": "BLAS_f77blas_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_f77blas_LIBRARY-NOTFOUND"}, {"name": "BLAS_flexiblas_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_flexiblas_LIBRARY-NOTFOUND"}, {"name": "BLAS_goto2_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_goto2_LIBRARY-NOTFOUND"}, {"name": "BLAS_mkl_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_mkl_LIBRARY-NOTFOUND"}, {"name": "BLAS_mkl_em64t_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_mkl_em64t_LIBRARY-NOTFOUND"}, {"name": "BLAS_mkl_ia32_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_mkl_ia32_LIBRARY-NOTFOUND"}, {"name": "BLAS_mkl_intel_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_mkl_intel_LIBRARY-NOTFOUND"}, {"name": "BLAS_mkl_intel_lp64_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_mkl_intel_lp64_LIBRARY-NOTFOUND"}, {"name": "BLAS_mkl_rt_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_mkl_rt_LIBRARY-NOTFOUND"}, {"name": "BLAS_openblas_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_openblas_LIBRARY-NOTFOUND"}, {"name": "BLAS_scs_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_scs_LIBRARY-NOTFOUND"}, {"name": "BLAS_sgemm_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_sgemm_LIBRARY-NOTFOUND"}, {"name": "BLAS_sunperf_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "BLAS_sunperf_LIBRARY-NOTFOUND"}, {"name": "CMAKE_ADDR2LINE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_ADDR2LINE-NOTFOUND"}, {"name": "CMAKE_AR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar"}, {"name": "CMAKE_BUILD_TYPE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Debug"}, {"name": "CMAKE_CACHEFILE_DIR", "properties": [{"name": "HELPSTRING", "value": "This is the directory where this CMakeCache.txt was created"}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug"}, {"name": "CMAKE_CACHE_MAJOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Major version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "3"}, {"name": "CMAKE_CACHE_MINOR_VERSION", "properties": [{"name": "HELPSTRING", "value": "Minor version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "31"}, {"name": "CMAKE_CACHE_PATCH_VERSION", "properties": [{"name": "HELPSTRING", "value": "Patch version of cmake used to create the current loaded cache"}], "type": "INTERNAL", "value": "4"}, {"name": "CMAKE_COLOR_DIAGNOSTICS", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "BOOL", "value": "ON"}, {"name": "CMAKE_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to CMake executable."}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/cmake/3.31.4/bin/cmake"}, {"name": "CMAKE_CPACK_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cpack program executable."}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/cmake/3.31.4/bin/cpack"}, {"name": "CMAKE_CTEST_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to ctest program executable."}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/cmake/3.31.4/bin/ctest"}, {"name": "CMAKE_CXX_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler"}], "type": "STRING", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++"}, {"name": "CMAKE_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_CXX_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_CXX_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_CXX_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the CXX compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_CXX_OUTPUT_EXTENSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ".o"}, {"name": "CMAKE_C_COMPILER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler"}], "type": "STRING", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang"}, {"name": "CMAKE_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_C_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during DEBUG builds."}], "type": "STRING", "value": "-g"}, {"name": "CMAKE_C_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during MINSIZEREL builds."}], "type": "STRING", "value": "-Os -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELEASE builds."}], "type": "STRING", "value": "-O3 -DNDEBUG"}, {"name": "CMAKE_C_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the C compiler during RELWITHDEBINFO builds."}], "type": "STRING", "value": "-O2 -g -DNDEBUG"}, {"name": "CMAKE_C_OUTPUT_EXTENSION", "properties": [{"name": "HELPSTRING", "value": ""}], "type": "STRING", "value": ".o"}, {"name": "CMAKE_DLLTOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_DLLTOOL-NOTFOUND"}, {"name": "CMAKE_EDIT_COMMAND", "properties": [{"name": "HELPSTRING", "value": "Path to cache edit program executable."}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/cmake/3.31.4/bin/ccmake"}, {"name": "CMAKE_EXECUTABLE_FORMAT", "properties": [{"name": "HELPSTRING", "value": "Executable file format"}], "type": "INTERNAL", "value": "MACHO"}, {"name": "CMAKE_EXE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_EXPORT_BUILD_DATABASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of build database during the build."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_EXPORT_COMPILE_COMMANDS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Enable/Disable output of compile commands during generation."}], "type": "BOOL", "value": ""}, {"name": "CMAKE_FIND_PACKAGE_REDIRECTS_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake."}], "type": "STATIC", "value": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/CMakeFiles/pkgRedirects"}, {"name": "CMAKE_GENERATOR", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "STRING", "value": "Ninja"}, {"name": "CMAKE_GENERATOR_INSTANCE", "properties": [{"name": "HELPSTRING", "value": "Generator instance identifier."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_PLATFORM", "properties": [{"name": "HELPSTRING", "value": "Name of generator platform."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_GENERATOR_TOOLSET", "properties": [{"name": "HELPSTRING", "value": "Name of generator toolset."}], "type": "INTERNAL", "value": ""}, {"name": "CMAKE_HAVE_LIBC_PTHREAD", "properties": [{"name": "HELPSTRING", "value": "Test CMAKE_HAVE_LIBC_PTHREAD"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_HOME_DIRECTORY", "properties": [{"name": "HELPSTRING", "value": "Source directory with the top level CMakeLists.txt file for this project"}], "type": "INTERNAL", "value": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src"}, {"name": "CMAKE_INSTALL_NAME_TOOL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/usr/bin/install_name_tool"}, {"name": "CMAKE_INSTALL_PREFIX", "properties": [{"name": "HELPSTRING", "value": "Install path prefix, prepended onto install directories."}], "type": "PATH", "value": "/usr/local"}, {"name": "CMAKE_LINKER", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld"}, {"name": "CMAKE_MAKE_PROGRAM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Program used to build from build.ninja files."}], "type": "FILEPATH", "value": "/Users/<USER>/Qt/Tools/Ninja/ninja"}, {"name": "CMAKE_MODULE_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of modules during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_NM", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/nm"}, {"name": "CMAKE_NUMBER_OF_MAKEFILES", "properties": [{"name": "HELPSTRING", "value": "number of local generators"}], "type": "INTERNAL", "value": "5"}, {"name": "CMAKE_OBJCOPY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_OBJCOPY-NOTFOUND"}, {"name": "CMAKE_OBJDUMP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/objdump"}, {"name": "CMAKE_OSX_ARCHITECTURES", "properties": [{"name": "HELPSTRING", "value": "Build architectures for OSX"}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_DEPLOYMENT_TARGET", "properties": [{"name": "HELPSTRING", "value": "Minimum OS X version to target for deployment (at runtime); newer APIs weak linked. Set to empty string for default value."}], "type": "STRING", "value": ""}, {"name": "CMAKE_OSX_SYSROOT", "properties": [{"name": "HELPSTRING", "value": "The product will be built against the headers and libraries located inside the indicated SDK."}], "type": "PATH", "value": "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk"}, {"name": "CMAKE_PLATFORM_INFO_INITIALIZED", "properties": [{"name": "HELPSTRING", "value": "Platform information initialized"}], "type": "INTERNAL", "value": "1"}, {"name": "CMAKE_PREFIX_PATH", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "PATH", "value": ""}, {"name": "CMAKE_PROJECT_DESCRIPTION", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_HOMEPAGE_URL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": ""}, {"name": "CMAKE_PROJECT_INCLUDE_BEFORE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc/package-manager/auto-setup.cmake"}, {"name": "CMAKE_PROJECT_NAME", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "povg_old_lib"}, {"name": "CMAKE_RANLIB", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib"}, {"name": "CMAKE_READELF", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "CMAKE_READELF-NOTFOUND"}, {"name": "CMAKE_ROOT", "properties": [{"name": "HELPSTRING", "value": "Path to CMake installation."}], "type": "INTERNAL", "value": "/opt/homebrew/Cellar/cmake/3.31.4/share/cmake"}, {"name": "CMAKE_SHARED_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of shared libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_SKIP_INSTALL_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when installing shared libraries, but are added when building."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_SKIP_RPATH", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If set, runtime paths are not added when using shared libraries."}], "type": "BOOL", "value": "NO"}, {"name": "CMAKE_STATIC_LINKER_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during all build types."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_DEBUG", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during DEBUG builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during MINSIZEREL builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELEASE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELEASE builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Flags used by the linker during the creation of static libraries during RELWITHDEBINFO builds."}], "type": "STRING", "value": ""}, {"name": "CMAKE_STRIP", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/strip"}, {"name": "CMAKE_TAPI", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a program."}], "type": "FILEPATH", "value": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi"}, {"name": "CMAKE_UNAME", "properties": [{"name": "HELPSTRING", "value": "uname command"}], "type": "INTERNAL", "value": "/usr/bin/uname"}, {"name": "CMAKE_VERBOSE_MAKEFILE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "If this value is on, makefiles will be generated without the .SILENT directive, and all commands will be echoed to the console during the make.  This is useful for debugging only. With Visual Studio IDE projects all commands are done without /nologo."}], "type": "BOOL", "value": "FALSE"}, {"name": "Eigen3_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for Eigen3."}], "type": "PATH", "value": "/opt/homebrew/share/eigen3/cmake"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_BLAS", "properties": [{"name": "HELPSTRING", "value": "Details about finding BLAS"}], "type": "INTERNAL", "value": "[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks/Accelerate.framework][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Gflags", "properties": [{"name": "HELPSTRING", "value": "Details about finding Gflags"}], "type": "INTERNAL", "value": "[/opt/homebrew/include][gflags_shared][google][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Glog", "properties": [{"name": "HELPSTRING", "value": "Details about finding <PERSON>log"}], "type": "INTERNAL", "value": "[glog::glog][v()]"}, {"name": "FIND_PACKAGE_MESSAGE_DETAILS_Threads", "properties": [{"name": "HELPSTRING", "value": "Details about finding Threads"}], "type": "INTERNAL", "value": "[TRUE][v()]"}, {"name": "GFLAGS_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "GFLAGS_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "GFLAGS_NAMESPACE", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "GLOG_INCLUDE_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "GLOG_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": ""}], "type": "UNINITIALIZED", "value": ""}, {"name": "OpenCV_DIR", "properties": [{"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for OpenCV."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/opencv4"}, {"name": "OpenMP_COMPILE_RESULT_CXX_Xclang fopenmp", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "FALSE"}, {"name": "OpenMP_COMPILE_RESULT_C_Xclang fopenmp", "properties": [{"name": "HELPSTRING", "value": "Result of TRY_COMPILE"}], "type": "INTERNAL", "value": "FALSE"}, {"name": "OpenMP_CXX_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler flags for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_CXX_LIB_NAMES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "CXX compiler libraries for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_C_FLAGS", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler flags for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_C_LIB_NAMES", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "C compiler libraries for OpenMP parallelization"}], "type": "STRING", "value": "NOTFOUND"}, {"name": "OpenMP_libomp_LIBRARY", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "Path to a library."}], "type": "FILEPATH", "value": "OpenMP_libomp_LIBRARY-NOTFOUND"}, {"name": "QT_CREATOR_SKIP_PACKAGE_MANAGER_SETUP", "properties": [{"name": "HELPSTRING", "value": "Skip Qt Creator's package manager auto-setup"}], "type": "BOOL", "value": "OFF"}, {"name": "QT_CREATOR_SOURCE_GROUPS", "properties": [{"name": "HELPSTRING", "value": "Qt Creator source groups extensions"}], "type": "BOOL", "value": "ON"}, {"name": "QT_QMAKE_EXECUTABLE", "properties": [{"name": "HELPSTRING", "value": "No help, variable specified on the command line."}], "type": "FILEPATH", "value": ""}, {"name": "gflags_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for gflags."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/gflags"}, {"name": "glog_DIR", "properties": [{"name": "ADVANCED", "value": "1"}, {"name": "HELPSTRING", "value": "The directory containing a CMake configuration file for glog."}], "type": "PATH", "value": "/opt/homebrew/lib/cmake/glog"}, {"name": "povg_old_lib_BINARY_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug"}, {"name": "povg_old_lib_IS_TOP_LEVEL", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "ON"}, {"name": "povg_old_lib_SOURCE_DIR", "properties": [{"name": "HELPSTRING", "value": "Value Computed by CMake"}], "type": "STATIC", "value": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src"}], "kind": "cache", "version": {"major": 2, "minor": 0}}