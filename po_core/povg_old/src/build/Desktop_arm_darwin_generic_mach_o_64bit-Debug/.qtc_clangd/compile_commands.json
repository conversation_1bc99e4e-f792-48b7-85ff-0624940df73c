[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_timer.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_timer.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fPIC", "-fcolor-diagnostics", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.hpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.hpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-mmacosx-version-min=10.15", "-fcolor-diagnostics", "-Wall", "-Wextra", "-g", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DHAVE_GFLAGS", "-DHAVE_GLOG", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/LiGT_algorithm.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-mmacosx-version-min=10.15", "-fcolor-diagnostics", "-Wall", "-Wextra", "-g", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DHAVE_GFLAGS", "-DHAVE_GLOG", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_tracks.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-mmacosx-version-min=10.15", "-fcolor-diagnostics", "-Wall", "-Wextra", "-g", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DHAVE_GFLAGS", "-DHAVE_GLOG", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/povg_types.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-mmacosx-version-min=10.15", "-fcolor-diagnostics", "-Wall", "-Wextra", "-g", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DHAVE_GFLAGS", "-DHAVE_GLOG", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/relative_residuals.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-mmacosx-version-min=10.15", "-fcolor-diagnostics", "-Wall", "-Wextra", "-g", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DHAVE_GFLAGS", "-DHAVE_GLOG", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT/robust_LiGT.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-mmacosx-version-min=10.15", "-fcolor-diagnostics", "-Wall", "-Wextra", "-g", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DHAVE_GFLAGS", "-DHAVE_GLOG", "-DQ_CREATOR_RUN", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-isystem", "/opt/homebrew/include/eigen3", "-isystem", "/opt/homebrew/include", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk/System/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-arch", "arm64", "-Wall", "-ffast-math", "-funroll-all-loops", "-fPIC", "-g", "-std=c++17", "-arch", "arm64", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk", "-fcolor-diagnostics", "-Wall", "-fPIC", "-funroll-all-loops", "-fsyntax-only", "--target=arm64-apple-darwin24.2.0", "-DGFLAGS_IS_A_DLL=0", "-DGLOG_CUSTOM_PREFIX_SUPPORT", "-DQ_CREATOR_RUN", "-I/opt/homebrew/include/eigen3", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/LiGT", "-I/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/spectra-master/include", "-isystem", "/opt/homebrew/Cellar/opencv/4.11.0/include/opencv4", "-isystem", "/opt/homebrew/include", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/c++/v1", "-isystem", "/Users/<USER>/Qt/Qt Creator.app/Contents/Resources/libexec/clang/lib/clang/19/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include", "-isystem", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm.cpp"], "directory": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/build/Desktop_arm_darwin_generic_mach_o_64bit-Debug/.qtc_clangd", "file": "/Users/<USER>/Documents/PoMVG/po_core/povg_old/src/mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm.cpp"}]