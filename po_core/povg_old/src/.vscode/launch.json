{"version": "0.2.0", "configurations": [{"name": "C++ Launch", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/build/your_executable", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "CMake: build", "miDebuggerPath": "/usr/bin/gdb", "logging": {"trace": true, "traceResponse": true, "engineLogging": true}}]}