################################################################################
# CMake minimum version and project name
################################################################################
cmake_minimum_required(VERSION 3.16)
project(povg_old_lib)

################################################################################
# Build type and installation settings
################################################################################
message(">>>>>make type:${CMAKE_BUILD_TYPE}")
set(CMAKE_INSTALL_PREFIX ${CMAKE_SOURCE_DIR})

# 添加CMake模块路径
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/cmake) # add FindMatlab module
set(CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake" ${CMAKE_MODULE_PATH})

# 设置GFlags路径
if(APPLE)
    # macOS设置 - 使用Homebrew路径
    set(CMAKE_PREFIX_PATH "${CMAKE_PREFIX_PATH}:/opt/homebrew/lib/cmake/gflags")
    set(CMAKE_PREFIX_PATH "${CMAKE_PREFIX_PATH}:/usr/local/lib/cmake/gflags")
else()
    # Linux设置
    set(CMAKE_PREFIX_PATH "${CMAKE_PREFIX_PATH}:/usr/local/lib/cmake/gflags")
endif()

################################################################################
# Compiler flags and settings
################################################################################
if(APPLE)
    # macOS特定设置
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -O3 -ffast-math -funroll-all-loops -fPIC")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -O3 -ffast-math -funroll-all-loops -fPIC")
else()
    # Linux设置
    set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}  -Wall  -O3 -ffast-math -funroll-all-loops -fPIC -march=x86-64")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall  -mtune=native -mavx")
endif()

# C++标准设置
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 输出路径设置
set(EXECUTABLE_OUTPUT_PATH "${PROJECT_BINARY_DIR}/${CMAKE_SYSTEM_NAME}-${CMAKE_SYSTEM_PROCESSOR}-${CMAKE_BUILD_TYPE}")
set(LIBRARY_OUTPUT_PATH "${PROJECT_BINARY_DIR}/${CMAKE_SYSTEM_NAME}-${CMAKE_SYSTEM_PROCESSOR}-${CMAKE_BUILD_TYPE}")

################################################################################
# Dependencies
################################################################################

# 添加Threads库支持
find_package(Threads REQUIRED)

# Eigen相关设置(已注释)
#add_definitions(-DEIGEN_USE_MKL_ALL)
#add_definitions(-DEIGEN_NO_DEBUG)
#add_definitions(-DEIGEN_USE_LAPACKE)
#add_definitions(-DEIGEN_USE_BLAS)

# BLAS - 根据平台设置
if(APPLE)
    # macOS使用Accelerate框架
    find_package(BLAS REQUIRED)
    if(BLAS_FOUND)
        message(STATUS "BLAS found: ${BLAS_LIBRARIES}")
    endif()
else()
    # Linux设置
    set(BLAS_LIBRARIES "/usr/lib/x86_64-linux-gnu/libblas.so")
    find_package(BLAS REQUIRED)
    if(NOT BLAS_FOUND)
        message(FATAL_ERROR "BLAS not found!")
    endif()
endif()

# Eigen3
find_package(Eigen3 REQUIRED)
if (Eigen3_FOUND)
  set(EIGEN_INCLUDE_DIRS ${EIGEN3_INCLUDE_DIR})
  message(STATUS "Eigen3 found: ${EIGEN3_INCLUDE_DIR}")
else()
    message(STATUS "Eigen3 not found via config, trying pkg-config")
    find_package(PkgConfig QUIET)
    if(PKG_CONFIG_FOUND)
        pkg_check_modules(EIGEN3 eigen3)
        if(EIGEN3_FOUND)
            set(EIGEN_INCLUDE_DIRS ${EIGEN3_INCLUDE_DIRS})
        endif()
    endif()
endif()

# GFlags
if(APPLE)
    # macOS Homebrew路径
    set(GFlags_DIR "/opt/homebrew/lib/cmake/gflags")
    if(NOT EXISTS ${GFlags_DIR})
        set(GFlags_DIR "/usr/local/lib/cmake/gflags") 
    endif()
else()
    set(GFlags_DIR "/usr/local/lib/cmake/gflags")
endif()
find_package(GFlags REQUIRED)

# Glog
if(APPLE)
    # macOS Homebrew路径
    set(GLOG_INCLUDE_DIR "/opt/homebrew/include")
    if(NOT EXISTS ${GLOG_INCLUDE_DIR}/glog)
        set(GLOG_INCLUDE_DIR "/usr/local/include")
    endif()
else()
    set(GLOG_INCLUDE_DIR "/usr/include/glog")
endif()
find_package(Glog REQUIRED)

################################################################################
# Include directories
################################################################################
include_directories(
    ${EIGEN_INCLUDE_DIRS}
    ${gflags_INCLUDE_DIRS}
    ${GLOG_INCLUDE_DIR}
)

message("GLOG_INCLUDE_DIR:" ${GLOG_INCLUDE_DIR})
message("EIGEN_INCLUDE_DIRS:" ${EIGEN_INCLUDE_DIRS})

################################################################################
# 定义静态库目标
################################################################################
# 初始化一个空列表，用于收集所有源文件
set(POVGLIB_SOURCES "")

# 包含子目录的CMakeLists文件
add_subdirectory(mex)

# 创建静态库
add_library(povg_old_lib STATIC ${POVGLIB_SOURCES})

# 为静态库设置链接依赖（稍后统一设置）

# Spectra库路径设置
set(SPECTRA_DIRS "${CMAKE_CURRENT_SOURCE_DIR}/mex/spectra-master/include")
message(STATUS "Spectra directory: ${SPECTRA_DIRS}")

# LiGT算法目录设置
set(LIGT_DIRS "${CMAKE_CURRENT_SOURCE_DIR}/mex/LiGT_algorithm/LiGT")

# 设置静态库的包含目录
target_include_directories(povg_old_lib PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:include>
    ${EIGEN_INCLUDE_DIRS}
    ${gflags_INCLUDE_DIRS}
    ${GLOG_INCLUDE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/mex/LiGT_algorithm/LiGT
    ${CMAKE_CURRENT_SOURCE_DIR}/mex/LiGT_algorithm/six_point_algorithm
    ${SPECTRA_DIRS}
)

# 如果OpenCV可用，添加其包含目录
if(OpenCV_FOUND)
    target_include_directories(povg_old_lib PRIVATE ${OpenCV_INCLUDE_DIRS})
endif()

################################################################################
# Subdirectories
################################################################################

# 兼容性设置
if(APPLE)
    set(CMAKE_CXX_COMPILER_ID "AppleClang")
    set(CMAKE_OSX_DEPLOYMENT_TARGET "10.15")
endif()

# OpenMP 支持 (可选)
find_package(OpenMP)
if(OpenMP_CXX_FOUND)
    target_link_libraries(povg_old_lib PUBLIC OpenMP::OpenMP_CXX)
endif()

# gflags 和 glog (可选，用于原始main函数的部分功能)
find_package(gflags REQUIRED)
find_package(glog REQUIRED)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/mex/LiGT_algorithm/LiGT
    ${CMAKE_CURRENT_SOURCE_DIR}/mex/LiGT_algorithm/six_point_algorithm
    ${EIGEN3_INCLUDE_DIRS}
)

# 源文件
set(POVG_OLD_SOURCES
    mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm.cpp
    mex/LiGT_algorithm/LiGT/povg_types.cpp
)

# 头文件
set(POVG_OLD_HEADERS
    mex/LiGT_algorithm/LiGT/povg_types.hpp
    mex/LiGT_algorithm/six_point_algorithm/sixpt_algorithm.hpp
)

# 统一设置链接库
target_link_libraries(povg_old_lib 
    PUBLIC 
        Eigen3::Eigen
        Threads::Threads
        ${GFLAGS_LIBRARIES}
        ${GLOG_LIBRARY}
        ${BLAS_LIBRARIES}
)

# 可选链接gflags和glog
if(gflags_FOUND)
    target_compile_definitions(povg_old_lib PUBLIC HAVE_GFLAGS)
endif()

if(glog_FOUND)
    target_compile_definitions(povg_old_lib PUBLIC HAVE_GLOG)
endif()

# 编译选项
target_compile_options(povg_old_lib PRIVATE
    $<$<COMPILE_LANGUAGE:CXX>:-Wall -Wextra -O3>
    $<$<AND:$<COMPILE_LANGUAGE:CXX>,$<CONFIG:Debug>>:-g>
)

# 为静态库设置目标属性
set_target_properties(povg_old_lib PROPERTIES
    OUTPUT_NAME "povg_old_lib"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 安装配置
install(TARGETS povg_old_lib
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

install(FILES ${POVG_OLD_HEADERS}
    DESTINATION include/povg_old
)
