# GetOptionAsPath 功能说明

## 概述

`GetOptionAsPath` 是一个增强的路径配置获取函数，支持多种占位符来动态构建路径，包括特定占位符和通用键值引用，解决了运行时路径灵活性的问题。

## 函数签名

```cpp
std::string GetOptionAsPath(const MethodParams &key, 
                           const std::string &root_dir = "", 
                           const std::string &default_value = "") const;
```

### 参数说明

- `key`: 配置项的键名
- `root_dir`: 根目录路径，用于{root_dir}占位符替换（可选）
- `default_value`: 默认值，当配置项不存在时使用（可选）

## 支持的占位符

### 1. `{exe_dir}` - 可执行文件目录
- **用途**: 指向当前运行的可执行文件所在目录
- **特点**: 运行时动态获取，跨平台支持
- **示例**: `{exe_dir}/output/results`

### 2. `{root_dir}` - 自定义根目录
- **用途**: 用户可自定义的根目录，推荐在库发布场景中使用
- **获取方式**: 
  1. 优先使用函数参数中的`root_dir`
  2. 如果参数为空，从`method_options_`中的`root_dir`键获取
- **示例**: `{root_dir}/data/images`

### 3. `{key_name}` - 通用键值引用
- **用途**: 引用`method_options_`中其他配置项的值
- **特点**: 可以引用任意已存在的配置键值，实现配置项之间的动态关联
- **注意事项**: 
  - 被引用的键值必须已经存在于`method_options_`中
  - 注意设置配置项的先后顺序，避免引用未设置的键值
  - 如果引用的键值不存在，会输出警告并用空字符串替换
- **示例**: `{images_folder}/../gt_data`, `{base_path}/config.ini`

## 使用示例

### 1. INI配置文件示例

```ini
[method_example]
# 设置根目录（推荐方式）
root_dir = /home/<USER>/project

# 设置基础路径
base_data_path = {root_dir}/dataset
images_folder = {base_data_path}/images
features_folder = {base_data_path}/features

# 使用键值引用构建相关路径
gt_dataset_path = {images_folder}/../gt_dense_cameras
output_dir = {exe_dir}/output/results
temp_dir = {exe_dir}/temp_files
config_file = {base_data_path}/config.ini

# 绝对路径示例
absolute_path = /usr/local/share/data
```

### 2. C++代码示例

```cpp
// 设置基础配置
method->SetMethodOptions({
    {"root_dir", "/home/<USER>/project"},
    {"dataset_name", "strecha_castle"},
    {"base_path", "{root_dir}/datasets/{dataset_name}"},
    {"images_folder", "{base_path}/images"},
    {"gt_folder", "{images_folder}/../gt_dense_cameras"}
});

// 获取路径（会自动解析所有占位符）
std::string images_path = method->GetOptionAsPath("images_folder");
// 结果：/home/<USER>/project/datasets/strecha_castle/images

std::string gt_path = method->GetOptionAsPath("gt_folder");
// 结果：/home/<USER>/project/datasets/strecha_castle/images/../gt_dense_cameras
//       (规范化后：/home/<USER>/project/datasets/strecha_castle/gt_dense_cameras)
```

### 3. 复杂配置示例

```cpp
// 动态构建多层路径引用
method->SetMethodOptions({
    {"project_root", "/opt/myapp"},
    {"version", "v1.2"},
    {"data_root", "{project_root}/data/{version}"},
    {"input_folder", "{data_root}/input"},
    {"output_folder", "{data_root}/output"},
    {"log_file", "{output_folder}/process.log"},
    {"temp_dir", "{exe_dir}/temp/{version}"}
});
```

## 错误处理

### 1. 缺失root_dir警告
当路径使用`{root_dir}`占位符但未提供root_dir时：

```
[method_name] WARNING >>> 路径 '{root_dir}/some/path' 使用了{root_dir}占位符，但未提供root_dir参数且配置中无root_dir设置
```

### 2. 键值引用失败警告
当使用`{key_name}`引用不存在的配置项时：

```
[method_name] WARNING >>> 路径 '{images_folder}/../gt' 中的占位符 {images_folder} 未在method_options_中找到对应值，将替换为空字符串
```

### 3. exe_dir获取失败警告
当无法获取可执行文件路径时：

```
[method_name] Warning: Failed to get executable path
```

## 路径处理规则

1. **占位符替换顺序**: 
   - 首先处理通用键值引用`{key_name}`
   - 然后处理特定占位符`{root_dir}`和`{exe_dir}`
2. **绝对路径**: 如果路径不包含任何占位符，直接作为绝对路径返回
3. **路径规范化**: 使用`std::filesystem::path::lexically_normal()`进行路径规范化
4. **跨平台兼容**: 支持Windows、macOS和Linux平台

## 配置最佳实践

### 1. 避免循环引用
```ini
# 错误示例 - 循环引用
path_a = {path_b}/folder
path_b = {path_a}/subfolder  # 会导致问题

# 正确示例 - 层次化配置
base_path = /home/<USER>/data
path_a = {base_path}/folder_a
path_b = {base_path}/folder_b
```

### 2. 注意配置顺序
```cpp
// 推荐：先设置被引用的配置项
method->SetMethodOptions({
    {"base_path", "/home/<USER>/data"},        // 先设置
    {"images_folder", "{base_path}/images"}, // 后引用
    {"gt_folder", "{images_folder}/../gt"}   // 再引用
});
```

### 3. 使用有意义的键名
```ini
# 推荐：使用清晰的键名
dataset_root = /data/strecha
images_path = {dataset_root}/images
ground_truth_path = {dataset_root}/gt_dense_cameras

# 避免：使用模糊的键名
path1 = /data/strecha
path2 = {path1}/images  # path1含义不清晰
```

## 调试技巧

1. **启用详细日志**: 使用`log_level = 2`查看路径解析过程
2. **检查占位符**: 确保占位符拼写正确（区分大小写）
3. **验证引用顺序**: 确保被引用的配置项在引用前已设置
4. **测试路径解析**: 在开发阶段验证所有路径配置的正确性 