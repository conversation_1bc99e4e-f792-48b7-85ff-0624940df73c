/**
 * @file test_ananalytical_reconstruction.cpp
 * @brief 测试解析式三角化重建方法
 * @copyright Copyright (c) 2024 PoSDK Project
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include "data/data_points_3d.hpp"
#include <memory>
#include <iostream>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <random>

namespace
{
    using namespace PoSDK;
    using namespace Interface;
    using namespace types;

    class AnalyticalReconstructionTest : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 创建视觉模拟器
            simulator_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("visual_simulator"));
            ASSERT_TRUE(simulator_ != nullptr) << "无法创建VisualSimulator实例";

            // 创建解析式重建方法实例 - 使用工厂创建
            auto reconstruction_ptr = FactoryMethod::Create("analytical_reconstruction");
            reconstruction_method_ = std::dynamic_pointer_cast<MethodPreset>(reconstruction_ptr);
            ASSERT_TRUE(reconstruction_method_ != nullptr) << "无法创建AnalyticalReconstruction实例";

            // 设置基本参数
            simulator_->SetMethodOption("debug_output", "true");
            simulator_->SetMethodOption("simu_folder", "test_ananalytical_reconstruction_data");

            // 确保存在目录
            std::filesystem::create_directories("test_ananalytical_reconstruction_data");
        }

        void TearDown() override
        {
            // 清理临时文件（可选）
            // std::filesystem::remove_all("test_ananalytical_reconstruction_data");
        }

        /**
         * @brief 计算统计数据
         */
        void PrintStatistics(const std::vector<double> &errors, const std::string &description)
        {
            if (errors.empty())
                return;

            double mean_error = std::accumulate(errors.begin(), errors.end(), 0.0) / errors.size();

            std::vector<double> sorted_errors = errors;
            std::sort(sorted_errors.begin(), sorted_errors.end());

            double median_error = sorted_errors[sorted_errors.size() / 2];
            double max_error = *std::max_element(errors.begin(), errors.end());
            double min_error = *std::min_element(errors.begin(), errors.end());

            std::cout << "\n=== " << description << " ===" << std::endl;
            std::cout << "点数量: " << errors.size() << std::endl;
            std::cout << "平均误差: " << std::fixed << std::setprecision(6) << mean_error << std::endl;
            std::cout << "中位数误差: " << median_error << std::endl;
            std::cout << "最大误差: " << max_error << std::endl;
            std::cout << "最小误差: " << min_error << std::endl;
            std::cout << "=========================" << std::endl;
        }

        std::shared_ptr<MethodPreset> simulator_;
        std::shared_ptr<MethodPreset> reconstruction_method_;
    };

    // 基本功能测试
    TEST_F(AnalyticalReconstructionTest, BasicFunctionality)
    {

        ASSERT_NE(reconstruction_method_, nullptr);

        // 测试GetType方法
        EXPECT_EQ(reconstruction_method_->GetType(), "analytical_reconstruction");

        // 测试配置参数设置
        reconstruction_method_->SetMethodOption("m3_ratio", "0.1");
        reconstruction_method_->SetMethodOption("min_num_observations_per_point", "2");
        reconstruction_method_->SetMethodOption("reconstruction_mode", "normal");

        std::cout << "AnalyticalReconstruction基本功能测试通过" << std::endl;

        // 说明下，这个测试用例是测试解析式重建方法的基本功能，包括设置参数、获取数据、运行重建等。
        std::cout << "测试内容：" << std::endl;
        std::cout << "1. 测试GetType方法" << std::endl;
        std::cout << "2. 测试配置参数设置" << std::endl;

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

    // 测试普通重建模式 - 使用视觉模拟器数据
    TEST_F(AnalyticalReconstructionTest, NormalReconstructionWithSimulator)
    {
        // 1. 生成模拟数据
        simulator_->SetMethodOption("num_views", "8");
        simulator_->SetMethodOption("num_points", "100");
        simulator_->SetMethodOption("noise_level", "0.0"); // 无噪声
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "10.0");

        auto simulator_result = simulator_->Build();
        ASSERT_TRUE(simulator_result != nullptr) << "VisualSimulator运行失败";

        // 2. 获取生成的数据
        auto package = std::dynamic_pointer_cast<DataPackage>(simulator_result);
        ASSERT_TRUE(package != nullptr);

        auto data_tracks = package->GetData("data_tracks");
        ASSERT_TRUE(data_tracks != nullptr) << "无法获取轨迹数据";

        auto simulator_poses_data = package->GetData("data_global_poses");
        ASSERT_TRUE(simulator_poses_data != nullptr) << "无法获取位姿数据";

        auto camera_model_data = package->GetData("data_camera_models");
        ASSERT_TRUE(camera_model_data != nullptr) << "无法获取相机模型数据";

        // 获取真值3D点
        auto gt_points_data = package->GetData("data_points_3d");
        ASSERT_TRUE(gt_points_data != nullptr) << "无法获取真值3D点";
        auto gt_world_point_info = GetDataPtr<WorldPointInfo>(gt_points_data);
        ASSERT_TRUE(gt_world_point_info != nullptr);

        std::cout << "\n模拟器数据统计:" << std::endl;
        std::cout << "  轨迹数量: " << GetDataPtr<Tracks>(data_tracks)->size() << std::endl;
        std::cout << "  视图数量: " << GetDataPtr<GlobalPoses>(simulator_poses_data)->Size() << std::endl;
        std::cout << "  真值3D点数量: " << gt_world_point_info->size() << std::endl;

        // 3. 设置重建方法参数
        reconstruction_method_->SetMethodOption("reconstruction_mode", "normal");
        reconstruction_method_->SetMethodOption("min_num_observations_per_point", "2");
        reconstruction_method_->SetMethodOption("log_level", "2");

        // 4. 创建输入数据包
        auto input_package = std::make_shared<DataPackage>();
        input_package->AddData("data_tracks", data_tracks);
        input_package->AddData("data_global_poses", simulator_poses_data);
        input_package->AddData("data_camera_models", camera_model_data);

        // 5. 设置输入数据并运行重建
        reconstruction_method_->SetRequiredData(input_package);
        auto reconstruction_result = reconstruction_method_->Build();

        // 6. 验证结果
        ASSERT_TRUE(reconstruction_result != nullptr) << "重建结果不应为空";

        // 正确获取重建的WorldPointInfo数据
        auto reconstructed_world_point_info = GetDataPtr<WorldPointInfo>(reconstruction_result);
        ASSERT_TRUE(reconstructed_world_point_info != nullptr) << "结果应该包含WorldPointInfo数据";

        std::cout << "重建成功，得到 " << reconstructed_world_point_info->getValidPointsCount()
                  << " 个有效3D点 / " << reconstructed_world_point_info->size() << " 个总点" << std::endl;

        // 7. 评估重建精度 - 使用WorldPointInfo的评估方法
        std::vector<double> position_errors;
        bool eval_success = reconstructed_world_point_info->EvaluateWith3DPoints(
            *gt_world_point_info, position_errors);

        if (eval_success)
        {
            PrintStatistics(position_errors, "重建精度统计");

            // 检查精度是否满足要求
            double mean_error = std::accumulate(position_errors.begin(), position_errors.end(), 0.0) / position_errors.size();
            EXPECT_LT(mean_error, 0.1) << "平均重建误差应该小于0.1";

            // 检查至少80%的点误差小于0.2
            int good_points = std::count_if(position_errors.begin(), position_errors.end(),
                                            [](double error)
                                            { return error < 0.2; });
            double good_ratio = static_cast<double>(good_points) / position_errors.size();
            EXPECT_GT(good_ratio, 0.8) << "至少80%的点重建误差应该小于0.2";
        }
        else
        {
            std::cout << "精度评估失败，可能是点数量不匹配" << std::endl;
        }

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

    // 测试鲁棒重建模式
    TEST_F(AnalyticalReconstructionTest, RobustReconstructionMode)
    {
        // 1. 生成模拟数据
        simulator_->SetMethodOption("num_views", "10");
        simulator_->SetMethodOption("num_points", "200");
        simulator_->SetMethodOption("noise_level", "0.5"); // 添加一些噪声
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "8.0");

        auto simulator_result = simulator_->Build();
        ASSERT_TRUE(simulator_result != nullptr) << "VisualSimulator运行失败";

        // 2. 获取生成的数据
        auto package = std::dynamic_pointer_cast<DataPackage>(simulator_result);
        ASSERT_TRUE(package != nullptr);

        auto data_tracks = package->GetData("data_tracks");
        ASSERT_TRUE(data_tracks != nullptr);

        auto simulator_poses_data = package->GetData("data_global_poses");
        ASSERT_TRUE(simulator_poses_data != nullptr);

        auto camera_model_data = package->GetData("data_camera_models");
        ASSERT_TRUE(camera_model_data != nullptr);

        std::cout << "\n鲁棒重建测试 - 模拟器数据统计:" << std::endl;
        std::cout << "  轨迹数量: " << GetDataPtr<Tracks>(data_tracks)->size() << std::endl;
        std::cout << "  视图数量: " << GetDataPtr<GlobalPoses>(simulator_poses_data)->Size() << std::endl;

        // 3. 设置鲁棒重建参数
        reconstruction_method_->SetMethodOption("reconstruction_mode", "robust_reconstruction");
        reconstruction_method_->SetMethodOption("m3_ratio", "0.05"); // 设置视差角阈值
        reconstruction_method_->SetMethodOption("min_num_observations_per_point", "3");
        reconstruction_method_->SetMethodOption("log_level", "2");

        // 添加额外的调试输出到stderr
        std::cerr << "=== 开始鲁棒重建测试 ===" << std::endl;

        // 4. 创建输入数据包
        auto input_package = std::make_shared<DataPackage>();
        input_package->AddData("data_tracks", data_tracks);
        input_package->AddData("data_global_poses", simulator_poses_data);
        input_package->AddData("data_camera_models", camera_model_data);

        // 5. 运行鲁棒重建
        reconstruction_method_->SetRequiredData(input_package);
        auto reconstruction_result = reconstruction_method_->Build();

        std::cerr << "=== 鲁棒重建方法调用完成 ===" << std::endl;

        // 6. 验证结果
        ASSERT_TRUE(reconstruction_result != nullptr) << "鲁棒重建结果不应为空";

        // 正确获取重建的WorldPointInfo数据
        auto reconstructed_world_point_info = GetDataPtr<WorldPointInfo>(reconstruction_result);
        ASSERT_TRUE(reconstructed_world_point_info != nullptr) << "结果应该包含WorldPointInfo数据";

        std::cout << "鲁棒重建成功，得到 " << reconstructed_world_point_info->getValidPointsCount()
                  << " 个有效3D点 / " << reconstructed_world_point_info->size() << " 个总点" << std::endl;

        // 鲁棒重建可能会过滤掉一些不可靠的点
        EXPECT_GT(reconstructed_world_point_info->getValidPointsCount(), 0) << "应该重建出至少一些3D点";

        // 输出一些重建点的坐标用于验证
        size_t valid_count = 0;
        for (size_t i = 0; i < reconstructed_world_point_info->size() && valid_count < 5; ++i)
        {
            if (reconstructed_world_point_info->isUsed(i))
            {
                Point3d point = reconstructed_world_point_info->getPoint(i);
                std::cout << "鲁棒重建点 " << valid_count << ": ("
                          << std::fixed << std::setprecision(3)
                          << point(0) << ", "
                          << point(1) << ", "
                          << point(2) << ")" << std::endl;
                ++valid_count;
            }
        }

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

    // 测试异常值检测模式
    TEST_F(AnalyticalReconstructionTest, OutlierDetectionMode)
    {
        // 1. 生成模拟数据
        simulator_->SetMethodOption("num_views", "8");
        simulator_->SetMethodOption("num_points", "150");
        simulator_->SetMethodOption("noise_level", "0.0");
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "12.0");

        auto simulator_result = simulator_->Build();
        ASSERT_TRUE(simulator_result != nullptr);

        // 2. 获取生成的数据
        auto package = std::dynamic_pointer_cast<DataPackage>(simulator_result);
        ASSERT_TRUE(package != nullptr);

        auto data_tracks = package->GetData("data_tracks");
        ASSERT_TRUE(data_tracks != nullptr);

        auto simulator_poses_data = package->GetData("data_global_poses");
        ASSERT_TRUE(simulator_poses_data != nullptr);

        auto camera_model_data = package->GetData("data_camera_models");
        ASSERT_TRUE(camera_model_data != nullptr);

        // 3. 设置异常值检测参数
        reconstruction_method_->SetMethodOption("reconstruction_mode", "remove_outliers");
        reconstruction_method_->SetMethodOption("m3_ratio", "0.1");
        reconstruction_method_->SetMethodOption("log_level", "2");

        // 4. 创建输入数据包
        auto input_package = std::make_shared<DataPackage>();
        input_package->AddData("data_tracks", data_tracks);
        input_package->AddData("data_global_poses", simulator_poses_data);
        input_package->AddData("data_camera_models", camera_model_data);

        // 5. 运行异常值检测
        reconstruction_method_->SetRequiredData(input_package);
        auto detection_result = reconstruction_method_->Build();

        // 6. 验证结果
        ASSERT_TRUE(detection_result != nullptr) << "异常值检测结果不应为空";

        auto outlier_data = std::dynamic_pointer_cast<DataMap<std::vector<IndexT>>>(detection_result);
        ASSERT_TRUE(outlier_data != nullptr) << "结果应该是异常值ID列表类型";

        auto &outlier_ids = *static_cast<std::vector<IndexT> *>(outlier_data->GetData());
        std::cout << "异常值检测完成，检测到 " << outlier_ids.size() << " 个异常值轨迹" << std::endl;

        // 输出一些异常值ID
        for (size_t i = 0; i < std::min(size_t(10), outlier_ids.size()); ++i)
        {
            std::cout << "异常值轨迹ID: " << outlier_ids[i] << std::endl;
        }

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

    // 测试错误处理
    TEST_F(AnalyticalReconstructionTest, ErrorHandling)
    {
        ASSERT_NE(reconstruction_method_, nullptr);

        // 测试没有输入数据的情况
        auto result = reconstruction_method_->Build();
        EXPECT_EQ(result, nullptr) << "没有输入数据时应该返回nullptr";

        // 测试只有部分输入数据的情况
        auto incomplete_package = std::make_shared<DataPackage>();

        // 创建一个空的轨迹数据
        auto empty_tracks = std::make_shared<DataMap<Tracks>>();
        incomplete_package->AddData("data_tracks", empty_tracks);
        // 故意不添加data_global_poses

        reconstruction_method_->SetRequiredData(incomplete_package);
        result = reconstruction_method_->Build();
        EXPECT_EQ(result, nullptr) << "缺少必要输入数据时应该返回nullptr";

        std::cout << "错误处理测试通过" << std::endl;

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

    // 测试PLY文件存储和读取功能
    TEST_F(AnalyticalReconstructionTest, PlyFileIOTest)
    {
        std::cout << "\n=== PLY文件读写测试 ===" << std::endl;

        // 1. 生成测试数据
        simulator_->SetMethodOption("num_views", "6");
        simulator_->SetMethodOption("num_points", "50");
        simulator_->SetMethodOption("noise_level", "0.0");
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "15.0");

        auto simulator_result = simulator_->Build();
        ASSERT_TRUE(simulator_result != nullptr) << "VisualSimulator运行失败";

        auto package = std::dynamic_pointer_cast<DataPackage>(simulator_result);
        ASSERT_TRUE(package != nullptr);

        // 2. 进行3D重建
        auto data_tracks = package->GetData("data_tracks");
        auto simulator_poses_data = package->GetData("data_global_poses");
        auto camera_model_data = package->GetData("data_camera_models");

        reconstruction_method_->SetMethodOption("reconstruction_mode", "normal");
        reconstruction_method_->SetMethodOption("min_num_observations_per_point", "2");

        auto input_package = std::make_shared<DataPackage>();
        input_package->AddData("data_tracks", data_tracks);
        input_package->AddData("data_global_poses", simulator_poses_data);
        input_package->AddData("data_camera_models", camera_model_data);

        reconstruction_method_->SetRequiredData(input_package);
        auto reconstruction_result = reconstruction_method_->Build();
        ASSERT_TRUE(reconstruction_result != nullptr);

        auto original_world_point_info = GetDataPtr<WorldPointInfo>(reconstruction_result);
        ASSERT_TRUE(original_world_point_info != nullptr);

        std::cout << "原始重建数据统计:" << std::endl;
        std::cout << "  总点数: " << original_world_point_info->size() << std::endl;
        std::cout << "  有效点数: " << original_world_point_info->getValidPointsCount() << std::endl;

        // 3. 直接使用重建结果进行PLY文件保存测试
        auto data_points_3d = std::dynamic_pointer_cast<DataPoints3D>(reconstruction_result);
        ASSERT_TRUE(data_points_3d != nullptr) << "重建结果应该是DataPoints3D类型";

        // 4. 测试PLY文件保存
        std::string test_folder = "test_ananalytical_reconstruction_data";
        std::string test_filename = "test_points";

        std::cout << "\n--- 测试PLY文件保存 ---" << std::endl;
        bool save_success = data_points_3d->Save(test_folder, test_filename, ".ply");
        ASSERT_TRUE(save_success) << "PLY文件保存应该成功";

        // 验证文件是否创建
        std::string ply_path = test_folder + "/" + test_filename + ".ply";
        std::string ids_path = test_folder + "/" + test_filename + ".ids";

        ASSERT_TRUE(std::filesystem::exists(ply_path)) << "PLY文件应该被创建: " << ply_path;
        ASSERT_TRUE(std::filesystem::exists(ids_path)) << "IDS文件应该被自动创建: " << ids_path;

        std::cout << "✓ PLY文件保存成功: " << ply_path << std::endl;
        std::cout << "✓ IDS文件自动创建: " << ids_path << std::endl;

        // 5. 测试PLY文件读取
        std::cout << "\n--- 测试PLY文件读取 ---" << std::endl;
        auto loaded_data_points_3d = std::make_shared<DataPoints3D>();
        bool load_success = loaded_data_points_3d->Load(ply_path, "ply");
        ASSERT_TRUE(load_success) << "PLY文件读取应该成功";

        auto loaded_world_point_info = static_cast<WorldPointInfo *>(loaded_data_points_3d->GetData());
        ASSERT_TRUE(loaded_world_point_info != nullptr);

        std::cout << "读取的数据统计:" << std::endl;
        std::cout << "  总点数: " << loaded_world_point_info->size() << std::endl;
        std::cout << "  有效点数: " << loaded_world_point_info->getValidPointsCount() << std::endl;

        // 6. 验证数据一致性
        std::cout << "\n--- 验证数据一致性 ---" << std::endl;

        // 检查点数量
        EXPECT_EQ(original_world_point_info->size(), loaded_world_point_info->size())
            << "读取的点数量应该与原始数据一致";

        EXPECT_EQ(original_world_point_info->getValidPointsCount(), loaded_world_point_info->getValidPointsCount())
            << "读取的有效点数量应该与原始数据一致";

        // 检查点坐标精度
        double max_coordinate_error = 0.0;
        int mismatched_usage_count = 0;

        for (size_t i = 0; i < original_world_point_info->size(); ++i)
        {
            // 检查使用状态
            bool original_used = original_world_point_info->isUsed(i);
            bool loaded_used = loaded_world_point_info->isUsed(i);
            if (original_used != loaded_used)
            {
                mismatched_usage_count++;
            }

            // 检查点坐标（仅对使用的点）
            if (original_used && loaded_used)
            {
                Point3d original_point = original_world_point_info->getPoint(i);
                Point3d loaded_point = loaded_world_point_info->getPoint(i);

                double coordinate_error = (original_point - loaded_point).norm();
                max_coordinate_error = std::max(max_coordinate_error, coordinate_error);
            }
        }

        EXPECT_EQ(mismatched_usage_count, 0) << "使用状态不匹配的点数: " << mismatched_usage_count;
        EXPECT_LT(max_coordinate_error, 1e-10) << "坐标精度误差应该极小，最大误差: " << max_coordinate_error;

        std::cout << "✓ 使用状态匹配点数: " << (original_world_point_info->size() - mismatched_usage_count)
                  << "/" << original_world_point_info->size() << std::endl;
        std::cout << "✓ 最大坐标误差: " << std::scientific << max_coordinate_error << std::endl;

        // 7. 测试部分点的具体数值
        std::cout << "\n--- 验证具体点坐标 ---" << std::endl;
        int verified_points = 0;
        for (size_t i = 0; i < original_world_point_info->size() && verified_points < 5; ++i)
        {
            if (original_world_point_info->isUsed(i))
            {
                Point3d original_point = original_world_point_info->getPoint(i);
                Point3d loaded_point = loaded_world_point_info->getPoint(i);

                std::cout << "点 " << i << ":" << std::endl;
                std::cout << "  原始: (" << std::fixed << std::setprecision(6)
                          << original_point(0) << ", "
                          << original_point(1) << ", "
                          << original_point(2) << ")" << std::endl;
                std::cout << "  读取: (" << std::fixed << std::setprecision(6)
                          << loaded_point(0) << ", "
                          << loaded_point(1) << ", "
                          << loaded_point(2) << ")" << std::endl;
                std::cout << "  误差: " << std::scientific
                          << (original_point - loaded_point).norm() << std::endl;

                verified_points++;
            }
        }

        std::cout << "✓ PLY文件读写功能验证通过" << std::endl;

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

    // 测试PLY文件错误处理
    TEST_F(AnalyticalReconstructionTest, PlyFileErrorHandlingTest)
    {
        std::cout << "\n=== PLY文件错误处理测试 ===" << std::endl;

        auto data_points_3d = std::make_shared<DataPoints3D>();

        // 1. 测试读取不存在的文件
        std::cout << "\n--- 测试读取不存在的文件 ---" << std::endl;
        std::string non_existent_file = "test_ananalytical_reconstruction_data/non_existent.ply";
        bool load_result = data_points_3d->Load(non_existent_file, "ply");
        EXPECT_FALSE(load_result) << "读取不存在的文件应该失败";
        std::cout << "✓ 不存在文件处理正确" << std::endl;

        // 2. 测试无效的PLY文件
        std::cout << "\n--- 测试无效的PLY文件 ---" << std::endl;
        std::string invalid_ply_path = "test_ananalytical_reconstruction_data/invalid.ply";

        // 创建一个无效的PLY文件
        std::ofstream invalid_file(invalid_ply_path);
        if (invalid_file.is_open())
        {
            invalid_file << "this is not a valid ply file\n";
            invalid_file << "invalid format\n";
            invalid_file.close();
        }

        load_result = data_points_3d->Load(invalid_ply_path, "ply");
        EXPECT_FALSE(load_result) << "读取无效PLY文件应该失败";
        std::cout << "✓ 无效PLY文件处理正确" << std::endl;

        // 3. 测试保存到无效路径
        std::cout << "\n--- 测试保存到无效路径 ---" << std::endl;

        // 创建一些测试数据
        auto test_data_points_3d = std::make_shared<DataPoints3D>();
        auto test_world_point_info_ptr = static_cast<WorldPointInfo *>(test_data_points_3d->GetData());
        test_world_point_info_ptr->resize(10);

        for (size_t i = 0; i < 10; ++i)
        {
            test_world_point_info_ptr->setPoint(i, Point3d(i, i * 2, i * 3));
            test_world_point_info_ptr->setUsed(i, i % 2 == 0); // 偶数索引的点可用
        }

        // 尝试保存到无效路径
        std::string invalid_folder = "/invalid/nonexistent/path";
        bool save_result = test_data_points_3d->Save(invalid_folder, "test", ".ply");
        EXPECT_FALSE(save_result) << "保存到无效路径应该失败";
        std::cout << "✓ 无效路径处理正确" << std::endl;

        // 4. 测试不支持的文件格式
        std::cout << "\n--- 测试不支持的文件格式 ---" << std::endl;
        load_result = data_points_3d->Load("test.xyz", "xyz");
        EXPECT_FALSE(load_result) << "不支持的文件格式应该失败";

        save_result = test_data_points_3d->Save("test_ananalytical_reconstruction_data", "test", ".xyz");
        EXPECT_FALSE(save_result) << "不支持的文件格式保存应该失败";
        std::cout << "✓ 不支持格式处理正确" << std::endl;

        // 5. 测试pb格式警告
        std::cout << "\n--- 测试pb格式警告 ---" << std::endl;
        std::cout << "注意：以下应该显示pb格式警告信息" << std::endl;

        // 首先用txt格式保存测试数据，创建一个txt格式的文件供pb格式读取测试
        save_result = test_data_points_3d->Save("test_ananalytical_reconstruction_data", "txt_for_pb_test", ".txt");
        ASSERT_TRUE(save_result) << "创建txt测试文件应该成功";

        // 测试pb格式读取（应该回退到txt格式并成功）
        std::string txt_test_path = "test_ananalytical_reconstruction_data/txt_for_pb_test.txt";
        load_result = data_points_3d->Load(txt_test_path, "pb");
        EXPECT_TRUE(load_result) << "pb格式应该回退到txt格式并成功读取txt文件";

        // 测试pb格式保存（应该回退到txt格式并成功）
        save_result = test_data_points_3d->Save("test_ananalytical_reconstruction_data", "pb_test", ".pb");
        EXPECT_TRUE(save_result) << "pb格式保存应该回退到txt格式并成功";

        // 验证pb保存的文件实际上是txt格式
        std::string pb_test_path = "test_ananalytical_reconstruction_data/pb_test.pb";
        EXPECT_TRUE(std::filesystem::exists(pb_test_path)) << "pb格式保存的文件应该存在";

        // 尝试用txt格式读取pb保存的文件，应该成功（因为实际是txt格式）
        auto verify_data = std::make_shared<DataPoints3D>();
        bool verify_result = verify_data->Load(pb_test_path, "txt");
        EXPECT_TRUE(verify_result) << "pb保存的文件应该能用txt格式正确读取";

        std::cout << "✓ pb格式警告测试完成" << std::endl;

        // 清理测试文件
        if (std::filesystem::exists(invalid_ply_path))
        {
            std::filesystem::remove(invalid_ply_path);
        }

        std::cout << "✓ PLY文件错误处理测试通过" << std::endl;

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

    // 测试IDS文件独立处理
    TEST_F(AnalyticalReconstructionTest, IdsFileHandlingTest)
    {
        std::cout << "\n=== IDS文件处理测试 ===" << std::endl;

        // 1. 创建测试数据
        auto data_points_3d = std::make_shared<DataPoints3D>();
        auto test_world_point_info_ptr = static_cast<WorldPointInfo *>(data_points_3d->GetData());
        test_world_point_info_ptr->resize(20);

        for (size_t i = 0; i < 20; ++i)
        {
            test_world_point_info_ptr->setPoint(i, Point3d(i * 0.1, i * 0.2, i * 0.3));
            test_world_point_info_ptr->setUsed(i, i % 3 != 0); // 每3个点中有1个不可用
        }

        std::cout << "测试数据统计:" << std::endl;
        std::cout << "  总点数: " << test_world_point_info_ptr->size() << std::endl;
        std::cout << "  有效点数: " << test_world_point_info_ptr->getValidPointsCount() << std::endl;

        // 2. 保存PLY和IDS文件
        std::cout << "\n--- 保存包含使用状态的数据 ---" << std::endl;
        std::string test_folder = "test_ananalytical_reconstruction_data";
        std::string test_filename = "ids_test_points";

        bool save_success = data_points_3d->Save(test_folder, test_filename, ".ply");
        ASSERT_TRUE(save_success) << "数据保存应该成功";

        std::string ply_path = test_folder + "/" + test_filename + ".ply";
        std::string ids_path = test_folder + "/" + test_filename + ".ids";

        ASSERT_TRUE(std::filesystem::exists(ply_path)) << "PLY文件应该存在";
        ASSERT_TRUE(std::filesystem::exists(ids_path)) << "IDS文件应该存在";

        // 3. 测试只读取PLY文件（无IDS文件）
        std::cout << "\n--- 测试无IDS文件的情况 ---" << std::endl;

        // 临时删除IDS文件
        std::string backup_ids_path = ids_path + ".backup";
        std::filesystem::rename(ids_path, backup_ids_path);

        auto loaded_data_no_ids = std::make_shared<DataPoints3D>();
        bool load_success = loaded_data_no_ids->Load(ply_path, "ply");
        ASSERT_TRUE(load_success) << "只读取PLY文件应该成功";

        auto loaded_info_no_ids = static_cast<WorldPointInfo *>(loaded_data_no_ids->GetData());

        // 验证所有点都被标记为可用（默认行为）
        EXPECT_EQ(loaded_info_no_ids->getValidPointsCount(), loaded_info_no_ids->size())
            << "无IDS文件时，所有点应该被标记为可用";

        std::cout << "✓ 无IDS文件时使用默认状态（所有点可用）" << std::endl;

        // 4. 恢复IDS文件并重新读取
        std::cout << "\n--- 测试有IDS文件的情况 ---" << std::endl;
        std::filesystem::rename(backup_ids_path, ids_path);

        auto loaded_data_with_ids = std::make_shared<DataPoints3D>();
        load_success = loaded_data_with_ids->Load(ply_path, "ply");
        ASSERT_TRUE(load_success) << "读取PLY+IDS文件应该成功";

        auto loaded_info_with_ids = static_cast<WorldPointInfo *>(loaded_data_with_ids->GetData());

        // 验证使用状态被正确恢复
        EXPECT_EQ(loaded_info_with_ids->getValidPointsCount(), test_world_point_info_ptr->getValidPointsCount())
            << "有IDS文件时，使用状态应该被正确恢复";

        std::cout << "✓ 有IDS文件时正确恢复使用状态" << std::endl;

        // 5. 验证具体的使用状态
        std::cout << "\n--- 验证具体使用状态 ---" << std::endl;
        int status_match_count = 0;
        for (size_t i = 0; i < test_world_point_info_ptr->size(); ++i)
        {
            bool original_used = test_world_point_info_ptr->isUsed(i);
            bool loaded_used = loaded_info_with_ids->isUsed(i);
            if (original_used == loaded_used)
            {
                status_match_count++;
            }

            // 输出前几个点的状态对比
            if (i < 10)
            {
                std::cout << "点 " << i << ": 原始=" << (original_used ? "✓" : "✗")
                          << ", 读取=" << (loaded_used ? "✓" : "✗")
                          << (original_used == loaded_used ? " [匹配]" : " [不匹配]") << std::endl;
            }
        }

        EXPECT_EQ(status_match_count, static_cast<int>(test_world_point_info_ptr->size()))
            << "所有点的使用状态都应该匹配";

        std::cout << "✓ 使用状态匹配: " << status_match_count << "/" << test_world_point_info_ptr->size() << std::endl;
        std::cout << "✓ IDS文件处理测试通过" << std::endl;

        // 分割线
        std::cout << "------------------------------------------------------------------------" << std::endl;
    }

} // namespace
