/**
 * @file test_two_view_optimizer.cpp
 * @brief TwoViewOptimizer 双视图位姿优化器测试 - 优化目标函数和损失函数性能评估
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>

#ifdef USE_OPENMP
#include <omp.h>
#endif

namespace
{
    using namespace PoSDK;

    class TestTwoViewOptimizer : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            std::string config_name;       // 配置名称，用作ProfileCommit
            double max_parallax = 2.0;     // 平移幅度
            double max_rotation = 0.3;     // 旋转幅度(弧度)
            size_t num_points = 150;       // 特征点数量（稍微多一些用于优化）
            double noise_level = 0.5;      // 噪声水平（固定中等噪声）
            double outlier_fraction = 0.0; // 外点比例（固定为0）
            int random_seed = 42;          // 随机种子
        };

        // 优化器配置结构体
        struct OptimizerConfig
        {
            std::string config_name;
            std::string residual_type;  // 残差函数类型
            std::string loss_type;      // 损失函数类型
            std::string optimizer_type; // 优化器类型
        };

        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 设置conda环境用于Python绘图
            Interface::EvaluatorManager::SetCondaEnv("pymagsac_py312");

            // 初始化LiRP方法（用于生成初值）
            lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(lirp_method_ != nullptr) << "Failed to create method_LiRP";

            // 初始化TwoViewOptimizer方法
            optimizer_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_TwoViewOptimizer"));
            ASSERT_TRUE(optimizer_method_ != nullptr) << "Failed to create method_TwoViewOptimizer";
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 使用OpenGVSimulator生成场景数据并创建真值
        std::pair<DataPtr, DataPtr> GenerateSceneAndGroundTruth(const SceneConfig &config)
        {
            // 创建OpenGV仿真器
            auto simulator = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("opengv_simulator"));
            EXPECT_TRUE(simulator != nullptr) << "Failed to create OpenGV simulator";

            // 设置仿真器参数
            MethodOptions simulator_options{
                {"max_parallax", std::to_string(config.max_parallax)},
                {"max_rotation", std::to_string(config.max_rotation)},
                {"num_points", std::to_string(config.num_points)},
                {"noise_level", std::to_string(config.noise_level)},
                {"outlier_fraction", std::to_string(config.outlier_fraction)},
                {"random_seed", std::to_string(config.random_seed)}};

            simulator->SetMethodOptions(simulator_options);

            // 运行仿真器生成数据
            auto simulator_data = simulator->Build();

            auto data_package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
            EXPECT_TRUE(data_package != nullptr) << "Simulator data is not a DataPackage";

            // 使用字典式访问方式获取method_LiRP数据和真值数据
            auto lirp_data = (*data_package)["method_LiRP"];
            auto gt_data = (*data_package)["ground_truth"];

            EXPECT_TRUE(lirp_data != nullptr) << "Failed to get LiRP data from simulator";
            EXPECT_TRUE(gt_data != nullptr) << "Failed to get ground truth data from simulator";

            return std::make_pair(lirp_data, gt_data);
        }

        // 使用LiRP方法计算初始位姿估计，并可选参与评估
        DataPtr ComputeInitialPoseWithLiRP(DataPtr lirp_data, DataPtr gt_data, const SceneConfig &scene_config, bool enable_iter_display = true, const std::string &profile_commit = "", bool enable_evaluation = true)
        {
            // 设置LiRP输入数据
            lirp_method_->SetRequiredData(lirp_data);

            // 配置LiRP选项 - 可选启用评估器
            MethodOptions lirp_options{
                {"enable_evaluator", enable_evaluation ? "true" : "false"},                            // 根据参数控制是否启用评估器
                {"ProfileCommit", profile_commit.empty() ? scene_config.config_name : profile_commit}, // 使用相同的ProfileCommit
                {"view_i", "0"},
                {"view_j", "1"},
                {"log_level", "0"},                                       // 减少日志输出
                {"iter_echo_on", enable_iter_display ? "true" : "false"}, // 根据参数控制迭代显示
                {"identify_mode", "PPO"},                                 // 使用PPO残差
                {"compute_mode", "true"},
                {"use_opt_mode", "false"},
                {"use_median_cost", "true"}};

            lirp_method_->SetMethodOptions(lirp_options);

            // 设置算法名称用于评估器（LiRP初值）- 仅在启用评估时执行
            if (enable_evaluation)
            {
                std::string lirp_algorithm_name = "LiRP_initial";
                auto lirp_profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(lirp_method_);
                if (lirp_profiler)
                {
                    lirp_profiler->SetEvaluatorAlgorithm(lirp_algorithm_name);
                    lirp_profiler->SetGTData(gt_data); // 提供真值数据
                }
            }

            // 运行LiRP获取初始位姿
            std::cout << "开始计算LiRP初值..." << std::endl;
            auto lirp_result = lirp_method_->Build();
            EXPECT_TRUE(lirp_result != nullptr) << "Failed to compute initial pose with LiRP";

            if (lirp_result)
            {
                if (enable_evaluation)
                {
                    std::cout << "LiRP初值计算成功，已加入评估系统" << std::endl;
                }
                else
                {
                    std::cout << "LiRP初值计算成功，仅用于优化器初始化" << std::endl;
                }

                // 手动评估LiRP初值的精度（用于控制台输出）
                auto lirp_pose_data = std::dynamic_pointer_cast<DataMap<RelativePose>>(lirp_result);
                if (lirp_pose_data && gt_data)
                {
                    auto eval_status = lirp_pose_data->EvaluateRelativePose(gt_data);
                    if (eval_status.is_successful)
                    {
                        auto rot_errors = eval_status.GetResults("rotation_error");
                        auto trans_errors = eval_status.GetResults("translation_error");
                        if (!rot_errors.empty() && !trans_errors.empty())
                        {
                            std::cout << "LiRP初值误差 - 旋转: " << std::fixed << std::setprecision(6)
                                      << rot_errors[0] << " (度), 平移: " << trans_errors[0] << std::endl;
                        }
                    }
                }
            }

            return lirp_result;
        }

        // 使用TwoViewOptimizer优化位姿
        bool RunTwoViewOptimizerWithEvaluator(const OptimizerConfig &opt_config,
                                              const SceneConfig &scene_config,
                                              DataPtr lirp_data,
                                              DataPtr initial_pose,
                                              DataPtr gt_data,
                                              const std::map<std::string, std::pair<double, double>> &loss_threshold_map = {},
                                              bool enable_iter_display = true,
                                              const std::string &profile_commit = "")
        {
            // 创建TwoViewOptimizer的输入数据包
            auto optimizer_package = std::make_shared<DataPackage>();
            (*optimizer_package)["data_sample"] = lirp_data; // bearing pairs数据
            (*optimizer_package)["data_map"] = initial_pose; // 初始位姿估计

            optimizer_method_->SetRequiredData(optimizer_package);

            // 配置TwoViewOptimizer选项
            MethodOptions optimizer_options{
                {"enable_evaluator", "true"},
                {"ProfileCommit", profile_commit.empty() ? scene_config.config_name : profile_commit},
                {"view_i", "0"},
                {"view_j", "1"},
                {"log_level", "1"},                                       // 显示优化过程
                {"iter_echo_on", enable_iter_display ? "true" : "false"}, // 根据参数控制迭代显示

                // 优化器配置
                {"optimizer_type", opt_config.optimizer_type},
                {"residual_type", opt_config.residual_type},
                {"loss_type", opt_config.loss_type},

                // 优化参数
                {"max_iterations", "100"},
                {"convergence_threshold", "1e-8"},

                // Eigen LM 特定参数
                {"eigen_lm_ftol", "1e-8"},
                {"eigen_lm_xtol", "1e-8"},
                {"eigen_lm_maxfev", "2000"}};

            // 根据残差函数类型查找对应的损失函数阈值
            auto threshold_iter = loss_threshold_map.find(opt_config.residual_type);
            if (threshold_iter != loss_threshold_map.end())
            {
                const auto &[huber_threshold, cauchy_threshold] = threshold_iter->second;

                // 设置显式损失函数阈值
                optimizer_options["huber_threshold_explicit"] = std::to_string(huber_threshold);
                optimizer_options["cauchy_threshold_explicit"] = std::to_string(cauchy_threshold);

                std::cout << "  使用显式阈值: Huber=" << huber_threshold << ", Cauchy=" << cauchy_threshold << std::endl;
            }
            else
            {
                // 使用默认的基于角度计算的阈值
                optimizer_options["reproj_theta_threshold_deg"] = "0.57";
                std::cout << "  使用角度基准阈值: " << 0.57 << "度" << std::endl;
            }

            optimizer_method_->SetMethodOptions(optimizer_options);

            // 设置算法名称用于评估器
            std::string algorithm_name = opt_config.config_name;
            auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(optimizer_method_);
            if (profiler)
            {
                profiler->SetEvaluatorAlgorithm(algorithm_name);
                profiler->SetGTData(gt_data);
            }

            // 运行优化器
            std::cout << "  开始优化迭代..." << std::endl;
            auto result = optimizer_method_->Build();

            if (result)
            {
                std::cout << "  优化完成！" << std::endl;
                // 评估优化后的结果
                auto optimized_pose_data = std::dynamic_pointer_cast<DataMap<RelativePose>>(result);
                if (optimized_pose_data && gt_data)
                {
                    auto eval_status = optimized_pose_data->EvaluateRelativePose(gt_data);
                    if (eval_status.is_successful)
                    {
                        auto rot_errors = eval_status.GetResults("rotation_error");
                        auto trans_errors = eval_status.GetResults("translation_error");
                        if (!rot_errors.empty() && !trans_errors.empty())
                        {
                            std::cout << "  优化后误差 - 旋转: " << std::fixed << std::setprecision(6)
                                      << rot_errors[0] << " (度), 平移: " << trans_errors[0] << std::endl;
                        }
                    }
                }
            }
            else
            {
                std::cout << "  优化失败！" << std::endl;
            }

            return result != nullptr;
        }

        // 显示评估结果
        void ShowEvaluationResults()
        {
            std::cout << "\n"
                      << std::string(80, '=') << std::endl;
            std::cout << "TWOVIEWOPTIMIZER 综合评估结果 (包含LiRP初值 vs 优化后结果)" << std::endl;
            std::cout << std::string(80, '=') << std::endl;

            // // 1. 打印所有评估报告
            // std::cout << "\n1. 所有评估报告:" << std::endl;
            // Interface::EvaluatorManager::PrintAllEvaluationReports();

            // // 2. 打印算法对比（特别关注LiRP_initial与其他算法的对比）
            // auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
            // for (const auto &eval_type : eval_types)
            // {
            //     auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

            //     // 检查是否包含LiRP_initial
            //     bool has_lirp_initial = std::find(algorithms.begin(), algorithms.end(), "LiRP_initial") != algorithms.end();

            //     if (has_lirp_initial && algorithms.size() > 1)
            //     {
            //         std::cout << "\n2. 算法性能对比 (" << eval_type << "):" << std::endl;
            //         std::cout << "   注意: LiRP_initial 为初值，其他为优化后结果" << std::endl;

            //         for (const auto &algorithm : algorithms)
            //         {
            //             auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
            //             for (const auto &metric : metrics)
            //             {
            //                 std::cout << "\n   指标: " << eval_type << "::" << metric << std::endl;
            //                 Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
            //             }
            //         }
            //     }
            //     else
            //     {
            //         // 如果没有LiRP_initial，按原来的方式显示
            //         for (const auto &algorithm : algorithms)
            //         {
            //             auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
            //             for (const auto &metric : metrics)
            //             {
            //                 std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
            //                 Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
            //             }
            //         }
            //     }
            // }

            // 3. 导出CSV文件和可视化
            TestCSVExport();

            std::cout << std::string(80, '=') << std::endl;
        }

        // 测试CSV导出和可视化功能
        void TestCSVExport()
        {
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();

            for (const auto &eval_type : eval_types)
            {
                // 获取当前测试信息
                const ::testing::TestInfo *const test_info =
                    ::testing::UnitTest::GetInstance()->current_test_info();
                std::string test_name = std::string(test_info->test_case_name()) + "_" +
                                        std::string(test_info->name());

                // 添加时间戳确保唯一性
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);

                std::ostringstream timestamp;
                timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

                std::string unique_dir_name = test_name + "_" + timestamp.str();

                // 创建测试输出目录
                std::filesystem::path test_output_dir = unique_dir_name;
                std::filesystem::create_directories(test_output_dir);

                std::cout << "\n=== 测试CSV导出和可视化功能 ===" << std::endl;

                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

                // 测试导出详细统计
                for (const auto &algorithm : algorithms)
                {
                    std::filesystem::path detailed_path = test_output_dir / (eval_type + "_" + algorithm + "_detailed.csv");
                    bool detail_success = Interface::EvaluatorManager::ExportDetailedStatsToCSV(eval_type, algorithm, detailed_path);
                    std::cout << "Export detailed stats for " << eval_type << "::" << algorithm << ": "
                              << (detail_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                // 测试单个指标导出和可视化
                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的对比表格..." << std::endl;

                        // 所有统计类型导出
                        std::filesystem::path all_stats_path = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        bool all_stats_success = Interface::EvaluatorManager::ExportMetricAllStatsToCSV(
                            eval_type, metric, all_stats_path);
                        std::cout << "Export all stats for " << eval_type << "::" << metric << ": "
                                  << (all_stats_success ? "SUCCESS" : "FAILED") << std::endl;

                        // 一键导出CSV并生成图表
                        std::filesystem::path combined_dir = test_output_dir / "combined_output";
                        std::vector<std::string> combined_stat_types = {"Mean", "Median", "Min", "Max"};

                        bool combined_success = Interface::EvaluatorManager::ExportMetricWithVisualization(
                            eval_type, metric, combined_dir, combined_stat_types);

                        std::cout << "Export CSV with visualization for " << eval_type << "::" << metric << ": "
                                  << (combined_success ? "SUCCESS" : "FAILED") << std::endl;
                    }
                }

                // 导出原始评估值
                std::filesystem::path raw_values_dir = test_output_dir / "raw_values";
                bool raw_success = Interface::EvaluatorManager::ExportAllRawValuesToCSV(eval_type, raw_values_dir, "");
                std::cout << "Export raw values for " << eval_type << ": "
                          << (raw_success ? "SUCCESS" : "FAILED") << std::endl;

                std::cout << "\n=== 可视化绘图功能测试完成 ===" << std::endl;
                std::cout << "图表文件保存在以下目录:" << std::endl;
                std::cout << "  - " << test_output_dir / "combined_output" / "plots" << std::endl;
            }
        }

        // 成员变量
        Interface::MethodPresetPtr lirp_method_;
        Interface::MethodPresetPtr optimizer_method_;
    };

    // // 综合测试：不同残差函数、损失函数、优化器组合的性能评估
    // TEST_F(TestTwoViewOptimizer, ComprehensiveOptimizerEvaluationTest)
    // {
    //     const int NUM_REPEATS = 50; // 重复测试次数

    //     std::cout << "\n=== TwoViewOptimizer 综合性能评估测试 ===" << std::endl;
    //     std::cout << "测试不同残差函数、损失函数、优化器类型的组合效果" << std::endl;
    //     std::cout << "包含: LiRP初值评估 + 各种优化器配置的优化后结果评估" << std::endl;
    //     std::cout << "注意: 已启用迭代显示功能，可以监控优化过程中的残差变化" << std::endl;

    //     // 定义针对不同残差函数的损失函数阈值映射表
    //     // 格式：{residual_type, {huber_threshold, cauchy_threshold}}
    //     std::map<std::string, std::pair<double, double>> loss_threshold_map = {
    //         {"ppo_opengv", {0.0016, 0.008}},     // PPO OpenGV: 6D残差，较严格阈值
    //         {"ppo", {0.0016, 0.008}},            // 标准PPO: 标量残差，中等阈值
    //         {"ppo_angle_sqrt", {0.0015, 0.008}}, // PPO角度: 角度残差，较严格阈值
    //         {"sampson", {0.0016, 0.008}},        // Sampson: 对outlier敏感，较宽松阈值
    //         {"ba", {0.002, 0.001}},              // Bundle Adjustment: 重投影误差，严格阈值
    //         // {"coplanar", {0.03, 0.015}},          // 共面约束: 几何误差，中等阈值
    //         // {"kneip", {0.02, 0.01}},              // Kneip: 5点算法，中等阈值
    //         // {"opengv", {0.01, 0.005}},            // OpenGV标准: 方向向量，严格阈值
    //         // {"ligt", {0.025, 0.012}},             // Linear Global Translation: 稍宽松
    //         // {"lirt", {0.02, 0.01}},               // Linear RT: 中等阈值
    //         // {"ppog", {0.015, 0.008}}              // PPOG: 几何残差，较严格阈值
    //     };

    //     std::cout << "\n=== 损失函数阈值配置表 ===" << std::endl;
    //     std::cout << std::left << std::setw(15) << "残差函数" << std::setw(15) << "Huber阈值" << std::setw(15) << "Cauchy阈值" << "说明" << std::endl;
    //     std::cout << std::string(65, '-') << std::endl;
    //     for (const auto &[residual_type, thresholds] : loss_threshold_map)
    //     {
    //         std::cout << std::left << std::setw(15) << residual_type
    //                   << std::setw(15) << std::fixed << std::setprecision(3) << thresholds.first
    //                   << std::setw(15) << std::fixed << std::setprecision(3) << thresholds.second;

    //         // 添加说明
    //         if (residual_type == "ppo_opengv")
    //             std::cout << "6D残差向量，较严格";
    //         else if (residual_type == "sampson")
    //             std::cout << "对outlier敏感，较宽松";
    //         else if (residual_type == "ba")
    //             std::cout << "重投影误差，严格";
    //         else
    //             std::cout << "中等阈值";

    //         std::cout << std::endl;
    //     }
    //     std::cout << std::string(65, '=') << std::endl;

    //     // 定义测试场景配置（与阈值测试保持一致：300特征点）
    //     SceneConfig test_scene{"comprehensive_test", 2.0, 0.3, 500, 1.0, 0.1, 42};

    //     // 定义不同残差函数类型（按重要性排序）
    //     std::vector<std::string> residual_types = {
    //         "ppo_opengv",     // 6D残差向量，推荐
    //         "ppo",            // 标准PPO残差
    //         "ppo_angle_sqrt", // PPO角度误差残差（数值稳定）
    //         // "ppo_angle",      // PPO角度+均匀性复合残差（新增，多目标优化）
    //         // // "ppo_angle_uniform", // PPO角度+均匀性复合残差（新增，多目标优化）
    //         "sampson", // Sampson残差
    //         "ba",      // Bundle Adjustment残差
    //         // "coplanar", // 共面残差
    //         // "kneip",    // Kneip残差（已修复）
    //         // "opengv", // OpenGV标准残差
    //         // "ligt",     // Linear Global Translation残差
    //         // "lirt",     // Linear Rotation Translation残差
    //         // "ppog"      // PPOG残差
    //         // 扩展类型（可选）：ppo_invd, ligt_direct, ligt_d3
    //     };

    //     // 定义不同损失函数类型
    //     std::vector<std::string> loss_types = {
    //         // "l2",    // L2范数损失
    //         "huber", // Huber鲁棒损失
    //         "cauchy" // Cauchy鲁棒损失
    //     };

    //     // 定义不同优化器类型
    //     std::vector<std::string> optimizer_types = {
    //         "eigen_lm", // Eigen LM优化器（推荐）
    //         "ceres"     // Ceres优化器（简化实现）
    //         // 注意：gauss_newton, dog_leg 暂未实现
    //     };

    //     // 计算总配置数
    //     size_t total_configs = residual_types.size() * loss_types.size() * optimizer_types.size();
    //     std::cout << "优化器测试配置: "
    //               << residual_types.size() << " 个残差函数 × "
    //               << loss_types.size() << " 个损失函数 × "
    //               << optimizer_types.size() << " 个优化器 = "
    //               << total_configs << " 个配置组合" << std::endl;
    //     std::cout << "重复次数: " << NUM_REPEATS << ", 总测试数: " << (total_configs * NUM_REPEATS) << std::endl;
    //     std::cout << "注意: 每次重复还会包含1个LiRP初值评估" << std::endl;
    //     std::cout << "总评估次数: " << (total_configs * NUM_REPEATS) << " (优化器) + " << NUM_REPEATS << " (LiRP初值) = " << ((total_configs + 1) * NUM_REPEATS) << std::endl;

    //     // 对每次重复进行测试
    //     for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
    //     {
    //         std::cout << "\n"
    //                   << std::string(60, '=') << std::endl;
    //         std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << " [迭代显示: 启用]" << std::endl;
    //         std::cout << std::string(60, '=') << std::endl;

    //         // 为每次重复修改随机种子
    //         SceneConfig current_scene = test_scene;
    //         current_scene.random_seed = 42 + repeat;

    //         // 生成场景数据和真值
    //         auto [lirp_data, gt_data] = GenerateSceneAndGroundTruth(current_scene);
    //         if (!lirp_data || !gt_data)
    //         {
    //             std::cerr << "Failed to generate scene data for repeat " << (repeat + 1) << std::endl;
    //             continue;
    //         }

    //         // 使用LiRP计算初始位姿（参与评估，用于对比初值与优化后结果）
    //         auto initial_pose = ComputeInitialPoseWithLiRP(lirp_data, gt_data, current_scene, true, "", true);
    //         if (!initial_pose)
    //         {
    //             std::cerr << "Failed to compute initial pose for repeat " << (repeat + 1) << std::endl;
    //             continue;
    //         }

    //         // 测试所有配置组合
    //         size_t config_counter = 0;
    //         for (const auto &residual_type : residual_types)
    //         {
    //             for (const auto &loss_type : loss_types)
    //             {
    //                 for (const auto &optimizer_type : optimizer_types)
    //                 {
    //                     config_counter++;

    //                     // 创建配置名称
    //                     std::string config_name = residual_type + "_" + loss_type + "_" + optimizer_type;

    //                     OptimizerConfig config{config_name, residual_type, loss_type, optimizer_type};

    //                     std::cout << "\n[" << config_counter << "/" << total_configs << "] "
    //                               << "测试配置: " << config_name << std::endl;
    //                     std::cout << "  残差函数: " << residual_type
    //                               << ", 损失函数: " << loss_type
    //                               << ", 优化器: " << optimizer_type << std::endl;
    //                     std::cout << "  迭代显示: 启用, 监控优化过程中的残差变化" << std::endl;

    //                     bool success = RunTwoViewOptimizerWithEvaluator(config, current_scene, lirp_data, initial_pose, gt_data, loss_threshold_map, true);

    //                     std::cout << " -> " << (success ? "成功" : "失败") << std::endl;

    //                     if (!success)
    //                     {
    //                         std::cerr << "    警告: 配置 " << config_name << " 优化失败" << std::endl;
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     std::cout << "\n=== TwoViewOptimizer 综合性能评估测试完成 ===" << std::endl;
    // }

    // 损失函数阈值性能测试：支持多种residual_type的参数调优
    TEST_F(TestTwoViewOptimizer, LossFunctionThresholdPerformanceTest)
    {
        const int NUM_REPEATS = 20;          // 重复测试次数（减少以便快速测试）
        const int NUM_THRESHOLD_POINTS = 200; // 阈值分割点数
        const int NUM_THREADS = 10;           // OpenMP线程数（匹配用户需求的5核）

#ifdef USE_OPENMP
        // 设置OpenMP线程数
        omp_set_num_threads(NUM_THREADS);
        std::cout << "\n=== OpenMP配置 ===" << std::endl;
        std::cout << "OpenMP线程数: " << omp_get_max_threads() << std::endl;
        std::cout << "OpenMP支持: 已启用" << std::endl;
#else
        std::cout << "\n=== OpenMP配置 ===" << std::endl;
        std::cout << "OpenMP支持: 未启用 (串行执行)" << std::endl;
#endif

        // 设置CSV精度为10，确保1e-8级别的数值能正确显示
        Interface::EvaluatorManager::SetCSVPrecision(10);
        std::cout << "\n=== TwoViewOptimizer 损失函数阈值性能测试 ===" << std::endl;
        std::cout << "CSV精度已设置为10位，支持1e-10级别数值的精确显示" << std::endl;
        std::cout << "支持多种残差函数的损失函数阈值调优" << std::endl;
        std::cout << "测试 Huber 和 Cauchy 损失函数在不同阈值下的性能表现" << std::endl;
        std::cout << "阈值分割方式: 等比例分割 (而非等距分割)" << std::endl;
        std::cout << "迭代显示: 关闭 (专注于性能数据收集)" << std::endl;
#ifdef USE_OPENMP
        std::cout << "并行处理: 使用 " << NUM_THREADS << " 个OpenMP线程（启用线程安全保护）" << std::endl;
#else
        std::cout << "并行处理: 串行执行（OpenMP未启用）" << std::endl;
#endif

        // 定义不同residual_type的阈值范围配置
        // 格式：{residual_type, {threshold_min, threshold_max, description}}
        std::map<std::string, std::tuple<double, double, std::string>> threshold_ranges = {
            // PPO系列残差函数
            {"ppo_opengv", {1e-5, 0.1, "PPO OpenGV: 6D残差，值范围较大"}},
            {"ppo", {1e-5, 0.1, "PPO标准: 标量残差，值范围中等"}},

            // 角度残差函数系列
            {"ppo_angle_sqrt", {1e-5, 0.1, "PPO角度sqrt: sqrt(1-cos(angle))，数值稳定"}},
            {"ppo_angle", {1e-8, 1e-3, "PPO角度: 1-cos(angle)，值极小需要精细阈值"}},
            {"ppo_angle_rad", {1e-5, 0.1, "PPO角度弧度: 直接角度值，范围0-π"}},
            // {"ppo_angle_sin", {0.001, 0.1, "PPO角度sin: sin(angle)，范围0-1"}},
            // {"ppo_angle_2sin", {0.001, 0.1, "PPO角度2sin: 2sin(angle/2)，范围0-2"}},

            // 传统残差函数
            {"sampson", {0.0001, 0.1, "Sampson距离: 几何距离，值范围较大"}},
            {"ba", {0.0001, 0.1, "Bundle Adjustment: 重投影误差，像素级误差"}}};

        // 可选择测试的residual_type列表（用户可以根据需要调整）
        std::vector<std::string> test_residual_types = {
            "ppo_opengv",     // 主要测试对象
            "ppo",            // 标准PPO
            "ppo_angle_sqrt", // 重要的角度残差
            "ppo_angle",      // 原始角度残差
            "ppo_angle_rad",  // 弧度制角度
            // "ppo_angle_sin",  // sin角度
            // "ppo_angle_2sin", // 2sin角度
            "sampson", // Sampson距离
            "ba"       // Bundle Adjustment
        };

        // 验证所有测试的residual_type都有对应的阈值范围配置
        for (const auto &residual_type : test_residual_types)
        {
            if (threshold_ranges.find(residual_type) == threshold_ranges.end())
            {
                std::cerr << "错误: residual_type '" << residual_type << "' 没有对应的阈值范围配置" << std::endl;
                FAIL() << "Missing threshold range configuration for " << residual_type;
            }
        }

        std::vector<std::string> loss_types = {"cauchy"};
        std::vector<std::string> optimizer_types = {"eigen_lm", "ceres"};

        // 计算总配置数
        size_t total_configs = 0;
        for (const auto &residual_type : test_residual_types)
        {
            total_configs += NUM_THRESHOLD_POINTS * loss_types.size() * optimizer_types.size();
        }

        std::cout << "\n=== 阈值测试配置总览 ===" << std::endl;
        std::cout << "测试的残差函数类型: " << test_residual_types.size() << " 个" << std::endl;
        for (const auto &residual_type : test_residual_types)
        {
            auto &[min_th, max_th, desc] = threshold_ranges[residual_type];
            std::cout << "  - " << residual_type << ": [" << min_th << ", " << max_th << "] - " << desc << std::endl;
        }
        std::cout << "阈值点数: " << NUM_THRESHOLD_POINTS << " 个/residual_type" << std::endl;
        std::cout << "损失函数: " << loss_types.size() << " 个 (huber, cauchy)" << std::endl;
        std::cout << "优化器: " << optimizer_types.size() << " 个 (eigen_lm, ceres)" << std::endl;
        std::cout << "总配置数: " << total_configs << " 个" << std::endl;
        std::cout << "重复次数: " << NUM_REPEATS << ", 总测试数: " << (total_configs * NUM_REPEATS) << std::endl;

        // 定义测试场景配置
        SceneConfig test_scene{"threshold_test", 2.0, 0.3, 300, 1.0, 0.1, 42};

        // 对每个residual_type进行测试
        for (const auto &residual_type : test_residual_types)
        {
            auto &[threshold_min, threshold_max, description] = threshold_ranges[residual_type];

            std::cout << "\n"
                      << std::string(80, '=') << std::endl;
            std::cout << "测试残差函数: " << residual_type << std::endl;
            std::cout << "描述: " << description << std::endl;
            std::cout << "阈值范围: [" << threshold_min << ", " << threshold_max << "]" << std::endl;
            std::cout << std::string(80, '=') << std::endl;

            // 生成该residual_type的阈值序列（等比例分割）
            const double threshold_ratio = std::pow(threshold_max / threshold_min, 1.0 / (NUM_THRESHOLD_POINTS - 1));
            std::vector<double> thresholds;
            for (int i = 0; i < NUM_THRESHOLD_POINTS; ++i)
            {
                double threshold = threshold_min * std::pow(threshold_ratio, i);
                thresholds.push_back(threshold);
            }

            std::cout << "阈值比率: " << threshold_ratio << " (等比例分割)" << std::endl;
            std::cout << "前5个阈值: ";
            for (int i = 0; i < std::min(5, (int)thresholds.size()); ++i)
            {
                std::cout << std::fixed << std::setprecision(10) << thresholds[i] << " ";
            }
            std::cout << std::endl;
            std::cout << "后5个阈值: ";
            for (int i = std::max(0, (int)thresholds.size() - 5); i < (int)thresholds.size(); ++i)
            {
                std::cout << std::fixed << std::setprecision(10) << thresholds[i] << " ";
            }
            std::cout << std::endl;

            // 对每次重复进行测试 - 使用OpenMP并行处理
#ifdef USE_OPENMP
#pragma omp parallel for schedule(dynamic) shared(residual_type, test_scene, threshold_ranges, loss_types, optimizer_types, thresholds)
#endif
            for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
            {
                // 线程安全的输出（使用临界区）
#ifdef USE_OPENMP
#pragma omp critical(output)
#endif
                {
                    std::cout << "\n--- " << residual_type << " 重复 " << (repeat + 1) << "/" << NUM_REPEATS
                              << " [线程ID: "
#ifdef USE_OPENMP
                              << omp_get_thread_num()
#else
                              << "0"
#endif
                              << "] ---" << std::endl;
                }

                // 为每次重复修改随机种子（确保每个线程使用不同的种子）
                SceneConfig current_scene = test_scene;
                current_scene.random_seed = 42 + repeat * 1000; // 增大种子差距确保随机性

                // 生成场景数据和真值（线程安全 - 使用临界区保护共享资源）
                std::pair<DataPtr, DataPtr> scene_data_pair;
#ifdef USE_OPENMP
#pragma omp critical(shared_methods)
#endif
                {
                    scene_data_pair = GenerateSceneAndGroundTruth(current_scene);
                }

                auto [lirp_data, gt_data] = scene_data_pair;
                if (!lirp_data || !gt_data)
                {
#ifdef USE_OPENMP
#pragma omp critical(output)
#endif
                    {
                        std::cerr << "Failed to generate scene data for repeat " << (repeat + 1) << std::endl;
                    }
                    continue;
                }

                // 使用LiRP计算初始位姿（线程安全 - 使用临界区保护共享资源）
                DataPtr initial_pose;
#ifdef USE_OPENMP
#pragma omp critical(shared_methods)
#endif
                {
                    initial_pose = ComputeInitialPoseWithLiRP(lirp_data, gt_data, current_scene, false, "", false);
                }

                if (!initial_pose)
                {
#ifdef USE_OPENMP
#pragma omp critical(output)
#endif
                    {
                        std::cerr << "Failed to compute initial pose for repeat " << (repeat + 1) << std::endl;
                    }
                    continue;
                }

                // 测试所有阈值和配置组合
                size_t config_counter = 0;
                for (const auto &loss_type : loss_types)
                {
                    for (const auto &optimizer_type : optimizer_types)
                    {
                        // 线程安全的组合输出
#ifdef USE_OPENMP
#pragma omp critical(output)
#endif
                        {
                            std::cout << "\n--- 测试 " << residual_type << " + " << loss_type << " + " << optimizer_type
                                      << " 组合 [线程ID: "
#ifdef USE_OPENMP
                                      << omp_get_thread_num()
#else
                                      << "0"
#endif
                                      << "] ---" << std::endl;
                        }

                        for (size_t threshold_idx = 0; threshold_idx < thresholds.size(); ++threshold_idx)
                        {
                            double threshold = thresholds[threshold_idx];
                            config_counter++;

                            // 创建配置名称（不包含阈值信息，阈值由ProfileCommit处理）
                            std::string config_name = residual_type + "_" + loss_type + "_" + optimizer_type;

                            // 创建阈值专用的ProfileCommit (evaluator用于绘图的x轴)
                            std::ostringstream threshold_commit_stream;
                            threshold_commit_stream << "threshold_" << std::fixed << std::setprecision(10) << threshold;
                            std::string threshold_commit = threshold_commit_stream.str();

                            OptimizerConfig config{config_name, residual_type, loss_type, optimizer_type};

                            // 每20个阈值显示一次进度（线程安全）
                            bool should_print_progress = (threshold_idx % 20 == 0 || threshold_idx == thresholds.size() - 1);
                            if (should_print_progress)
                            {
#ifdef USE_OPENMP
#pragma omp critical(output)
#endif
                                {
                                    std::cout << "[" << config_counter << "/" << (thresholds.size() * loss_types.size() * optimizer_types.size()) << "] "
                                              << "阈值: " << std::fixed << std::setprecision(10) << threshold
                                              << " (" << (threshold_idx + 1) << "/" << thresholds.size() << ")"
                                              << " [迭代显示: 关闭] [线程ID: "
#ifdef USE_OPENMP
                                              << omp_get_thread_num()
#else
                                              << "0"
#endif
                                              << "]";
                                }
                            }

                            // 创建阈值映射（同时设置huber和cauchy为相同阈值）
                            std::map<std::string, std::pair<double, double>> threshold_map;
                            if (loss_type == "huber")
                            {
                                threshold_map[residual_type] = {threshold, 0.005}; // cauchy使用默认值
                            }
                            else if (loss_type == "cauchy")
                            {
                                threshold_map[residual_type] = {0.01, threshold}; // huber使用默认值
                            }

                            // 运行优化器（线程安全 - 使用临界区保护共享资源）
                            bool success = false;
#ifdef USE_OPENMP
#pragma omp critical(shared_methods)
#endif
                            {
                                success = RunTwoViewOptimizerWithEvaluator(config, current_scene, lirp_data, initial_pose, gt_data, threshold_map, false, threshold_commit);
                            }

                            // 每20个阈值显示一次结果（线程安全）
                            if (should_print_progress)
                            {
#ifdef USE_OPENMP
#pragma omp critical(output)
#endif
                                {
                                    std::cout << " -> " << (success ? "成功" : "失败") << std::endl;
                                }
                            }

                            if (!success)
                            {
#ifdef USE_OPENMP
#pragma omp critical(output)
#endif
                                {
                                    std::cerr << "    警告: 配置 " << config_name << " 优化失败 (阈值=" << threshold
                                              << ") [线程ID: "
#ifdef USE_OPENMP
                                              << omp_get_thread_num()
#else
                                              << "0"
#endif
                                              << "]" << std::endl;
                                }
                            }
                        }
                    }
                }
            }
        }

        std::cout << "\n=== TwoViewOptimizer 损失函数阈值性能测试完成 ===" << std::endl;
        std::cout << "共测试了 " << test_residual_types.size() << " 种残差函数" << std::endl;
        std::cout << "每种残差函数测试了 " << NUM_THRESHOLD_POINTS << " 个阈值点（等比例分割）" << std::endl;
        std::cout << "重复测试次数: " << NUM_REPEATS << " 次" << std::endl;
#ifdef USE_OPENMP
        std::cout << "并行处理: 使用 " << NUM_THREADS << " 个OpenMP线程" << std::endl;
#else
        std::cout << "并行处理: 串行执行（OpenMP未启用）" << std::endl;
#endif
        std::cout << "结果将在评估报告中显示不同残差函数和阈值下的性能曲线" << std::endl;

        // 重置CSV精度为默认值，避免影响其他测试
        Interface::EvaluatorManager::ResetCSVPrecision();
        std::cout << "CSV精度已重置为默认值" << std::endl;
    }

} // namespace
