
21:56:44: Starting /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/bin/test_all...
Running main() from /tmp/googletest-20250207-4735-e28tfc/googletest-1.16.0/googletest/src/gtest_main.cc
[==========] Running 6 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 6 tests from TestLiRPMethod
[ RUN      ] TestLiRPMethod.BasicTest

=== Test Configuration: basic_test ===
Points: 50, Noise: 0, Outliers: 0
the random position is:
 0.255501
 0.203192
-0.958882

the random rotation is:
 0.961269 -0.211117  0.177179
 0.211266  0.977258 0.0182426
-0.177001 0.0198959   0.98401

the noise in the data is:
0
the outlier fraction is:
0
Results for basic_test:
  Rotation Error: 2.7515e-15
  Translation Error: 8.45685e-15
  Runtime: 0.208 ms

============================================================
EVALUATION SYSTEM TEST RESULTS
============================================================

1. All Evaluation Reports:
[EvaluatorManager] No evaluation data available

1. Testing CSV Export Functions:
============================================================
[       OK ] TestLiRPMethod.BasicTest (1 ms)
[ RUN      ] TestLiRPMethod.NoiseLevel_Tests

=== Test Configuration: noise_0.000000 ===
Points: 100, Noise: 0, Outliers: 0
the random position is:
 0.265112
-0.268405
 0.924453

the random rotation is:
 0.962199 0.0643059  0.264647
 -0.11514  0.976663  0.181306
-0.246812 -0.204924  0.947148

the noise in the data is:
0
the outlier fraction is:
0
Results for noise_0.000000:
  Rotation Error: 9.1688e-16
  Translation Error: 1.37775e-15
  Runtime: 0.177 ms

[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed

=== Test Configuration: noise_0.001000 ===
Points: 100, Noise: 0.001, Outliers: 0
the random position is:
 0.271185
-0.195081
-0.725816

the random rotation is:
 0.986471  0.122574  0.108865
-0.144451   0.96391  0.223633
-0.077524 -0.236333  0.968575

the noise in the data is:
0.001
the outlier fraction is:
0
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for noise_0.001000:
  Rotation Error: 1.05227e-06
  Translation Error: 6.13256e-06
  Runtime: 0.192 ms


=== Test Configuration: noise_0.005000 ===
Points: 100, Noise: 0.005, Outliers: 0
the random position is:
 0.278354
 0.293533
-0.591341

the random rotation is:
  0.996336 -0.0405399  0.0753132
 0.0245009   0.978913   0.202804
-0.0819467  -0.200216   0.976319

the noise in the data is:
0.005
the outlier fraction is:
0
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for noise_0.005000:
  Rotation Error: 4.76475e-06
  Translation Error: 2.5331e-05
  Runtime: 0.18 ms


=== Test Configuration: noise_0.010000 ===
Points: 100, Noise: 0.01, Outliers: 0
the random position is:
0.284521
-0.05469
0.825653

the random rotation is:
  0.982261  -0.174761  0.0679834
  0.185384   0.959557  -0.211856
-0.0282099   0.220701   0.974933

the noise in the data is:
0.01
the outlier fraction is:
0
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for noise_0.010000:
  Rotation Error: 1.7558e-05
  Translation Error: 4.86522e-05
  Runtime: 0.165 ms


============================================================
EVALUATION SYSTEM TEST RESULTS
============================================================

1. All Evaluation Reports:
[EvaluatorManager] No evaluation data available

3. Testing CSV Export Functions:
============================================================
[       OK ] TestLiRPMethod.NoiseLevel_Tests (1 ms)
[ RUN      ] TestLiRPMethod.OutlierRatio_Tests

=== Test Configuration: outlier_0.000000 ===
Points: 100, Noise: 0.001, Outliers: 0
the random position is:
 0.291393
-0.564512
 0.247125

the random rotation is:
  0.965482  -0.247148 -0.0822299
   0.25724   0.954305   0.152086
 0.0408846  -0.167989   0.984941

the noise in the data is:
0.001
the outlier fraction is:
0
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for outlier_0.000000:
  Rotation Error: 4.3606e-07
  Translation Error: 3.27987e-06
  Runtime: 0.18 ms


=== Test Configuration: outlier_0.100000 ===
Points: 100, Noise: 0.001, Outliers: 0.1
the random position is:
 0.297247
-0.174246
-0.560094

the random rotation is:
 0.974926 -0.174731  0.137798
 0.192384  0.973028 -0.127302
-0.111838   0.15062  0.982245

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for outlier_0.100000:
  Rotation Error: 0.0936243
  Translation Error: 0.662439
  Runtime: 0.225 ms


=== Test Configuration: outlier_0.200000 ===
Points: 100, Noise: 0.001, Outliers: 0.2
the random position is:
    0.30404
0.000553658
  -0.694674

the random rotation is:
 0.948539 -0.163167  0.271386
  0.20396  0.970383 -0.129446
-0.242227  0.178137  0.953726

the noise in the data is:
0.001
the outlier fraction is:
0.2
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for outlier_0.200000:
  Rotation Error: 0.104272
  Translation Error: 1.01427
  Runtime: 0.194 ms

/Users/<USER>/Documents/PoMVG/po_core/tests/test_lirp_method.cpp:202: Failure
Expected: (t_error) < (1.0), actual: 1.0142699680490321 vs 1
Translation error too large for config: outlier_0.200000


=== Test Configuration: outlier_0.300000 ===
Points: 100, Noise: 0.001, Outliers: 0.3
the random position is:
 0.311303
0.0676211
 0.507065

the random rotation is:
  0.972555  0.0130837  -0.232304
 -0.029148   0.997403 -0.0658546
   0.23084  0.0708185   0.970411

the noise in the data is:
0.001
the outlier fraction is:
0.3
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for outlier_0.300000:
  Rotation Error: 0.178102
  Translation Error: 0.617887
  Runtime: 0.201 ms


============================================================
EVALUATION SYSTEM TEST RESULTS
============================================================

1. All Evaluation Reports:
[EvaluatorManager] No evaluation data available

3. Testing CSV Export Functions:
============================================================
[  FAILED  ] TestLiRPMethod.OutlierRatio_Tests (1 ms)
[ RUN      ] TestLiRPMethod.PointCount_Tests

=== Test Configuration: points_20 ===
Points: 20, Noise: 0.001, Outliers: 0.1
the random position is:
0.319834
-0.55619
0.116866

the random rotation is:
   0.985476   -0.169571 -0.00908638
   0.168852    0.984176  -0.0537253
  0.0180528   0.0514107    0.998514

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for points_20:
  Rotation Error: 0.0557127
  Translation Error: 0.344712
  Runtime: 0.08 ms


=== Test Configuration: points_50 ===
Points: 50, Noise: 0.001, Outliers: 0.1
the random position is:
0.324044
 0.21114
 0.63253

the random rotation is:
  0.979562 -0.0590581   0.192275
  0.107896   0.961035  -0.254501
 -0.169752   0.270045   0.947766

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for points_50:
  Rotation Error: 0.0263184
  Translation Error: 0.394751
  Runtime: 0.115 ms


=== Test Configuration: points_100 ===
Points: 100, Noise: 0.001, Outliers: 0.1
the random position is:
0.328928
 0.29072
0.130251

the random rotation is:
 0.914279  -0.21194 -0.345219
 0.281683  0.945074  0.165802
 0.291118 -0.248831  0.923761

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for points_100:
  Rotation Error: 0.142689
  Translation Error: 0.407497
  Runtime: 0.169 ms


=== Test Configuration: points_200 ===
Points: 200, Noise: 0.001, Outliers: 0.1
the random position is:
 0.334813
-0.792863
 0.345453

the random rotation is:
  0.989321   0.102898  -0.103231
 -0.103205   0.994657 0.00237616
  0.102923  0.0083031   0.994655

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for points_200:
  Rotation Error: 0.0624582
  Translation Error: 0.305823
  Runtime: 0.285 ms


============================================================
EVALUATION SYSTEM TEST RESULTS
============================================================

1. All Evaluation Reports:
[EvaluatorManager] No evaluation data available

3. Testing CSV Export Functions:
============================================================
[       OK ] TestLiRPMethod.PointCount_Tests (1 ms)
[ RUN      ] TestLiRPMethod.Parallax_Tests

=== Test Configuration: parallax_0.500000 ===
Points: 100, Noise: 0.001, Outliers: 0.1
the random position is:
0.171727
0.212427
0.266864

the random rotation is:
  0.981741   0.176817 -0.0701506
 -0.183378   0.977749  -0.101881
 0.0505755   0.112885    0.99232

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for parallax_0.500000:
  Rotation Error: 0.068486
  Translation Error: 0.373198
  Runtime: 0.175 ms


=== Test Configuration: parallax_1.000000 ===
Points: 100, Noise: 0.001, Outliers: 0.1
the random position is:
 0.349089
-0.867938
 0.569561

the random rotation is:
 0.944108  0.250961 -0.213725
-0.281195  0.951489  -0.12489
 0.172014  0.178008  0.968878

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for parallax_1.000000:
  Rotation Error: 0.143577
  Translation Error: 0.385375
  Runtime: 0.167 ms


=== Test Configuration: parallax_1.500000 ===
Points: 100, Noise: 0.001, Outliers: 0.1
the random position is:
  0.53225
-0.478802
  -1.2242

the random rotation is:
   0.98711  0.0413486   0.154608
-0.0738381    0.97475   0.210738
  -0.14199  -0.219438   0.965239

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for parallax_1.500000:
  Rotation Error: 0.119261
  Translation Error: 0.422223
  Runtime: 0.168 ms


=== Test Configuration: parallax_2.000000 ===
Points: 100, Noise: 0.001, Outliers: 0.1
the random position is:
 0.721343
-0.384023
  1.73087

the random rotation is:
 0.963061  0.268172 0.0244427
-0.268192  0.947036  0.176626
 0.024218 -0.176657  0.983974

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for parallax_2.000000:
  Rotation Error: 0.0931901
  Translation Error: 0.116582
  Runtime: 0.17 ms


============================================================
EVALUATION SYSTEM TEST RESULTS
============================================================

1. All Evaluation Reports:
[EvaluatorManager] No evaluation data available

3. Testing CSV Export Functions:
============================================================
[       OK ] TestLiRPMethod.Parallax_Tests (1 ms)
[ RUN      ] TestLiRPMethod.EvaluatorManager_FunctionalityTest

=== Test Configuration: func_test_1 ===
Points: 50, Noise: 0, Outliers: 0
the random position is:
  0.36723
0.0366551
0.0626975

the random rotation is:
  0.978439  -0.181523  0.0985189
  0.172736   0.980713  0.0914544
  -0.11322 -0.0724648   0.990924

the noise in the data is:
0
the outlier fraction is:
0
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for func_test_1:
  Rotation Error: 2.91764e-16
  Translation Error: 1.3552e-15
  Runtime: 0.121 ms


=== Test Configuration: func_test_2 ===
Points: 50, Noise: 0.001, Outliers: 0.1
the random position is:
0.372004
0.274706
0.981944

the random rotation is:
  0.981017 -0.0174747  -0.193133
 0.0434611    0.99041   0.131148
  0.188989  -0.137052   0.972368

the noise in the data is:
0.001
the outlier fraction is:
0.1
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for func_test_2:
  Rotation Error: 0.183925
  Translation Error: 0.522149
  Runtime: 0.112 ms


=== Test Configuration: func_test_3 ===
Points: 50, Noise: 0.005, Outliers: 0.2
the random position is:
0.376684
0.934303
0.833927

the random rotation is:
  0.962509   0.271212 0.00452851
 -0.270998   0.960761  0.0591441
 0.0116898 -0.0581539   0.998239

the noise in the data is:
0.005
the outlier fraction is:
0.2
[DataMap<RelativePose>] ComputeErrors failed
[method_LiRP] Evaluation failed for single data to single data
[method_LiRP] Warning: Evaluation failed
Results for func_test_3:
  Rotation Error: 0.0475113
  Translation Error: 0.215497
  Runtime: 0.111 ms


Testing evaluator management functions...
/Users/<USER>/Documents/PoMVG/po_core/tests/test_lirp_method.cpp:420: Failure
Value of: eval_types.empty()
  Actual: true
Expected: false
Should have evaluation types after running scenarios

Evaluator management functionality tests completed.

============================================================
EVALUATION SYSTEM TEST RESULTS
============================================================

1. All Evaluation Reports:
[EvaluatorManager] No evaluation data available

3. Testing CSV Export Functions:
============================================================
[  FAILED  ] TestLiRPMethod.EvaluatorManager_FunctionalityTest (0 ms)
[----------] 6 tests from TestLiRPMethod (8 ms total)

[----------] Global test environment tear-down
[==========] 6 tests from 1 test suite ran. (8 ms total)
[  PASSED  ] 4 tests.
[  FAILED  ] 2 tests, listed below:
[  FAILED  ] TestLiRPMethod.OutlierRatio_Tests
[  FAILED  ] TestLiRPMethod.EvaluatorManager_FunctionalityTest

 2 FAILED TESTS
21:56:46: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/bin/test_all 退出，退出代码: 1
 {1 ?} {2?}
