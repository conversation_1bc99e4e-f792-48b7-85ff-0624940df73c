/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/test_all.dir/experiment_helpers.o CMakeFiles/test_all.dir/random_generators.o CMakeFiles/test_all.dir/time_measurement.o CMakeFiles/test_all.dir/test_ransac_lirp.o -o test_all   -L/opt/homebrew/lib  -lpomvg_proto -lpo_core /opt/homebrew/lib/libgtest_main.a /opt/homebrew/lib/libgtest.a -lThreads::Threads
