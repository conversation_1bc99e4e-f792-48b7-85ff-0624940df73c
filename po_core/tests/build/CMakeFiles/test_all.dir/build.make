# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Documents/PoMVG/po_core/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Documents/PoMVG/po_core/tests/build

# Include any dependencies generated for this target.
include CMakeFiles/test_all.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_all.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_all.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_all.dir/flags.make

CMakeFiles/test_all.dir/codegen:
.PHONY : CMakeFiles/test_all.dir/codegen

CMakeFiles/test_all.dir/experiment_helpers.o: CMakeFiles/test_all.dir/flags.make
CMakeFiles/test_all.dir/experiment_helpers.o: /Users/<USER>/Documents/PoMVG/po_core/tests/experiment_helpers.cpp
CMakeFiles/test_all.dir/experiment_helpers.o: CMakeFiles/test_all.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/PoMVG/po_core/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_all.dir/experiment_helpers.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_all.dir/experiment_helpers.o -MF CMakeFiles/test_all.dir/experiment_helpers.o.d -o CMakeFiles/test_all.dir/experiment_helpers.o -c /Users/<USER>/Documents/PoMVG/po_core/tests/experiment_helpers.cpp

CMakeFiles/test_all.dir/experiment_helpers.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_all.dir/experiment_helpers.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/PoMVG/po_core/tests/experiment_helpers.cpp > CMakeFiles/test_all.dir/experiment_helpers.i

CMakeFiles/test_all.dir/experiment_helpers.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_all.dir/experiment_helpers.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/PoMVG/po_core/tests/experiment_helpers.cpp -o CMakeFiles/test_all.dir/experiment_helpers.s

CMakeFiles/test_all.dir/random_generators.o: CMakeFiles/test_all.dir/flags.make
CMakeFiles/test_all.dir/random_generators.o: /Users/<USER>/Documents/PoMVG/po_core/tests/random_generators.cpp
CMakeFiles/test_all.dir/random_generators.o: CMakeFiles/test_all.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/PoMVG/po_core/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_all.dir/random_generators.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_all.dir/random_generators.o -MF CMakeFiles/test_all.dir/random_generators.o.d -o CMakeFiles/test_all.dir/random_generators.o -c /Users/<USER>/Documents/PoMVG/po_core/tests/random_generators.cpp

CMakeFiles/test_all.dir/random_generators.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_all.dir/random_generators.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/PoMVG/po_core/tests/random_generators.cpp > CMakeFiles/test_all.dir/random_generators.i

CMakeFiles/test_all.dir/random_generators.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_all.dir/random_generators.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/PoMVG/po_core/tests/random_generators.cpp -o CMakeFiles/test_all.dir/random_generators.s

CMakeFiles/test_all.dir/time_measurement.o: CMakeFiles/test_all.dir/flags.make
CMakeFiles/test_all.dir/time_measurement.o: /Users/<USER>/Documents/PoMVG/po_core/tests/time_measurement.cpp
CMakeFiles/test_all.dir/time_measurement.o: CMakeFiles/test_all.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/PoMVG/po_core/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/test_all.dir/time_measurement.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_all.dir/time_measurement.o -MF CMakeFiles/test_all.dir/time_measurement.o.d -o CMakeFiles/test_all.dir/time_measurement.o -c /Users/<USER>/Documents/PoMVG/po_core/tests/time_measurement.cpp

CMakeFiles/test_all.dir/time_measurement.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_all.dir/time_measurement.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/PoMVG/po_core/tests/time_measurement.cpp > CMakeFiles/test_all.dir/time_measurement.i

CMakeFiles/test_all.dir/time_measurement.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_all.dir/time_measurement.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/PoMVG/po_core/tests/time_measurement.cpp -o CMakeFiles/test_all.dir/time_measurement.s

CMakeFiles/test_all.dir/test_ransac_lirp.o: CMakeFiles/test_all.dir/flags.make
CMakeFiles/test_all.dir/test_ransac_lirp.o: /Users/<USER>/Documents/PoMVG/po_core/tests/test_ransac_lirp.cpp
CMakeFiles/test_all.dir/test_ransac_lirp.o: CMakeFiles/test_all.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Documents/PoMVG/po_core/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/test_all.dir/test_ransac_lirp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_all.dir/test_ransac_lirp.o -MF CMakeFiles/test_all.dir/test_ransac_lirp.o.d -o CMakeFiles/test_all.dir/test_ransac_lirp.o -c /Users/<USER>/Documents/PoMVG/po_core/tests/test_ransac_lirp.cpp

CMakeFiles/test_all.dir/test_ransac_lirp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_all.dir/test_ransac_lirp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/Documents/PoMVG/po_core/tests/test_ransac_lirp.cpp > CMakeFiles/test_all.dir/test_ransac_lirp.i

CMakeFiles/test_all.dir/test_ransac_lirp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_all.dir/test_ransac_lirp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/Documents/PoMVG/po_core/tests/test_ransac_lirp.cpp -o CMakeFiles/test_all.dir/test_ransac_lirp.s

# Object files for target test_all
test_all_OBJECTS = \
"CMakeFiles/test_all.dir/experiment_helpers.o" \
"CMakeFiles/test_all.dir/random_generators.o" \
"CMakeFiles/test_all.dir/time_measurement.o" \
"CMakeFiles/test_all.dir/test_ransac_lirp.o"

# External object files for target test_all
test_all_EXTERNAL_OBJECTS =

test_all: CMakeFiles/test_all.dir/experiment_helpers.o
test_all: CMakeFiles/test_all.dir/random_generators.o
test_all: CMakeFiles/test_all.dir/time_measurement.o
test_all: CMakeFiles/test_all.dir/test_ransac_lirp.o
test_all: CMakeFiles/test_all.dir/build.make
test_all: /opt/homebrew/lib/libgtest_main.a
test_all: /opt/homebrew/lib/libgtest.a
test_all: CMakeFiles/test_all.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Documents/PoMVG/po_core/tests/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX executable test_all"
	/opt/homebrew/bin/cmake -E echo Ensuring\ po_core.hpp\ exists...
	/opt/homebrew/bin/cmake -E make_directory 
	/opt/homebrew/bin/cmake -E copy /Users/<USER>/Documents/PoMVG/po_core/tests/po_core.hpp.in /po_core.hpp
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_all.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_all.dir/build: test_all
.PHONY : CMakeFiles/test_all.dir/build

CMakeFiles/test_all.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_all.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_all.dir/clean

CMakeFiles/test_all.dir/depend:
	cd /Users/<USER>/Documents/PoMVG/po_core/tests/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Documents/PoMVG/po_core/tests /Users/<USER>/Documents/PoMVG/po_core/tests /Users/<USER>/Documents/PoMVG/po_core/tests/build /Users/<USER>/Documents/PoMVG/po_core/tests/build /Users/<USER>/Documents/PoMVG/po_core/tests/build/CMakeFiles/test_all.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_all.dir/depend

