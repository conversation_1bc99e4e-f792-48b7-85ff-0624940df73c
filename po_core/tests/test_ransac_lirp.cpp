/**
 * @file test_ransac_lirp.cpp
 * @brief 测试RANSAC与LiRP相对位姿估计方法的集成
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <internal/gnc_irls.hpp>
#include <internal/types.hpp>

#include <filesystem>
#include <opengv/relative_pose/methods.hpp>
#include <opengv/relative_pose/CentralRelativeAdapter.hpp>
#include "random_generators.hpp"
#include "experiment_helpers.hpp"
#include <iomanip> // 添加iomanip头文件用于格式化输出

namespace
{
    using namespace PoSDK;
    using namespace opengv;
    using namespace Interface;

    class TestOutlierLiRP : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            double max_parallax = 1.0;     // 平移幅度
            double max_rotation = 0.5;     // 旋转幅度(弧度)
            size_t num_points = 100;       // 特征点数量
            double noise = 0.0;            // 噪声水平
            double outlier_fraction = 0.1; // 外点比例 (10%)
        };

        // 计算旋转误差（使用Frobenius范数）
        double ComputeRotationError(const opengv::rotation_t &R_estimated,
                                    const opengv::rotation_t &R_ground_truth)
        {
            return (R_estimated - R_ground_truth).norm();
        }

        // 计算平移误差（方向误差，因为尺度不确定）
        double ComputeTranslationError(const opengv::translation_t &t_estimated,
                                       const opengv::translation_t &t_ground_truth)
        {
            return (t_estimated.normalized() - t_ground_truth.normalized()).norm();
        }

        void SetUp() override
        {
            // 初始化LiRP方法作为模型估计器
            lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(lirp_method_ != nullptr);

            // 创建一个距离评估器
            cost_evaluator_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_relative_cost"));
            ASSERT_TRUE(cost_evaluator_ != nullptr);

            // 初始化RANSAC估计器
            ransac_estimator_ = std::make_shared<RANSACEstimator<BearingPairs>>();
            ASSERT_TRUE(ransac_estimator_ != nullptr);

            // 设置RANSAC参数
            MethodOptions ransac_options{
                {"model_estimator_type", "method_LiRP"},
                {"cost_evaluator_type", "method_relative_cost"},
                {"max_iterations", "1000"},
                {"min_iterations", "50"},
                {"confidence", "0.99"},
                {"inlier_threshold", "1e-4"},
                {"min_sample_size", "8"}};
            ransac_estimator_->SetMethodOptions(ransac_options);

            // 初始化GNC-IRLS估计器
            gnc_irls_estimator_ = std::make_shared<GNCIRLSEstimator<BearingPairs>>();
            ASSERT_TRUE(gnc_irls_estimator_ != nullptr);

            // 设置GNC-IRLS参数
            MethodOptions gnc_irls_options{
                {"model_estimator_type", "method_LiRP"},
                {"cost_evaluator_type", "method_relative_cost"},
                {"max_iterations", "20"},
                {"min_iterations", "3"},
                {"noise_scale", "3.0"},
                {"convergence_threshold", "1e-10"},
                {"gamma", "1.4"},
                {"use_majorization", "true"},
                {"use_superlinear", "true"},
                {"min_inlier_ratio", "0.1"},
                {"sigma_mode", "2"},
                {"fixed_sigma", "1.0"},
                {"inlier_threshold", "1e-4"},
                {"track_best_model", "false"},
                {"debug_output", "true"}};
            gnc_irls_estimator_->SetMethodOptions(gnc_irls_options);
        }

        // 生成场景数据
        void GenerateScene(const SceneConfig &config)
        {
            // 初始化随机种子
            initializeRandomSeed();

            // 生成相对位姿
            translation_t position1 = Eigen::Vector3d::Zero();
            rotation_t rotation1 = Eigen::Matrix3d::Identity();
            translation_t position2 = generateRandomTranslation(config.max_parallax);
            rotation_t rotation2 = generateRandomRotation(config.max_rotation);

            // 提取真值相对位姿
            extractRelativePose(
                position1, position2,
                rotation1, rotation2,
                ground_truth_t_, ground_truth_R_,
                true); // normalize translation

            // 使用OpenGV的辅助函数生成中心相机系统
            translations_t camOffsets;
            rotations_t camRotations;
            generateCentralCameraSystem(camOffsets, camRotations);

            // 生成2D-2D对应点
            bearingVectors_t bearingVectors1, bearingVectors2;
            std::vector<int> camCorrespondences1, camCorrespondences2;
            Eigen::MatrixXd gt(3, config.num_points);

            // 使用OpenGV的函数生成对应点
            generateRandom2D2DCorrespondences(
                position1, rotation1,
                position2, rotation2,
                camOffsets, camRotations,
                config.num_points,
                config.noise,
                config.outlier_fraction,
                bearingVectors1, bearingVectors2,
                camCorrespondences1, camCorrespondences2,
                gt);

            // 为LiRP方法准备数据
            BearingPairs bearing_pairs;
            ConvertToBearingPairs(bearingVectors1, bearingVectors2, bearing_pairs);

            // 创建BearingPairs数据样本
            auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

            // 设置RANSAC估计器的样本数据
            ransac_estimator_->SetRequiredData(sample_data);

            // 设置GNC-IRLS估计器的样本数据
            gnc_irls_estimator_->SetRequiredData(sample_data);

            // 设置LiRP特定的选项
            MethodOptions lirp_options{
                {"compute_mode", "false"},     // 使用改进模式
                {"use_opt_mode", "false"},     // 使用标准ProcessEvec2
                {"identify_mode", "PPO"},      // 使用PPO残差函数
                {"debug_output", "false"},     // 启用调试输出
                {"enable_profiling", "false"}, // 禁用性能分析
                {"view_i", "0"},
                {"view_j", "1"}};
            ransac_estimator_->SetModelEstimatorOptions(lirp_options);
            gnc_irls_estimator_->SetModelEstimatorOptions(lirp_options);

            // 设置代价评估器选项
            MethodOptions cost_options{
                {"debug_output", "false"},
                {"residual_type", "ppo"},
                {"enable_profiling", "false"} // 禁用性能分析
            };
            ransac_estimator_->SetCostEvaluatorOptions(cost_options);
            gnc_irls_estimator_->SetCostEvaluatorOptions(cost_options);

            // 可选：打印实验特征
            if (log_level_ > 0)
            {
                printExperimentCharacteristics(position2, rotation2,
                                               config.noise, config.outlier_fraction);
                printEssentialMatrix(ground_truth_t_, ground_truth_R_);
            }
        }

        Interface::MethodPresetPtr lirp_method_;
        Interface::MethodPresetPtr cost_evaluator_;
        Interface::RobustEstimatorPtr ransac_estimator_;
        Interface::RobustEstimatorPtr gnc_irls_estimator_;
        rotation_t ground_truth_R_;
        translation_t ground_truth_t_;

        // 日志等级 (0: 不输出, 1: 输出基本信息, 2: 输出详细)
        int log_level_ = 1;

        // 添加一个辅助函数用于转换数据格式
        void ConvertToBearingPairs(const bearingVectors_t &bearingVectors1,
                                   const bearingVectors_t &bearingVectors2,
                                   BearingPairs &bearing_pairs)
        {
            bearing_pairs.clear();
            const size_t num_points = bearingVectors1.size();
            bearing_pairs.reserve(num_points);

            for (size_t i = 0; i < num_points; i++)
            {
                Eigen::Matrix<double, 6, 1> match_pair;

                // 使用types.hpp中定义的BearingVector类型
                // bearingVectors1[i]和bearingVectors2[i]已经是归一化的单位向量
                BearingVector bearing1 = bearingVectors1[i];
                BearingVector bearing2 = bearingVectors2[i];

                match_pair.head<3>() = bearing1;
                match_pair.tail<3>() = bearing2;
                bearing_pairs.push_back(match_pair);
            }
        }
    };

    // GNC-IRLS在零噪声10%异常情况下的基准测试
    TEST_F(TestOutlierLiRP, GNCIRLSPureTest)
    {
        // 定义测试场景（零噪声，10%异常点）
        SceneConfig config{1.0, 0.3, 100, 0.0, 0.1}; // 100个点，无噪声，10%异常点

        // 生成场景数据
        GenerateScene(config);

        // 运行算法并计时
        std::cout << "开始运行GNC-IRLS-LiRP算法（零噪声10%异常）..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = gnc_irls_estimator_->Build();
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            end_time - start_time)
                            .count() /
                        1000.0;

        // 验证结果
        ASSERT_TRUE(result != nullptr) << "GNC-IRLS-LiRP方法在理想条件下未能生成结果";

        auto pose = GetDataPtr<RelativePose>(result);
        ASSERT_TRUE(pose != nullptr) << "未能从结果中获取相对位姿";

        // 计算误差
        double R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
        double t_error = ComputeTranslationError(pose->tij, ground_truth_t_);

        // 输出结果
        std::cout << "\nGNC-IRLS-LiRP理想条件测试结果:\n"
                  << std::string(50, '-') << "\n"
                  << "旋转误差: " << R_error << "\n"
                  << "平移误差: " << t_error << "\n"
                  << "运行时间: " << duration << " ms\n"
                  << "异常点比例: 10%\n"
                  << "噪声水平: 0.0\n"
                  << std::string(50, '-') << std::endl;

        // 验证结果，在理想条件下应该有很高的精度
        EXPECT_LT(R_error, 1e-6) << "理想条件下旋转误差过大";
        EXPECT_LT(t_error, 1e-6) << "理想条件下平移误差过大";
    }

    // GNC-IRLS在不同异常点比例下的测试
    TEST_F(TestOutlierLiRP, GNCIRLSTest)
    {
        // 测试不同的异常点比例
        std::vector<double> outlier_fractions = {0.0, 0.1, 0.2, 0.3, 0.4, 0.5};

        // 定义结构体保存测试结果
        struct TestResult
        {
            double outlier_fraction;
            double R_error;
            double t_error;
            double duration;
            bool success;
        };

        // 用于保存所有测试结果的向量
        std::vector<TestResult> results;
        results.reserve(outlier_fractions.size());

        // 测试每个异常点比例
        for (double fraction : outlier_fractions)
        {
            // 配置测试场景
            SceneConfig config{1.0, 0.3, 100, 0.0, fraction};

            // 生成场景数据
            GenerateScene(config);

            // 设置LiRP特定的选项（确保使用PPO残差）
            MethodOptions lirp_options{
                {"compute_mode", "false"},
                {"use_opt_mode", "false"},
                {"identify_mode", "PPO"}, // 使用PPO残差函数
                {"debug_output", "false"},
                {"enable_profiling", "false"},
                {"view_i", "0"},
                {"view_j", "1"}};
            gnc_irls_estimator_->SetModelEstimatorOptions(lirp_options);

            // 设置代价评估器选项
            MethodOptions cost_options{
                {"debug_output", "false"},
                {"residual_type", "ppo"}, // 使用PPO残差
                {"enable_profiling", "false"}};
            gnc_irls_estimator_->SetCostEvaluatorOptions(cost_options);

            // 运行算法并计时
            auto start_time = std::chrono::high_resolution_clock::now();
            auto result = gnc_irls_estimator_->Build();
            auto end_time = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                                end_time - start_time)
                                .count() /
                            1000.0;

            // 分析结果
            bool success = (result != nullptr);
            double R_error = 0.0, t_error = 0.0;

            if (success)
            {
                auto pose = GetDataPtr<RelativePose>(result);
                if (pose)
                {
                    R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
                    t_error = ComputeTranslationError(pose->tij, ground_truth_t_);
                }
                else
                {
                    success = false;
                }
            }

            // 保存测试结果
            results.push_back({fraction, R_error, t_error, duration, success});

            // 进度提示
            std::cout << "完成对" << (fraction * 100) << "%异常点比例的测试..." << std::endl;

            // 验证结果（只有在异常点比例较低时才断言）
            if (fraction <= 0.3)
            {
                EXPECT_TRUE(success) << "在" << (fraction * 100) << "%异常点比例下未能生成结果";
                if (success)
                {
                    EXPECT_LT(R_error, 0.1) << "在" << (fraction * 100) << "%异常点比例下旋转误差过大";
                    EXPECT_LT(t_error, 0.1) << "在" << (fraction * 100) << "%异常点比例下平移误差过大";
                }
            }
        }

        // 统一输出表格结果
        std::cout << "\nGNC-IRLS-LiRP在不同异常点比例下的测试结果：\n"
                  << std::string(80, '=') << std::endl;
        std::cout << std::left << std::setw(10) << "异常比例"
                  << std::setw(20) << "旋转误差"
                  << std::setw(20) << "平移误差"
                  << std::setw(20) << "耗时(ms)"
                  << std::setw(10) << "成功" << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        // 打印所有测试结果
        for (const auto &res : results)
        {
            std::cout << std::left << std::setw(10) << (res.outlier_fraction * 100) << "% "
                      << std::setw(20) << (res.success ? std::to_string(res.R_error) : "N/A")
                      << std::setw(20) << (res.success ? std::to_string(res.t_error) : "N/A")
                      << std::setw(20) << res.duration
                      << std::setw(10) << (res.success ? "是" : "否") << std::endl;
        }

        std::cout << std::string(80, '=') << std::endl;
        std::cout << "注意：以上测试使用的是LiRP模型和PPO残差类型" << std::endl;
    }

    // RANSAC测试用例 - 10%异常点，无噪声
    TEST_F(TestOutlierLiRP, RANSACOutlierTest)
    {
        // 定义测试场景（10%异常点，无噪声）
        SceneConfig config{1.0, 0.3, 100, 0.0, 0.2}; // 100个点，10%异常点

        // 生成场景数据
        GenerateScene(config);

        // 运行算法并计时
        std::cout << "开始运行RANSAC-LiRP算法..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = ransac_estimator_->Build();
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            end_time - start_time)
                            .count() /
                        1000.0;

        // 验证结果
        ASSERT_TRUE(result != nullptr) << "RANSAC-LiRP方法未能生成结果";

        auto pose = GetDataPtr<RelativePose>(result);
        ASSERT_TRUE(pose != nullptr) << "未能从结果中获取相对位姿";

        // 计算误差
        double R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
        double t_error = ComputeTranslationError(pose->tij, ground_truth_t_);

        // 输出结果
        std::cout << "\nRANSAC-LiRP测试结果:\n"
                  << std::string(50, '-') << "\n"
                  << "旋转误差: " << R_error << "\n"
                  << "平移误差: " << t_error << "\n"
                  << "运行时间: " << duration << " ms\n"
                  << "异常点比例: " << config.outlier_fraction * 100 << "%\n"
                  << std::string(50, '-') << std::endl;

        // 验证结果
        EXPECT_LT(R_error, 0.1) << "旋转误差过大";
        EXPECT_LT(t_error, 0.1) << "平移误差过大";
    }

    // 对比测试：直接使用LiRP（不使用RANSAC）
    TEST_F(TestOutlierLiRP, DirectLiRPOutlierTest)
    {
        // 定义测试场景（10%异常点，无噪声）
        SceneConfig config{1.0, 0.3, 100, 0.0, 0.2}; // 100个点，10%异常点

        // 生成场景数据但不使用RANSAC
        initializeRandomSeed();

        // 生成相对位姿
        translation_t position1 = Eigen::Vector3d::Zero();
        rotation_t rotation1 = Eigen::Matrix3d::Identity();
        translation_t position2 = generateRandomTranslation(config.max_parallax);
        rotation_t rotation2 = generateRandomRotation(config.max_rotation);

        // 提取真值相对位姿
        extractRelativePose(
            position1, position2,
            rotation1, rotation2,
            ground_truth_t_, ground_truth_R_,
            true);

        // 使用OpenGV的辅助函数生成中心相机系统
        translations_t camOffsets;
        rotations_t camRotations;
        generateCentralCameraSystem(camOffsets, camRotations);

        // 生成2D-2D对应点
        bearingVectors_t bearingVectors1, bearingVectors2;
        std::vector<int> camCorrespondences1, camCorrespondences2;
        Eigen::MatrixXd gt(3, config.num_points);

        // 使用OpenGV的函数生成对应点
        generateRandom2D2DCorrespondences(
            position1, rotation1,
            position2, rotation2,
            camOffsets, camRotations,
            config.num_points,
            config.noise,
            config.outlier_fraction,
            bearingVectors1, bearingVectors2,
            camCorrespondences1, camCorrespondences2,
            gt);

        // 为LiRP方法准备数据
        BearingPairs bearing_pairs;
        ConvertToBearingPairs(bearingVectors1, bearingVectors2, bearing_pairs);

        // 创建BearingPairs数据样本
        auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);
        lirp_method_->SetRequiredData(sample_data);

        // 设置LiRP选项
        MethodOptions lirp_options{
            {"compute_mode", "false"},
            {"use_opt_mode", "false"},
            {"identify_mode", "PPO"},
            {"debug_output", "true"},
            {"view_i", "0"},
            {"view_j", "1"}};
        lirp_method_->SetMethodOptions(lirp_options);

        // 运行算法并计时
        std::cout << "开始运行直接LiRP算法（不使用RANSAC）..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = lirp_method_->Build();
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            end_time - start_time)
                            .count() /
                        1000.0;

        // 验证结果
        ASSERT_TRUE(result != nullptr) << "直接LiRP方法未能生成结果";

        auto pose = GetDataPtr<RelativePose>(result);
        ASSERT_TRUE(pose != nullptr) << "未能从结果中获取相对位姿";

        // 计算误差
        double R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
        double t_error = ComputeTranslationError(pose->tij, ground_truth_t_);

        // 输出结果
        std::cout << "\n直接LiRP测试结果（无RANSAC）:\n"
                  << std::string(50, '-') << "\n"
                  << "旋转误差: " << R_error << "\n"
                  << "平移误差: " << t_error << "\n"
                  << "运行时间: " << duration << " ms\n"
                  << "异常点比例: " << config.outlier_fraction * 100 << "%\n"
                  << std::string(50, '-') << std::endl;

        // 记录误差但不断言（因为期望表现不好）
        std::cout << "注意：由于存在10%的异常点，直接LiRP方法可能表现不佳" << std::endl;
    }

    // 图表数据展示：不同方法在不同异常点比例下的精度对比
    TEST_F(TestOutlierLiRP, TmpData)
    {
        // 本测试仅展示图中数据，不执行具体算法
        std::cout << "\n不同方法在不同异常点比例下的本质矩阵误差对比\n"
                  << std::string(80, '=') << std::endl;

        // 定义异常点比例
        std::vector<double> outlier_fractions = {
            0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.75, 0.8, 0.85, 0.9, 0.95};

        // 定义各方法在不同异常点比例下的本质矩阵误差（从图中估计的中位数值）
        // 格式：方法名 -> {在各异常点比例下的误差}
        std::map<std::string, std::vector<double>> error_data = {
            {"RANSAC", {0.067, 0.085, 0.079, 0.082, 0.11, 0.22, 12.5, 22.3, 35.2, 56.8, 78.3, 87.2}},
            {"RHO", {0.052, 0.058, 0.063, 0.072, 0.16, 27.8, 48.3, 67.9, 74.2, 82.1, 85.4, 89.7}},
            {"LMEDS", {0.048, 0.053, 0.062, 0.077, 0.14, 0.23, 65.7, 73.4, 78.6, 83.2, 86.8, 88.9}},
            {"RAPIDS", {0.012, 0.014, 0.011, 0.013, 0.015, 0.023, 0.025, 0.028, 0.034, 0.038, 0.042, 0.057}}};

        // 打印表头
        std::cout << std::left << std::setw(10) << "方法";
        for (auto frac : outlier_fractions)
        {
            std::cout << std::right << std::setw(8) << frac;
        }
        std::cout << std::endl
                  << std::string(10 + 8 * outlier_fractions.size(), '-') << std::endl;

        // 打印数据
        for (const auto &method : error_data)
        {
            std::cout << std::left << std::setw(10) << method.first;
            for (size_t i = 0; i < outlier_fractions.size(); ++i)
            {
                std::cout << std::right << std::setw(8) << std::fixed << std::setprecision(2) << method.second[i];
            }
            std::cout << std::endl;
        }

        std::cout << std::string(80, '=') << std::endl;
        std::cout << "注意：上述数据是从图表中估计得出，并添加了一些随机扰动，仅用于展示不同方法在不同异常点比例下的表现趋势" << std::endl;
        std::cout << "RAPIDS方法在高异常点比例下表现最好，误差始终保持在较低水平" << std::endl;
    }

    // GNC-IRLS测试用例 - 10%异常点，无噪声
    TEST_F(TestOutlierLiRP, GNCIRLSOutlierTest)
    {
        // 定义测试场景（10%异常点，无噪声）
        SceneConfig config{1.0, 0.3, 100, 0.0, 0.1}; // 100个点，10%异常点

        // 生成场景数据
        GenerateScene(config);

        // 运行算法并计时
        std::cout << "开始运行GNC-IRLS-LiRP算法..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = gnc_irls_estimator_->Build();
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            end_time - start_time)
                            .count() /
                        1000.0;

        // 验证结果
        ASSERT_TRUE(result != nullptr) << "GNC-IRLS-LiRP方法未能生成结果";

        auto pose = GetDataPtr<RelativePose>(result);
        ASSERT_TRUE(pose != nullptr) << "未能从结果中获取相对位姿";

        // 计算误差
        double R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
        double t_error = ComputeTranslationError(pose->tij, ground_truth_t_);

        // 输出结果
        std::cout << "\nGNC-IRLS-LiRP测试结果:\n"
                  << std::string(50, '-') << "\n"
                  << "旋转误差: " << R_error << "\n"
                  << "平移误差: " << t_error << "\n"
                  << "运行时间: " << duration << " ms\n"
                  << "异常点比例: " << config.outlier_fraction * 100 << "%\n"
                  << std::string(50, '-') << std::endl;

        // 验证结果
        EXPECT_LT(R_error, 0.1) << "旋转误差过大";
        EXPECT_LT(t_error, 0.1) << "平移误差过大";
    }

    // RANSAC vs GNC-IRLS对比测试 - 不同异常点比例
    TEST_F(TestOutlierLiRP, ComparativeOutlierTest)
    {
        std::cout << "\n开始执行RANSAC vs GNC-IRLS在不同异常点比例下的对比测试...\n"
                  << std::endl;

        // 测试不同的异常点比例
        std::vector<double> outlier_fractions = {0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7};

        // 定义结构体保存测试结果
        struct ComparisonResult
        {
            std::string method_name;
            double outlier_fraction;
            double R_error;
            double t_error;
            double duration;
            bool success;
        };

        // 用于保存所有测试结果的向量
        std::vector<ComparisonResult> all_results;
        all_results.reserve(outlier_fractions.size() * 2); // 两种方法

        // 测试数据
        for (double fraction : outlier_fractions)
        {
            // 配置测试场景
            SceneConfig config{1.0, 0.3, 200, 0.0, fraction};
            GenerateScene(config);

            std::cout << "测试" << (fraction * 100) << "%异常点比例..." << std::endl;

            // RANSAC测试
            {
                auto start_time = std::chrono::high_resolution_clock::now();
                auto result = ransac_estimator_->Build();
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                                    end_time - start_time)
                                    .count() /
                                1000.0;

                bool success = (result != nullptr);
                double R_error = 0.0, t_error = 0.0;

                if (success)
                {
                    auto pose = GetDataPtr<RelativePose>(result);
                    if (pose)
                    {
                        R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
                        t_error = ComputeTranslationError(pose->tij, ground_truth_t_);
                    }
                    else
                    {
                        success = false;
                    }
                }

                // 保存结果
                all_results.push_back({"RANSAC", fraction, R_error, t_error, duration, success});
            }

            // GNC-IRLS测试
            {
                auto start_time = std::chrono::high_resolution_clock::now();
                auto result = gnc_irls_estimator_->Build();
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                                    end_time - start_time)
                                    .count() /
                                1000.0;

                bool success = (result != nullptr);
                double R_error = 0.0, t_error = 0.0;

                if (success)
                {
                    auto pose = GetDataPtr<RelativePose>(result);
                    if (pose)
                    {
                        R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
                        t_error = ComputeTranslationError(pose->tij, ground_truth_t_);
                    }
                    else
                    {
                        success = false;
                    }
                }

                // 保存结果
                all_results.push_back({"GNC-IRLS", fraction, R_error, t_error, duration, success});
            }
        }

        // 统一输出表格结果
        std::cout << "\nRANSAC vs GNC-IRLS在不同异常点比例下的对比:\n"
                  << std::string(70, '=') << std::endl;
        std::cout << std::left << std::setw(10) << "异常比例"
                  << std::setw(15) << "方法"
                  << std::setw(20) << "旋转误差"
                  << std::setw(20) << "平移误差"
                  << std::setw(15) << "耗时(ms)" << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        // 按异常点比例分组输出
        double current_fraction = -1.0;
        for (const auto &res : all_results)
        {
            // 如果异常点比例改变，打印分隔线
            if (res.outlier_fraction != current_fraction)
            {
                if (current_fraction >= 0)
                {
                    std::cout << std::string(80, '-') << std::endl;
                }
                current_fraction = res.outlier_fraction;
            }

            std::cout << std::left << std::setw(10) << (res.outlier_fraction * 100) << "% "
                      << std::setw(15) << res.method_name
                      << std::setw(20) << (res.success ? std::to_string(res.R_error) : "失败")
                      << std::setw(20) << (res.success ? std::to_string(res.t_error) : "失败")
                      << std::setw(15) << res.duration << std::endl;
        }

        std::cout << std::string(70, '=') << std::endl;
    }

    // 高异常点比例下的 GNC-IRLS vs RANSAC 测试
    TEST_F(TestOutlierLiRP, HighOutlierTest)
    {
        std::cout << "开始执行高异常点比例测试（70%异常点）..." << std::endl;

        // 定义测试场景（70%异常点，无噪声）- 极端情况测试
        SceneConfig config{1.0, 0.3, 200, 0.0, 0.7}; // 200个点，70%异常点

        // 生成场景数据
        GenerateScene(config);

        // 定义结构保存测试结果
        struct HighOutlierResult
        {
            std::string method_name;
            bool success;
            double R_error;
            double t_error;
            double duration;
        };

        // 存储结果的容器
        std::vector<HighOutlierResult> results;
        results.reserve(2); // RANSAC和GNC-IRLS两种方法

        // 运行RANSAC算法并计时
        std::cout << "运行RANSAC-LiRP算法..." << std::endl;
        auto ransac_start = std::chrono::high_resolution_clock::now();
        auto ransac_result = ransac_estimator_->Build();
        auto ransac_end = std::chrono::high_resolution_clock::now();
        auto ransac_duration = std::chrono::duration_cast<std::chrono::microseconds>(
                                   ransac_end - ransac_start)
                                   .count() /
                               1000.0;

        // 处理RANSAC结果
        {
            bool success = ransac_result != nullptr;
            double R_error = 0, t_error = 0;

            if (success)
            {
                auto pose = GetDataPtr<RelativePose>(ransac_result);
                if (pose)
                {
                    R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
                    t_error = ComputeTranslationError(pose->tij, ground_truth_t_);
                }
                else
                {
                    success = false;
                }
            }

            // 保存RANSAC结果
            results.push_back({"RANSAC", success, R_error, t_error, ransac_duration});
        }

        // 运行GNC-IRLS算法并计时
        std::cout << "运行GNC-IRLS-LiRP算法..." << std::endl;
        auto gnc_start = std::chrono::high_resolution_clock::now();
        auto gnc_result = gnc_irls_estimator_->Build();
        auto gnc_end = std::chrono::high_resolution_clock::now();
        auto gnc_duration = std::chrono::duration_cast<std::chrono::microseconds>(
                                gnc_end - gnc_start)
                                .count() /
                            1000.0;

        // 处理GNC-IRLS结果
        {
            bool success = gnc_result != nullptr;
            double R_error = 0, t_error = 0;

            if (success)
            {
                auto pose = GetDataPtr<RelativePose>(gnc_result);
                if (pose)
                {
                    R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
                    t_error = ComputeTranslationError(pose->tij, ground_truth_t_);
                }
                else
                {
                    success = false;
                }
            }

            // 保存GNC-IRLS结果
            results.push_back({"GNC-IRLS", success, R_error, t_error, gnc_duration});
        }

        // 输出结果表格
        std::cout << "\n高异常点比例测试结果 (70%异常点):\n"
                  << std::string(80, '=') << std::endl;
        std::cout << std::left << std::setw(15) << "方法"
                  << std::setw(10) << "成功"
                  << std::setw(20) << "旋转误差"
                  << std::setw(20) << "平移误差"
                  << std::setw(15) << "耗时(ms)" << std::endl;
        std::cout << std::string(80, '-') << std::endl;

        // 输出每种方法的结果
        for (const auto &res : results)
        {
            std::cout << std::left << std::setw(15) << res.method_name
                      << std::setw(10) << (res.success ? "是" : "否")
                      << std::setw(20) << (res.success ? std::to_string(res.R_error) : "N/A")
                      << std::setw(20) << (res.success ? std::to_string(res.t_error) : "N/A")
                      << std::setw(15) << res.duration << std::endl;
        }

        std::cout << std::string(80, '=') << std::endl;
        std::cout << "注意：在高异常点比例下，GNC-IRLS通常表现更好，而RANSAC可能需要更多迭代才能找到好的模型" << std::endl;
    }

} // namespace
