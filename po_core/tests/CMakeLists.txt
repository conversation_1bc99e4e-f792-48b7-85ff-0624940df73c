# ==============================================================================
# Copyright (c) 2024 PoSDK Project
# 文件: tests/CMakeLists.txt
# 描述: 测试程序构建配置
# ==============================================================================

# 添加cmake模块路径
list(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake")

# ------------------------------------------------------------------------------
# 依赖包配置
# ------------------------------------------------------------------------------
find_package(GTest REQUIRED)
find_package(OpenGV REQUIRED)
find_package(Protobuf REQUIRED)

# # 确保使用与项目其他部分相同的Abseil版本
# if(NOT TARGET absl::base)
#     # 我们期望主CMakeLists.txt已经查找并验证了Abseil
#     # 如果这里仍需要查找，说明可能有问题
#     message(STATUS "Tests: Abseil targets not found, verifying configuration...")
#     if(NOT DEFINED ABSEIL_MIN_VERSION)
#         set(ABSEIL_MIN_VERSION "20240722")
#     endif()
    
#     # 尝试在同一位置查找
#     if(DEFINED absl_DIR)
#         find_package(absl CONFIG PATHS "${absl_DIR}" NO_DEFAULT_PATH)
#         message(STATUS "Tests: Reusing main project's Abseil from ${absl_DIR}")
#     else()
#         # 使用与主CMakeLists.txt相同的查找逻辑
#         message(STATUS "Tests: Searching for Abseil with version >= ${ABSEIL_MIN_VERSION}")
#         find_package(absl CONFIG PATHS "/usr/local/lib/cmake/absl" NO_DEFAULT_PATH)
        
#         if(NOT absl_FOUND OR absl_VERSION VERSION_LESS ABSEIL_MIN_VERSION)
#             message(FATAL_ERROR "Tests: Cannot find suitable Abseil version")
#         endif()
#     endif()
    
#     message(STATUS "Tests: Found Abseil version: ${absl_VERSION} at ${absl_DIR}")
# endif()

message(STATUS "OpenGV_LIBRARIES: ${OpenGV_LIBRARIES}")
message(STATUS "OpenGV_INCLUDE_DIRS: ${OpenGV_INCLUDE_DIRS}")
message(STATUS "Protobuf_LIBRARIES: ${Protobuf_LIBRARIES}")

# ------------------------------------------------------------------------------
# 测试程序配置
# ------------------------------------------------------------------------------
# 添加测试可执行文件
add_executable(test_all


    # test_ransac_lirp.cpp
    # test_robust_LiRP.cpp
    # test_povg_sixpoint.cpp
    # test_two_view_optimizer.cpp  # TwoViewOptimizer 优化器测试
    # test_opengv_simulator.cpp
    # test_visual_simulator.cpp
    # test_global_removal.cpp

    # test_proto_tracks.cpp
    # test_tracks_io.cpp    # 添加tracks文件IO测试

    # test_lirp_method.cpp  # 添加新的测试文件
    # test_relative_cost_method.cpp
    # test_method_LiGT.cpp
    # test_g2o_io.cpp
    # test_data_relative_poses.cpp
    # test_method_PA.cpp
    # test_log_macro.cpp   # 添加日志宏测试
    # test_ananalytical_reconstruction.cpp  # 添加解析式重建测试
    # test_visual_simulator.cpp
    # test_ini_comments.cpp
    # test_RtCheck_performance.cpp  # RtCheck性能测试
    test_rtcheck_types_performance.cpp
    # test_nullspace_performance.cpp

)

# 设置输出目录
set_target_properties(test_all PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${OUTPUT_BIN_DIR}"
)

# 确保在编译测试之前生成po_core.hpp
add_custom_command(
    TARGET test_all
    PRE_BUILD
    COMMAND ${CMAKE_COMMAND} -E echo "Ensuring po_core.hpp exists..."
    COMMAND ${CMAKE_COMMAND} -E make_directory "${OUTPUT_INCLUDE_DIR}"
    COMMAND ${CMAKE_COMMAND} -E copy 
            "${CMAKE_SOURCE_DIR}/po_core.hpp.in"
            "${OUTPUT_INCLUDE_DIR}/po_core.hpp"
)

# 添加依赖库
target_link_libraries(test_all
    PRIVATE
        pomvg_proto
        po_core
        GTest::GTest
        GTest::Main
        # ${OpenGV_LIBRARIES}
        ${Protobuf_LIBRARIES}
)

# 添加OpenMP支持（如果可用）
if(BUILD_WITH_OPENMP AND (OpenMP_FOUND OR OpenMP_CXX_FOUND))
    if(TARGET OpenMP::OpenMP_CXX)
        target_link_libraries(test_all PRIVATE OpenMP::OpenMP_CXX)
        message(STATUS "Linked OpenMP::OpenMP_CXX to test_all")
    endif()
endif()

# 添加包含目录
target_include_directories(test_all
    PRIVATE
        ${OUTPUT_INCLUDE_DIR}    # 这里包含了生成的头文件目录
        ${CMAKE_SOURCE_DIR}/src  # 添加源码目录
        # ${OpenGV_INCLUDE_DIRS}   # 添加OpenGV的头文件目录
        ${CMAKE_CURRENT_SOURCE_DIR}/../src/internal
        ${Protobuf_INCLUDE_DIRS}
)

# 确保依赖关系
add_dependencies(test_all
    po_core
    pomvg_proto
)

target_compile_definitions(test_all
    PRIVATE
        PROJECT_SOURCE_DIR="${CMAKE_SOURCE_DIR}"
)

# ------------------------------------------------------------------------------
# 调试信息
# ------------------------------------------------------------------------------
# 打印包含目录信息
get_target_property(include_dirs test_all INCLUDE_DIRECTORIES)
message(STATUS "test_all include directories:")
foreach(dir ${include_dirs})
    message(STATUS "  ${dir}")
endforeach()

# 验证文件存在
message(STATUS "Checking po_core.hpp location:")
message(STATUS "  Expected at: ${OUTPUT_INCLUDE_DIR}/po_core.hpp")
if(EXISTS "${OUTPUT_INCLUDE_DIR}/po_core.hpp")
    message(STATUS "  File exists")
else()
    message(WARNING "  File does not exist")
endif()

# ------------------------------------------------------------------------------
# 添加到CTest
# ------------------------------------------------------------------------------
add_test(
    NAME test_all
    COMMAND test_all
    WORKING_DIRECTORY ${OUTPUT_BIN_DIR}
)
