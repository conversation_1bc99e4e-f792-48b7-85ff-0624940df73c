/**
 * @file test_lirp_method.cpp
 * @brief 测试LiRP相对位姿估计方法和新的评估系统
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <tuple>
// OpenGV头文件现在通过OpenGVSimulator间接使用，不需要直接包含

// 显式包含DataRelativePoses头文件
#include "data/data_relative_poses.hpp"

namespace
{
    using namespace PoSDK;

    class TestLiRPMethod : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 初始化LiRP方法
            method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(method_ != nullptr);
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 使用OpenGVSimulator生成场景数据并创建真值
        DataPtr GenerateSceneAndGroundTruth(const std::string &profile_commit,
                                            const MethodOptions &simulator_options)
        {
            // 创建OpenGV仿真器
            auto simulator = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("opengv_simulator"));
            EXPECT_TRUE(simulator != nullptr) << "Failed to create OpenGV simulator";

            // 设置仿真器参数
            simulator->SetMethodOptions(simulator_options);

            // 运行仿真器生成数据
            auto simulator_data = simulator->Build();
            EXPECT_TRUE(simulator_data != nullptr) << "Simulator failed to generate data";

            auto data_package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
            EXPECT_TRUE(data_package != nullptr) << "Simulator data is not a DataPackage";

            // 使用字典式访问方式获取LiRP数据和真值数据
            auto lirp_data = (*data_package)["method_LiRP"];
            auto gt_data = (*data_package)["ground_truth"];

            EXPECT_TRUE(lirp_data != nullptr) << "Failed to get LiRP data from simulator";
            EXPECT_TRUE(gt_data != nullptr) << "Failed to get ground truth data from simulator";

            // 设置LiRP方法的输入数据
            method_->SetRequiredData(lirp_data);

            // 设置LiRP特定的选项，启用评估
            MethodOptions lirp_options{
                {"compute_mode", "false"},         // 使用改进模式
                {"use_opt_mode", "false"},         // 使用标准ProcessEvec2
                {"identify_mode", "PPOG"},         // 使用PPO残差函数
                {"enable_evaluator", "true"},      // 启用评估
                {"enable_profiling", "false"},     // 关闭性能分析
                {"ProfileCommit", profile_commit}, // 设置配置提交名称
                {"view_i", "0"},
                {"view_j", "1"}};
            method_->SetMethodOptions(lirp_options);

            // 验证真值数据格式正确
            auto gt_pose = GetDataPtr<RelativePose>(gt_data);
            EXPECT_TRUE(gt_pose != nullptr) << "Failed to extract RelativePose from ground truth";

            return gt_data;
        }

        // 运行单个测试场景
        void RunScenario(const std::string &scenario_name, const MethodOptions &simulator_options)
        {
            if (log_level_ > 0)
            {
                std::cout << "\n=== Running scenario: " << scenario_name
                          << " (repeating " << num_repeated_tests_ << " times) ===" << std::endl;
            }

            // 重复运行测试以生成更多评估数据
            for (int repeat = 0; repeat < num_repeated_tests_; ++repeat)
            {
                if (log_level_ > 1)
                {
                    std::cout << "\n--- Repeat " << (repeat + 1) << "/" << num_repeated_tests_
                              << " for " << scenario_name << " ---" << std::endl;
                }

                // 生成场景数据和真值
                auto gt_data = GenerateSceneAndGroundTruth(scenario_name, simulator_options);

                // 设置真值数据用于评估器
                auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(method_);
                if (profiler)
                {
                    profiler->SetGTData(gt_data);
                }

                // 运行算法 - EvaluatorManager会自动处理评估
                auto result = method_->Build();

                // 验证结果存在
                ASSERT_TRUE(result != nullptr) << "LiRP method failed to produce a result for scenario: "
                                               << scenario_name << ", repeat: " << (repeat + 1);

                auto pose = GetDataPtr<RelativePose>(result);
                ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from result for scenario: "
                                             << scenario_name << ", repeat: " << (repeat + 1);

                if (log_level_ > 1)
                {
                    std::cout << "  Repeat " << (repeat + 1) << " completed successfully" << std::endl;
                }
            }
        }

        // 显示评估结果和测试评估管理器功能
        void ShowEvaluationResults()
        {
            if (log_level_ > 0)
            {
                std::cout << "\n"
                          << std::string(60, '=') << std::endl;
                std::cout << "EVALUATION SYSTEM TEST RESULTS" << std::endl;
                std::cout << std::string(60, '=') << std::endl;

                // 1. 打印所有评估报告
                std::cout << "\n1. All Evaluation Reports:" << std::endl;
                Interface::EvaluatorManager::PrintAllEvaluationReports();

                // 2. 打印算法对比（如果有多个指标）
                auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
                for (const auto &eval_type : eval_types)
                {
                    auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
                    for (const auto &algorithm : algorithms)
                    {
                        auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                        for (const auto &metric : metrics)
                        {
                            std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
                            Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
                        }
                    }
                }

                // 3. 导出CSV文件测试 - 生成基于测试名称和时间戳的唯一目录名
                std::cout << "\n3. Testing CSV Export Functions:" << std::endl;

                // 获取当前测试信息
                const ::testing::TestInfo *const test_info =
                    ::testing::UnitTest::GetInstance()->current_test_info();
                std::string test_name = std::string(test_info->test_case_name()) + "_" +
                                        std::string(test_info->name());

                // 添加时间戳确保唯一性
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                              now.time_since_epoch()) %
                          1000;

                std::ostringstream timestamp;
                timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d");

                std::string unique_dir_name = test_name + "_" + timestamp.str();

                TestCSVExport(unique_dir_name);

                std::cout << std::string(60, '=') << std::endl;
            }
        }

        // 测试CSV导出功能
        void TestCSVExport(const std::string &output_dir_name)
        {
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();

            for (const auto &eval_type : eval_types)
            {
                // 创建测试输出目录 - 使用传入的唯一目录名
                std::filesystem::path test_output_dir = output_dir_name;
                std::filesystem::create_directories(test_output_dir);

                std::cout << "\n=== 测试CSV导出功能（自动智能解析）===" << std::endl;

                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

                // // 测试导出所有指标对比（现在会自动使用智能解析）
                // bool success = Interface::EvaluatorManager::ExportAllMetricsToCSV(eval_type, test_output_dir);
                // std::cout << "Export all metrics for " << eval_type << " to " << test_output_dir << ": "
                //           << (success ? "SUCCESS" : "FAILED") << std::endl;

                // 测试导出详细统计
                for (const auto &algorithm : algorithms)
                {
                    std::filesystem::path detailed_path = test_output_dir / (eval_type + "_" + algorithm + "_detailed.csv");
                    bool detail_success = Interface::EvaluatorManager::ExportDetailedStatsToCSV(eval_type, algorithm, detailed_path);
                    std::cout << "Export detailed stats for " << eval_type << "::" << algorithm << ": "
                              << (detail_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                // 测试单个指标导出（现在会自动使用智能解析）
                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的对比表格..." << std::endl;

                        // 原有的单统计类型导出
                        std::filesystem::path single_metric_path = test_output_dir / (eval_type + "_" + metric + "_comparison.csv");
                        bool single_success = Interface::EvaluatorManager::ExportAlgorithmComparisonToCSV(
                            eval_type, metric, single_metric_path, "mean");
                        std::cout << "Export single metric " << eval_type << "::" << metric << ": "
                                  << (single_success ? "SUCCESS" : "FAILED") << std::endl;

                        // 新增：所有统计类型导出（统一智能解析格式）
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的所有统计类型（统一智能解析格式）..." << std::endl;
                        std::filesystem::path all_stats_path = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        bool all_stats_success = Interface::EvaluatorManager::ExportMetricAllStatsToCSV(
                            eval_type, metric, all_stats_path);
                        std::cout << "Export all stats for " << eval_type << "::" << metric << ": "
                                  << (all_stats_success ? "SUCCESS" : "FAILED") << std::endl;
                    }
                }
            }
        }

        Interface::MethodPresetPtr method_;

        // 日志等级 (0: 不输出, 1: 输出基本信息, 2: 输出详细)
        int log_level_ = 1; // 设置为1，减少冗余输出

        // 重复测试次数 - 用于生成足够的评估数据
        int num_repeated_tests_ = 10;
    };

    // 基本测试用例（无噪声、无外点）
    TEST_F(TestLiRPMethod, BasicTest)
    {
        MethodOptions simulator_options{
            {"max_parallax", "2.0"},
            {"max_rotation", "0.3"},
            {"num_points", "50"},
            {"noise_level", "0.0"},
            {"outlier_fraction", "0.0"},
            {"focal_length", "1000.0"},
            {"image_width", "640.0"},
            {"image_height", "480.0"},
            {"random_seed", "42"},
            {"log_level", "0"}};

        RunScenario("basic_test", simulator_options);
    }

    // 噪声水平测试
    TEST_F(TestLiRPMethod, NoiseLevel_Tests)
    {
        std::vector<double> noise_levels = {0.0, 0.001, 0.005, 0.01};

        for (double noise : noise_levels)
        {
            // 使用更清晰的数值格式，便于智能解析
            std::ostringstream oss;
            oss << std::fixed << std::setprecision(3) << noise;
            std::string scenario_name = "noise_" + oss.str();

            MethodOptions simulator_options{
                {"max_parallax", "1.0"},
                {"max_rotation", "0.3"},
                {"num_points", "100"},
                {"noise_level", std::to_string(noise)},
                {"outlier_fraction", "0.0"},
                {"focal_length", "1000.0"},
                {"image_width", "640.0"},
                {"image_height", "480.0"},
                {"random_seed", "42"},
                {"log_level", "0"}};

            RunScenario(scenario_name, simulator_options);
        }
    }

    // 特征点数量测试
    TEST_F(TestLiRPMethod, PointCount_Tests)
    {
        std::vector<size_t> point_counts = {20, 50, 100, 200};

        for (size_t point_count : point_counts)
        {
            // 整数格式保持简洁
            std::string scenario_name = "points_" + std::to_string(point_count);

            MethodOptions simulator_options{
                {"max_parallax", "1.0"},
                {"max_rotation", "0.3"},
                {"num_points", std::to_string(point_count)},
                {"noise_level", "0.001"},
                {"outlier_fraction", "0.0"},
                {"focal_length", "1000.0"},
                {"image_width", "640.0"},
                {"image_height", "480.0"},
                {"random_seed", "42"},
                {"log_level", "0"}};

            RunScenario(scenario_name, simulator_options);
        }
    }

    // 视差测试
    TEST_F(TestLiRPMethod, Parallax_Tests)
    {
        std::vector<double> parallax_levels = {0.5, 1.0, 1.5, 2.0};

        for (double parallax : parallax_levels)
        {
            // 使用精确的浮点格式
            std::ostringstream oss;
            oss << std::fixed << std::setprecision(1) << parallax;
            std::string scenario_name = "parallax_" + oss.str();

            MethodOptions simulator_options{
                {"max_parallax", std::to_string(parallax)},
                {"max_rotation", "0.3"},
                {"num_points", "100"},
                {"noise_level", "0.001"},
                {"outlier_fraction", "0.0"},
                {"focal_length", "1000.0"},
                {"image_width", "640.0"},
                {"image_height", "480.0"},
                {"random_seed", "42"},
                {"log_level", "0"}};

            RunScenario(scenario_name, simulator_options);
        }
    }

    // 评估器管理功能测试
    TEST_F(TestLiRPMethod, EvaluatorManager_FunctionalityTest)
    {
        // 运行几个基本场景来生成数据
        std::vector<std::pair<std::string, MethodOptions>> test_scenarios = {
            {"func_test_1", {{"max_parallax", "1.0"}, {"max_rotation", "0.3"}, {"num_points", "50"}, {"noise_level", "0.0"}, {"outlier_fraction", "0.0"}, {"focal_length", "1000.0"}, {"image_width", "640.0"}, {"image_height", "480.0"}, {"random_seed", "42"}, {"log_level", "0"}}},
            {"func_test_2", {{"max_parallax", "1.0"}, {"max_rotation", "0.3"}, {"num_points", "50"}, {"noise_level", "0.001"}, {"outlier_fraction", "0.0"}, {"focal_length", "1000.0"}, {"image_width", "640.0"}, {"image_height", "480.0"}, {"random_seed", "42"}, {"log_level", "0"}}},
            {"func_test_3", {{"max_parallax", "1.0"}, {"max_rotation", "0.3"}, {"num_points", "50"}, {"noise_level", "0.005"}, {"outlier_fraction", "0.0"}, {"focal_length", "1000.0"}, {"image_width", "640.0"}, {"image_height", "480.0"}, {"random_seed", "42"}, {"log_level", "0"}}}};

        for (const auto &[scenario_name, simulator_options] : test_scenarios)
        {
            RunScenario(scenario_name, simulator_options);
        }

        // 测试清理功能
        std::cout << "\nTesting evaluator management functions..." << std::endl;

        // 获取所有评估类型
        auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
        EXPECT_FALSE(eval_types.empty()) << "Should have evaluation types after running scenarios";

        // 测试获取算法列表
        for (const auto &eval_type : eval_types)
        {
            auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
            EXPECT_FALSE(algorithms.empty()) << "Should have algorithms for type: " << eval_type;

            for (const auto &algorithm : algorithms)
            {
                auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                EXPECT_FALSE(metrics.empty()) << "Should have metrics for " << eval_type << "::" << algorithm;

                for (const auto &metric : metrics)
                {
                    auto commits = Interface::EvaluatorManager::GetAllEvalCommits(eval_type, algorithm, metric);
                    EXPECT_FALSE(commits.empty()) << "Should have commits for " << eval_type << "::" << algorithm << "::" << metric;
                }
            }
        }

        // 测试清理算法
        if (!eval_types.empty())
        {
            auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_types[0]);
            if (!algorithms.empty())
            {
                bool clear_success = Interface::EvaluatorManager::ClearAlgorithm(eval_types[0], algorithms[0]);
                EXPECT_TRUE(clear_success) << "Should successfully clear algorithm";
            }
        }

        std::cout << "Evaluator management functionality tests completed." << std::endl;
    }

} // namespace
