# plot_residuals.py
import pandas as pd
import matplotlib.pyplot as plt
import argparse
import os # Import os for path checking

def plot_residuals(csv_path, actual_outliers_path=None, output_image_path=None):
    """
    Reads a CSV file containing residual data and plots it, optionally 
    highlighting actual outliers from a separate file.

    Args:
        csv_path (str): Path to the CSV file.
                        Expected columns: ObsID, Residual, IsOutlierDetected
        actual_outliers_path (str, optional): Path to the text file containing 
                                             actual outlier ObsIDs (one per line).
                                             Defaults to None.
        output_image_path (str, optional): Path to save the plot image.
                                          If None, the plot is shown interactively.
                                          Defaults to None.
    """
    try:
        # Read the CSV data
        data = pd.read_csv(csv_path)
        # 检查必需的列是否存在
        required_csv_cols = ['ObsID', 'Residual', 'IsOutlierDetected']
        if not all(col in data.columns for col in required_csv_cols):
            raise KeyError(f"CSV file must contain columns: {required_csv_cols}")

        # Read actual outlier indices (ObsIDs) if path is provided
        actual_outlier_obs_ids = set()
        num_actual_outliers = 0
        if actual_outliers_path and os.path.exists(actual_outliers_path):
            try:
                with open(actual_outliers_path, 'r') as f:
                    # 假设文件每行一个 ObsID
                    actual_outlier_obs_ids = {int(line.strip()) for line in f if line.strip().isdigit()}
                num_actual_outliers = len(actual_outlier_obs_ids)
                print(f"Read {num_actual_outliers} actual outlier ObsIDs from {actual_outliers_path}")
            except ValueError as e:
                 print(f"Warning: Invalid data format in {actual_outliers_path}. Ensure it contains only integer ObsIDs. Error: {e}")
                 actual_outliers_path = None
            except Exception as e:
                print(f"Warning: Could not read actual outlier indices from {actual_outliers_path}. Error: {e}")
                actual_outliers_path = None 
        elif actual_outliers_path:
            print(f"Warning: Actual outlier file not found at {actual_outliers_path}. Skipping actual outlier plotting.")
            actual_outliers_path = None

        # Separate inliers and detected outliers based on the flag in the CSV
        inliers = data[data['IsOutlierDetected'] == 0]
        detected_outliers = data[data['IsOutlierDetected'] == 1]

        # Create the scatter plot
        plt.figure(figsize=(15, 7)) 

        # Plot inliers (blue dots)
        plt.scatter(inliers['ObsID'], inliers['Residual'], # 使用 ObsID 作为 x 轴
                    color='blue', label='Inliers (Not Detected)', alpha=0.5, s=15)

        # Plot detected outliers (red dots)
        plt.scatter(detected_outliers['ObsID'], detected_outliers['Residual'], # 使用 ObsID
                    color='red', label='Detected as Outlier', alpha=0.7, s=25) 

        # Plot actual outliers (green 'x') if available
        if actual_outlier_obs_ids: # 检查集合是否非空
            # Filter the original data to get actual outliers using ObsID
            actual_outlier_data = data[data['ObsID'].isin(actual_outlier_obs_ids)]
            if not actual_outlier_data.empty:
                plt.scatter(actual_outlier_data['ObsID'], actual_outlier_data['Residual'], # 使用 ObsID
                            color='lime', marker='x', 
                            label=f'Actual Outliers ({num_actual_outliers})', 
                            s=50, linewidth=1.5, zorder=3) # zorder确保标记在上方
            else:
                 print("Warning: No matching ObsIDs found in the CSV for the actual outliers provided.")


        # Add labels and title
        plt.xlabel('Observation ID (ObsID)') # 更新标签
        plt.ylabel('Residual Value')
        num_detected_outliers = len(detected_outliers)
        total_obs = len(data)
        detected_outlier_percent = (num_detected_outliers / total_obs * 100) if total_obs > 0 else 0

        title_str = f'Residuals vs. Observation ID ({num_detected_outliers}/{total_obs} detected, {detected_outlier_percent:.2f}%)'
        if actual_outliers_path: # 使用更新后的值
             title_str += f' - {num_actual_outliers} actual outliers marked'
        plt.title(title_str)

        # Add legend
        plt.legend()

        # Improve layout and add grid
        plt.grid(True, linestyle='--', alpha=0.6)
        plt.tight_layout()

        # Save or show the plot
        if output_image_path:
            plt.savefig(output_image_path, dpi=100)
            print(f"Plot saved to {output_image_path}")
        else:
            plt.show()

    except FileNotFoundError:
        print(f"Error: CSV file not found at {csv_path}")
    except KeyError as e:
        print(f"Error: Missing expected column in CSV file: {e}. Expected columns: ['ObsID', 'Residual', 'IsOutlierDetected']")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Plot residuals from a CSV file, optionally marking actual outliers.')
    parser.add_argument('csv_file', help='Path to the input CSV file (ObsID, Residual, IsOutlierDetected)') # 更新帮助文本
    parser.add_argument('--actual-outliers', help='Path to the text file containing actual outlier ObsIDs (one per line)', default=None) # 更新帮助文本
    parser.add_argument('-o', '--output', help='Path to save the output plot image (e.g., plot.png)', default=None)

    args = parser.parse_args()
    plot_residuals(args.csv_file, args.actual_outliers, args.output)
