/**
 * @file test_rtcheck_types_performance.cpp
 * @brief 测试不同RT_Check函数类型的性能对比
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>

namespace
{
    using namespace PoSDK;

    class TestRTCheckTypesPerformance : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 初始化LiRP方法
            method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(method_ != nullptr);
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 使用OpenGVSimulator生成场景数据并创建真值
        DataPtr GenerateSceneAndGroundTruth(const std::string &profile_commit,
                                            const MethodOptions &simulator_options)
        {
            // 创建OpenGV仿真器
            auto simulator = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("opengv_simulator"));
            EXPECT_TRUE(simulator != nullptr) << "Failed to create OpenGV simulator";

            // 设置仿真器参数
            simulator->SetMethodOptions(simulator_options);

            // 运行仿真器生成数据
            auto simulator_data = simulator->Build();
            EXPECT_TRUE(simulator_data != nullptr) << "Simulator failed to generate data";

            auto data_package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
            EXPECT_TRUE(data_package != nullptr) << "Simulator data is not a DataPackage";

            // 使用字典式访问方式获取LiRP数据和真值数据
            auto lirp_data = (*data_package)["method_LiRP"];
            auto gt_data = (*data_package)["ground_truth"];

            EXPECT_TRUE(lirp_data != nullptr) << "Failed to get LiRP data from simulator";
            EXPECT_TRUE(gt_data != nullptr) << "Failed to get ground truth data from simulator";

            // 设置LiRP方法的输入数据
            method_->SetRequiredData(lirp_data);

            // 验证真值数据格式正确
            auto gt_pose = GetDataPtr<RelativePose>(gt_data);
            EXPECT_TRUE(gt_pose != nullptr) << "Failed to extract RelativePose from ground truth";

            return gt_data;
        }

        // 运行单个RT_Check类型测试
        void RunSingleRTCheckTest(const std::string &rt_check_type,
                                  const std::string &profile_commit,
                                  const MethodOptions &simulator_options,
                                  int num_iterations = 10)
        {
            if (log_level_ > 0)
            {
                std::cout << "\n=== 测试RT_Check类型: " << rt_check_type
                          << " (重复 " << num_iterations << " 次) ===" << std::endl;
            }

            // 重复运行测试以生成更多评估数据
            for (int repeat = 0; repeat < num_iterations; ++repeat)
            {
                if (log_level_ > 1)
                {
                    std::cout << "\n--- 重复 " << (repeat + 1) << "/" << num_iterations
                              << " for " << rt_check_type << " ---" << std::endl;
                }

                // 生成场景数据和真值
                auto gt_data = GenerateSceneAndGroundTruth(profile_commit, simulator_options);

                // 设置LiRP特定的选项，启用评估
                MethodOptions lirp_options{
                    {"compute_mode", "true"},          // 必须为true
                    {"use_opt_mode", "false"},         // 使用标准ProcessEvec
                    {"identify_mode", "PPO"},          // 使用PPO残差函数
                    {"rt_check_type", rt_check_type},  // 指定RT_Check类型
                    {"enable_evaluator", "true"},      // 启用评估
                    {"enable_profiling", "false"},     // 关闭性能分析
                    {"ProfileCommit", profile_commit}, // 设置配置提交名称
                    {"view_i", "0"},
                    {"view_j", "1"},
                    {"log_level", "0"}}; // 关闭日志输出
                method_->SetMethodOptions(lirp_options);

                // 设置真值数据用于评估器
                auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(method_);
                if (profiler)
                {
                    profiler->SetGTData(gt_data);
                }

                // 运行算法 - EvaluatorManager会自动处理评估
                auto result = method_->Build();

                // 验证结果存在
                ASSERT_TRUE(result != nullptr) << "LiRP method failed to produce a result for RT_Check type: "
                                               << rt_check_type << ", repeat: " << (repeat + 1);

                auto pose = GetDataPtr<RelativePose>(result);
                ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from result for RT_Check type: "
                                             << rt_check_type << ", repeat: " << (repeat + 1);

                if (log_level_ > 1)
                {
                    std::cout << "  重复 " << (repeat + 1) << " 完成" << std::endl;
                }
            }
        }

        // 运行单个opt_mode测试
        void RunSingleOptModeTest(bool use_opt_mode,
                                  const std::string &profile_commit,
                                  const MethodOptions &simulator_options,
                                  int num_iterations = 10)
        {
            std::string opt_mode_str = use_opt_mode ? "use_opt_mode_true" : "use_opt_mode_false";

            if (log_level_ > 0)
            {
                std::cout << "\n=== 测试OptMode: " << opt_mode_str
                          << " (重复 " << num_iterations << " 次) ===" << std::endl;
            }

            // 重复运行测试以生成更多评估数据
            for (int repeat = 0; repeat < num_iterations; ++repeat)
            {
                if (log_level_ > 1)
                {
                    std::cout << "\n--- 重复 " << (repeat + 1) << "/" << num_iterations
                              << " for " << opt_mode_str << " ---" << std::endl;
                }

                // 生成场景数据和真值
                auto gt_data = GenerateSceneAndGroundTruth(profile_commit, simulator_options);

                // 设置LiRP特定的选项，启用评估
                MethodOptions lirp_options{
                    {"compute_mode", "true"},                                      // 必须为true
                    {"use_opt_mode", use_opt_mode ? "true" : "false"},             // 测试变量
                    {"identify_mode", "PPO"},                                      // 使用PPO残差函数
                    {"rt_check_type", "RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED"}, // 使用最优的RT_Check类型
                    {"enable_evaluator", "true"},                                  // 启用评估
                    {"enable_profiling", "false"},                                 // 关闭性能分析
                    {"ProfileCommit", profile_commit},                             // 设置配置提交名称
                    {"view_i", "0"},
                    {"view_j", "1"},
                    {"log_level", "0"}}; // 关闭日志输出
                method_->SetMethodOptions(lirp_options);

                // 设置真值数据用于评估器
                auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(method_);
                if (profiler)
                {
                    profiler->SetGTData(gt_data);
                }

                // 运行算法 - EvaluatorManager会自动处理评估
                auto result = method_->Build();

                // 验证结果存在
                ASSERT_TRUE(result != nullptr) << "LiRP method failed to produce a result for OptMode: "
                                               << opt_mode_str << ", repeat: " << (repeat + 1);

                auto pose = GetDataPtr<RelativePose>(result);
                ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from result for OptMode: "
                                             << opt_mode_str << ", repeat: " << (repeat + 1);

                if (log_level_ > 1)
                {
                    std::cout << "  重复 " << (repeat + 1) << " 完成" << std::endl;
                }
            }
        }

        // 显示评估结果和测试评估管理器功能
        void ShowEvaluationResults()
        {
            if (log_level_ > 0)
            {
                std::cout << "\n"
                          << std::string(60, '=') << std::endl;
                std::cout << "RT_CHECK TYPES PERFORMANCE TEST RESULTS" << std::endl;
                std::cout << std::string(60, '=') << std::endl;

                // 1. 打印所有评估报告
                std::cout << "\n1. All Evaluation Reports:" << std::endl;
                Interface::EvaluatorManager::PrintAllEvaluationReports();

                // 2. 打印算法对比（如果有多个指标）
                auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
                for (const auto &eval_type : eval_types)
                {
                    auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
                    for (const auto &algorithm : algorithms)
                    {
                        auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                        for (const auto &metric : metrics)
                        {
                            std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
                            Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
                        }
                    }
                }

                // 3. 导出CSV文件测试 - 生成基于测试名称和时间戳的唯一目录名
                std::cout << "\n3. Testing CSV Export Functions:" << std::endl;

                // 获取当前测试信息
                const ::testing::TestInfo *const test_info =
                    ::testing::UnitTest::GetInstance()->current_test_info();
                std::string test_name = std::string(test_info->test_case_name()) + "_" +
                                        std::string(test_info->name());

                // 添加时间戳确保唯一性
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);

                std::ostringstream timestamp;
                timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

                std::string unique_dir_name = test_name + "_" + timestamp.str();

                TestCSVExport(unique_dir_name);

                std::cout << std::string(60, '=') << std::endl;
            }
        }

        // 测试CSV导出功能
        void TestCSVExport(const std::string &output_dir_name)
        {
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();

            for (const auto &eval_type : eval_types)
            {
                // 创建测试输出目录 - 使用传入的唯一目录名
                std::filesystem::path test_output_dir = output_dir_name;
                std::filesystem::create_directories(test_output_dir);

                std::cout << "\n=== 测试CSV导出功能（RT_Check性能对比）===" << std::endl;

                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

                // 测试导出详细统计
                for (const auto &algorithm : algorithms)
                {
                    std::filesystem::path detailed_path = test_output_dir / (eval_type + "_" + algorithm + "_detailed.csv");
                    bool detail_success = Interface::EvaluatorManager::ExportDetailedStatsToCSV(eval_type, algorithm, detailed_path);
                    std::cout << "Export detailed stats for " << eval_type << "::" << algorithm << ": "
                              << (detail_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                // 测试单个指标导出
                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[性能对比] 导出指标 " << metric << " 的对比表格..." << std::endl;

                        // 原有的单统计类型导出
                        std::filesystem::path single_metric_path = test_output_dir / (eval_type + "_" + metric + "_comparison.csv");
                        bool single_success = Interface::EvaluatorManager::ExportAlgorithmComparisonToCSV(
                            eval_type, metric, single_metric_path, "mean");
                        std::cout << "Export single metric " << eval_type << "::" << metric << ": "
                                  << (single_success ? "SUCCESS" : "FAILED") << std::endl;

                        // 所有统计类型导出
                        std::cout << "\n[性能对比] 导出指标 " << metric << " 的所有统计类型..." << std::endl;
                        std::filesystem::path all_stats_path = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        bool all_stats_success = Interface::EvaluatorManager::ExportMetricAllStatsToCSV(
                            eval_type, metric, all_stats_path);
                        std::cout << "Export all stats for " << eval_type << "::" << metric << ": "
                                  << (all_stats_success ? "SUCCESS" : "FAILED") << std::endl;
                    }
                }

                // 测试时间统计导出功能
                std::cout << "\n=== 测试时间统计导出功能 ===" << std::endl;

                // 测试导出时间统计CSV和可视化
                std::filesystem::path time_stats_dir = test_output_dir / "time_statistics";
                std::vector<std::string> time_stat_types = {"Mean", "Median", "Min", "Max"};

                bool time_stats_with_viz_success = Interface::EvaluatorManager::ExportTimeStatisticsWithVisualization(
                    eval_type, time_stats_dir, time_stat_types);
                std::cout << "Export time statistics with visualization for " << eval_type << ": "
                          << (time_stats_with_viz_success ? "SUCCESS" : "FAILED") << std::endl;

                // 测试单独导出时间统计CSV
                std::filesystem::path time_stats_path = test_output_dir / (eval_type + "_time_statistics.csv");
                bool time_stats_success = Interface::EvaluatorManager::ExportTimeStatisticsToCSV(eval_type, time_stats_path);
                std::cout << "Export time statistics CSV for " << eval_type << ": "
                          << (time_stats_success ? "SUCCESS" : "FAILED") << std::endl;

                // 输出时间统计信息到控制台
                if (time_stats_success && std::filesystem::exists(time_stats_path))
                {
                    std::cout << "\n=== RT_Check函数运行时间统计 ===" << std::endl;
                    std::cout << std::string(80, '-') << std::endl;

                    // 简化显示：只显示前几行作为示例
                    std::ifstream time_file(time_stats_path);
                    if (time_file.is_open())
                    {
                        std::string line;
                        int line_count = 0;
                        while (std::getline(time_file, line) && line_count < 15)
                        {
                            std::cout << line << std::endl;
                            line_count++;
                        }

                        if (line_count >= 15)
                        {
                            std::cout << "... (省略其余内容，完整时间统计见CSV文件)" << std::endl;
                        }

                        time_file.close();
                    }
                    std::cout << std::string(80, '-') << std::endl;
                }

                // 显示时间统计图表路径
                if (time_stats_with_viz_success)
                {
                    std::cout << "\n时间统计图表保存在: " << time_stats_dir / "plots" << std::endl;
                }
            }
        }

        Interface::MethodPresetPtr method_;

        // 日志等级 (0: 不输出, 1: 输出基本信息, 2: 输出详细)
        int log_level_ = 1;
    };

    // 测试所有RT_Check类型的性能
    TEST_F(TestRTCheckTypesPerformance, CompareAllRTCheckTypes)
    {
        // 定义测试场景
        MethodOptions simulator_options{
            {"max_parallax", "2.0"},
            {"max_rotation", "0.3"},
            {"num_points", "30"}, // 与之前的测试保持一致
            {"noise_level", "5"},
            {"outlier_fraction", "0.0"},
            {"focal_length", "1000.0"},
            {"image_width", "640.0"},
            {"image_height", "480.0"},
            {"random_seed", "42"},
            {"log_level", "0"}};

        int num_iterations = 5000;

        // 定义要测试的RT_Check函数类型
        std::vector<std::string> rtcheck_types = {
            "RT_CHECK2",
            "RT_CHECK2_SUM_OPTIMIZED",
            "RT_CHECK2_STACK_OPTIMIZED",
            "RT_CHECK2_DIRECT_OPTIMIZED",
            "RT_CHECK2_PRECOMPUTED_SUM",
            "RT_CHECK2_SIMD_OPTIMIZED",
            "RT_CHECK2_ULTRA_OPTIMIZED",
            "RT_CHECK2_MATRIX_FREE_OPTIMIZED",
            "RT_CHECK2_BARE_METAL_OPTIMIZED",
            "RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED",
            "RT_CHECK2_SAFE_ULTRA_OPTIMIZED",
            "RT_CHECK2_FINAL_OPTIMIZED"};

        std::cout << "\n=== RT_Check函数性能对比测试 ===\n";
        std::cout << "测试场景: 300个点，" << num_iterations << "次迭代\n";
        std::cout << "平移幅度: 1.0，旋转幅度: 0.3 弧度\n";
        std::cout << "噪声水平: 0.0，外点比例: 0.0\n";

        // 运行所有RT_Check类型测试
        for (const auto &rt_check_type : rtcheck_types)
        {
            const std::string &test_name = rt_check_type; // 使用类型名称作为测试名称

            std::cout << "\n开始测试: " << test_name << " (" << rt_check_type << ")" << std::endl;

            RunSingleRTCheckTest(rt_check_type, rt_check_type, simulator_options, num_iterations);
        }
    }

    // 测试不同规模数据的性能
    TEST_F(TestRTCheckTypesPerformance, CompareScalability)
    {
        // 定义要测试的RT_Check函数类型（选择性能较好的几个）
        std::vector<std::string> rtcheck_types = {
            "RT_CHECK2",
            "RT_CHECK2_STACK_OPTIMIZED",
            "RT_CHECK2_ULTRA_OPTIMIZED",
            "RT_CHECK2_BARE_METAL_OPTIMIZED",
            "RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED",
            "RT_CHECK2_FINAL_OPTIMIZED"};

        // 定义不同规模的测试场景
        std::vector<size_t> num_points_list = {50, 100, 300, 500, 1000};
        int num_iterations = 1000;

        std::cout << "\n=== RT_Check函数可扩展性测试 ===\n";
        std::cout << "测试不同数据规模下的性能表现\n";

        for (size_t num_points : num_points_list)
        {
            MethodOptions simulator_options{
                {"max_parallax", "1.0"},
                {"max_rotation", "0.3"},
                {"num_points", std::to_string(num_points)},
                {"noise_level", "1"},
                {"outlier_fraction", "0.0"},
                {"focal_length", "1000.0"},
                {"image_width", "640.0"},
                {"image_height", "480.0"},
                {"random_seed", "42"},
                {"log_level", "0"}};

            std::cout << "\n--- 测试规模: " << num_points << " 个点 ---\n";

            for (const auto &rt_check_type : rtcheck_types)
            {
                const std::string &test_name = rt_check_type; // 使用类型名称作为测试名称

                std::string profile_commit = rt_check_type + "_points_" + std::to_string(num_points);

                RunSingleRTCheckTest(rt_check_type, profile_commit, simulator_options, num_iterations);
            }
        }
    }

    // 测试特定RT_Check类型的性能
    TEST_F(TestRTCheckTypesPerformance, TestSpecificRTCheckType)
    {
        // 定义测试场景
        MethodOptions simulator_options{
            {"max_parallax", "1.0"},
            {"max_rotation", "0.3"},
            {"num_points", "300"}, // 与之前的测试保持一致
            {"noise_level", "1"},
            {"outlier_fraction", "0.0"},
            {"focal_length", "1000.0"},
            {"image_width", "640.0"},
            {"image_height", "480.0"},
            {"random_seed", "42"},
            {"log_level", "0"}};

        int num_iterations = 1000;

        // 选择要测试的RT_Check类型
        std::string rt_check_type = "RT_CHECK2_SAFE_ULTRA_OPTIMIZED"; // 选择安全超级优化版本

        std::cout << "\n=== 特定RT_Check类型深度性能测试 ===\n";
        std::cout << "专门测试RT_CHECK2_SAFE_ULTRA_OPTIMIZED版本\n";

        RunSingleRTCheckTest(rt_check_type, rt_check_type + "_deep_test", simulator_options, num_iterations);
    }

    // 测试use_opt_mode=true和false的性能对比
    TEST_F(TestRTCheckTypesPerformance, CompareOptModePerformance)
    {
        // 定义测试场景
        MethodOptions simulator_options{
            {"max_parallax", "1.0"},
            {"max_rotation", "0.3"},
            {"num_points", "300"}, // 与之前的测试保持一致
            {"noise_level", "1"},
            {"outlier_fraction", "0.0"},
            {"focal_length", "1000.0"},
            {"image_width", "640.0"},
            {"image_height", "480.0"},
            {"random_seed", "42"},
            {"log_level", "0"}};

        int num_iterations = 1000;

        std::cout << "\n=== use_opt_mode性能对比测试 ===\n";
        std::cout << "测试use_opt_mode=true和false在300个点下的性能表现\n";

        // 运行use_opt_mode=true的测试
        RunSingleOptModeTest(true, "opt_mode_true_test", simulator_options, num_iterations);

        // 运行use_opt_mode=false的测试
        RunSingleOptModeTest(false, "opt_mode_false_test", simulator_options, num_iterations);
    }

    // 测试use_opt_mode=true和false在不同点数下的可扩展性性能对比
    TEST_F(TestRTCheckTypesPerformance, CompareOptModeScalability)
    {
        // 定义不同规模的测试场景
        std::vector<size_t> num_points_list = {50, 100, 200, 300, 500, 800, 1000};
        int num_iterations = 500; // 由于要测试多个规模，减少迭代次数

        std::cout << "\n=== use_opt_mode可扩展性性能对比测试 ===\n";
        std::cout << "测试use_opt_mode=true和false在不同数据规模下的性能表现\n";
        std::cout << "RT_Check类型: RT_CHECK2_BARE_METAL_OPTIMIZED_CORRECTED (最优版本)\n";

        for (size_t num_points : num_points_list)
        {
            MethodOptions simulator_options{
                {"max_parallax", "1.0"},
                {"max_rotation", "0.3"},
                {"num_points", std::to_string(num_points)},
                {"noise_level", "1"},
                {"outlier_fraction", "0.0"},
                {"focal_length", "1000.0"},
                {"image_width", "640.0"},
                {"image_height", "480.0"},
                {"random_seed", "42"},
                {"log_level", "0"}};

            std::cout << "\n--- 测试规模: " << num_points << " 个点 ---\n";

            // 测试use_opt_mode=true
            std::string profile_commit_true = "opt_mode_true_points_" + std::to_string(num_points);
            std::cout << "  测试use_opt_mode=true..." << std::endl;
            RunSingleOptModeTest(true, profile_commit_true, simulator_options, num_iterations);

            // 测试use_opt_mode=false
            std::string profile_commit_false = "opt_mode_false_points_" + std::to_string(num_points);
            std::cout << "  测试use_opt_mode=false..." << std::endl;
            RunSingleOptModeTest(false, profile_commit_false, simulator_options, num_iterations);
        }

        std::cout << "\n=== OptMode可扩展性测试完成 ===\n";
        std::cout << "请查看评估结果以对比精度和时间性能\n";
        std::cout << "预期结果:\n";
        std::cout << "- use_opt_mode=true (cursor优化版本): 可能在时间性能上有优势\n";
        std::cout << "- use_opt_mode=false (标准版本): 可能在精度上有优势或保持相同精度\n";
        std::cout << "- 不同点数量下的表现差异值得关注\n";
    }

} // namespace