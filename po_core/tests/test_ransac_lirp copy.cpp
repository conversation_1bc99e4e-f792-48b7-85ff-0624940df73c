/**
 * @file test_ransac_lirp.cpp
 * @brief 测试RANSAC与LiRP相对位姿估计方法的集成
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>

#include <filesystem>
#include <opengv/relative_pose/methods.hpp>
#include <opengv/relative_pose/CentralRelativeAdapter.hpp>
#include "random_generators.hpp"
#include "experiment_helpers.hpp"

namespace
{
    using namespace PoSDK;
    using namespace opengv;
    using namespace Interface;

    class TestRANSACLiRP : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            double max_parallax = 1.0;     // 平移幅度
            double max_rotation = 0.5;     // 旋转幅度(弧度)
            size_t num_points = 100;       // 特征点数量
            double noise = 0.0;            // 噪声水平
            double outlier_fraction = 0.1; // 外点比例 (10%)
        };

        // 计算旋转误差（使用Frobenius范数）
        double ComputeRotationError(const opengv::rotation_t &R_estimated,
                                    const opengv::rotation_t &R_ground_truth)
        {
            return (R_estimated - R_ground_truth).norm();
        }

        // 计算平移误差（方向误差，因为尺度不确定）
        double ComputeTranslationError(const opengv::translation_t &t_estimated,
                                       const opengv::translation_t &t_ground_truth)
        {
            return (t_estimated.normalized() - t_ground_truth.normalized()).norm();
        }

        void SetUp() override
        {
            // 初始化LiRP方法作为模型估计器
            lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(lirp_method_ != nullptr);

            // 创建一个距离评估器
            cost_evaluator_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_relative_cost"));
            ASSERT_TRUE(cost_evaluator_ != nullptr);

            // 初始化RANSAC估计器
            ransac_estimator_ = std::make_shared<RANSACEstimator<BearingPairs>>();
            ASSERT_TRUE(ransac_estimator_ != nullptr);

            // 设置RANSAC参数
            MethodOptions ransac_options{
                {"model_estimator_type", "method_LiRP"},
                {"cost_evaluator_type", "method_relative_cost"},
                {"max_iterations", "1000"},
                {"min_iterations", "50"},
                {"confidence", "0.99"},
                {"inlier_threshold", "1e-4"},
                {"min_sample_size", "8"}};
            ransac_estimator_->SetMethodOptions(ransac_options);
        }

        // 生成场景数据
        void GenerateScene(const SceneConfig &config)
        {
            // 初始化随机种子
            initializeRandomSeed();

            // 生成相对位姿
            translation_t position1 = Eigen::Vector3d::Zero();
            rotation_t rotation1 = Eigen::Matrix3d::Identity();
            translation_t position2 = generateRandomTranslation(config.max_parallax);
            rotation_t rotation2 = generateRandomRotation(config.max_rotation);

            // 提取真值相对位姿
            extractRelativePose(
                position1, position2,
                rotation1, rotation2,
                ground_truth_t_, ground_truth_R_,
                true); // normalize translation

            // 使用OpenGV的辅助函数生成中心相机系统
            translations_t camOffsets;
            rotations_t camRotations;
            generateCentralCameraSystem(camOffsets, camRotations);

            // 生成2D-2D对应点
            bearingVectors_t bearingVectors1, bearingVectors2;
            std::vector<int> camCorrespondences1, camCorrespondences2;
            Eigen::MatrixXd gt(3, config.num_points);

            // 使用OpenGV的函数生成对应点
            generateRandom2D2DCorrespondences(
                position1, rotation1,
                position2, rotation2,
                camOffsets, camRotations,
                config.num_points,
                config.noise,
                config.outlier_fraction,
                bearingVectors1, bearingVectors2,
                camCorrespondences1, camCorrespondences2,
                gt);

            // 为LiRP方法准备数据
            BearingPairs bearing_pairs;
            ConvertToBearingPairs(bearingVectors1, bearingVectors2, bearing_pairs);

            // 创建BearingPairs数据样本
            auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

            // 设置RANSAC估计器的样本数据
            ransac_estimator_->SetRequiredData(sample_data);

            // 设置LiRP特定的选项
            MethodOptions lirp_options{
                {"compute_mode", "false"},     // 使用改进模式
                {"use_opt_mode", "false"},     // 使用标准ProcessEvec2
                {"identify_mode", "PPO"},      // 使用PPO残差函数
                {"debug_output", "false"},     // 启用调试输出
                {"enable_profiling", "false"}, // 禁用性能分析
                {"view_i", "0"},
                {"view_j", "1"}};
            ransac_estimator_->SetModelEstimatorOptions(lirp_options);

            // 设置代价评估器选项
            MethodOptions cost_options{
                {"debug_output", "false"},
                {"residual_type", "sampson"},
                {"enable_profiling", "false"} // 禁用性能分析

            };
            ransac_estimator_->SetCostEvaluatorOptions(cost_options);

            // 可选：打印实验特征
            if (log_level_ > 0)
            {
                printExperimentCharacteristics(position2, rotation2,
                                               config.noise, config.outlier_fraction);
                printEssentialMatrix(ground_truth_t_, ground_truth_R_);
            }
        }

        Interface::MethodPresetPtr lirp_method_;
        Interface::MethodPresetPtr cost_evaluator_;
        Interface::RobustEstimatorPtr ransac_estimator_;
        rotation_t ground_truth_R_;
        translation_t ground_truth_t_;

        // 日志等级 (0: 不输出, 1: 输出基本信息, 2: 输出详细)
        int log_level_ = 1;

        // 添加一个辅助函数用于转换数据格式
        void ConvertToBearingPairs(const bearingVectors_t &bearingVectors1,
                                   const bearingVectors_t &bearingVectors2,
                                   BearingPairs &bearing_pairs)
        {
            bearing_pairs.clear();
            const size_t num_points = bearingVectors1.size();
            bearing_pairs.reserve(num_points);

            for (size_t i = 0; i < num_points; i++)
            {
                Eigen::Matrix<double, 6, 1> match_pair;
                match_pair.head<3>() = bearingVectors1[i];
                match_pair.tail<3>() = bearingVectors2[i];
                bearing_pairs.push_back(match_pair);
            }
        }
    };

    // RANSAC测试用例 - 10%异常点，无噪声
    TEST_F(TestRANSACLiRP, RANSACOutlierTest)
    {
        // 定义测试场景（10%异常点，无噪声）
        SceneConfig config{1.0, 0.3, 100, 0.0, 0.2}; // 100个点，10%异常点

        // 生成场景数据
        GenerateScene(config);

        // 运行算法并计时
        std::cout << "开始运行RANSAC-LiRP算法..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = ransac_estimator_->Build();
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            end_time - start_time)
                            .count() /
                        1000.0;

        // 验证结果
        ASSERT_TRUE(result != nullptr) << "RANSAC-LiRP方法未能生成结果";

        auto pose = GetDataPtr<RelativePose>(result);
        ASSERT_TRUE(pose != nullptr) << "未能从结果中获取相对位姿";

        // 计算误差
        double R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
        double t_error = ComputeTranslationError(pose->tij, ground_truth_t_);

        // 输出结果
        std::cout << "\nRANSAC-LiRP测试结果:\n"
                  << std::string(50, '-') << "\n"
                  << "旋转误差: " << R_error << "\n"
                  << "平移误差: " << t_error << "\n"
                  << "运行时间: " << duration << " ms\n"
                  << "异常点比例: " << config.outlier_fraction * 100 << "%\n"
                  << std::string(50, '-') << std::endl;

        // 验证结果
        EXPECT_LT(R_error, 0.1) << "旋转误差过大";
        EXPECT_LT(t_error, 0.1) << "平移误差过大";
    }

    // 对比测试：直接使用LiRP（不使用RANSAC）
    TEST_F(TestRANSACLiRP, DirectLiRPOutlierTest)
    {
        // 定义测试场景（10%异常点，无噪声）
        SceneConfig config{1.0, 0.3, 100, 0.0, 0.2}; // 100个点，10%异常点

        // 生成场景数据但不使用RANSAC
        initializeRandomSeed();

        // 生成相对位姿
        translation_t position1 = Eigen::Vector3d::Zero();
        rotation_t rotation1 = Eigen::Matrix3d::Identity();
        translation_t position2 = generateRandomTranslation(config.max_parallax);
        rotation_t rotation2 = generateRandomRotation(config.max_rotation);

        // 提取真值相对位姿
        extractRelativePose(
            position1, position2,
            rotation1, rotation2,
            ground_truth_t_, ground_truth_R_,
            true);

        // 使用OpenGV的辅助函数生成中心相机系统
        translations_t camOffsets;
        rotations_t camRotations;
        generateCentralCameraSystem(camOffsets, camRotations);

        // 生成2D-2D对应点
        bearingVectors_t bearingVectors1, bearingVectors2;
        std::vector<int> camCorrespondences1, camCorrespondences2;
        Eigen::MatrixXd gt(3, config.num_points);

        // 使用OpenGV的函数生成对应点
        generateRandom2D2DCorrespondences(
            position1, rotation1,
            position2, rotation2,
            camOffsets, camRotations,
            config.num_points,
            config.noise,
            config.outlier_fraction,
            bearingVectors1, bearingVectors2,
            camCorrespondences1, camCorrespondences2,
            gt);

        // 为LiRP方法准备数据
        BearingPairs bearing_pairs;
        ConvertToBearingPairs(bearingVectors1, bearingVectors2, bearing_pairs);

        // 创建BearingPairs数据样本
        auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);
        lirp_method_->SetRequiredData(sample_data);

        // 设置LiRP选项
        MethodOptions lirp_options{
            {"compute_mode", "false"},
            {"use_opt_mode", "false"},
            {"identify_mode", "PPO"},
            {"debug_output", "true"},
            {"view_i", "0"},
            {"view_j", "1"}};
        lirp_method_->SetMethodOptions(lirp_options);

        // 运行算法并计时
        std::cout << "开始运行直接LiRP算法（不使用RANSAC）..." << std::endl;
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = lirp_method_->Build();
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            end_time - start_time)
                            .count() /
                        1000.0;

        // 验证结果
        ASSERT_TRUE(result != nullptr) << "直接LiRP方法未能生成结果";

        auto pose = GetDataPtr<RelativePose>(result);
        ASSERT_TRUE(pose != nullptr) << "未能从结果中获取相对位姿";

        // 计算误差
        double R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
        double t_error = ComputeTranslationError(pose->tij, ground_truth_t_);

        // 输出结果
        std::cout << "\n直接LiRP测试结果（无RANSAC）:\n"
                  << std::string(50, '-') << "\n"
                  << "旋转误差: " << R_error << "\n"
                  << "平移误差: " << t_error << "\n"
                  << "运行时间: " << duration << " ms\n"
                  << "异常点比例: " << config.outlier_fraction * 100 << "%\n"
                  << std::string(50, '-') << std::endl;

        // 记录误差但不断言（因为期望表现不好）
        std::cout << "注意：由于存在10%的异常点，直接LiRP方法可能表现不佳" << std::endl;
    }

} // namespace
