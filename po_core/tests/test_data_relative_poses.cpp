/**
 * @file test_data_relative_poses.cpp
 * @brief 测试相对位姿数据的序列化和反序列化
 * @details 测试相对位姿数据的创建、存储和加载功能
 *
 * @copyright Copyright (c) 2024 XXX公司
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <random>
#include <filesystem>

namespace
{
    using namespace PoSDK;
    using namespace types;

    class TestDataRelativePoses : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            test_dir_ = std::filesystem::temp_directory_path() / "pomvg_test";
            std::filesystem::create_directories(test_dir_);
            test_file_ = test_dir_ / "test_poses";
            factory_data_ = std::make_unique<FactoryData>();

            // 配置仿真参数
            sim_config_.num_cameras = 5;
            sim_config_.num_points = 100;
            sim_config_.num_observations = 300;
            sim_config_.noise_sigma = 0.01;
            sim_config_.outlier_ratio = 0.1;
            sim_config_.cam_position_range = 10.0;
            sim_config_.point_position_range = 20.0;
            sim_config_.add_relative_motion = true;
            sim_config_.output_path = (test_dir_ / "sim_data.g2o").string();
        }

        void TearDown() override
        {
            std::filesystem::remove_all(test_dir_);
        }

        // 验证相对位姿数据
        void ValidatePoses(const RelativePoses &original, const RelativePoses &loaded)
        {
            ASSERT_EQ(loaded.size(), original.size()) << "位姿数量不匹配";

            for (size_t i = 0; i < original.size(); ++i)
            {
                const auto &orig_pose = original[i];
                const auto &loaded_pose = loaded[i];

                EXPECT_EQ(loaded_pose.i, orig_pose.i) << "视图i不匹配";
                EXPECT_EQ(loaded_pose.j, orig_pose.j) << "视图j不匹配";

                // 验证旋转矩阵
                EXPECT_TRUE(loaded_pose.Rij.isApprox(orig_pose.Rij, 1e-6))
                    << "旋转矩阵不匹配";

                // 验证平移向量
                EXPECT_TRUE(loaded_pose.tij.isApprox(orig_pose.tij, 1e-6))
                    << "平移向量不匹配";

                EXPECT_FLOAT_EQ(loaded_pose.weight, orig_pose.weight)
                    << "权重不匹配";
            }
        }

        std::filesystem::path test_dir_;
        std::filesystem::path test_file_;
        std::unique_ptr<FactoryData> factory_data_;
        file::G2OSimConfig sim_config_;
    };

    /**
     * @brief 测试相对位姿数据的创建
     */
    TEST_F(TestDataRelativePoses, CreateAndUseDataPoses)
    {
        auto data_ptr = factory_data_->Create("data_relative_poses");
        ASSERT_TRUE(data_ptr != nullptr) << "Failed to create data_relative_poses";
        EXPECT_EQ(data_ptr->GetType(), "data_relative_poses");
    }

    /**
     * @brief 测试从仿真数据加载相对位姿
     */
    TEST_F(TestDataRelativePoses, LoadFromSimulatedG2O)
    {
        // 生成仿真数据
        auto sim_data = file::GenerateG2OSimData(sim_config_);
        ASSERT_TRUE(sim_data.success) << "Failed to generate simulation data";

        // 加载并验证数据
        auto data_ptr = factory_data_->Create("data_relative_poses");
        ASSERT_TRUE(data_ptr != nullptr);
        ASSERT_TRUE(data_ptr->Load(sim_config_.output_path, "g2o"))
            << "Failed to load G2O file";

        // 使用Call打印加载的数据信息
        std::cout << "\n=== Loaded Poses ===" << std::endl;
        data_ptr->Call("PrintPosesInfo");

        auto loaded_poses = GetDataPtr<RelativePoses>(data_ptr);
        ASSERT_TRUE(loaded_poses != nullptr);
        ValidatePoses(sim_data.relative_poses, *loaded_poses);
    }

    /**
     * @brief 测试相对位姿数据的G2O格式序列化和反序列化
     */
    TEST_F(TestDataRelativePoses, G2OSerializeAndDeserialize)
    {
        // 生成仿真数据
        auto sim_data = file::GenerateG2OSimData(sim_config_);
        ASSERT_TRUE(sim_data.success);

        // 创建数据对象并设置相对位姿
        auto data_ptr = factory_data_->Create("data_relative_poses");
        ASSERT_TRUE(data_ptr != nullptr);
        auto poses_ptr = GetDataPtr<RelativePoses>(data_ptr);
        ASSERT_TRUE(poses_ptr != nullptr);
        *poses_ptr = sim_data.relative_poses;

        // 使用Call打印原始数据信息
        std::cout << "\n=== Original Poses ===" << std::endl;
        data_ptr->Call("PrintPosesInfo");

        // 保存为G2O格式
        ASSERT_TRUE(data_ptr->Save(test_dir_.string(), "test_poses", ".g2o"));

        // 加载数据
        auto loaded_ptr = factory_data_->Create("data_relative_poses");
        ASSERT_TRUE(loaded_ptr != nullptr);
        ASSERT_TRUE(loaded_ptr->Load(test_file_.string() + ".g2o", "g2o"));

        // 使用Call打印加载的数据信息
        std::cout << "\n=== Loaded Poses ===" << std::endl;
        loaded_ptr->Call("PrintPosesInfo");

        auto loaded_poses = GetDataPtr<RelativePoses>(loaded_ptr);
        ASSERT_TRUE(loaded_poses != nullptr);
        ValidatePoses(*poses_ptr, *loaded_poses);
    }

    /**
     * @brief 测试空位姿数据和无效文件处理
     */
    TEST_F(TestDataRelativePoses, EdgeCases)
    {
        auto data_ptr = factory_data_->Create("data_relative_poses");
        ASSERT_TRUE(data_ptr != nullptr);

        // 测试空数据序列化
        ASSERT_TRUE(data_ptr->Save(test_dir_.string(), "test_poses", ".g2o"));

        auto loaded_ptr = factory_data_->Create("data_relative_poses");
        ASSERT_TRUE(loaded_ptr != nullptr);
        ASSERT_TRUE(loaded_ptr->Load(test_file_.string() + ".g2o", "g2o"));

        auto poses = GetDataPtr<RelativePoses>(loaded_ptr);
        ASSERT_TRUE(poses != nullptr);
        EXPECT_TRUE(poses->empty());

        // 测试无效文件处理
        EXPECT_FALSE(loaded_ptr->Load("nonexistent_file.g2o", "g2o"));
        EXPECT_FALSE(data_ptr->Save("/invalid/path", "invalid_file", ".g2o"));
    }

    /**
     * @brief 测试本质矩阵计算
     */
    TEST_F(TestDataRelativePoses, EssentialMatrixTest)
    {
        // 生成仿真数据
        sim_config_.num_cameras = 2; // 只需要两个相机
        auto sim_data = file::GenerateG2OSimData(sim_config_);
        ASSERT_TRUE(sim_data.success);
        ASSERT_FALSE(sim_data.relative_poses.empty());

        const auto &pose = sim_data.relative_poses.front();
        Matrix3d E = pose.GetEssentialMatrix();

        // 验证本质矩阵的性质
        // 1. det(E) = 0
        EXPECT_NEAR(E.determinant(), 0.0, 1e-6);

        // 2. 2EE^T E - trace(EE^T)E = 0 (Huang-Faugeras条件)
        Matrix3d EEt = E * E.transpose();
        double trace_EEt = EEt.trace();
        Matrix3d constraint = 2 * E * E.transpose() * E - trace_EEt * E;
        EXPECT_TRUE(constraint.isZero(1e-6));
    }

} // namespace