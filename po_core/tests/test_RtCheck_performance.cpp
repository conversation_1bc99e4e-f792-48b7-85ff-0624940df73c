/**
 * @file test_RtCheck_performance.cpp
 * @brief RT_Check函数性能测试
 * @copyright Copyright (c) 2024 PoSDK
 */

#include <gtest/gtest.h>
#include <chrono>
#include <iostream>
#include <random>
#include <iomanip>

#include "po_core.hpp"
#include "relative_process/relative_pose.hpp"
// #include "internal/methods/method_LiRP.hpp"  // 注释掉，因为不需要

namespace PoSDK
{
    namespace test
    {

        class RTCheckPerformanceTest : public ::testing::Test
        {
        protected:
            void SetUp() override
            {
                // 设置随机数生成器
                std::random_device rd;
                generator_.seed(rd());

                // 初始化测试数据
                InitializeTestData();
            }

            void InitializeTestData()
            {
                const int n_points = 300;

                // 生成随机的归一化特征点
                v1_.resize(3, n_points);
                v2_.resize(3, n_points);

                // 生成真实的R和t
                Matrix3d R_true = GenerateRandomRotation();
                Vector3d t_true = GenerateRandomTranslation();

                // 生成3D点
                Matrix<double, 3, Dynamic> points_3d(3, n_points);
                for (int i = 0; i < n_points; ++i)
                {
                    points_3d.col(i) = Vector3d(
                        distribution_(generator_) * 5.0,
                        distribution_(generator_) * 5.0,
                        distribution_(generator_) * 5.0 + 10.0 // 确保在相机前方
                    );
                }

                // 投影到相机1
                for (int i = 0; i < n_points; ++i)
                {
                    Vector3d p1 = points_3d.col(i);
                    p1 = p1 / p1.norm(); // 归一化
                    v1_.col(i) = p1;
                }

                // 投影到相机2
                for (int i = 0; i < n_points; ++i)
                {
                    Vector3d p2 = R_true * points_3d.col(i) + t_true;
                    p2 = p2 / p2.norm(); // 归一化
                    v2_.col(i) = p2;
                }

                // 构建A矩阵
                A_.resize(n_points, 9);
                for (int i = 0; i < n_points; ++i)
                {
                    const Vector3d &p1 = v1_.col(i);
                    const Vector3d &p2 = v2_.col(i);

                    // 计算kron(p2, p1)
                    for (int j = 0; j < 3; ++j)
                    {
                        for (int k = 0; k < 3; ++k)
                        {
                            A_(i, j * 3 + k) = p2(j) * p1(k);
                        }
                    }
                }

                // 生成候选解
                R_sols_.resize(2);
                t_sols_.resize(2);

                // 第一个解接近真实解
                R_sols_[0] = R_true * GenerateSmallRotation();
                t_sols_[0] = t_true.normalized() + 0.1 * GenerateRandomTranslation();
                t_sols_[0] = t_sols_[0].normalized();

                // 第二个解是错误的
                R_sols_[1] = GenerateRandomRotation();
                t_sols_[1] = GenerateRandomTranslation().normalized();

                // 权重向量
                weights_.resize(n_points);
                for (int i = 0; i < n_points; ++i)
                {
                    weights_(i) = 1.0;
                }
            }

            Matrix3d GenerateRandomRotation()
            {
                // 生成随机的旋转矩阵
                Vector3d axis(distribution_(generator_), distribution_(generator_), distribution_(generator_));
                axis = axis.normalized();
                double angle = distribution_(generator_) * M_PI;

                Matrix3d K = cross_matrix(axis);
                return Matrix3d::Identity() + sin(angle) * K + (1 - cos(angle)) * K * K;
            }

            Matrix3d GenerateSmallRotation()
            {
                // 生成小角度旋转矩阵
                Vector3d axis(distribution_(generator_), distribution_(generator_), distribution_(generator_));
                axis = axis.normalized();
                double angle = distribution_(generator_) * 0.1; // 小角度

                Matrix3d K = cross_matrix(axis);
                return Matrix3d::Identity() + sin(angle) * K + (1 - cos(angle)) * K * K;
            }

            Vector3d GenerateRandomTranslation()
            {
                return Vector3d(distribution_(generator_), distribution_(generator_), distribution_(generator_));
            }

            // 测试数据
            BearingVectors v1_, v2_;
            MatrixXd A_;
            std::vector<Matrix3d> R_sols_;
            std::vector<Vector3d> t_sols_;
            VectorXd weights_;

            // 随机数生成器
            std::mt19937 generator_;
            std::normal_distribution<double> distribution_{0.0, 1.0};
        };

// 性能测试宏
#define BENCHMARK_RT_CHECK(func_name, iterations)                                           \
    do                                                                                      \
    {                                                                                       \
        auto start = std::chrono::high_resolution_clock::now();                             \
        for (int i = 0; i < iterations; ++i)                                                \
        {                                                                                   \
            double ratio;                                                                   \
            auto result = func_name(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio);      \
        }                                                                                   \
        auto end = std::chrono::high_resolution_clock::now();                               \
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start); \
        std::cout << std::setw(25) << #func_name << ": "                                    \
                  << std::setw(8) << duration.count() / (double)iterations << " μs/call"    \
                  << " (total: " << duration.count() << " μs)" << std::endl;                \
    } while (0)

        TEST_F(RTCheckPerformanceTest, CompareRTCheckMethods)
        {
            const int iterations = 1000;

            std::cout << "\n=== RT_Check 性能测试 (n=300, iterations=" << iterations << ") ===" << std::endl;
            std::cout << std::setw(25) << "方法名称" << std::setw(20) << "平均时间" << std::setw(20) << "总时间" << std::endl;
            std::cout << std::string(65, '-') << std::endl;

            // 原始版本
            BENCHMARK_RT_CHECK(RT_Check2, iterations);

            // 优化版本1：直接累加
            BENCHMARK_RT_CHECK(RT_Check2_sum_optimized, iterations);

            // 优化版本2：栈内存优化
            BENCHMARK_RT_CHECK(RT_Check2_stack_optimized, iterations);

            // 优化版本3：直接计算
            BENCHMARK_RT_CHECK(RT_Check2_direct_optimized, iterations);

            // 超级深度优化版本1：SIMD + 内联
            BENCHMARK_RT_CHECK(RT_Check2_simd_optimized, iterations);

            // 超级深度优化版本2：预计算 + 展开循环
            BENCHMARK_RT_CHECK(RT_Check2_ultra_optimized, iterations);

            // 极致优化版本：完全去除H矩阵构建
            BENCHMARK_RT_CHECK(RT_Check2_matrix_free_optimized, iterations);

            // 裸机优化版本：最小内存分配
            BENCHMARK_RT_CHECK(RT_Check2_bare_metal_optimized, iterations);

            // 安全极致优化版本：确保算法正确性
            BENCHMARK_RT_CHECK(RT_Check2_safe_ultra_optimized, iterations);

            // 终极优化版本：在确保正确性基础上的最大优化
            BENCHMARK_RT_CHECK(RT_Check2_final_optimized, iterations);

            // 超级优化版本：使用预计算的sum_A
            std::cout << "\n--- 超级优化版本测试（预计算sum_A）---" << std::endl;
            Vector<double, 9> sum_A = Vector<double, 9>::Zero();
            for (int i = 0; i < v1_.cols(); ++i)
            {
                const Vector3d &p1 = v1_.col(i);
                const Vector3d &p2 = v2_.col(i);
                const double w = weights_(i);

                for (int j = 0; j < 3; ++j)
                {
                    for (int k = 0; k < 3; ++k)
                    {
                        sum_A(j * 3 + k) += w * p2(j) * p1(k);
                    }
                }
            }

            // 测试预计算版本
            auto start = std::chrono::high_resolution_clock::now();
            for (int i = 0; i < iterations; ++i)
            {
                double ratio;
                auto result = RT_Check2_precomputed_sum(sum_A, v1_, v2_, R_sols_, t_sols_, &weights_, ratio);
            }
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            std::cout << std::setw(25) << "RT_Check2_precomputed_sum" << ": "
                      << std::setw(8) << duration.count() / (double)iterations << " μs/call"
                      << " (total: " << duration.count() << " μs)" << std::endl;

            // 测试算法正确性
            std::cout << "\n=== 算法正确性测试 ===" << std::endl;

            double ratio1, ratio2, ratio3, ratio4, ratio5, ratio6, ratio7, ratio8, ratio9;
            auto result1 = RT_Check2(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio1);
            auto result2 = RT_Check2_sum_optimized(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio2);
            auto result3 = RT_Check2_stack_optimized(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio3);
            auto result4 = RT_Check2_direct_optimized(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio4);
            auto result5 = RT_Check2_precomputed_sum(sum_A, v1_, v2_, R_sols_, t_sols_, &weights_, ratio5);
            auto result6 = RT_Check2_simd_optimized(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio6);
            auto result7 = RT_Check2_ultra_optimized(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio7);
            auto result8 = RT_Check2_matrix_free_optimized(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio8);
            auto result9 = RT_Check2_bare_metal_optimized(A_, v1_, v2_, R_sols_, t_sols_, &weights_, ratio9);

            // 检查结果一致性
            double rot_diff_2 = (result1.Rij - result2.Rij).norm();
            double trans_diff_2 = (result1.tij - result2.tij).norm();
            double rot_diff_3 = (result1.Rij - result3.Rij).norm();
            double trans_diff_3 = (result1.tij - result3.tij).norm();
            double rot_diff_4 = (result1.Rij - result4.Rij).norm();
            double trans_diff_4 = (result1.tij - result4.tij).norm();
            double rot_diff_5 = (result1.Rij - result5.Rij).norm();
            double trans_diff_5 = (result1.tij - result5.tij).norm();
            double rot_diff_6 = (result1.Rij - result6.Rij).norm();
            double trans_diff_6 = (result1.tij - result6.tij).norm();
            double rot_diff_7 = (result1.Rij - result7.Rij).norm();
            double trans_diff_7 = (result1.tij - result7.tij).norm();
            double rot_diff_8 = (result1.Rij - result8.Rij).norm();
            double trans_diff_8 = (result1.tij - result8.tij).norm();
            double rot_diff_9 = (result1.Rij - result9.Rij).norm();
            double trans_diff_9 = (result1.tij - result9.tij).norm();

            std::cout << "原始版本 vs 优化版本1:" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_2 << std::endl;
            std::cout << "  平移差异: " << trans_diff_2 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio2) << std::endl;

            std::cout << "原始版本 vs 优化版本2:" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_3 << std::endl;
            std::cout << "  平移差异: " << trans_diff_3 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio3) << std::endl;

            std::cout << "原始版本 vs 优化版本3:" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_4 << std::endl;
            std::cout << "  平移差异: " << trans_diff_4 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio4) << std::endl;

            std::cout << "原始版本 vs 超级优化版本:" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_5 << std::endl;
            std::cout << "  平移差异: " << trans_diff_5 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio5) << std::endl;

            std::cout << "原始版本 vs 超级深度优化版本1 (SIMD):" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_6 << std::endl;
            std::cout << "  平移差异: " << trans_diff_6 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio6) << std::endl;

            std::cout << "原始版本 vs 超级深度优化版本2 (Ultra):" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_7 << std::endl;
            std::cout << "  平移差异: " << trans_diff_7 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio7) << std::endl;

            std::cout << "原始版本 vs 极致优化版本 (Matrix-Free):" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_8 << std::endl;
            std::cout << "  平移差异: " << trans_diff_8 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio8) << std::endl;

            std::cout << "原始版本 vs 裸机优化版本 (Bare-Metal):" << std::endl;
            std::cout << "  旋转差异: " << rot_diff_9 << std::endl;
            std::cout << "  平移差异: " << trans_diff_9 << std::endl;
            std::cout << "  ratio差异: " << std::abs(ratio1 - ratio9) << std::endl;

            // 验证算法正确性
            EXPECT_LT(rot_diff_2, 1e-10);
            EXPECT_LT(trans_diff_2, 1e-10);
            EXPECT_LT(rot_diff_3, 1e-10);
            EXPECT_LT(trans_diff_3, 1e-10);
            EXPECT_LT(rot_diff_4, 1e-10);
            EXPECT_LT(trans_diff_4, 1e-10);
            EXPECT_LT(rot_diff_5, 1e-10);
            EXPECT_LT(trans_diff_5, 1e-10);
            EXPECT_LT(rot_diff_6, 1e-10);
            EXPECT_LT(trans_diff_6, 1e-10);
            EXPECT_LT(rot_diff_7, 1e-10);
            EXPECT_LT(trans_diff_7, 1e-10);
            EXPECT_LT(rot_diff_8, 1e-10);
            EXPECT_LT(trans_diff_8, 1e-10);
            EXPECT_LT(rot_diff_9, 1e-10);
            EXPECT_LT(trans_diff_9, 1e-10);
        }

        TEST_F(RTCheckPerformanceTest, TestDifferentDataSizes)
        {
            std::vector<int> sizes = {50, 100, 200, 300, 500, 1000};
            const int iterations = 100;

            std::cout << "\n=== 不同数据规模的性能测试 ===" << std::endl;
            std::cout << std::setw(8) << "n"
                      << std::setw(15) << "原始版本"
                      << std::setw(15) << "优化版本1"
                      << std::setw(15) << "优化版本2"
                      << std::setw(15) << "优化版本3"
                      << std::setw(15) << "SIMD版本"
                      << std::setw(15) << "Ultra版本"
                      << std::setw(15) << "Matrix-Free"
                      << std::setw(15) << "Bare-Metal" << std::endl;
            std::cout << std::string(128, '-') << std::endl;

            for (int n : sizes)
            {
                // 重新生成测试数据
                BearingVectors v1_test(3, n);
                BearingVectors v2_test(3, n);
                MatrixXd A_test(n, 9);
                VectorXd weights_test(n);

                // 生成随机数据
                for (int i = 0; i < n; ++i)
                {
                    v1_test.col(i) = Vector3d(distribution_(generator_), distribution_(generator_), 1.0).normalized();
                    v2_test.col(i) = Vector3d(distribution_(generator_), distribution_(generator_), 1.0).normalized();
                    weights_test(i) = 1.0;

                    const Vector3d &p1 = v1_test.col(i);
                    const Vector3d &p2 = v2_test.col(i);
                    for (int j = 0; j < 3; ++j)
                    {
                        for (int k = 0; k < 3; ++k)
                        {
                            A_test(i, j * 3 + k) = p2(j) * p1(k);
                        }
                    }
                }

                // 测试各种方法
                std::vector<double> times(8);

                // 原始版本
                auto start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                auto end = std::chrono::high_resolution_clock::now();
                times[0] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                // 优化版本1
                start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2_sum_optimized(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                end = std::chrono::high_resolution_clock::now();
                times[1] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                // 优化版本2
                start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2_stack_optimized(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                end = std::chrono::high_resolution_clock::now();
                times[2] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                // 优化版本3
                start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2_direct_optimized(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                end = std::chrono::high_resolution_clock::now();
                times[3] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                // 超级深度优化版本1：SIMD
                start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2_simd_optimized(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                end = std::chrono::high_resolution_clock::now();
                times[4] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                // 超级深度优化版本2：Ultra
                start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2_ultra_optimized(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                end = std::chrono::high_resolution_clock::now();
                times[5] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                // 极致优化版本：Matrix-Free
                start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2_matrix_free_optimized(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                end = std::chrono::high_resolution_clock::now();
                times[6] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                // 裸机优化版本：Bare-Metal
                start = std::chrono::high_resolution_clock::now();
                for (int i = 0; i < iterations; ++i)
                {
                    double ratio;
                    RT_Check2_bare_metal_optimized(A_test, v1_test, v2_test, R_sols_, t_sols_, &weights_test, ratio);
                }
                end = std::chrono::high_resolution_clock::now();
                times[7] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count() / (double)iterations;

                std::cout << std::setw(8) << n
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[0]
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[1]
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[2]
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[3]
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[4]
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[5]
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[6]
                          << std::setw(15) << std::fixed << std::setprecision(2) << times[7]
                          << std::endl;
            }
        }

    } // namespace test
} // namespace PoSDK