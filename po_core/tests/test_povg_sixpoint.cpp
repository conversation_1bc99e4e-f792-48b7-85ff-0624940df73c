/**
 * @file test_povg_sixpoint.cpp
 * @brief PoVG SixPoint 方法测试 - 两阶段优化性能评估
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>

namespace
{
    using namespace PoSDK;

    class TestPovgSixPoint : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            std::string config_name;       // 配置名称，用作ProfileCommit
            double max_parallax = 2.0;     // 平移幅度
            double max_rotation = 0.3;     // 旋转幅度(弧度)
            size_t num_points = 100;       // 特征点数量
            double noise_level = 0.0;      // 噪声水平
            double outlier_fraction = 0.0; // 外点比例
            int random_seed = 42;          // 随机种子
        };

        // 优化器配置结构体
        struct OptimizerConfig
        {
            std::string config_name;
            bool enable_two_stage;
            std::string optimizer_coster; // 残差函数类型
            std::string loss_type;        // 损失函数类型
            std::string optimizer_type;   // 优化器类型
        };

        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 设置conda环境用于Python绘图（根据您的环境调整）
            Interface::EvaluatorManager::SetCondaEnv("pymagsac_py312");

            // 初始化method_povgSixPoint方法
            povg_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_povgSixPoint"));
            ASSERT_TRUE(povg_method_ != nullptr) << "Failed to create method_povgSixPoint";
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 使用OpenGVSimulator生成场景数据并创建真值
        DataPtr GenerateSceneAndGroundTruth(const SceneConfig &config)
        {
            // 创建OpenGV仿真器
            auto simulator = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("opengv_simulator"));
            EXPECT_TRUE(simulator != nullptr) << "Failed to create OpenGV simulator";

            // 设置仿真器参数
            MethodOptions simulator_options{
                {"max_parallax", std::to_string(config.max_parallax)},
                {"max_rotation", std::to_string(config.max_rotation)},
                {"num_points", std::to_string(config.num_points)},
                {"noise_level", std::to_string(config.noise_level)},
                {"outlier_fraction", std::to_string(config.outlier_fraction)},
                {"random_seed", std::to_string(config.random_seed)}};

            simulator->SetMethodOptions(simulator_options);

            // 运行仿真器生成数据
            auto simulator_data = simulator->Build();

            auto data_package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
            EXPECT_TRUE(data_package != nullptr) << "Simulator data is not a DataPackage";

            // 使用字典式访问方式获取method_povgSixPoint数据和真值数据
            auto povg_data = (*data_package)["method_povgSixPoint"];
            auto gt_data = (*data_package)["ground_truth"];

            EXPECT_TRUE(povg_data != nullptr) << "Failed to get PoVG data from simulator";
            EXPECT_TRUE(gt_data != nullptr) << "Failed to get ground truth data from simulator";

            // 设置method_povgSixPoint输入数据
            povg_method_->SetRequiredData(povg_data);

            return gt_data;
        }

        // 使用评估器系统运行PoVG SixPoint估计器
        bool RunPovgSixPointWithEvaluator(const OptimizerConfig &opt_config,
                                          const SceneConfig &scene_config)
        {
            // 生成场景数据和真值
            auto gt_data = GenerateSceneAndGroundTruth(scene_config);
            if (!gt_data)
            {
                std::cerr << "Failed to generate scene data" << std::endl;
                return false;
            }

            // 配置method_povgSixPoint选项
            MethodOptions povg_options{
                {"enable_evaluator", "true"},
                {"ProfileCommit", scene_config.config_name},
                {"view_i", "0"},
                {"view_j", "1"},
                {"log_level", "1"},

                // 两阶段优化配置
                {"enable_two_stage_refinement", opt_config.enable_two_stage ? "true" : "false"},

                // TwoViewOptimizer 配置（仅在两阶段优化启用时生效）
                {"optimizer_coster", opt_config.optimizer_coster},
                {"loss_type", opt_config.loss_type},
                {"optimizer_type", opt_config.optimizer_type},
                {"max_iterations", "50"},
                {"convergence_threshold", "1e-8"},
                {"huber_threshold", "0.01"},

                // Eigen LM 特定参数
                {"eigen_lm_ftol", "1e-8"},
                {"eigen_lm_xtol", "1e-8"},
                {"eigen_lm_maxfev", "500"}};

            povg_method_->SetMethodOptions(povg_options);

            // 设置算法名称用于评估器
            std::string algorithm_name = opt_config.config_name;
            auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(povg_method_);
            if (profiler)
            {
                profiler->SetEvaluatorAlgorithm(algorithm_name);
                profiler->SetGTData(gt_data);
            }

            // 运行估计器
            auto result = povg_method_->Build();
            return result != nullptr;
        }

        // 显示评估结果
        void ShowEvaluationResults()
        {
            std::cout << "\n"
                      << std::string(60, '=') << std::endl;
            std::cout << "POVG SIXPOINT TWO-STAGE OPTIMIZATION EVALUATION RESULTS" << std::endl;
            std::cout << std::string(60, '=') << std::endl;

            // 1. 打印所有评估报告
            std::cout << "\n1. All Evaluation Reports:" << std::endl;
            Interface::EvaluatorManager::PrintAllEvaluationReports();

            // 2. 打印算法对比
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
            for (const auto &eval_type : eval_types)
            {
                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
                for (const auto &algorithm : algorithms)
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
                        Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
                    }
                }
            }

            // 3. 导出CSV文件和可视化
            TestCSVExport();

            std::cout << std::string(60, '=') << std::endl;
        }

        // 测试CSV导出和可视化功能
        void TestCSVExport()
        {
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();

            for (const auto &eval_type : eval_types)
            {
                // 获取当前测试信息
                const ::testing::TestInfo *const test_info =
                    ::testing::UnitTest::GetInstance()->current_test_info();
                std::string test_name = std::string(test_info->test_case_name()) + "_" +
                                        std::string(test_info->name());

                // 添加时间戳确保唯一性
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);

                std::ostringstream timestamp;
                timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

                std::string unique_dir_name = test_name + "_" + timestamp.str();

                // 创建测试输出目录
                std::filesystem::path test_output_dir = unique_dir_name;
                std::filesystem::create_directories(test_output_dir);

                std::cout << "\n=== 测试CSV导出和可视化功能 ===" << std::endl;

                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

                // 测试导出详细统计
                for (const auto &algorithm : algorithms)
                {
                    std::filesystem::path detailed_path = test_output_dir / (eval_type + "_" + algorithm + "_detailed.csv");
                    bool detail_success = Interface::EvaluatorManager::ExportDetailedStatsToCSV(eval_type, algorithm, detailed_path);
                    std::cout << "Export detailed stats for " << eval_type << "::" << algorithm << ": "
                              << (detail_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                // 测试单个指标导出和可视化
                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的对比表格..." << std::endl;

                        // 所有统计类型导出
                        std::filesystem::path all_stats_path = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        bool all_stats_success = Interface::EvaluatorManager::ExportMetricAllStatsToCSV(
                            eval_type, metric, all_stats_path);
                        std::cout << "Export all stats for " << eval_type << "::" << metric << ": "
                                  << (all_stats_success ? "SUCCESS" : "FAILED") << std::endl;

                        // 一键导出CSV并生成图表
                        std::filesystem::path combined_dir = test_output_dir / "combined_output";
                        std::vector<std::string> combined_stat_types = {"Mean", "Median", "Min", "Max"};

                        bool combined_success = Interface::EvaluatorManager::ExportMetricWithVisualization(
                            eval_type, metric, combined_dir, combined_stat_types);

                        std::cout << "Export CSV with visualization for " << eval_type << "::" << metric << ": "
                                  << (combined_success ? "SUCCESS" : "FAILED") << std::endl;
                    }
                }

                // 导出原始评估值
                std::filesystem::path raw_values_dir = test_output_dir / "raw_values";
                bool raw_success = Interface::EvaluatorManager::ExportAllRawValuesToCSV(eval_type, raw_values_dir, "");
                std::cout << "Export raw values for " << eval_type << ": "
                          << (raw_success ? "SUCCESS" : "FAILED") << std::endl;

                std::cout << "\n=== 可视化绘图功能测试完成 ===" << std::endl;
                std::cout << "图表文件保存在以下目录:" << std::endl;
                std::cout << "  - " << test_output_dir / "combined_output" / "plots" << std::endl;
            }
        }

        // 成员变量
        Interface::MethodPresetPtr povg_method_;
    };

    // 主要测试：两阶段优化性能评估
    TEST_F(TestPovgSixPoint, TwoStageOptimizationTest)
    {
        const int NUM_REPEATS = 20; // 重复测试次数，增加统计数据

        std::cout << "\n=== PoVG SixPoint 两阶段优化性能评估测试 ===" << std::endl;

        // 定义测试场景配置
        std::vector<SceneConfig> test_scenes = {
            {"clean_scene", 2.0, 0.3, 100, 0.0, 0.0, 42},      // 理想场景
            {"noisy_scene", 2.0, 0.3, 100, 0.5, 0.1, 42},      // 噪声+少量外点
            {"challenging_scene", 2.0, 0.3, 100, 1.0, 0.2, 42} // 挑战性场景
        };

        // 定义优化器配置 - 测试不同的 optimizer_coster 和 loss_type 组合
        std::vector<OptimizerConfig> optimizer_configs = {
            // 基准：仅PoVG SixPoint (无两阶段优化)
            {"PoVG_Only", false, "", "", ""},

            // 两阶段优化：不同residual函数 + L2损失
            {"TwoStage_PPO_L2", true, "ppo", "l2", "eigen_lm"},
            {"TwoStage_PPO_OpenGV_L2", true, "ppo_opengv", "l2", "eigen_lm"},
            {"TwoStage_BA_L2", true, "ba", "l2", "eigen_lm"},
            {"TwoStage_Sampson_L2", true, "sampson", "l2", "eigen_lm"},

            // 两阶段优化：最佳residual函数 + 不同损失函数
            {"TwoStage_PPO_Huber", true, "ppo", "huber", "eigen_lm"},
            {"TwoStage_PPO_Cauchy", true, "ppo", "cauchy", "eigen_lm"},
            {"TwoStage_PPO_OpenGV_Huber", true, "ppo_opengv", "huber", "eigen_lm"},
            {"TwoStage_PPO_OpenGV_Cauchy", true, "ppo_opengv", "cauchy", "eigen_lm"}};

        std::cout << "测试配置: " << test_scenes.size() << " 个场景, "
                  << optimizer_configs.size() << " 个优化配置, "
                  << NUM_REPEATS << " 次重复" << std::endl;

        // 对每个场景进行测试
        for (const auto &scene : test_scenes)
        {
            std::cout << "\n--- 测试场景: " << scene.config_name << " ---" << std::endl;
            std::cout << "场景参数: noise=" << scene.noise_level
                      << ", outliers=" << (scene.outlier_fraction * 100) << "%" << std::endl;

            for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
            {
                std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << std::endl;

                // 为每次重复修改随机种子
                SceneConfig current_scene = scene;
                current_scene.random_seed = 42 + repeat;

                // 测试每个优化器配置
                for (const auto &opt_config : optimizer_configs)
                {
                    std::cout << "  测试配置: " << opt_config.config_name;

                    bool success = RunPovgSixPointWithEvaluator(opt_config, current_scene);

                    std::cout << " -> " << (success ? "成功" : "失败") << std::endl;

                    if (!success)
                    {
                        std::cerr << "    警告: 配置 " << opt_config.config_name
                                  << " 在场景 " << scene.config_name << " 中失败" << std::endl;
                    }
                }
            }
        }

        std::cout << "\n=== 两阶段优化性能评估测试完成，评估结果将在TearDown中显示 ===" << std::endl;
    }

    // // 补充测试：外点鲁棒性测试
    // TEST_F(TestPovgSixPoint, OutlierRobustnessTest)
    // {
    //     const int NUM_REPEATS = 10; // 重复测试次数

    //     std::cout << "\n=== PoVG SixPoint 外点鲁棒性测试 ===" << std::endl;

    //     // 定义不同外点比例的测试场景
    //     std::vector<double> outlier_ratios = {0.1, 0.2, 0.3, 0.4};

    //     // 选择最有希望的几个配置进行鲁棒性测试
    //     std::vector<OptimizerConfig> robust_configs = {
    //         {"PoVG_Only", false, "", "", ""},
    //         {"TwoStage_PPO_L2", true, "ppo", "l2", "eigen_lm"},
    //         {"TwoStage_PPO_OpenGV_Huber", true, "ppo_opengv", "huber", "eigen_lm"},
    //         {"TwoStage_Sampson_Huber", true, "sampson", "huber", "eigen_lm"}};

    //     std::cout << "测试配置: " << outlier_ratios.size() << " 个外点比例, "
    //               << robust_configs.size() << " 个优化配置, "
    //               << NUM_REPEATS << " 次重复" << std::endl;

    //     // 对每个外点比例进行测试
    //     for (double outlier_ratio : outlier_ratios)
    //     {
    //         std::string outlier_commit = "outlier_" + std::to_string(static_cast<int>(outlier_ratio * 100)) + "pct";
    //         std::cout << "\n--- 测试外点比例: " << (outlier_ratio * 100) << "% ---" << std::endl;

    //         for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
    //         {
    //             std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << std::endl;

    //             // 创建场景配置
    //             SceneConfig scene_config;
    //             scene_config.config_name = outlier_commit;
    //             scene_config.max_parallax = 2.0;
    //             scene_config.max_rotation = 0.3;
    //             scene_config.num_points = 200;  // 增加点数以支持高外点比例
    //             scene_config.noise_level = 0.5; // 固定中等噪声
    //             scene_config.outlier_fraction = outlier_ratio;
    //             scene_config.random_seed = 42 + repeat;

    //             // 测试每个优化器配置
    //             for (const auto &opt_config : robust_configs)
    //             {
    //                 std::cout << "  测试配置: " << opt_config.config_name;

    //                 bool success = RunPovgSixPointWithEvaluator(opt_config, scene_config);

    //                 std::cout << " -> " << (success ? "成功" : "失败") << std::endl;

    //                 if (!success)
    //                 {
    //                     std::cerr << "    警告: 配置 " << opt_config.config_name
    //                               << " 在外点比例 " << (outlier_ratio * 100) << "% 中失败" << std::endl;
    //                 }
    //             }
    //         }
    //     }

    //     std::cout << "\n=== 外点鲁棒性测试完成，评估结果将在TearDown中显示 ===" << std::endl;
    // }

} // namespace