/**
 * @file test_robust_LiRP.cpp
 * @brief 测试鲁棒LiRP方法与原生RANSAC/GNC-IRLS方法进行对比
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <tuple>

// 显式包含DataRelativePoses头文件
#include "data/data_relative_poses.hpp"

// 显式包含鲁棒估计器头文件

namespace
{
    using namespace PoSDK;

    class TestRobustLiRP : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 初始化LiRP方法作为基础方法
            lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(lirp_method_ != nullptr);

            // 初始化RANSAC估计器
            ransac_estimator_ = std::make_shared<RANSACEstimator<BearingPairs>>();
            ASSERT_TRUE(ransac_estimator_ != nullptr);

            // 初始化GNC-IRLS估计器
            gnc_irls_estimator_ = std::make_shared<GNCIRLSEstimator<BearingPairs>>();
            ASSERT_TRUE(gnc_irls_estimator_ != nullptr);

            // 初始化RobustLiRP方法
            robust_lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_robustLiRP"));
            ASSERT_TRUE(robust_lirp_method_ != nullptr);
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 使用OpenGVSimulator生成场景数据并创建真值
        DataPtr GenerateSceneAndGroundTruth(const std::string &profile_commit,
                                            const MethodOptions &simulator_options)
        {
            // 创建OpenGV仿真器
            auto simulator = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("opengv_simulator"));
            EXPECT_TRUE(simulator != nullptr) << "Failed to create OpenGV simulator";

            // 设置仿真器参数
            simulator->SetMethodOptions(simulator_options);

            // 运行仿真器生成数据
            auto simulator_data = simulator->Build();
            EXPECT_TRUE(simulator_data != nullptr) << "Simulator failed to generate data";

            auto data_package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
            EXPECT_TRUE(data_package != nullptr) << "Simulator data is not a DataPackage";

            // 使用字典式访问方式获取LiRP数据和真值数据
            auto lirp_data = (*data_package)["method_LiRP"];
            auto gt_data = (*data_package)["ground_truth"];

            EXPECT_TRUE(lirp_data != nullptr) << "Failed to get LiRP data from simulator";
            EXPECT_TRUE(gt_data != nullptr) << "Failed to get ground truth data from simulator";

            // 设置各个方法的输入数据
            auto bearing_sample = std::dynamic_pointer_cast<DataSample<BearingPairs>>(lirp_data);
            EXPECT_TRUE(bearing_sample != nullptr) << "Failed to cast to BearingPairs sample";

            // 设置LiRP方法数据
            lirp_method_->SetRequiredData(lirp_data);

            // 设置鲁棒估计器数据
            ransac_estimator_->SetRequiredData(bearing_sample);
            gnc_irls_estimator_->SetRequiredData(bearing_sample);
            robust_lirp_method_->SetRequiredData(lirp_data);

            return gt_data;
        }

        // 配置RANSAC估计器
        void ConfigureRANSAC(const std::string &profile_commit)
        {
            MethodOptions ransac_options{
                {"model_estimator_type", "method_LiRP"},
                {"cost_evaluator_type", "method_relative_cost"},
                {"max_iterations", "1000"},
                {"min_iterations", "50"},
                {"confidence", "0.99"},
                {"inlier_threshold", "1e-4"},
                {"min_sample_size", "8"},
                {"enable_evaluator", "true"},
                {"ProfileCommit", profile_commit}};
            ransac_estimator_->SetMethodOptions(ransac_options);

            // 设置LiRP特定的选项
            MethodOptions lirp_options{
                {"compute_mode", "false"},
                {"use_opt_mode", "false"},
                {"identify_mode", "PPO"},
                {"debug_output", "false"},
                {"enable_profiling", "false"},
                {"view_i", "0"},
                {"view_j", "1"}};
            ransac_estimator_->SetModelEstimatorOptions(lirp_options);

            // 设置代价评估器选项
            MethodOptions cost_options{
                {"debug_output", "false"},
                {"residual_type", "PPO"},
                {"enable_profiling", "false"}};
            ransac_estimator_->SetCostEvaluatorOptions(cost_options);
        }

        // 配置GNC-IRLS估计器
        void ConfigureGNCIRLS(const std::string &profile_commit)
        {
            MethodOptions gnc_irls_options{
                {"model_estimator_type", "method_LiRP"},
                {"cost_evaluator_type", "method_relative_cost"},
                {"max_iterations", "20"},
                {"min_iterations", "3"},
                {"noise_scale", "3.0"},
                {"convergence_threshold", "1e-10"},
                {"gamma", "1.4"},
                {"use_majorization", "true"},
                {"use_superlinear", "true"},
                {"min_inlier_ratio", "0.1"},
                {"sigma_mode", "2"},
                {"fixed_sigma", "1.0"},
                {"inlier_threshold", "1e-4"},
                {"track_best_model", "false"},
                {"debug_output", "false"},
                {"enable_evaluator", "true"},
                {"ProfileCommit", profile_commit}};
            gnc_irls_estimator_->SetMethodOptions(gnc_irls_options);

            // 设置相同的LiRP和代价评估器选项
            MethodOptions lirp_options{
                {"compute_mode", "false"},
                {"use_opt_mode", "false"},
                {"identify_mode", "PPO"},
                {"debug_output", "false"},
                {"enable_profiling", "false"},
                {"view_i", "0"},
                {"view_j", "1"}};
            gnc_irls_estimator_->SetModelEstimatorOptions(lirp_options);

            MethodOptions cost_options{
                {"debug_output", "false"},
                {"residual_type", "PPO"},
                {"enable_profiling", "false"}};
            gnc_irls_estimator_->SetCostEvaluatorOptions(cost_options);
        }

        // 配置RobustLiRP方法
        void ConfigureRobustLiRP(const std::string &robust_type, const std::string &profile_commit)
        {
            MethodOptions robust_options{
                {"robust_type", robust_type},
                {"cost_evaluator_type", "method_relative_cost"},
                {"compute_mode", "false"},
                {"use_opt_mode", "false"},
                {"identify_mode", "PPO"},
                {"debug_output", "false"},
                {"enable_evaluator", "true"},
                {"enable_profiling", "false"},
                {"ProfileCommit", profile_commit},
                {"view_i", "0"},
                {"view_j", "1"}};

            if (robust_type == "ransac")
            {
                robust_options["ransac_max_iterations"] = "1000";
                robust_options["ransac_min_iterations"] = "50";
                robust_options["ransac_confidence"] = "0.99";
                robust_options["ransac_inlier_threshold"] = "1e-4";
                robust_options["ransac_min_sample_size"] = "8";
            }
            else if (robust_type == "gnc_irls")
            {
                robust_options["gnc_irls_max_iterations"] = "20";
                robust_options["gnc_irls_min_iterations"] = "3";
                robust_options["gnc_irls_noise_scale"] = "3.0";
                robust_options["gnc_irls_convergence_threshold"] = "1e-10";
                robust_options["gnc_irls_gamma"] = "1.4";
                robust_options["gnc_irls_use_majorization"] = "true";
                robust_options["gnc_irls_use_superlinear"] = "true";
                robust_options["gnc_irls_min_inlier_ratio"] = "0.1";
                robust_options["gnc_irls_sigma_mode"] = "2";
                robust_options["gnc_irls_fixed_sigma"] = "1.0";
                robust_options["gnc_irls_inlier_threshold"] = "1e-4";
                robust_options["gnc_irls_track_best_model"] = "false";
            }

            robust_lirp_method_->SetMethodOptions(robust_options);
        }

        // 运行单个测试场景
        void RunScenario(const std::string &scenario_name,
                         const MethodOptions &simulator_options,
                         int num_repeats = 5)
        {
            if (log_level_ > 0)
            {
                std::cout << "\n=== Running scenario: " << scenario_name
                          << " (repeating " << num_repeats << " times) ===" << std::endl;
            }

            // 重复运行测试以生成更多评估数据
            for (int repeat = 0; repeat < num_repeats; ++repeat)
            {
                if (log_level_ > 1)
                {
                    std::cout << "\n--- Repeat " << (repeat + 1) << "/" << num_repeats
                              << " for " << scenario_name << " ---" << std::endl;
                }

                // 生成场景数据和真值
                auto gt_data = GenerateSceneAndGroundTruth(scenario_name, simulator_options);

                // 设置真值数据用于评估器
                auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(lirp_method_);
                if (profiler)
                {
                    profiler->SetGTData(gt_data);
                }

                auto ransac_profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(ransac_estimator_);
                if (ransac_profiler)
                {
                    ransac_profiler->SetGTData(gt_data);
                }

                auto gnc_irls_profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(gnc_irls_estimator_);
                if (gnc_irls_profiler)
                {
                    gnc_irls_profiler->SetGTData(gt_data);
                }

                auto robust_profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(robust_lirp_method_);
                if (robust_profiler)
                {
                    robust_profiler->SetGTData(gt_data);
                }

                // 验证基础算法结果
                auto lirp_result = lirp_method_->Build();
                ASSERT_TRUE(lirp_result != nullptr) << "LiRP method failed for scenario: " << scenario_name;

                if (log_level_ > 1)
                {
                    std::cout << "  Repeat " << (repeat + 1) << " completed successfully" << std::endl;
                }
            }
        }

        // 显示评估结果
        void ShowEvaluationResults()
        {
            if (log_level_ > 0)
            {
                std::cout << "\n"
                          << std::string(60, '=') << std::endl;
                std::cout << "ROBUST LIRP EVALUATION RESULTS" << std::endl;
                std::cout << std::string(60, '=') << std::endl;

                // 1. 打印所有评估报告
                std::cout << "\n1. All Evaluation Reports:" << std::endl;
                Interface::EvaluatorManager::PrintAllEvaluationReports();

                // 2. 打印算法对比
                auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
                for (const auto &eval_type : eval_types)
                {
                    auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
                    for (const auto &algorithm : algorithms)
                    {
                        auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                        for (const auto &metric : metrics)
                        {
                            std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
                            Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
                        }
                    }
                }

                std::cout << std::string(60, '=') << std::endl;
            }
        }

        // 成员变量
        Interface::MethodPresetPtr lirp_method_;
        Interface::RobustEstimatorPtr ransac_estimator_;
        Interface::RobustEstimatorPtr gnc_irls_estimator_;
        Interface::MethodPresetPtr robust_lirp_method_;

        int log_level_ = 1;          // 设置为1，减少冗余输出
        int num_repeated_tests_ = 5; // 减少重复次数以加快测试
    };

    // 基础RANSAC测试
    TEST_F(TestRobustLiRP, BasicRANSACTest)
    {
        std::cout << "\n🔍 Testing Basic RANSAC Robust LiRP" << std::endl;

        MethodOptions simulator_options{
            {"max_parallax", "2.0"},
            {"max_rotation", "0.3"},
            {"num_points", "100"},
            {"noise_level", "0.5"},
            {"outlier_fraction", "0.1"},
            {"random_seed", "42"}};

        // 配置并测试RANSAC
        ConfigureRANSAC("BasicRANSACTest");
        RunScenario("BasicRANSACTest", simulator_options, num_repeated_tests_);

        // 运行RANSAC估计器
        auto ransac_result = ransac_estimator_->Build();
        ASSERT_TRUE(ransac_result != nullptr) << "RANSAC estimator failed";

        auto pose = GetDataPtr<RelativePose>(ransac_result);
        ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from RANSAC result";

        std::cout << "✅ Basic RANSAC test completed successfully" << std::endl;
    }

    // 基础GNC-IRLS测试
    TEST_F(TestRobustLiRP, BasicGNCIRLSTest)
    {
        std::cout << "\n🔍 Testing Basic GNC-IRLS Robust LiRP" << std::endl;

        MethodOptions simulator_options{
            {"max_parallax", "2.0"},
            {"max_rotation", "0.3"},
            {"num_points", "100"},
            {"noise_level", "0.5"},
            {"outlier_fraction", "0.1"},
            {"random_seed", "42"}};

        // 配置并测试GNC-IRLS
        ConfigureGNCIRLS("BasicGNCIRLSTest");
        RunScenario("BasicGNCIRLSTest", simulator_options, num_repeated_tests_);

        // 运行GNC-IRLS估计器
        auto gnc_irls_result = gnc_irls_estimator_->Build();
        ASSERT_TRUE(gnc_irls_result != nullptr) << "GNC-IRLS estimator failed";

        auto pose = GetDataPtr<RelativePose>(gnc_irls_result);
        ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from GNC-IRLS result";

        std::cout << "✅ Basic GNC-IRLS test completed successfully" << std::endl;
    }

    // RobustLiRP RANSAC模式测试
    TEST_F(TestRobustLiRP, RobustLiRPRANSACTest)
    {
        std::cout << "\n🔍 Testing RobustLiRP in RANSAC mode" << std::endl;

        MethodOptions simulator_options{
            {"max_parallax", "2.0"},
            {"max_rotation", "0.3"},
            {"num_points", "100"},
            {"noise_level", "0.5"},
            {"outlier_fraction", "0.1"},
            {"random_seed", "42"}};

        // 配置并测试RobustLiRP RANSAC模式
        ConfigureRobustLiRP("ransac", "RobustLiRPRANSACTest");
        RunScenario("RobustLiRPRANSACTest", simulator_options, num_repeated_tests_);

        // 运行RobustLiRP
        auto robust_result = robust_lirp_method_->Build();
        ASSERT_TRUE(robust_result != nullptr) << "RobustLiRP RANSAC failed";

        auto pose = GetDataPtr<RelativePose>(robust_result);
        ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from RobustLiRP RANSAC result";

        std::cout << "✅ RobustLiRP RANSAC test completed successfully" << std::endl;
    }

    // RobustLiRP GNC-IRLS模式测试
    TEST_F(TestRobustLiRP, RobustLiRPGNCIRLSTest)
    {
        std::cout << "\n🔍 Testing RobustLiRP in GNC-IRLS mode" << std::endl;

        MethodOptions simulator_options{
            {"max_parallax", "2.0"},
            {"max_rotation", "0.3"},
            {"num_points", "100"},
            {"noise_level", "0.5"},
            {"outlier_fraction", "0.1"},
            {"random_seed", "42"}};

        // 配置并测试RobustLiRP GNC-IRLS模式
        ConfigureRobustLiRP("gnc_irls", "RobustLiRPGNCIRLSTest");
        RunScenario("RobustLiRPGNCIRLSTest", simulator_options, num_repeated_tests_);

        // 运行RobustLiRP
        auto robust_result = robust_lirp_method_->Build();
        ASSERT_TRUE(robust_result != nullptr) << "RobustLiRP GNC-IRLS failed";

        auto pose = GetDataPtr<RelativePose>(robust_result);
        ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from RobustLiRP GNC-IRLS result";

        std::cout << "✅ RobustLiRP GNC-IRLS test completed successfully" << std::endl;
    }

    // 高外点比例鲁棒性测试
    TEST_F(TestRobustLiRP, HighOutlierRobustnessTest)
    {
        std::cout << "\n🔍 Testing Robustness with High Outlier Ratio" << std::endl;

        // 测试不同外点比例下的性能
        std::vector<double> outlier_ratios = {0.2, 0.3, 0.4, 0.5};

        for (double outlier_ratio : outlier_ratios)
        {
            std::string scenario_name = "HighOutlier_" + std::to_string(static_cast<int>(outlier_ratio * 100)) + "pct";

            MethodOptions simulator_options{
                {"max_parallax", "2.0"},
                {"max_rotation", "0.3"},
                {"num_points", "200"}, // 增加点数以支持高外点比例
                {"noise_level", "1.0"},
                {"outlier_fraction", std::to_string(outlier_ratio)},
                {"random_seed", "42"}};

            // 测试RANSAC
            ConfigureRANSAC(scenario_name + "_RANSAC");
            RunScenario(scenario_name + "_RANSAC", simulator_options, 3);

            // 测试GNC-IRLS
            ConfigureGNCIRLS(scenario_name + "_GNCI");
            RunScenario(scenario_name + "_GNCI", simulator_options, 3);

            // 测试RobustLiRP RANSAC模式
            ConfigureRobustLiRP("ransac", scenario_name + "_RobustRANSAC");
            RunScenario(scenario_name + "_RobustRANSAC", simulator_options, 3);

            if (log_level_ > 0)
            {
                std::cout << "✅ Completed outlier ratio: " << outlier_ratio << std::endl;
            }
        }

        std::cout << "✅ High outlier robustness test completed successfully" << std::endl;
    }

    // 噪声水平测试
    TEST_F(TestRobustLiRP, NoiseLevelTest)
    {
        std::cout << "\n🔍 Testing Performance across Different Noise Levels" << std::endl;

        // 测试不同噪声水平下的性能
        std::vector<double> noise_levels = {0.0, 0.5, 1.0, 2.0};

        for (double noise_level : noise_levels)
        {
            std::string scenario_name = "NoiseLevel_" + std::to_string(static_cast<int>(noise_level * 10)) + "x";

            MethodOptions simulator_options{
                {"max_parallax", "2.0"},
                {"max_rotation", "0.3"},
                {"num_points", "150"},
                {"noise_level", std::to_string(noise_level)},
                {"outlier_fraction", "0.15"},
                {"random_seed", "42"}};

            // 测试RobustLiRP RANSAC模式
            ConfigureRobustLiRP("ransac", scenario_name + "_RobustRANSAC");
            RunScenario(scenario_name + "_RobustRANSAC", simulator_options, 3);

            // 测试RobustLiRP GNC-IRLS模式
            ConfigureRobustLiRP("gnc_irls", scenario_name + "_RobustGNCI");
            RunScenario(scenario_name + "_RobustGNCI", simulator_options, 3);

            if (log_level_ > 0)
            {
                std::cout << "✅ Completed noise level: " << noise_level << std::endl;
            }
        }

        std::cout << "✅ Noise level test completed successfully" << std::endl;
    }

} // namespace
