/**
 * @file test_robust_LiRP.cpp
 * @brief 测试鲁棒LiRP方法与原生RANSAC/GNC-IRLS方法进行对比
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <tuple>

// 显式包含DataRelativePoses头文件
#include "data/data_relative_poses.hpp"

namespace
{
    using namespace PoSDK;

    class TestRobustLiRP : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            std::string config_name;       // 配置名称，用作ProfileCommit
            double max_parallax = 2.0;     // 平移幅度
            double max_rotation = 0.3;     // 旋转幅度(弧度)
            size_t num_points = 150;       // 特征点数量
            double noise_level = 0.0;      // 噪声水平
            double outlier_fraction = 0.0; // 外点比例
            int random_seed = 42;          // 随机种子
        };

        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 初始化LiRP方法作为基础方法
            lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(lirp_method_ != nullptr);

            // 初始化RANSAC估计器
            ransac_estimator_ = std::make_shared<RANSACEstimator<BearingPairs>>();
            ASSERT_TRUE(ransac_estimator_ != nullptr);

            // 初始化GNC-IRLS估计器
            gnc_irls_estimator_ = std::make_shared<GNCIRLSEstimator<BearingPairs>>();
            ASSERT_TRUE(gnc_irls_estimator_ != nullptr);

            // 初始化RobustLiRP方法
            robust_lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_robustLiRP"));
            ASSERT_TRUE(robust_lirp_method_ != nullptr);
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 使用OpenGVSimulator生成场景数据并创建真值
        DataPtr GenerateSceneAndGroundTruth(const SceneConfig &config)
        {
            // 创建OpenGV仿真器
            auto simulator = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("opengv_simulator"));
            EXPECT_TRUE(simulator != nullptr) << "Failed to create OpenGV simulator";

            // 设置仿真器参数
            MethodOptions simulator_options{
                {"max_parallax", std::to_string(config.max_parallax)},
                {"max_rotation", std::to_string(config.max_rotation)},
                {"num_points", std::to_string(config.num_points)},
                {"noise_level", std::to_string(config.noise_level)},
                {"outlier_fraction", std::to_string(config.outlier_fraction)},
                {"random_seed", std::to_string(config.random_seed)}};

            simulator->SetMethodOptions(simulator_options);

            // 运行仿真器生成数据
            auto simulator_data = simulator->Build();

            auto data_package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
            EXPECT_TRUE(data_package != nullptr) << "Simulator data is not a DataPackage";

            // 使用字典式访问方式获取LiRP数据和真值数据
            auto lirp_data = (*data_package)["method_LiRP"];
            auto gt_data = (*data_package)["ground_truth"];

            EXPECT_TRUE(lirp_data != nullptr) << "Failed to get LiRP data from simulator";
            EXPECT_TRUE(gt_data != nullptr) << "Failed to get ground truth data from simulator";

            // 设置各个方法的输入数据
            auto bearing_sample = std::dynamic_pointer_cast<DataSample<BearingPairs>>(lirp_data);
            EXPECT_TRUE(bearing_sample != nullptr) << "Failed to cast to BearingPairs sample";

            // 设置LiRP方法数据
            lirp_method_->SetRequiredData(lirp_data);

            // 设置鲁棒估计器数据
            ransac_estimator_->SetRequiredData(lirp_data);
            gnc_irls_estimator_->SetRequiredData(lirp_data);
            robust_lirp_method_->SetRequiredData(lirp_data);

            return gt_data;
        }

        // 使用评估器系统运行单个估计器配置
        bool RunEstimatorWithEvaluator(const std::string &estimator_name,
                                       const std::string &algorithm_type,
                                       const SceneConfig &config)
        {
            // 生成场景数据和真值
            auto gt_data = GenerateSceneAndGroundTruth(config);

            // 根据估计器类型配置和运行
            if (estimator_name == "method_LiRP")
            {
                return RunLiRPEstimator(config, gt_data);
            }
            else if (estimator_name == "RANSAC")
            {
                return RunRANSACEstimator(config, gt_data);
            }
            else if (estimator_name == "GNC_IRLS")
            {
                return RunGNCIRLSEstimator(config, gt_data);
            }
            else if (estimator_name == "method_RobustLiRP")
            {
                return RunRobustLiRPEstimator(algorithm_type, config, gt_data);
            }
            else
            {
                std::cerr << "Unknown estimator: " << estimator_name << std::endl;
                return false;
            }
        }

        // 运行method_LiRP估计器
        bool RunLiRPEstimator(const SceneConfig &config, DataPtr gt_data)
        {
            // 配置LiRP方法 - 使用与test_lirp_method.cpp相同的参数
            MethodOptions lirp_options{
                {"enable_evaluator", "true"}, // 启用评估
                {"ProfileCommit", config.config_name},
                {"view_i", "0"},
                {"view_j", "1"}};
            lirp_method_->SetMethodOptions(lirp_options);

            // 设置算法名称用于评估器
            auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(lirp_method_);
            if (profiler)
            {
                profiler->SetEvaluatorAlgorithm("method_LiRP");
                profiler->SetGTData(gt_data);
            }

            // 运行估计器
            auto result = lirp_method_->Build();
            return result != nullptr;
        }

        // 运行RANSAC估计器
        bool RunRANSACEstimator(const SceneConfig &config, DataPtr gt_data)
        {
            // 配置RANSAC估计器
            MethodOptions ransac_options{
                {"enable_evaluator", "true"},
                {"log_level", "2"},
                {"ProfileCommit", config.config_name}};
            ransac_estimator_->SetMethodOptions(ransac_options);

            // 设置算法名称用于评估器
            auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(ransac_estimator_);
            if (profiler)
            {
                profiler->SetEvaluatorAlgorithm("RANSAC");
                profiler->SetGTData(gt_data);
            }

            // 设置LiRP特定的选项
            MethodOptions lirp_options{
                {"view_i", "0"},
                {"view_j", "1"}};
            ransac_estimator_->SetModelEstimatorOptions(lirp_options);

            // 设置代价评估器选项
            MethodOptions cost_options{
                {"residual_type", "PPO"}};
            ransac_estimator_->SetCostEvaluatorOptions(cost_options);

            // 运行估计器
            auto result = ransac_estimator_->Build();
            return result != nullptr;
        }

        // 运行GNC-IRLS估计器
        bool RunGNCIRLSEstimator(const SceneConfig &config, DataPtr gt_data)
        {
            // 配置GNC-IRLS估计器
            MethodOptions gnc_irls_options{
                {"model_estimator_type", "method_LiRP"},
                {"cost_evaluator_type", "method_relative_cost"},
                {"max_iterations", "20"},
                {"noise_scale", "5.54"},
                {"inlier_threshold", "5*1e-3"},
                {"enable_evaluator", "true"},
                {"log_level", "2"},
                {"sigma_mode", "2"},
                {"ProfileCommit", config.config_name}};
            gnc_irls_estimator_->SetMethodOptions(gnc_irls_options);

            // 设置算法名称用于评估器
            auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(gnc_irls_estimator_);
            if (profiler)
            {
                profiler->SetEvaluatorAlgorithm("GNC_IRLS");
                profiler->SetGTData(gt_data);
            }

            // 设置相同的LiRP和代价评估器选项
            MethodOptions lirp_options{
                {"view_i", "0"},
                {"view_j", "1"},
                {"identify_mode", "PPO"},
            };
            gnc_irls_estimator_->SetModelEstimatorOptions(lirp_options);

            MethodOptions cost_options{
                {"residual_type", "PPO"}};
            gnc_irls_estimator_->SetCostEvaluatorOptions(cost_options);

            // 运行估计器
            auto result = gnc_irls_estimator_->Build();
            return result != nullptr;
        }

        // 运行RobustLiRP估计器
        bool RunRobustLiRPEstimator(const std::string &robust_type, const SceneConfig &config, DataPtr gt_data)
        {
            // 配置RobustLiRP方法
            MethodOptions robust_options{
                {"robust_type", robust_type},
                {"cost_evaluator_type", "method_relative_cost"},
                {"enable_evaluator", "true"},
                {"ProfileCommit", config.config_name},
                {"view_i", "0"},
                {"view_j", "1"},
                {"identify_mode", "sampson"},
                {"residual_type", "sampson"},
            };

            if (robust_type == "ransac")
            {
                robust_options["ransac_estimator|max_iterations"] = "50000";
                robust_options["ransac_estimator|confidence"] = "0.99";
                robust_options["ransac_estimator|inlier_threshold"] = "5*1e-3";
                robust_options["ransac_estimator|min_sample_size"] = "8";
            }
            else if (robust_type == "gnc_irls")
            {
                robust_options["gnc_irls_estimator|sigma_mode"] = "2";
                robust_options["gnc_irls_estimator|max_iterations"] = "20";
                robust_options["gnc_irls_estimator|noise_scale"] = "5.54";
                robust_options["gnc_irls_estimator|inlier_threshold"] = "5*1e-3";
            }
            else if (robust_type == "two_stage")
            {
                // 两阶段优化：先RANSAC后GNC-IRLS
                robust_options["robust_type"] = "ransac";               // 基础类型设为RANSAC
                robust_options["enable_two_stage_refinement"] = "true"; // 启用两阶段优化

                // RANSAC参数（第一阶段）- 使用新的前缀格式
                robust_options["ransac_estimator|max_iterations"] = "50000"; // 适中的迭代次数
                robust_options["ransac_estimator|confidence"] = "0.99";
                robust_options["ransac_estimator|inlier_threshold"] = "5*1e-3";
                robust_options["ransac_estimator|min_sample_size"] = "8";

                // GNC-IRLS参数（第二阶段精细优化）- 使用新的前缀格式
                robust_options["gnc_irls_estimator|max_iterations"] = "5"; // 稍少的迭代次数，因为已有好的初值
                robust_options["gnc_irls_estimator|noise_scale"] = "5.54";
                robust_options["gnc_irls_estimator|inlier_threshold"] = "5*1e-3";
            }

            robust_lirp_method_->SetMethodOptions(robust_options);

            // 设置算法名称用于评估器
            std::string algorithm_name;
            if (robust_type == "ransac")
            {
                algorithm_name = "method_RobustLiRP-RANSAC";
            }
            else if (robust_type == "gnc_irls")
            {
                algorithm_name = "method_RobustLiRP-GNC_IRLS";
            }
            else if (robust_type == "two_stage")
            {
                algorithm_name = "method_RobustLiRP-TwoStage"; // 两阶段优化的专用标识
            }
            else
            {
                algorithm_name = "method_RobustLiRP-" + robust_type;
            }

            auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(robust_lirp_method_);
            if (profiler)
            {
                profiler->SetEvaluatorAlgorithm(algorithm_name);
                profiler->SetGTData(gt_data);
            }

            // 运行估计器
            auto result = robust_lirp_method_->Build();
            return result != nullptr;
        }

        // 显示评估结果
        void ShowEvaluationResults()
        {
            std::cout << "\n"
                      << std::string(60, '=') << std::endl;
            std::cout << "ROBUST LIRP EVALUATION RESULTS" << std::endl;
            std::cout << std::string(60, '=') << std::endl;

            // 1. 打印所有评估报告
            std::cout << "\n1. All Evaluation Reports:" << std::endl;
            Interface::EvaluatorManager::PrintAllEvaluationReports();

            // 2. 打印算法对比并导出可视化
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
            for (const auto &eval_type : eval_types)
            {
                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
                for (const auto &algorithm : algorithms)
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
                        Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
                    }
                }
            }

            // 3. 导出CSV文件和可视化
            TestCSVExport();

            std::cout << std::string(60, '=') << std::endl;
        }

        // 测试CSV导出功能
        void TestCSVExport()
        {
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();

            for (const auto &eval_type : eval_types)
            {
                // 获取当前测试信息
                const ::testing::TestInfo *const test_info =
                    ::testing::UnitTest::GetInstance()->current_test_info();
                std::string test_name = std::string(test_info->test_case_name()) + "_" +
                                        std::string(test_info->name());

                // 添加时间戳确保唯一性
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);

                std::ostringstream timestamp;
                timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

                std::string unique_dir_name = test_name + "_" + timestamp.str();

                // 创建测试输出目录
                std::filesystem::path test_output_dir = unique_dir_name;
                std::filesystem::create_directories(test_output_dir);

                std::cout << "\n=== 测试CSV导出功能（智能解析评估结果）===" << std::endl;

                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

                // 测试导出详细统计
                for (const auto &algorithm : algorithms)
                {
                    std::filesystem::path detailed_path = test_output_dir / (eval_type + "_" + algorithm + "_detailed.csv");
                    bool detail_success = Interface::EvaluatorManager::ExportDetailedStatsToCSV(eval_type, algorithm, detailed_path);
                    std::cout << "Export detailed stats for " << eval_type << "::" << algorithm << ": "
                              << (detail_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                // 测试单个指标导出和可视化
                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的对比表格..." << std::endl;

                        // 所有统计类型导出
                        std::filesystem::path all_stats_path = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        bool all_stats_success = Interface::EvaluatorManager::ExportMetricAllStatsToCSV(
                            eval_type, metric, all_stats_path);
                        std::cout << "Export all stats for " << eval_type << "::" << metric << ": "
                                  << (all_stats_success ? "SUCCESS" : "FAILED") << std::endl;

                        // 一键导出CSV并生成图表
                        std::filesystem::path combined_dir = test_output_dir / "combined_output";
                        std::vector<std::string> combined_stat_types = {"Mean", "Median", "Min", "Max"};

                        bool combined_success = Interface::EvaluatorManager::ExportMetricWithVisualization(
                            eval_type, metric, combined_dir, combined_stat_types);

                        std::cout << "Export CSV with visualization for " << eval_type << "::" << metric << ": "
                                  << (combined_success ? "SUCCESS" : "FAILED") << std::endl;
                    }
                }

                // 导出原始评估值
                std::filesystem::path raw_values_dir = test_output_dir / "raw_values";
                bool raw_success = Interface::EvaluatorManager::ExportAllRawValuesToCSV(eval_type, raw_values_dir, "");
                std::cout << "Export raw values for " << eval_type << ": "
                          << (raw_success ? "SUCCESS" : "FAILED") << std::endl;

                std::cout << "\n=== 可视化绘图功能测试完成 ===" << std::endl;
                std::cout << "图表文件保存在以下目录:" << std::endl;
                std::cout << "  - " << test_output_dir / "combined_output" / "plots" << std::endl;
            }
        }

        // 成员变量
        Interface::MethodPresetPtr lirp_method_;
        Interface::RobustEstimatorPtr ransac_estimator_;
        Interface::RobustEstimatorPtr gnc_irls_estimator_;
        Interface::MethodPresetPtr robust_lirp_method_;
    };

    // // 测试1：不同噪声水平下的算法性能对比
    // TEST_F(TestRobustLiRP, NoiseRobustnessTest)
    // {
    //     const int NUM_REPEATS = 10; // 重复测试次数，增加统计数据

    //     std::cout << "\n=== 测试不同噪声水平下的算法性能对比 ===" << std::endl;

    //     // 定义不同噪声水平的测试场景
    //     std::vector<double> noise_levels = {0.0, 0.1, 0.5, 1.0, 2.0};

    //     // 定义要测试的估计器配置
    //     std::vector<std::pair<std::string, std::string>> estimator_configs = {
    //         {"method_LiRP", ""},
    //         {"RANSAC", ""},
    //         {"GNC_IRLS", ""},
    //         {"method_RobustLiRP", "ransac"},
    //         {"method_RobustLiRP", "gnc_irls"},
    //         {"method_RobustLiRP", "two_stage"}}; // 新增两阶段优化测试

    //     std::cout << "测试配置: " << noise_levels.size() << " 个噪声级别, "
    //               << estimator_configs.size() << " 个算法, "
    //               << NUM_REPEATS << " 次重复" << std::endl;

    //     // 对每个噪声级别进行测试
    //     for (double noise_level : noise_levels)
    //     {
    //         std::string noise_commit = "noise_" + std::to_string(static_cast<float>(noise_level)) + "x";
    //         std::cout << "\n--- 测试噪声级别: " << noise_level << " (commit: " << noise_commit << ") ---" << std::endl;

    //         for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
    //         {
    //             std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << std::endl;

    //             // 创建场景配置
    //             SceneConfig config;
    //             config.config_name = noise_commit;
    //             config.max_parallax = 2.0;
    //             config.max_rotation = 0.3;
    //             config.num_points = 150;
    //             config.noise_level = noise_level;
    //             config.outlier_fraction = 0;      // 固定10%外点
    //             config.random_seed = 42 + repeat; // 每次重复使用不同种子

    //             // 测试每个估计器配置
    //             for (const auto &[estimator, algorithm] : estimator_configs)
    //             {
    //                 std::string algorithm_display = algorithm.empty() ? "(default)" : algorithm;
    //                 std::cout << "  测试算法: " << estimator << " + " << algorithm_display;

    //                 bool success = RunEstimatorWithEvaluator(estimator, algorithm, config);

    //                 std::cout << " -> " << (success ? "成功" : "失败") << std::endl;

    //                 if (!success)
    //                 {
    //                     std::cerr << "    警告: 算法 " << estimator << " + " << algorithm_display
    //                               << " 在噪声级别 " << noise_level << " 中失败" << std::endl;
    //                 }
    //             }
    //         }
    //     }

    //     std::cout << "\n=== 噪声鲁棒性测试完成，评估结果将在TearDown中显示 ===" << std::endl;
    // }

    // 测试2：不同外点比例下的算法性能对比
    TEST_F(TestRobustLiRP, OutlierRobustnessTest)
    {
        const int NUM_REPEATS = 100; // 重复测试次数，增加统计数据

        std::cout << "\n=== 测试不同外点比例下的算法性能对比 ===" << std::endl;

        // 定义不同外点比例的测试场景
        std::vector<double> outlier_ratios = {0.1, 0.2, 0.3, 0.4, 0.5};

        // 定义要测试的估计器配置
        std::vector<std::pair<std::string, std::string>> estimator_configs = {
            {"method_LiRP", ""},
            {"RANSAC", ""},
            {"GNC_IRLS", ""},
            // {"method_RobustLiRP", "ransac"},
            {"method_RobustLiRP", "gnc_irls"},
            // {"method_RobustLiRP", "two_stage"}
        }; // 新增两阶段优化测试

        std::cout << "测试配置: " << outlier_ratios.size() << " 个外点比例, "
                  << estimator_configs.size() << " 个算法, "
                  << NUM_REPEATS << " 次重复" << std::endl;

        // 对每个外点比例进行测试
        for (double outlier_ratio : outlier_ratios)
        {
            std::string outlier_commit = "outlier_" + std::to_string(static_cast<int>(outlier_ratio * 100)) + "pct";
            std::cout << "\n--- 测试外点比例: " << (outlier_ratio * 100) << "% (commit: " << outlier_commit << ") ---" << std::endl;

            for (int repeat = 0; repeat < NUM_REPEATS; ++repeat)
            {
                std::cout << "重复 " << (repeat + 1) << "/" << NUM_REPEATS << std::endl;

                // 创建场景配置
                SceneConfig config;
                config.config_name = outlier_commit;
                config.max_parallax = 2.0;
                config.max_rotation = 0.3;
                config.num_points = 20;   // 增加点数以支持高外点比例
                config.noise_level = 0.1; // 固定中等噪声
                config.outlier_fraction = outlier_ratio;
                config.random_seed = 42 + repeat; // 每次重复使用不同种子

                // 测试每个估计器配置
                for (const auto &[estimator, algorithm] : estimator_configs)
                {
                    std::string algorithm_display = algorithm.empty() ? "(default)" : algorithm;
                    std::cout << "  测试算法: " << estimator << " + " << algorithm_display;

                    bool success = RunEstimatorWithEvaluator(estimator, algorithm, config);

                    std::cout << " -> " << (success ? "成功" : "失败") << std::endl;

                    if (!success)
                    {
                        std::cerr << "    警告: 算法 " << estimator << " + " << algorithm_display
                                  << " 在外点比例 " << (outlier_ratio * 100) << "% 中失败" << std::endl;
                    }
                }
            }
        }

        std::cout << "\n=== 外点鲁棒性测试完成，评估结果将在TearDown中显示 ===" << std::endl;
    }

} // namespace
