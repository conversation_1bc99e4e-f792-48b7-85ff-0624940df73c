/**
 * @file test_tracks_io.cpp
 * @brief 测试tracks文件IO功能
 * @details 测试file_io.cpp中LoadTracks和SaveTracks函数的正确性
 *
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <random>
#include <filesystem>
#include <fstream>

namespace
{
    using namespace PoSDK;
    using namespace types;

    class TestTracksIO : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            test_dir_ = std::filesystem::temp_directory_path() / "pomvg_tracks_io_test";
            std::filesystem::create_directories(test_dir_);
            test_tracks_file_ = test_dir_ / "test_tracks.tracks";
        }

        void TearDown() override
        {
            std::filesystem::remove_all(test_dir_);
        }

        // 生成随机Track数据
        void GenerateRandomTracks(Tracks &tracks, size_t num_tracks = 30, size_t obs_per_track = 10)
        {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<ViewId> view_dist(0, 10);           // 视图索引范围
            std::uniform_int_distribution<PtsId> pts_dist(0, 100);            // 点索引范围
            std::uniform_real_distribution<double> coord_dist(-100.0, 100.0); // 坐标范围

            tracks.clear();
            tracks.reserve(num_tracks);

            for (size_t i = 0; i < num_tracks; ++i)
            {
                TrackInfo track_info;
                track_info.is_used = true;

                for (size_t j = 0; j < obs_per_track; ++j)
                {
                    types::ObsInfo obs;
                    obs.view_id = view_dist(gen);
                    obs.pts_id = static_cast<PtsId>(i); // 使用track索引作为pts_id
                    obs.coord = Eigen::Vector2d(
                        coord_dist(gen),
                        coord_dist(gen));
                    obs.is_used = true;
                    obs.obs_id = static_cast<IndexT>(j);
                    track_info.track.push_back(obs);
                }

                tracks.push_back(std::move(track_info));
            }
        }

        // 验证两个Tracks数据是否相同
        void ValidateTracksEquality(const Tracks &original, const Tracks &loaded)
        {
            ASSERT_EQ(original.size(), loaded.size()) << "Track数量不匹配";

            for (size_t i = 0; i < original.size(); ++i)
            {
                const auto &orig_track = original[i];
                const auto &load_track = loaded[i];

                ASSERT_EQ(orig_track.is_used, load_track.is_used)
                    << "Track " << i << " 使用标记不匹配";
                ASSERT_EQ(orig_track.track.size(), load_track.track.size())
                    << "Track " << i << " 观测点数量不匹配";

                for (size_t j = 0; j < orig_track.track.size(); ++j)
                {
                    const auto &orig_obs = orig_track.track[j];
                    const auto &load_obs = load_track.track[j];

                    ASSERT_EQ(orig_obs.view_id, load_obs.view_id)
                        << "Track " << i << " 观测点 " << j << " view_id不匹配";
                    ASSERT_EQ(orig_obs.pts_id, load_obs.pts_id)
                        << "Track " << i << " 观测点 " << j << " pts_id不匹配";

                    // 坐标比较需要考虑到文件IO过程中的坐标变换
                    ASSERT_NEAR(orig_obs.coord[0], load_obs.coord[0], 1e-6)
                        << "Track " << i << " 观测点 " << j << " x坐标不匹配";
                    ASSERT_NEAR(orig_obs.coord[1], load_obs.coord[1], 1e-6)
                        << "Track " << i << " 观测点 " << j << " y坐标不匹配";
                    ASSERT_EQ(orig_obs.is_used, load_obs.is_used)
                        << "Track " << i << " 观测点 " << j << " 使用标记不匹配";
                }
            }
        }

        // 创建一个简单的tracks文件用于测试
        void CreateSimpleTracksFile(const std::string &filename)
        {
            std::ofstream file(filename);
            ASSERT_TRUE(file.is_open()) << "无法创建测试文件: " << filename;

            // 写入头部信息：3个视图，2个点，6个观测，非归一化(0)
            file << "3 2 6 0\n";

            // 写入track数据
            // Track 0 (pts_id = 0): 在视图0,1,2中都有观测
            file << "0 0 10.5 20.3\n"; // view_id=0, pts_id=0, x=10.5, y=20.3
            file << "1 0 15.2 25.8\n"; // view_id=1, pts_id=0, x=15.2, y=25.8
            file << "2 0 12.1 18.9\n"; // view_id=2, pts_id=0, x=12.1, y=18.9

            // Track 1 (pts_id = 1): 在视图0,1,2中都有观测
            file << "0 1 -5.3 30.7\n"; // view_id=0, pts_id=1, x=-5.3, y=30.7
            file << "1 1 -8.1 35.2\n"; // view_id=1, pts_id=1, x=-8.1, y=35.2
            file << "2 1 -6.8 28.4\n"; // view_id=2, pts_id=1, x=-6.8, y=28.4

            file.close();
        }

        std::filesystem::path test_dir_;
        std::filesystem::path test_tracks_file_;
    };

    /**
     * @brief 测试基本的Tracks文件保存和加载功能
     */
    TEST_F(TestTracksIO, BasicSaveAndLoadTest)
    {
        using namespace PoSDK::file;

        // 生成随机tracks数据
        Tracks original_tracks;
        GenerateRandomTracks(original_tracks, 10, 5);

        // 保存tracks数据
        ASSERT_TRUE(SaveTracks(test_tracks_file_.string(), original_tracks))
            << "保存tracks文件失败";

        // 验证文件确实被创建
        ASSERT_TRUE(std::filesystem::exists(test_tracks_file_))
            << "tracks文件未被创建";

        // 加载tracks数据
        Tracks loaded_tracks;
        ASSERT_TRUE(LoadTracks(test_tracks_file_.string(), loaded_tracks))
            << "加载tracks文件失败";

        // 验证数据一致性
        ValidateTracksEquality(original_tracks, loaded_tracks);
    }

    /**
     * @brief 测试空tracks数据的处理
     */
    TEST_F(TestTracksIO, EmptyTracksTest)
    {
        using namespace PoSDK::file;

        // 创建空的tracks
        Tracks empty_tracks;

        // 保存空tracks
        ASSERT_TRUE(SaveTracks(test_tracks_file_.string(), empty_tracks))
            << "保存空tracks文件失败";

        // 加载空tracks
        Tracks loaded_tracks;
        ASSERT_TRUE(LoadTracks(test_tracks_file_.string(), loaded_tracks))
            << "加载空tracks文件失败";

        // 验证加载的tracks也是空的
        EXPECT_TRUE(loaded_tracks.empty()) << "加载的tracks应该是空的";
    }

    /**
     * @brief 测试预定义格式的tracks文件加载
     */
    TEST_F(TestTracksIO, PredefinedFormatTest)
    {
        using namespace PoSDK::file;

        // 创建预定义格式的tracks文件
        CreateSimpleTracksFile(test_tracks_file_.string());

        // 加载tracks数据
        Tracks loaded_tracks;
        ASSERT_TRUE(LoadTracks(test_tracks_file_.string(), loaded_tracks))
            << "加载预定义tracks文件失败";

        // 验证加载的数据
        ASSERT_EQ(loaded_tracks.size(), 2) << "应该有2个tracks";

        // 验证第一个track
        const auto &track0 = loaded_tracks[0];
        ASSERT_EQ(track0.track.size(), 3) << "第一个track应该有3个观测";
        ASSERT_TRUE(track0.is_used) << "第一个track应该被标记为使用";

        // 验证第二个track
        const auto &track1 = loaded_tracks[1];
        ASSERT_EQ(track1.track.size(), 3) << "第二个track应该有3个观测";
        ASSERT_TRUE(track1.is_used) << "第二个track应该被标记为使用";

        // 验证具体的观测数据（考虑坐标变换：文件中的坐标会被取反）
        const auto &obs0 = track0.track[0];
        EXPECT_EQ(obs0.view_id, 0);
        EXPECT_EQ(obs0.pts_id, 0);
        EXPECT_NEAR(obs0.coord[0], -10.5, 1e-6); // 坐标被取反
        EXPECT_NEAR(obs0.coord[1], -20.3, 1e-6); // 坐标被取反
    }

    /**
     * @brief 测试错误处理：无效文件路径
     */
    TEST_F(TestTracksIO, InvalidFilePathTest)
    {
        using namespace PoSDK::file;

        Tracks tracks;

        // 测试加载不存在的文件
        EXPECT_FALSE(LoadTracks("/nonexistent/path/tracks.tracks", tracks))
            << "加载不存在的文件应该失败";

        // 测试保存到无效路径
        GenerateRandomTracks(tracks, 5, 3);
        EXPECT_FALSE(SaveTracks("/invalid/path/tracks.tracks", tracks))
            << "保存到无效路径应该失败";
    }

    /**
     * @brief 测试tracks数量过少的处理（少于2个观测的track）
     */
    TEST_F(TestTracksIO, ShortTracksTest)
    {
        using namespace PoSDK::file;

        // 创建包含短tracks的文件
        std::ofstream file(test_tracks_file_);
        ASSERT_TRUE(file.is_open());

        // 3个视图，3个点，6个观测，非归一化(0)
        file << "3 3 6 0\n";

        // Track 0: 2个观测（应该被标记为使用）
        file << "0 0 10.0 20.0\n";
        file << "1 0 15.0 25.0\n";

        // Track 1: 1个观测（应该被标记为不使用）
        file << "0 1 -5.0 30.0\n";

        // Track 2: 3个观测（应该被标记为使用）
        file << "0 2 12.0 18.0\n";
        file << "1 2 16.0 22.0\n";
        file << "2 2 14.0 20.0\n";

        file.close();

        // 加载tracks
        Tracks loaded_tracks;
        ASSERT_TRUE(LoadTracks(test_tracks_file_.string(), loaded_tracks));

        ASSERT_EQ(loaded_tracks.size(), 3);

        // 第一个track（2个观测）应该被标记为使用
        EXPECT_TRUE(loaded_tracks[0].is_used);
        EXPECT_EQ(loaded_tracks[0].track.size(), 2);

        // 第二个track（1个观测）应该被标记为不使用
        EXPECT_FALSE(loaded_tracks[1].is_used);
        EXPECT_EQ(loaded_tracks[1].track.size(), 1);

        // 第三个track（3个观测）应该被标记为使用
        EXPECT_TRUE(loaded_tracks[2].is_used);
        EXPECT_EQ(loaded_tracks[2].track.size(), 3);
    }

    /**
     * @brief 测试往返一致性（保存后加载的数据与原数据一致）
     */
    TEST_F(TestTracksIO, RoundTripConsistencyTest)
    {
        using namespace PoSDK::file;

        // 生成原始数据
        Tracks original_tracks;
        GenerateRandomTracks(original_tracks, 20, 8);

        // 第一次保存和加载
        ASSERT_TRUE(SaveTracks(test_tracks_file_.string(), original_tracks));

        Tracks first_load;
        ASSERT_TRUE(LoadTracks(test_tracks_file_.string(), first_load));

        // 第二次保存和加载
        auto second_file = test_dir_ / "second_tracks.tracks";
        ASSERT_TRUE(SaveTracks(second_file.string(), first_load));

        Tracks second_load;
        ASSERT_TRUE(LoadTracks(second_file.string(), second_load));

        // 验证两次加载的结果一致
        ValidateTracksEquality(first_load, second_load);
    }

    /**
     * @brief 测试DataTracks类的tracks格式保存和加载功能
     */
    TEST_F(TestTracksIO, DataTracksFileIOTest)
    {
        // 创建工厂并使用工厂创建DataTracks对象
        auto factory_data = std::make_unique<PoSDK::FactoryData>();
        auto data_tracks = factory_data->Create("data_tracks");
        ASSERT_TRUE(data_tracks != nullptr) << "工厂创建DataTracks对象失败";

        // 获取内部tracks对象并填充数据
        auto tracks_ptr = GetDataPtr<Tracks>(data_tracks);
        ASSERT_TRUE(tracks_ptr != nullptr);

        GenerateRandomTracks(*tracks_ptr, 15, 6);

        // 使用DataTracks的Save方法保存为tracks格式
        auto tracks_file = test_dir_ / "dataclass_tracks.tracks";
        ASSERT_TRUE(data_tracks->Save(test_dir_.string(), "dataclass_tracks", ".tracks"))
            << "DataTracks保存tracks格式文件失败";

        // 验证文件确实被创建
        ASSERT_TRUE(std::filesystem::exists(tracks_file))
            << "DataTracks未能创建tracks文件";

        // 创建新的DataTracks对象并加载数据
        auto loaded_data_tracks = factory_data->Create("data_tracks");
        ASSERT_TRUE(loaded_data_tracks != nullptr) << "工厂创建第二个DataTracks对象失败";
        ASSERT_TRUE(loaded_data_tracks->Load(tracks_file.string(), "tracks"))
            << "DataTracks加载tracks格式文件失败";

        // 获取加载的tracks数据并验证
        auto loaded_tracks_ptr = GetDataPtr<Tracks>(loaded_data_tracks);
        ASSERT_TRUE(loaded_tracks_ptr != nullptr);

        // 验证数据一致性
        ValidateTracksEquality(*tracks_ptr, *loaded_tracks_ptr);

        std::cout << "DataTracks tracks格式IO测试通过！" << std::endl;
    }

} // namespace