/**
 * @file test_relative_cost_method.cpp
 * @brief 测试相对位姿残差评估器方法
 * @details 使用仿真数据测试各种残差函数的计算结果
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <opengv/relative_pose/methods.hpp>
#include <opengv/relative_pose/CentralRelativeAdapter.hpp>
#include "random_generators.hpp"
#include "experiment_helpers.hpp"

// 显式包含相关头文件
#include "data/data_relative_poses.hpp"
#include "methods/method_relative_cost.hpp"
#include <fstream>

namespace
{
    using namespace PoSDK;
    using namespace opengv;

    class TestRelativeCostMethod : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            std::string config_name;       // 配置名称
            double max_parallax = 1.0;     // 平移幅度
            double max_rotation = 0.5;     // 旋转幅度(弧度)
            size_t num_points = 100;       // 特征点数量
            double noise = 0.0;            // 噪声水平
            double outlier_fraction = 0.0; // 外点比例
        };

        // 残差函数类型列表
        std::vector<std::string> residual_types_ = {
            "coplanar",
            "sampson",
            "kneip",
            "ba",
            "opengv",
            "ppo",
            // "ppog",
            // "ppo_invd",
            // "ppo_bvc_invd",
            // "ppo_bva_invd",
            // "ligt_direct",
            // "ligt_d3",
            // "ligt",
            // "lirt"
        };

        void SetUp() override
        {
            // 初始化LiRP方法用于生成相对位姿
            lirp_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(lirp_method_ != nullptr);

            // 初始化相对位姿残差评估器
            cost_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_relative_cost"));
            ASSERT_TRUE(cost_method_ != nullptr);
        }

        void TearDown() override
        {
            // 测试完成后输出结果表格
            OutputResultsTable();
        }

        // 生成场景数据
        std::pair<DataPtr, DataPtr> GenerateSceneData(const SceneConfig &config)
        {
            // 初始化随机种子
            initializeRandomSeed();

            // 生成相对位姿
            translation_t position1 = Eigen::Vector3d::Zero();
            rotation_t rotation1 = Eigen::Matrix3d::Identity();
            translation_t position2 = generateRandomTranslation(config.max_parallax);
            rotation_t rotation2 = generateRandomRotation(config.max_rotation);

            // 提取真值相对位姿
            extractRelativePose(
                position1, position2,
                rotation1, rotation2,
                ground_truth_t_, ground_truth_R_,
                true); // normalize translation

            // 使用OpenGV的辅助函数生成中心相机系统
            translations_t camOffsets;
            rotations_t camRotations;
            generateCentralCameraSystem(camOffsets, camRotations);

            // 生成2D-2D对应点
            bearingVectors_t bearingVectors1, bearingVectors2;
            std::vector<int> camCorrespondences1, camCorrespondences2;
            Eigen::MatrixXd gt(3, config.num_points);

            // 使用OpenGV的函数生成对应点
            generateRandom2D2DCorrespondences(
                position1, rotation1,
                position2, rotation2,
                camOffsets, camRotations,
                config.num_points,
                config.noise,
                config.outlier_fraction,
                bearingVectors1, bearingVectors2,
                camCorrespondences1, camCorrespondences2,
                gt);

            // 为LiRP方法准备数据
            BearingPairs bearing_pairs;
            ConvertToBearingPairs(bearingVectors1, bearingVectors2, bearing_pairs);

            // 创建BearingPairs数据样本
            auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);

            // 创建真值相对位姿数据
            RelativePose gt_pose;
            gt_pose.i = 0;
            gt_pose.j = 1;
            gt_pose.Rij = ground_truth_R_;
            gt_pose.tij = ground_truth_t_;
            gt_pose.weight = 1.0;

            auto gt_data = std::make_shared<DataMap<RelativePose>>(gt_pose);

            return std::make_pair(sample_data, gt_data);
        }

        // 使用LiRP方法估计相对位姿
        DataPtr EstimateRelativePose(DataPtr sample_data)
        {
            // 设置LiRP方法的输入数据
            lirp_method_->SetRequiredData(sample_data);

            // 设置LiRP特定的选项
            MethodOptions lirp_options{
                {"compute_mode", "false"},
                {"use_opt_mode", "false"},
                {"identify_mode", "PPO"},
                {"debug_output", "false"},
                {"enable_evaluator", "false"},
                {"enable_profiling", "false"},
                {"view_i", "0"},
                {"view_j", "1"}};
            lirp_method_->SetMethodOptions(lirp_options);

            // 运行LiRP方法
            auto result = lirp_method_->Build();
            EXPECT_TRUE(result != nullptr) << "LiRP method failed to produce a result";

            return result;
        }

        // 计算指定残差类型的残差值
        std::vector<double> ComputeResiduals(const std::string &residual_type,
                                             DataPtr sample_data,
                                             DataPtr pose_data)
        {
            // 设置残差评估器的输入数据 - 使用GetRequiredPackage方式
            auto &required_package = cost_method_->GetRequiredPackage();
            required_package["data_sample"] = sample_data;
            required_package["data_map"] = pose_data;

            // 设置残差类型选项
            MethodOptions cost_options{
                {"residual_type", residual_type},
                {"use_weights", "false"},
                {"enable_evaluator", "false"},
                {"enable_profiling", "false"}};
            cost_method_->SetMethodOptions(cost_options);

            // 运行残差计算
            auto result = cost_method_->Build();
            if (!result)
            {
                std::cerr << "Failed to compute residuals for type: " << residual_type << std::endl;
                return {};
            }

            // 调试信息：输出结果类型
            std::cout << "  Result type: " << result->GetType() << std::endl;

            // 提取残差值 - 直接转换为DataCosts指针
            auto costs = std::dynamic_pointer_cast<DataCosts>(result);
            if (!costs)
            {
                std::cerr << "Failed to cast result to DataCosts for type: " << residual_type
                          << ", actual type: " << result->GetType() << std::endl;
                return {};
            }

            if (costs->empty())
            {
                std::cerr << "Empty residuals for type: " << residual_type << std::endl;
                return {};
            }

            std::vector<double> residuals;
            residuals.reserve(costs->size());
            for (const auto &cost : *costs)
            {
                residuals.push_back(cost);
            }

            std::cout << "  Successfully computed " << residuals.size() << " residuals" << std::endl;
            return residuals;
        }

        // 计算统计信息
        struct Statistics
        {
            double mean = 0.0;
            double median = 0.0;
            double std_dev = 0.0;
            double min_val = 0.0;
            double max_val = 0.0;
            size_t count = 0;
        };

        Statistics ComputeStatistics(const std::vector<double> &values)
        {
            Statistics stats;
            if (values.empty())
                return stats;

            stats.count = values.size();

            // 计算均值
            double sum = std::accumulate(values.begin(), values.end(), 0.0);
            stats.mean = sum / values.size();

            // 计算中值
            auto sorted_values = values;
            std::sort(sorted_values.begin(), sorted_values.end());
            if (sorted_values.size() % 2 == 0)
            {
                stats.median = (sorted_values[sorted_values.size() / 2 - 1] +
                                sorted_values[sorted_values.size() / 2]) /
                               2.0;
            }
            else
            {
                stats.median = sorted_values[sorted_values.size() / 2];
            }

            // 计算标准差
            double sq_sum = 0.0;
            for (const auto &val : values)
            {
                sq_sum += (val - stats.mean) * (val - stats.mean);
            }
            stats.std_dev = std::sqrt(sq_sum / values.size());

            // 最小值和最大值
            stats.min_val = *std::min_element(values.begin(), values.end());
            stats.max_val = *std::max_element(values.begin(), values.end());

            return stats;
        }

        // 计算多次运行的聚合统计信息
        Statistics ComputeAggregatedStatistics(const std::vector<std::vector<double>> &all_runs)
        {
            Statistics stats;
            if (all_runs.empty())
                return stats;

            // 将所有运行的结果合并到一个向量中
            std::vector<double> all_values;
            for (const auto &run_values : all_runs)
            {
                all_values.insert(all_values.end(), run_values.begin(), run_values.end());
            }

            if (all_values.empty())
                return stats;

            // 使用现有的ComputeStatistics方法
            stats = ComputeStatistics(all_values);

            // 记录每次运行的点数（假设每次运行的点数相同）
            if (!all_runs.empty() && !all_runs[0].empty())
            {
                stats.count = all_runs[0].size(); // 每次运行的点数
            }

            return stats;
        }

        // 运行单个测试场景（重复多次）
        void RunScenario(const SceneConfig &config, int num_repeats = 10)
        {
            std::cout << "\n=== Running scenario: " << config.config_name
                      << " (repeating " << num_repeats << " times) ===" << std::endl;

            // 存储每种残差类型的多次运行结果
            std::map<std::string, std::vector<std::vector<double>>> all_residuals;

            // 重复运行多次
            for (int repeat = 0; repeat < num_repeats; ++repeat)
            {
                std::cout << "\n--- Repeat " << (repeat + 1) << "/" << num_repeats << " ---" << std::endl;

                try
                {
                    // 1. 生成场景数据
                    auto [sample_data, gt_data] = GenerateSceneData(config);

                    // 2. 使用LiRP方法估计相对位姿
                    auto estimated_pose_data = EstimateRelativePose(sample_data);
                    ASSERT_TRUE(estimated_pose_data != nullptr);

                    // 3. 对每种残差类型计算残差值
                    for (const auto &residual_type : residual_types_)
                    {
                        std::cout << "Computing " << residual_type << "..." << std::endl;

                        auto residuals = ComputeResiduals(residual_type, sample_data, estimated_pose_data);

                        if (!residuals.empty())
                        {
                            all_residuals[residual_type].push_back(residuals);
                            std::cout << "  ✓ Success: " << residuals.size() << " values" << std::endl;
                        }
                        else
                        {
                            std::cerr << "  ✗ Failed to compute residuals for: " << residual_type
                                      << " in repeat " << (repeat + 1) << std::endl;
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    std::cerr << "Exception in repeat " << (repeat + 1) << ": " << e.what() << std::endl;
                }
            }

            // 4. 计算每种残差类型的综合统计
            for (const auto &residual_type : residual_types_)
            {
                if (all_residuals.find(residual_type) != all_residuals.end() &&
                    !all_residuals[residual_type].empty())
                {
                    auto stats = ComputeAggregatedStatistics(all_residuals[residual_type]);
                    results_[residual_type] = stats;

                    std::cout << "  " << residual_type << " - Runs: " << all_residuals[residual_type].size()
                              << ", Points per run: " << stats.count
                              << ", Mean: " << std::fixed << std::setprecision(6) << stats.mean
                              << ", Median: " << stats.median
                              << ", Std: " << stats.std_dev << std::endl;
                }
                else
                {
                    std::cerr << "  No valid results for: " << residual_type << std::endl;
                }
            }
        }

        // 输出结果表格
        void OutputResultsTable()
        {
            if (results_.empty())
            {
                std::cout << "No results to display." << std::endl;
                return;
            }

            std::cout << "\n"
                      << std::string(120, '=') << std::endl;
            std::cout << "RELATIVE POSE RESIDUALS COMPARISON TABLE (10 RUNS AGGREGATED)" << std::endl;
            std::cout << std::string(120, '=') << std::endl;

            // 表头
            std::cout << std::left
                      << std::setw(20) << "Residual Type"
                      << std::right
                      << std::setw(12) << "Points/Run"
                      << std::setw(15) << "Mean"
                      << std::setw(15) << "Median"
                      << std::setw(15) << "Std Dev"
                      << std::setw(15) << "Min"
                      << std::setw(15) << "Max" << std::endl;
            std::cout << std::string(120, '-') << std::endl;

            // 数据行
            for (const auto &residual_type : residual_types_)
            {
                auto it = results_.find(residual_type);
                if (it != results_.end())
                {
                    const auto &stats = it->second;
                    std::cout << std::left
                              << std::setw(20) << residual_type
                              << std::right
                              << std::setw(12) << stats.count
                              << std::fixed << std::setprecision(8)
                              << std::setw(15) << stats.mean
                              << std::setw(15) << stats.median
                              << std::setw(15) << stats.std_dev
                              << std::setw(15) << stats.min_val
                              << std::setw(15) << stats.max_val << std::endl;
                }
                else
                {
                    std::cout << std::left
                              << std::setw(20) << residual_type
                              << std::right
                              << std::setw(12) << "FAILED"
                              << std::setw(15) << "-"
                              << std::setw(15) << "-"
                              << std::setw(15) << "-"
                              << std::setw(15) << "-"
                              << std::setw(15) << "-" << std::endl;
                }
            }

            std::cout << std::string(120, '=') << std::endl;

            // 导出CSV文件
            ExportToCSV();
        }

        // 导出CSV文件
        void ExportToCSV()
        {
            // 生成带时间戳的文件名
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            std::ostringstream timestamp;
            timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

            std::string csv_filename = "relative_residuals_comparison_" + timestamp.str() + ".csv";
            std::ofstream csv_file(csv_filename);

            if (!csv_file.is_open())
            {
                std::cerr << "Failed to create CSV file: " << csv_filename << std::endl;
                return;
            }

            // CSV表头
            csv_file << "Residual_Type,Points_Per_Run,Mean,Median,Std_Dev,Min,Max\n";

            // CSV数据
            for (const auto &residual_type : residual_types_)
            {
                auto it = results_.find(residual_type);
                if (it != results_.end())
                {
                    const auto &stats = it->second;
                    csv_file << residual_type << ","
                             << stats.count << ","
                             << std::fixed << std::setprecision(10)
                             << stats.mean << ","
                             << stats.median << ","
                             << stats.std_dev << ","
                             << stats.min_val << ","
                             << stats.max_val << "\n";
                }
                else
                {
                    csv_file << residual_type << ",FAILED,-,-,-,-,-\n";
                }
            }

            csv_file.close();
            std::cout << "\nResults exported to: " << csv_filename << std::endl;
        }

        // 添加一个辅助函数用于转换数据格式
        void ConvertToBearingPairs(const bearingVectors_t &bearingVectors1,
                                   const bearingVectors_t &bearingVectors2,
                                   BearingPairs &bearing_pairs)
        {
            bearing_pairs.clear();
            const size_t num_points = bearingVectors1.size();
            bearing_pairs.reserve(num_points);

            for (size_t i = 0; i < num_points; i++)
            {
                Eigen::Matrix<double, 6, 1> match_pair;
                match_pair.head<3>() = bearingVectors1[i];
                match_pair.tail<3>() = bearingVectors2[i];
                bearing_pairs.push_back(match_pair);
            }
        }

        Interface::MethodPresetPtr lirp_method_;
        Interface::MethodPresetPtr cost_method_;
        rotation_t ground_truth_R_;
        translation_t ground_truth_t_;

        // 存储各种残差类型的统计结果
        std::map<std::string, Statistics> results_;
    };

    // 基本测试用例（无噪声、无外点）
    TEST_F(TestRelativeCostMethod, BasicResidualComparison)
    {
        SceneConfig config;
        config.config_name = "basic_residual_test";
        config.max_parallax = 1.0;
        config.max_rotation = 0.3;
        config.num_points = 100;
        config.noise = 0.0;
        config.outlier_fraction = 0.0;

        RunScenario(config, 10); // 运行10次
    }

} // namespace
