#include <gtest/gtest.h>
#include <fstream>
#include <filesystem>
#include <string>
#include <memory>

// 包含配置工具
#include "internal/inifile.hpp"

class IniCommentsTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        test_filename_ = "test_config.ini";
        config_tool_ = std::make_unique<PoSDK::ConfigurationTools>();

        // 创建测试ini文件
        CreateTestIniFile();
    }

    void TearDown() override
    {
        // 清理测试文件
        if (std::filesystem::exists(test_filename_))
        {
            std::filesystem::remove(test_filename_);
        }
        config_tool_.reset();
    }

    void CreateTestIniFile()
    {
        std::ofstream file(test_filename_);
        file << R"(# 这是一个测试配置文件
# 支持#和;两种注释格式

[section1]
# 这是一个注释行，应该被忽略
option1=value1
option2=value2 # 这是行内注释
; 这也是一个注释行，应该被忽略
option3=value3 ; 这也是行内注释

[section2]
; 分号注释行
; 另一个分号注释行
option4=value4
option5=value5; inline semicolon comment
option6=value6#inline hash comment

# 混合注释测试
[section3]
option7=value7
; option8=disabled_value8  # 这一行应该完全被忽略
option9=value9
#option10=disabled_value10 ; 这一行也应该被忽略

# 空行和空白行测试
[section4]

    # 缩进的注释行
option11=value11
    ; 缩进的分号注释
option12=value12   

# 边界情况测试  
[section5]
option13=value13#
option14=value14;
option15=value15 #   
option16=value16 ;   
# option17=value17 但这整行是注释
option18=value18
)";
        file.close();
    }

    std::string test_filename_;
    std::unique_ptr<PoSDK::ConfigurationTools> config_tool_;
};

TEST_F(IniCommentsTest, TestFileCreationAndLoading)
{
    // 测试文件是否正确创建
    ASSERT_TRUE(std::filesystem::exists(test_filename_));

    // 测试配置工具是否能正确加载文件
    int result = config_tool_->Init(test_filename_);
    EXPECT_EQ(result, 0) << "配置文件加载失败";
}

TEST_F(IniCommentsTest, TestHashCommentLines)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    std::string value;

    // 测试#注释行是否被正确忽略
    // option8 和 option10 应该不存在，因为它们在注释行中
    int result1 = config_tool_->ReadItem("section3", "option8", "default", value);
    EXPECT_EQ(result1, -1) << "注释行中的option8应该不存在";
    EXPECT_EQ(value, "default") << "option8应该返回默认值";

    int result2 = config_tool_->ReadItem("section3", "option10", "default", value);
    EXPECT_EQ(result2, -1) << "注释行中的option10应该不存在";
    EXPECT_EQ(value, "default") << "option10应该返回默认值";
}

TEST_F(IniCommentsTest, TestSemicolonCommentLines)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    std::string value;

    // 测试;注释行是否被正确忽略
    // 在section3中，以;开头的option8行应该被忽略
    int result = config_tool_->ReadItem("section3", "option8", "default", value);
    EXPECT_EQ(result, -1) << "分号注释行中的option应该不存在";
    EXPECT_EQ(value, "default") << "应该返回默认值";
}

TEST_F(IniCommentsTest, TestInlineHashComments)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    std::string value;

    // 测试行内#注释是否被正确处理
    int result1 = config_tool_->ReadItem("section1", "option2", "default", value);
    EXPECT_EQ(result1, 0) << "option2应该存在";
    EXPECT_EQ(value, "value2") << "option2的值应该是value2，注释部分应该被移除";

    int result2 = config_tool_->ReadItem("section2", "option6", "default", value);
    EXPECT_EQ(result2, 0) << "option6应该存在";
    EXPECT_EQ(value, "value6") << "option6的值应该是value6，#注释部分应该被移除";
}

TEST_F(IniCommentsTest, TestInlineSemicolonComments)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    std::string value;

    // 测试行内;注释是否被正确处理
    int result1 = config_tool_->ReadItem("section1", "option3", "default", value);
    EXPECT_EQ(result1, 0) << "option3应该存在";
    EXPECT_EQ(value, "value3") << "option3的值应该是value3，;注释部分应该被移除";

    int result2 = config_tool_->ReadItem("section2", "option5", "default", value);
    EXPECT_EQ(result2, 0) << "option5应该存在";
    EXPECT_EQ(value, "value5") << "option5的值应该是value5，;注释部分应该被移除";
}

TEST_F(IniCommentsTest, TestValidOptions)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    std::string value;

    // 测试有效的配置项是否正确读取
    EXPECT_EQ(config_tool_->ReadItem("section1", "option1", "default", value), 0);
    EXPECT_EQ(value, "value1");

    EXPECT_EQ(config_tool_->ReadItem("section2", "option4", "default", value), 0);
    EXPECT_EQ(value, "value4");

    EXPECT_EQ(config_tool_->ReadItem("section3", "option7", "default", value), 0);
    EXPECT_EQ(value, "value7");

    EXPECT_EQ(config_tool_->ReadItem("section3", "option9", "default", value), 0);
    EXPECT_EQ(value, "value9");

    EXPECT_EQ(config_tool_->ReadItem("section4", "option11", "default", value), 0);
    EXPECT_EQ(value, "value11");

    EXPECT_EQ(config_tool_->ReadItem("section4", "option12", "default", value), 0);
    EXPECT_EQ(value, "value12");
}

TEST_F(IniCommentsTest, TestBoundaryCase)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    std::string value;

    // 测试边界情况：注释符号后没有内容或只有空格
    EXPECT_EQ(config_tool_->ReadItem("section5", "option13", "default", value), 0);
    EXPECT_EQ(value, "value13") << "option13应该正确读取，空注释应该被处理";

    EXPECT_EQ(config_tool_->ReadItem("section5", "option14", "default", value), 0);
    EXPECT_EQ(value, "value14") << "option14应该正确读取，空;注释应该被处理";

    EXPECT_EQ(config_tool_->ReadItem("section5", "option15", "default", value), 0);
    EXPECT_EQ(value, "value15") << "option15应该正确读取，#后的空格应该被处理";

    EXPECT_EQ(config_tool_->ReadItem("section5", "option16", "default", value), 0);
    EXPECT_EQ(value, "value16") << "option16应该正确读取，;后的空格应该被处理";

    // option17应该不存在（整行都是注释）
    EXPECT_EQ(config_tool_->ReadItem("section5", "option17", "default", value), -1);
    EXPECT_EQ(value, "default") << "option17应该不存在";

    EXPECT_EQ(config_tool_->ReadItem("section5", "option18", "default", value), 0);
    EXPECT_EQ(value, "value18") << "option18应该正确读取";
}

TEST_F(IniCommentsTest, TestConfigDump)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    std::cout << "\n=== 配置文件内容转储测试 ===" << std::endl;
    std::cout << "以下内容应该只包含有效的配置项，不包含注释行：" << std::endl;

    // 测试Dump功能
    int result = config_tool_->Dump();
    EXPECT_EQ(result, 0) << "配置转储应该成功";

    std::cout << "=== 转储结束 ===" << std::endl;
}

TEST_F(IniCommentsTest, TestWriteAndReload)
{
    // 加载配置文件
    ASSERT_EQ(config_tool_->Init(test_filename_), 0);

    // 写入一个新的配置项
    EXPECT_EQ(config_tool_->WriteItem("new_section", "new_option", "new_value"), 0);

    // 重新加载并验证
    std::string value;
    EXPECT_EQ(config_tool_->ReadItem("new_section", "new_option", "default", value), 0);
    EXPECT_EQ(value, "new_value");

    // 验证原有的配置项仍然存在
    EXPECT_EQ(config_tool_->ReadItem("section1", "option1", "default", value), 0);
    EXPECT_EQ(value, "value1");
}

// 测试工厂函数
TEST_F(IniCommentsTest, TestMultipleCommentFormats)
{
    // 创建一个包含混合注释格式的更复杂的测试文件
    std::string complex_filename = "complex_test.ini";
    std::ofstream complex_file(complex_filename);
    complex_file << R"(# 头部注释
; 另一种头部注释

[mixed_comments]
option1=value1 # hash comment
option2=value2 ; semicolon comment  
option3=value3#no_space_hash
option4=value4;no_space_semicolon
# 完整注释行
; 另一种完整注释行
option5=value5

# 混合section
[test_section]
; 这里有一些配置
param1=test1
param2=test2 # 带注释的参数
; param3=disabled # 被注释掉的参数
param4=test4 ; 分号注释
)";
    complex_file.close();

    // 使用新的配置工具实例
    PoSDK::ConfigurationTools complex_config;
    ASSERT_EQ(complex_config.Init(complex_filename), 0);

    std::string value;

    // 验证各种注释格式
    EXPECT_EQ(complex_config.ReadItem("mixed_comments", "option1", "default", value), 0);
    EXPECT_EQ(value, "value1");

    EXPECT_EQ(complex_config.ReadItem("mixed_comments", "option2", "default", value), 0);
    EXPECT_EQ(value, "value2");

    EXPECT_EQ(complex_config.ReadItem("mixed_comments", "option3", "default", value), 0);
    EXPECT_EQ(value, "value3");

    EXPECT_EQ(complex_config.ReadItem("mixed_comments", "option4", "default", value), 0);
    EXPECT_EQ(value, "value4");

    EXPECT_EQ(complex_config.ReadItem("mixed_comments", "option5", "default", value), 0);
    EXPECT_EQ(value, "value5");

    // 验证被注释的参数不存在
    EXPECT_EQ(complex_config.ReadItem("test_section", "param3", "default", value), -1);
    EXPECT_EQ(value, "default");

    // 验证有效参数
    EXPECT_EQ(complex_config.ReadItem("test_section", "param1", "default", value), 0);
    EXPECT_EQ(value, "test1");

    EXPECT_EQ(complex_config.ReadItem("test_section", "param2", "default", value), 0);
    EXPECT_EQ(value, "test2");

    EXPECT_EQ(complex_config.ReadItem("test_section", "param4", "default", value), 0);
    EXPECT_EQ(value, "test4");

    // 清理
    std::filesystem::remove(complex_filename);
}

// 主函数测试入口
int main(int argc, char **argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}