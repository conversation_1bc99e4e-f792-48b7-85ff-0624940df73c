/**
 * @file test_g2o_io.cpp
 * @brief 测试g2o文件格式的读写功能
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <random>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <Eigen/Geometry>

namespace
{
    using namespace PoSDK;
    using namespace types;
    using namespace Eigen;

    // 用于存储仿真数据的结构
    struct SimulatedData
    {
        // 相机位姿
        std::vector<Matrix3d> rotations;
        std::vector<Vector3d> translations;

        // 3D点和观测
        std::vector<Vector3d> points_3d;
        std::vector<ObsInfo> observations;

        // 相对位姿
        std::vector<RelativePose> relative_poses;
    };

    class TestG2OIO : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 创建临时文件
            g2o_file_ = std::filesystem::temp_directory_path() / "test.g2o";
        }

        void TearDown() override
        {
            // 清理临时文件
            if (std::filesystem::exists(g2o_file_))
            {
                std::filesystem::remove(g2o_file_);
            }
        }

        // 生成随机数据
        SimulatedData GenerateRandomData(size_t num_poses = 5,
                                         size_t num_points = 20,
                                         size_t num_observations = 100)
        {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_real_distribution<> rot_dist(-M_PI, M_PI);
            std::uniform_real_distribution<> trans_dist(-10.0, 10.0);
            std::uniform_real_distribution<> point_dist(-5.0, 5.0);
            std::uniform_real_distribution<> pixel_dist(-1.0, 1.0); // 用于生成观测坐标

            SimulatedData data;

            // 生成相机位姿
            for (size_t i = 0; i < num_poses; ++i)
            {
                Vector3d axis(rot_dist(gen), rot_dist(gen), rot_dist(gen));
                axis.normalize();
                double angle = rot_dist(gen);
                Matrix3d R = AngleAxisd(angle, axis).toRotationMatrix();
                Vector3d t(trans_dist(gen), trans_dist(gen), trans_dist(gen));

                data.rotations.push_back(R);
                data.translations.push_back(t);
            }

            // 生成相对位姿
            for (size_t i = 1; i < num_poses; ++i)
            {
                Matrix3d Rij = data.rotations[i] * data.rotations[i - 1].transpose();
                Vector3d tij = data.translations[i] - Rij * data.translations[i - 1];
                data.relative_poses.emplace_back(i - 1, i, Rij, tij, 1.0f);
            }

            // 生成3D点
            for (size_t i = 0; i < num_points; ++i)
            {
                Vector3d point(point_dist(gen), point_dist(gen), point_dist(gen));
                data.points_3d.push_back(point);
            }

            // 生成不重复的观测
            std::set<std::pair<size_t, size_t>> used_observations;
            data.observations.clear();

            while (data.observations.size() < num_observations &&
                   used_observations.size() < num_poses * num_points)
            {

                size_t cam_id = gen() % num_poses;
                size_t point_id = gen() % num_points;

                // 检查这个观测是否已经存在
                auto obs_pair = std::make_pair(cam_id, point_id);
                if (used_observations.insert(obs_pair).second)
                {
                    // 生成随机的2D观测坐标
                    ObsInfo obs;
                    obs.view_id = cam_id;
                    obs.pts_id = point_id;
                    obs.coord = Vector2d(pixel_dist(gen), pixel_dist(gen));
                    data.observations.push_back(obs);
                }
            }

            std::cout << "Generated data summary:\n"
                      << "Number of poses: " << data.rotations.size() << "\n"
                      << "Number of points: " << data.points_3d.size() << "\n"
                      << "Number of observations: " << data.observations.size() << "\n";

            return data;
        }

        // 创建g2o文件
        void CreateG2OFile(const std::string &filename, const SimulatedData &data)
        {
            std::ofstream file(filename);
            ASSERT_TRUE(file.is_open());

            // 写入相机位姿顶点
            file << "# Camera pose vertices\n";
            for (size_t i = 0; i < data.rotations.size(); ++i)
            {
                Quaterniond q(data.rotations[i]);
                file << "VERTEX_SE3:QUAT " << i << " "
                     << std::setprecision(16)
                     << data.translations[i].x() << " "
                     << data.translations[i].y() << " "
                     << data.translations[i].z() << " "
                     << q.x() << " " << q.y() << " " << q.z() << " " << q.w() << "\n";
            }

            // 写入3D点顶点，直接使用连续ID
            file << "\n# 3D point vertices\n";
            for (size_t i = 0; i < data.points_3d.size(); ++i)
            {
                file << "VERTEX_POINT_XYZ " << i << " " // 直接使用i作为点ID
                     << std::setprecision(16)
                     << data.points_3d[i].x() << " "
                     << data.points_3d[i].y() << " "
                     << data.points_3d[i].z() << "\n";
            }

            // 写入相对位姿边
            file << "\n# Relative pose edges\n";
            for (const auto &pose : data.relative_poses)
            {
                Quaterniond q(pose.Rij);
                file << "EDGE_SE3:QUAT " << pose.i << " " << pose.j << " "
                     << std::setprecision(16)
                     << pose.tij.x() << " " << pose.tij.y() << " " << pose.tij.z() << " "
                     << q.x() << " " << q.y() << " " << q.z() << " " << q.w();

                // 写入信息矩阵（简化版本）
                for (int i = 0; i < 21; ++i)
                {
                    file << " " << 1.0;
                }
                file << "\n";
            }

            // 写入投影观测边
            file << "\n# Projection edges\n";
            for (const auto &obs : data.observations)
            {
                // EDGE_PROJECT_P2MC 格式：cam_id point_id x y [information matrix]
                file << "EDGE_PROJECT_P2MC "
                     << obs.view_id << " "
                     << obs.pts_id << " " // 直接使用原始点ID
                     << std::setprecision(16)
                     << obs.coord.x() << " "
                     << obs.coord.y();

                // 添加2x2的信息矩阵（简单起见使用单位矩阵）
                file << " 1.0 0.0 0.0 1.0\n";
            }

            file.close();
        }

        // 验证位姿数据
        void ValidatePoses(const SimulatedData &sim_data, const GlobalPoses &loaded_poses)
        {
            ASSERT_EQ(sim_data.rotations.size(), loaded_poses.Size());

            for (size_t i = 0; i < sim_data.rotations.size(); ++i)
            {
                const auto &sim_R = sim_data.rotations[i];
                const auto &sim_t = sim_data.translations[i];
                const auto &loaded_R = loaded_poses.GetRotation(i);
                const auto &loaded_t = loaded_poses.GetTranslation(i);

                // 验证旋转矩阵
                EXPECT_TRUE(sim_R.isApprox(loaded_R, 1e-6))
                    << "位姿 " << i << " 的旋转矩阵不匹配\n"
                    << "期望值:\n"
                    << sim_R << "\n"
                    << "实际值:\n"
                    << loaded_R;

                // 验证平移向量
                EXPECT_TRUE(sim_t.isApprox(loaded_t, 1e-6))
                    << "位姿 " << i << " 的平移向量不匹配\n"
                    << "期望值:\n"
                    << sim_t.transpose() << "\n"
                    << "实际值:\n"
                    << loaded_t.transpose();
            }
        }

        // 验证相对位姿数据
        void ValidateRelativePoses(const SimulatedData &sim_data, const RelativePoses &loaded_poses)
        {
            ASSERT_EQ(sim_data.relative_poses.size(), loaded_poses.size());

            for (size_t i = 0; i < sim_data.relative_poses.size(); ++i)
            {
                const auto &sim_pose = sim_data.relative_poses[i];
                const auto &loaded_pose = loaded_poses[i];

                // 验证索引
                EXPECT_EQ(sim_pose.i, loaded_pose.i);
                EXPECT_EQ(sim_pose.j, loaded_pose.j);

                // 验证旋转矩阵
                EXPECT_TRUE(sim_pose.Rij.isApprox(loaded_pose.Rij, 1e-6));

                // 验证平移向量
                EXPECT_TRUE(sim_pose.tij.isApprox(loaded_pose.tij, 1e-6));
            }
        }

        // 验证轨迹数据
        void ValidateTracks(const SimulatedData &sim_data, const Tracks &loaded_tracks)
        {
            std::cout << "\nValidating tracks:\n"
                      << "Simulated observations: " << sim_data.observations.size() << "\n"
                      << "Loaded tracks: " << loaded_tracks.size() << "\n";

            // 构建观测点映射
            std::map<std::pair<int, int>, Vector2d> sim_observations;
            for (const auto &obs : sim_data.observations)
            {
                sim_observations[{obs.view_id, obs.pts_id}] = obs.coord;
            }

            // 验证每个轨迹
            size_t found_observations = 0;
            for (const auto &track : loaded_tracks)
            {
                if (!track.is_used)
                    continue;

                for (const auto &loaded_obs : track.track)
                {
                    auto key = std::make_pair(loaded_obs.view_id, loaded_obs.pts_id);

                    auto it = sim_observations.find(key);
                    if (it == sim_observations.end())
                    {
                        std::cout << "Missing observation: view_id=" << loaded_obs.view_id
                                  << ", pts_id=" << loaded_obs.pts_id << "\n";
                    }
                    ASSERT_TRUE(it != sim_observations.end())
                        << "找不到观测点: view_id=" << loaded_obs.view_id
                        << ", pts_id=" << loaded_obs.pts_id;

                    EXPECT_TRUE(it->second.isApprox(loaded_obs.coord, 1e-6))
                        << "观测坐标不匹配:\n"
                        << "期望值: " << it->second.transpose() << "\n"
                        << "实际值: " << loaded_obs.coord.transpose();

                    sim_observations.erase(it);
                    found_observations++;
                }
            }

            std::cout << "Found observations: " << found_observations << "\n"
                      << "Remaining observations: " << sim_observations.size() << "\n";

            EXPECT_TRUE(sim_observations.empty())
                << "有 " << sim_observations.size() << " 个模拟观测点未在加载的轨迹中找到";
        }

        std::filesystem::path g2o_file_;
    };

    /**
     * @brief 测试基础版本的LoadFromG2O函数
     */
    TEST_F(TestG2OIO, BasicLoadTest)
    {
        SimulatedData sim_data = GenerateRandomData();
        CreateG2OFile(g2o_file_.string(), sim_data);

        GlobalRotations rotations;
        GlobalTranslations translations;
        ASSERT_TRUE(file::LoadFromG2O(g2o_file_.string(), rotations, translations));

        // 验证数据
        ASSERT_EQ(sim_data.rotations.size(), rotations.size());
        ASSERT_EQ(sim_data.translations.size(), translations.size());

        for (size_t i = 0; i < rotations.size(); ++i)
        {
            EXPECT_TRUE(sim_data.rotations[i].isApprox(rotations[i], 1e-6));
            EXPECT_TRUE(sim_data.translations[i].isApprox(translations[i], 1e-6));
        }
    }

    /**
     * @brief 测试GlobalPoses版本的LoadFromG2O函数
     */
    TEST_F(TestG2OIO, GlobalPosesLoadTest)
    {
        SimulatedData sim_data = GenerateRandomData();
        CreateG2OFile(g2o_file_.string(), sim_data);

        GlobalPoses global_poses;
        ASSERT_TRUE(file::LoadFromG2O(g2o_file_.string(), global_poses));

        // 验证数据
        ValidatePoses(sim_data, global_poses);
    }

    /**
     * @brief 测试RelativePoses版本的LoadFromG2O函数
     */
    TEST_F(TestG2OIO, RelativePosesLoadTest)
    {
        SimulatedData sim_data = GenerateRandomData();
        CreateG2OFile(g2o_file_.string(), sim_data);

        RelativePoses relative_poses;
        ASSERT_TRUE(file::LoadFromG2O(g2o_file_.string(), relative_poses));

        // 验证数据
        ValidateRelativePoses(sim_data, relative_poses);
    }

    /**
     * @brief 测试Tracks版本的LoadFromG2O函数
     */
    TEST_F(TestG2OIO, TracksLoadTest)
    {
        SimulatedData sim_data = GenerateRandomData();
        CreateG2OFile(g2o_file_.string(), sim_data);

        GlobalPoses global_poses;
        Tracks tracks;
        ASSERT_TRUE(file::LoadFromG2O(g2o_file_.string(), global_poses, tracks));

        // 验证数据
        ValidatePoses(sim_data, global_poses);
        ValidateTracks(sim_data, tracks);
    }

    /**
     * @brief 测试G2OData类的读写功能
     */
    TEST_F(TestG2OIO, G2ODataTest)
    {
        // 生成测试数据
        SimulatedData sim_data = GenerateRandomData();
        CreateG2OFile(g2o_file_.string(), sim_data);

        // 创建G2OData对象并设置指针
        file::G2OData data;
        data.rotations = std::make_shared<GlobalRotations>();
        data.translations = std::make_shared<GlobalTranslations>();
        data.relative_poses = std::make_shared<RelativePoses>();
        data.tracks = std::make_shared<Tracks>();

        // 测试加载功能
        ASSERT_TRUE(data.LoadFromG2O(g2o_file_.string()));

        // 验证全局位姿数据
        ASSERT_EQ(sim_data.rotations.size(), data.rotations->size());
        ASSERT_EQ(sim_data.translations.size(), data.translations->size());
        for (size_t i = 0; i < data.rotations->size(); ++i)
        {
            EXPECT_TRUE(sim_data.rotations[i].isApprox((*data.rotations)[i], 1e-6))
                << "位姿 " << i << " 的旋转矩阵不匹配";
            EXPECT_TRUE(sim_data.translations[i].isApprox((*data.translations)[i], 1e-6))
                << "位姿 " << i << " 的平移向量不匹配";
        }

        // 验证相对位姿数据
        ValidateRelativePoses(sim_data, *data.relative_poses);

        // 验证轨迹数据
        ValidateTracks(sim_data, *data.tracks);

        // 测试部分数据的读写
        {
            // 只读取位姿数据
            file::G2OData poses_only;
            poses_only.rotations = std::make_shared<GlobalRotations>();
            poses_only.translations = std::make_shared<GlobalTranslations>();
            ASSERT_TRUE(poses_only.LoadFromG2O(g2o_file_.string()));

            // 验证位姿数据
            ASSERT_EQ(sim_data.rotations.size(), poses_only.rotations->size());
            for (size_t i = 0; i < poses_only.rotations->size(); ++i)
            {
                EXPECT_TRUE(sim_data.rotations[i].isApprox((*poses_only.rotations)[i], 1e-6));
                EXPECT_TRUE(sim_data.translations[i].isApprox((*poses_only.translations)[i], 1e-6));
            }
        }

        {
            // 只读取轨迹数据
            file::G2OData tracks_only;
            tracks_only.tracks = std::make_shared<Tracks>();
            ASSERT_TRUE(tracks_only.LoadFromG2O(g2o_file_.string()));

            // 验证轨迹数据
            ValidateTracks(sim_data, *tracks_only.tracks);
        }

        // 测试保存功能
        std::filesystem::path output_file = std::filesystem::temp_directory_path() / "output.g2o";
        ASSERT_TRUE(data.SaveToG2O(output_file.string()));

        // 读取保存的文件并验证
        file::G2OData loaded_data;
        loaded_data.rotations = std::make_shared<GlobalRotations>();
        loaded_data.translations = std::make_shared<GlobalTranslations>();
        loaded_data.relative_poses = std::make_shared<RelativePoses>();
        loaded_data.tracks = std::make_shared<Tracks>();

        ASSERT_TRUE(loaded_data.LoadFromG2O(output_file.string()));

        // 验证保存和重新加载的数据
        ASSERT_EQ(data.rotations->size(), loaded_data.rotations->size());
        for (size_t i = 0; i < data.rotations->size(); ++i)
        {
            EXPECT_TRUE((*data.rotations)[i].isApprox((*loaded_data.rotations)[i], 1e-6));
            EXPECT_TRUE((*data.translations)[i].isApprox((*loaded_data.translations)[i], 1e-6));
        }

        ASSERT_EQ(data.relative_poses->size(), loaded_data.relative_poses->size());
        for (size_t i = 0; i < data.relative_poses->size(); ++i)
        {
            const auto &orig_pose = (*data.relative_poses)[i];
            const auto &loaded_pose = (*loaded_data.relative_poses)[i];
            EXPECT_EQ(orig_pose.i, loaded_pose.i);
            EXPECT_EQ(orig_pose.j, loaded_pose.j);
            EXPECT_TRUE(orig_pose.Rij.isApprox(loaded_pose.Rij, 1e-6));
            EXPECT_TRUE(orig_pose.tij.isApprox(loaded_pose.tij, 1e-6));
        }

        ValidateTracks(sim_data, *loaded_data.tracks);

        // 清理临时文件
        if (std::filesystem::exists(output_file))
        {
            std::filesystem::remove(output_file);
        }
    }

} // namespace
