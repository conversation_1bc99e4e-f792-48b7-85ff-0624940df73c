/**
 * @file test_method_PA.cpp
 * @brief 测试PA方法的功能
 * @details 测试包括数据加载、位姿优化等功能
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <fstream>
#include <Eigen/Core>

namespace
{
    using namespace PoSDK;
    using namespace Interface;

    class TestMethodPA : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            initial_memory_ = GetCurrentMemoryUsage();

            // 设置测试数据路径
            test_data_root_ = std::string(PROJECT_SOURCE_DIR) + "/tests/LiGT";
            tracks_file_ = test_data_root_ + "/Matches_5Image_100Points_10.00Dt_0.txt";
            rotation_file_ = test_data_root_ + "/global_R_1.txt";

            // 确保测试数据存在
            ASSERT_TRUE(std::filesystem::exists(tracks_file_))
                << "Tracks file not found: " << tracks_file_;
            ASSERT_TRUE(std::filesystem::exists(rotation_file_))
                << "Rotation file not found: " << rotation_file_;

            // 创建输出目录
            std::filesystem::create_directories("storage/PA");
        }
        DataPtr LoadGlobalPoses(GlobalPosesPtr &global_poses_ptr)
        {
            // 创建全局位姿数据对象
            auto data_global_poses = FactoryData::Create("data_global_poses");
            if (!data_global_poses)
            {
                ADD_FAILURE() << "Failed to create data_global_poses";
                return nullptr;
            }

            // 加载旋转数据
            bool load_success = data_global_poses->Load(rotation_file_, "rot");
            if (!load_success)
            {
                ADD_FAILURE() << "Failed to load rotation file";
                return nullptr;
            }

            // 获取全局位姿数据指针
            global_poses_ptr = GetDataPtr<GlobalPoses>(data_global_poses);
            if (!global_poses_ptr)
            {
                ADD_FAILURE() << "Invalid global poses data";
                return nullptr;
            }

            return data_global_poses;
        }
        DataPtr LoadTracksData(TracksPtr &tracks_ptr)
        {
            // 创建数据对象
            auto data_tracks = FactoryData::Create("data_tracks");
            if (!data_tracks)
            {
                ADD_FAILURE() << "Failed to create data_tracks";
                return nullptr;
            }

            // 加载tracks数据
            bool load_success = data_tracks->Load(tracks_file_, "tracks");
            if (!load_success)
            {
                ADD_FAILURE() << "Failed to load tracks file";
                return nullptr;
            }

            // 获取tracks数据指针
            tracks_ptr = GetDataPtr<Tracks>(data_tracks);
            if (!tracks_ptr || tracks_ptr->empty())
            {
                ADD_FAILURE() << "Invalid tracks data";
                return nullptr;
            }

            return data_tracks;
        }

        DataPtr CreateCameraModel()
        {
            // 创建相机模型数据对象
            auto data_camera_models = FactoryData::Create("data_camera_models");
            if (!data_camera_models)
            {
                ADD_FAILURE() << "Failed to create data_camera_models";
                return nullptr;
            }

            // 获取相机模型数据指针
            auto camera_models_ptr = GetDataPtr<CameraModels>(data_camera_models);
            if (!camera_models_ptr)
            {
                ADD_FAILURE() << "Invalid camera model data";
                return nullptr;
            }

            // 创建无畸变单位内参的相机模型
            CameraModel camera_model;
            camera_model.intrinsics.fx = 1.0;
            camera_model.intrinsics.fy = 1.0;
            camera_model.intrinsics.cx = 0.0;
            camera_model.intrinsics.cy = 0.0;
            camera_model.intrinsics.radial_distortion = {0.0, 0.0}; // 无畸变

            camera_models_ptr->push_back(camera_model);

            return data_camera_models;
        }

        void ValidateResults(const GlobalPosesPtr &global_poses_ptr)
        {
            ASSERT_TRUE(global_poses_ptr != nullptr);

            // 验证旋转矩阵
            const auto &rotations = global_poses_ptr->rotations;
            ASSERT_FALSE(rotations.empty());

            // 验证平移向量
            const auto &translations = global_poses_ptr->translations;
            ASSERT_FALSE(translations.empty());
            ASSERT_EQ(rotations.size(), translations.size());

            // 验证每个位姿的有效性
            for (size_t i = 0; i < rotations.size(); ++i)
            {
                // 验证旋转矩阵的正交性
                const Matrix3d &R = rotations[i];
                Matrix3d RRt = R * R.transpose();
                Matrix3d I = Matrix3d::Identity();
                EXPECT_TRUE((RRt - I).norm() < 1e-6)
                    << "Rotation matrix " << i << " is not orthogonal";

                // 验证平移向量的范数
                const Vector3d &t = translations[i];
                EXPECT_FALSE(t.hasNaN())
                    << "Translation " << i << " contains NaN values";
            }
        }

        void TearDown() override
        {
            size_t final_memory = GetCurrentMemoryUsage();
            EXPECT_LE(final_memory - initial_memory_, MAX_ALLOWED_MEMORY_LEAK)
                << "Possible memory leak detected";
        }

    protected:
        std::string test_data_root_;
        std::string tracks_file_;
        std::string rotation_file_;
        size_t initial_memory_;
        static constexpr size_t MAX_ALLOWED_MEMORY_LEAK = 1024; // 1KB

    private:
        size_t GetCurrentMemoryUsage()
        {
            // TODO: 实现具体的内存检测逻辑
            return 0;
        }
    };

    // 基础功能测试
    TEST_F(TestMethodPA, BasicFunctionality)
    {
        // 准备输入数据
        TracksPtr tracks_ptr;
        GlobalPosesPtr global_poses_ptr;

        // 加载tracks数据
        auto data_tracks_ptr = LoadTracksData(tracks_ptr);
        ASSERT_TRUE(data_tracks_ptr != nullptr);

        // 创建相机模型数据
        auto data_camera_model_ptr = CreateCameraModel();
        ASSERT_TRUE(data_camera_model_ptr != nullptr);

        // 首先使用LiGT方法获取初始位姿
        auto ligt_method_ptr = FactoryMethod::Create("method_LiGT");
        ASSERT_TRUE(ligt_method_ptr != nullptr);

        // 设置LiGT方法参数
        auto ligt_preset_method = std::dynamic_pointer_cast<Interface::MethodPreset>(ligt_method_ptr);
        ASSERT_TRUE(ligt_preset_method != nullptr);

        MethodOptions ligt_options{
            {"bal_file_path", "storage/LiGT/output.bal"},
            {"ProfileCommit", "LiGT初始化"}};
        ligt_preset_method->SetMethodOptions(ligt_options);

        // 创建LiGT数据包
        auto ligt_package_ptr = std::make_shared<DataPackage>();
        ligt_package_ptr->AddData(data_tracks_ptr);

        // 加载全局位姿数据（LiGT需要初始旋转）
        auto data_global_poses_ptr = LoadGlobalPoses(global_poses_ptr);
        ASSERT_TRUE(data_global_poses_ptr != nullptr);
        ligt_package_ptr->AddData(data_global_poses_ptr);

        // 执行LiGT算法获取初始位姿
        auto initial_poses_ptr = ligt_method_ptr->Build(ligt_package_ptr);
        ASSERT_TRUE(initial_poses_ptr != nullptr);
        auto initial_poses_data = GetDataPtr<GlobalPoses>(initial_poses_ptr);
        ASSERT_TRUE(initial_poses_data != nullptr);
        ValidateResults(initial_poses_data); // 验证LiGT结果

        // // 转换平移向量从世界坐标系到相机坐标系
        // for (ViewId view_id = 0; view_id < initial_poses_data->Size(); ++view_id) {
        //     // 获取当前视图的旋转和平移
        //     const Matrix3d& R = initial_poses_data->GetRotation(view_id);
        //     const Vector3d& t_world = initial_poses_data->GetTranslation(view_id);

        //     // 转换平移向量: t_camera = -R * t_world
        //     Vector3d t_camera = -R * t_world;

        //     // 更新平移向量
        //     initial_poses_data->SetTranslation(view_id, t_camera);
        // }

        // 创建PA方法
        auto pa_method_ptr = FactoryMethod::Create("method_PA");
        ASSERT_TRUE(pa_method_ptr != nullptr);

        // 设置PA方法参数
        auto pa_preset_method = std::dynamic_pointer_cast<Interface::MethodPreset>(pa_method_ptr);
        ASSERT_TRUE(pa_preset_method != nullptr);

        MethodOptions pa_options{
            {"verbose", "1"},
            {"max_num_iterations", "100"},
            {"function_tolerance", "1e-6"},
            {"gradient_tolerance", "1e-10"},
            {"parameter_tolerance", "1e-8"},
            {"num_threads", "8"},
            {"loss_function", "huber"},
            {"huber_parameter", "1.0"},
            {"log_level", "1"},
            {"ProfileCommit", "PA基础功能测试"}};
        pa_preset_method->SetMethodOptions(pa_options);

        // 创建PA数据包
        auto pa_package_ptr = std::make_shared<DataPackage>();
        pa_package_ptr->AddData(data_tracks_ptr);
        pa_package_ptr->AddData(data_camera_model_ptr);
        pa_package_ptr->AddData(initial_poses_ptr);

        // 执行PA算法
        auto result = pa_method_ptr->Build(pa_package_ptr);
        ASSERT_TRUE(result != nullptr);

        // 验证结果
        auto final_poses_ptr = GetDataPtr<GlobalPoses>(result);
        ASSERT_TRUE(final_poses_ptr != nullptr);
        ValidateResults(final_poses_ptr);
    }

} // namespace
