/**
 * @file test_global_removal.cpp
 * @brief 测试全局异常点检测与移除功能
 * @copyright Copyright (c) 2024 PoSDK Project
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include "internal/methods/visual_simulator.hpp"
#include "internal/methods/method_global_outlier_removal.hpp"
#include <memory>
#include <iostream>
#include <filesystem>
#include <random>
#include <iomanip>
#include <fstream>
#include <set>
#include <sstream>
#include <string>
#include <vector>
#include <stdexcept>                  // For exception handling
#include <boost/algorithm/string.hpp> // 用于不区分大小写的字符串比较

namespace
{
    using namespace PoSDK;
    using namespace Interface;
    using namespace types;

    // 定义评估结果结构体
    struct EvaluationResult
    {
        double recall = 0.0;
        double precision = 0.0;
        int actual_outliers_count = 0;
        int detected_outliers_count = 0;
        int correctly_detected_count = 0;
    };

    class GlobalOutlierRemovalTest : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 创建视觉模拟器
            simulator_ = std::dynamic_pointer_cast<VisualSimulator>(
                FactoryMethod::Create("visual_simulator"));
            ASSERT_TRUE(simulator_ != nullptr) << "无法创建VisualSimulator实例";

            // 创建全局异常点移除方法
            outlier_removal_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_global_outlier_removal"));
            ASSERT_TRUE(outlier_removal_ != nullptr) << "无法创建MethodGlobalOutlierRemoval实例";

            // 创建LiGT方法
            ligt_method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiGT"));
            ASSERT_TRUE(ligt_method_ != nullptr) << "无法创建method_LiGT实例";

            // 重置所有方法选项到INI文件初始状态，确保测试环境干净
            simulator_->ResetOptionsFromIniFile();
            outlier_removal_->ResetOptionsFromIniFile();
            ligt_method_->ResetOptionsFromIniFile();

            // 设置基本参数
            simulator_->SetMethodOption("simu_folder", "test_global_removal_data");

            // 确保存在目录
            std::filesystem::create_directories("test_global_removal_data");
        }

        void TearDown() override
        {
            // 清理临时文件
            // 注释以下代码以保留测试数据
            // std::filesystem::remove_all("test_global_removal_data");
        }

        // 向轨迹中添加一定比例的异常点，并返回实际添加的异常点的obs_id集合
        std::set<IndexT> AddOutliersToTracks(Tracks &tracks, double outlier_ratio)
        {
            std::set<IndexT> actual_outlier_ids;
            std::mt19937 rng(42); // 固定随机种子以保证结果可复现
            std::uniform_real_distribution<double> dist(0, 1);
            std::uniform_real_distribution<double> noise_dist(-50, 50);

            int total_obs = 0;
            int outliers_added = 0;

            for (auto &track_info : tracks)
            {
                for (auto &obs : track_info.track)
                {
                    total_obs++;

                    // 以outlier_ratio的概率将该观测点变为异常点
                    if (dist(rng) < outlier_ratio)
                    {
                        // 添加大量噪声，使其成为异常点
                        obs.coord.x() += noise_dist(rng);
                        obs.coord.y() += noise_dist(rng);
                        actual_outlier_ids.insert(obs.obs_id); // 记录异常点的obs_id
                        outliers_added++;
                    }
                }
            }

            std::cout << "添加了 " << outliers_added << " 个异常点，异常点比例: "
                      << (total_obs > 0 ? (static_cast<double>(outliers_added) / total_obs * 100) : 0) << "%" << std::endl;

            return actual_outlier_ids;
        }

        // 分析轨迹数据
        void AnalyzeTracks(const Tracks &tracks, const std::string &description = "")
        {
            if (!description.empty())
            {
                std::cout << "\n"
                          << description << ":" << std::endl;
            }

            if (tracks.empty())
            {
                std::cout << "  无轨迹数据！" << std::endl;
                return;
            }

            // 计算统计数据
            int total_tracks = 0;
            int total_observations = 0;
            int valid_tracks = 0;
            int valid_observations = 0;

            for (const auto &track_info : tracks)
            {
                total_tracks++;

                if (track_info.is_used)
                {
                    valid_tracks++;

                    for (const auto &obs : track_info.track)
                    {
                        total_observations++;

                        if (obs.is_used)
                        {
                            valid_observations++;
                        }
                    }
                }
            }

            double valid_track_ratio = total_tracks > 0 ? static_cast<double>(valid_tracks) / total_tracks * 100 : 0;
            double valid_obs_ratio = total_observations > 0 ? static_cast<double>(valid_observations) / total_observations * 100 : 0;

            // 输出统计结果
            std::cout << "  轨迹统计:" << std::endl;
            std::cout << "  - 轨迹总数: " << total_tracks << std::endl;
            std::cout << "  - 有效轨迹数: " << valid_tracks
                      << " (" << valid_track_ratio << "%)" << std::endl;
            std::cout << "  - 观测总数: " << total_observations << std::endl;
            std::cout << "  - 有效观测数: " << valid_observations
                      << " (" << valid_obs_ratio << "%)" << std::endl;
        }

        // 新增：提示 Python 脚本绘图的辅助函数
        void PlotResiduals(const std::string &csv_path,
                           const std::string &actual_outliers_path,
                           const std::string &plot_filename)
        {
            // 获取Python脚本的路径 (相对于项目源目录)
            std::string script_dir = std::string(PROJECT_SOURCE_DIR) + "/tests/python";
            std::string script_path = script_dir + "/plot_residuals.py";

            // 检查CSV文件是否存在
            if (!std::filesystem::exists(csv_path))
            {
                std::cerr << "\n警告：未找到残差CSV文件 " << csv_path << "，无法生成绘图提示。" << std::endl;
                return;
            }
            // 检查实际异常点文件是否存在
            if (!std::filesystem::exists(actual_outliers_path))
            {
                std::cerr << "\n警告：未找到实际异常点文件 " << actual_outliers_path << "，绘图将缺少实际异常点标记。" << std::endl;
            }

            // 构建Python命令字符串，添加 --actual-outliers 参数
            std::string command_hint = "python \"" + script_path + "\" \"" + csv_path + "\"" +
                                       " --actual-outliers \"" + actual_outliers_path + "\"" +
                                       " -o \"" + plot_filename + "\"";

            // 输出提示信息给用户
            std::cout << "\n--- 绘图提示 ---" << std::endl;
            std::cout << "残差数据已保存到: " << std::filesystem::absolute(csv_path) << std::endl;
            std::cout << "实际异常点ID已保存到: " << std::filesystem::absolute(actual_outliers_path) << std::endl;
            std::cout << "请在安装了 Python、pandas 和 matplotlib 的环境（例如您的 Mac）中手动运行以下命令生成绘图:" << std::endl;
            std::cout << command_hint << std::endl;
            std::cout << "-----------------" << std::endl;
        }

        /**
         * @brief 根据输出文件评估异常点检测结果
         * @param actual_outliers_path 包含实际异常点 ObsID 的文件路径
         * @param residuals_csv_path 包含检测结果的 CSV 文件路径 (ObsID, Residual, IsOutlierDetected)
         * @return EvaluationResult 包含召回率、精确率等指标
         */
        EvaluationResult EvaluateOutlierDetection(
            const std::string &actual_outliers_path,
            const std::string &residuals_csv_path)
        {
            EvaluationResult result;
            std::set<IndexT> actual_outlier_ids;

            // 1. 读取实际异常点ID
            std::ifstream actual_file(actual_outliers_path);
            if (!actual_file.is_open())
            {
                std::cerr << "错误：无法打开实际异常点文件 " << actual_outliers_path << std::endl;
                return result; // 返回空结果
            }
            IndexT id;
            while (actual_file >> id)
            {
                actual_outlier_ids.insert(id);
            }
            actual_file.close();
            result.actual_outliers_count = actual_outlier_ids.size();

            // 2. 读取残差CSV文件并进行评估
            std::ifstream residuals_file(residuals_csv_path);
            if (!residuals_file.is_open())
            {
                std::cerr << "错误：无法打开残差CSV文件 " << residuals_csv_path << std::endl;
                return result; // 返回空结果
            }

            std::string line;
            // 跳过表头
            if (!std::getline(residuals_file, line))
            {
                std::cerr << "错误：残差CSV文件为空或无法读取表头 " << residuals_csv_path << std::endl;
                return result;
            }

            while (std::getline(residuals_file, line))
            {
                std::stringstream ss(line);
                std::string segment;
                std::vector<std::string> seglist;

                while (std::getline(ss, segment, ','))
                {
                    seglist.push_back(segment);
                }

                if (seglist.size() == 3)
                { // 确保行格式正确 ObsID,Residual,IsOutlierDetected
                    try
                    {
                        IndexT obs_id = std::stoull(seglist[0]); // 使用 stoull 以匹配 IndexT (通常是 unsigned long long)
                        int is_outlier_detected = std::stoi(seglist[2]);

                        if (is_outlier_detected == 1)
                        {
                            result.detected_outliers_count++;
                            if (actual_outlier_ids.count(obs_id))
                            {
                                result.correctly_detected_count++;
                            }
                        }
                    }
                    catch (const std::invalid_argument &e)
                    {
                        std::cerr << "警告：解析CSV行时出错 (无效参数): " << line << " - " << e.what() << std::endl;
                    }
                    catch (const std::out_of_range &e)
                    {
                        std::cerr << "警告：解析CSV行时出错 (超出范围): " << line << " - " << e.what() << std::endl;
                    }
                }
                else
                {
                    std::cerr << "警告：跳过格式不正确的CSV行: " << line << std::endl;
                }
            }
            residuals_file.close();

            // 3. 计算召回率和精确率
            if (result.actual_outliers_count > 0)
            {
                result.recall = static_cast<double>(result.correctly_detected_count) / result.actual_outliers_count * 100.0;
            }
            else
            {
                result.recall = (result.detected_outliers_count == 0) ? 100.0 : 0.0;
            }

            if (result.detected_outliers_count > 0)
            {
                result.precision = static_cast<double>(result.correctly_detected_count) / result.detected_outliers_count * 100.0;
            }
            else
            {
                result.precision = (result.actual_outliers_count == 0) ? 100.0 : 0.0;
            }

            // // 添加一些调试输出
            // std::cout << "评估调试 (" << residuals_csv_path << "): "
            //           << "Actual=" << result.actual_outliers_count
            //           << ", Detected=" << result.detected_outliers_count
            //           << ", CorrectlyDetected=" << result.correctly_detected_count
            //           << ", Recall=" << result.recall << "%, Precision=" << result.precision << "%" << std::endl;

            return result;
        }

        std::shared_ptr<VisualSimulator> simulator_;
        MethodPresetPtr outlier_removal_;
        MethodPresetPtr ligt_method_;
    };

    // 基本功能测试：使用视觉模拟器生成数据，然后检测和移除异常点
    TEST_F(GlobalOutlierRemovalTest, BasicOutlierRemoval)
    {
        // 0. 重置所有方法选项到INI文件初始状态，避免测试间参数干扰
        simulator_->ResetOptionsFromIniFile();
        outlier_removal_->ResetOptionsFromIniFile();
        ligt_method_->ResetOptionsFromIniFile();

        // 1. 生成模拟数据
        simulator_->SetMethodOption("num_views", "10");
        simulator_->SetMethodOption("num_points", "500");  // 增加点数以获得更可靠的测试
        simulator_->SetMethodOption("noise_level", "0.0"); // 0噪声
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "10.0");

        auto result = simulator_->Run();
        ASSERT_TRUE(result != nullptr) << "VisualSimulator运行失败";

        // 2. 获取生成的轨迹和位姿数据
        auto package = std::dynamic_pointer_cast<DataPackage>(result);
        ASSERT_TRUE(package != nullptr);
        auto data_tracks_orig = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks_orig != nullptr) << "无法获取原始轨迹数据";
        auto simulator_poses_data = simulator_->GetGlobalPoses(); // RwTw 格式
        ASSERT_TRUE(simulator_poses_data != nullptr) << "无法获取模拟器位姿数据";
        auto camera_model_data = simulator_->GetCameraModel();
        ASSERT_TRUE(camera_model_data != nullptr) << "无法获取相机模型数据";

        // 3. 创建带异常点的轨迹副本
        auto data_tracks_with_outliers = std::dynamic_pointer_cast<DataTracks>(data_tracks_orig->CopyData());
        ASSERT_TRUE(data_tracks_with_outliers != nullptr);
        auto tracks_with_outliers_ptr = GetDataPtr<Tracks>(data_tracks_with_outliers);
        ASSERT_TRUE(tracks_with_outliers_ptr != nullptr);
        double outlier_ratio = 0.00; // 添加0%的异常点
        std::set<IndexT> actual_outlier_ids = AddOutliersToTracks(*tracks_with_outliers_ptr, outlier_ratio);
        std::string actual_outliers_path = "actual_outliers_basic.txt";
        std::ofstream actual_out_file(actual_outliers_path);
        if (actual_out_file.is_open())
        {
            for (const auto &id : actual_outlier_ids)
            {
                actual_out_file << id << "\n";
            }
            actual_out_file.close();
        }
        else
        { /* ... error handling ... */
        }
        AnalyzeTracks(*tracks_with_outliers_ptr, "添加异常点后的轨迹 (用于测试)");

        // 4. 运行LiGT获取估计位姿
        auto ligt_input = std::make_shared<DataPackage>();
        ligt_input->AddData(data_tracks_with_outliers);              // 使用带异常点的轨迹
        auto poses_copy_for_ligt = simulator_poses_data->CopyData(); // 拷贝 RwTw 真值
        ASSERT_TRUE(poses_copy_for_ligt != nullptr);
        auto *poses_ptr_for_ligt = static_cast<GlobalPoses *>(poses_copy_for_ligt->GetData());
        ASSERT_TRUE(poses_ptr_for_ligt != nullptr);
        ASSERT_TRUE(poses_ptr_for_ligt->ConvertPoseFormat(PoseFormat::RwTc)) // 转为 RwTc
            << "Failed to convert GT poses to RwTc for LiGT";
        ligt_input->AddData(poses_copy_for_ligt);
        ligt_input->AddData(camera_model_data);

        ligt_method_->SetMethodOption("compute_t", "true");
        ligt_method_->SetMethodOption("estimate_R", "false");
        ligt_method_->SetRequiredData(ligt_input);
        auto ligt_result_poses = std::dynamic_pointer_cast<DataGlobalPoses>(ligt_method_->Build()); // LiGT 输出 RwTc
        ASSERT_TRUE(ligt_result_poses != nullptr) << "LiGT运行失败，无法获取估计位姿";
        std::cout << "\nLiGT 运行完成，得到估计位姿 (RwTc 格式)" << std::endl;

        // --- 测试场景1：使用LiGT估计的位姿进行异常移除 ---
        std::cout << "\n--- 测试场景 1: 使用 LiGT 估计位姿进行异常移除 ---" << std::endl;
        std::string residuals_csv_estimated = "residuals_basic_estimated.csv";
        std::string plot_output_estimated = "plot_basic_estimated.png";

        outlier_removal_->SetMethodOption("output_residuals_path", residuals_csv_estimated);

        auto removal_input_estimated = std::make_shared<DataPackage>();
        removal_input_estimated->AddData(data_tracks_with_outliers);
        removal_input_estimated->AddData(ligt_result_poses); // 使用 LiGT 的输出 (RwTc)
        outlier_removal_->SetRequiredData(removal_input_estimated);

        auto cleaned_result_estimated = outlier_removal_->Build();
        ASSERT_TRUE(cleaned_result_estimated != nullptr) << "使用估计位姿进行异常点移除失败";
        auto cleaned_tracks_estimated_ptr = GetDataPtr<Tracks>(cleaned_result_estimated);
        ASSERT_TRUE(cleaned_tracks_estimated_ptr != nullptr);
        AnalyzeTracks(*cleaned_tracks_estimated_ptr, "清理后的轨迹 (使用估计位姿)");
        PlotResiduals(residuals_csv_estimated, actual_outliers_path, plot_output_estimated);

        // --- 测试场景2：使用真值位姿进行异常移除 ---
        std::cout << "\n--- 测试场景 2: 使用 GT 真值位姿进行异常移除 ---" << std::endl;
        std::string residuals_csv_gt = "residuals_basic_gt.csv";
        std::string plot_output_gt = "plot_basic_gt.png";

        // 准备输入：带异常点的轨迹 和 转换格式后的真值位姿
        auto poses_copy_for_removal_gt = simulator_poses_data->CopyData(); // 拷贝 RwTw 真值
        ASSERT_TRUE(poses_copy_for_removal_gt != nullptr);
        auto *poses_ptr_for_removal_gt = static_cast<GlobalPoses *>(poses_copy_for_removal_gt->GetData());
        ASSERT_TRUE(poses_ptr_for_removal_gt != nullptr);
        ASSERT_TRUE(poses_ptr_for_removal_gt->ConvertPoseFormat(PoseFormat::RwTc)) // 转为 RwTc
            << "Failed to convert GT poses to RwTc for outlier removal (GT case)";

        outlier_removal_->SetMethodOption("output_residuals_path", residuals_csv_gt); // 更新输出文件名
        // 其他选项保持不变

        auto removal_input_gt = std::make_shared<DataPackage>();
        removal_input_gt->AddData(data_tracks_with_outliers);
        removal_input_gt->AddData(poses_copy_for_removal_gt); // 使用转换后的 GT 位姿 (RwTc)
        outlier_removal_->SetRequiredData(removal_input_gt);

        auto cleaned_result_gt = outlier_removal_->Build();
        ASSERT_TRUE(cleaned_result_gt != nullptr) << "使用真值位姿进行异常点移除失败";
        auto cleaned_tracks_gt_ptr = GetDataPtr<Tracks>(cleaned_result_gt);
        ASSERT_TRUE(cleaned_tracks_gt_ptr != nullptr);
        AnalyzeTracks(*cleaned_tracks_gt_ptr, "清理后的轨迹 (使用真值位姿)");
        PlotResiduals(residuals_csv_gt, actual_outliers_path, plot_output_gt);

        // (可选) 比较两种场景下的清理效果
        // 例如，可以比较 valid_observations 数量，预期使用GT位姿时清理效果可能更好
        // int valid_cleaned_obs_est = /* ... count valid obs in cleaned_tracks_estimated_ptr ... */;
        // int valid_cleaned_obs_gt = /* ... count valid obs in cleaned_tracks_gt_ptr ... */;
        // EXPECT_GE(valid_cleaned_obs_gt, valid_cleaned_obs_est) << "Removal with GT poses should be at least as good as with estimated poses";
    }

    // 比较不同异常点检测方法的效果
    TEST_F(GlobalOutlierRemovalTest, CompareDetectionMethodsForLiGT)
    {
        // 0. 重置所有方法选项到INI文件初始状态，避免测试间参数干扰
        simulator_->ResetOptionsFromIniFile();
        outlier_removal_->ResetOptionsFromIniFile();
        ligt_method_->ResetOptionsFromIniFile();

        // 生成模拟数据
        simulator_->SetMethodOption("num_views", "10");
        simulator_->SetMethodOption("num_points", "500");
        simulator_->SetMethodOption("noise_level", "0");
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "10.0");

        auto result = simulator_->Run();
        ASSERT_TRUE(result != nullptr);

        auto package = std::dynamic_pointer_cast<DataPackage>(result);
        ASSERT_TRUE(package != nullptr);

        auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks != nullptr);
        auto simulator_poses_data = simulator_->GetGlobalPoses();
        ASSERT_TRUE(simulator_poses_data != nullptr);

        // 获取原始轨迹副本
        auto original_tracks_data = data_tracks->CopyData();
        ASSERT_TRUE(original_tracks_data != nullptr);
        auto tracks_ptr = GetDataPtr<Tracks>(original_tracks_data);
        ASSERT_TRUE(tracks_ptr != nullptr);
        Tracks original_tracks_copy = *tracks_ptr;

        // 创建用于添加异常点的副本
        auto tracks_with_outliers_data = data_tracks->CopyData();
        ASSERT_TRUE(tracks_with_outliers_data != nullptr);
        auto tracks_with_outliers_ptr = GetDataPtr<Tracks>(tracks_with_outliers_data);
        ASSERT_TRUE(tracks_with_outliers_ptr != nullptr);

        // 添加异常点并获取实际异常ID
        double outlier_ratio = 0.05;
        std::set<IndexT> actual_outlier_ids = AddOutliersToTracks(*tracks_with_outliers_ptr, outlier_ratio);

        // --- 新增：保存实际异常点ID到文件 ---
        std::string actual_outliers_path = "actual_outliers_method_comparison.txt";
        std::ofstream actual_out_file(actual_outliers_path);
        if (actual_out_file.is_open())
        {
            for (const auto &id : actual_outlier_ids)
            {
                actual_out_file << id << "\n";
            }
            actual_out_file.close();
            std::cout << "实际异常点ID已保存到: " << actual_outliers_path << std::endl;
        }
        else
        {
            std::cerr << "警告：无法打开文件写入实际异常点ID: " << actual_outliers_path << std::endl;
        }
        // --- 保存结束 ---

        // 创建新的 DataTracks 对象来持有带异常点的轨迹
        auto data_tracks_with_outliers = std::make_shared<DataTracks>();
        *GetDataPtr<Tracks>(data_tracks_with_outliers) = *tracks_with_outliers_ptr;

        // 保存真值数据
        bool gt_saved = simulator_->SaveGroundTruth();
        ASSERT_TRUE(gt_saved);

        // --- LiGT 运行 (只需要运行一次, 但其输入需要 RwTc) ---
        auto ligt_input = std::make_shared<DataPackage>();
        ligt_input->AddData(data_tracks_with_outliers);
        auto poses_copy_for_ligt = simulator_poses_data->CopyData(); // 创建位姿副本 (RwTw)
        ASSERT_TRUE(poses_copy_for_ligt != nullptr);
        // **转换位姿格式为 RwTc**
        auto *poses_ptr_for_ligt = static_cast<GlobalPoses *>(poses_copy_for_ligt->GetData());
        ASSERT_TRUE(poses_ptr_for_ligt != nullptr);
        ASSERT_TRUE(poses_ptr_for_ligt->ConvertPoseFormat(PoseFormat::RwTc))
            << "Failed to convert GT poses to RwTc for LiGT";
        ligt_input->AddData(poses_copy_for_ligt); // 添加转换后的副本
        ligt_input->AddData(simulator_->GetCameraModel());

        ligt_method_->SetMethodOption("compute_t", "true");
        ligt_method_->SetMethodOption("estimate_R", "false");
        ligt_method_->SetRequiredData(ligt_input);
        auto ligt_result = ligt_method_->Build(); // LiGT 输出 RwTc
        ASSERT_TRUE(ligt_result != nullptr);
        // --- LiGT 结束 ---

        // 比较两种检测方法
        std::vector<std::string> detection_methods = {"sigma", "mad", "chi2"};
        std::vector<std::string> method_names = {"3-Sigma法则", "MAD法则", "卡方检验"};

        // 存储结果的数据结构 (使用 EvaluationResult)
        struct MethodResult
        {
            std::string method_name;
            EvaluationResult eval_result; // 存储评估结果
        };
        std::vector<MethodResult> results_storage;

        for (size_t i = 0; i < detection_methods.size(); ++i)
        {
            const auto &method = detection_methods[i];
            const auto &method_name = method_names[i];

            // --- 异常点移除运行 ---
            std::string residuals_csv_path = "residuals_method_" + method + ".csv";
            std::string plot_output_path = "plot_method_" + method + ".png";

            // 创建异常点移除输入包
            auto outlier_removal_input = std::make_shared<DataPackage>();
            outlier_removal_input->AddData(data_tracks_with_outliers);
            auto poses_copy_for_removal = ligt_result->CopyData();
            ASSERT_TRUE(poses_copy_for_removal != nullptr);
            outlier_removal_input->AddData(poses_copy_for_removal);

            outlier_removal_->SetMethodOption("output_residuals_path", residuals_csv_path);
            outlier_removal_->SetMethodOption("residual_method", "LiGT");
            outlier_removal_->SetMethodOption("detection_method", method);
            // 设置特定于方法的参数
            if (boost::iequals(method, "sigma") || boost::iequals(method, "mad"))
            {
                outlier_removal_->SetMethodOption("threshold_multiplier", "3.0");
            }
            else if (boost::iequals(method, "chi2"))
            {
                // 显式设置卡方检验使用的标准分位数，即使它是默认值
                outlier_removal_->SetMethodOption("standard_chi2_quantile", "7.8147");
            }
            outlier_removal_->SetMethodOption("remove_outliers", "false");
            outlier_removal_->SetMethodOption("log_level", "0");

            outlier_removal_->SetRequiredData(outlier_removal_input);
            // **** 运行 Build 但不直接使用其返回的 cleaned_tracks 进行评估 ****
            auto build_output = outlier_removal_->Build();
            ASSERT_TRUE(build_output != nullptr) << "异常点移除构建步骤失败 for method " << method;
            // --- 异常点移除结束 ---

            // **** 调用新的评估函数 ****
            EvaluationResult current_eval = EvaluateOutlierDetection(actual_outliers_path, residuals_csv_path);

            PlotResiduals(residuals_csv_path, actual_outliers_path, plot_output_path);

            // auto cleaned_tracks_ptr = GetDataPtr<Tracks>(cleaned_result);
            // ASSERT_TRUE(cleaned_tracks_ptr != nullptr);

            // // 统计异常点检测效果 (旧逻辑，注释掉)
            // int actual_outliers = actual_outlier_ids.size();
            // int detected_outliers = 0;
            // int correctly_detected = 0;
            // for (const auto& track_info : *cleaned_tracks_ptr) {
            //     for (const auto& obs : track_info.track) {
            //         if (!obs.is_used) {
            //             detected_outliers++;
            //             if (actual_outlier_ids.count(obs.obs_id)) {
            //                 correctly_detected++;
            //             }
            //         }
            //     }
            // }
            // // 计算指标 (召回率和精确率) (旧逻辑，注释掉)
            // double recall = actual_outliers > 0 ?
            //     static_cast<double>(correctly_detected) / actual_outliers * 100 : (detected_outliers == 0 ? 100.0 : 0.0);
            // double precision = detected_outliers > 0 ?
            //     static_cast<double>(correctly_detected) / detected_outliers * 100 : 100.0;

            // 存储结果 (使用新的评估结果)
            results_storage.push_back({method_name, current_eval});

            // 添加断言 (基于新的评估结果)
            EXPECT_GT(current_eval.recall, 70.0)
                << method_name << "方法的召回率应该较高 (基于文件评估)";
            EXPECT_GT(current_eval.precision, 70.0) // 假设精确率也应该比较高
                << method_name << "方法的精确率应该较高 (基于文件评估)";
        }

        // 统一打印结果表格 (使用新的评估结果)
        std::cout << "\n不同异常点检测方法对比 (异常点比例: " << outlier_ratio * 100 << "%):\n"
                  << std::string(100, '=') << std::endl; // 宽度应该足够
        std::cout << std::left
                  << std::setw(15) << "检测方法" << "|"
                  << std::setw(15) << "实际异常数" << "|"
                  << std::setw(15) << "检测异常数" << "|"
                  << std::setw(15) << "正确检测数" << "|"
                  << std::setw(18) << "召回率 (%)" << "|"
                  << std::setw(18) << "精确率 (%)" << std::endl;
        std::cout << std::string(100, '-') << std::endl;

        for (const auto &res : results_storage)
        {
            std::cout << std::left << std::fixed << std::setprecision(2)
                      << std::setw(16) << res.method_name // 调整宽度
                      << std::setw(16) << res.eval_result.actual_outliers_count
                      << std::setw(16) << res.eval_result.detected_outliers_count
                      << std::setw(16) << res.eval_result.correctly_detected_count
                      << std::setw(19) << res.eval_result.recall    // 调整宽度
                      << std::setw(19) << res.eval_result.precision // 调整宽度
                      << std::endl;
        }

        std::cout << std::string(100, '-') << std::endl;
        std::cout << "召回率 (Recall): (正确检测出的异常点 / 实际异常点总数) * 100%" << std::endl;
        std::cout << "精确率 (Precision): (正确检测出的异常点 / 所有被检测为异常点的观测) * 100%" << std::endl;
    }

    // ============ 比较不同异常点检测方法的效果 (MedLiGT) ============

    TEST_F(GlobalOutlierRemovalTest, CompareDetectionMethodsForMedLiGT)
    {
        // 0. 重置所有方法选项到INI文件初始状态，避免测试间参数干扰
        simulator_->ResetOptionsFromIniFile();
        outlier_removal_->ResetOptionsFromIniFile();
        ligt_method_->ResetOptionsFromIniFile();

        // 生成模拟数据
        simulator_->SetMethodOption("num_views", "10");
        simulator_->SetMethodOption("num_points", "500");
        simulator_->SetMethodOption("noise_level", "0");
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "10.0");

        auto result = simulator_->Run();
        ASSERT_TRUE(result != nullptr);

        auto package = std::dynamic_pointer_cast<DataPackage>(result);
        ASSERT_TRUE(package != nullptr);

        auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks != nullptr);
        auto simulator_poses_data = simulator_->GetGlobalPoses();
        ASSERT_TRUE(simulator_poses_data != nullptr);

        // 获取原始轨迹副本
        auto original_tracks_data = data_tracks->CopyData();
        ASSERT_TRUE(original_tracks_data != nullptr);
        auto tracks_ptr = GetDataPtr<Tracks>(original_tracks_data);
        ASSERT_TRUE(tracks_ptr != nullptr);
        Tracks original_tracks_copy = *tracks_ptr;

        // 创建用于添加异常点的副本
        auto tracks_with_outliers_data = data_tracks->CopyData();
        ASSERT_TRUE(tracks_with_outliers_data != nullptr);
        auto tracks_with_outliers_ptr = GetDataPtr<Tracks>(tracks_with_outliers_data);
        ASSERT_TRUE(tracks_with_outliers_ptr != nullptr);

        // 添加异常点并获取实际异常ID
        double outlier_ratio = 0.3;
        std::set<IndexT> actual_outlier_ids = AddOutliersToTracks(*tracks_with_outliers_ptr, outlier_ratio);

        // --- 新增：保存实际异常点ID到文件 ---
        std::string actual_outliers_path = "actual_outliers_method_comparison.txt";
        std::ofstream actual_out_file(actual_outliers_path);
        if (actual_out_file.is_open())
        {
            for (const auto &id : actual_outlier_ids)
            {
                actual_out_file << id << "\n";
            }
            actual_out_file.close();
            std::cout << "实际异常点ID已保存到: " << actual_outliers_path << std::endl;
        }
        else
        {
            std::cerr << "警告：无法打开文件写入实际异常点ID: " << actual_outliers_path << std::endl;
        }
        // --- 保存结束 ---

        // 创建新的 DataTracks 对象来持有带异常点的轨迹
        auto data_tracks_with_outliers = std::make_shared<DataTracks>();
        *GetDataPtr<Tracks>(data_tracks_with_outliers) = *tracks_with_outliers_ptr;

        // 保存真值数据
        bool gt_saved = simulator_->SaveGroundTruth();
        ASSERT_TRUE(gt_saved);

        // --- LiGT 运行 (只需要运行一次, 但其输入需要 RwTc) ---
        auto ligt_input = std::make_shared<DataPackage>();
        ligt_input->AddData(data_tracks_with_outliers);
        auto poses_copy_for_ligt = simulator_poses_data->CopyData(); // 创建位姿副本 (RwTw)
        ASSERT_TRUE(poses_copy_for_ligt != nullptr);
        // **转换位姿格式为 RwTc**
        auto *poses_ptr_for_ligt = static_cast<GlobalPoses *>(poses_copy_for_ligt->GetData());
        ASSERT_TRUE(poses_ptr_for_ligt != nullptr);
        ASSERT_TRUE(poses_ptr_for_ligt->ConvertPoseFormat(PoseFormat::RwTc))
            << "Failed to convert GT poses to RwTc for LiGT";
        ligt_input->AddData(poses_copy_for_ligt); // 添加转换后的副本
        ligt_input->AddData(simulator_->GetCameraModel());

        ligt_method_->SetMethodOption("compute_t", "true");
        ligt_method_->SetMethodOption("estimate_R", "false");
        ligt_method_->SetRequiredData(ligt_input);
        auto ligt_result = ligt_method_->Build(); // LiGT 输出 RwTc
        ASSERT_TRUE(ligt_result != nullptr);
        // --- LiGT 结束 ---

        // 比较两种检测方法
        std::vector<std::string> detection_methods = {"sigma", "mad", "chi2"};
        std::vector<std::string> method_names = {"3-Sigma法则", "MAD法则", "卡方检验"};

        // 存储结果的数据结构 (使用 EvaluationResult)
        struct MethodResult
        {
            std::string method_name;
            EvaluationResult eval_result; // 存储评估结果
        };
        std::vector<MethodResult> results_storage;

        for (size_t i = 0; i < detection_methods.size(); ++i)
        {
            const auto &method = detection_methods[i];
            const auto &method_name = method_names[i];

            // --- 异常点移除运行 ---
            std::string residuals_csv_path = "residuals_method_" + method + ".csv";
            std::string plot_output_path = "plot_method_" + method + ".png";

            // 创建异常点移除输入包
            auto outlier_removal_input = std::make_shared<DataPackage>();
            outlier_removal_input->AddData(data_tracks_with_outliers);
            auto poses_copy_for_removal = ligt_result->CopyData();
            ASSERT_TRUE(poses_copy_for_removal != nullptr);
            outlier_removal_input->AddData(poses_copy_for_removal);

            outlier_removal_->SetMethodOption("output_residuals_path", residuals_csv_path);
            outlier_removal_->SetMethodOption("residual_method", "MedLiGT");
            outlier_removal_->SetMethodOption("detection_method", method);
            // 设置特定于方法的参数
            if (boost::iequals(method, "sigma") || boost::iequals(method, "mad"))
            {
                outlier_removal_->SetMethodOption("threshold_multiplier", "3.0");
            }
            else if (boost::iequals(method, "chi2"))
            {
                // 显式设置卡方检验使用的标准分位数，即使它是默认值
                outlier_removal_->SetMethodOption("standard_chi2_quantile", "7.8147");
            }
            outlier_removal_->SetMethodOption("remove_outliers", "false");
            outlier_removal_->SetMethodOption("log_level", "0");

            outlier_removal_->SetRequiredData(outlier_removal_input);
            // **** 运行 Build 但不直接使用其返回的 cleaned_tracks 进行评估 ****
            auto build_output = outlier_removal_->Build();
            ASSERT_TRUE(build_output != nullptr) << "异常点移除构建步骤失败 for method " << method;
            // --- 异常点移除结束 ---

            // **** 调用新的评估函数 ****
            EvaluationResult current_eval = EvaluateOutlierDetection(actual_outliers_path, residuals_csv_path);

            PlotResiduals(residuals_csv_path, actual_outliers_path, plot_output_path);

            // auto cleaned_tracks_ptr = GetDataPtr<Tracks>(cleaned_result);
            // ASSERT_TRUE(cleaned_tracks_ptr != nullptr);

            // // 统计异常点检测效果 (旧逻辑，注释掉)
            // int actual_outliers = actual_outlier_ids.size();
            // int detected_outliers = 0;
            // int correctly_detected = 0;
            // for (const auto& track_info : *cleaned_tracks_ptr) {
            //     for (const auto& obs : track_info.track) {
            //         if (!obs.is_used) {
            //             detected_outliers++;
            //             if (actual_outlier_ids.count(obs.obs_id)) {
            //                 correctly_detected++;
            //             }
            //         }
            //     }
            // }
            // // 计算指标 (召回率和精确率) (旧逻辑，注释掉)
            // double recall = actual_outliers > 0 ?
            //     static_cast<double>(correctly_detected) / actual_outliers * 100 : (detected_outliers == 0 ? 100.0 : 0.0);
            // double precision = detected_outliers > 0 ?
            //     static_cast<double>(correctly_detected) / detected_outliers * 100 : 100.0;

            // 存储结果 (使用新的评估结果)
            results_storage.push_back({method_name, current_eval});

            // 添加断言 (基于新的评估结果)
            EXPECT_GT(current_eval.recall, 70.0)
                << method_name << "方法的召回率应该较高 (基于文件评估)";
            EXPECT_GT(current_eval.precision, 70.0) // 假设精确率也应该比较高
                << method_name << "方法的精确率应该较高 (基于文件评估)";
        }

        // 统一打印结果表格 (使用新的评估结果)
        std::cout << "\n不同异常点检测方法对比 (异常点比例: " << outlier_ratio * 100 << "%):\n"
                  << std::string(100, '=') << std::endl; // 宽度应该足够
        std::cout << std::left
                  << std::setw(15) << "检测方法" << "|"
                  << std::setw(15) << "实际异常数" << "|"
                  << std::setw(15) << "检测异常数" << "|"
                  << std::setw(15) << "正确检测数" << "|"
                  << std::setw(18) << "召回率 (%)" << "|"
                  << std::setw(18) << "精确率 (%)" << std::endl;
        std::cout << std::string(100, '-') << std::endl;

        for (const auto &res : results_storage)
        {
            std::cout << std::left << std::fixed << std::setprecision(2)
                      << std::setw(16) << res.method_name // 调整宽度
                      << std::setw(16) << res.eval_result.actual_outliers_count
                      << std::setw(16) << res.eval_result.detected_outliers_count
                      << std::setw(16) << res.eval_result.correctly_detected_count
                      << std::setw(19) << res.eval_result.recall    // 调整宽度
                      << std::setw(19) << res.eval_result.precision // 调整宽度
                      << std::endl;
        }

        std::cout << std::string(100, '-') << std::endl;
        std::cout << "召回率 (Recall): (正确检测出的异常点 / 实际异常点总数) * 100%" << std::endl;
        std::cout << "精确率 (Precision): (正确检测出的异常点 / 所有被检测为异常点的观测) * 100%" << std::endl;
    }

    // ============ 测试角度误差模式 vs 代数误差模式 ============

    TEST_F(GlobalOutlierRemovalTest, CompareAngleErrorVsAlgebraicError)
    {
        // 0. 重置所有方法选项到INI文件初始状态，避免测试间参数干扰
        simulator_->ResetOptionsFromIniFile();
        outlier_removal_->ResetOptionsFromIniFile();
        ligt_method_->ResetOptionsFromIniFile();

        // 生成模拟数据
        // simulator_->SetMethodOption("num_views", "8");
        // simulator_->SetMethodOption("num_points", "300");
        // simulator_->SetMethodOption("noise_level", "0");
        // simulator_->SetMethodOption("structure_type", "close_loop_circle");
        // simulator_->SetMethodOption("num_circles", "1");
        // simulator_->SetMethodOption("dt_setting", "10.0");

        simulator_->SetMethodOption("num_views", "10");
        simulator_->SetMethodOption("num_points", "500");
        simulator_->SetMethodOption("noise_level", "0");
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "10.0");

        auto result = simulator_->Run();
        ASSERT_TRUE(result != nullptr);

        auto package = std::dynamic_pointer_cast<DataPackage>(result);
        ASSERT_TRUE(package != nullptr);

        auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks != nullptr);
        auto simulator_poses_data = simulator_->GetGlobalPoses();
        ASSERT_TRUE(simulator_poses_data != nullptr);

        // 创建用于添加异常点的副本
        auto tracks_with_outliers_data = data_tracks->CopyData();
        ASSERT_TRUE(tracks_with_outliers_data != nullptr);
        auto tracks_with_outliers_ptr = GetDataPtr<Tracks>(tracks_with_outliers_data);
        ASSERT_TRUE(tracks_with_outliers_ptr != nullptr);

        // 添加异常点并获取实际异常ID
        double outlier_ratio = 0.3;
        std::set<IndexT> actual_outlier_ids = AddOutliersToTracks(*tracks_with_outliers_ptr, outlier_ratio);

        // 保存实际异常点ID到文件
        std::string actual_outliers_path = "actual_outliers_angle_vs_algebraic.txt";
        std::ofstream actual_out_file(actual_outliers_path);
        if (actual_out_file.is_open())
        {
            for (const auto &id : actual_outlier_ids)
            {
                actual_out_file << id << "\n";
            }
            actual_out_file.close();
            std::cout << "实际异常点ID已保存到: " << actual_outliers_path << std::endl;
        }

        // 创建新的 DataTracks 对象来持有带异常点的轨迹
        auto data_tracks_with_outliers = std::make_shared<DataTracks>();
        *GetDataPtr<Tracks>(data_tracks_with_outliers) = *tracks_with_outliers_ptr;

        // 准备位姿数据（转换为RwTc格式）
        auto poses_copy = simulator_poses_data->CopyData();
        ASSERT_TRUE(poses_copy != nullptr);
        auto *poses_ptr = static_cast<GlobalPoses *>(poses_copy->GetData());
        ASSERT_TRUE(poses_ptr != nullptr);
        ASSERT_TRUE(poses_ptr->ConvertPoseFormat(PoseFormat::RwTc))
            << "Failed to convert GT poses to RwTc";

        // 保存真值数据
        bool gt_saved = simulator_->SaveGroundTruth();
        ASSERT_TRUE(gt_saved);

        // --- LiGT 运行 (只需要运行一次, 但其输入需要 RwTc) ---
        auto ligt_input = std::make_shared<DataPackage>();
        ligt_input->AddData(data_tracks_with_outliers);
        auto poses_copy_for_ligt = simulator_poses_data->CopyData(); // 创建位姿副本 (RwTw)
        ASSERT_TRUE(poses_copy_for_ligt != nullptr);
        // **转换位姿格式为 RwTc**
        auto *poses_ptr_for_ligt = static_cast<GlobalPoses *>(poses_copy_for_ligt->GetData());
        ASSERT_TRUE(poses_ptr_for_ligt != nullptr);
        ASSERT_TRUE(poses_ptr_for_ligt->ConvertPoseFormat(PoseFormat::RwTc))
            << "Failed to convert GT poses to RwTc for LiGT";
        ligt_input->AddData(poses_copy_for_ligt); // 添加转换后的副本
        ligt_input->AddData(simulator_->GetCameraModel());

        ligt_method_->SetMethodOption("compute_t", "true");
        ligt_method_->SetMethodOption("estimate_R", "false");
        ligt_method_->SetRequiredData(ligt_input);
        auto ligt_result = ligt_method_->Build(); // LiGT 输出 RwTc
        ASSERT_TRUE(ligt_result != nullptr);
        // --- LiGT 结束 ---

        // 比较两种误差模式
        std::vector<bool> angle_error_modes = {false, true};
        std::vector<std::string> mode_names = {"代数误差模式", "角度误差模式"};

        // 存储结果的数据结构
        struct ErrorModeResult
        {
            std::string mode_name;
            EvaluationResult eval_result;
        };
        std::vector<ErrorModeResult> results_storage;

        for (size_t i = 0; i < angle_error_modes.size(); ++i)
        {
            bool use_angle_error = angle_error_modes[i];
            const auto &mode_name = mode_names[i];

            // 设置残差输出文件
            std::string residuals_csv_path = use_angle_error ? "residuals_angle_error.csv" : "residuals_algebraic_error.csv";
            std::string plot_output_path = use_angle_error ? "plot_angle_error.png" : "plot_algebraic_error.png";

            // 创建异常点移除输入包
            auto outlier_removal_input = std::make_shared<DataPackage>();
            outlier_removal_input->AddData(data_tracks_with_outliers);
            auto poses_copy_for_removal = ligt_result->CopyData();
            ASSERT_TRUE(poses_copy_for_removal != nullptr);
            outlier_removal_input->AddData(poses_copy_for_removal);

            // 设置方法选项
            outlier_removal_->SetMethodOption("output_residuals_path", residuals_csv_path);
            outlier_removal_->SetMethodOption("residual_method", "MedLiGT");
            outlier_removal_->SetMethodOption("detection_method", "mad"); // 使用小写保持一致
            outlier_removal_->SetMethodOption("threshold_multiplier", "3.0");
            outlier_removal_->SetMethodOption("angle_error_mode", use_angle_error ? "true" : "false");
            outlier_removal_->SetMethodOption("remove_outliers", "false");
            outlier_removal_->SetMethodOption("log_level", "0");

            outlier_removal_->SetRequiredData(outlier_removal_input);
            auto build_output = outlier_removal_->Build();
            ASSERT_TRUE(build_output != nullptr) << "异常点移除构建步骤失败 for " << mode_name;

            // 评估结果
            EvaluationResult current_eval = EvaluateOutlierDetection(actual_outliers_path, residuals_csv_path);
            PlotResiduals(residuals_csv_path, actual_outliers_path, plot_output_path);

            // 存储结果
            results_storage.push_back({mode_name, current_eval});

            // 基本断言：两种模式都应该能检测到一些异常点
            EXPECT_GT(current_eval.detected_outliers_count, 0)
                << mode_name << "应该能检测到一些异常点";
        }

        // 统一打印结果表格
        std::cout << "\n角度误差模式 vs 代数误差模式对比 (异常点比例: " << outlier_ratio * 100 << "%):\n"
                  << std::string(100, '=') << std::endl;
        std::cout << std::left
                  << std::setw(20) << "误差模式" << "|"
                  << std::setw(15) << "实际异常数" << "|"
                  << std::setw(15) << "检测异常数" << "|"
                  << std::setw(15) << "正确检测数" << "|"
                  << std::setw(18) << "召回率 (%)" << "|"
                  << std::setw(18) << "精确率 (%)" << std::endl;
        std::cout << std::string(100, '-') << std::endl;

        for (const auto &res : results_storage)
        {
            std::cout << std::left << std::fixed << std::setprecision(2)
                      << std::setw(21) << res.mode_name
                      << std::setw(16) << res.eval_result.actual_outliers_count
                      << std::setw(16) << res.eval_result.detected_outliers_count
                      << std::setw(16) << res.eval_result.correctly_detected_count
                      << std::setw(19) << res.eval_result.recall
                      << std::setw(19) << res.eval_result.precision
                      << std::endl;
        }

        std::cout << std::string(100, '-') << std::endl;
        std::cout << "说明：" << std::endl;
        std::cout << "- 代数误差模式：使用残差向量的平方范数（原始方法）" << std::endl;
        std::cout << "- 角度误差模式：使用观测方向与重建方向之间的角度误差（弧度）" << std::endl;
        std::cout << "- 角度误差通常更直观，代表重投影角度偏差" << std::endl;
    }

    // ============ 测试直接阈值检测方法 ============

    TEST_F(GlobalOutlierRemovalTest, TestDirectThresholdDetection)
    {
        // 0. 重置所有方法选项到INI文件初始状态，避免测试间参数干扰
        simulator_->ResetOptionsFromIniFile();
        outlier_removal_->ResetOptionsFromIniFile();
        ligt_method_->ResetOptionsFromIniFile();

        // 生成模拟数据
        simulator_->SetMethodOption("num_views", "10");
        simulator_->SetMethodOption("num_points", "500");
        simulator_->SetMethodOption("noise_level", "0");
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "10.0");

        auto result = simulator_->Run();
        ASSERT_TRUE(result != nullptr);

        auto package = std::dynamic_pointer_cast<DataPackage>(result);
        ASSERT_TRUE(package != nullptr);

        auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks != nullptr);
        auto simulator_poses_data = simulator_->GetGlobalPoses();
        ASSERT_TRUE(simulator_poses_data != nullptr);

        // 创建用于添加异常点的副本
        auto tracks_with_outliers_data = data_tracks->CopyData();
        ASSERT_TRUE(tracks_with_outliers_data != nullptr);
        auto tracks_with_outliers_ptr = GetDataPtr<Tracks>(tracks_with_outliers_data);
        ASSERT_TRUE(tracks_with_outliers_ptr != nullptr);

        // 添加异常点并获取实际异常ID
        double outlier_ratio = 0.3;
        std::set<IndexT> actual_outlier_ids = AddOutliersToTracks(*tracks_with_outliers_ptr, outlier_ratio);

        // 保存实际异常点ID到文件
        std::string actual_outliers_path = "actual_outliers_threshold_test.txt";
        std::ofstream actual_out_file(actual_outliers_path);
        if (actual_out_file.is_open())
        {
            for (const auto &id : actual_outlier_ids)
            {
                actual_out_file << id << "\n";
            }
            actual_out_file.close();
            std::cout << "实际异常点ID已保存到: " << actual_outliers_path << std::endl;
        }

        // 创建新的 DataTracks 对象来持有带异常点的轨迹
        auto data_tracks_with_outliers = std::make_shared<DataTracks>();
        *GetDataPtr<Tracks>(data_tracks_with_outliers) = *tracks_with_outliers_ptr;

        // 保存真值数据
        bool gt_saved = simulator_->SaveGroundTruth();
        ASSERT_TRUE(gt_saved);

        // --- LiGT 运行 ---
        auto ligt_input = std::make_shared<DataPackage>();
        ligt_input->AddData(data_tracks_with_outliers);
        auto poses_copy_for_ligt = simulator_poses_data->CopyData();
        ASSERT_TRUE(poses_copy_for_ligt != nullptr);
        auto *poses_ptr_for_ligt = static_cast<GlobalPoses *>(poses_copy_for_ligt->GetData());
        ASSERT_TRUE(poses_ptr_for_ligt != nullptr);
        ASSERT_TRUE(poses_ptr_for_ligt->ConvertPoseFormat(PoseFormat::RwTc))
            << "Failed to convert GT poses to RwTc for LiGT";
        ligt_input->AddData(poses_copy_for_ligt);
        ligt_input->AddData(simulator_->GetCameraModel());

        ligt_method_->SetMethodOption("compute_t", "true");
        ligt_method_->SetMethodOption("estimate_R", "false");
        ligt_method_->SetRequiredData(ligt_input);
        auto ligt_result = ligt_method_->Build();
        ASSERT_TRUE(ligt_result != nullptr);
        // --- LiGT 结束 ---

        // 测试不同的直接阈值配置
        struct ThresholdTestCase
        {
            std::string residual_method;
            bool angle_error_mode;
            std::string threshold_option; // e.g., "direct_threshold_medligt_algebraic"
            double threshold_value;       // The actual numeric threshold
            std::string description;
        };

        std::vector<ThresholdTestCase> test_cases;

        // 定义阈值范围
        std::vector<double> algebraic_thresholds = {1e-3, 1e-4, 1e-5, 1e-6, 1e-7, 1e-8};
        std::vector<double> angle_thresholds_degrees = {1.0, 0.5, 0.1, 0.05, 0.01};

        // --- 生成 MedLiGT 测试用例 ---
        // MedLiGT 代数误差
        for (double alg_thresh : algebraic_thresholds)
        {
            test_cases.push_back({"MedLiGT", false, "direct_threshold_medligt_algebraic", alg_thresh,
                                  "MedLiGT_Alg_" + std::to_string(alg_thresh)});
        }
        // MedLiGT 角度误差
        for (double angle_deg : angle_thresholds_degrees)
        {
            test_cases.push_back({"MedLiGT", true, "direct_threshold_angle_error", angle_deg,
                                  "MedLiGT_Angle_" + std::to_string(angle_deg) + "deg"});
        }

        // --- 生成 LiGT 测试用例 ---
        // LiGT 代数误差
        for (double alg_thresh : algebraic_thresholds)
        {
            test_cases.push_back({"LiGT", false, "direct_threshold_ligt_algebraic", alg_thresh,
                                  "LiGT_Alg_" + std::to_string(alg_thresh)});
        }
        // LiGT 角度误差
        for (double angle_deg : angle_thresholds_degrees)
        {
            test_cases.push_back({"LiGT", true, "direct_threshold_angle_error", angle_deg,
                                  "LiGT_Angle_" + std::to_string(angle_deg) + "deg"});
        }

        // 存储结果的数据结构
        struct ThresholdTestResult
        {
            std::string description;
            EvaluationResult eval_result;
        };
        std::vector<ThresholdTestResult> results_storage;

        for (const auto &test_case : test_cases)
        {
            // 设置残差输出文件
            std::string residuals_csv_path = "residuals_threshold_" + test_case.residual_method + "_" +
                                             (test_case.angle_error_mode ? "angle" : "algebraic") + "_" +
                                             std::to_string(test_case.threshold_value) + ".csv";
            std::string plot_output_path = "plot_threshold_" + test_case.residual_method + "_" +
                                           (test_case.angle_error_mode ? "angle" : "algebraic") + "_" +
                                           std::to_string(test_case.threshold_value) + ".png";

            // 创建异常点移除输入包
            auto outlier_removal_input = std::make_shared<DataPackage>();
            outlier_removal_input->AddData(data_tracks_with_outliers);
            auto poses_copy_for_removal = ligt_result->CopyData();
            ASSERT_TRUE(poses_copy_for_removal != nullptr);
            outlier_removal_input->AddData(poses_copy_for_removal);

            // 设置方法选项
            outlier_removal_->SetMethodOption("output_residuals_path", residuals_csv_path);
            outlier_removal_->SetMethodOption("residual_method", test_case.residual_method);
            outlier_removal_->SetMethodOption("detection_method", "threshold");
            outlier_removal_->SetMethodOption("angle_error_mode", test_case.angle_error_mode ? "true" : "false");
            outlier_removal_->SetMethodOption(test_case.threshold_option, std::to_string(test_case.threshold_value));
            outlier_removal_->SetMethodOption("remove_outliers", "false");
            outlier_removal_->SetMethodOption("log_level", "2"); // 详细日志

            outlier_removal_->SetRequiredData(outlier_removal_input);
            auto build_output = outlier_removal_->Build();
            ASSERT_TRUE(build_output != nullptr) << "异常点移除构建步骤失败 for " << test_case.description;

            // 评估结果
            EvaluationResult current_eval = EvaluateOutlierDetection(actual_outliers_path, residuals_csv_path);
            PlotResiduals(residuals_csv_path, actual_outliers_path, plot_output_path);

            // 存储结果
            results_storage.push_back({test_case.description, current_eval});

            // 基本断言：应该能检测到一些异常点
            EXPECT_GT(current_eval.detected_outliers_count, 0)
                << test_case.description << "应该能检测到一些异常点";
        }

        // 统一打印结果表格
        std::cout << "\n直接阈值检测方法对比 (异常点比例: " << outlier_ratio * 100 << "%):\n"
                  << std::string(120, '=') << std::endl;
        std::cout << std::left
                  << std::setw(30) << "配置描述" << "|"
                  << std::setw(15) << "实际异常数" << "|"
                  << std::setw(15) << "检测异常数" << "|"
                  << std::setw(15) << "正确检测数" << "|"
                  << std::setw(18) << "召回率 (%)" << "|"
                  << std::setw(18) << "精确率 (%)" << std::endl;
        std::cout << std::string(120, '-') << std::endl;

        for (const auto &res : results_storage)
        {
            std::cout << std::left << std::fixed << std::setprecision(2)
                      << std::setw(31) << res.description
                      << std::setw(16) << res.eval_result.actual_outliers_count
                      << std::setw(16) << res.eval_result.detected_outliers_count
                      << std::setw(16) << res.eval_result.correctly_detected_count
                      << std::setw(19) << res.eval_result.recall
                      << std::setw(19) << res.eval_result.precision
                      << std::endl;
        }

        std::cout << std::string(120, '-') << std::endl;
        std::cout << "说明：" << std::endl;
        std::cout << "- 直接阈值检测：超过设定阈值的残差被标记为异常点" << std::endl;
        std::cout << "- 角度误差阈值单位为度，代数误差阈值为残差平方范数" << std::endl;
        std::cout << "- 不同残差方法和误差模式需要不同的阈值设置" << std::endl;

        // 将结果写入CSV文件
        std::string summary_csv_path = "direct_threshold_summary_results.csv";
        std::ofstream summary_file(summary_csv_path);
        if (summary_file.is_open())
        {
            summary_file << "Description,ActualOutliers,DetectedOutliers,CorrectlyDetected,Recall(%),Precision(%)\n";
            for (const auto &res : results_storage)
            {
                summary_file << res.description << ","
                             << res.eval_result.actual_outliers_count << ","
                             << res.eval_result.detected_outliers_count << ","
                             << res.eval_result.correctly_detected_count << ","
                             << std::fixed << std::setprecision(2) << res.eval_result.recall << ","
                             << std::fixed << std::setprecision(2) << res.eval_result.precision << "\n";
            }
            summary_file.close();
            std::cout << "\n详细的阈值测试结果已保存到: " << std::filesystem::absolute(summary_csv_path) << std::endl;
        }
        else
        {
            std::cerr << "\n错误：无法打开文件写入阈值测试摘要: " << summary_csv_path << std::endl;
        }
    }

} // namespace
