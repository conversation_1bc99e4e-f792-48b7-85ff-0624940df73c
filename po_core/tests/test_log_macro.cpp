#include <gtest/gtest.h>
#include <sstream>
#include <iostream>
#include <string>
#include <memory>

// 包含日志宏定义
#include "interfaces_preset.hpp"

// 测试用的简单类，继承自MethodPreset以测试日志宏
class TestLogMethod : public PoSDK::Interface::MethodPreset
{
public:
    TestLogMethod()
    {
        // 设置不同的日志级别来测试
        log_level_ = PO_LOG_VERBOSE; // 设置为详细日志级别
    }

    const std::string &GetType() const override
    {
        static const std::string type = "test_log_method";
        return type;
    }

    PoSDK::Interface::DataPtr Run() override
    {
        return nullptr; // 简单实现
    }

    // 测试不同级别的日志输出
    void TestLogLevels()
    {
        std::cout << "\n=== 测试日志级别输出 ===" << std::endl;
        std::cout << "当前日志级别: " << log_level_ << std::endl;

        // 测试不同级别的日志
        PO_LOG(PO_LOG_NONE) << "这是 PO_LOG_NONE 级别的日志 (level=0)" << std::endl;
        PO_LOG(PO_LOG_NORMAL) << "这是 PO_LOG_NORMAL 级别的日志 (level=1)" << std::endl;
        PO_LOG(PO_LOG_VERBOSE) << "这是 PO_LOG_VERBOSE 级别的日志 (level=2)" << std::endl;

        // 测试错误和警告日志
        PO_LOG_ERR << "这是错误日志测试" << std::endl;
        PO_LOG_WARNING << "这是警告日志测试" << std::endl;
    }

    // 测试_DEBUG宏是否正确定义
    void TestDebugMacro()
    {
        std::cout << "\n=== 编译时宏定义检查 ===" << std::endl;

#ifdef _DEBUG
        std::cout << "_DEBUG 宏已定义 - Debug模式" << std::endl;
#else
        std::cout << "_DEBUG 宏未定义 - Release模式" << std::endl;
#endif

#ifdef NDEBUG
        std::cout << "NDEBUG 宏已定义" << std::endl;
#else
        std::cout << "NDEBUG 宏未定义" << std::endl;
#endif

        std::cout << "CMAKE_BUILD_TYPE: ";
#ifdef CMAKE_BUILD_TYPE
        std::cout << CMAKE_BUILD_TYPE << std::endl;
#else
        std::cout << "未定义" << std::endl;
#endif
    }

    // 测试FileNameOnly函数
    void TestFileNameOnly()
    {
        std::cout << "\n=== FileNameOnly 函数测试 ===" << std::endl;
        std::cout << "当前文件: " << __FILE__ << std::endl;
        std::cout << "提取的文件名: " << PoSDK::Interface::FileNameOnly(__FILE__) << std::endl;
        std::cout << "当前行号: " << __LINE__ << std::endl;
    }

    // 公开log_level_以便测试
    int &GetLogLevel() { return log_level_; }

    // 添加一个专门的测试方法来调用LOG宏
    void TestSingleLogCall()
    {
        PO_LOG(PO_LOG_NORMAL) << "测试消息 - 应该显示文件名和行号" << std::endl;
    }
};

class LogMacroTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        test_method = std::make_unique<TestLogMethod>();
    }

    void TearDown() override
    {
        test_method.reset();
    }

    std::unique_ptr<TestLogMethod> test_method;
};

TEST_F(LogMacroTest, TestDebugMacroDefinition)
{
    ASSERT_NE(test_method, nullptr);
    test_method->TestDebugMacro();

    // 验证在Debug模式下_DEBUG应该被定义
#ifdef _DEBUG
    std::cout << "✓ _DEBUG宏已正确定义" << std::endl;
#else
    std::cout << "✗ _DEBUG宏未定义，可能影响日志格式" << std::endl;
#endif
}

TEST_F(LogMacroTest, TestFileNameOnlyFunction)
{
    ASSERT_NE(test_method, nullptr);
    test_method->TestFileNameOnly();

    // 验证FileNameOnly函数工作正常
    std::string current_file = __FILE__;
    std::string extracted_name = PoSDK::Interface::FileNameOnly(__FILE__);

    EXPECT_FALSE(extracted_name.empty());
    EXPECT_EQ(extracted_name, "test_log_macro.cpp");
    std::cout << "✓ FileNameOnly函数工作正常" << std::endl;
}

TEST_F(LogMacroTest, TestLogLevelOutput)
{
    ASSERT_NE(test_method, nullptr);

    // 测试不同的日志级别设置
    std::cout << "\n--- 测试 PO_LOG_NONE 级别 ---" << std::endl;
    test_method->GetLogLevel() = PO_LOG_NONE;
    test_method->TestLogLevels();

    std::cout << "\n--- 测试 PO_LOG_NORMAL 级别 ---" << std::endl;
    test_method->GetLogLevel() = PO_LOG_NORMAL;
    test_method->TestLogLevels();

    std::cout << "\n--- 测试 PO_LOG_VERBOSE 级别 ---" << std::endl;
    test_method->GetLogLevel() = PO_LOG_VERBOSE;
    test_method->TestLogLevels();
}

TEST_F(LogMacroTest, TestLogMacroExpansion)
{
    ASSERT_NE(test_method, nullptr);

    std::cout << "\n=== 日志宏展开测试 ===" << std::endl;

    // 直接测试宏展开
    test_method->GetLogLevel() = PO_LOG_VERBOSE;

    std::cout << "\n当前宏定义状态:" << std::endl;
    std::cout << "PO_LOG_NONE = " << PO_LOG_NONE << std::endl;
    std::cout << "PO_LOG_NORMAL = " << PO_LOG_NORMAL << std::endl;
    std::cout << "PO_LOG_VERBOSE = " << PO_LOG_VERBOSE << std::endl;

    // 手动验证level > 0的条件
    int test_level = PO_LOG_NORMAL;
    bool should_show_file_info = (test_level > 0);
    std::cout << "level = " << test_level << ", 应该显示文件信息: " << (should_show_file_info ? "是" : "否") << std::endl;

#ifdef _DEBUG
    std::cout << "\nDebug模式下的日志格式:" << std::endl;
    test_method->TestLogLevels();
#else
    std::cout << "\nRelease模式下的日志格式:" << std::endl;
    test_method->TestLogLevels();
#endif
}

// 主函数手动测试
TEST_F(LogMacroTest, ManualLogFormatTest)
{
    ASSERT_NE(test_method, nullptr);

    std::cout << "\n=== 手动日志格式验证 ===" << std::endl;

    // 设置为VERBOSE级别
    test_method->GetLogLevel() = PO_LOG_VERBOSE;

    std::cout << "预期输出格式（level=1时）: [PoSDK | test_log_method] >>> test_log_macro.cpp line[行号]: [消息内容]" << std::endl;
    std::cout << "实际输出: ";

    // 通过test_method对象调用LOG宏，这样就在正确的作用域内了
    test_method->TestSingleLogCall();

    std::cout << "如果上面的输出没有显示文件名和行号，则存在问题" << std::endl;
}