/**
 * @file test_proto_tracks.cpp
 * @brief 测试Track数据的序列化和反序列化
 * @details 测试Track数据的创建、存储和加载功能
 *
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <random>
#include <filesystem>

namespace
{
    using namespace PoSDK;
    using namespace types;

    class TestProtoTracks : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            test_dir_ = std::filesystem::temp_directory_path() / "pomvg_test";
            std::filesystem::create_directories(test_dir_);
            test_file_ = test_dir_ / "test_tracks.pb";
            factory_data_ = std::make_unique<FactoryData>();
        }

        void TearDown() override
        {
            std::filesystem::remove_all(test_dir_);
        }

        // 生成随机Track数据
        void GenerateRandomTracks(DataPtr &data_ptr, size_t num_tracks = 30, size_t obs_per_track = 10)
        {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<ViewId> view_dist(0, 10);           // 视图索引范围
            std::uniform_int_distribution<PtsId> pts_dist(0, 100);            // 点索引范围
            std::uniform_real_distribution<double> coord_dist(-100.0, 100.0); // 坐标范围

            auto tracks = GetDataPtr<Tracks>(data_ptr);
            ASSERT_TRUE(tracks != nullptr);

            for (size_t i = 0; i < num_tracks; ++i)
            {
                TrackInfo track_info;
                track_info.is_used = true;

                for (size_t j = 0; j < obs_per_track; ++j)
                {
                    types::ObsInfo obs;
                    obs.view_id = view_dist(gen);
                    obs.pts_id = pts_dist(gen);
                    obs.coord = Eigen::Vector2d(
                        coord_dist(gen),
                        coord_dist(gen));
                    obs.is_used = true;
                    track_info.track.push_back(obs);
                }

                tracks->push_back(std::move(track_info));
            }
        }

        // 验证两个Tracks数据是否相同
        void ValidateTracks(const Tracks &original, const Tracks &loaded)
        {
            ASSERT_EQ(original.size(), loaded.size()) << "Track数量不匹配";

            for (size_t i = 0; i < original.size(); ++i)
            {
                const auto &orig_track = original[i];
                const auto &load_track = loaded[i];

                ASSERT_EQ(orig_track.is_used, load_track.is_used)
                    << "Track " << i << " 使用标记不匹配";
                ASSERT_EQ(orig_track.track.size(), load_track.track.size())
                    << "Track " << i << " 观测点数量不匹配";

                for (size_t j = 0; j < orig_track.track.size(); ++j)
                {
                    const auto &orig_obs = orig_track.track[j];
                    const auto &load_obs = load_track.track[j];

                    ASSERT_EQ(orig_obs.view_id, load_obs.view_id)
                        << "Track " << i << " 观测点 " << j << " view_id不匹配";
                    ASSERT_EQ(orig_obs.pts_id, load_obs.pts_id)
                        << "Track " << i << " 观测点 " << j << " pts_id不匹配";
                    ASSERT_TRUE(orig_obs.coord.isApprox(load_obs.coord))
                        << "Track " << i << " 观测点 " << j << " 坐标不匹配";
                    ASSERT_EQ(orig_obs.is_used, load_obs.is_used)
                        << "Track " << i << " 观测点 " << j << " 使用标记不匹配";
                }
            }
        }

        std::filesystem::path test_dir_;
        std::filesystem::path test_file_;
        std::unique_ptr<FactoryData> factory_data_;
    };

    /**
     * @brief 测试Track数据的基本序列化和反序列化
     */
    TEST_F(TestProtoTracks, BasicSerializationTest)
    {
        auto data_ptr = factory_data_->Create("data_tracks");
        ASSERT_TRUE(data_ptr != nullptr);

        // 生成随机数据
        GenerateRandomTracks(data_ptr);

        // 保存数据
        ASSERT_TRUE(data_ptr->Save(test_dir_.string(), "test_tracks", ".pb"));

        // 获取原始数据用于比较
        auto original_tracks = GetDataPtr<Tracks>(data_ptr);
        ASSERT_TRUE(original_tracks != nullptr);

        // 加载数据
        auto loaded_ptr = factory_data_->Create("data_tracks");
        ASSERT_TRUE(loaded_ptr != nullptr);
        ASSERT_TRUE(loaded_ptr->Load(test_file_.string()));

        auto loaded_tracks = GetDataPtr<Tracks>(loaded_ptr);
        ASSERT_TRUE(loaded_tracks != nullptr);

        ValidateTracks(*original_tracks, *loaded_tracks);
    }

    /**
     * @brief 测试空Track数据和无效文件处理
     */
    TEST_F(TestProtoTracks, EdgeCases)
    {
        auto data_ptr = factory_data_->Create("data_tracks");
        ASSERT_TRUE(data_ptr != nullptr);

        // 测试空数据序列化
        ASSERT_TRUE(data_ptr->Save(test_dir_.string(), "test_tracks", ".pb"));

        auto loaded_ptr = factory_data_->Create("data_tracks");
        ASSERT_TRUE(loaded_ptr != nullptr);
        ASSERT_TRUE(loaded_ptr->Load(test_file_.string()));

        auto tracks = GetDataPtr<Tracks>(loaded_ptr);
        ASSERT_TRUE(tracks != nullptr);
        EXPECT_TRUE(tracks->empty());

        // 测试无效文件处理
        EXPECT_FALSE(loaded_ptr->Load("nonexistent_file.pb"));
    }

} // namespace
