
03:27:56: Starting /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/bin/test_all...
Running main() from /tmp/googletest-20250207-4735-e28tfc/googletest-1.16.0/googletest/src/gtest_main.cc
[==========] Running 1 test from 1 test suite.
[----------] Global test environment set-up.
[----------] 1 test from TestRobustLiRP
[ RUN      ] TestRobustLiRP.OutlierRobustnessTest
Initializing RANSACEstimator...
[PoSDK | ransac_estimator] >>> Loaded configuration files in order (low to high priority):
[PoSDK | ransac_estimator] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/ransac_estimator.ini
[PoSDK | gnc_irls_estimator] >>> Loaded configuration files in order (low to high priority):
[PoSDK | gnc_irls_estimator] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/gnc_irls_estimator.ini
[PoSDK | method_robustLiRP] >>> Loaded configuration files in order (low to high priority):
[PoSDK | method_robustLiRP] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/method_robustLiRP.ini
[PoSDK | method_robustLiRP] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/method_robustLiRP.ini
[PoSDK | method_robustLiRP] >>> local_config: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/method_robustLiRP.ini
[PoSDK | method_robustLiRP] >>> Loaded configuration files in order (low to high priority):
[PoSDK | method_robustLiRP] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/method_robustLiRP.ini
[PoSDK | method_robustLiRP] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/method_robustLiRP.ini

=== 测试不同外点比例下的算法性能对比 ===
测试配置: 5 个外点比例, 4 个算法, 20 次重复

--- 测试外点比例: 10% (commit: outlier_10pct) ---
重复 1/20
  测试算法: method_LiRP + (default)[PoSDK | opengv_simulator] >>> OpenGVSimulator Finished 
[method_LiRP] Set evaluator algorithm to: method_LiRP
 -> 成功
  测试算法: RANSAC + (default)[PoSDK | opengv_simulator] >>> OpenGVSimulator Finished 
[ransac_estimator] Set evaluator algorithm to: RANSAC

=== Loading Configuration ===
Config File: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/ransac_estimator.ini

=== Configuration Description ===
outlier_10pct
-----------------------------------

=== Method Options for ransac_estimator ===
log_level                 = 2 (详细)
max_iterations         = 50000          inlier_threshold       = 5*1e-3         
enable_profiling       = false          confidence             = 0.99           
enable_evaluator       = true           model_estimator_type   = method_LiRP    
min_sample_size        = 8              cost_evaluator_type    = method_relative_cost
=====================================

[pomvg]:iterations=0,best_inlier_count=98,best_cost=0.2399809192,k=2114446.6434761574
[pomvg]:iterations=1,best_inlier_count=450,best_cost=0.0423897617,k=8.1805875708
[pomvg]:iterations=5,best_inlier_count=450,best_cost=0.0313431578,k=8.1805875708
[pomvg]:iterations=8,best_inlier_count=450,best_cost=0.0293440409,k=8.1805875708
[PoSDK | ransac_estimator] >>> [ransac_estimator] Successfully added 2 evaluation results to global evaluator (type: RelativePose, algorithm: RANSAC, commit: outlier_10pct[PoSDK | ransac_estimator] >>> )
 -> 成功
  测试算法: GNC_IRLS + (default)[PoSDK | opengv_simulator] >>> OpenGVSimulator Finished 
[gnc_irls_estimator] Set evaluator algorithm to: GNC_IRLS

=== Loading Configuration ===
Config File: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/gnc_irls_estimator.ini

=== Configuration Description ===
outlier_10pct
-----------------------------------

=== Method Options for gnc_irls_estimator ===
log_level                 = 2 (详细)
use_majorization       = true           use_superlinear        = true           
gamma                  = 1.4            model_estimator_type   = method_LiRP    
enable_evaluator       = true           max_iterations         = 20             
enable_profiling       = false          convergence_threshold  = 1e-10          
cost_evaluator_type    = method_relative_costinlier_threshold       = 5*1e-3         
noise_scale            = 5.54           sigma_mode             = 2              
=====================================

[PoSDK | gnc_irls_estimator] >>> GNC-IRLS完成: 内点比例=90.20%, GNC指示器=200.0000, 阈值=10.0000
[PoSDK | gnc_irls_estimator] >>> [gnc_irls_estimator] Successfully added 2 evaluation results to global evaluator (type: RelativePose, algorithm: GNC_IRLS, commit: outlier_10pct[PoSDK | gnc_irls_estimator] >>> )
 -> 成功
  测试算法: method_RobustLiRP + gnc_irls[PoSDK | opengv_simulator] >>> OpenGVSimulator Finished 
[method_robustLiRP] Set evaluator algorithm to: method_RobustLiRP-GNC_IRLS

=== Loading Configuration ===
Config File: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/method_robustLiRP.ini

=== Configuration Description ===
outlier_10pct
-----------------------------------

=== Method Options for method_robustLiRP ===
log_level                 = 2 (详细)
cost_evaluator_type    = method_relative_costenable_two_stage_refinement = false          
lirp_use_opt_mode      = false          view_j                 = 1              
lirp_use_median_cost   = true           ransac_confidence      = 0.99           
gnc_track_best_model   = false          gnc_gamma              = 1.4            
gnc_inlier_threshold   = 5*1e-3         ransac_min_iterations  = 5              
ransac_max_iterations  = 50000          view_i                 = 0              
enable_evaluator       = true           ransac_inlier_threshold = 5*1e-3         
gnc_convergence_threshold = 1e-10          gnc_noise_scale        = 5.54           
gnc_max_iterations     = 20             gnc_irls_max_iterations = 20             
robust_type            = gnc_irls       gnc_use_majorization   = true           
gnc_sigma_mode         = 2              ransac_min_sample_size = 6              
gnc_irls_noise_scale   = 5.54           lirp_identify_mode     = PPO            
enable_profiling       = false          gnc_min_iterations     = 3              
lirp_compute_mode      = true           gnc_use_superlinear    = true           
=====================================

[PoSDK | method_robustLiRP] >>> LiRP algorithm check passed: Total matches: 500, Min required: 12
[PoSDK | gnc_irls_estimator] >>> Loaded configuration files in order (low to high priority):
[PoSDK | gnc_irls_estimator] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/gnc_irls_estimator.ini
[PoSDK | gnc_irls_estimator] >>>   - /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/gnc_irls_estimator.ini
[PoSDK | method_robustLiRP] >>> Running robust estimation...

=== Loading Configuration ===
Config File: /Users/<USER>/Documents/PoMVG/po_core/build/Desktop_arm_darwin_generic_mach_o_64bit-Release/po_core_lib/configs/methods/gnc_irls_estimator.ini

=== Configuration Description ===
全局特征观测异常处理配置
-----------------------------------

=== Method Options for gnc_irls_estimator ===
log_level                 = 2 (详细)
use_majorization       = true           model_estimator_type   = method_LiRP    
gamma                  = 1.4            max_iterations         = 20             
enable_evaluator       = false          convergence_threshold  = 1e-10          
enable_profiling       = false          track_best_model       = false          
cost_evaluator_type    = method_relative_costinlier_threshold       = 5*1e-3         
noise_scale            = 5.54           sigma_mode             = 2              
use_superlinear        = true           min_iterations         = 3              
=====================================

[PoSDK | gnc_irls_estimator] >>> GNC-IRLS完成: 内点比例=90.00%, GNC指示器=200.0000, 阈值=10.0000
[PoSDK | method_robustLiRP] >>> Robust estimation completed with 450 inliers
[PoSDK | method_robustLiRP] >>> Robust estimation completed successfully
[PoSDK | method_robustLiRP] >>> Rotation matrix: 
0.9381  0.2440  0.2457 
-0.2025 0.9621  -0.1823
-0.2809 0.1213  0.9520 
[PoSDK | method_robustLiRP] >>> Translation vector: 
0.2590
0.7249
0.6383
[PoSDK | method_robustLiRP] >>> [method_robustLiRP] Successfully added 2 evaluation results to global evaluator (type: RelativePose, algorithm: method_RobustLiRP-GNC_IRLS, commit: outlier_10pct[PoSDK | method_robustLiRP] >>> )
 -> 成功