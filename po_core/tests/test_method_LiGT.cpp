/**
 * @file test_method_LiGT.cpp
 * @brief 测试LiGT方法的功能
 * @details 测试包括数据加载、位姿估计等功能
 *
 * @copyright Copyright (c) 2024
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <fstream>
#include <Eigen/Core>

namespace
{
    using namespace PoSDK;
    using namespace Interface;

    class TestMethodLiGT : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            initial_memory_ = GetCurrentMemoryUsage();

            // 设置测试数据路径
            test_data_root_ = std::string(PROJECT_SOURCE_DIR) + "/tests/LiGT";
            tracks_file_ = test_data_root_ + "/Matches_5Image_100Points_10.00Dt_0.txt";
            rotation_file_ = test_data_root_ + "/global_R_1.txt";

            // 确保测试数据存在
            ASSERT_TRUE(std::filesystem::exists(tracks_file_))
                << "Tracks file not found: " << tracks_file_;
            ASSERT_TRUE(std::filesystem::exists(rotation_file_))
                << "Rotation file not found: " << rotation_file_;

            // 创建输出目录
            std::filesystem::create_directories("storage/LiGT");
        }

        DataPtr LoadTracksData(TracksPtr &tracks_ptr)
        {
            // 创建数据对象
            auto data_tracks = FactoryData::Create("data_tracks");
            if (!data_tracks)
            {
                ADD_FAILURE() << "Failed to create data_tracks";
                return nullptr;
            }

            // 加载tracks数据
            bool load_success = data_tracks->Load(tracks_file_, "tracks");
            if (!load_success)
            {
                ADD_FAILURE() << "Failed to load tracks file";
                return nullptr;
            }

            // 获取tracks数据指针
            tracks_ptr = GetDataPtr<Tracks>(data_tracks);
            if (!tracks_ptr || tracks_ptr->empty())
            {
                ADD_FAILURE() << "Invalid tracks data";
                return nullptr;
            }

            return data_tracks;
        }

        DataPtr LoadGlobalPoses(GlobalPosesPtr &global_poses_ptr)
        {
            // 创建全局位姿数据对象
            auto data_global_poses = FactoryData::Create("data_global_poses");
            if (!data_global_poses)
            {
                ADD_FAILURE() << "Failed to create data_global_poses";
                return nullptr;
            }

            // 加载旋转数据
            bool load_success = data_global_poses->Load(rotation_file_, "rot");
            if (!load_success)
            {
                ADD_FAILURE() << "Failed to load rotation file";
                return nullptr;
            }

            // 获取全局位姿数据指针
            global_poses_ptr = GetDataPtr<GlobalPoses>(data_global_poses);
            if (!global_poses_ptr)
            {
                ADD_FAILURE() << "Invalid global poses data";
                return nullptr;
            }

            return data_global_poses;
        }

        void ValidateResults(const GlobalPosesPtr &global_poses_ptr)
        {
            ASSERT_TRUE(global_poses_ptr != nullptr);

            // 验证旋转矩阵
            const auto &rotations = global_poses_ptr->rotations;
            ASSERT_FALSE(rotations.empty());

            // 验证平移向量
            const auto &translations = global_poses_ptr->translations;
            ASSERT_FALSE(translations.empty());
            ASSERT_EQ(rotations.size(), translations.size());

            // 验证每个位姿的有效性
            for (size_t i = 0; i < rotations.size(); ++i)
            {
                // 验证旋转矩阵的正交性
                const Matrix3d &R = rotations[i];
                Matrix3d RRt = R * R.transpose();
                Matrix3d I = Matrix3d::Identity();
                EXPECT_TRUE((RRt - I).norm() < 1e-6)
                    << "Rotation matrix " << i << " is not orthogonal";

                // 验证平移向量的范数
                const Vector3d &t = translations[i];
                EXPECT_FALSE(t.hasNaN())
                    << "Translation " << i << " contains NaN values";
            }
        }

        void TearDown() override
        {
            size_t final_memory = GetCurrentMemoryUsage();
            EXPECT_LE(final_memory - initial_memory_, MAX_ALLOWED_MEMORY_LEAK)
                << "Possible memory leak detected";
        }

    protected:
        std::string test_data_root_;
        std::string tracks_file_;
        std::string rotation_file_;
        size_t initial_memory_;
        static constexpr size_t MAX_ALLOWED_MEMORY_LEAK = 1024; // 1KB

    private:
        size_t GetCurrentMemoryUsage()
        {
            // TODO: 实现具体的内存检测逻辑
            return 0;
        }
    };

    // 基础功能测试
    TEST_F(TestMethodLiGT, BasicFunctionality)
    {
        // 准备输入数据
        TracksPtr tracks_ptr;
        GlobalPosesPtr global_poses_ptr;

        // 加载数据
        auto data_tracks_ptr = LoadTracksData(tracks_ptr);
        ASSERT_TRUE(data_tracks_ptr != nullptr);

        auto data_global_poses_ptr = LoadGlobalPoses(global_poses_ptr);
        ASSERT_TRUE(data_global_poses_ptr != nullptr);

        // 创建LiGT方法
        auto method_ptr = FactoryMethod::Create("method_LiGT");
        ASSERT_TRUE(method_ptr != nullptr);

        // 设置方法参数
        auto preset_method = std::dynamic_pointer_cast<Interface::MethodPreset>(method_ptr);
        ASSERT_TRUE(preset_method != nullptr);

        MethodOptions options{
            {"bal_file_path", "storage/LiGT/output.bal"},
            {"ProfileCommit", "LiGT基础功能测试"}};
        preset_method->SetMethodOptions(options);

        // 创建数据包
        auto package_ptr = std::make_shared<DataPackage>();
        package_ptr->AddData(data_tracks_ptr);
        package_ptr->AddData(data_global_poses_ptr);

        // 执行LiGT算法
        auto result = method_ptr->Build(package_ptr);
        ASSERT_TRUE(result != nullptr);

        // 验证结果
        ValidateResults(global_poses_ptr);
    }

} // namespace
