/**
 * @file test_visual_simulator.cpp
 * @brief 测试VisualSimulator功能
 * @copyright Copyright (c) 2024 PoSDK Project
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include "internal/methods/visual_simulator.hpp"
#include <memory>
#include <iostream>
#include <map>
#include <vector>
#include <set>
#include <filesystem>
#include <fstream>

namespace
{
    using namespace PoSDK;
    using namespace Interface;

    class VisualSimulatorTest : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 创建模拟器
            simulator_ = std::dynamic_pointer_cast<VisualSimulator>(
                FactoryMethod::Create("visual_simulator"));
            ASSERT_TRUE(simulator_ != nullptr);
        }

        std::shared_ptr<VisualSimulator> simulator_;

        // 辅助函数：分析轨迹数据
        void AnalyzeTracks(const Tracks *tracks_ptr)
        {
            ASSERT_TRUE(tracks_ptr != nullptr) << "AnalyzeTracks received a null pointer";
            const Tracks &tracks = *tracks_ptr; // Dereference the pointer

            if (tracks.empty())
            {
                std::cout << "No tracks found!" << std::endl;
                return;
            }

            // 计算平均轨迹长度
            double avg_length = 0.0;
            int min_length = std::numeric_limits<int>::max();
            int max_length = 0;

            for (const auto &track : tracks)
            {
                int length = track.track.size();
                avg_length += length;
                min_length = std::min(min_length, length);
                max_length = std::max(max_length, length);
            }
            avg_length /= tracks.size();

            // 计算每个视图中的点数
            std::map<ViewId, int> points_per_view;
            for (const auto &track : tracks)
            {
                for (const auto &obs : track.track)
                {
                    points_per_view[obs.view_id]++;
                }
            }

            // 输出统计信息
            std::cout << "轨迹分析结果:" << std::endl;
            std::cout << "  - 轨迹总数: " << tracks.size() << std::endl;
            std::cout << "  - 平均轨迹长度: " << avg_length << std::endl;
            std::cout << "  - 最短轨迹长度: " << min_length << std::endl;
            std::cout << "  - 最长轨迹长度: " << max_length << std::endl;
            std::cout << "  - 视图数量: " << points_per_view.size() << std::endl;

            std::cout << "  - 各视图点数: " << std::endl;
            for (const auto &[view_id, count] : points_per_view)
            {
                std::cout << "    视图 " << view_id << ": " << count << " 个点" << std::endl;
            }
        }
    };

    // 基本功能测试
    TEST_F(VisualSimulatorTest, BasicFunctionality)
    {
        // 设置基本参数
        simulator_->SetMethodOption("num_views", "10");
        simulator_->SetMethodOption("num_points", "500");
        simulator_->SetMethodOption("noise_level", "1.0");
        simulator_->SetMethodOption("debug_output", "true");

        // 运行模拟器
        auto result = simulator_->Run();

        // 验证结果
        ASSERT_TRUE(result != nullptr);
        auto package = std::dynamic_pointer_cast<DataPackage>(result);
        ASSERT_TRUE(package != nullptr);

        // 从package中获取DataTracks
        auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks != nullptr);

        // 分析生成的轨迹
        auto tracks_sptr = GetDataPtr<Tracks>(data_tracks);
        ASSERT_TRUE(tracks_sptr != nullptr) << "Failed to get tracks pointer from DataTracks";
        EXPECT_FALSE(tracks_sptr->empty());
        std::cout << "生成了 " << tracks_sptr->size() << " 个轨迹" << std::endl;

        // 分析轨迹详情
        AnalyzeTracks(tracks_sptr.get());
    }

    // 测试噪声设置
    TEST_F(VisualSimulatorTest, NoiseSettings)
    {
        // 测试没有噪声的情况
        simulator_->SetMethodOption("num_views", "3");
        simulator_->SetMethodOption("num_points", "500");
        simulator_->SetMethodOption("noise_level", "0.0");

        auto result_no_noise = simulator_->Run();
        auto package_no_noise = std::dynamic_pointer_cast<DataPackage>(result_no_noise);
        ASSERT_TRUE(package_no_noise != nullptr);
        auto data_no_noise = std::dynamic_pointer_cast<DataTracks>(package_no_noise->GetData("data_tracks"));
        ASSERT_TRUE(data_no_noise != nullptr);

        std::cout << "\n无噪声测试:" << std::endl;
        auto no_noise_tracks_sptr = GetDataPtr<Tracks>(data_no_noise);
        AnalyzeTracks(no_noise_tracks_sptr.get());

        // 测试高噪声水平
        simulator_->SetMethodOption("noise_level", "5.0");
        auto result_high_noise = simulator_->Run();
        auto package_high_noise = std::dynamic_pointer_cast<DataPackage>(result_high_noise);
        ASSERT_TRUE(package_high_noise != nullptr);
        auto data_high_noise = std::dynamic_pointer_cast<DataTracks>(package_high_noise->GetData("data_tracks"));
        ASSERT_TRUE(data_high_noise != nullptr);

        std::cout << "\n高噪声测试:" << std::endl;
        auto high_noise_tracks_sptr = GetDataPtr<Tracks>(data_high_noise);
        AnalyzeTracks(high_noise_tracks_sptr.get());

        // 测试不同噪声类型
        simulator_->SetMethodOption("noise_level", "2.0");
        simulator_->SetMethodOption("gaussian_noise", "true");
        auto result_gaussian = simulator_->Run();
        auto package_gaussian = std::dynamic_pointer_cast<DataPackage>(result_gaussian);
        ASSERT_TRUE(package_gaussian != nullptr);
        auto data_gaussian = std::dynamic_pointer_cast<DataTracks>(package_gaussian->GetData("data_tracks"));
        ASSERT_TRUE(data_gaussian != nullptr);

        std::cout << "\n高斯噪声测试:" << std::endl;
        auto gaussian_tracks_sptr = GetDataPtr<Tracks>(data_gaussian);
        AnalyzeTracks(gaussian_tracks_sptr.get());
    }

    // 测试轨迹长度和视图数量
    TEST_F(VisualSimulatorTest, TrackLengthAndViewCount)
    {
        // 增加视图数量
        simulator_->SetMethodOption("num_views", "8");
        simulator_->SetMethodOption("num_points", "1000");
        simulator_->SetMethodOption("noise_level", "1.0");

        auto result = simulator_->Run();
        DataPackagePtr package = std::dynamic_pointer_cast<DataPackage>(result);
        ASSERT_TRUE(package != nullptr);
        auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks != nullptr);

        std::cout << "\n多视图测试 (8个视图):" << std::endl;
        auto multi_view_tracks_sptr = GetDataPtr<Tracks>(data_tracks);
        AnalyzeTracks(multi_view_tracks_sptr.get());

        // 验证至少有一些轨迹跨越多个视图
        bool has_long_tracks = false;
        for (const auto &track : *multi_view_tracks_sptr)
        { // Dereference shared_ptr
            std::set<ViewId> unique_views;
            for (const auto &obs : track.track)
            {
                unique_views.insert(obs.view_id);
            }
            if (unique_views.size() >= 3)
            {
                has_long_tracks = true;
                break;
            }
        }
        EXPECT_TRUE(has_long_tracks) << "没有找到跨越至少3个视图的轨迹";
    }

    // 测试不同场景设置
    TEST_F(VisualSimulatorTest, SceneConfigurations)
    {
        // 小场景设置
        simulator_->SetMethodOption("num_views", "3");
        simulator_->SetMethodOption("num_points", "200");
        simulator_->SetMethodOption("closest_points", "100");

        auto result_small = simulator_->Run();
        auto package_small = std::dynamic_pointer_cast<DataPackage>(result_small);
        ASSERT_TRUE(package_small != nullptr);
        auto data_small = std::dynamic_pointer_cast<DataTracks>(package_small->GetData("data_tracks"));
        ASSERT_TRUE(data_small != nullptr);

        std::cout << "\n小场景测试:" << std::endl;
        auto small_tracks_sptr = GetDataPtr<Tracks>(data_small);
        AnalyzeTracks(small_tracks_sptr.get());

        // 大场景设置
        simulator_->SetMethodOption("num_views", "5");
        simulator_->SetMethodOption("num_points", "2000");
        simulator_->SetMethodOption("closest_points", "1000");

        auto result_large = simulator_->Run();
        auto package_large = std::dynamic_pointer_cast<DataPackage>(result_large);
        ASSERT_TRUE(package_large != nullptr);
        auto data_large = std::dynamic_pointer_cast<DataTracks>(package_large->GetData("data_tracks"));
        ASSERT_TRUE(data_large != nullptr);

        std::cout << "\n大场景测试:" << std::endl;
        auto large_tracks_sptr = GetDataPtr<Tracks>(data_large);
        AnalyzeTracks(large_tracks_sptr.get());
    }

    // 测试随机种子的一致性
    TEST_F(VisualSimulatorTest, RandomSeedConsistency)
    {
        // 用相同的种子生成两次数据
        simulator_->SetMethodOption("num_views", "3");
        simulator_->SetMethodOption("num_points", "500");
        simulator_->SetMethodOption("random_seed", "42");

        auto result1 = simulator_->Run();
        auto package1 = std::dynamic_pointer_cast<DataPackage>(result1);
        ASSERT_TRUE(package1 != nullptr);
        auto data1 = std::dynamic_pointer_cast<DataTracks>(package1->GetData("data_tracks"));
        ASSERT_TRUE(data1 != nullptr);

        auto result2 = simulator_->Run();
        auto package2 = std::dynamic_pointer_cast<DataPackage>(result2);
        ASSERT_TRUE(package2 != nullptr);
        auto data2 = std::dynamic_pointer_cast<DataTracks>(package2->GetData("data_tracks"));
        ASSERT_TRUE(data2 != nullptr);

        // 比较两次生成的轨迹数量是否相同
        auto tracks1_sptr = GetDataPtr<Tracks>(data1);
        auto tracks2_sptr = GetDataPtr<Tracks>(data2);
        EXPECT_EQ(tracks1_sptr->size(), tracks2_sptr->size())
            << "相同种子生成的轨迹数量应该相同";

        // 用不同的种子生成的数据应该不同
        simulator_->SetMethodOption("random_seed", "43");
        auto result3 = simulator_->Run();
        auto package3 = std::dynamic_pointer_cast<DataPackage>(result3);
        ASSERT_TRUE(package3 != nullptr);
        auto data3 = std::dynamic_pointer_cast<DataTracks>(package3->GetData("data_tracks"));
        ASSERT_TRUE(data3 != nullptr);

        // 注意：这里有很小的概率测试失败，因为随机数可能生成相同数量的轨迹
        // 但轨迹的具体内容应该不同
    }

    // 测试CloseLoopCircle结构生成
    TEST_F(VisualSimulatorTest, CloseLoopCircleStructure)
    {
        std::cout << "\n开始测试CloseLoopCircle结构生成...\n"
                  << std::endl;

        // 1. 标准圆环结构测试
        std::cout << "1. 标准圆环结构测试:" << std::endl;
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_views", "12");          // 12个视图
        simulator_->SetMethodOption("num_points", "500");        // 500个点
        simulator_->SetMethodOption("num_circles", "1");         // 单环
        simulator_->SetMethodOption("dt_setting", "5.0");        // 步长
        simulator_->SetMethodOption("PRO_ratio", "0.0");         // 无纯旋转异常点
        simulator_->SetMethodOption("obs_outlier_ratio", "0.0"); // 无观测点异常值
        simulator_->SetMethodOption("debug_output", "true");

        auto result_single = simulator_->Run();
        auto package_single = std::dynamic_pointer_cast<DataPackage>(result_single);
        ASSERT_TRUE(package_single != nullptr);

        auto data_tracks_single = std::dynamic_pointer_cast<DataTracks>(package_single->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks_single != nullptr);

        auto tracks_sptr_single = GetDataPtr<Tracks>(data_tracks_single);
        ASSERT_TRUE(tracks_sptr_single != nullptr);
        AnalyzeTracks(tracks_sptr_single.get());

        // 获取全局位姿进行验证 - 在圆环结构中，相机位姿应该近似呈圆形分布
        auto global_poses = simulator_->GetGlobalPoses();
        ASSERT_TRUE(global_poses != nullptr);
        auto poses_ptr = GetDataPtr<GlobalPoses>(global_poses);
        ASSERT_TRUE(poses_ptr != nullptr);

        // 验证位姿数量
        EXPECT_EQ(poses_ptr->Size(), 12) << "位姿数量应为12";

        // 验证轨迹覆盖度 - 在圆环结构中，大多数轨迹应该能被至少3个相机观测到
        int tracks_with_multiple_views = 0;
        for (const auto &track : *tracks_sptr_single)
        {
            std::set<ViewId> unique_views;
            for (const auto &obs : track.track)
            {
                unique_views.insert(obs.view_id);
            }
            if (unique_views.size() >= 3)
            {
                tracks_with_multiple_views++;
            }
        }
        double multi_view_ratio = static_cast<double>(tracks_with_multiple_views) / tracks_sptr_single->size();
        std::cout << "多视图轨迹比例: " << (multi_view_ratio * 100) << "%" << std::endl;
        EXPECT_GT(multi_view_ratio, 0.5) << "至少50%的轨迹应被多个视图观测";

        // 2. 多环结构测试
        std::cout << "\n2. 多环结构测试 (2个环):" << std::endl;
        simulator_->SetMethodOption("num_circles", "2");  // 双环
        simulator_->SetMethodOption("num_views", "24");   // 24个视图
        simulator_->SetMethodOption("dt_setting", "3.0"); // 步长减小

        auto result_multi = simulator_->Run();
        auto package_multi = std::dynamic_pointer_cast<DataPackage>(result_multi);
        ASSERT_TRUE(package_multi != nullptr);

        auto data_tracks_multi = std::dynamic_pointer_cast<DataTracks>(package_multi->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks_multi != nullptr);

        auto tracks_sptr_multi = GetDataPtr<Tracks>(data_tracks_multi);
        ASSERT_TRUE(tracks_sptr_multi != nullptr);
        AnalyzeTracks(tracks_sptr_multi.get());

        // 3. 纯旋转异常点测试
        std::cout << "\n3. 含纯旋转异常点的圆环结构测试:" << std::endl;
        simulator_->SetMethodOption("num_views", "12");  // 12个视图
        simulator_->SetMethodOption("num_circles", "1"); // 单环
        simulator_->SetMethodOption("PRO_ratio", "0.2"); // 20%纯旋转异常点

        auto result_pro_outlier = simulator_->Run();
        auto package_pro_outlier = std::dynamic_pointer_cast<DataPackage>(result_pro_outlier);
        ASSERT_TRUE(package_pro_outlier != nullptr);

        auto data_tracks_pro_outlier = std::dynamic_pointer_cast<DataTracks>(package_pro_outlier->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks_pro_outlier != nullptr);

        auto tracks_sptr_pro_outlier = GetDataPtr<Tracks>(data_tracks_pro_outlier);
        ASSERT_TRUE(tracks_sptr_pro_outlier != nullptr);
        AnalyzeTracks(tracks_sptr_pro_outlier.get());

        // 4. 观测点异常值测试
        std::cout << "\n4. 含观测点异常值的圆环结构测试:" << std::endl;
        simulator_->SetMethodOption("PRO_ratio", "0.0");          // 无纯旋转异常点
        simulator_->SetMethodOption("obs_outlier_ratio", "0.15"); // 15%观测点异常值

        auto result_obs_outlier = simulator_->Run();
        auto package_obs_outlier = std::dynamic_pointer_cast<DataPackage>(result_obs_outlier);
        ASSERT_TRUE(package_obs_outlier != nullptr);

        auto data_tracks_obs_outlier = std::dynamic_pointer_cast<DataTracks>(package_obs_outlier->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks_obs_outlier != nullptr);

        auto tracks_sptr_obs_outlier = GetDataPtr<Tracks>(data_tracks_obs_outlier);
        ASSERT_TRUE(tracks_sptr_obs_outlier != nullptr);
        AnalyzeTracks(tracks_sptr_obs_outlier.get());

        // 5. 综合异常点测试（同时包含两种异常点）
        std::cout << "\n5. 综合异常点测试（PRO + 观测点异常值）:" << std::endl;
        simulator_->SetMethodOption("PRO_ratio", "0.1");         // 10%纯旋转异常点
        simulator_->SetMethodOption("obs_outlier_ratio", "0.1"); // 10%观测点异常值

        auto result_combined = simulator_->Run();
        auto package_combined = std::dynamic_pointer_cast<DataPackage>(result_combined);
        ASSERT_TRUE(package_combined != nullptr);

        auto data_tracks_combined = std::dynamic_pointer_cast<DataTracks>(package_combined->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks_combined != nullptr);

        auto tracks_sptr_combined = GetDataPtr<Tracks>(data_tracks_combined);
        ASSERT_TRUE(tracks_sptr_combined != nullptr);
        AnalyzeTracks(tracks_sptr_combined.get());
    }

    // 测试异常值比例功能
    TEST_F(VisualSimulatorTest, OutlierRatioTests)
    {
        std::cout << "\n开始测试异常值比例功能...\n"
                  << std::endl;
        simulator_->ResetOptionsFromIniFile();

        // 基本设置：较小的数据集以便观察异常值效果
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_views", "6");
        simulator_->SetMethodOption("num_points", "200");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "8.0");
        simulator_->SetMethodOption("random_seed", "12345"); // 固定种子确保可重现性

        // 1. 测试纯旋转异常点(PRO_ratio)的不同比例
        std::cout << "1. 测试纯旋转异常点(PRO_ratio)不同比例效果:" << std::endl;

        std::vector<double> pro_ratios = {0.0, 0.1, 0.2, 0.3};
        for (double pro_ratio : pro_ratios)
        {
            std::cout << "\n  测试PRO_ratio = " << pro_ratio << std::endl;

            simulator_->SetMethodOption("PRO_ratio", std::to_string(pro_ratio));
            simulator_->SetMethodOption("obs_outlier_ratio", "0.0"); // 先只测试PRO

            auto result = simulator_->Run();
            auto package = std::dynamic_pointer_cast<DataPackage>(result);
            ASSERT_TRUE(package != nullptr);

            auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
            ASSERT_TRUE(data_tracks != nullptr);

            auto tracks_ptr = GetDataPtr<Tracks>(data_tracks);
            ASSERT_TRUE(tracks_ptr != nullptr);
            EXPECT_FALSE(tracks_ptr->empty()) << "PRO_ratio=" << pro_ratio << "时应生成轨迹";

            std::cout << "    生成轨迹数: " << tracks_ptr->size() << std::endl;
        }

        // 2. 测试观测点异常值(obs_outlier_ratio)的不同比例
        std::cout << "\n2. 测试观测点异常值(obs_outlier_ratio)不同比例效果:" << std::endl;

        std::vector<double> obs_ratios = {0.0, 0.05, 0.1, 0.2, 0.3};
        for (double obs_ratio : obs_ratios)
        {
            std::cout << "\n  测试obs_outlier_ratio = " << obs_ratio << std::endl;

            simulator_->SetMethodOption("PRO_ratio", "0.0"); // 先只测试观测异常值
            simulator_->SetMethodOption("obs_outlier_ratio", std::to_string(obs_ratio));

            auto result = simulator_->Run();
            auto package = std::dynamic_pointer_cast<DataPackage>(result);
            ASSERT_TRUE(package != nullptr);

            auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
            ASSERT_TRUE(data_tracks != nullptr);

            auto tracks_ptr = GetDataPtr<Tracks>(data_tracks);
            ASSERT_TRUE(tracks_ptr != nullptr);
            EXPECT_FALSE(tracks_ptr->empty()) << "obs_outlier_ratio=" << obs_ratio << "时应生成轨迹";

            // 计算总观测数用于验证异常值比例是否正确
            int total_obs = 0;
            for (const auto &track : *tracks_ptr)
            {
                total_obs += track.track.size();
            }

            std::cout << "    生成轨迹数: " << tracks_ptr->size()
                      << ", 总观测数: " << total_obs << std::endl;

            // 对于非零异常值比例，总观测数应该大于0
            if (obs_ratio > 0.0)
            {
                EXPECT_GT(total_obs, 0) << "obs_outlier_ratio=" << obs_ratio << "时应有观测数据";
            }
        }

        // 3. 测试组合异常值效果
        std::cout << "\n3. 测试组合异常值效果:" << std::endl;

        struct CombinedTest
        {
            double pro_ratio;
            double obs_ratio;
            std::string description;
        };

        std::vector<CombinedTest> combined_tests = {
            {0.1, 0.05, "轻度PRO + 轻度观测异常"},
            {0.2, 0.1, "中度PRO + 中度观测异常"},
            {0.3, 0.2, "重度PRO + 重度观测异常"},
            {0.0, 0.3, "无PRO + 重度观测异常"},
            {0.3, 0.0, "重度PRO + 无观测异常"}};

        for (const auto &test : combined_tests)
        {
            std::cout << "\n  测试: " << test.description
                      << " (PRO=" << test.pro_ratio
                      << ", obs=" << test.obs_ratio << ")" << std::endl;

            simulator_->SetMethodOption("PRO_ratio", std::to_string(test.pro_ratio));
            simulator_->SetMethodOption("obs_outlier_ratio", std::to_string(test.obs_ratio));

            auto result = simulator_->Run();
            auto package = std::dynamic_pointer_cast<DataPackage>(result);
            ASSERT_TRUE(package != nullptr);

            auto data_tracks = std::dynamic_pointer_cast<DataTracks>(package->GetData("data_tracks"));
            ASSERT_TRUE(data_tracks != nullptr);

            auto tracks_ptr = GetDataPtr<Tracks>(data_tracks);
            ASSERT_TRUE(tracks_ptr != nullptr);
            EXPECT_FALSE(tracks_ptr->empty()) << test.description << "时应生成轨迹";

            std::cout << "    生成轨迹数: " << tracks_ptr->size() << std::endl;
        }

        std::cout << "\n异常值比例功能测试完成。" << std::endl;
    }

    // 测试meshlab导出功能
    TEST_F(VisualSimulatorTest, MeshlabExportTest)
    {
        std::cout << "\n开始测试Meshlab导出功能...\n"
                  << std::endl;
        simulator_->ResetOptionsFromIniFile();

        // 基本设置：较小的数据集以便快速测试
        simulator_->SetMethodOption("structure_type", "close_loop_circle");
        simulator_->SetMethodOption("num_views", "20");
        simulator_->SetMethodOption("num_points", "10000");
        simulator_->SetMethodOption("num_circles", "1");
        simulator_->SetMethodOption("dt_setting", "6.0");
        simulator_->SetMethodOption("random_seed", "54321");
        simulator_->SetMethodOption("PRO_ratio", "0.0");
        simulator_->SetMethodOption("obs_outlier_ratio", "0.0");

        // 1. 测试不导出meshlab（空路径）
        std::cout << "1. 测试不导出meshlab（空路径）:" << std::endl;
        simulator_->SetMethodOption("export_meshlab_path", "");

        auto result_no_export = simulator_->Run();
        auto package_no_export = std::dynamic_pointer_cast<DataPackage>(result_no_export);
        ASSERT_TRUE(package_no_export != nullptr);

        auto data_tracks_no_export = std::dynamic_pointer_cast<DataTracks>(package_no_export->GetData("data_tracks"));
        ASSERT_TRUE(data_tracks_no_export != nullptr);

        auto tracks_ptr_no_export = GetDataPtr<Tracks>(data_tracks_no_export);
        ASSERT_TRUE(tracks_ptr_no_export != nullptr);
        EXPECT_FALSE(tracks_ptr_no_export->empty()) << "应该成功生成轨迹";

        std::cout << "  生成轨迹数: " << tracks_ptr_no_export->size() << std::endl;

        // 2. 测试PoSDK风格的meshlab导出
        std::cout << "\n2. 测试PoSDK风格的meshlab导出:" << std::endl;
        std::string meshlab_posdk_path = "temp_files/meshlab_posdk_test";
        simulator_->SetMethodOption("export_meshlab_path", meshlab_posdk_path);

        auto result_posdk_export = simulator_->Run();
        auto package_posdk_export = std::dynamic_pointer_cast<DataPackage>(result_posdk_export);
        ASSERT_TRUE(package_posdk_export != nullptr);

        // 验证PoSDK风格导出的文件
        std::filesystem::path posdk_export_dir(meshlab_posdk_path);
        std::filesystem::path posdk_mlp_file = posdk_export_dir / "scene_posdk.mlp";
        std::filesystem::path posdk_ply_file = posdk_export_dir / "points.ply";
        std::filesystem::path posdk_image_file = posdk_export_dir / "simulate_pic.tiff";

        EXPECT_TRUE(std::filesystem::exists(posdk_mlp_file)) << "应该生成PoSDK风格meshlab工程文件: " << posdk_mlp_file;
        EXPECT_TRUE(std::filesystem::exists(posdk_ply_file)) << "应该生成点云文件: " << posdk_ply_file;
        EXPECT_TRUE(std::filesystem::exists(posdk_image_file)) << "应该生成模拟图像文件: " << posdk_image_file;

        // 检查PoSDK风格的MLP文件内容
        if (std::filesystem::exists(posdk_mlp_file))
        {
            std::ifstream posdk_mlp_stream(posdk_mlp_file);
            std::string posdk_mlp_content((std::istreambuf_iterator<char>(posdk_mlp_stream)),
                                          std::istreambuf_iterator<char>());
            posdk_mlp_stream.close();

            EXPECT_TRUE(posdk_mlp_content.find("<!DOCTYPE MeshLabDocument>") != std::string::npos)
                << "PoSDK MLP文件应包含正确的文档头";
            EXPECT_TRUE(posdk_mlp_content.find("simulate_pic.tiff") != std::string::npos)
                << "PoSDK MLP文件应引用正确的图像文件";
            EXPECT_TRUE(posdk_mlp_content.find("VCGCamera") != std::string::npos)
                << "PoSDK MLP文件应包含相机信息";
            EXPECT_TRUE(posdk_mlp_content.find("FocalMm=") != std::string::npos)
                << "PoSDK MLP文件应包含焦距信息";

            std::cout << "  PoSDK风格MLP文件验证通过" << std::endl;
        }

        // 3. 测试含异常值的PoSDK风格导出
        std::cout << "\n3. 测试含异常值的PoSDK风格导出:" << std::endl;
        simulator_->SetMethodOption("PRO_ratio", "0.1");
        simulator_->SetMethodOption("obs_outlier_ratio", "0.1");
        simulator_->SetMethodOption("export_meshlab_path", "temp_files/meshlab_posdk_outlier_test");
        simulator_->SetMethodOption("export_meshlab_style", "posdk");

        auto result_outlier_export = simulator_->Run();
        auto package_outlier_export = std::dynamic_pointer_cast<DataPackage>(result_outlier_export);
        ASSERT_TRUE(package_outlier_export != nullptr);

        // 验证异常值场景下的PoSDK风格文件生成
        std::filesystem::path outlier_export_dir("temp_files/meshlab_posdk_outlier_test");
        std::filesystem::path outlier_mlp_file = outlier_export_dir / "scene_posdk.mlp";

        EXPECT_TRUE(std::filesystem::exists(outlier_mlp_file))
            << "含异常值场景应该也能成功导出PoSDK风格meshlab工程";

        std::cout << "  含异常值的PoSDK风格导出验证通过" << std::endl;

        std::cout << "\nMeshlab导出功能测试完成。" << std::endl;
    }

} // 命名空间结束
