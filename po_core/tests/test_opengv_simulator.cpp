/**
 * @file test_opengv_simulator.cpp
 * @brief 测试OpenGV双视图仿真器，演示多接口数据生成功能
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <iomanip>

namespace
{
    using namespace PoSDK;
    using namespace Interface;

    class TestOpenGVSimulator : public ::testing::Test
    {
    protected:
        void SetUp() override
        {
            // 创建OpenGV仿真器
            simulator_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("opengv_simulator"));
            ASSERT_TRUE(simulator_ != nullptr) << "Failed to create OpenGV simulator";
        }

        // 测试DataPackage内容
        void AnalyzeDataPackage(DataPtr package_data)
        {
            auto package = std::dynamic_pointer_cast<DataPackage>(package_data);
            if (!package)
            {
                std::cout << "❌ 数据不是DataPackage类型" << std::endl;
                return;
            }

            std::cout << "\n📦 DataPackage内容分析：" << std::endl;
            std::cout << std::string(50, '-') << std::endl;

            // 获取包中的所有数据
            const auto &package_info = package->GetPackage();

            std::cout << "包含 " << package_info.size() << " 个数据接口：" << std::endl;
            for (const auto &[alias, data] : package_info)
            {
                std::cout << "  - " << alias << " -> " << (data ? data->GetType() : "nullptr") << std::endl;
            }
        }

        Interface::MethodPresetPtr simulator_;
    };

    // 基本功能测试
    TEST_F(TestOpenGVSimulator, BasicFunctionality)
    {
        std::cout << "\n🔬 OpenGV仿真器基本功能测试" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        // 运行仿真器
        auto start_time = std::chrono::high_resolution_clock::now();
        auto result = simulator_->Build();
        auto end_time = std::chrono::high_resolution_clock::now();

        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                            end_time - start_time)
                            .count() /
                        1000.0;

        ASSERT_TRUE(result != nullptr) << "仿真器运行失败";

        std::cout << "⏱️  仿真耗时: " << std::fixed << std::setprecision(2)
                  << duration << " ms" << std::endl;

        // 分析生成的数据包
        AnalyzeDataPackage(result);
    }

    // 真值数据验证测试
    TEST_F(TestOpenGVSimulator, GroundTruthValidation)
    {
        std::cout << "\n🎯 真值数据验证测试" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        // 运行仿真器
        auto simulator_data = simulator_->Build();
        ASSERT_TRUE(simulator_data != nullptr);

        auto package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
        ASSERT_TRUE(package != nullptr);

        // 获取真值数据 - 使用新的字典式访问方式
        auto gt_data = (*package)["ground_truth"];
        ASSERT_TRUE(gt_data != nullptr) << "没有找到真值数据";

        auto gt_pose = GetDataPtr<RelativePose>(gt_data);
        ASSERT_TRUE(gt_pose != nullptr) << "真值数据格式错误";

        std::cout << "📐 真值相对位姿信息：" << std::endl;
        std::cout << "  视图对: (" << gt_pose->i << ", " << gt_pose->j << ")" << std::endl;
        std::cout << "  权重: " << gt_pose->weight << std::endl;

        // 验证旋转矩阵的正交性
        double det_R = gt_pose->Rij.determinant();
        double orthogonality_error = (gt_pose->Rij * gt_pose->Rij.transpose() -
                                      Eigen::Matrix3d::Identity())
                                         .norm();

        std::cout << "  旋转矩阵行列式: " << std::fixed << std::setprecision(6) << det_R << std::endl;
        std::cout << "  正交性误差: " << orthogonality_error << std::endl;

        EXPECT_NEAR(det_R, 1.0, 1e-10) << "旋转矩阵行列式应该为1";
        EXPECT_LT(orthogonality_error, 1e-10) << "旋转矩阵应该是正交的";

        // 验证平移向量归一化
        double translation_norm = gt_pose->tij.norm();
        std::cout << "  平移向量模长: " << translation_norm << std::endl;
        EXPECT_NEAR(translation_norm, 1.0, 1e-10) << "平移向量应该是归一化的";
    }

    // LiRP方法专项测试
    TEST_F(TestOpenGVSimulator, LiRPMethodSpecificTest)
    {
        std::cout << "\n🧮 LiRP方法专项测试" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        // 运行仿真器
        auto simulator_data = simulator_->Build();
        ASSERT_TRUE(simulator_data != nullptr);

        auto package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
        ASSERT_TRUE(package != nullptr);

        // 获取LiRP数据 - 使用新的字典式访问方式
        auto lirp_data = (*package)["method_LiRP"];
        ASSERT_TRUE(lirp_data != nullptr) << "没有找到LiRP数据";

        auto bearing_pairs_sample = std::dynamic_pointer_cast<DataSample<BearingPairs>>(lirp_data);
        ASSERT_TRUE(bearing_pairs_sample != nullptr) << "LiRP数据格式错误";

        auto bearing_pairs = GetDataPtr<BearingPairs>(lirp_data);
        ASSERT_TRUE(bearing_pairs != nullptr);

        std::cout << "📊 BearingPairs数据统计：" << std::endl;
        std::cout << "  特征点对数量: " << bearing_pairs->size() << std::endl;

        if (!bearing_pairs->empty())
        {
            const auto &first_pair = bearing_pairs->front();
            std::cout << "  第一对向量长度: " << first_pair.size() << std::endl;
            std::cout << "  向量1: " << first_pair.head<3>().transpose() << std::endl;
            std::cout << "  向量2: " << first_pair.tail<3>().transpose() << std::endl;

            // 验证向量是否归一化
            double norm1 = first_pair.head<3>().norm();
            double norm2 = first_pair.tail<3>().norm();
            std::cout << "  向量1模长: " << norm1 << std::endl;
            std::cout << "  向量2模长: " << norm2 << std::endl;

            EXPECT_NEAR(norm1, 1.0, 1e-10) << "bearing vector应该是归一化的";
            EXPECT_NEAR(norm2, 1.0, 1e-10) << "bearing vector应该是归一化的";
        }

        // 尝试运行LiRP方法
        try
        {
            auto lirp_method = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));

            if (lirp_method)
            {
                lirp_method->SetRequiredData(lirp_data);

                // 设置LiRP选项
                MethodOptions lirp_options{
                    {"compute_mode", "false"},
                    {"use_opt_mode", "false"},
                    {"identify_mode", "ppo"},
                    {"view_i", "0"},
                    {"view_j", "1"}};
                lirp_method->SetMethodOptions(lirp_options);

                std::cout << "\n🚀 运行LiRP方法..." << std::endl;
                auto lirp_result = lirp_method->Build();

                if (lirp_result)
                {
                    auto estimated_pose = GetDataPtr<RelativePose>(lirp_result);
                    if (estimated_pose)
                    {
                        std::cout << "✅ LiRP方法运行成功" << std::endl;
                        std::cout << "   估计的旋转矩阵行列式: "
                                  << estimated_pose->Rij.determinant() << std::endl;
                        std::cout << "   估计的平移向量模长: "
                                  << estimated_pose->tij.norm() << std::endl;
                    }
                }
                else
                {
                    std::cout << "⚠️  LiRP方法运行失败" << std::endl;
                }
            }
        }
        catch (const std::exception &e)
        {
            std::cout << "⚠️  LiRP方法测试异常: " << e.what() << std::endl;
        }
    }

    // 性能基准测试
    TEST_F(TestOpenGVSimulator, PerformanceBenchmark)
    {
        std::cout << "\n⚡ 性能基准测试" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        std::vector<size_t> point_counts = {50, 100, 200, 500};

        std::cout << std::left << std::setw(12) << "特征点数"
                  << std::setw(15) << "仿真耗时(ms)"
                  << std::setw(15) << "数据大小(接口)"
                  << std::setw(15) << "内存使用" << std::endl;
        std::cout << std::string(60, '-') << std::endl;

        for (size_t num_points : point_counts)
        {
            // 设置特征点数量
            MethodOptions options{
                {"num_points", std::to_string(num_points)},
                {"log_level", "0"} // 静默模式
            };
            simulator_->SetMethodOptions(options);

            // 测量仿真时间
            auto start_time = std::chrono::high_resolution_clock::now();
            auto result = simulator_->Build();
            auto end_time = std::chrono::high_resolution_clock::now();

            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                                end_time - start_time)
                                .count() /
                            1000.0;

            // 分析数据包大小
            auto package = std::dynamic_pointer_cast<DataPackage>(result);
            size_t interface_count = package ? package->GetPackage().size() : 0;

            std::cout << std::left << std::setw(12) << num_points
                      << std::setw(15) << std::fixed << std::setprecision(2) << duration
                      << std::setw(15) << interface_count
                      << std::setw(15) << "~MB" << std::endl;

            EXPECT_TRUE(result != nullptr) << "仿真失败，特征点数量: " << num_points;
            EXPECT_LT(duration, 1000.0) << "仿真时间过长，特征点数量: " << num_points;
        }

        std::cout << std::string(60, '-') << std::endl;
        std::cout << "📈 性能测试完成" << std::endl;
    }

} // namespace