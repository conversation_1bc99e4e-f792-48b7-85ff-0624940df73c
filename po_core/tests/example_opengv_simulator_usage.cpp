/**
 * @file example_opengv_simulator_usage.cpp
 * @brief OpenGV双视图仿真器使用示例
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <po_core.hpp>
#include <iostream>
#include <chrono>
#include <iomanip>

using namespace PoSDK;
using namespace Interface;

/**
 * @brief 演示如何使用OpenGV仿真器为不同的双视图估计方法提供数据
 */
void DemonstrateOpenGVSimulatorUsage()
{
    std::cout << "\n🚀 OpenGV双视图仿真器使用示例" << std::endl;
    std::cout << std::string(80, '=') << std::endl;

    // ========================================
    // 步骤1: 创建并配置OpenGV仿真器
    // ========================================
    std::cout << "\n📋 步骤1: 创建并配置OpenGV仿真器" << std::endl;
    std::cout << std::string(50, '-') << std::endl;

    auto simulator = std::dynamic_pointer_cast<MethodPreset>(
        FactoryMethod::Create("opengv_simulator"));

    if (!simulator)
    {
        std::cerr << "❌ 无法创建OpenGV仿真器" << std::endl;
        return;
    }

    // 配置仿真参数
    MethodOptions simulator_options{
        {"ProfileCommit", "双视图估计器性能对比测试"},
        {"max_parallax", "2.0"},      // 较大的基线
        {"max_rotation", "0.4"},      // 适中的旋转
        {"num_points", "200"},        // 充足的特征点
        {"noise_level", "0.5"},       // 添加一些噪声
        {"outlier_fraction", "0.15"}, // 15%外点
        {"focal_length", "800.0"},
        {"image_width", "1024.0"},
        {"image_height", "768.0"},
        {"random_seed", "42"},
        {"log_level", "1"}};
    simulator->SetMethodOptions(simulator_options);

    std::cout << "✅ 仿真器配置完成" << std::endl;

    // ========================================
    // 步骤2: 运行仿真器生成数据包
    // ========================================
    std::cout << "\n📦 步骤2: 运行仿真器生成数据包" << std::endl;
    std::cout << std::string(50, '-') << std::endl;

    auto start_time = std::chrono::high_resolution_clock::now();
    auto simulator_data = simulator->Build();
    auto end_time = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                        end_time - start_time)
                        .count() /
                    1000.0;

    if (!simulator_data)
    {
        std::cerr << "❌ 仿真器运行失败" << std::endl;
        return;
    }

    auto package = std::dynamic_pointer_cast<DataPackage>(simulator_data);
    if (!package)
    {
        std::cerr << "❌ 返回的数据不是DataPackage类型" << std::endl;
        return;
    }

    std::cout << "✅ 仿真器运行成功，耗时: " << std::fixed << std::setprecision(2)
              << duration << " ms" << std::endl;
    std::cout << "📊 生成的数据接口数量: " << package->GetPackageInfo().size() << std::endl;

    // ========================================
    // 步骤3: 测试不同的双视图估计方法
    // ========================================
    std::cout << "\n🔧 步骤3: 测试不同的双视图估计方法" << std::endl;
    std::cout << std::string(50, '-') << std::endl;

    // 存储结果的结构体
    struct EstimationResult
    {
        std::string method_name;
        bool success = false;
        double rotation_error = 0.0;
        double translation_error = 0.0;
        double runtime_ms = 0.0;
    };

    std::vector<EstimationResult> results;

    // 获取真值数据用于误差计算
    auto gt_data = package->GetData("ground_truth");
    auto gt_pose = GetDataPtr<RelativePose>(gt_data);

    if (!gt_pose)
    {
        std::cerr << "❌ 无法获取真值数据" << std::endl;
        return;
    }

    std::cout << "🎯 真值相对位姿已获取" << std::endl;

    // ----------------------------------------
    // 测试 method_LiRP
    // ----------------------------------------
    std::cout << "\n🧮 测试 method_LiRP..." << std::endl;
    EstimationResult lirp_result{"method_LiRP"};

    try
    {
        auto lirp_method = std::dynamic_pointer_cast<MethodPreset>(
            FactoryMethod::Create("method_LiRP"));

        if (lirp_method)
        {
            // 获取LiRP专用数据
            auto lirp_data = package->GetData("method_LiRP");
            if (lirp_data)
            {
                lirp_method->SetRequiredData(lirp_data);

                // 设置LiRP选项
                MethodOptions lirp_options{
                    {"compute_mode", "false"},
                    {"use_opt_mode", "false"},
                    {"identify_mode", "ppo"},
                    {"view_i", "0"},
                    {"view_j", "1"},
                    {"debug_output", "false"}};
                lirp_method->SetMethodOptions(lirp_options);

                // 运行并计时
                auto lirp_start = std::chrono::high_resolution_clock::now();
                auto lirp_output = lirp_method->Build();
                auto lirp_end = std::chrono::high_resolution_clock::now();

                lirp_result.runtime_ms = std::chrono::duration_cast<std::chrono::microseconds>(
                                             lirp_end - lirp_start)
                                             .count() /
                                         1000.0;

                if (lirp_output)
                {
                    auto estimated_pose = GetDataPtr<RelativePose>(lirp_output);
                    if (estimated_pose)
                    {
                        lirp_result.success = true;

                        // 计算误差
                        lirp_result.rotation_error = (estimated_pose->Rij - gt_pose->Rij).norm();
                        lirp_result.translation_error = (estimated_pose->tij.normalized() -
                                                         gt_pose->tij.normalized())
                                                            .norm();

                        std::cout << "   ✅ LiRP方法运行成功" << std::endl;
                        std::cout << "   📐 旋转误差: " << lirp_result.rotation_error << std::endl;
                        std::cout << "   📏 平移误差: " << lirp_result.translation_error << std::endl;
                    }
                }
                else
                {
                    std::cout << "   ❌ LiRP方法返回空结果" << std::endl;
                }
            }
            else
            {
                std::cout << "   ❌ 无法获取LiRP数据" << std::endl;
            }
        }
        else
        {
            std::cout << "   ❌ 无法创建LiRP方法" << std::endl;
        }
    }
    catch (const std::exception &e)
    {
        std::cout << "   ❌ LiRP方法异常: " << e.what() << std::endl;
    }

    results.push_back(lirp_result);

    // ----------------------------------------
    // 测试 opengv_model_estimator
    // ----------------------------------------
    std::cout << "\n🔍 测试 opengv_model_estimator..." << std::endl;
    EstimationResult opengv_result{"opengv_model_estimator"};

    try
    {
        auto opengv_method = std::dynamic_pointer_cast<MethodPreset>(
            FactoryMethod::Create("opengv_model_estimator"));

        if (opengv_method)
        {
            // 获取OpenGV专用数据
            auto opengv_data = package->GetData("opengv_model_estimator");
            if (opengv_data)
            {
                opengv_method->SetRequiredData(opengv_data);

                // 设置OpenGV选项
                MethodOptions opengv_options{
                    {"ProfileCommit", "OpenGV仿真器测试"},
                    {"algorithm", "fivept_stewenius"}, // 使用Stewenius五点法
                    {"view_i", "0"},
                    {"view_j", "1"},
                    {"debug_output", "false"}};
                opengv_method->SetMethodOptions(opengv_options);

                // 运行并计时
                auto opengv_start = std::chrono::high_resolution_clock::now();
                auto opengv_output = opengv_method->Build();
                auto opengv_end = std::chrono::high_resolution_clock::now();

                opengv_result.runtime_ms = std::chrono::duration_cast<std::chrono::microseconds>(
                                               opengv_end - opengv_start)
                                               .count() /
                                           1000.0;

                if (opengv_output)
                {
                    auto estimated_pose = GetDataPtr<RelativePose>(opengv_output);
                    if (estimated_pose)
                    {
                        opengv_result.success = true;

                        // 计算误差
                        opengv_result.rotation_error = (estimated_pose->Rij - gt_pose->Rij).norm();
                        opengv_result.translation_error = (estimated_pose->tij.normalized() -
                                                           gt_pose->tij.normalized())
                                                              .norm();

                        std::cout << "   ✅ OpenGV方法运行成功" << std::endl;
                        std::cout << "   📐 旋转误差: " << opengv_result.rotation_error << std::endl;
                        std::cout << "   📏 平移误差: " << opengv_result.translation_error << std::endl;
                    }
                }
                else
                {
                    std::cout << "   ❌ OpenGV方法返回空结果" << std::endl;
                }
            }
            else
            {
                std::cout << "   ❌ 无法获取OpenGV数据" << std::endl;
            }
        }
        else
        {
            std::cout << "   ❌ 无法创建OpenGV方法" << std::endl;
        }
    }
    catch (const std::exception &e)
    {
        std::cout << "   ❌ OpenGV方法异常: " << e.what() << std::endl;
    }

    results.push_back(opengv_result);

    // ========================================
    // 步骤4: 汇总和分析结果
    // ========================================
    std::cout << "\n📊 步骤4: 汇总和分析结果" << std::endl;
    std::cout << std::string(50, '-') << std::endl;

    std::cout << "\n🏆 双视图估计器性能对比：" << std::endl;
    std::cout << std::string(80, '=') << std::endl;

    std::cout << std::left << std::setw(25) << "方法名称"
              << std::setw(10) << "状态"
              << std::setw(15) << "旋转误差"
              << std::setw(15) << "平移误差"
              << std::setw(15) << "运行时间(ms)" << std::endl;
    std::cout << std::string(80, '-') << std::endl;

    for (const auto &result : results)
    {
        std::cout << std::left << std::setw(25) << result.method_name
                  << std::setw(10) << (result.success ? "✅" : "❌")
                  << std::fixed << std::setprecision(6)
                  << std::setw(15) << (result.success ? std::to_string(result.rotation_error) : "N/A")
                  << std::setw(15) << (result.success ? std::to_string(result.translation_error) : "N/A")
                  << std::setprecision(2)
                  << std::setw(15) << result.runtime_ms << std::endl;
    }

    std::cout << std::string(80, '=') << std::endl;

    // 找出表现最好的方法
    auto best_method = std::min_element(results.begin(), results.end(),
                                        [](const auto &a, const auto &b)
                                        {
                                            if (!a.success)
                                                return false;
                                            if (!b.success)
                                                return true;
                                            return (a.rotation_error + a.translation_error) < (b.rotation_error + b.translation_error);
                                        });

    if (best_method != results.end() && best_method->success)
    {
        std::cout << "\n🏅 表现最佳方法: " << best_method->method_name << std::endl;
        std::cout << "   综合误差: " << (best_method->rotation_error + best_method->translation_error) << std::endl;
        std::cout << "   运行时间: " << best_method->runtime_ms << " ms" << std::endl;
    }

    // ========================================
    // 步骤5: 使用技巧和建议
    // ========================================
    std::cout << "\n💡 步骤5: 使用技巧和建议" << std::endl;
    std::cout << std::string(50, '-') << std::endl;

    std::cout << "🔧 OpenGV仿真器使用技巧：" << std::endl;
    std::cout << "  1. 通过调整 outlier_fraction 测试鲁棒性" << std::endl;
    std::cout << "  2. 通过调整 noise_level 测试噪声敏感性" << std::endl;
    std::cout << "  3. 通过调整 max_parallax 测试不同基线长度" << std::endl;
    std::cout << "  4. 通过固定 random_seed 确保实验可重复" << std::endl;
    std::cout << "  5. 使用 ProfileCommit 进行性能分析和记录" << std::endl;

    std::cout << "\n📝 代码模板：" << std::endl;
    std::cout << "```cpp" << std::endl;
    std::cout << "// 1. 创建仿真器" << std::endl;
    std::cout << "auto simulator = FactoryMethod::Create(\"opengv_simulator\");" << std::endl;
    std::cout << "// 2. 设置参数" << std::endl;
    std::cout << "simulator->SetMethodOptions(options);" << std::endl;
    std::cout << "// 3. 生成数据" << std::endl;
    std::cout << "auto data_package = simulator->Build();" << std::endl;
    std::cout << "// 4. 获取方法专用数据" << std::endl;
    std::cout << "auto method_data = data_package->GetData(method->GetType());" << std::endl;
    std::cout << "// 5. 设置并运行方法" << std::endl;
    std::cout << "method->SetRequiredData(method_data);" << std::endl;
    std::cout << "auto result = method->Build();" << std::endl;
    std::cout << "```" << std::endl;

    std::cout << "\n🎉 OpenGV双视图仿真器使用示例完成！" << std::endl;
}

// 简化的main函数用于独立运行示例
#ifndef RUNNING_TESTS
int main()
{
    try
    {
        DemonstrateOpenGVSimulatorUsage();
        return 0;
    }
    catch (const std::exception &e)
    {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
}
#endif