/**
 * @file test_lirp_method.cpp
 * @brief 测试LiRP相对位姿估计方法和新的评估系统
 * @copyright Copyright (c) 2024 Qi Cai
 */

#include <gtest/gtest.h>
#include <po_core.hpp>
#include <filesystem>
#include <chrono>
#include <ctime>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <tuple>
#include <opengv/relative_pose/methods.hpp>
#include <opengv/relative_pose/CentralRelativeAdapter.hpp>
#include "random_generators.hpp"
#include "experiment_helpers.hpp"

// 显式包含DataRelativePoses头文件
#include "data/data_relative_poses.hpp"

namespace
{
    using namespace PoSDK;
    using namespace opengv;

    class TestLiRPMethod : public ::testing::Test
    {
    protected:
        // 场景配置结构体
        struct SceneConfig
        {
            std::string config_name;       // 配置名称，用作ProfileCommit
            double max_parallax = 1.0;     // 平移幅度
            double max_rotation = 0.5;     // 旋转幅度(弧度)
            size_t num_points = 100;       // 特征点数量
            double noise = 0.0;            // 噪声水平
            double outlier_fraction = 0.0; // 外点比例
        };

        // 计算旋转误差（使用Frobenius范数）
        double ComputeRotationError(const opengv::rotation_t &R_estimated,
                                    const opengv::rotation_t &R_ground_truth)
        {
            return (R_estimated - R_ground_truth).norm();
        }

        // 计算平移误差（方向误差，因为尺度不确定）
        double ComputeTranslationError(const opengv::translation_t &t_estimated,
                                       const opengv::translation_t &t_ground_truth)
        {
            return (t_estimated.normalized() - t_ground_truth.normalized()).norm();
        }

        // 新增：计算角度形式的旋转误差（与ComputeErrors方法对应）
        double ComputeRotationErrorAngle(const opengv::rotation_t &R_estimated,
                                         const opengv::rotation_t &R_ground_truth)
        {
            Matrix3d R_error = R_estimated * R_ground_truth.transpose();
            Eigen::AngleAxisd angle_axis(R_error);
            return std::abs(angle_axis.angle()) * 180.0 / M_PI;
        }

        // 新增：计算角度形式的平移误差（与ComputeErrors方法对应）
        double ComputeTranslationErrorAngle(const opengv::translation_t &t_estimated,
                                            const opengv::translation_t &t_ground_truth)
        {
            Vector3d t1_normalized = t_estimated.normalized();
            Vector3d t2_normalized = t_ground_truth.normalized();
            double dot_product = t1_normalized.dot(t2_normalized);
            dot_product = std::max(-1.0, std::min(1.0, dot_product));

            if (std::abs(dot_product - 1.0) < 1e-15)
            {
                return 0.0;
            }
            else if (std::abs(dot_product + 1.0) < 1e-15)
            {
                return 180.0;
            }
            else
            {
                return std::acos(dot_product) * 180.0 / M_PI;
            }
        }

        // 比较两种误差计算方法
        void CompareErrorMethods(const RelativePose &estimated_pose, const RelativePose &gt_pose_obj)
        {
            if (log_level_ > 1)
            {
                // 方法1：直接计算（范数方法）
                double R_error_norm = ComputeRotationError(estimated_pose.Rij, ground_truth_R_);
                double t_error_norm = ComputeTranslationError(estimated_pose.tij, ground_truth_t_);

                // 方法2：角度方法（对应ComputeErrors）
                double R_error_angle = ComputeRotationErrorAngle(estimated_pose.Rij, ground_truth_R_);
                double t_error_angle = ComputeTranslationErrorAngle(estimated_pose.tij, ground_truth_t_);

                // 方法3：使用RelativePose的ComputeErrors方法
                double R_error_method, t_error_method;
                estimated_pose.ComputeErrors(gt_pose_obj, R_error_method, t_error_method);

                std::cout << "\n--- Error Method Comparison ---" << std::endl;
                std::cout << std::fixed << std::setprecision(10);
                std::cout << "Rotation Error:" << std::endl;
                std::cout << "  Norm method:     " << R_error_norm << std::endl;
                std::cout << "  Angle method:    " << R_error_angle << " degrees" << std::endl;
                std::cout << "  ComputeErrors:   " << R_error_method << " degrees" << std::endl;
                std::cout << "Translation Error:" << std::endl;
                std::cout << "  Norm method:     " << t_error_norm << std::endl;
                std::cout << "  Angle method:    " << t_error_angle << " degrees" << std::endl;
                std::cout << "  ComputeErrors:   " << t_error_method << " degrees" << std::endl;
                std::cout << "--------------------------------" << std::endl;
            }
        }

        void SetUp() override
        {
            // 清空之前的评估数据
            Interface::EvaluatorManager::ClearAllEvaluators();

            // 初始化LiRP方法
            method_ = std::dynamic_pointer_cast<MethodPreset>(
                FactoryMethod::Create("method_LiRP"));
            ASSERT_TRUE(method_ != nullptr);
        }

        void TearDown() override
        {
            // 测试完成后，展示评估结果
            ShowEvaluationResults();
        }

        // 生成场景数据并创建真值
        DataPtr GenerateSceneAndGroundTruth(const SceneConfig &config)
        {
            // 初始化随机种子
            initializeRandomSeed();

            // 生成相对位姿
            translation_t position1 = Eigen::Vector3d::Zero();
            rotation_t rotation1 = Eigen::Matrix3d::Identity();
            translation_t position2 = generateRandomTranslation(config.max_parallax);
            rotation_t rotation2 = generateRandomRotation(config.max_rotation);

            // 提取真值相对位姿
            extractRelativePose(
                position1, position2,
                rotation1, rotation2,
                ground_truth_t_, ground_truth_R_,
                true); // normalize translation

            // 使用OpenGV的辅助函数生成中心相机系统
            translations_t camOffsets;
            rotations_t camRotations;
            generateCentralCameraSystem(camOffsets, camRotations);

            // 生成2D-2D对应点
            bearingVectors_t bearingVectors1, bearingVectors2;
            std::vector<int> camCorrespondences1, camCorrespondences2;
            Eigen::MatrixXd gt(3, config.num_points);

            // 使用OpenGV的函数生成对应点
            generateRandom2D2DCorrespondences(
                position1, rotation1,
                position2, rotation2,
                camOffsets, camRotations,
                config.num_points,
                config.noise,
                config.outlier_fraction,
                bearingVectors1, bearingVectors2,
                camCorrespondences1, camCorrespondences2,
                gt);

            // 为LiRP方法准备数据
            BearingPairs bearing_pairs;
            ConvertToBearingPairs(bearingVectors1, bearingVectors2, bearing_pairs);

            // 创建BearingPairs数据样本
            auto sample_data = std::make_shared<DataSample<BearingPairs>>(bearing_pairs);
            method_->SetRequiredData(sample_data);

            // 设置LiRP特定的选项，启用评估
            MethodOptions lirp_options{
                {"compute_mode", "false"},             // 使用改进模式
                {"use_opt_mode", "false"},             // 使用标准ProcessEvec2
                {"identify_mode", "PPO"},              // 使用PPO残差函数
                {"debug_output", "false"},             // 关闭调试输出
                {"enable_evaluator", "true"},          // 启用评估
                {"enable_profiling", "false"},         // 关闭性能分析
                {"ProfileCommit", config.config_name}, // 设置配置提交名称
                {"view_i", "0"},
                {"view_j", "1"}};
            method_->SetMethodOptions(lirp_options);

            // 创建真值相对位姿数据
            RelativePoses gt_poses;
            RelativePose gt_pose;
            gt_pose.i = 0;
            gt_pose.j = 1;
            gt_pose.Rij = ground_truth_R_;
            gt_pose.tij = ground_truth_t_;
            gt_pose.weight = 1.0;
            gt_poses.push_back(gt_pose);

            // 创建真值数据对象 - 使用make_shared创建智能指针
            DataPtr gt_data = std::make_shared<DataMap<RelativePose>>(gt_pose);

            // 可选：打印实验特征
            if (log_level_ > 0)
            {
                std::cout << std::setprecision(16) << ground_truth_R_ << std::endl;
                std::cout << std::setprecision(16) << ground_truth_t_.transpose() << std::endl;
                // std::cout << "\n=== Test Configuration: " << config.config_name << " ===\n";
                // std::cout << "Points: " << config.num_points
                //           << ", Noise: " << config.noise
                //           << ", Outliers: " << config.outlier_fraction << std::endl;
                // printExperimentCharacteristics(position2, rotation2,
                //                                config.noise, config.outlier_fraction);
            }

            return gt_data;
        }

        // 运行单个测试场景
        void RunScenario(const SceneConfig &config)
        {
            std::cout << "\n=== Running scenario: " << config.config_name
                      << " (repeating " << num_repeated_tests_ << " times) ===" << std::endl;

            // 添加EvalCommit格式调试输出
            std::cout << "[DEBUG] 当前EvalCommit格式: '" << config.config_name << "'" << std::endl;

            std::vector<double> rotation_errors, translation_errors, runtimes;

            // 重复运行测试以生成更多评估数据
            for (int repeat = 0; repeat < num_repeated_tests_; ++repeat)
            {
                if (log_level_ > 1)
                {
                    std::cout << "\n--- Repeat " << (repeat + 1) << "/" << num_repeated_tests_
                              << " for " << config.config_name << " ---" << std::endl;
                }

                // 生成场景数据和真值
                auto gt_data = GenerateSceneAndGroundTruth(config);

                // 设置真值数据用于评估
                auto profiler = std::dynamic_pointer_cast<Interface::MethodPresetProfiler>(method_);
                if (profiler)
                {
                    profiler->SetGTData(gt_data);
                }

                // 运行算法并计时
                auto start_time = std::chrono::high_resolution_clock::now();
                auto result = method_->Build();
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
                                    end_time - start_time)
                                    .count() /
                                1000.0;

                // 验证结果
                ASSERT_TRUE(result != nullptr) << "LiRP method failed to produce a result for config: "
                                               << config.config_name << ", repeat: " << (repeat + 1);

                auto pose = GetDataPtr<RelativePose>(result);
                ASSERT_TRUE(pose != nullptr) << "Failed to get RelativePose from result for config: "
                                             << config.config_name << ", repeat: " << (repeat + 1);

                // 计算误差
                double R_error = ComputeRotationError(pose->Rij, ground_truth_R_);
                double t_error = ComputeTranslationError(pose->tij, ground_truth_t_);

                // 收集统计数据
                rotation_errors.push_back(R_error);
                translation_errors.push_back(t_error);
                runtimes.push_back(duration);

                // 详细输出（仅在高日志级别时）
                if (log_level_ > 1)
                {
                    std::cout << "  Repeat " << (repeat + 1) << " - R_error: " << R_error
                              << ", t_error: " << t_error << ", runtime: " << duration << " ms" << std::endl;
                }

                // 验证结果合理性（放宽一些以适应不同配置）
                EXPECT_LT(R_error, 1.0) << "Rotation error too large for config: " << config.config_name
                                        << ", repeat: " << (repeat + 1);
                EXPECT_LT(t_error, 1.0) << "Translation error too large for config: " << config.config_name
                                        << ", repeat: " << (repeat + 1);

                // 比较两种误差计算方法（为演示创建真值RelativePose对象）
                if (log_level_ > 1)
                {
                    RelativePose gt_pose_obj;
                    gt_pose_obj.i = 0;
                    gt_pose_obj.j = 1;
                    gt_pose_obj.Rij = ground_truth_R_;
                    gt_pose_obj.tij = ground_truth_t_;
                    gt_pose_obj.weight = 1.0;

                    CompareErrorMethods(*pose, gt_pose_obj);
                }
            }

            // 计算并输出统计信息
            if (log_level_ > 0)
            {
                auto calc_stats = [](const std::vector<double> &values)
                {
                    if (values.empty())
                        return std::make_tuple(0.0, 0.0, 0.0, 0.0);

                    double sum = std::accumulate(values.begin(), values.end(), 0.0);
                    double mean = sum / values.size();

                    auto sorted_values = values;
                    std::sort(sorted_values.begin(), sorted_values.end());
                    double median = sorted_values.size() % 2 == 0 ? (sorted_values[sorted_values.size() / 2 - 1] + sorted_values[sorted_values.size() / 2]) / 2.0 : sorted_values[sorted_values.size() / 2];

                    double min_val = *std::min_element(values.begin(), values.end());
                    double max_val = *std::max_element(values.begin(), values.end());

                    return std::make_tuple(mean, median, min_val, max_val);
                };

                auto [r_mean, r_median, r_min, r_max] = calc_stats(rotation_errors);
                auto [t_mean, t_median, t_min, t_max] = calc_stats(translation_errors);
                auto [rt_mean, rt_median, rt_min, rt_max] = calc_stats(runtimes);

                std::cout << "\n=== Summary for " << config.config_name << " (" << num_repeated_tests_ << " runs) ===" << std::endl;
                std::cout << "Rotation Error  - Mean: " << std::fixed << std::setprecision(10) << r_mean
                          << ", Median: " << r_median << ", Range: [" << r_min << ", " << r_max << "]" << std::endl;
                std::cout << "Translation Error - Mean: " << t_mean
                          << ", Median: " << t_median << ", Range: [" << t_min << ", " << t_max << "]" << std::endl;
                std::cout << "Runtime (ms)    - Mean: " << std::fixed << std::setprecision(3) << rt_mean
                          << ", Median: " << rt_median << ", Range: [" << rt_min << ", " << rt_max << "]" << std::endl;
                std::cout << std::string(60, '-') << std::endl;
            }
        }

        // 显示评估结果和测试评估管理器功能
        void ShowEvaluationResults()
        {
            if (log_level_ > 0)
            {
                std::cout << "\n"
                          << std::string(60, '=') << std::endl;
                std::cout << "EVALUATION SYSTEM TEST RESULTS" << std::endl;
                std::cout << std::string(60, '=') << std::endl;

                // 1. 打印所有评估报告
                std::cout << "\n1. All Evaluation Reports:" << std::endl;
                Interface::EvaluatorManager::PrintAllEvaluationReports();

                // 2. 打印算法对比（如果有多个指标）
                auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
                for (const auto &eval_type : eval_types)
                {
                    auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
                    for (const auto &algorithm : algorithms)
                    {
                        auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                        for (const auto &metric : metrics)
                        {
                            std::cout << "\n2. Algorithm Comparison for " << eval_type << "::" << metric << ":" << std::endl;
                            Interface::EvaluatorManager::PrintAlgorithmComparison(eval_type, metric);
                        }
                    }
                }

                // 3. 导出CSV文件测试 - 生成基于测试名称和时间戳的唯一目录名
                std::cout << "\n3. Testing CSV Export Functions:" << std::endl;

                // 获取当前测试信息
                const ::testing::TestInfo *const test_info =
                    ::testing::UnitTest::GetInstance()->current_test_info();
                std::string test_name = std::string(test_info->test_case_name()) + "_" +
                                        std::string(test_info->name());

                // 添加时间戳确保唯一性
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                              now.time_since_epoch()) %
                          1000;

                std::ostringstream timestamp;
                timestamp << std::put_time(std::localtime(&time_t), "%Y%m%d");

                std::string unique_dir_name = test_name + "_" + timestamp.str();

                TestCSVExport(unique_dir_name);

                std::cout << std::string(60, '=') << std::endl;
            }
        }

        // 测试CSV导出功能
        void TestCSVExport(const std::string &output_dir_name)
        {
            auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();

            for (const auto &eval_type : eval_types)
            {
                // 创建测试输出目录 - 使用传入的唯一目录名
                std::filesystem::path test_output_dir = output_dir_name;
                std::filesystem::create_directories(test_output_dir);

                std::cout << "\n=== 测试CSV导出功能（自动智能解析）===" << std::endl;

                auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);

                // // 测试导出所有指标对比（现在会自动使用智能解析）
                // bool success = Interface::EvaluatorManager::ExportAllMetricsToCSV(eval_type, test_output_dir);
                // std::cout << "Export all metrics for " << eval_type << " to " << test_output_dir << ": "
                //           << (success ? "SUCCESS" : "FAILED") << std::endl;

                // 测试导出详细统计
                for (const auto &algorithm : algorithms)
                {
                    std::filesystem::path detailed_path = test_output_dir / (eval_type + "_" + algorithm + "_detailed.csv");
                    bool detail_success = Interface::EvaluatorManager::ExportDetailedStatsToCSV(eval_type, algorithm, detailed_path);
                    std::cout << "Export detailed stats for " << eval_type << "::" << algorithm << ": "
                              << (detail_success ? "SUCCESS" : "FAILED") << std::endl;
                }

                // 测试单个指标导出（现在会自动使用智能解析）
                if (!algorithms.empty())
                {
                    auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithms[0]);
                    for (const auto &metric : metrics)
                    {
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的对比表格..." << std::endl;

                        // 原有的单统计类型导出
                        std::filesystem::path single_metric_path = test_output_dir / (eval_type + "_" + metric + "_comparison.csv");
                        bool single_success = Interface::EvaluatorManager::ExportAlgorithmComparisonToCSV(
                            eval_type, metric, single_metric_path, "mean");
                        std::cout << "Export single metric " << eval_type << "::" << metric << ": "
                                  << (single_success ? "SUCCESS" : "FAILED") << std::endl;

                        // 新增：所有统计类型导出（统一智能解析格式）
                        std::cout << "\n[智能解析] 导出指标 " << metric << " 的所有统计类型（统一智能解析格式）..." << std::endl;
                        std::filesystem::path all_stats_path = test_output_dir / (eval_type + "_" + metric + "_ALL_STATS.csv");
                        bool all_stats_success = Interface::EvaluatorManager::ExportMetricAllStatsToCSV(
                            eval_type, metric, all_stats_path);
                        std::cout << "Export all stats for " << eval_type << "::" << metric << ": "
                                  << (all_stats_success ? "SUCCESS" : "FAILED") << std::endl;
                    }
                }
            }
        }

        Interface::MethodPresetPtr method_;
        rotation_t ground_truth_R_;
        translation_t ground_truth_t_;

        // 日志等级 (0: 不输出, 1: 输出基本信息, 2: 输出详细)
        int log_level_ = 2; // 设置为2以启用详细比较

        // 重复测试次数 - 用于更系统地测试评估器功能
        int num_repeated_tests_ = 10; // 减少到3次以便观察误差计算差异

        // 添加一个辅助函数用于转换数据格式
        void ConvertToBearingPairs(const bearingVectors_t &bearingVectors1,
                                   const bearingVectors_t &bearingVectors2,
                                   BearingPairs &bearing_pairs)
        {
            bearing_pairs.clear();
            const size_t num_points = bearingVectors1.size();
            bearing_pairs.reserve(num_points);

            for (size_t i = 0; i < num_points; i++)
            {
                Eigen::Matrix<double, 6, 1> match_pair;
                match_pair.head<3>() = bearingVectors1[i];
                match_pair.tail<3>() = bearingVectors2[i];
                bearing_pairs.push_back(match_pair);
            }
        }
    };

    // 基本测试用例（无噪声、无外点）
    TEST_F(TestLiRPMethod, BasicTest)
    {
        SceneConfig config;
        config.config_name = "basic_test";
        config.max_parallax = 1.0;
        config.max_rotation = 0.3;
        config.num_points = 50;
        config.noise = 0.0;
        config.outlier_fraction = 0.0;

        RunScenario(config);
    }

    // 噪声水平测试
    TEST_F(TestLiRPMethod, NoiseLevel_Tests)
    {
        std::vector<double> noise_levels = {0.0, 0.001, 0.005, 0.01};

        for (double noise : noise_levels)
        {
            SceneConfig config;
            // 使用更清晰的数值格式，便于智能解析
            std::ostringstream oss;
            oss << std::fixed << std::setprecision(3) << noise;
            config.config_name = "noise_" + oss.str();
            config.max_parallax = 1.0;
            config.max_rotation = 0.3;
            config.num_points = 100;
            config.noise = noise;
            config.outlier_fraction = 0.0;

            RunScenario(config);
        }
    }

    // 特征点数量测试
    TEST_F(TestLiRPMethod, PointCount_Tests)
    {
        std::vector<size_t> point_counts = {20, 50, 100, 200};

        for (size_t point_count : point_counts)
        {
            SceneConfig config;
            // 整数格式保持简洁
            config.config_name = "points_" + std::to_string(point_count);
            config.max_parallax = 1.0;
            config.max_rotation = 0.3;
            config.num_points = point_count;
            config.noise = 0.001;
            config.outlier_fraction = 0;

            RunScenario(config);
        }
    }

    // 视差测试
    TEST_F(TestLiRPMethod, Parallax_Tests)
    {
        std::vector<double> parallax_levels = {0.5, 1.0, 1.5, 2.0};

        for (double parallax : parallax_levels)
        {
            SceneConfig config;
            // 使用精确的浮点格式
            std::ostringstream oss;
            oss << std::fixed << std::setprecision(1) << parallax;
            config.config_name = "parallax_" + oss.str();
            config.max_parallax = parallax;
            config.max_rotation = 0.3;
            config.num_points = 100;
            config.noise = 0.001;
            config.outlier_fraction = 0.0;

            RunScenario(config);
        }
    }

    // 评估器管理功能测试
    TEST_F(TestLiRPMethod, EvaluatorManager_FunctionalityTest)
    {
        // 运行几个基本场景来生成数据
        std::vector<SceneConfig> test_configs = {
            {"func_test_1", 1.0, 0.3, 50, 0.0, 0.0},
            {"func_test_2", 1.0, 0.3, 50, 0.001, 0},
            {"func_test_3", 1.0, 0.3, 50, 0.005, 0}};

        for (const auto &config : test_configs)
        {
            RunScenario(config);
        }

        // 测试清理功能
        std::cout << "\nTesting evaluator management functions..." << std::endl;

        // 获取所有评估类型
        auto eval_types = Interface::EvaluatorManager::GetAllEvaluationTypes();
        EXPECT_FALSE(eval_types.empty()) << "Should have evaluation types after running scenarios";

        // 测试获取算法列表
        for (const auto &eval_type : eval_types)
        {
            auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_type);
            EXPECT_FALSE(algorithms.empty()) << "Should have algorithms for type: " << eval_type;

            for (const auto &algorithm : algorithms)
            {
                auto metrics = Interface::EvaluatorManager::GetAllMetrics(eval_type, algorithm);
                EXPECT_FALSE(metrics.empty()) << "Should have metrics for " << eval_type << "::" << algorithm;

                for (const auto &metric : metrics)
                {
                    auto commits = Interface::EvaluatorManager::GetAllEvalCommits(eval_type, algorithm, metric);
                    EXPECT_FALSE(commits.empty()) << "Should have commits for " << eval_type << "::" << algorithm << "::" << metric;
                }
            }
        }

        // 测试清理算法
        if (!eval_types.empty())
        {
            auto algorithms = Interface::EvaluatorManager::GetAllAlgorithms(eval_types[0]);
            if (!algorithms.empty())
            {
                bool clear_success = Interface::EvaluatorManager::ClearAlgorithm(eval_types[0], algorithms[0]);
                EXPECT_TRUE(clear_success) << "Should successfully clear algorithm";
            }
        }

        std::cout << "Evaluator management functionality tests completed." << std::endl;
    }

} // namespace
