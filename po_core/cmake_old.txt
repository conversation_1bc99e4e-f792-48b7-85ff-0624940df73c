
# ------------------------------------------------------------------------------
# Abseil库配置
# ------------------------------------------------------------------------------

# 查找Abseil库 - 根据平台设置搜索路径并检查版本
set(ABSEIL_MIN_VERSION "20240722")
set(ABSEIL_SEARCH_PATHS "")

# 根据平台设置搜索路径
if(APPLE)
    # macOS可能的Abseil库位置
    list(APPEND ABSEIL_SEARCH_PATHS 
        "/usr/local/lib/cmake/absl"
        "/opt/homebrew/lib/cmake/absl"
    )
elseif(UNIX AND NOT APPLE)
    # Linux可能的Abseil库位置
    list(APPEND ABSEIL_SEARCH_PATHS 
        "/usr/local/lib/cmake/absl"  # 优先搜索/usr/local
    )
    # 之后才搜索系统路径
    list(APPEND ABSEIL_SEARCH_PATHS
        "/usr/lib/${CMAKE_SYSTEM_PROCESSOR}-linux-gnu/cmake/absl"
    )
endif()

# 添加自定义搜索路径
if(DEFINED ENV{ABSEIL_DIR})
    list(APPEND ABSEIL_SEARCH_PATHS "$ENV{ABSEIL_DIR}")
endif()

# 先尝试直接在/usr/local查找
find_package(absl CONFIG PATHS "/usr/local/lib/cmake/absl" NO_DEFAULT_PATH)

# 如果找不到，再尝试使用搜索路径列表
if(NOT absl_FOUND)
    # 尝试查找Abseil
    find_package(absl CONFIG PATHS ${ABSEIL_SEARCH_PATHS})
endif()

# 检查版本
if(absl_FOUND)
    message(STATUS "Found Abseil version: ${absl_VERSION} at ${absl_DIR}")
    
    # 检查版本是否满足要求
    if(absl_VERSION VERSION_LESS ABSEIL_MIN_VERSION)
        message(WARNING "Found Abseil version ${absl_VERSION} is older than required version ${ABSEIL_MIN_VERSION}")
        message(STATUS "Searching for newer version...")
        
        # 尝试明确指定使用/usr/local的版本
        find_package(absl CONFIG PATHS "/usr/local/lib/cmake/absl" NO_DEFAULT_PATH)
        
        if(absl_FOUND AND NOT absl_VERSION VERSION_LESS ABSEIL_MIN_VERSION)
            message(STATUS "Found suitable Abseil version: ${absl_VERSION} at ${absl_DIR}")
        else()
            message(FATAL_ERROR "Cannot find Abseil version >= ${ABSEIL_MIN_VERSION}. Please install a newer version.")
        endif()
    endif()
else()
    message(FATAL_ERROR "Abseil not found. Please install Abseil.")
endif()