4.特色与创新之处（限1000字）
 
特色之处
（1）基础理论原创、技术路线新颖。本项目以申请人团队原创提出的纯位姿成像几何理论为核心，研究方向瞄准经典双视图几何描述缺陷等基础问题，预期研究成果将推动视觉几何理论向更高效、更完备方向发展。
（2）纯位姿视觉工作的影响力逐渐彰显。前期工作被T-PAMI评审专家誉为"groundbreaking"，被NVIDIA列为三维视觉重建方法典型代表，并为瑞士联邦理工ETH与微软联合推出的国际视觉重建平台GLOMAP提供设计灵感；提出了相机观测信息使用的新范式，被澳洲国立大学/中科院自动化所、国防科大、武汉大学等团队应用于惯性视觉里程计的高效估计。
（3）算法成果具自主产权、应用前景广阔。本项目目标算法构建在原创基础理论之上，可与前期成果集成为自主知识产权的完备算法平台，助力我国智能空间计算发展。纯位姿核心算法框架已获美国发明专利授权，部分算法受邀嵌入著名视觉平台OpenMVG核心库。

创新之处
（1）理论方法创新——建立原创的纯位姿视觉计算理论体系
本项目构建的纯位姿成像几何理论体系将空间成像过程从高维参数空间无损表达到低维位姿流形上，具备经典视觉几何理论不具有的优势：
几何完备描述性：符合手性约束且无纯旋转退化的几何描述，从根本上避免平行刚性、局部共线和纯旋转等运动导致的求解异常；
线性性：景深与全局位移间存在线性关系，多视图几何可用全局位移线性形式等价表达，实现全局最优位移的线性求解；
参数解耦性：实现高维3D场景参数与低维位姿参数解耦，以及易异常位移参数与鲁棒姿态参数解耦，提高计算效率和鲁棒性，便于处理复杂场景下的三维视觉计算；
低维性：内在表达具低维性，有望突破当前三维视觉计算的效率瓶颈。

（2）应用平台创新——构建自主知识产权纯位姿视觉计算平台
针对三维视觉计算核心瓶颈，本项目将研发纯位姿视觉计算核心算法并封装为通用工具库，构建自主知识产权计算平台。该平台将充分发挥纯位姿成像几何理论特色，有望在相对位姿估计、全局姿态优化与位移估计、三维场景重建等关键环节达到国际领先水平，实现相机位姿估计与场景重建完全分离、姿态估计与位移求解完全分离的视觉计算框架。通过构建姿态估计、位移求解和3D场景重建的独立化模块，该计算框架将有效避免复杂场景与特殊运动结构引起的求解异常，能根据任务需求灵活调整计算模式，实现大比例匹配异常下的稳健位姿估计，支持实时高效且稳定的空间视觉计算。
