#!/bin/bash

# 需要root权限
if [ "$EUID" -ne 0 ]; then 
    echo "请使用sudo运行此脚本"
    exit 1
fi

# 删除库文件
rm -f /usr/local/lib/libpo_core.so*
rm -f /usr/local/lib/libpomvg_proto.so*
rm -f /usr/local/lib/libpomvg_file_io.so*
rm -f /usr/local/lib/libpomvg_factory.so*
rm -f /usr/local/lib/libpomvg_relative_process.so*

# 删除CMake配置文件
rm -rf /usr/local/lib/cmake/po_core

# 删除头文件
rm -f /usr/local/include/po_core.hpp
rm -rf /usr/local/include/po_core

# 删除proto文件
rm -rf /proto
rm -rf /pomvg/proto

# 删除其他相关目录
rm -rf /po_core
rm -rf /pomvg

echo "卸载完成"