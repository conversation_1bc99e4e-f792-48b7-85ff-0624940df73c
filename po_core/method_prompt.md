那么接下来是ModelEstimator接口的实现了，比如 OpenGVEstimator
要求：Run()函数(以DataPacakge [DataSample<OpenGV's Match>  + DataFeatures + DataCameraModel]对应的输入, required_packages里有：
    >> DataPtr data_sample_ptr = required_package_["data_sample"];
    >> TSample samples = GetDataPtr<TSample>(data_sample_ptr);
    >> .... (具体处理， 你可以参考OpenGV来调用处理TSample=IdMatches）
    >> 返回DataPtr 指向的是DataMap<RelativePose>的类型。

Note: 不要用RANSAC等鲁棒估计方式，而是采用直接的model estimator的方法
你可以参考：
基础类型参考： @types.hpp 

基本数据接口参考：  @data_features_plugin.hpp ,  @data_matches.hpp, @data_camera_model.hpp 

转换接口参考： @converter_opencv.hpp  (可以设计一个转换接口， 比如： @converter_opengv.hpp)

插件库参考：
 @method_img2features_plugin.hpp ,  @method_img2features_plugin.cpp , @method_img2features.ini 

内置库参考：
@method_matches2tracks.hpp , @method_matches2tracks.cpp 

通用接口参考：
@interfaces.hpp , @interfaces_preset.hpp , @interfaces_robust_estimator.hpp ,  @interfaces_preset_profiler.hpp ,  @ransac_estimator.hpp 

参考文档：
@OpenGV  @opengv  @test_relative_pose.cpp 

帮我重新实现：
@method_opengv_estimator_plugin.hpp  @method_opengv_estimator_plugin.cpp 



请辅助我设计class OpenGVModelEstimator : public MethodPresetProfiler;
（我已经创建好opengv_model_estimator.hpp和opengv_model_estimator.cpp文件）

OpenGVModelEstimator model_estimator; 

输入方式：method->Build(); // 这种方式，required_package_会提前分配，并且必须有data_sample类型，输入的方式是[ DataSample<IdMatches> + DataFeatures + DataCameraModel]的方式；

基本数据接口参考：  @data_features_plugin.hpp ,  @data_matches.hpp, @data_camera_model.hpp  

要求1：需要拓展Bulid函数, 在基类Build之前转换输入数据：先调用对应的转换函数，将我们PoMVG数据格式，转成OpenGV的方式，然后调用基类Build处理方式 （ABI拓展）

要求2：在Run()函数里，利用openGV调用不同方法来实现RelativePose估计的代码；

要求3：临时封装类型 using MatchSamples = std::pair<ViewPair, IdMatches>;

获取数据指针，请参考  @interfaces.hpp   @interfaces_preset.hpp  @interfaces_robust_estimator.hpp 中的GetDataPtr  GetSampleDataPtr   GetMapDataPtr

类型参考：
@types.hpp；  DataMatches对应GetData返回的是Matches指针 ，而 DataSample<IdMatches> 对应的是IdMatches指针。
