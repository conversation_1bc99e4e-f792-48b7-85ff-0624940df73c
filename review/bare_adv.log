This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2025.3.4)  20 JUL 2025 11:05
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/PoMVG/review/bare_adv.tex
(/Users/<USER>/Documents/PoMVG/review/bare_adv.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/local/texlive/2024/texmf-dist/tex/latex/ieeetran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count188
\@IEEEtrantmpcountB=\count189
\@IEEEtrantmpcountC=\count190
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ppl on input line 503.
(/usr/local/texlive/2024/texmf-dist/tex/latex/psnfss/ot1ppl.fd
File: ot1ppl.fd 2001/06/04 font definitions for OT1/ppl.
)
-- Using IEEE Computer Society mode.
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <5.01874> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <5.01874> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <7.02625> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <7.02625> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <8.03> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <8.03> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <9.03374> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <9.03374> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <9.53561> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <9.53561> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <11.04124> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <11.04124> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <12.045> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <12.045> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <17.06374> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <17.06374> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <20.075> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <20.075> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/n' in size <24.09> not available
(Font)              Font shape `OT1/ppl/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ppl/bx/it' in size <24.09> not available
(Font)              Font shape `OT1/ppl/b/it' tried instead on input line 1090.
\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count191
\c@subsection=\count192
\c@subsubsection=\count193
\c@paragraph=\count194
\c@IEEEsubequation=\count195
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count196
\c@table=\count197
\@IEEEeqnnumcols=\count198
\@IEEEeqncolcnt=\count199
\@IEEEsubeqnnumrollback=\count266
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count267
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count268
\@IEEEtranrubishbin=\box52
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count269
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count270
\leftroot@=\count271
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count272
\DOTSCASE@=\count273
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count274
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count275
\dotsspace@=\muskip16
\c@parentequation=\count276
\dspbrk@lvl=\count277
\tag@help=\toks19
\row@=\count278
\column@=\count279
\maxfields@=\count280
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen173
\ar@mcellbox=\box55
\extrarowheight=\dimen174
\NC@list=\toks23
\extratabsurround=\skip54
\backup@length=\skip55
\ar@cellbox=\box56
) (/usr/local/texlive/2024/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks24
) (/usr/local/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen175
\captionmargin=\dimen176
\caption@leftmargin=\dimen177
\caption@rightmargin=\dimen178
\caption@width=\dimen179
\caption@indent=\dimen180
\caption@parindent=\dimen181
\caption@hangindent=\dimen182
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \sffamily \footnotesize #1}\\{\normalfont \sffamily \footnotesize #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \sffamily \footnotesize {#1.}\nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \sffamily \footnotesize {#1.}\nobreakspace }\parbox [t]{\hsize }{\normalfont \sffamily \footnotesize \noindent \unhbox \@tempboxa #2}\else \hbox to\hsize {\normalfont \sffamily \footnotesize \box \@tempboxa \hfil }\fi \fi  on input line 1175.
)
\c@KVtest=\count281
\sf@farskip=\skip56
\sf@captopadj=\dimen183
\sf@capskip=\skip57
\sf@nearskip=\skip58
\c@subfigure=\count282
\c@subfigure@save=\count283
\c@lofdepth=\count284
\c@subtable=\count285
\c@subtable@save=\count286
\c@lotdepth=\count287
\sf@top=\skip59
\sf@bottom=\skip60
) (/usr/local/texlive/2024/texmf-dist/tex/latex/adjustbox/adjustbox.sty
Package: adjustbox 2022/10/17 v1.3a Adjusting TeX boxes (trim, clip, ...)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local/texlive/2024/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks25
\XKV@tempa@toks=\toks26
)
\XKV@depth=\count288
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/adjustbox/adjcalc.sty
Package: adjcalc 2012/05/16 v1.1 Provides advanced setlength with multiple back-ends (calc, etex, pgfmath)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/adjustbox/trimclip.sty
Package: trimclip 2020/08/19 v1.2 Trim and clip general TeX material
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen184
\Gin@req@width=\dimen185
) (/usr/local/texlive/2024/texmf-dist/tex/latex/collectbox/collectbox.sty
Package: collectbox 2022/10/17 v0.4c Collect macro arguments as boxes
\collectedbox=\box57
)
\tc@llx=\dimen186
\tc@lly=\dimen187
\tc@urx=\dimen188
\tc@ury=\dimen189
Package trimclip Info: Using driver 'tc-pdftex.def'.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/adjustbox/tc-pdftex.def
File: tc-pdftex.def 2019/01/04 v2.2 Clipping driver for pdftex
))
\adjbox@Width=\dimen190
\adjbox@Height=\dimen191
\adjbox@Depth=\dimen192
\adjbox@Totalheight=\dimen193
\adjbox@pwidth=\dimen194
\adjbox@pheight=\dimen195
\adjbox@pdepth=\dimen196
\adjbox@ptotalheight=\dimen197
 (/usr/local/texlive/2024/texmf-dist/tex/latex/ifoddpage/ifoddpage.sty
Package: ifoddpage 2022/10/18 v1.2 Conditionals for odd/even page detection
\c@checkoddpage=\count289
) (/usr/local/texlive/2024/texmf-dist/tex/latex/varwidth/varwidth.sty
Package: varwidth 2009/03/30 ver 0.92;  Variable-width minipages
\@vwid@box=\box58
\sift@deathcycles=\count290
\@vwid@loff=\dimen198
\@vwid@roff=\dimen199
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
) (/usr/local/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen256
\lightrulewidth=\dimen257
\cmidrulewidth=\dimen258
\belowrulesep=\dimen259
\belowbottomsep=\dimen260
\aboverulesep=\dimen261
\abovetopsep=\dimen262
\cmidrulesep=\dimen263
\cmidrulekern=\dimen264
\defaultaddspace=\dimen265
\@cmidla=\count291
\@cmidlb=\count292
\@aboverulesep=\dimen266
\@belowrulesep=\dimen267
\@thisruleclass=\count293
\@lastruleclass=\count294
\@thisrulewidth=\dimen268
) (/usr/local/texlive/2024/texmf-dist/tex/generic/soul/soul.sty
Package: soul 2023-06-14 v3.1 Permit use of UTF-8 characters in soul (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/soul/soul-ori.sty
Package: soul-ori 2023-06-14 v3.1 letterspacing/underlining (mf)
\SOUL@word=\toks27
\SOUL@lasttoken=\toks28
\SOUL@syllable=\toks29
\SOUL@cmds=\toks30
\SOUL@buffer=\toks31
\SOUL@token=\toks32
\SOUL@syllgoal=\dimen269
\SOUL@syllwidth=\dimen270
\SOUL@charkern=\dimen271
\SOUL@hyphkern=\dimen272
\SOUL@dimen=\dimen273
\SOUL@dimeni=\dimen274
\SOUL@minus=\count295
\SOUL@comma=\count296
\SOUL@apo=\count297
\SOUL@grave=\count298
\SOUL@spaceskip=\skip61
\SOUL@ttwidth=\dimen275
\SOUL@uldp=\dimen276
\SOUL@ulht=\dimen277
) (/usr/local/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))) (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)) (/usr/local/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
LaTeX Info: Redefining \color on input line 758.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (/usr/local/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count299
\float@exts=\toks33
\float@box=\box59
\@float@everytoks=\toks34
\@floatcapt=\box60
) (/usr/local/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks35
\c@algorithm=\count300
) (/usr/local/texlive/2024/texmf-dist/tex/latex/algorithmicx/algpseudocode.sty
Package: algpseudocode 
 (/usr/local/texlive/2024/texmf-dist/tex/latex/algorithmicx/algorithmicx.sty
Package: algorithmicx 2005/04/27 v1.2 Algorithmicx

Document Style algorithmicx 1.2 - a greatly improved `algorithmic' style
\c@ALG@line=\count301
\c@ALG@rem=\count302
\c@ALG@nested=\count303
\ALG@tlm=\skip62
\ALG@thistlm=\skip63
\c@ALG@Lnr=\count304
\c@ALG@blocknr=\count305
\c@ALG@storecount=\count306
\c@ALG@tmpcounter=\count307
\ALG@tmplength=\skip64
)
Document Style - pseudocode environments for use with the `algorithmicx' style
) (/usr/local/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip65
\multirow@cntb=\count308
\multirow@dima=\skip66
\bigstrutjot=\dimen278
) (/usr/local/texlive/2024/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)


Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count309
\c@continuedfloat=\count310
Package caption Info: float package is loaded.
)
LaTeX Info: Redefining \subref on input line 189.
Package subcaption Info: The counter `subfigure' was already defined by
(subcaption)             \newsubfloat (offered by the subfig package) on input line 238.
Package subcaption Info: The counter `subtable' was already defined by
(subcaption)             \newsubfloat (offered by the subfig package) on input line 238.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks36
\thm@bodyfont=\toks37
\thm@headfont=\toks38
\thm@notefont=\toks39
\thm@headpunct=\toks40
\thm@preskip=\skip67
\thm@postskip=\skip68
\thm@headsep=\skip69
\dth@everypar=\toks41
) (/usr/local/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count311
\Gm@cntv=\count312
\c@Gm@tempcnt=\count313
\Gm@bindingoffset=\dimen279
\Gm@wd@mp=\dimen280
\Gm@odd@mp=\dimen281
\Gm@even@mp=\dimen282
\Gm@layoutwidth=\dimen283
\Gm@layoutheight=\dimen284
\Gm@layouthoffset=\dimen285
\Gm@layoutvoffset=\dimen286
\Gm@dimlist=\toks42
) (/usr/local/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip70
\enit@outerparindent=\dimen287
\enit@toks=\toks43
\enit@inbox=\box61
\enit@count@id=\count314
\enitdp@description=\count315
) (/usr/local/texlive/2024/texmf-dist/tex/latex/threeparttable/threeparttable.sty
Package: threeparttable 2003/06/13  v 3.0
\@tempboxb=\box62
)
\c@proposition=\count316
 (/usr/local/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (/usr/local/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count317
\l__pdf_internal_box=\box63
)
No file bare_adv.aux.
\openout1 = `bare_adv.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 574.
LaTeX Font Info:    ... okay on input line 574.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 574.
LaTeX Font Info:    ... okay on input line 574.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 574.
LaTeX Font Info:    ... okay on input line 574.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 574.
LaTeX Font Info:    ... okay on input line 574.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 574.
LaTeX Font Info:    ... okay on input line 574.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 574.
LaTeX Font Info:    ... okay on input line 574.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 574.
LaTeX Font Info:    ... okay on input line 574.
-- Lines per column: 60 (approximate, difference = 5.51083pt).
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: threeparttable package is loaded.
Package caption Info: End \AtBeginDocument code.
(/usr/local/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count318
\scratchdimen=\dimen288
\scratchbox=\box64
\nofMPsegments=\count319
\nofMParguments=\count320
\everyMPshowfont=\toks44
\MPscratchCnt=\count321
\MPscratchDim=\dimen289
\MPnumerator=\count322
\makeMPintoPDFobject=\count323
\everyMPtoPDFconversion=\toks45
) (/usr/local/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
 (/usr/local/texlive/2024/texmf-dist/tex/latex/grfext/grfext.sty
Package: grfext 2019/12/03 v1.3 Manage graphics extensions (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
Package grfext Info: Graphics extension search list:
(grfext)             [.pdf,.png,.jpg,.mps,.jpeg,.jbig2,.jb2,.PDF,.PNG,.JPG,.JPEG,.JBIG2,.JB2,.eps]
(grfext)             \AppendGraphicsExtensions on input line 504.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-18.69788pt
* \headheight=12.0pt
* \headsep=6.69788pt
* \topskip=11.58327pt
* \footskip=19.1765pt
* \marginparwidth=20.0pt
* \marginparsep=10.0pt
* \columnsep=12.045pt
* \skip\footins=10.42487pt plus 4.63322pt minus 2.3166pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumntrue
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Font Info:    Trying to load font information for OT1+phv on input line 687.
(/usr/local/texlive/2024/texmf-dist/tex/latex/psnfss/ot1phv.fd
File: ot1phv.fd 2020/03/25 scalable font definitions for OT1/phv.
)
LaTeX Font Info:    Font shape `OT1/phv/m/it' in size <11.04124> not available
(Font)              Font shape `OT1/phv/m/sl' tried instead on input line 687.
LaTeX Font Info:    Calculating math sizes for size <8.03> on input line 687.
LaTeX Font Info:    Trying to load font information for U+msa on input line 687.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 687.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Calculating math sizes for size <11.04124> on input line 687.
LaTeX Font Info:    Trying to load font information for U+pzd on input line 687.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/psnfss/upzd.fd
File: upzd.fd 2001/06/04 font definitions for U/pzd.
)
Underfull \hbox (badness 1783) in paragraph at lines 687--687
\OT1/ppl/m/it/8.03 Ser-vices, School of Elec-tronic In-for-ma-tion and Elec-tri-cal
 []


Underfull \hbox (badness 10000) in paragraph at lines 687--687
\OT1/ppl/m/it/8.03 China. E-mail: <EMAIL>, <EMAIL>,
 []


LaTeX Warning: Citation `ozyecsil2017survey' on page 1 undefined on input line 745.


LaTeX Warning: Citation `szeliski2022computer' on page 1 undefined on input line 745.


LaTeX Warning: Citation `decker2008dealing' on page 1 undefined on input line 747.


LaTeX Warning: Citation `nister2004efficient' on page 1 undefined on input line 747.


LaTeX Warning: Citation `stewenius2006recent' on page 1 undefined on input line 747.


LaTeX Warning: Citation `kneip2012finding' on page 1 undefined on input line 747.


LaTeX Warning: Citation `cai2019equivalent' on page 1 undefined on input line 747.


LaTeX Warning: Citation `kneip2012finding' on page 1 undefined on input line 747.


LaTeX Warning: Citation `cai2019equivalent' on page 1 undefined on input line 747.


LaTeX Warning: Citation `enqvist2011non' on page 1 undefined on input line 747.


LaTeX Warning: Citation `nister2004efficient' on page 1 undefined on input line 747.


LaTeX Warning: Citation `stewenius2006recent' on page 1 undefined on input line 747.


LaTeX Warning: Citation `longuet1981computer' on page 1 undefined on input line 747.


LaTeX Warning: Citation `hartley2003multiple' on page 1 undefined on input line 747.


LaTeX Warning: Citation `zhao2020efficient' on page 1 undefined on input line 749.


LaTeX Warning: Citation `stewenius2006recent' on page 1 undefined on input line 749.


LaTeX Warning: Citation `pizarro2003relative' on page 1 undefined on input line 749.


LaTeX Warning: Citation `hartley2003multiple' on page 1 undefined on input line 749.


LaTeX Warning: Citation `longuet1981computer' on page 1 undefined on input line 749.


LaTeX Warning: Citation `kneip2014opengv' on page 1 undefined on input line 749.


LaTeX Warning: Citation `hartley2003multiple' on page 1 undefined on input line 749.


LaTeX Warning: Citation `zhao2020efficient' on page 1 undefined on input line 749.


LaTeX Warning: Citation `zhao2020efficient' on page 1 undefined on input line 749.


LaTeX Warning: Citation `Philip1998' on page 1 undefined on input line 749.


LaTeX Warning: Citation `pizarro2003relative' on page 1 undefined on input line 749.


LaTeX Warning: Citation `stewenius2006recent' on page 1 undefined on input line 749.


LaTeX Warning: Citation `Jing2023TPAMI_survey' on page 1 undefined on input line 754.


LaTeX Warning: Citation `longuet1981computer' on page 1 undefined on input line 754.


LaTeX Warning: Citation `fischler1981random' on page 1 undefined on input line 754.


LaTeX Warning: Citation `ozyecsil2017survey' on page 1 undefined on input line 754.


LaTeX Warning: Citation `rousseeuw1984least' on page 1 undefined on input line 754.


LaTeX Warning: Citation `moulon2013adaptive' on page 1 undefined on input line 754.


LaTeX Warning: Citation `barath2019magsac' on page 1 undefined on input line 754.


LaTeX Warning: Citation `barath2020magsac++' on page 1 undefined on input line 754.


LaTeX Warning: Citation `li2020gesac' on page 1 undefined on input line 754.


LaTeX Warning: Citation `huber1992robust' on page 1 undefined on input line 755.


LaTeX Warning: Citation `huber2004robust' on page 1 undefined on input line 755.


LaTeX Warning: Citation `huber1992robust' on page 1 undefined on input line 755.


LaTeX Warning: Citation `li2020gesac' on page 1 undefined on input line 755.


LaTeX Warning: Citation `peng2023convergence' on page 1 undefined on input line 755.


Underfull \vbox (badness 2913) has occurred while \output is active []

 [1{/usr/local/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{/usr/local/texlive/2024/texmf-dist/fonts/enc/dvips/base/8r.enc}


]

LaTeX Warning: Citation `cai2021pose' on page 2 undefined on input line 759.


LaTeX Warning: Citation `moulon2022' on page 2 undefined on input line 759.


LaTeX Warning: Citation `cai2019equivalent' on page 2 undefined on input line 761.


LaTeX Warning: Citation `cai2021pose' on page 2 undefined on input line 761.


LaTeX Warning: Reference `sec:related works' on page 2 undefined on input line 780.


LaTeX Warning: Reference `sec:Preliminaries and Notation' on page 2 undefined on input line 780.


LaTeX Warning: Reference `sec:Essential_Equation_and_Its_Properties' on page 2 undefined on input line 780.


LaTeX Warning: Reference `sec: LiRP_based_on_PPO' on page 2 undefined on input line 780.


LaTeX Warning: Reference `sec: GNC-RANSAC method' on page 2 undefined on input line 780.


LaTeX Warning: Reference `sec:Experiments' on page 2 undefined on input line 780.


LaTeX Warning: Reference `sec:conclusion' on page 2 undefined on input line 780.


LaTeX Warning: Citation `moulon2017openmvg' on page 2 undefined on input line 790.


LaTeX Warning: Citation `schonberger2016structure' on page 2 undefined on input line 790.


LaTeX Warning: Citation `kneip2014opengv' on page 2 undefined on input line 790.


LaTeX Warning: Citation `hartley2003multiple' on page 2 undefined on input line 790.


LaTeX Warning: Citation `longuet1981computer' on page 2 undefined on input line 790.


LaTeX Warning: Citation `stewenius2006recent' on page 2 undefined on input line 790.


LaTeX Warning: Citation `kneip2014opengv' on page 2 undefined on input line 790.


LaTeX Warning: Citation `moulon2017openmvg' on page 2 undefined on input line 790.


LaTeX Warning: Citation `cai2019equivalent' on page 2 undefined on input line 790.


LaTeX Warning: Citation `cai2021pose' on page 2 undefined on input line 790.


LaTeX Warning: Citation `nister2004efficient' on page 2 undefined on input line 790.


LaTeX Warning: Citation `stewenius2006recent' on page 2 undefined on input line 790.


LaTeX Warning: Citation `pizarro2003relative' on page 2 undefined on input line 790.


LaTeX Warning: Citation `Philip1998' on page 2 undefined on input line 790.


LaTeX Warning: Citation `kneip2012finding' on page 2 undefined on input line 790.


LaTeX Warning: Citation `zhao2020efficient' on page 2 undefined on input line 790.


LaTeX Warning: Citation `philip1996non' on page 2 undefined on input line 790.


LaTeX Warning: Citation `pizarro2003relative' on page 2 undefined on input line 790.


LaTeX Warning: Citation `nister2004efficient' on page 2 undefined on input line 790.


LaTeX Warning: Citation `stewenius2006recent' on page 2 undefined on input line 790.


LaTeX Warning: Citation `kneip2012finding' on page 2 undefined on input line 792.


LaTeX Warning: Citation `kneip2014opengv' on page 2 undefined on input line 792.

LaTeX Font Info:    Calculating math sizes for size <9.53561> on input line 792.

LaTeX Warning: Citation `cai2019equivalent' on page 2 undefined on input line 792.


LaTeX Warning: Citation `cai2021pose' on page 2 undefined on input line 792.


LaTeX Warning: Citation `fischler1981random' on page 2 undefined on input line 795.


LaTeX Warning: Citation `rousseeuw1984least' on page 2 undefined on input line 795.


LaTeX Warning: Citation `moulon2013adaptive' on page 2 undefined on input line 795.


LaTeX Warning: Citation `ozyecsil2017survey' on page 2 undefined on input line 795.


LaTeX Warning: Citation `barath2019magsac' on page 2 undefined on input line 795.


LaTeX Warning: Citation `barath2020magsac++' on page 2 undefined on input line 795.


LaTeX Warning: Citation `li2020gesac' on page 2 undefined on input line 795.

[2]

LaTeX Warning: Citation `huber1992robust' on page 3 undefined on input line 797.


LaTeX Warning: Citation `de2021review' on page 3 undefined on input line 797.


LaTeX Warning: Citation `zhao2020efficient' on page 3 undefined on input line 797.


LaTeX Warning: Citation `holland1977robust' on page 3 undefined on input line 799.


LaTeX Warning: Citation `huber2004robust' on page 3 undefined on input line 799.


LaTeX Warning: Citation `chatterjee2017robust' on page 3 undefined on input line 799.


LaTeX Warning: Citation `peng2023convergence' on page 3 undefined on input line 799.


LaTeX Warning: Citation `cai2021pose' on page 3 undefined on input line 807.


LaTeX Warning: Citation `zhao2020efficient' on page 3 undefined on input line 814.


Overfull \hbox (11.11473pt too wide) detected at line 817
[]\OMS/cmsy/m/n/10 M[] \U/msa/m/n/9.53561 , [] \OML/cmm/m/it/10 :
 []


LaTeX Warning: Citation `faugeras1990motion' on page 3 undefined on input line 819.


LaTeX Warning: Citation `zhao2020efficient' on page 3 undefined on input line 819.


LaTeX Warning: Citation `nister2004efficient' on page 3 undefined on input line 819.


LaTeX Warning: Citation `hartley1995investigation' on page 3 undefined on input line 819.


LaTeX Warning: Citation `faugeras1990motion' on page 3 undefined on input line 844.


LaTeX Warning: Citation `philip1996non' on page 3 undefined on input line 844.

[3]

LaTeX Warning: Citation `longuet1981computer' on page 4 undefined on input line 863.


LaTeX Warning: Reference `eq:eq2' on page 4 undefined on input line 869.


LaTeX Warning: Reference `eq:essential equation' on page 4 undefined on input line 869.


LaTeX Warning: Reference `eq:essential equation' on page 4 undefined on input line 869.


LaTeX Warning: Citation `cai2021pose' on page 4 undefined on input line 869.


LaTeX Warning: Citation `kneip2012finding' on page 4 undefined on input line 869.


LaTeX Warning: Reference `eq:eq2' on page 4 undefined on input line 869.


LaTeX Warning: Reference `eq:eq5' on page 4 undefined on input line 877.


LaTeX Warning: Citation `kneip2012finding' on page 4 undefined on input line 877.


LaTeX Warning: Citation `kneip2013direct' on page 4 undefined on input line 877.


LaTeX Warning: Reference `eq:essential equation' on page 4 undefined on input line 882.


/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:887: LaTeX Error: There's no line here to end.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.887 \noindent
                a) Sufficiency Proof:
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


Underfull \hbox (badness 4940) in paragraph at lines 887--889
[]\OT1/ppl/m/n/9.53561 a) Suf-fi-ciency Proof: As-sume a ma-trix
 []


LaTeX Warning: Reference `eq:eq2' on page 4 undefined on input line 934.


LaTeX Warning: Reference `eq:essential equation' on page 4 undefined on input line 941.


LaTeX Warning: Reference `eq:eq5' on page 4 undefined on input line 944.


LaTeX Warning: Reference `eq:eq6' on page 4 undefined on input line 947.


LaTeX Warning: Reference `prop:pure rotation' on page 4 undefined on input line 961.

[4]

LaTeX Warning: Citation `cai2019equivalent' on page 5 undefined on input line 968.


LaTeX Warning: Reference `eq:Q_equation' on page 5 undefined on input line 968.


LaTeX Warning: Citation `hartley2003multiple' on page 5 undefined on input line 971.


LaTeX Warning: Citation `pizarro2003relative' on page 5 undefined on input line 973.


LaTeX Warning: Citation `nister2004efficient' on page 5 undefined on input line 973.


LaTeX Warning: Citation `stewenius2006recent' on page 5 undefined on input line 973.


LaTeX Warning: Citation `stewenius2006recent' on page 5 undefined on input line 973.


LaTeX Warning: Reference `eq:eq2' on page 5 undefined on input line 982.


LaTeX Warning: Reference `eq:Q_equation' on page 5 undefined on input line 983.


LaTeX Warning: Citation `cai2019equivalent' on page 5 undefined on input line 983.


Underfull \vbox (badness 2012) has occurred while \output is active []



LaTeX Font Warning: Command \small invalid in math mode on input line 994.

LaTeX Font Info:    Calculating math sizes for size <9.03374> on input line 996.
LaTeX Font Info:    Calculating math sizes for size <> on input line 1004.

LaTeX Font Warning: Font shape `OT1/cmr/m/n' in size <> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OT1/cmr/m/n' in size <0.7> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OT1/cmr/m/n' in size <0.5> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OML/cmm/m/it' in size <> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OML/cmm/m/it' in size <0.7> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OML/cmm/m/it' in size <0.5> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OMS/cmsy/m/n' in size <> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OMS/cmsy/m/n' in size <0.7> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Font shape `OMS/cmsy/m/n' in size <0.5> not available
(Font)              size <5> substituted on input line 1004.


LaTeX Font Warning: Command \small invalid in math mode on input line 1007.


LaTeX Font Warning: Command \small invalid in math mode on input line 1061.

[5]

LaTeX Warning: Reference `eq:QRax' on page 6 undefined on input line 1080.


LaTeX Warning: Reference `eq:tQb' on page 6 undefined on input line 1080.


LaTeX Warning: Reference `eq:QRax' on page 6 undefined on input line 1080.


LaTeX Font Warning: Command \small invalid in math mode on input line 1082.


/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1092: LaTeX Error: There's no line here to end.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1092 \begin
             {proposition}
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


LaTeX Warning: Reference `eq:planar structure' on page 6 undefined on input line 1102.


LaTeX Warning: Reference `eq:QRax' on page 6 undefined on input line 1106.


LaTeX Warning: Reference `eq:tQb' on page 6 undefined on input line 1106.


LaTeX Font Warning: Command \small invalid in math mode on input line 1108.


/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1116: LaTeX Error: There's no line here to end.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1116 
       
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1118: LaTeX Error: There's no line here to end.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1118 \begin
             {proposition}
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


LaTeX Warning: Reference `proposition:properties of coplanar degeneracy' on page 6 undefined on input line 1124.


LaTeX Warning: Reference `eq:QRax' on page 6 undefined on input line 1124.


LaTeX Warning: Reference `eq:tQb' on page 6 undefined on input line 1124.


LaTeX Warning: Reference `proposition:relationship between Q and H' on page 6 undefined on input line 1124.


LaTeX Warning: Reference `eq:solveH' on page 6 undefined on input line 1130.


LaTeX Warning: Reference `proposition:linear representation of H' on page 6 undefined on input line 1133.


LaTeX Warning: Reference `eq:solveH' on page 6 undefined on input line 1133.


LaTeX Warning: Citation `ma2004invitation' on page 6 undefined on input line 1133.


Underfull \hbox (badness 3701) in paragraph at lines 1136--1136
 \OT1/phv/b/sc/11.04124 5 Lin-ear Rel-a-tive Pose Es-ti-ma-tion
 []


LaTeX Warning: Reference `eq:Q_equation' on page 6 undefined on input line 1142.


LaTeX Warning: Reference `proposition:relationship between Q and H' on page 6 undefined on input line 1146.


LaTeX Warning: Reference `eq:relationQH' on page 6 undefined on input line 1146.


LaTeX Warning: Citation `faugeras1993three' on page 6 undefined on input line 1148.


LaTeX Warning: Reference `eq:Dy=0' on page 6 undefined on input line 1154.


LaTeX Warning: Reference `table1' on page 6 undefined on input line 1158.

[6]

LaTeX Warning: Citation `hartley2003multiple' on page 7 undefined on input line 1197.


LaTeX Warning: Citation `cai2019equivalent' on page 7 undefined on input line 1197.


LaTeX Warning: Reference `eq:Dy=0' on page 7 undefined on input line 1209.


LaTeX Warning: Reference `eq:Dz=0' on page 7 undefined on input line 1213.


LaTeX Warning: Citation `cai2019equivalent' on page 7 undefined on input line 1221.


LaTeX Warning: Citation `cai2021pose' on page 7 undefined on input line 1221.


LaTeX Warning: Citation `cai2019equivalent' on page 7 undefined on input line 1221.


LaTeX Warning: Citation `cai2021pose' on page 7 undefined on input line 1226.


LaTeX Warning: Citation `cai2021pose' on page 7 undefined on input line 1233.


LaTeX Warning: Reference `eq:eq7' on page 7 undefined on input line 1233.

<images/framework.png, id=51, 842.1864pt x 506.8536pt>
File: images/framework.png Graphic file (type png)
<use images/framework.png>
Package pdftex.def Info: images/framework.png  used on input line 1237.
(pdftex.def)             Requested size: 452.9679pt x 272.6141pt.

LaTeX Warning: Reference `eq:essential equation' on page 7 undefined on input line 1248.


LaTeX Warning: Reference `eq:eq5' on page 7 undefined on input line 1248.


LaTeX Warning: Citation `cai2019equivalent' on page 7 undefined on input line 1258.


LaTeX Warning: Reference `alg:LiRP algorithm' on page 7 undefined on input line 1263.

[7]

LaTeX Warning: Reference `eq:essential equation' on page 8 undefined on input line 1271.


LaTeX Warning: Citation `barath2019magsac' on page 8 undefined on input line 1271.


LaTeX Warning: Citation `hartley2003multiple' on page 8 undefined on input line 1271.


LaTeX Warning: Citation `zhao2020efficient' on page 8 undefined on input line 1271.


LaTeX Warning: Reference `proposition:properties of coplanar degeneracy' on page 8 undefined on input line 1271.


LaTeX Warning: Citation `kneip2014opengv' on page 8 undefined on input line 1275.


Underfull \hbox (badness 1642) in paragraph at lines 1275--1276
\OT1/ppl/m/n/9.53561 con-structs the re-pro-jec-tion resid-ual on the 2D
 []


Underfull \hbox (badness 10000) in paragraph at lines 1275--1276

 []


LaTeX Warning: Reference `fig:framework' on page 8 undefined on input line 1282.


LaTeX Warning: Citation `peng2023convergence' on page 8 undefined on input line 1286.


LaTeX Warning: Citation `blake1987visual' on page 8 undefined on input line 1286.


Underfull \hbox (badness 1629) in paragraph at lines 1286--1287
\OT1/ppl/m/n/9.53561 The re-cent GNC-based IRLS method [\OT1/ppl/b/n/9.53561 ?\OT1/ppl/m/n/9.53561 ] al-ter-
 []


LaTeX Warning: Citation `peng2023convergence' on page 8 undefined on input line 1288.


Underfull \hbox (badness 2781) in paragraph at lines 1288--1289
[]\OT1/ppl/m/n/9.53561 Let the loss func-tion of TLS be $\OML/cmm/m/it/10 ^^Z\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 ^^O\OT1/cmr/m/n/10 ) =
 []


Overfull \hbox (6.53261pt too wide) detected at line 1296
[]\OML/cmm/m/it/10 ^^Z\OT1/cmr/m/n/10 (\OML/cmm/m/it/10 ^^O; ^^V\OT1/cmr/m/n/10 ) = []
 []

[8 <./images/framework.png>]

LaTeX Warning: Citation `peng2023convergence' on page 9 undefined on input line 1314.


LaTeX Warning: Reference `fig:ms_tls_figs' on page 9 undefined on input line 1314.


LaTeX Warning: Citation `huber2004robust' on page 9 undefined on input line 1316.


LaTeX Warning: Reference `alg:IRLS-Pose-only' on page 9 undefined on input line 1316.

<images/loss_function.png, id=64, 722.7pt x 620.0766pt>
File: images/loss_function.png Graphic file (type png)
<use images/loss_function.png>
Package pdftex.def Info: images/loss_function.png  used on input line 1324.
(pdftex.def)             Requested size: 110.23071pt x 94.5781pt.
<images/weight_function.png, id=65, 730.4088pt x 614.7768pt>
File: images/weight_function.png Graphic file (type png)
<use images/weight_function.png>
Package pdftex.def Info: images/weight_function.png  used on input line 1327.
(pdftex.def)             Requested size: 110.23071pt x 92.77538pt.

Overfull \hbox (5.33989pt too wide) in paragraph at lines 1324--1328
 [][][] 
 []


LaTeX Warning: Reference `alg:LiRP algorithm' on page 9 undefined on input line 1349.


LaTeX Warning: Reference `eq:v_LiGT' on page 9 undefined on input line 1350.


LaTeX Warning: Reference `eq:weight' on page 9 undefined on input line 1354.


LaTeX Warning: Reference `eq:superlinear_update' on page 9 undefined on input line 1361.


LaTeX Warning: Reference `alg:IRLS-Pose-only' on page 9 undefined on input line 1400.


LaTeX Warning: Reference `eq:v_LiGT' on page 9 undefined on input line 1401.


LaTeX Warning: Reference `alg:IRLS-Pose-only' on page 9 undefined on input line 1411.


LaTeX Warning: Reference `alg:Gnc-ransac' on page 9 undefined on input line 1419.

[9 <./images/loss_function.png (PNG copy)> <./images/weight_function.png (PNG copy)>]

LaTeX Warning: Reference `table: Niter of GNC-RANSAC' on page 10 undefined on input line 1433.

<images/structure.png, id=69, 849.8952pt x 315.579pt>
File: images/structure.png Graphic file (type png)
<use images/structure.png>
Package pdftex.def Info: images/structure.png  used on input line 1438.
(pdftex.def)             Requested size: 452.9679pt x 168.19505pt.

LaTeX Warning: Citation `cai2021pose' on page 10 undefined on input line 1452.


LaTeX Warning: Citation `pizarro2003relative' on page 10 undefined on input line 1452.


LaTeX Warning: Citation `nister2004efficient' on page 10 undefined on input line 1452.


LaTeX Warning: Citation `stewenius2006recent' on page 10 undefined on input line 1452.


LaTeX Warning: Citation `zhao2020efficient' on page 10 undefined on input line 1452.


LaTeX Warning: Citation `stewenius2006recent' on page 10 undefined on input line 1465.


LaTeX Warning: Citation `stewenius2006recent' on page 10 undefined on input line 1465.


LaTeX Warning: Citation `zhao2020efficient' on page 10 undefined on input line 1465.


LaTeX Warning: Reference `fig:RTX' on page 10 undefined on input line 1480.


LaTeX Warning: Citation `kneip2014opengv' on page 10 undefined on input line 1480.


LaTeX Warning: Citation `pizarro2003relative' on page 10 undefined on input line 1482.


LaTeX Warning: Citation `stewenius2006recent' on page 10 undefined on input line 1482.


LaTeX Warning: Citation `hartley2003multiple' on page 10 undefined on input line 1482.


LaTeX Warning: Citation `hartley2003multiple' on page 10 undefined on input line 1482.


LaTeX Warning: Citation `zhao2020efficient' on page 10 undefined on input line 1482.


LaTeX Warning: Citation `hartley2003multiple' on page 10 undefined on input line 1482.


LaTeX Warning: Citation `kneip2013direct' on page 10 undefined on input line 1482.


LaTeX Warning: Citation `kneip2014opengv' on page 10 undefined on input line 1482.

LaTeX Font Info:    Font shape `OT1/phv/m/it' in size <9.53561> not available
(Font)              Font shape `OT1/phv/m/sl' tried instead on input line 1487.

LaTeX Warning: Citation `stewenius2006recent' on page 10 undefined on input line 1491.


LaTeX Warning: Citation `stewenius2006recent' on page 10 undefined on input line 1491.


LaTeX Warning: Citation `pizarro2003relative' on page 10 undefined on input line 1491.


LaTeX Warning: Citation `philip1996non' on page 10 undefined on input line 1491.


LaTeX Warning: Reference `tab:E_mat_problem' on page 10 undefined on input line 1493.


LaTeX Warning: Citation `stewenius2006recent' on page 10 undefined on input line 1493.


LaTeX Warning: Reference `tab:E_mat_problem' on page 10 undefined on input line 1493.


LaTeX Warning: Citation `zhao2020efficient' on page 10 undefined on input line 1493.

[10]

LaTeX Warning: Reference `fig:identification_methods' on page 11 undefined on input line 1500.


LaTeX Warning: Reference `sec:idm' on page 11 undefined on input line 1500.


LaTeX Warning: Reference `fig:idm_LiRP' on page 11 undefined on input line 1503.


LaTeX Warning: Reference `fig:idm_5pts' on page 11 undefined on input line 1503.


Underfull \hbox (badness 10000) in paragraph at lines 1503--1504

 []

<images/identification/legend.png, id=73, 738.1176pt x 66.9702pt>
File: images/identification/legend.png Graphic file (type png)
<use images/identification/legend.png>
Package pdftex.def Info: images/identification/legend.png  used on input line 1510.
(pdftex.def)             Requested size: 220.46144pt x 20.0023pt.
<images/identification/5pts.png, id=74, 722.7pt x 660.96938pt>
File: images/identification/5pts.png Graphic file (type png)
<use images/identification/5pts.png>
Package pdftex.def Info: images/identification/5pts.png  used on input line 1512.
(pdftex.def)             Requested size: 99.20697pt x 90.72975pt.
<images/identification/LiRP.png, id=75, 722.7pt x 660.96938pt>
File: images/identification/LiRP.png Graphic file (type png)
<use images/identification/LiRP.png>
Package pdftex.def Info: images/identification/LiRP.png  used on input line 1513.
(pdftex.def)             Requested size: 99.20697pt x 90.72975pt.
<images/identification/5pts_planar.png, id=76, 722.7pt x 660.96938pt>
File: images/identification/5pts_planar.png Graphic file (type png)
<use images/identification/5pts_planar.png>
Package pdftex.def Info: images/identification/5pts_planar.png  used on input line 1515.
(pdftex.def)             Requested size: 99.20697pt x 90.72975pt.
<images/identification/LiRP_planar.png, id=77, 722.7pt x 660.96938pt>
File: images/identification/LiRP_planar.png Graphic file (type png)
<use images/identification/LiRP_planar.png>
Package pdftex.def Info: images/identification/LiRP_planar.png  used on input line 1516.
(pdftex.def)             Requested size: 99.20697pt x 90.72975pt.

LaTeX Warning: `!h' float specifier changed to `!ht'.


/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1521: LaTeX Error: There's no line here to end.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1521 \noindent
                 \textbf{Noise test for initial two-view methods.}
Your command was ignored.
Type  I <command> <return>  to replace it with another command,
or  <return>  to continue without it.


LaTeX Warning: Reference `fig:test_ini_methods' on page 11 undefined on input line 1522.


LaTeX Warning: Reference `fig:noise_opt_test' on page 11 undefined on input line 1522.

<images/noise_without_outliers/initial/legend_noise_test_ini.png, id=78, 319.4334pt x 22.1628pt>
/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1528: Missing number, treated as zero.
<to be read again> 
                   \relax 
l.1528 ...liers/initial/legend_noise_test_ini.png}
                                                  \\
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1528: Illegal unit of measure (pt inserted).
<to be read again> 
                   \relax 
l.1528 ...liers/initial/legend_noise_test_ini.png}
                                                  \\
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1528: Missing number, treated as zero.
<to be read again> 
                   \relax 
l.1528 ...liers/initial/legend_noise_test_ini.png}
                                                  \\
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1528: Illegal unit of measure (pt inserted).
<to be read again> 
                   \relax 
l.1528 ...liers/initial/legend_noise_test_ini.png}
                                                  \\
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

File: images/noise_without_outliers/initial/legend_noise_test_ini.png Graphic file (type png)
<use images/noise_without_outliers/initial/legend_noise_test_ini.png>
Package pdftex.def Info: images/noise_without_outliers/initial/legend_noise_test_ini.png  used on input line 1528.
(pdftex.def)             Requested size: 164.03479pt x 11.38092pt.
<images/noise_without_outliers/initial/noiseInI_normal_forward_3_mean_error.png, id=79, 730.4088pt x 655.7298pt>
File: images/noise_without_outliers/initial/noiseInI_normal_forward_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_normal_forward_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_normal_forward_3_mean_error.png  used on input line 1530.
(pdftex.def)             Requested size: 99.65349pt x 89.46022pt.
<images/noise_without_outliers/initial/noiseInI_normal_sidewaysX_3_mean_error.png, id=80, 730.4088pt x 655.7298pt>
File: images/noise_without_outliers/initial/noiseInI_normal_sidewaysX_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_normal_sidewaysX_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_normal_sidewaysX_3_mean_error.png  used on input line 1531.
(pdftex.def)             Requested size: 99.65349pt x 89.46022pt.
<images/noise_without_outliers/initial/noiseInI_normal_sidewaysY_3_mean_error.png, id=81, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/initial/noiseInI_normal_sidewaysY_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_normal_sidewaysY_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_normal_sidewaysY_3_mean_error.png  used on input line 1532.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.
<images/noise_without_outliers/initial/noiseInI_normal_normal_3_mean_error.png, id=82, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/initial/noiseInI_normal_normal_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_normal_normal_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_normal_normal_3_mean_error.png  used on input line 1533.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.

Underfull \hbox (badness 1389) in paragraph at lines 1533--1533
[]\OT1/ppl/m/n/7.02625 (d) |\OT1/phv/m/n/7.02625 Nor-mal scene with 3DoF
 []

<images/noise_without_outliers/initial/noiseInI_planar_planarForward_3_mean_error.png, id=83, 730.4088pt x 655.7298pt>
File: images/noise_without_outliers/initial/noiseInI_planar_planarForward_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_planar_planarForward_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_planar_planarForward_3_mean_error.png  used on input line 1535.
(pdftex.def)             Requested size: 99.65349pt x 89.46022pt.
<images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysX_3_mean_error.png, id=84, 730.4088pt x 655.7298pt>
File: images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysX_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysX_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysX_3_mean_error.png  used on input line 1536.
(pdftex.def)             Requested size: 99.65349pt x 89.46022pt.
<images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysY_3_mean_error.png, id=85, 730.4088pt x 655.7298pt>
File: images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysY_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysY_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysY_3_mean_error.png  used on input line 1537.
(pdftex.def)             Requested size: 99.65349pt x 89.46022pt.
<images/noise_without_outliers/initial/noiseInI_planar_planar_3_mean_error.png, id=86, 730.22812pt x 655.69969pt>
File: images/noise_without_outliers/initial/noiseInI_planar_planar_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/initial/noiseInI_planar_planar_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/initial/noiseInI_planar_planar_3_mean_error.png  used on input line 1538.
(pdftex.def)             Requested size: 99.65349pt x 89.47614pt.
[11 <./images/structure.png>]

LaTeX Warning: `!h' float specifier changed to `!ht'.


LaTeX Warning: Reference `fig:test_ini_methods' on page 12 undefined on input line 1543.


LaTeX Warning: Reference `fig:normal_motion' on page 12 undefined on input line 1547.


LaTeX Warning: Reference `fig:normal_forward' on page 12 undefined on input line 1549.


LaTeX Warning: Reference `fig:normal_sidewaysY' on page 12 undefined on input line 1549.


LaTeX Warning: Reference `fig:planar_forward' on page 12 undefined on input line 1551.


LaTeX Warning: Reference `fig:planar_sidewaysY' on page 12 undefined on input line 1551.


Underfull \hbox (badness 5036) in paragraph at lines 1551--1552
[]\OT1/ppl/m/n/9.53561 For spe-cific mo-tions in pla-nar sce-nar-ios
 []

<images/noise_without_outliers/opt/legend_noise_opt.png, id=91, 694.7556pt x 31.7988pt>
/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1558: Missing number, treated as zero.
<to be read again> 
                   \relax 
l.1558 ...thout_outliers/opt/legend_noise_opt.png}
                                                  \\
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1558: Illegal unit of measure (pt inserted).
<to be read again> 
                   \relax 
l.1558 ...thout_outliers/opt/legend_noise_opt.png}
                                                  \\
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1558: Missing number, treated as zero.
<to be read again> 
                   \relax 
l.1558 ...thout_outliers/opt/legend_noise_opt.png}
                                                  \\
A number should have been here; I inserted `0'.
(If you can't figure out why I needed to see a number,
look up `weird error' in the index to The TeXbook.)

/Users/<USER>/Documents/PoMVG/review/bare_adv.tex:1558: Illegal unit of measure (pt inserted).
<to be read again> 
                   \relax 
l.1558 ...thout_outliers/opt/legend_noise_opt.png}
                                                  \\
Dimensions can be in units of em, ex, in, pt, pc,
cm, mm, dd, cc, nd, nc, bp, or sp; but yours is a new one!
I'll assume that you meant to say pt, for printer's points.
To recover gracefully from this error, it's best to
delete the erroneous units; e.g., type `2' to delete
two letters. (See Chapter 27 of The TeXbook.)

File: images/noise_without_outliers/opt/legend_noise_opt.png Graphic file (type png)
<use images/noise_without_outliers/opt/legend_noise_opt.png>
Package pdftex.def Info: images/noise_without_outliers/opt/legend_noise_opt.png  used on input line 1558.
(pdftex.def)             Requested size: 310.82434pt x 14.22636pt.
<images/noise_without_outliers/opt/noiseOpt_forward_3_mean_error.png, id=92, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/opt/noiseOpt_forward_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_forward_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_forward_3_mean_error.png  used on input line 1560.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.
<images/noise_without_outliers/opt/noiseOpt_sidewaysX_3_mean_error.png, id=93, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/opt/noiseOpt_sidewaysX_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_sidewaysX_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_sidewaysX_3_mean_error.png  used on input line 1561.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.
<images/noise_without_outliers/opt/noiseOpt_sidewaysY_3_mean_error.png, id=94, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/opt/noiseOpt_sidewaysY_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_sidewaysY_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_sidewaysY_3_mean_error.png  used on input line 1562.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.
<images/noise_without_outliers/opt/noiseOpt_normal_3_mean_error.png, id=95, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/opt/noiseOpt_normal_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_normal_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_normal_3_mean_error.png  used on input line 1563.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.

Underfull \hbox (badness 1389) in paragraph at lines 1563--1563
[]\OT1/ppl/m/n/7.02625 (d) |\OT1/phv/m/n/7.02625 Nor-mal scene with 3DoF
 []

<images/noise_without_outliers/opt/noiseOpt_planarForward_3_mean_error.png, id=96, 722.7pt x 660.96938pt>
File: images/noise_without_outliers/opt/noiseOpt_planarForward_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_planarForward_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_planarForward_3_mean_error.png  used on input line 1565.
(pdftex.def)             Requested size: 99.65349pt x 91.13318pt.
<images/noise_without_outliers/opt/noiseOpt_planarsidewaysX_3_mean_error.png, id=97, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/opt/noiseOpt_planarsidewaysX_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_planarsidewaysX_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_planarsidewaysX_3_mean_error.png  used on input line 1566.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.
<images/noise_without_outliers/opt/noiseOpt_planarsidewaysY_3_mean_error.png, id=98, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/opt/noiseOpt_planarsidewaysY_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_planarsidewaysY_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_planarsidewaysY_3_mean_error.png  used on input line 1567.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.
<images/noise_without_outliers/opt/noiseOpt_planar_3_mean_error.png, id=99, 722.7pt x 661.0296pt>
File: images/noise_without_outliers/opt/noiseOpt_planar_3_mean_error.png Graphic file (type png)
<use images/noise_without_outliers/opt/noiseOpt_planar_3_mean_error.png>
Package pdftex.def Info: images/noise_without_outliers/opt/noiseOpt_planar_3_mean_error.png  used on input line 1568.
(pdftex.def)             Requested size: 99.65349pt x 91.14148pt.

LaTeX Warning: Reference `fig:noise_opt_test' on page 12 undefined on input line 1575.


LaTeX Warning: Reference `fig:opt_planar_forward' on page 12 undefined on input line 1575.


LaTeX Warning: Reference `fig:opt_planar_motion' on page 12 undefined on input line 1575.


LaTeX Warning: Reference `tab:E_mat_problem' on page 12 undefined on input line 1575.


LaTeX Warning: Reference `fig:test_ini_methods' on page 12 undefined on input line 1575.


LaTeX Warning: Reference `fig:noise_opt_test' on page 12 undefined on input line 1575.


Overfull \hbox (15.51056pt too wide) in paragraph at lines 1580--1606
 [][] 
 []


Underfull \hbox (badness 10000) in paragraph at lines 1612--1613

 []


LaTeX Warning: Reference `tab:residuals' on page 12 undefined on input line 1615.


LaTeX Warning: Reference `alg:IRLS-Pose-only' on page 12 undefined on input line 1615.


LaTeX Warning: Reference `fig:outlier_v_test' on page 12 undefined on input line 1619.

[12 <./images/identification/legend.png> <./images/identification/5pts.png (PNG copy)> <./images/identification/LiRP.png (PNG copy)> <./images/identification/5pts_planar.png (PNG copy)> <./images/identification/LiRP_planar.png (PNG copy)>]
Underfull \vbox (badness 10000) has occurred while \output is active []

<images/outlier_test/GNC-TLS/fig_residual_test_normal_2_median_error.png, id=107, 730.22812pt x 655.69969pt>
File: images/outlier_test/GNC-TLS/fig_residual_test_normal_2_median_error.png Graphic file (type png)
<use images/outlier_test/GNC-TLS/fig_residual_test_normal_2_median_error.png>
Package pdftex.def Info: images/outlier_test/GNC-TLS/fig_residual_test_normal_2_median_error.png  used on input line 1628.
(pdftex.def)             Requested size: 110.23071pt x 98.98103pt.
<images/outlier_test/GNC-TLS/fig_residual_test_planar_2_median_error.png, id=108, 737.75626pt x 650.43pt>
File: images/outlier_test/GNC-TLS/fig_residual_test_planar_2_median_error.png Graphic file (type png)
<use images/outlier_test/GNC-TLS/fig_residual_test_planar_2_median_error.png>
Package pdftex.def Info: images/outlier_test/GNC-TLS/fig_residual_test_planar_2_median_error.png  used on input line 1631.
(pdftex.def)             Requested size: 110.23071pt x 97.18314pt.

Overfull \hbox (5.33989pt too wide) in paragraph at lines 1628--1632
 [][][] 
 []


LaTeX Warning: Reference `fig:mstls_initial_test' on page 13 undefined on input line 1636.


LaTeX Warning: Reference `fig:identification_methods' on page 13 undefined on input line 1636.


Underfull \hbox (badness 6428) in paragraph at lines 1636--1637
[]\OT1/ppl/m/n/9.53561 Figure [] il-lus-trates the out-lier han-dling
 []


Underfull \hbox (badness 4913) in paragraph at lines 1636--1637
\OT1/ppl/m/n/9.53561 per-for-mance of dif-fer-ent ini-tial rel-a-tive pose
 []


Underfull \hbox (badness 10000) in paragraph at lines 1636--1637

 []


Underfull \vbox (badness 10000) has occurred while \output is active []

 [13 <./images/noise_without_outliers/initial/legend_noise_test_ini.png> <./images/noise_without_outliers/initial/noiseInI_normal_forward_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/initial/noiseInI_normal_sidewaysX_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/initial/noiseInI_normal_sidewaysY_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/initial/noiseInI_normal_normal_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/initial/noiseInI_planar_planarForward_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysX_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/initial/noiseInI_planar_planarsidewaysY_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/initial/noiseInI_planar_planar_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/legend_noise_opt.png> <./images/noise_without_outliers/opt/noiseOpt_forward_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/noiseOpt_sidewaysX_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/noiseOpt_sidewaysY_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/noiseOpt_normal_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/noiseOpt_planarForward_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/noiseOpt_planarsidewaysX_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/noiseOpt_planarsidewaysY_3_mean_error.png (PNG copy)> <./images/noise_without_outliers/opt/noiseOpt_planar_3_mean_error.png (PNG copy)>]
<images/ms_tls_initial/fig_mstls_normal_normal_2_median_error.png, id=115, 737.75626pt x 650.43pt>
File: images/ms_tls_initial/fig_mstls_normal_normal_2_median_error.png Graphic file (type png)
<use images/ms_tls_initial/fig_mstls_normal_normal_2_median_error.png>
Package pdftex.def Info: images/ms_tls_initial/fig_mstls_normal_normal_2_median_error.png  used on input line 1645.
(pdftex.def)             Requested size: 110.23071pt x 97.18314pt.
<images/ms_tls_initial/fig_mstls_planar_planar_2_median_error.png, id=116, 722.7pt x 660.96938pt>
File: images/ms_tls_initial/fig_mstls_planar_planar_2_median_error.png Graphic file (type png)
<use images/ms_tls_initial/fig_mstls_planar_planar_2_median_error.png>
Package pdftex.def Info: images/ms_tls_initial/fig_mstls_planar_planar_2_median_error.png  used on input line 1648.
(pdftex.def)             Requested size: 110.23071pt x 100.81532pt.

Overfull \hbox (5.33989pt too wide) in paragraph at lines 1645--1649
 [][][] 
 []


LaTeX Warning: Reference `fig:gnc-ransac-test' on page 14 undefined on input line 1658.


LaTeX Warning: Citation `barath2020magsac++' on page 14 undefined on input line 1658.


LaTeX Warning: Citation `stewenius2006recent' on page 14 undefined on input line 1658.


LaTeX Warning: Citation `nister2004efficient' on page 14 undefined on input line 1658.


LaTeX Warning: Citation `zhao2020efficient' on page 14 undefined on input line 1658.

<images/gnc-ransac-figures/legend.png, id=117, 413.8662pt x 56.3706pt>
File: images/gnc-ransac-figures/legend.png Graphic file (type png)
<use images/gnc-ransac-figures/legend.png>
Package pdftex.def Info: images/gnc-ransac-figures/legend.png  used on input line 1662.
(pdftex.def)             Requested size: 158.54152pt x 21.59393pt.
<images/gnc-ransac-figures/OutlierProcess_normal_2_boxchart.png, id=118, 1195.3458pt x 365.2044pt>
File: images/gnc-ransac-figures/OutlierProcess_normal_2_boxchart.png Graphic file (type png)
<use images/gnc-ransac-figures/OutlierProcess_normal_2_boxchart.png>
Package pdftex.def Info: images/gnc-ransac-figures/OutlierProcess_normal_2_boxchart.png  used on input line 1667.
(pdftex.def)             Requested size: 220.46144pt x 67.35556pt.

Overfull \hbox (2.66995pt too wide) in paragraph at lines 1662--1668
 [] 
 []

<images/gnc-ransac-figures/OutlierProcess_planar_2_boxchart.png, id=119, 1195.3458pt x 365.2044pt>
File: images/gnc-ransac-figures/OutlierProcess_planar_2_boxchart.png Graphic file (type png)
<use images/gnc-ransac-figures/OutlierProcess_planar_2_boxchart.png>
Package pdftex.def Info: images/gnc-ransac-figures/OutlierProcess_planar_2_boxchart.png  used on input line 1672.
(pdftex.def)             Requested size: 220.46144pt x 67.35556pt.

Overfull \hbox (2.66995pt too wide) in paragraph at lines 1672--1673
 [][] 
 []


LaTeX Warning: Citation `stewenius2006recent' on page 14 undefined on input line 1678.


LaTeX Warning: Citation `nister2004efficient' on page 14 undefined on input line 1678.


LaTeX Warning: Citation `barath2020magsac++' on page 14 undefined on input line 1678.


LaTeX Warning: Citation `lowe2004distinctive' on page 14 undefined on input line 1681.


LaTeX Warning: Citation `cheng2014fast' on page 14 undefined on input line 1681.

<images/Strecha/strecha_Rerr.png, id=120, 1267.6158pt x 541.5432pt>
File: images/Strecha/strecha_Rerr.png Graphic file (type png)
<use images/Strecha/strecha_Rerr.png>
Package pdftex.def Info: images/Strecha/strecha_Rerr.png  used on input line 1687.
(pdftex.def)             Requested size: 220.46144pt x 94.17651pt.
<images/Strecha/strecha_repeated.png, id=121, 412.54124pt x 398.23781pt>
File: images/Strecha/strecha_repeated.png Graphic file (type png)
<use images/Strecha/strecha_repeated.png>
Package pdftex.def Info: images/Strecha/strecha_repeated.png  used on input line 1698.
(pdftex.def)             Requested size: 88.18323pt x 85.12117pt.
<images/Strecha/strecha_reconstruction.png, id=122, 198.0198pt x 164.2938pt>
File: images/Strecha/strecha_reconstruction.png Graphic file (type png)
<use images/Strecha/strecha_reconstruction.png>
Package pdftex.def Info: images/Strecha/strecha_reconstruction.png  used on input line 1702.
(pdftex.def)             Requested size: 121.25446pt x 100.60262pt.

LaTeX Warning: Reference `fig:StrechaErr' on page 14 undefined on input line 1711.


LaTeX Warning: Reference `fig:StrechaErr' on page 14 undefined on input line 1711.

[14 <./images/outlier_test/GNC-TLS/fig_residual_test_normal_2_median_error.png (PNG copy)> <./images/outlier_test/GNC-TLS/fig_residual_test_planar_2_median_error.png (PNG copy)> <./images/ms_tls_initial/fig_mstls_normal_normal_2_median_error.png (PNG copy)> <./images/ms_tls_initial/fig_mstls_planar_planar_2_median_error.png (PNG copy)> <./images/gnc-ransac-figures/legend.png> <./images/gnc-ransac-figures/OutlierProcess_normal_2_boxchart.png (PNG copy)> <./images/gnc-ransac-figures/OutlierProcess_planar_2_boxchart.png (PNG copy)>]

LaTeX Warning: Reference `fig:Strecha1' on page 15 undefined on input line 1713.


LaTeX Warning: Reference `fig:gnc-ransac-test' on page 15 undefined on input line 1713.


Underfull \hbox (badness 10000) in paragraph at lines 1713--1714
[]\OT1/ppl/m/n/9.53561 Regarding the Castle-P19 and Castle-P30
 []


Underfull \hbox (badness 5832) in paragraph at lines 1713--1714
\OT1/ppl/m/n/9.53561 pro-posed GNC-RANSAC method out-per-forms
 []


LaTeX Warning: Reference `fig:poses_3DHerz' on page 15 undefined on input line 1715.


LaTeX Warning: Citation `chatterjee2017robust' on page 15 undefined on input line 1715.


LaTeX Warning: Citation `cai2021pose' on page 15 undefined on input line 1715.

<images/ransac2020/st_peters_square_R_error_box4.png, id=127, 1090.82532pt x 444.15938pt>
File: images/ransac2020/st_peters_square_R_error_box4.png Graphic file (type png)
<use images/ransac2020/st_peters_square_R_error_box4.png>
Package pdftex.def Info: images/ransac2020/st_peters_square_R_error_box4.png  used on input line 1760.
(pdftex.def)             Requested size: 220.46144pt x 89.76556pt.
<images/ransac2020/sacre_coeur_R_error_box4.png, id=128, 1090.82532pt x 444.15938pt>
File: images/ransac2020/sacre_coeur_R_error_box4.png Graphic file (type png)
<use images/ransac2020/sacre_coeur_R_error_box4.png>
Package pdftex.def Info: images/ransac2020/sacre_coeur_R_error_box4.png  used on input line 1763.
(pdftex.def)             Requested size: 220.46144pt x 89.76556pt.

Underfull \hbox (badness 10000) in paragraph at lines 1769--1770
\OT1/ppl/m/n/9.53561 We fur-ther con-ducted ex-per-i-ments on the
 []


LaTeX Warning: Reference `fig:Rerror_ransac2020' on page 15 undefined on input line 1772.


LaTeX Warning: Reference `tab:ransac2020Test' on page 15 undefined on input line 1779.


LaTeX Warning: Reference `fig:Rerror_ransac2020' on page 15 undefined on input line 1779.


Underfull \hbox (badness 2103) in paragraph at lines 1779--1780
\OT1/ppl/m/n/9.53561 the best per-for-mance across var-i-ous resid-u-als,
 []


Underfull \hbox (badness 5359) in paragraph at lines 1779--1780
\OT1/ppl/m/n/9.53561 fol-lowed by MAGSAC++ at max-i-mum 2000
 []


Underfull \hbox (badness 10000) in paragraph at lines 1779--1780

 []


Underfull \vbox (badness 10000) has occurred while \output is active []

 [15 <./images/Strecha/strecha_Rerr.png> <./images/Strecha/strecha_repeated.png> <./images/Strecha/strecha_reconstruction.png> <./images/ransac2020/st_peters_square_R_error_box4.png (PNG copy)> <./images/ransac2020/sacre_coeur_R_error_box4.png (PNG copy)>]
Underfull \hbox (badness 1824) in paragraph at lines 1791--1792
[]\OT1/ppl/m/n/9.53561 Experimental re-sults on both syn-thetic and
 []


Underfull \vbox (badness 10000) has occurred while \output is active []

 (./bare_adv.bbl [16]
Underfull \hbox (badness 10000) in paragraph at lines 99--100
[]\OT1/ppl/m/n/8.03 P. Moulon, ``Add im-ple-men-ta-tion
 []


Underfull \hbox (badness 10000) in paragraph at lines 99--100
\OT1/ppl/m/n/8.03 of lin-ear global trans-la-tion (LiGT),''
 []


Underfull \hbox (badness 10000) in paragraph at lines 99--100
\OT1/ppl/m/n/8.03 https://github.com/openMVG/openMVG/pull/2065,
 []

)
<images/cq.png, id=138, 71.2845pt x 92.637pt>
File: images/cq.png Graphic file (type png)
<use images/cq.png>
Package pdftex.def Info: images/cq.png  used on input line 1973.
(pdftex.def)             Requested size: 69.51787pt x 90.34117pt.
File: images/cq.png Graphic file (type png)
<use images/cq.png>
Package pdftex.def Info: images/cq.png  used on input line 1973.
(pdftex.def)             Requested size: 69.51787pt x 90.34117pt.
<images/xinruiLi.jpeg, id=141, 296.10625pt x 414.54875pt>
File: images/xinruiLi.jpeg Graphic file (type jpg)
<use images/xinruiLi.jpeg>
Package pdftex.def Info: images/xinruiLi.jpeg  used on input line 1978.
(pdftex.def)             Requested size: 64.52457pt x 90.33441pt.
File: images/xinruiLi.jpeg Graphic file (type jpg)
<use images/xinruiLi.jpeg>
Package pdftex.def Info: images/xinruiLi.jpeg  used on input line 1978.
(pdftex.def)             Requested size: 64.52457pt x 90.33441pt.

Underfull \hbox (badness 2012) in paragraph at lines 1978--1980
\OT1/phv/m/n/8.03 in School of Physics, Uni-ver-sity of
 []

<images/yuanxinWu.jpg, id=144, 1053.9375pt x 1505.625pt>
File: images/yuanxinWu.jpg Graphic file (type jpg)
<use images/yuanxinWu.jpg>
Package pdftex.def Info: images/yuanxinWu.jpg  used on input line 1984.
(pdftex.def)             Requested size: 63.23352pt x 90.3336pt.
File: images/yuanxinWu.jpg Graphic file (type jpg)
<use images/yuanxinWu.jpg>
Package pdftex.def Info: images/yuanxinWu.jpg  used on input line 1984.
(pdftex.def)             Requested size: 63.23352pt x 90.3336pt.
[17 <./images/cq.png> <./images/xinruiLi.jpeg>]
<images/wenxianYu.png, id=152, 3670.14406pt x 4779.58621pt>
File: images/wenxianYu.png Graphic file (type png)
<use images/wenxianYu.png>
Package pdftex.def Info: images/wenxianYu.png  used on input line 1990.
(pdftex.def)             Requested size: 69.33025pt x 90.28798pt.
File: images/wenxianYu.png Graphic file (type png)
<use images/wenxianYu.png>
Package pdftex.def Info: images/wenxianYu.png  used on input line 1990.
(pdftex.def)             Requested size: 69.33025pt x 90.28798pt.

Underfull \hbox (badness 5217) in paragraph at lines 1990--1992
\OT1/phv/m/n/8.03 and Ph.D. de-grees from the Na-
 []

[18

 <./images/yuanxinWu.jpg> <./images/wenxianYu.png>] (./bare_adv.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Font Warning: Size substitutions with differences
(Font)              up to 4.5pt have occurred.


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 10134 strings out of 474116
 184857 string characters out of 5743683
 1972187 words of memory out of 5000000
 32275 multiletter control sequences out of 15000+600000
 626001 words of font info for 199 fonts, out of 8000000 for 9000
 1144 hyphenation exceptions out of 8191
 86i,19n,93p,1875b,524s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmex9.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi5.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi9.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmib10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmmib6.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmmib7.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmmib8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cmextra/cmmib9.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr9.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy5.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy9.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msam10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/urw/helvetic/uhvb8a.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/urw/helvetic/uhvr8a.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/urw/helvetic/uhvro8a.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/urw/palatino/uplb8a.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/urw/palatino/uplr8a.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/urw/palatino/uplri8a.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/urw/zapfding/uzdr.pfb>
Output written on bare_adv.pdf (18 pages, 11133727 bytes).
PDF statistics:
 309 PDF objects out of 1000 (max. 8388607)
 155 compressed objects within 2 object streams
 0 named destinations out of 1000 (max. 500000)
 264 words of extra memory for PDF output out of 10000 (max. 10000000)

